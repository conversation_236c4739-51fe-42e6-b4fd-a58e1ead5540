"""
Base class for provincial tax bureau integrations.
"""

import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional

import httpx
from tenacity import retry, stop_after_attempt, wait_exponential

from app.core.exceptions import ProvinceIntegrationError, ExternalServiceError
from app.core.logging import get_logger
from app.models.province import ProvinceConfig
from app.models.tax_submission import TaxSubmission, SubmissionStatus, SubmissionStatusEnum
from app.models.validation import ValidationResult

logger = get_logger(__name__)


class BaseProvinceIntegration(ABC):
    """
    Abstract base class for provincial tax bureau integrations.
    
    This class defines the common interface and provides shared functionality
    for all provincial tax bureau integrations.
    """
    
    def __init__(self, config: ProvinceConfig):
        """
        Initialize the province integration.
        
        Args:
            config: Province configuration containing URLs, auth, etc.
        """
        self.config = config
        self.province_code = config.code
        self.province_name = config.name
        self.base_url = str(config.base_url)
        self.timeout = config.timeout
        self.max_retries = config.max_retries
        self.retry_delay = config.retry_delay
        
        # HTTP client configuration
        self.client_config = {
            "timeout": httpx.Timeout(self.timeout),
            "limits": httpx.Limits(max_keepalive_connections=10, max_connections=20),
        }
        
        logger.info(f"Initialized {self.province_name} integration")
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.client = httpx.AsyncClient(**self.client_config)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if hasattr(self, 'client'):
            await self.client.aclose()
    
    @abstractmethod
    async def authenticate(self) -> Dict[str, Any]:
        """
        Authenticate with the provincial tax bureau.
        
        Returns:
            Authentication result containing tokens, session info, etc.
            
        Raises:
            ProvinceIntegrationError: If authentication fails
        """
        pass
    
    @abstractmethod
    async def submit_tax_return(self, submission: TaxSubmission) -> Dict[str, Any]:
        """
        Submit a tax return to the provincial tax bureau.
        
        Args:
            submission: Tax submission data
            
        Returns:
            Submission result containing external ID, status, etc.
            
        Raises:
            ProvinceIntegrationError: If submission fails
        """
        pass
    
    @abstractmethod
    async def get_submission_status(self, external_id: str) -> SubmissionStatus:
        """
        Get the status of a submitted tax return.
        
        Args:
            external_id: External system submission ID
            
        Returns:
            Current submission status
            
        Raises:
            ProvinceIntegrationError: If status check fails
        """
        pass
    
    @abstractmethod
    async def validate_submission_data(self, submission: TaxSubmission) -> ValidationResult:
        """
        Validate submission data according to provincial requirements.
        
        Args:
            submission: Tax submission data to validate
            
        Returns:
            Validation result with errors, warnings, etc.
        """
        pass
    
    @abstractmethod
    def transform_submission_data(self, submission: TaxSubmission) -> Dict[str, Any]:
        """
        Transform submission data to province-specific format.
        
        Args:
            submission: Standard tax submission data
            
        Returns:
            Province-specific formatted data
        """
        pass
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10)
    )
    async def make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Make an HTTP request to the provincial tax bureau API.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            data: Request body data
            params: Query parameters
            headers: Additional headers
            
        Returns:
            Response data
            
        Raises:
            ExternalServiceError: If request fails
        """
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        # Prepare headers
        request_headers = self._get_default_headers()
        if headers:
            request_headers.update(headers)
        
        try:
            logger.debug(f"Making {method} request to {url}")
            
            response = await self.client.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers=request_headers,
            )
            
            # Log response details
            logger.debug(f"Response status: {response.status_code}")
            
            # Handle different response status codes
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 401:
                raise ProvinceIntegrationError(
                    self.province_code,
                    "Authentication failed",
                    "AUTHENTICATION_ERROR",
                    {"status_code": response.status_code}
                )
            elif response.status_code >= 400:
                error_data = {}
                try:
                    error_data = response.json()
                except:
                    error_data = {"message": response.text}
                
                raise ExternalServiceError(
                    self.province_name,
                    f"HTTP {response.status_code}: {error_data.get('message', 'Unknown error')}",
                    response.status_code,
                    error_data
                )
            
            return response.json()
            
        except httpx.TimeoutException:
            raise ExternalServiceError(
                self.province_name,
                f"Request timeout after {self.timeout} seconds",
                details={"endpoint": endpoint}
            )
        except httpx.RequestError as e:
            raise ExternalServiceError(
                self.province_name,
                f"Request failed: {str(e)}",
                details={"endpoint": endpoint, "error": str(e)}
            )
    
    def _get_default_headers(self) -> Dict[str, str]:
        """Get default headers for API requests."""
        return {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": f"TaxFilingService/1.0 ({self.province_code})",
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the provincial tax bureau API.
        
        Returns:
            Health check result
        """
        start_time = datetime.utcnow()
        
        try:
            # Try to make a simple request (usually to a status endpoint)
            await self.make_request("GET", self._get_health_endpoint())
            
            response_time = (datetime.utcnow() - start_time).total_seconds()
            
            return {
                "status": "healthy",
                "response_time": response_time,
                "timestamp": start_time.isoformat(),
            }
            
        except Exception as e:
            response_time = (datetime.utcnow() - start_time).total_seconds()
            
            return {
                "status": "unhealthy",
                "response_time": response_time,
                "timestamp": start_time.isoformat(),
                "error": str(e),
            }
    
    def _get_health_endpoint(self) -> str:
        """Get the health check endpoint for this province."""
        # Default implementation - subclasses can override
        return self.config.api_endpoints.get("health", "health")
    
    async def batch_submit(self, submissions: List[TaxSubmission]) -> List[Dict[str, Any]]:
        """
        Submit multiple tax returns in batch (if supported).
        
        Args:
            submissions: List of tax submissions
            
        Returns:
            List of submission results
        """
        # Default implementation: submit one by one
        # Subclasses can override for true batch support
        results = []
        
        for submission in submissions:
            try:
                result = await self.submit_tax_return(submission)
                results.append(result)
            except Exception as e:
                results.append({
                    "success": False,
                    "error": str(e),
                    "submission_id": str(submission.id),
                })
        
        return results
    
    def get_supported_tax_types(self) -> List[str]:
        """Get list of supported tax types for this province."""
        # Default implementation - subclasses should override
        return ["vat", "income_tax", "business_tax"]
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get integration capabilities for this province."""
        return {
            "province_code": self.province_code,
            "province_name": self.province_name,
            "supported_tax_types": self.get_supported_tax_types(),
            "batch_submission": hasattr(self, 'batch_submit_native'),
            "real_time_status": True,
            "file_upload": False,
            "digital_signature": False,
        }
