"""
Beijing Tax Bureau integration implementation.
"""

from datetime import datetime
from typing import Any, Dict, List

from app.core.exceptions import ProvinceIntegrationError
from app.core.logging import get_logger
from app.models.tax_submission import TaxSubmission, SubmissionStatus, SubmissionStatusEnum
from app.models.validation import ValidationResult, ValidationLevel
from .base import BaseProvinceIntegration

logger = get_logger(__name__)


class BeijingTaxIntegration(BaseProvinceIntegration):
    """
    Beijing Tax Bureau integration implementation.
    
    This class implements the specific API calls and data transformations
    required for the Beijing Electronic Tax Bureau.
    """
    
    def __init__(self, config):
        """Initialize Beijing tax integration."""
        super().__init__(config)
        self.auth_token = None
        self.session_id = None
        
        # Beijing-specific endpoints
        self.endpoints = {
            "auth": "api/v1/auth/login",
            "submit": "api/v1/tax/submit",
            "status": "api/v1/tax/status",
            "validate": "api/v1/tax/validate",
            "health": "api/v1/system/health",
        }
        
        logger.info("Beijing Tax Bureau integration initialized")
    
    async def authenticate(self) -> Dict[str, Any]:
        """
        Authenticate with Beijing Tax Bureau.
        
        Beijing uses username/password authentication with session tokens.
        """
        try:
            auth_data = {
                "username": self.config.auth_config.get("username"),
                "password": self.config.auth_config.get("password"),
                "client_id": self.config.auth_config.get("client_id", "tax-filing-service"),
            }
            
            response = await self.make_request(
                "POST",
                self.endpoints["auth"],
                data=auth_data
            )
            
            if response.get("success"):
                self.auth_token = response.get("access_token")
                self.session_id = response.get("session_id")
                
                logger.info("Successfully authenticated with Beijing Tax Bureau")
                
                return {
                    "success": True,
                    "token": self.auth_token,
                    "session_id": self.session_id,
                    "expires_at": response.get("expires_at"),
                }
            else:
                raise ProvinceIntegrationError(
                    self.province_code,
                    f"Authentication failed: {response.get('message', 'Unknown error')}",
                    "BEIJING_AUTH_ERROR"
                )
                
        except Exception as e:
            logger.error(f"Beijing authentication failed: {str(e)}")
            raise ProvinceIntegrationError(
                self.province_code,
                f"Authentication failed: {str(e)}",
                "BEIJING_AUTH_ERROR"
            )
    
    async def submit_tax_return(self, submission: TaxSubmission) -> Dict[str, Any]:
        """
        Submit tax return to Beijing Tax Bureau.
        """
        try:
            # Ensure we're authenticated
            if not self.auth_token:
                await self.authenticate()
            
            # Transform data to Beijing format
            beijing_data = self.transform_submission_data(submission)
            
            # Add authentication headers
            headers = {
                "Authorization": f"Bearer {self.auth_token}",
                "X-Session-ID": self.session_id,
            }
            
            response = await self.make_request(
                "POST",
                self.endpoints["submit"],
                data=beijing_data,
                headers=headers
            )
            
            if response.get("success"):
                external_id = response.get("submission_id")
                
                logger.info(f"Successfully submitted to Beijing: {external_id}")
                
                return {
                    "success": True,
                    "external_id": external_id,
                    "status": SubmissionStatusEnum.SUBMITTED,
                    "message": "Tax return submitted successfully",
                    "estimated_processing_time": response.get("estimated_processing_minutes", 30),
                    "reference_number": response.get("reference_number"),
                }
            else:
                raise ProvinceIntegrationError(
                    self.province_code,
                    f"Submission failed: {response.get('message', 'Unknown error')}",
                    "BEIJING_SUBMIT_ERROR",
                    {"response": response}
                )
                
        except ProvinceIntegrationError:
            raise
        except Exception as e:
            logger.error(f"Beijing submission failed: {str(e)}")
            raise ProvinceIntegrationError(
                self.province_code,
                f"Submission failed: {str(e)}",
                "BEIJING_SUBMIT_ERROR"
            )
    
    async def get_submission_status(self, external_id: str) -> SubmissionStatus:
        """
        Get submission status from Beijing Tax Bureau.
        """
        try:
            # Ensure we're authenticated
            if not self.auth_token:
                await self.authenticate()
            
            headers = {
                "Authorization": f"Bearer {self.auth_token}",
                "X-Session-ID": self.session_id,
            }
            
            params = {"submission_id": external_id}
            
            response = await self.make_request(
                "GET",
                self.endpoints["status"],
                params=params,
                headers=headers
            )
            
            if response.get("success"):
                beijing_status = response.get("status")
                status = self._map_beijing_status(beijing_status)
                
                return SubmissionStatus(
                    submission_id=response.get("internal_id"),  # This would be our UUID
                    status=status,
                    external_id=external_id,
                    submitted_at=response.get("submitted_at"),
                    processed_at=response.get("processed_at"),
                    error_message=response.get("error_message"),
                    progress=response.get("progress", {})
                )
            else:
                raise ProvinceIntegrationError(
                    self.province_code,
                    f"Status check failed: {response.get('message', 'Unknown error')}",
                    "BEIJING_STATUS_ERROR"
                )
                
        except ProvinceIntegrationError:
            raise
        except Exception as e:
            logger.error(f"Beijing status check failed: {str(e)}")
            raise ProvinceIntegrationError(
                self.province_code,
                f"Status check failed: {str(e)}",
                "BEIJING_STATUS_ERROR"
            )
    
    async def validate_submission_data(self, submission: TaxSubmission) -> ValidationResult:
        """
        Validate submission data according to Beijing requirements.
        """
        result = ValidationResult(
            is_valid=True,
            province_code=self.province_code
        )
        
        try:
            # Basic validation
            self._validate_company_info(submission, result)
            self._validate_tax_data(submission, result)
            self._validate_beijing_specific(submission, result)
            
            # Call Beijing validation API if available
            if self.config.api_endpoints.get("validate"):
                await self._validate_with_beijing_api(submission, result)
            
            result.update_summary()
            
        except Exception as e:
            logger.error(f"Beijing validation failed: {str(e)}")
            result.add_error(
                "validation",
                f"Validation process failed: {str(e)}",
                rule_name="system_error"
            )
        
        return result
    
    def transform_submission_data(self, submission: TaxSubmission) -> Dict[str, Any]:
        """
        Transform submission data to Beijing-specific format.
        """
        # Beijing Tax Bureau specific data format
        beijing_data = {
            "taxpayer_info": {
                "name": submission.company_info.company_name,
                "tax_id": submission.company_info.tax_id,
                "registration_number": submission.company_info.registration_number,
                "legal_representative": submission.company_info.legal_representative,
                "address": submission.company_info.address,
                "contact": {
                    "phone": submission.company_info.phone,
                    "email": submission.company_info.email,
                }
            },
            "tax_period": {
                "year": submission.tax_period.year,
                "month": submission.tax_period.month,
                "quarter": submission.tax_period.quarter,
                "period_type": submission.tax_period.period_type,
            },
            "tax_items": []
        }
        
        # Transform tax data
        for tax_item in submission.tax_data:
            beijing_tax_item = {
                "tax_type_code": self._map_tax_type_to_beijing(tax_item.tax_type),
                "taxable_amount": tax_item.taxable_amount,
                "tax_rate": tax_item.tax_rate * 100,  # Beijing expects percentage
                "tax_amount": tax_item.tax_amount,
                "deductions": tax_item.deductions or 0,
                "credits": tax_item.credits or 0,
                "final_amount": tax_item.final_amount,
            }
            beijing_data["tax_items"].append(beijing_tax_item)
        
        # Add Beijing-specific fields
        if submission.additional_data:
            beijing_data.update(submission.additional_data.get("beijing", {}))
        
        # Add metadata
        beijing_data["metadata"] = {
            "submission_time": datetime.utcnow().isoformat(),
            "client_version": "1.0.0",
            "source_system": "tax-filing-service",
        }
        
        return beijing_data
    
    def _map_tax_type_to_beijing(self, tax_type: str) -> str:
        """Map standard tax types to Beijing tax type codes."""
        mapping = {
            "vat": "01",  # 增值税
            "income_tax": "02",  # 所得税
            "business_tax": "03",  # 营业税
            "consumption_tax": "04",  # 消费税
            "customs_duty": "05",  # 关税
            "other": "99",  # 其他
        }
        return mapping.get(tax_type, "99")
    
    def _map_beijing_status(self, beijing_status: str) -> SubmissionStatusEnum:
        """Map Beijing status to standard status."""
        mapping = {
            "pending": SubmissionStatusEnum.PENDING,
            "processing": SubmissionStatusEnum.PROCESSING,
            "submitted": SubmissionStatusEnum.SUBMITTED,
            "accepted": SubmissionStatusEnum.ACCEPTED,
            "rejected": SubmissionStatusEnum.REJECTED,
            "failed": SubmissionStatusEnum.FAILED,
            "cancelled": SubmissionStatusEnum.CANCELLED,
        }
        return mapping.get(beijing_status.lower(), SubmissionStatusEnum.PENDING)
    
    def _validate_company_info(self, submission: TaxSubmission, result: ValidationResult):
        """Validate company information for Beijing requirements."""
        company = submission.company_info
        
        # Beijing requires 18-digit tax ID
        if len(company.tax_id) != 18:
            result.add_error(
                "company_info.tax_id",
                "Beijing requires 18-digit tax ID",
                rule_name="beijing_tax_id_format"
            )
        
        # Beijing requires specific registration number format
        if not company.registration_number.startswith("110"):
            result.add_warning(
                "company_info.registration_number",
                "Registration number should start with 110 for Beijing companies",
                rule_name="beijing_registration_format"
            )
    
    def _validate_tax_data(self, submission: TaxSubmission, result: ValidationResult):
        """Validate tax data for Beijing requirements."""
        for i, tax_item in enumerate(submission.tax_data):
            # Beijing has specific tax rate limits
            if tax_item.tax_rate > 0.17:  # 17% max VAT rate
                result.add_error(
                    f"tax_data[{i}].tax_rate",
                    f"Tax rate {tax_item.tax_rate} exceeds Beijing maximum of 17%",
                    rule_name="beijing_tax_rate_limit"
                )
    
    def _validate_beijing_specific(self, submission: TaxSubmission, result: ValidationResult):
        """Validate Beijing-specific requirements."""
        # Check for required Beijing-specific data
        if submission.additional_data:
            beijing_data = submission.additional_data.get("beijing", {})
            
            # Example: Beijing requires special authorization for certain tax types
            if any(item.tax_type == "consumption_tax" for item in submission.tax_data):
                if not beijing_data.get("consumption_tax_license"):
                    result.add_error(
                        "additional_data.beijing.consumption_tax_license",
                        "Consumption tax requires special license in Beijing",
                        rule_name="beijing_consumption_tax_license"
                    )
    
    async def _validate_with_beijing_api(self, submission: TaxSubmission, result: ValidationResult):
        """Validate using Beijing's validation API."""
        try:
            if not self.auth_token:
                await self.authenticate()
            
            headers = {
                "Authorization": f"Bearer {self.auth_token}",
                "X-Session-ID": self.session_id,
            }
            
            validation_data = self.transform_submission_data(submission)
            
            response = await self.make_request(
                "POST",
                self.endpoints["validate"],
                data=validation_data,
                headers=headers
            )
            
            if response.get("success"):
                # Process Beijing validation results
                beijing_errors = response.get("errors", [])
                for error in beijing_errors:
                    result.add_error(
                        error.get("field", "unknown"),
                        error.get("message", "Validation error"),
                        rule_name=f"beijing_{error.get('code', 'unknown')}"
                    )
                
                beijing_warnings = response.get("warnings", [])
                for warning in beijing_warnings:
                    result.add_warning(
                        warning.get("field", "unknown"),
                        warning.get("message", "Validation warning"),
                        rule_name=f"beijing_{warning.get('code', 'unknown')}"
                    )
            
        except Exception as e:
            logger.warning(f"Beijing API validation failed: {str(e)}")
            # Don't fail the entire validation if API call fails
            result.add_warning(
                "api_validation",
                f"Could not validate with Beijing API: {str(e)}",
                rule_name="beijing_api_unavailable"
            )
    
    def get_supported_tax_types(self) -> List[str]:
        """Get supported tax types for Beijing."""
        return ["vat", "income_tax", "business_tax", "consumption_tax"]
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get Beijing integration capabilities."""
        capabilities = super().get_capabilities()
        capabilities.update({
            "batch_submission": False,  # Beijing doesn't support batch
            "real_time_status": True,
            "file_upload": False,
            "digital_signature": True,  # Beijing requires digital signature
            "max_submission_size": 1000000,  # 1MB limit
            "supported_formats": ["json"],
        })
        return capabilities
