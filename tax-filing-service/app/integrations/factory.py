"""
Factory for creating provincial tax bureau integrations.
"""

from typing import Dict, Type

from app.core.exceptions import ProvinceIntegrationError
from app.core.logging import get_logger
from app.models.province import ProvinceConfig
from .base import BaseProvinceIntegration
from .beijing import BeijingTaxIntegration

logger = get_logger(__name__)


class ProvinceIntegrationFactory:
    """
    Factory class for creating provincial tax bureau integrations.
    
    This factory manages the registration and creation of province-specific
    integration classes.
    """
    
    # Registry of province integrations
    _integrations: Dict[str, Type[BaseProvinceIntegration]] = {
        "BJ": BeijingTaxIntegration,
        # Add more provinces here as they are implemented
        # "SH": ShanghaiTaxIntegration,
        # "GD": GuangdongTaxIntegration,
    }
    
    @classmethod
    def register_integration(
        cls, 
        province_code: str, 
        integration_class: Type[BaseProvinceIntegration]
    ) -> None:
        """
        Register a new province integration.
        
        Args:
            province_code: Two-letter province code (e.g., 'BJ')
            integration_class: Integration class that extends BaseProvinceIntegration
        """
        if not issubclass(integration_class, BaseProvinceIntegration):
            raise ValueError(
                f"Integration class must extend BaseProvinceIntegration"
            )
        
        cls._integrations[province_code.upper()] = integration_class
        logger.info(f"Registered integration for province: {province_code}")
    
    @classmethod
    def create_integration(
        cls, 
        config: ProvinceConfig
    ) -> BaseProvinceIntegration:
        """
        Create a province integration instance.
        
        Args:
            config: Province configuration
            
        Returns:
            Province integration instance
            
        Raises:
            ProvinceIntegrationError: If province is not supported
        """
        province_code = config.code.upper()
        
        if province_code not in cls._integrations:
            raise ProvinceIntegrationError(
                province_code,
                f"Province {province_code} is not supported",
                "UNSUPPORTED_PROVINCE",
                {"supported_provinces": list(cls._integrations.keys())}
            )
        
        integration_class = cls._integrations[province_code]
        
        try:
            integration = integration_class(config)
            logger.info(f"Created integration for province: {province_code}")
            return integration
            
        except Exception as e:
            logger.error(f"Failed to create integration for {province_code}: {str(e)}")
            raise ProvinceIntegrationError(
                province_code,
                f"Failed to create integration: {str(e)}",
                "INTEGRATION_CREATION_ERROR"
            )
    
    @classmethod
    def get_supported_provinces(cls) -> list[str]:
        """
        Get list of supported province codes.
        
        Returns:
            List of supported province codes
        """
        return list(cls._integrations.keys())
    
    @classmethod
    def is_province_supported(cls, province_code: str) -> bool:
        """
        Check if a province is supported.
        
        Args:
            province_code: Province code to check
            
        Returns:
            True if province is supported, False otherwise
        """
        return province_code.upper() in cls._integrations
    
    @classmethod
    def get_integration_info(cls) -> Dict[str, Dict[str, any]]:
        """
        Get information about all registered integrations.
        
        Returns:
            Dictionary with integration information
        """
        info = {}
        
        for province_code, integration_class in cls._integrations.items():
            info[province_code] = {
                "class_name": integration_class.__name__,
                "module": integration_class.__module__,
                "description": integration_class.__doc__ or "No description available",
            }
        
        return info
