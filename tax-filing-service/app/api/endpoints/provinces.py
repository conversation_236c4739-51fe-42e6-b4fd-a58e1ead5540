"""
Province management API endpoints.
"""

from typing import List

from fastapi import APIRouter, Depends, HTTPException, status

from app.core.exceptions import ProvinceIntegrationError
from app.core.logging import get_logger
from app.models.base import ResponseModel
from app.models.province import (
    ProvinceConfig,
    ProvinceConfigCreate,
    ProvinceConfigUpdate,
    ProvinceInfo,
    SupportedProvince,
    ProvinceHealthCheck,
    ProvinceCapabilities,
    ProvinceStatistics,
)
from app.api.dependencies import get_province_manager_service
from app.services.province_manager import ProvinceManagerService

logger = get_logger(__name__)
router = APIRouter()


@router.get("", response_model=List[SupportedProvince])
async def list_supported_provinces(
    province_service: ProvinceManagerService = Depends(get_province_manager_service),
) -> List[SupportedProvince]:
    """
    Get list of supported provinces.
    
    This endpoint returns a list of all configured provinces
    with their basic information and status.
    """
    try:
        provinces = await province_service.list_provinces()
        return provinces
        
    except Exception as e:
        logger.error(f"Failed to list provinces: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Failed to list provinces",
                "message": str(e),
            }
        )


@router.get("/{province_code}", response_model=ProvinceInfo)
async def get_province_info(
    province_code: str,
    province_service: ProvinceManagerService = Depends(get_province_manager_service),
) -> ProvinceInfo:
    """
    Get detailed information about a specific province.
    
    This endpoint returns public information about a province
    including supported features and tax types.
    """
    try:
        province_info = await province_service.get_province_info(province_code)
        return province_info
        
    except ProvinceIntegrationError as e:
        logger.warning(f"Province not found: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "error": "Province not found",
                "message": e.message,
                "province_code": province_code,
            }
        )
    except Exception as e:
        logger.error(f"Failed to get province info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Failed to get province info",
                "message": str(e),
            }
        )


@router.post("", response_model=ProvinceConfig)
async def create_province_config(
    config_data: ProvinceConfigCreate,
    province_service: ProvinceManagerService = Depends(get_province_manager_service),
) -> ProvinceConfig:
    """
    Create a new province configuration.
    
    This endpoint allows creating configuration for a new province.
    Requires administrative privileges.
    """
    try:
        config = await province_service.create_province_config(config_data)
        
        logger.info(f"Created province configuration: {config.code}")
        return config
        
    except ProvinceIntegrationError as e:
        logger.warning(f"Province creation failed: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "Province creation failed",
                "message": e.message,
                "province_code": config_data.code,
            }
        )
    except Exception as e:
        logger.error(f"Failed to create province: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Failed to create province",
                "message": str(e),
            }
        )


@router.put("/{province_code}", response_model=ProvinceConfig)
async def update_province_config(
    province_code: str,
    update_data: ProvinceConfigUpdate,
    province_service: ProvinceManagerService = Depends(get_province_manager_service),
) -> ProvinceConfig:
    """
    Update province configuration.
    
    This endpoint allows updating configuration for an existing province.
    Requires administrative privileges.
    """
    try:
        config = await province_service.update_province_config(
            province_code, update_data
        )
        
        logger.info(f"Updated province configuration: {province_code}")
        return config
        
    except ProvinceIntegrationError as e:
        logger.warning(f"Province update failed: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "error": "Province update failed",
                "message": e.message,
                "province_code": province_code,
            }
        )
    except Exception as e:
        logger.error(f"Failed to update province: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Failed to update province",
                "message": str(e),
            }
        )


@router.delete("/{province_code}", response_model=ResponseModel)
async def delete_province_config(
    province_code: str,
    province_service: ProvinceManagerService = Depends(get_province_manager_service),
) -> ResponseModel:
    """
    Delete province configuration.
    
    This endpoint removes configuration for a province.
    Requires administrative privileges.
    """
    try:
        success = await province_service.delete_province_config(province_code)
        
        if success:
            logger.info(f"Deleted province configuration: {province_code}")
            return ResponseModel(
                success=True,
                message=f"Province {province_code} configuration deleted successfully"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": "Deletion failed",
                    "message": "Failed to delete province configuration",
                }
            )
            
    except ProvinceIntegrationError as e:
        logger.warning(f"Province deletion failed: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "error": "Province deletion failed",
                "message": e.message,
                "province_code": province_code,
            }
        )
    except Exception as e:
        logger.error(f"Failed to delete province: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Failed to delete province",
                "message": str(e),
            }
        )


@router.get("/{province_code}/health", response_model=ProvinceHealthCheck)
async def check_province_health(
    province_code: str,
    province_service: ProvinceManagerService = Depends(get_province_manager_service),
) -> ProvinceHealthCheck:
    """
    Perform health check on a province integration.
    
    This endpoint tests connectivity and basic functionality
    of a province's tax bureau integration.
    """
    try:
        health_result = await province_service.health_check_province(province_code)
        return health_result
        
    except Exception as e:
        logger.error(f"Health check failed for {province_code}: {str(e)}")
        # Return error status instead of raising exception
        return ProvinceHealthCheck(
            province_code=province_code,
            status="error",
            last_check="",
            error_message=str(e),
        )


@router.get("/{province_code}/capabilities", response_model=ProvinceCapabilities)
async def get_province_capabilities(
    province_code: str,
    province_service: ProvinceManagerService = Depends(get_province_manager_service),
) -> ProvinceCapabilities:
    """
    Get province integration capabilities.
    
    This endpoint returns information about what features
    and operations are supported by a province integration.
    """
    try:
        capabilities = await province_service.get_province_capabilities(province_code)
        return capabilities
        
    except ProvinceIntegrationError as e:
        logger.warning(f"Province not found: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "error": "Province not found",
                "message": e.message,
                "province_code": province_code,
            }
        )
    except Exception as e:
        logger.error(f"Failed to get capabilities: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Failed to get capabilities",
                "message": str(e),
            }
        )


@router.get("/{province_code}/statistics", response_model=ProvinceStatistics)
async def get_province_statistics(
    province_code: str,
    province_service: ProvinceManagerService = Depends(get_province_manager_service),
) -> ProvinceStatistics:
    """
    Get province integration statistics.
    
    This endpoint returns usage statistics and performance
    metrics for a province integration.
    """
    try:
        statistics = province_service.get_statistics(province_code)
        
        if not statistics:
            # Return empty statistics if none found
            statistics = ProvinceStatistics(province_code=province_code)
        
        return statistics
        
    except Exception as e:
        logger.error(f"Failed to get statistics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Failed to get statistics",
                "message": str(e),
            }
        )
