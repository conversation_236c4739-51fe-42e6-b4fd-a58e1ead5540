"""
Health check API endpoints.
"""

from datetime import datetime
from typing import Dict, List

from fastapi import APIRouter, Depends

from app.core.config import get_settings
from app.core.logging import get_logger
from app.integrations.factory import ProvinceIntegrationFactory
from app.models.province import ProvinceHealthCheck
from app.api.dependencies import get_province_manager_service, get_notification_service
from app.services.province_manager import ProvinceManagerService
from app.services.notification import NotificationService

logger = get_logger(__name__)
router = APIRouter()


@router.get("")
async def health_check() -> Dict[str, any]:
    """
    Basic health check endpoint.
    
    This endpoint returns the overall health status of the service
    and basic system information.
    """
    settings = get_settings()
    
    return {
        "status": "healthy",
        "service": "tax-filing-service",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat(),
        "environment": "development" if settings.DEBUG else "production",
        "supported_provinces": ProvinceIntegrationFactory.get_supported_provinces(),
    }


@router.get("/detailed")
async def detailed_health_check(
    province_service: ProvinceManagerService = Depends(get_province_manager_service),
    notification_service: NotificationService = Depends(get_notification_service),
) -> Dict[str, any]:
    """
    Detailed health check endpoint.
    
    This endpoint returns comprehensive health information including
    the status of all integrated services and provinces.
    """
    settings = get_settings()
    
    # Basic service info
    health_info = {
        "status": "healthy",
        "service": "tax-filing-service",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat(),
        "environment": "development" if settings.DEBUG else "production",
    }
    
    # Check provinces
    try:
        provinces = await province_service.list_provinces()
        province_status = []
        
        for province in provinces:
            try:
                health_result = await province_service.health_check_province(province.code)
                province_status.append({
                    "code": province.code,
                    "name": province.name,
                    "status": health_result.status,
                    "response_time": health_result.response_time,
                    "last_check": health_result.last_check,
                    "error": health_result.error_message,
                })
            except Exception as e:
                province_status.append({
                    "code": province.code,
                    "name": province.name,
                    "status": "error",
                    "error": str(e),
                })
        
        health_info["provinces"] = province_status
        
        # Overall province health
        healthy_provinces = sum(1 for p in province_status if p["status"] == "healthy")
        total_provinces = len(province_status)
        
        health_info["province_summary"] = {
            "total": total_provinces,
            "healthy": healthy_provinces,
            "unhealthy": total_provinces - healthy_provinces,
            "health_percentage": (healthy_provinces / total_provinces * 100) if total_provinces > 0 else 0,
        }
        
    except Exception as e:
        logger.error(f"Failed to check province health: {str(e)}")
        health_info["provinces"] = []
        health_info["province_error"] = str(e)
    
    # Check notification service
    try:
        async with notification_service:
            notification_test = await notification_service.test_notification_channels()
            health_info["notifications"] = {
                "status": "healthy" if any(notification_test.values()) else "unhealthy",
                "channels": notification_test,
            }
    except Exception as e:
        logger.error(f"Failed to check notification service: {str(e)}")
        health_info["notifications"] = {
            "status": "error",
            "error": str(e),
        }
    
    # Check integration factory
    try:
        integration_info = ProvinceIntegrationFactory.get_integration_info()
        health_info["integrations"] = {
            "status": "healthy",
            "supported_provinces": list(integration_info.keys()),
            "details": integration_info,
        }
    except Exception as e:
        logger.error(f"Failed to check integrations: {str(e)}")
        health_info["integrations"] = {
            "status": "error",
            "error": str(e),
        }
    
    # Determine overall status
    if (
        health_info.get("province_summary", {}).get("health_percentage", 0) < 50 or
        health_info.get("notifications", {}).get("status") == "error" or
        health_info.get("integrations", {}).get("status") == "error"
    ):
        health_info["status"] = "degraded"
    
    return health_info


@router.get("/provinces")
async def check_all_provinces_health(
    province_service: ProvinceManagerService = Depends(get_province_manager_service),
) -> List[ProvinceHealthCheck]:
    """
    Check health of all configured provinces.
    
    This endpoint performs health checks on all configured
    province integrations and returns their status.
    """
    try:
        provinces = await province_service.list_provinces()
        health_results = []
        
        for province in provinces:
            try:
                health_result = await province_service.health_check_province(province.code)
                health_results.append(health_result)
            except Exception as e:
                logger.error(f"Health check failed for {province.code}: {str(e)}")
                health_results.append(
                    ProvinceHealthCheck(
                        province_code=province.code,
                        status="error",
                        last_check=datetime.utcnow().isoformat(),
                        error_message=str(e),
                    )
                )
        
        return health_results
        
    except Exception as e:
        logger.error(f"Failed to check provinces health: {str(e)}")
        return []


@router.get("/readiness")
async def readiness_check(
    province_service: ProvinceManagerService = Depends(get_province_manager_service),
) -> Dict[str, any]:
    """
    Readiness check endpoint.
    
    This endpoint indicates whether the service is ready to accept requests.
    Used by container orchestration systems like Kubernetes.
    """
    try:
        # Check if we have at least one configured province
        provinces = await province_service.list_provinces()
        
        if not provinces:
            return {
                "ready": False,
                "reason": "No provinces configured",
                "timestamp": datetime.utcnow().isoformat(),
            }
        
        # Check if integration factory is working
        supported_provinces = ProvinceIntegrationFactory.get_supported_provinces()
        
        if not supported_provinces:
            return {
                "ready": False,
                "reason": "No province integrations available",
                "timestamp": datetime.utcnow().isoformat(),
            }
        
        return {
            "ready": True,
            "provinces_configured": len(provinces),
            "integrations_available": len(supported_provinces),
            "timestamp": datetime.utcnow().isoformat(),
        }
        
    except Exception as e:
        logger.error(f"Readiness check failed: {str(e)}")
        return {
            "ready": False,
            "reason": f"Readiness check failed: {str(e)}",
            "timestamp": datetime.utcnow().isoformat(),
        }


@router.get("/liveness")
async def liveness_check() -> Dict[str, any]:
    """
    Liveness check endpoint.
    
    This endpoint indicates whether the service is alive and functioning.
    Used by container orchestration systems like Kubernetes.
    """
    return {
        "alive": True,
        "timestamp": datetime.utcnow().isoformat(),
        "uptime": "unknown",  # In production, you'd track actual uptime
    }
