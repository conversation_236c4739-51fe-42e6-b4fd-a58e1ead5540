"""
Data validation API endpoints.
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status

from app.core.logging import get_logger
from app.models.validation import (
    ValidationResult,
    ValidationRequest,
    BatchValidationRequest,
    BatchValidationResponse,
)
from app.models.tax_submission import TaxSubmission
from app.api.dependencies import get_validation_service
from app.services.validation import ValidationService

logger = get_logger(__name__)
router = APIRouter()


@router.post("/validate-submission", response_model=ValidationResult)
async def validate_tax_submission(
    submission: TaxSubmission,
    validation_service: ValidationService = Depends(get_validation_service),
) -> ValidationResult:
    """
    Validate a complete tax submission.
    
    This endpoint validates a tax submission against both general
    business rules and province-specific requirements.
    """
    try:
        result = await validation_service.validate_submission(submission)
        
        logger.debug(
            f"Validation completed for {submission.province_code}: "
            f"{len(result.errors)} errors, {len(result.warnings)} warnings"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Validation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Validation failed",
                "message": str(e),
            }
        )


@router.post("/validate-data", response_model=ValidationResult)
async def validate_data(
    request: ValidationRequest,
    validation_service: ValidationService = Depends(get_validation_service),
) -> ValidationResult:
    """
    Validate arbitrary data against province requirements.
    
    This endpoint allows validating data structures without
    creating a complete submission object.
    """
    try:
        result = await validation_service.validate_data(request)
        
        logger.debug(
            f"Data validation completed for {request.province_code}: "
            f"valid={result.is_valid}"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Data validation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Data validation failed",
                "message": str(e),
            }
        )


@router.post("/batch-validate", response_model=BatchValidationResponse)
async def batch_validate_submissions(
    request: BatchValidationRequest,
    validation_service: ValidationService = Depends(get_validation_service),
) -> BatchValidationResponse:
    """
    Validate multiple submissions in batch.
    
    This endpoint allows validating multiple submissions
    concurrently for improved performance.
    """
    try:
        response = await validation_service.batch_validate(request)
        
        logger.info(
            f"Batch validation completed for {request.province_code}: "
            f"{response.valid_submissions} valid, {response.invalid_submissions} invalid"
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Batch validation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Batch validation failed",
                "message": str(e),
            }
        )


@router.post("/{province_code}/validate", response_model=ValidationResult)
async def validate_for_province(
    province_code: str,
    data: dict,
    validation_service: ValidationService = Depends(get_validation_service),
) -> ValidationResult:
    """
    Validate data for a specific province.
    
    This is a convenience endpoint that validates data
    for a specific province without requiring a full request object.
    """
    try:
        request = ValidationRequest(
            province_code=province_code,
            data=data,
        )
        
        result = await validation_service.validate_data(request)
        
        logger.debug(
            f"Province validation completed for {province_code}: "
            f"valid={result.is_valid}"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Province validation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Province validation failed",
                "message": str(e),
            }
        )
