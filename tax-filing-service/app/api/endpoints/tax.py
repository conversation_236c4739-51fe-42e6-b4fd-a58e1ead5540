"""
Tax submission API endpoints.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse

from app.core.exceptions import SubmissionError, ValidationError
from app.core.logging import get_logger
from app.models.base import PaginatedResponse, ResponseModel
from app.models.tax_submission import (
    TaxSubmissionCreate,
    TaxSubmissionResponse,
    SubmissionStatus,
    SubmissionStatusEnum,
    BatchSubmissionRequest,
    BatchSubmissionResponse,
    TaxSubmission,
)
from app.api.dependencies import get_tax_submission_service, get_notification_service
from app.services.tax_submission import TaxSubmissionService
from app.services.notification import NotificationService

logger = get_logger(__name__)
router = APIRouter()


@router.post("/submit", response_model=TaxSubmissionResponse)
async def submit_tax_return(
    submission_data: TaxSubmissionCreate,
    tax_service: TaxSubmissionService = Depends(get_tax_submission_service),
    notification_service: NotificationService = Depends(get_notification_service),
) -> TaxSubmissionResponse:
    """
    Submit a tax return to the appropriate provincial bureau.
    
    This endpoint accepts tax submission data, validates it, and submits it
    to the corresponding provincial tax bureau.
    """
    try:
        # Submit tax return
        response = await tax_service.submit_tax_return(submission_data)
        
        # Send notification
        async with notification_service:
            await notification_service.notify_submission_created(
                submission_id=response.submission_id,
                province_code=submission_data.province_code,
                company_name=submission_data.company_info.company_name,
            )
        
        logger.info(f"Tax return submitted successfully: {response.submission_id}")
        return response
        
    except ValidationError as e:
        logger.warning(f"Validation failed: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail={
                "error": "Validation failed",
                "message": e.message,
                "details": e.details,
            }
        )
    except SubmissionError as e:
        logger.error(f"Submission failed: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "Submission failed",
                "message": e.message,
                "submission_id": e.submission_id,
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Internal server error",
                "message": "An unexpected error occurred",
            }
        )


@router.get("/status/{submission_id}", response_model=SubmissionStatus)
async def get_submission_status(
    submission_id: UUID,
    tax_service: TaxSubmissionService = Depends(get_tax_submission_service),
) -> SubmissionStatus:
    """
    Get the current status of a tax submission.
    
    This endpoint returns the current status of a submission, including
    any updates from the provincial tax bureau.
    """
    try:
        status_info = await tax_service.get_submission_status(submission_id)
        return status_info
        
    except SubmissionError as e:
        logger.warning(f"Status check failed: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "error": "Submission not found",
                "message": e.message,
                "submission_id": str(submission_id),
            }
        )
    except Exception as e:
        logger.error(f"Status check error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Status check failed",
                "message": "Failed to retrieve submission status",
            }
        )


@router.post("/batch-submit", response_model=BatchSubmissionResponse)
async def batch_submit_tax_returns(
    batch_request: BatchSubmissionRequest,
    tax_service: TaxSubmissionService = Depends(get_tax_submission_service),
    notification_service: NotificationService = Depends(get_notification_service),
) -> BatchSubmissionResponse:
    """
    Submit multiple tax returns in batch.
    
    This endpoint accepts multiple tax submissions and processes them
    concurrently for improved performance.
    """
    try:
        response = await tax_service.batch_submit(batch_request)
        
        # Send batch notification
        async with notification_service:
            notifications = []
            for submission_id in response.successful_submissions:
                notifications.append({
                    "type": "submission_created",
                    "submission_id": str(submission_id),
                    "batch_id": response.batch_id,
                })
            
            if notifications:
                await notification_service.send_batch_notification(notifications)
        
        logger.info(
            f"Batch submission completed: {response.batch_id} - "
            f"{len(response.successful_submissions)} successful, "
            f"{len(response.failed_submissions)} failed"
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Batch submission error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Batch submission failed",
                "message": str(e),
            }
        )


@router.post("/retry/{submission_id}", response_model=TaxSubmissionResponse)
async def retry_submission(
    submission_id: UUID,
    tax_service: TaxSubmissionService = Depends(get_tax_submission_service),
) -> TaxSubmissionResponse:
    """
    Retry a failed tax submission.
    
    This endpoint allows retrying submissions that have failed or been rejected.
    """
    try:
        response = await tax_service.retry_failed_submission(submission_id)
        
        logger.info(f"Submission retry successful: {submission_id}")
        return response
        
    except SubmissionError as e:
        logger.warning(f"Retry failed: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "Retry failed",
                "message": e.message,
                "submission_id": str(submission_id),
            }
        )
    except Exception as e:
        logger.error(f"Retry error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Retry failed",
                "message": "Failed to retry submission",
            }
        )


@router.delete("/cancel/{submission_id}", response_model=ResponseModel)
async def cancel_submission(
    submission_id: UUID,
    tax_service: TaxSubmissionService = Depends(get_tax_submission_service),
) -> ResponseModel:
    """
    Cancel a pending or processing submission.
    
    This endpoint cancels submissions that are still pending or processing.
    """
    try:
        success = await tax_service.cancel_submission(submission_id)
        
        if success:
            logger.info(f"Submission cancelled: {submission_id}")
            return ResponseModel(
                success=True,
                message="Submission cancelled successfully"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": "Cancellation failed",
                    "message": "Failed to cancel submission",
                }
            )
            
    except SubmissionError as e:
        logger.warning(f"Cancellation failed: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "Cancellation failed",
                "message": e.message,
                "submission_id": str(submission_id),
            }
        )
    except Exception as e:
        logger.error(f"Cancellation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Cancellation failed",
                "message": "Failed to cancel submission",
            }
        )


@router.get("/submissions", response_model=PaginatedResponse)
async def list_submissions(
    province_code: Optional[str] = Query(None, description="Filter by province code"),
    status: Optional[SubmissionStatusEnum] = Query(None, description="Filter by status"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    tax_service: TaxSubmissionService = Depends(get_tax_submission_service),
) -> PaginatedResponse:
    """
    List tax submissions with optional filtering and pagination.
    
    This endpoint returns a paginated list of submissions with optional
    filtering by province and status.
    """
    try:
        offset = (page - 1) * size
        
        submissions = tax_service.list_submissions(
            province_code=province_code,
            status=status,
            limit=size,
            offset=offset,
        )
        
        # Convert to dict for response
        submission_data = [
            {
                "id": str(submission.id),
                "province_code": submission.province_code,
                "company_name": submission.company_info.company_name,
                "status": submission.status,
                "created_at": submission.created_at.isoformat() if submission.created_at else None,
                "submitted_at": submission.submitted_at.isoformat() if submission.submitted_at else None,
                "external_id": submission.external_id,
            }
            for submission in submissions
        ]
        
        # For demo purposes, we'll use the actual count as total
        # In production, you'd want a separate count query
        total = len(submission_data)
        
        return PaginatedResponse.create(
            data=submission_data,
            total=total,
            page=page,
            size=size,
            message="Submissions retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"List submissions error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Failed to list submissions",
                "message": str(e),
            }
        )


@router.get("/submissions/{submission_id}", response_model=TaxSubmission)
async def get_submission_details(
    submission_id: UUID,
    tax_service: TaxSubmissionService = Depends(get_tax_submission_service),
) -> TaxSubmission:
    """
    Get detailed information about a specific submission.
    
    This endpoint returns complete submission details including
    all tax data and metadata.
    """
    try:
        submission = tax_service.get_submission(submission_id)
        
        if not submission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "Submission not found",
                    "message": f"Submission {submission_id} not found",
                }
            )
        
        return submission
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get submission error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Failed to get submission",
                "message": str(e),
            }
        )
