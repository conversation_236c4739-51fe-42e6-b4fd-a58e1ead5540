"""
Dependency injection for API endpoints.
"""

from functools import lru_cache

from app.services.tax_submission import TaxSubmissionService
from app.services.province_manager import ProvinceManagerService
from app.services.validation import ValidationService
from app.services.notification import NotificationService


@lru_cache()
def get_province_manager_service() -> ProvinceManagerService:
    """Get province manager service instance."""
    return ProvinceManagerService()


@lru_cache()
def get_validation_service() -> ValidationService:
    """Get validation service instance."""
    province_manager = get_province_manager_service()
    return ValidationService(province_manager)


@lru_cache()
def get_tax_submission_service() -> TaxSubmissionService:
    """Get tax submission service instance."""
    province_manager = get_province_manager_service()
    validation_service = get_validation_service()
    return TaxSubmissionService(province_manager, validation_service)


@lru_cache()
def get_notification_service() -> NotificationService:
    """Get notification service instance."""
    return NotificationService()
