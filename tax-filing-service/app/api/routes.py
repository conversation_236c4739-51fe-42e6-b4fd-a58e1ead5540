"""
Main API router configuration.
"""

from fastapi import APIRouter

from .endpoints import tax, provinces, validation, health

# Create main API router
api_router = APIRouter()

# Include endpoint routers
api_router.include_router(
    tax.router,
    prefix="/tax",
    tags=["tax-submission"]
)

api_router.include_router(
    provinces.router,
    prefix="/provinces",
    tags=["provinces"]
)

api_router.include_router(
    validation.router,
    prefix="/validation",
    tags=["validation"]
)

api_router.include_router(
    health.router,
    prefix="/health",
    tags=["health"]
)
