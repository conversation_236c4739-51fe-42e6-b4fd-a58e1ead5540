"""
Province configuration management service.
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

from app.core.config import get_settings
from app.core.exceptions import ProvinceIntegrationError
from app.core.logging import get_logger
from app.integrations.factory import ProvinceIntegrationFactory
from app.models.province import (
    ProvinceConfig,
    ProvinceConfigCreate,
    ProvinceConfigUpdate,
    ProvinceInfo,
    SupportedProvince,
    ProvinceHealthCheck,
    ProvinceCapabilities,
    ProvinceStatistics,
    ProvinceStatus,
)

logger = get_logger(__name__)


class ProvinceManagerService:
    """
    Service for managing provincial tax bureau configurations.
    
    This service handles loading, updating, and managing configurations
    for different provincial tax bureaus.
    """
    
    def __init__(self):
        """Initialize the province manager service."""
        self.settings = get_settings()
        self.provinces: Dict[str, ProvinceConfig] = {}
        self.statistics: Dict[str, ProvinceStatistics] = {}
        
        # Load initial configurations
        self._load_province_configs()
        
        logger.info("Province manager service initialized")
    
    def _load_province_configs(self) -> None:
        """Load province configurations from file."""
        config_path = Path(self.settings.PROVINCE_CONFIG_PATH)
        
        if not config_path.exists():
            logger.warning(f"Province config file not found: {config_path}")
            self._create_default_configs()
            return
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                configs_data = json.load(f)
            
            for province_data in configs_data.get('provinces', []):
                config = ProvinceConfig(**province_data)
                self.provinces[config.code] = config
                
                # Initialize statistics
                self.statistics[config.code] = ProvinceStatistics(
                    province_code=config.code
                )
            
            logger.info(f"Loaded {len(self.provinces)} province configurations")
            
        except Exception as e:
            logger.error(f"Failed to load province configs: {str(e)}")
            self._create_default_configs()
    
    def _create_default_configs(self) -> None:
        """Create default province configurations."""
        # Beijing configuration
        beijing_config = ProvinceConfig(
            code="BJ",
            name="北京市",
            name_en="Beijing",
            base_url="https://etax.beijing.chinatax.gov.cn:8443/",
            status=ProvinceStatus.ACTIVE,
            auth_method="username_password",
            timeout=30,
            max_retries=3,
            retry_delay=1.0,
            auth_config={
                "username": "demo_user",
                "password": "demo_password",
                "client_id": "tax-filing-service",
            },
            api_endpoints={
                "auth": "api/v1/auth/login",
                "submit": "api/v1/tax/submit",
                "status": "api/v1/tax/status",
                "validate": "api/v1/tax/validate",
                "health": "api/v1/system/health",
            },
            data_mappings={
                "tax_types": {
                    "vat": "01",
                    "income_tax": "02",
                    "business_tax": "03",
                    "consumption_tax": "04",
                }
            },
            validation_rules={
                "tax_id_length": 18,
                "max_tax_rate": 0.17,
                "required_fields": ["company_name", "tax_id", "legal_representative"],
            },
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        self.provinces["BJ"] = beijing_config
        self.statistics["BJ"] = ProvinceStatistics(province_code="BJ")
        
        # Save to file
        self._save_province_configs()
        
        logger.info("Created default province configurations")
    
    def _save_province_configs(self) -> None:
        """Save province configurations to file."""
        config_path = Path(self.settings.PROVINCE_CONFIG_PATH)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            configs_data = {
                "provinces": [
                    config.model_dump() for config in self.provinces.values()
                ],
                "last_updated": datetime.utcnow().isoformat(),
            }
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(configs_data, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info("Saved province configurations")
            
        except Exception as e:
            logger.error(f"Failed to save province configs: {str(e)}")
    
    async def get_province_config(self, province_code: str) -> ProvinceConfig:
        """
        Get province configuration by code.
        
        Args:
            province_code: Province code (e.g., 'BJ')
            
        Returns:
            Province configuration
            
        Raises:
            ProvinceIntegrationError: If province not found
        """
        province_code = province_code.upper()
        
        if province_code not in self.provinces:
            raise ProvinceIntegrationError(
                province_code,
                f"Province {province_code} not configured",
                "PROVINCE_NOT_CONFIGURED"
            )
        
        return self.provinces[province_code]
    
    async def create_province_config(
        self, 
        config_data: ProvinceConfigCreate
    ) -> ProvinceConfig:
        """
        Create a new province configuration.
        
        Args:
            config_data: Province configuration data
            
        Returns:
            Created province configuration
            
        Raises:
            ProvinceIntegrationError: If province already exists
        """
        province_code = config_data.code.upper()
        
        if province_code in self.provinces:
            raise ProvinceIntegrationError(
                province_code,
                f"Province {province_code} already configured",
                "PROVINCE_ALREADY_EXISTS"
            )
        
        config = ProvinceConfig(
            **config_data.model_dump(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        self.provinces[province_code] = config
        self.statistics[province_code] = ProvinceStatistics(province_code=province_code)
        
        # Save to file
        self._save_province_configs()
        
        logger.info(f"Created province configuration: {province_code}")
        return config
    
    async def update_province_config(
        self,
        province_code: str,
        update_data: ProvinceConfigUpdate,
    ) -> ProvinceConfig:
        """
        Update province configuration.
        
        Args:
            province_code: Province code
            update_data: Update data
            
        Returns:
            Updated province configuration
            
        Raises:
            ProvinceIntegrationError: If province not found
        """
        province_code = province_code.upper()
        
        if province_code not in self.provinces:
            raise ProvinceIntegrationError(
                province_code,
                f"Province {province_code} not found",
                "PROVINCE_NOT_FOUND"
            )
        
        config = self.provinces[province_code]
        
        # Update fields
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(config, field, value)
        
        config.updated_at = datetime.utcnow()
        
        # Save to file
        self._save_province_configs()
        
        logger.info(f"Updated province configuration: {province_code}")
        return config
    
    async def delete_province_config(self, province_code: str) -> bool:
        """
        Delete province configuration.
        
        Args:
            province_code: Province code
            
        Returns:
            True if deleted successfully
            
        Raises:
            ProvinceIntegrationError: If province not found
        """
        province_code = province_code.upper()
        
        if province_code not in self.provinces:
            raise ProvinceIntegrationError(
                province_code,
                f"Province {province_code} not found",
                "PROVINCE_NOT_FOUND"
            )
        
        del self.provinces[province_code]
        if province_code in self.statistics:
            del self.statistics[province_code]
        
        # Save to file
        self._save_province_configs()
        
        logger.info(f"Deleted province configuration: {province_code}")
        return True
    
    async def list_provinces(self) -> List[SupportedProvince]:
        """
        List all configured provinces.
        
        Returns:
            List of supported provinces
        """
        return [
            SupportedProvince(
                code=config.code,
                name=config.name,
                status=config.status,
                last_updated=config.updated_at.isoformat() if config.updated_at else None,
            )
            for config in self.provinces.values()
        ]
    
    async def get_province_info(self, province_code: str) -> ProvinceInfo:
        """
        Get public province information.
        
        Args:
            province_code: Province code
            
        Returns:
            Province information
            
        Raises:
            ProvinceIntegrationError: If province not found
        """
        config = await self.get_province_config(province_code)
        
        # Get supported tax types from integration
        supported_tax_types = []
        features = []
        
        try:
            if ProvinceIntegrationFactory.is_province_supported(province_code):
                integration = ProvinceIntegrationFactory.create_integration(config)
                supported_tax_types = integration.get_supported_tax_types()
                capabilities = integration.get_capabilities()
                
                # Extract features from capabilities
                if capabilities.get("batch_submission"):
                    features.append("batch_submission")
                if capabilities.get("real_time_status"):
                    features.append("real_time_status")
                if capabilities.get("file_upload"):
                    features.append("file_upload")
                if capabilities.get("digital_signature"):
                    features.append("digital_signature")
                    
        except Exception as e:
            logger.warning(f"Failed to get integration info for {province_code}: {str(e)}")
        
        return ProvinceInfo(
            code=config.code,
            name=config.name,
            name_en=config.name_en,
            status=config.status,
            supported_tax_types=supported_tax_types,
            features=features,
        )
    
    async def health_check_province(self, province_code: str) -> ProvinceHealthCheck:
        """
        Perform health check on a province integration.
        
        Args:
            province_code: Province code
            
        Returns:
            Health check result
        """
        try:
            config = await self.get_province_config(province_code)
            
            if not ProvinceIntegrationFactory.is_province_supported(province_code):
                return ProvinceHealthCheck(
                    province_code=province_code,
                    status="unsupported",
                    last_check=datetime.utcnow().isoformat(),
                    error_message="Province integration not implemented",
                )
            
            integration = ProvinceIntegrationFactory.create_integration(config)
            
            async with integration:
                health_result = await integration.health_check()
                
                # Update config with health check result
                config.last_health_check = datetime.utcnow().isoformat()
                config.health_status = health_result["status"]
                
                return ProvinceHealthCheck(
                    province_code=province_code,
                    status=health_result["status"],
                    response_time=health_result.get("response_time"),
                    last_check=health_result["timestamp"],
                    error_message=health_result.get("error"),
                )
                
        except Exception as e:
            logger.error(f"Health check failed for {province_code}: {str(e)}")
            return ProvinceHealthCheck(
                province_code=province_code,
                status="error",
                last_check=datetime.utcnow().isoformat(),
                error_message=str(e),
            )
    
    async def get_province_capabilities(self, province_code: str) -> ProvinceCapabilities:
        """
        Get province integration capabilities.
        
        Args:
            province_code: Province code
            
        Returns:
            Province capabilities
        """
        config = await self.get_province_config(province_code)
        
        if not ProvinceIntegrationFactory.is_province_supported(province_code):
            return ProvinceCapabilities(
                province_code=province_code,
                supported_operations=[],
                supported_tax_types=[],
            )
        
        integration = ProvinceIntegrationFactory.create_integration(config)
        capabilities_dict = integration.get_capabilities()
        
        return ProvinceCapabilities(**capabilities_dict)
    
    def update_statistics(
        self,
        province_code: str,
        successful: bool,
        processing_time: Optional[float] = None,
    ) -> None:
        """
        Update province statistics.
        
        Args:
            province_code: Province code
            successful: Whether the operation was successful
            processing_time: Processing time in minutes
        """
        if province_code not in self.statistics:
            self.statistics[province_code] = ProvinceStatistics(
                province_code=province_code
            )
        
        stats = self.statistics[province_code]
        stats.total_submissions += 1
        
        if successful:
            stats.successful_submissions += 1
        else:
            stats.failed_submissions += 1
        
        if processing_time is not None:
            # Update average processing time
            if stats.average_processing_time is None:
                stats.average_processing_time = processing_time
            else:
                # Simple moving average
                total_time = stats.average_processing_time * (stats.total_submissions - 1)
                stats.average_processing_time = (total_time + processing_time) / stats.total_submissions
        
        stats.last_submission = datetime.utcnow().isoformat()
    
    def get_statistics(self, province_code: str) -> Optional[ProvinceStatistics]:
        """Get statistics for a province."""
        return self.statistics.get(province_code.upper())
