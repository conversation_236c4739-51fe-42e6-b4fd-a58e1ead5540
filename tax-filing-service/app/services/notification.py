"""
Notification service for tax filing events.
"""

import asyncio
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID

import httpx

from app.core.config import get_settings
from app.core.logging import get_logger
from app.models.tax_submission import SubmissionStatusEnum

logger = get_logger(__name__)


class NotificationType(str, Enum):
    """Notification type enumeration."""
    
    SUBMISSION_CREATED = "submission_created"
    SUBMISSION_SUBMITTED = "submission_submitted"
    SUBMISSION_ACCEPTED = "submission_accepted"
    SUBMISSION_REJECTED = "submission_rejected"
    SUBMISSION_FAILED = "submission_failed"
    VALIDATION_FAILED = "validation_failed"
    SYSTEM_ERROR = "system_error"


class NotificationChannel(str, Enum):
    """Notification channel enumeration."""
    
    HTTP_WEBHOOK = "http_webhook"
    EMAIL = "email"
    SMS = "sms"
    INTERNAL_LOG = "internal_log"


class NotificationService:
    """
    Service for sending notifications about tax filing events.
    
    This service handles various notification channels and event types
    to keep the main system and users informed about submission status.
    """
    
    def __init__(self):
        """Initialize the notification service."""
        self.settings = get_settings()
        self.client = httpx.AsyncClient(timeout=30.0)
        
        # Notification configuration
        self.webhook_url = self.settings.GO_BACKEND_URL + "/api/tax-filing/webhook"
        self.enabled_channels = [NotificationChannel.HTTP_WEBHOOK, NotificationChannel.INTERNAL_LOG]
        
        logger.info("Notification service initialized")
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.aclose()
    
    async def notify_submission_created(
        self,
        submission_id: UUID,
        province_code: str,
        company_name: str,
        **kwargs
    ) -> None:
        """
        Notify that a new submission has been created.
        
        Args:
            submission_id: Submission ID
            province_code: Province code
            company_name: Company name
            **kwargs: Additional notification data
        """
        await self._send_notification(
            notification_type=NotificationType.SUBMISSION_CREATED,
            data={
                "submission_id": str(submission_id),
                "province_code": province_code,
                "company_name": company_name,
                "timestamp": datetime.utcnow().isoformat(),
                **kwargs
            }
        )
    
    async def notify_submission_status_change(
        self,
        submission_id: UUID,
        old_status: SubmissionStatusEnum,
        new_status: SubmissionStatusEnum,
        province_code: str,
        external_id: Optional[str] = None,
        error_message: Optional[str] = None,
        **kwargs
    ) -> None:
        """
        Notify about submission status changes.
        
        Args:
            submission_id: Submission ID
            old_status: Previous status
            new_status: New status
            province_code: Province code
            external_id: External system ID
            error_message: Error message if applicable
            **kwargs: Additional notification data
        """
        # Determine notification type based on new status
        notification_type = self._get_notification_type_for_status(new_status)
        
        await self._send_notification(
            notification_type=notification_type,
            data={
                "submission_id": str(submission_id),
                "old_status": old_status,
                "new_status": new_status,
                "province_code": province_code,
                "external_id": external_id,
                "error_message": error_message,
                "timestamp": datetime.utcnow().isoformat(),
                **kwargs
            }
        )
    
    async def notify_validation_failed(
        self,
        submission_id: Optional[UUID],
        province_code: str,
        errors: List[Dict[str, Any]],
        warnings: List[Dict[str, Any]],
        **kwargs
    ) -> None:
        """
        Notify about validation failures.
        
        Args:
            submission_id: Submission ID (if available)
            province_code: Province code
            errors: Validation errors
            warnings: Validation warnings
            **kwargs: Additional notification data
        """
        await self._send_notification(
            notification_type=NotificationType.VALIDATION_FAILED,
            data={
                "submission_id": str(submission_id) if submission_id else None,
                "province_code": province_code,
                "error_count": len(errors),
                "warning_count": len(warnings),
                "errors": errors[:5],  # Limit to first 5 errors
                "warnings": warnings[:5],  # Limit to first 5 warnings
                "timestamp": datetime.utcnow().isoformat(),
                **kwargs
            }
        )
    
    async def notify_system_error(
        self,
        error_type: str,
        error_message: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> None:
        """
        Notify about system errors.
        
        Args:
            error_type: Type of error
            error_message: Error message
            context: Error context
            **kwargs: Additional notification data
        """
        await self._send_notification(
            notification_type=NotificationType.SYSTEM_ERROR,
            data={
                "error_type": error_type,
                "error_message": error_message,
                "context": context or {},
                "timestamp": datetime.utcnow().isoformat(),
                **kwargs
            }
        )
    
    async def _send_notification(
        self,
        notification_type: NotificationType,
        data: Dict[str, Any]
    ) -> None:
        """
        Send notification through configured channels.
        
        Args:
            notification_type: Type of notification
            data: Notification data
        """
        notification_payload = {
            "type": notification_type,
            "data": data,
            "service": "tax-filing-service",
            "timestamp": datetime.utcnow().isoformat(),
        }
        
        # Send through all enabled channels
        tasks = []
        
        for channel in self.enabled_channels:
            if channel == NotificationChannel.HTTP_WEBHOOK:
                tasks.append(self._send_webhook_notification(notification_payload))
            elif channel == NotificationChannel.INTERNAL_LOG:
                tasks.append(self._log_notification(notification_payload))
            # Add more channels as needed
        
        # Send notifications concurrently
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _send_webhook_notification(self, payload: Dict[str, Any]) -> None:
        """
        Send notification via HTTP webhook.
        
        Args:
            payload: Notification payload
        """
        try:
            response = await self.client.post(
                self.webhook_url,
                json=payload,
                headers={
                    "Content-Type": "application/json",
                    "X-Service": "tax-filing-service",
                    "X-Notification-Type": payload["type"],
                }
            )
            
            if response.status_code == 200:
                logger.debug(f"Webhook notification sent: {payload['type']}")
            else:
                logger.warning(
                    f"Webhook notification failed: {response.status_code} - {response.text}"
                )
                
        except httpx.TimeoutException:
            logger.warning("Webhook notification timeout")
        except httpx.RequestError as e:
            logger.warning(f"Webhook notification error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected webhook error: {str(e)}")
    
    async def _log_notification(self, payload: Dict[str, Any]) -> None:
        """
        Log notification to internal logs.
        
        Args:
            payload: Notification payload
        """
        try:
            logger.info(
                f"Notification: {payload['type']}",
                extra={
                    "notification_type": payload["type"],
                    "notification_data": payload["data"],
                    "service": payload["service"],
                }
            )
        except Exception as e:
            logger.error(f"Failed to log notification: {str(e)}")
    
    def _get_notification_type_for_status(
        self, 
        status: SubmissionStatusEnum
    ) -> NotificationType:
        """
        Get notification type for submission status.
        
        Args:
            status: Submission status
            
        Returns:
            Corresponding notification type
        """
        status_mapping = {
            SubmissionStatusEnum.SUBMITTED: NotificationType.SUBMISSION_SUBMITTED,
            SubmissionStatusEnum.ACCEPTED: NotificationType.SUBMISSION_ACCEPTED,
            SubmissionStatusEnum.REJECTED: NotificationType.SUBMISSION_REJECTED,
            SubmissionStatusEnum.FAILED: NotificationType.SUBMISSION_FAILED,
        }
        
        return status_mapping.get(status, NotificationType.SUBMISSION_CREATED)
    
    async def send_batch_notification(
        self,
        notifications: List[Dict[str, Any]]
    ) -> None:
        """
        Send multiple notifications in batch.
        
        Args:
            notifications: List of notification data
        """
        batch_payload = {
            "type": "batch_notification",
            "notifications": notifications,
            "service": "tax-filing-service",
            "timestamp": datetime.utcnow().isoformat(),
            "count": len(notifications),
        }
        
        try:
            response = await self.client.post(
                self.webhook_url + "/batch",
                json=batch_payload,
                headers={
                    "Content-Type": "application/json",
                    "X-Service": "tax-filing-service",
                    "X-Notification-Type": "batch",
                }
            )
            
            if response.status_code == 200:
                logger.debug(f"Batch notification sent: {len(notifications)} items")
            else:
                logger.warning(
                    f"Batch notification failed: {response.status_code} - {response.text}"
                )
                
        except Exception as e:
            logger.error(f"Batch notification error: {str(e)}")
    
    def configure_channels(self, channels: List[NotificationChannel]) -> None:
        """
        Configure enabled notification channels.
        
        Args:
            channels: List of channels to enable
        """
        self.enabled_channels = channels
        logger.info(f"Notification channels configured: {channels}")
    
    def configure_webhook_url(self, url: str) -> None:
        """
        Configure webhook URL.
        
        Args:
            url: Webhook URL
        """
        self.webhook_url = url
        logger.info(f"Webhook URL configured: {url}")
    
    async def test_notification_channels(self) -> Dict[str, bool]:
        """
        Test all configured notification channels.
        
        Returns:
            Dictionary with channel test results
        """
        results = {}
        
        test_payload = {
            "type": "test_notification",
            "data": {
                "message": "Test notification from tax filing service",
                "timestamp": datetime.utcnow().isoformat(),
            },
            "service": "tax-filing-service",
        }
        
        for channel in self.enabled_channels:
            try:
                if channel == NotificationChannel.HTTP_WEBHOOK:
                    await self._send_webhook_notification(test_payload)
                    results[channel] = True
                elif channel == NotificationChannel.INTERNAL_LOG:
                    await self._log_notification(test_payload)
                    results[channel] = True
                else:
                    results[channel] = False
                    
            except Exception as e:
                logger.error(f"Channel test failed for {channel}: {str(e)}")
                results[channel] = False
        
        return results
