"""
Tax submission service for handling tax return submissions.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional
from uuid import UUID, uuid4

from app.core.exceptions import SubmissionError, ValidationError
from app.core.logging import get_logger
from app.integrations.factory import ProvinceIntegrationFactory
from app.models.tax_submission import (
    TaxSubmission,
    TaxSubmissionCreate,
    TaxSubmissionResponse,
    SubmissionStatus,
    SubmissionStatusEnum,
    BatchSubmissionRequest,
    BatchSubmissionResponse,
)
from app.services.province_manager import ProvinceManagerService
from app.services.validation import ValidationService

logger = get_logger(__name__)


class TaxSubmissionService:
    """
    Service for handling tax submission operations.
    
    This service manages the entire tax submission lifecycle including
    validation, submission to provincial bureaus, and status tracking.
    """
    
    def __init__(
        self,
        province_manager: ProvinceManagerService,
        validation_service: ValidationService,
    ):
        """
        Initialize the tax submission service.
        
        Args:
            province_manager: Province configuration manager
            validation_service: Data validation service
        """
        self.province_manager = province_manager
        self.validation_service = validation_service
        
        # In-memory storage for demo purposes
        # In production, this would be a database
        self.submissions: Dict[UUID, TaxSubmission] = {}
        
        logger.info("Tax submission service initialized")
    
    async def submit_tax_return(
        self, 
        submission_data: TaxSubmissionCreate
    ) -> TaxSubmissionResponse:
        """
        Submit a tax return to the appropriate provincial bureau.
        
        Args:
            submission_data: Tax submission data
            
        Returns:
            Submission response with ID and status
            
        Raises:
            ValidationError: If submission data is invalid
            SubmissionError: If submission fails
        """
        # Create submission record
        submission = TaxSubmission(
            id=uuid4(),
            **submission_data.dict(),
            status=SubmissionStatusEnum.PENDING,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        try:
            # Validate submission data
            validation_result = await self.validation_service.validate_submission(
                submission
            )
            
            if not validation_result.is_valid:
                raise ValidationError(
                    "Submission validation failed",
                    details={
                        "errors": [error.model_dump() for error in validation_result.errors],
                        "warnings": [warning.model_dump() for warning in validation_result.warnings],
                    }
                )
            
            # Store submission
            self.submissions[submission.id] = submission
            
            # Get province configuration
            province_config = await self.province_manager.get_province_config(
                submission.province_code
            )
            
            # Create province integration
            integration = ProvinceIntegrationFactory.create_integration(province_config)
            
            # Submit to provincial bureau
            async with integration:
                submission.status = SubmissionStatusEnum.PROCESSING
                submission.updated_at = datetime.utcnow()
                
                result = await integration.submit_tax_return(submission)
                
                # Update submission with result
                submission.external_id = result.get("external_id")
                submission.status = result.get("status", SubmissionStatusEnum.SUBMITTED)
                submission.submitted_at = datetime.utcnow()
                submission.updated_at = datetime.utcnow()
                
                logger.info(
                    f"Successfully submitted tax return {submission.id} "
                    f"to {submission.province_code}"
                )
                
                return TaxSubmissionResponse(
                    submission_id=submission.id,
                    status=submission.status,
                    message=result.get("message", "Tax return submitted successfully"),
                    external_id=submission.external_id,
                    estimated_processing_time=result.get("estimated_processing_time"),
                )
        
        except ValidationError:
            submission.status = SubmissionStatusEnum.FAILED
            submission.error_message = "Validation failed"
            submission.updated_at = datetime.utcnow()
            raise
            
        except Exception as e:
            submission.status = SubmissionStatusEnum.FAILED
            submission.error_message = str(e)
            submission.updated_at = datetime.utcnow()
            
            logger.error(f"Tax submission failed: {str(e)}")
            raise SubmissionError(
                f"Failed to submit tax return: {str(e)}",
                submission_id=str(submission.id)
            )
    
    async def get_submission_status(self, submission_id: UUID) -> SubmissionStatus:
        """
        Get the current status of a tax submission.
        
        Args:
            submission_id: Submission ID
            
        Returns:
            Current submission status
            
        Raises:
            SubmissionError: If submission not found or status check fails
        """
        if submission_id not in self.submissions:
            raise SubmissionError(
                f"Submission {submission_id} not found",
                submission_id=str(submission_id)
            )
        
        submission = self.submissions[submission_id]
        
        try:
            # If submission has external ID, check with provincial bureau
            if submission.external_id and submission.status in [
                SubmissionStatusEnum.SUBMITTED,
                SubmissionStatusEnum.PROCESSING,
            ]:
                province_config = await self.province_manager.get_province_config(
                    submission.province_code
                )
                
                integration = ProvinceIntegrationFactory.create_integration(province_config)
                
                async with integration:
                    status = await integration.get_submission_status(submission.external_id)
                    
                    # Update local submission status
                    submission.status = status.status
                    if status.processed_at:
                        submission.processed_at = status.processed_at
                    if status.error_message:
                        submission.error_message = status.error_message
                    submission.updated_at = datetime.utcnow()
                    
                    return status
            
            # Return local status
            return SubmissionStatus(
                submission_id=submission.id,
                status=submission.status,
                external_id=submission.external_id,
                submitted_at=submission.submitted_at,
                processed_at=submission.processed_at,
                error_message=submission.error_message,
            )
            
        except Exception as e:
            logger.error(f"Failed to get submission status: {str(e)}")
            raise SubmissionError(
                f"Failed to get submission status: {str(e)}",
                submission_id=str(submission_id)
            )
    
    async def batch_submit(
        self, 
        batch_request: BatchSubmissionRequest
    ) -> BatchSubmissionResponse:
        """
        Submit multiple tax returns in batch.
        
        Args:
            batch_request: Batch submission request
            
        Returns:
            Batch submission response
        """
        batch_id = batch_request.batch_id or str(uuid4())
        successful_submissions = []
        failed_submissions = []
        
        # Process submissions concurrently
        tasks = []
        for submission_data in batch_request.submissions:
            task = self._submit_single_with_error_handling(submission_data)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_submissions.append({
                    "index": i,
                    "error": str(result),
                    "submission_data": batch_request.submissions[i].dict(),
                })
            else:
                successful_submissions.append(result.submission_id)
        
        logger.info(
            f"Batch {batch_id} completed: "
            f"{len(successful_submissions)} successful, "
            f"{len(failed_submissions)} failed"
        )
        
        return BatchSubmissionResponse(
            batch_id=batch_id,
            total_submissions=len(batch_request.submissions),
            successful_submissions=successful_submissions,
            failed_submissions=failed_submissions,
        )
    
    async def _submit_single_with_error_handling(
        self, 
        submission_data: TaxSubmissionCreate
    ) -> TaxSubmissionResponse:
        """Submit a single tax return with error handling."""
        try:
            return await self.submit_tax_return(submission_data)
        except Exception as e:
            logger.error(f"Single submission failed: {str(e)}")
            raise e
    
    async def retry_failed_submission(self, submission_id: UUID) -> TaxSubmissionResponse:
        """
        Retry a failed tax submission.
        
        Args:
            submission_id: Submission ID to retry
            
        Returns:
            Submission response
            
        Raises:
            SubmissionError: If submission cannot be retried
        """
        if submission_id not in self.submissions:
            raise SubmissionError(
                f"Submission {submission_id} not found",
                submission_id=str(submission_id)
            )
        
        submission = self.submissions[submission_id]
        
        if submission.status not in [SubmissionStatusEnum.FAILED, SubmissionStatusEnum.REJECTED]:
            raise SubmissionError(
                f"Submission {submission_id} cannot be retried (status: {submission.status})",
                submission_id=str(submission_id)
            )
        
        # Increment retry count
        submission.retry_count += 1
        
        if submission.retry_count > 3:  # Max 3 retries
            raise SubmissionError(
                f"Submission {submission_id} has exceeded maximum retry attempts",
                submission_id=str(submission_id)
            )
        
        # Reset status and retry
        submission.status = SubmissionStatusEnum.PENDING
        submission.error_message = None
        submission.updated_at = datetime.utcnow()
        
        # Create submission data for retry
        submission_data = TaxSubmissionCreate(
            province_code=submission.province_code,
            company_info=submission.company_info,
            tax_period=submission.tax_period,
            tax_data=submission.tax_data,
            additional_data=submission.additional_data,
            notes=submission.notes,
        )
        
        return await self.submit_tax_return(submission_data)
    
    async def cancel_submission(self, submission_id: UUID) -> bool:
        """
        Cancel a pending or processing submission.
        
        Args:
            submission_id: Submission ID to cancel
            
        Returns:
            True if cancelled successfully
            
        Raises:
            SubmissionError: If submission cannot be cancelled
        """
        if submission_id not in self.submissions:
            raise SubmissionError(
                f"Submission {submission_id} not found",
                submission_id=str(submission_id)
            )
        
        submission = self.submissions[submission_id]
        
        if submission.status not in [
            SubmissionStatusEnum.PENDING,
            SubmissionStatusEnum.PROCESSING,
        ]:
            raise SubmissionError(
                f"Submission {submission_id} cannot be cancelled (status: {submission.status})",
                submission_id=str(submission_id)
            )
        
        submission.status = SubmissionStatusEnum.CANCELLED
        submission.updated_at = datetime.utcnow()
        
        logger.info(f"Cancelled submission {submission_id}")
        return True
    
    def get_submission(self, submission_id: UUID) -> Optional[TaxSubmission]:
        """Get a submission by ID."""
        return self.submissions.get(submission_id)
    
    def list_submissions(
        self, 
        province_code: Optional[str] = None,
        status: Optional[SubmissionStatusEnum] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[TaxSubmission]:
        """List submissions with optional filtering."""
        submissions = list(self.submissions.values())
        
        # Apply filters
        if province_code:
            submissions = [s for s in submissions if s.province_code == province_code]
        
        if status:
            submissions = [s for s in submissions if s.status == status]
        
        # Sort by creation time (newest first)
        submissions.sort(key=lambda x: x.created_at, reverse=True)
        
        # Apply pagination
        return submissions[offset:offset + limit]
