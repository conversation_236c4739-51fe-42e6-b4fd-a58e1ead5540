"""
Data validation service for tax submissions.
"""

import re
from typing import Any, Dict, List

from app.core.logging import get_logger
from app.integrations.factory import ProvinceIntegrationFactory
from app.models.tax_submission import TaxSubmission
from app.models.validation import (
    ValidationResult,
    ValidationLevel,
    ValidationRequest,
    BatchValidationRequest,
    BatchValidationResponse,
)
from app.services.province_manager import ProvinceManagerService

logger = get_logger(__name__)


class ValidationService:
    """
    Service for validating tax submission data.
    
    This service provides comprehensive validation including:
    - Basic data format validation
    - Business rule validation
    - Province-specific validation
    """
    
    def __init__(self, province_manager: ProvinceManagerService):
        """
        Initialize the validation service.
        
        Args:
            province_manager: Province configuration manager
        """
        self.province_manager = province_manager
        
        # Common validation patterns
        self.patterns = {
            "tax_id": re.compile(r'^\d{15}|\d{18}|\d{20}$'),  # 15, 18, or 20 digits
            "phone": re.compile(r'^1[3-9]\d{9}$'),  # Chinese mobile phone
            "email": re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
        }
        
        logger.info("Validation service initialized")
    
    async def validate_submission(self, submission: TaxSubmission) -> ValidationResult:
        """
        Validate a complete tax submission.
        
        Args:
            submission: Tax submission to validate
            
        Returns:
            Validation result with errors, warnings, and info
        """
        result = ValidationResult(
            is_valid=True,
            province_code=submission.province_code
        )
        
        try:
            # Basic validation
            self._validate_basic_structure(submission, result)
            self._validate_company_info(submission, result)
            self._validate_tax_period(submission, result)
            self._validate_tax_data(submission, result)
            
            # Province-specific validation
            await self._validate_province_specific(submission, result)
            
            # Business rules validation
            self._validate_business_rules(submission, result)
            
            result.update_summary()
            
            logger.debug(
                f"Validation completed for {submission.province_code}: "
                f"{len(result.errors)} errors, {len(result.warnings)} warnings"
            )
            
        except Exception as e:
            logger.error(f"Validation failed: {str(e)}")
            result.add_error(
                "validation_system",
                f"Validation system error: {str(e)}",
                rule_name="system_error"
            )
        
        return result
    
    def _validate_basic_structure(self, submission: TaxSubmission, result: ValidationResult):
        """Validate basic submission structure."""
        # Check required fields
        if not submission.province_code:
            result.add_error("province_code", "Province code is required")
        
        if not submission.company_info:
            result.add_error("company_info", "Company information is required")
        
        if not submission.tax_period:
            result.add_error("tax_period", "Tax period is required")
        
        if not submission.tax_data or len(submission.tax_data) == 0:
            result.add_error("tax_data", "At least one tax item is required")
        
        # Validate province code format
        if submission.province_code and len(submission.province_code) != 2:
            result.add_error(
                "province_code",
                "Province code must be 2 characters",
                rule_name="province_code_format"
            )
    
    def _validate_company_info(self, submission: TaxSubmission, result: ValidationResult):
        """Validate company information."""
        company = submission.company_info
        
        if not company:
            return
        
        # Required fields
        if not company.company_name or len(company.company_name.strip()) == 0:
            result.add_error("company_info.company_name", "Company name is required")
        
        if not company.tax_id:
            result.add_error("company_info.tax_id", "Tax ID is required")
        elif not self.patterns["tax_id"].match(company.tax_id):
            result.add_error(
                "company_info.tax_id",
                "Tax ID must be 15, 18, or 20 digits",
                rule_name="tax_id_format"
            )
        
        if not company.registration_number:
            result.add_error("company_info.registration_number", "Registration number is required")
        
        if not company.legal_representative:
            result.add_error("company_info.legal_representative", "Legal representative is required")
        
        if not company.address:
            result.add_error("company_info.address", "Company address is required")
        
        # Optional field validation
        if company.phone and not self.patterns["phone"].match(company.phone):
            result.add_warning(
                "company_info.phone",
                "Phone number format may be invalid",
                rule_name="phone_format"
            )
        
        if company.email and not self.patterns["email"].match(company.email):
            result.add_warning(
                "company_info.email",
                "Email format may be invalid",
                rule_name="email_format"
            )
    
    def _validate_tax_period(self, submission: TaxSubmission, result: ValidationResult):
        """Validate tax period information."""
        period = submission.tax_period
        
        if not period:
            return
        
        # Validate year
        if period.year < 2000 or period.year > 2100:
            result.add_error(
                "tax_period.year",
                "Tax year must be between 2000 and 2100",
                rule_name="year_range"
            )
        
        # Validate period type consistency
        if period.period_type == "monthly":
            if not period.month:
                result.add_error(
                    "tax_period.month",
                    "Month is required for monthly period type"
                )
            elif period.month < 1 or period.month > 12:
                result.add_error(
                    "tax_period.month",
                    "Month must be between 1 and 12"
                )
            
            if period.quarter:
                result.add_warning(
                    "tax_period.quarter",
                    "Quarter should not be specified for monthly period"
                )
        
        elif period.period_type == "quarterly":
            if not period.quarter:
                result.add_error(
                    "tax_period.quarter",
                    "Quarter is required for quarterly period type"
                )
            elif period.quarter < 1 or period.quarter > 4:
                result.add_error(
                    "tax_period.quarter",
                    "Quarter must be between 1 and 4"
                )
            
            if period.month:
                result.add_warning(
                    "tax_period.month",
                    "Month should not be specified for quarterly period"
                )
        
        elif period.period_type == "yearly":
            if period.month or period.quarter:
                result.add_warning(
                    "tax_period",
                    "Month and quarter should not be specified for yearly period"
                )
    
    def _validate_tax_data(self, submission: TaxSubmission, result: ValidationResult):
        """Validate tax calculation data."""
        if not submission.tax_data:
            return
        
        for i, tax_item in enumerate(submission.tax_data):
            field_prefix = f"tax_data[{i}]"
            
            # Validate amounts
            if tax_item.taxable_amount < 0:
                result.add_error(
                    f"{field_prefix}.taxable_amount",
                    "Taxable amount cannot be negative"
                )
            
            if tax_item.tax_rate < 0 or tax_item.tax_rate > 1:
                result.add_error(
                    f"{field_prefix}.tax_rate",
                    "Tax rate must be between 0 and 1"
                )
            
            if tax_item.tax_amount < 0:
                result.add_error(
                    f"{field_prefix}.tax_amount",
                    "Tax amount cannot be negative"
                )
            
            if tax_item.deductions and tax_item.deductions < 0:
                result.add_error(
                    f"{field_prefix}.deductions",
                    "Deductions cannot be negative"
                )
            
            if tax_item.credits and tax_item.credits < 0:
                result.add_error(
                    f"{field_prefix}.credits",
                    "Credits cannot be negative"
                )
            
            if tax_item.final_amount < 0:
                result.add_error(
                    f"{field_prefix}.final_amount",
                    "Final amount cannot be negative"
                )
            
            # Validate calculation consistency
            expected_tax = tax_item.taxable_amount * tax_item.tax_rate
            if abs(tax_item.tax_amount - expected_tax) > 0.01:
                result.add_warning(
                    f"{field_prefix}.tax_amount",
                    f"Tax amount {tax_item.tax_amount} does not match calculation {expected_tax:.2f}",
                    rule_name="tax_calculation_mismatch"
                )
            
            expected_final = tax_item.tax_amount - (tax_item.deductions or 0) - (tax_item.credits or 0)
            if abs(tax_item.final_amount - expected_final) > 0.01:
                result.add_warning(
                    f"{field_prefix}.final_amount",
                    f"Final amount {tax_item.final_amount} does not match calculation {expected_final:.2f}",
                    rule_name="final_amount_mismatch"
                )
    
    async def _validate_province_specific(self, submission: TaxSubmission, result: ValidationResult):
        """Validate province-specific requirements."""
        try:
            # Check if province integration is available
            if not ProvinceIntegrationFactory.is_province_supported(submission.province_code):
                result.add_warning(
                    "province_code",
                    f"Province {submission.province_code} integration not available",
                    rule_name="province_not_supported"
                )
                return
            
            # Get province configuration
            province_config = await self.province_manager.get_province_config(
                submission.province_code
            )
            
            # Create integration and validate
            integration = ProvinceIntegrationFactory.create_integration(province_config)
            
            async with integration:
                province_result = await integration.validate_submission_data(submission)
                
                # Merge results
                result.errors.extend(province_result.errors)
                result.warnings.extend(province_result.warnings)
                result.info.extend(province_result.info)
                
                if not province_result.is_valid:
                    result.is_valid = False
                    
        except Exception as e:
            logger.warning(f"Province-specific validation failed: {str(e)}")
            result.add_warning(
                "province_validation",
                f"Province-specific validation unavailable: {str(e)}",
                rule_name="province_validation_error"
            )
    
    def _validate_business_rules(self, submission: TaxSubmission, result: ValidationResult):
        """Validate business rules."""
        # Check for duplicate tax types
        tax_types = [item.tax_type for item in submission.tax_data]
        if len(tax_types) != len(set(tax_types)):
            result.add_error(
                "tax_data",
                "Duplicate tax types are not allowed",
                rule_name="duplicate_tax_types"
            )
        
        # Check total amounts
        total_tax = sum(item.tax_amount for item in submission.tax_data)
        total_final = sum(item.final_amount for item in submission.tax_data)
        
        if total_tax > 10000000:  # 10 million limit
            result.add_warning(
                "tax_data",
                f"Total tax amount {total_tax} is very high",
                rule_name="high_tax_amount"
            )
        
        if total_final > total_tax:
            result.add_error(
                "tax_data",
                "Total final amount cannot exceed total tax amount",
                rule_name="final_exceeds_tax"
            )
    
    async def validate_data(self, request: ValidationRequest) -> ValidationResult:
        """
        Validate arbitrary data against province requirements.
        
        Args:
            request: Validation request
            
        Returns:
            Validation result
        """
        result = ValidationResult(
            is_valid=True,
            province_code=request.province_code
        )
        
        try:
            # Create a temporary submission object for validation
            # This is a simplified approach - in practice, you might want
            # more sophisticated data mapping
            
            # Basic structure validation
            if "company_info" not in request.data:
                result.add_error("company_info", "Company information is required")
            
            if "tax_period" not in request.data:
                result.add_error("tax_period", "Tax period is required")
            
            if "tax_data" not in request.data:
                result.add_error("tax_data", "Tax data is required")
            
            result.update_summary()
            
        except Exception as e:
            logger.error(f"Data validation failed: {str(e)}")
            result.add_error(
                "validation_system",
                f"Validation system error: {str(e)}",
                rule_name="system_error"
            )
        
        return result
    
    async def batch_validate(
        self, 
        request: BatchValidationRequest
    ) -> BatchValidationResponse:
        """
        Validate multiple submissions in batch.
        
        Args:
            request: Batch validation request
            
        Returns:
            Batch validation response
        """
        results = []
        valid_count = 0
        invalid_count = 0
        
        for i, submission_data in enumerate(request.submissions):
            try:
                # Create validation request for each submission
                validation_request = ValidationRequest(
                    province_code=request.province_code,
                    data=submission_data,
                    validation_level=request.validation_level,
                )
                
                result = await self.validate_data(validation_request)
                results.append(result)
                
                if result.is_valid:
                    valid_count += 1
                else:
                    invalid_count += 1
                    
                # Stop on first error if requested
                if request.stop_on_first_error and not result.is_valid:
                    break
                    
            except Exception as e:
                logger.error(f"Batch validation failed for item {i}: {str(e)}")
                error_result = ValidationResult(
                    is_valid=False,
                    province_code=request.province_code
                )
                error_result.add_error(
                    "validation_system",
                    f"Validation failed: {str(e)}",
                    rule_name="system_error"
                )
                results.append(error_result)
                invalid_count += 1
        
        # Create summary
        summary = {
            "total_processed": len(results),
            "valid_submissions": valid_count,
            "invalid_submissions": invalid_count,
            "validation_level": request.validation_level,
            "stopped_early": request.stop_on_first_error and invalid_count > 0,
        }
        
        return BatchValidationResponse(
            province_code=request.province_code,
            total_submissions=len(request.submissions),
            valid_submissions=valid_count,
            invalid_submissions=invalid_count,
            results=results,
            summary=summary,
        )
