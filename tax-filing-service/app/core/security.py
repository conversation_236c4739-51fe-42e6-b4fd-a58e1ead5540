"""
Security and authentication utilities.
"""

import secrets
from datetime import datetime, timedelta
from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext

from app.core.config import get_settings
from app.core.logging import get_logger
from app.models.auth import ServiceAuth

logger = get_logger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer token scheme
security = HTTPBearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Generate password hash."""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token.
    
    Args:
        data: Data to encode in the token
        expires_delta: Token expiration time
        
    Returns:
        Encoded JWT token
    """
    settings = get_settings()
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=24)
    
    to_encode.update({"exp": expire})
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.SECRET_KEY, 
        algorithm="HS256"
    )
    
    return encoded_jwt


def verify_token(token: str) -> dict:
    """
    Verify and decode a JWT token.
    
    Args:
        token: JWT token to verify
        
    Returns:
        Decoded token payload
        
    Raises:
        HTTPException: If token is invalid
    """
    settings = get_settings()
    
    try:
        payload = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=["HS256"]
        )
        return payload
        
    except JWTError as e:
        logger.warning(f"Token verification failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


def generate_api_key() -> str:
    """Generate a secure API key."""
    return secrets.token_urlsafe(32)


def verify_api_key(api_key: str) -> bool:
    """
    Verify an API key.
    
    Args:
        api_key: API key to verify
        
    Returns:
        True if valid, False otherwise
    """
    settings = get_settings()
    
    # In production, you would check against a database
    # For now, we'll check against the configured API key
    return api_key == settings.API_KEY


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> dict:
    """
    Get current authenticated user from JWT token.
    
    Args:
        credentials: HTTP authorization credentials
        
    Returns:
        User information from token
        
    Raises:
        HTTPException: If authentication fails
    """
    token = credentials.credentials
    payload = verify_token(token)
    
    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return {
        "user_id": user_id,
        "permissions": payload.get("permissions", []),
        "service": payload.get("service"),
    }


async def get_api_key_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> ServiceAuth:
    """
    Authenticate using API key.
    
    Args:
        credentials: HTTP authorization credentials
        
    Returns:
        Service authentication information
        
    Raises:
        HTTPException: If authentication fails
    """
    api_key = credentials.credentials
    
    if not verify_api_key(api_key):
        logger.warning(f"Invalid API key attempted: {api_key[:8]}...")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # In production, you would fetch service info from database
    return ServiceAuth(
        service_name="go-backend",
        api_key=api_key,
        permissions=["tax:submit", "tax:status", "provinces:read"],
        is_active=True,
    )


def require_permissions(required_permissions: list[str]):
    """
    Decorator to require specific permissions.
    
    Args:
        required_permissions: List of required permissions
        
    Returns:
        Dependency function
    """
    async def permission_checker(
        current_user: dict = Depends(get_current_user)
    ) -> dict:
        user_permissions = current_user.get("permissions", [])
        
        for permission in required_permissions:
            if permission not in user_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission required: {permission}",
                )
        
        return current_user
    
    return permission_checker


class RateLimiter:
    """Simple in-memory rate limiter."""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        """
        Initialize rate limiter.
        
        Args:
            max_requests: Maximum requests per window
            window_seconds: Time window in seconds
        """
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}  # {client_id: [(timestamp, count), ...]}
    
    def is_allowed(self, client_id: str) -> bool:
        """
        Check if request is allowed for client.
        
        Args:
            client_id: Client identifier
            
        Returns:
            True if allowed, False if rate limited
        """
        now = datetime.utcnow()
        window_start = now - timedelta(seconds=self.window_seconds)
        
        # Clean old entries
        if client_id in self.requests:
            self.requests[client_id] = [
                (timestamp, count) for timestamp, count in self.requests[client_id]
                if timestamp > window_start
            ]
        else:
            self.requests[client_id] = []
        
        # Count requests in current window
        total_requests = sum(count for _, count in self.requests[client_id])
        
        if total_requests >= self.max_requests:
            return False
        
        # Add current request
        self.requests[client_id].append((now, 1))
        return True


# Global rate limiter instance
rate_limiter = RateLimiter(max_requests=1000, window_seconds=3600)  # 1000 requests per hour


async def check_rate_limit(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> None:
    """
    Check rate limit for current client.
    
    Args:
        credentials: HTTP authorization credentials
        
    Raises:
        HTTPException: If rate limit exceeded
    """
    # Use API key as client identifier
    client_id = credentials.credentials[:16]  # First 16 chars of API key
    
    if not rate_limiter.is_allowed(client_id):
        logger.warning(f"Rate limit exceeded for client: {client_id}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded",
            headers={"Retry-After": "3600"},
        )


def create_service_token(service_name: str, permissions: list[str]) -> str:
    """
    Create a service-to-service authentication token.
    
    Args:
        service_name: Name of the service
        permissions: List of permissions
        
    Returns:
        JWT token for service authentication
    """
    data = {
        "sub": service_name,
        "service": service_name,
        "permissions": permissions,
        "type": "service",
    }
    
    return create_access_token(data, expires_delta=timedelta(days=30))


def validate_request_signature(
    payload: str, 
    signature: str, 
    secret: str
) -> bool:
    """
    Validate request signature for webhook security.
    
    Args:
        payload: Request payload
        signature: Provided signature
        secret: Shared secret
        
    Returns:
        True if signature is valid
    """
    import hmac
    import hashlib
    
    expected_signature = hmac.new(
        secret.encode(),
        payload.encode(),
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(signature, expected_signature)


# Security dependencies for different access levels
async def require_api_key(
    service_auth: ServiceAuth = Depends(get_api_key_user)
) -> ServiceAuth:
    """Require valid API key authentication."""
    return service_auth


async def require_admin_access(
    current_user: dict = Depends(require_permissions(["admin:write"]))
) -> dict:
    """Require admin-level access."""
    return current_user


async def require_tax_access(
    service_auth: ServiceAuth = Depends(get_api_key_user)
) -> ServiceAuth:
    """Require tax submission access."""
    required_perms = ["tax:submit", "tax:status"]
    
    for perm in required_perms:
        if perm not in service_auth.permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission required: {perm}",
            )
    
    return service_auth
