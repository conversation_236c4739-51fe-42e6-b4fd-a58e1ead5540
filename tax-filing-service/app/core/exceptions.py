"""
Custom exceptions and global exception handlers for the Tax Filing Service.
"""

from typing import Any, Dict, Optional
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from .logging import get_logger

logger = get_logger(__name__)


class TaxFilingException(Exception):
    """Base exception for tax filing service."""
    
    def __init__(
        self,
        message: str,
        error_code: str = "GENERAL_ERROR",
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ProvinceIntegrationError(TaxFilingException):
    """Exception for provincial tax bureau integration errors."""
    
    def __init__(
        self,
        province: str,
        message: str,
        error_code: str = "PROVINCE_INTEGRATION_ERROR",
        details: Optional[Dict[str, Any]] = None,
    ):
        self.province = province
        super().__init__(message, error_code, details)


class ValidationError(TaxFilingException):
    """Exception for data validation errors."""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.field = field
        super().__init__(message, "VALIDATION_ERROR", details)


class AuthenticationError(TaxFilingException):
    """Exception for authentication errors."""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(message, "AUTHENTICATION_ERROR")


class SubmissionError(TaxFilingException):
    """Exception for tax submission errors."""
    
    def __init__(
        self,
        message: str,
        submission_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.submission_id = submission_id
        super().__init__(message, "SUBMISSION_ERROR", details)


class ExternalServiceError(TaxFilingException):
    """Exception for external service communication errors."""
    
    def __init__(
        self,
        service: str,
        message: str,
        status_code: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.service = service
        self.status_code = status_code
        super().__init__(message, "EXTERNAL_SERVICE_ERROR", details)


def setup_exception_handlers(app: FastAPI) -> None:
    """Setup global exception handlers for the FastAPI application."""
    
    @app.exception_handler(TaxFilingException)
    async def tax_filing_exception_handler(request: Request, exc: TaxFilingException):
        """Handle custom tax filing exceptions."""
        logger.error(
            f"Tax filing error: {exc.message}",
            error_code=exc.error_code,
            details=exc.details,
            path=request.url.path,
        )
        
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "error": {
                    "code": exc.error_code,
                    "message": exc.message,
                    "details": exc.details,
                }
            },
        )
    
    @app.exception_handler(AuthenticationError)
    async def authentication_exception_handler(request: Request, exc: AuthenticationError):
        """Handle authentication errors."""
        logger.warning(
            f"Authentication error: {exc.message}",
            path=request.url.path,
        )
        
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={
                "error": {
                    "code": exc.error_code,
                    "message": exc.message,
                }
            },
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """Handle request validation errors."""
        logger.warning(
            f"Validation error: {exc.errors()}",
            path=request.url.path,
        )
        
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content={
                "error": {
                    "code": "VALIDATION_ERROR",
                    "message": "Request validation failed",
                    "details": exc.errors(),
                }
            },
        )
    
    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(request: Request, exc: StarletteHTTPException):
        """Handle HTTP exceptions."""
        logger.warning(
            f"HTTP error {exc.status_code}: {exc.detail}",
            path=request.url.path,
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "code": "HTTP_ERROR",
                    "message": exc.detail,
                }
            },
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle unexpected exceptions."""
        logger.exception(
            f"Unexpected error: {str(exc)}",
            path=request.url.path,
        )
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": "An unexpected error occurred",
                }
            },
        )
