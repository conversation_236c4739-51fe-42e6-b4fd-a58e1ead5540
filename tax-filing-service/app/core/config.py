"""
Core configuration settings for the Tax Filing Service.
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, field_validator, ConfigDict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # Application settings
    APP_NAME: str = "Tax Filing Service"
    DEBUG: bool = Field(default=False, env="DEBUG")
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8082, env="PORT")
    
    # Security settings
    SECRET_KEY: str = Field(..., env="SECRET_KEY")
    API_KEY: str = Field(..., env="API_KEY")
    ALLOWED_HOSTS: List[str] = Field(default=["*"], env="ALLOWED_HOSTS")
    
    # Database settings (optional, for storing configurations and status)
    DATABASE_URL: Optional[str] = Field(default=None, env="DATABASE_URL")
    
    # External service settings
    GO_BACKEND_URL: str = Field(default="http://localhost:8081", env="GO_BACKEND_URL")
    
    # Logging settings
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: Optional[str] = Field(default=None, env="LOG_FILE")
    
    # HTTP client settings
    HTTP_TIMEOUT: int = Field(default=30, env="HTTP_TIMEOUT")
    MAX_RETRIES: int = Field(default=3, env="MAX_RETRIES")
    RETRY_DELAY: float = Field(default=1.0, env="RETRY_DELAY")
    
    # Provincial tax bureau settings
    PROVINCE_CONFIG_PATH: str = Field(
        default="config/provinces.json", 
        env="PROVINCE_CONFIG_PATH"
    )
    
    @field_validator("ALLOWED_HOSTS", mode="before")
    @classmethod
    def parse_allowed_hosts(cls, v):
        """Parse allowed hosts from string or list."""
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v

    @field_validator("LOG_LEVEL")
    @classmethod
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()
    
    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
    )


class ProvinceSettings(BaseSettings):
    """Settings for provincial tax bureau integrations."""
    
    # Beijing Tax Bureau
    BEIJING_TAX_URL: str = Field(
        default="https://etax.beijing.chinatax.gov.cn:8443/",
        env="BEIJING_TAX_URL"
    )
    BEIJING_TAX_API_KEY: Optional[str] = Field(default=None, env="BEIJING_TAX_API_KEY")
    BEIJING_TAX_USERNAME: Optional[str] = Field(default=None, env="BEIJING_TAX_USERNAME")
    BEIJING_TAX_PASSWORD: Optional[str] = Field(default=None, env="BEIJING_TAX_PASSWORD")
    
    # Add more provinces as needed
    # SHANGHAI_TAX_URL: str = Field(default="", env="SHANGHAI_TAX_URL")
    # GUANGDONG_TAX_URL: str = Field(default="", env="GUANGDONG_TAX_URL")
    
    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
    )


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()


@lru_cache()
def get_province_settings() -> ProvinceSettings:
    """Get cached province settings."""
    return ProvinceSettings()
