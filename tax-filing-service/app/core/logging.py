"""
Logging configuration for the Tax Filing Service.
"""

import sys
from pathlib import Path
from typing import Optional

from loguru import logger

from .config import Settings


def setup_logging(settings: Settings) -> None:
    """Setup logging configuration using loguru."""
    
    # Remove default handler
    logger.remove()
    
    # Console handler
    logger.add(
        sys.stdout,
        level=settings.LOG_LEVEL,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True,
    )
    
    # File handler (if specified)
    if settings.LOG_FILE:
        log_path = Path(settings.LOG_FILE)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            settings.LOG_FILE,
            level=settings.LOG_LEVEL,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="10 MB",
            retention="30 days",
            compression="gz",
        )
    
    # Add structured logging for production
    if not settings.DEBUG:
        logger.add(
            "logs/tax-filing-service.json",
            level="INFO",
            format="{time} | {level} | {name} | {function} | {line} | {message}",
            serialize=True,
            rotation="100 MB",
            retention="90 days",
        )


def get_logger(name: str):
    """Get a logger instance for a specific module."""
    return logger.bind(name=name)
