"""
Tax submission related data models.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import Field, field_validator, model_validator

from .base import BaseModel, TimestampMixin, UUIDMixin


class SubmissionStatusEnum(str, Enum):
    """Tax submission status enumeration."""
    
    PENDING = "pending"
    PROCESSING = "processing"
    SUBMITTED = "submitted"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaxType(str, Enum):
    """Tax type enumeration."""
    
    VAT = "vat"  # 增值税
    INCOME_TAX = "income_tax"  # 所得税
    BUSINESS_TAX = "business_tax"  # 营业税
    CONSUMPTION_TAX = "consumption_tax"  # 消费税
    CUSTOMS_DUTY = "customs_duty"  # 关税
    OTHER = "other"  # 其他


class TaxPeriod(BaseModel):
    """Tax period information."""
    
    year: int = Field(..., ge=2000, le=2100, description="Tax year")
    month: Optional[int] = Field(None, ge=1, le=12, description="Tax month")
    quarter: Optional[int] = Field(None, ge=1, le=4, description="Tax quarter")
    period_type: str = Field(..., description="Period type: monthly, quarterly, yearly")
    
    @field_validator("period_type")
    @classmethod
    def validate_period_type(cls, v):
        """Validate period type."""
        valid_types = ["monthly", "quarterly", "yearly"]
        if v not in valid_types:
            raise ValueError(f"Period type must be one of: {valid_types}")
        return v


class CompanyInfo(BaseModel):
    """Company information for tax submission."""
    
    company_name: str = Field(..., description="Company name")
    tax_id: str = Field(..., description="Tax identification number")
    registration_number: str = Field(..., description="Business registration number")
    legal_representative: str = Field(..., description="Legal representative")
    address: str = Field(..., description="Company address")
    phone: Optional[str] = Field(None, description="Contact phone")
    email: Optional[str] = Field(None, description="Contact email")


class TaxData(BaseModel):
    """Tax calculation data."""
    
    tax_type: TaxType = Field(..., description="Type of tax")
    taxable_amount: float = Field(..., ge=0, description="Taxable amount")
    tax_rate: float = Field(..., ge=0, le=1, description="Tax rate")
    tax_amount: float = Field(..., ge=0, description="Calculated tax amount")
    deductions: Optional[float] = Field(0, ge=0, description="Tax deductions")
    credits: Optional[float] = Field(0, ge=0, description="Tax credits")
    final_amount: float = Field(..., ge=0, description="Final tax amount to pay")
    
    @model_validator(mode="after")
    def validate_final_amount(self):
        """Validate final amount calculation."""
        if self.tax_amount is not None and self.final_amount is not None:
            expected = self.tax_amount - (self.deductions or 0) - (self.credits or 0)
            if abs(self.final_amount - expected) > 0.01:  # Allow small floating point differences
                raise ValueError("Final amount does not match calculation")
        return self


class TaxSubmissionBase(BaseModel):
    """Base tax submission model."""
    
    province_code: str = Field(..., description="Province code")
    company_info: CompanyInfo = Field(..., description="Company information")
    tax_period: TaxPeriod = Field(..., description="Tax period")
    tax_data: List[TaxData] = Field(..., description="Tax calculation data")
    additional_data: Optional[Dict[str, Any]] = Field(None, description="Additional province-specific data")
    notes: Optional[str] = Field(None, description="Additional notes")


class TaxSubmissionCreate(TaxSubmissionBase):
    """Model for creating a new tax submission."""
    pass


class TaxSubmissionUpdate(BaseModel):
    """Model for updating a tax submission."""
    
    status: Optional[SubmissionStatusEnum] = None
    notes: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


class TaxSubmission(TaxSubmissionBase, UUIDMixin, TimestampMixin):
    """Complete tax submission model."""
    
    status: SubmissionStatusEnum = Field(default=SubmissionStatusEnum.PENDING)
    external_id: Optional[str] = Field(None, description="External system submission ID")
    submitted_at: Optional[datetime] = Field(None, description="Submission timestamp")
    processed_at: Optional[datetime] = Field(None, description="Processing completion timestamp")
    error_message: Optional[str] = Field(None, description="Error message if submission failed")
    retry_count: int = Field(default=0, description="Number of retry attempts")


class SubmissionStatus(BaseModel):
    """Submission status information."""
    
    submission_id: UUID = Field(..., description="Submission ID")
    status: SubmissionStatusEnum = Field(..., description="Current status")
    external_id: Optional[str] = Field(None, description="External system ID")
    submitted_at: Optional[datetime] = Field(None, description="Submission timestamp")
    processed_at: Optional[datetime] = Field(None, description="Processing timestamp")
    error_message: Optional[str] = Field(None, description="Error message")
    progress: Optional[Dict[str, Any]] = Field(None, description="Processing progress")


class TaxSubmissionResponse(BaseModel):
    """Tax submission response model."""
    
    submission_id: UUID = Field(..., description="Submission ID")
    status: SubmissionStatusEnum = Field(..., description="Submission status")
    message: str = Field(..., description="Response message")
    external_id: Optional[str] = Field(None, description="External system ID")
    estimated_processing_time: Optional[int] = Field(None, description="Estimated processing time in minutes")


class BatchSubmissionRequest(BaseModel):
    """Batch submission request model."""
    
    submissions: List[TaxSubmissionCreate] = Field(..., description="List of submissions")
    batch_id: Optional[str] = Field(None, description="Batch identifier")


class BatchSubmissionResponse(BaseModel):
    """Batch submission response model."""
    
    batch_id: str = Field(..., description="Batch identifier")
    total_submissions: int = Field(..., description="Total number of submissions")
    successful_submissions: List[UUID] = Field(..., description="Successfully created submission IDs")
    failed_submissions: List[Dict[str, Any]] = Field(..., description="Failed submissions with errors")
