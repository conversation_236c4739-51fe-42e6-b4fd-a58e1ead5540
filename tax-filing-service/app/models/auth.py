"""
Authentication related data models.
"""

from datetime import datetime
from typing import Optional

from pydantic import Field

from .base import BaseModel


class APIKeyAuth(BaseModel):
    """API key authentication model."""
    
    api_key: str = Field(..., description="API key")
    client_id: Optional[str] = Field(None, description="Client identifier")


class AuthToken(BaseModel):
    """Authentication token model."""
    
    access_token: str = Field(..., description="Access token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: Optional[int] = Field(None, description="Token expiration time in seconds")
    expires_at: Optional[datetime] = Field(None, description="Token expiration timestamp")
    refresh_token: Optional[str] = Field(None, description="Refresh token")
    scope: Optional[str] = Field(None, description="Token scope")


class AuthRequest(BaseModel):
    """Authentication request model."""
    
    username: Optional[str] = Field(None, description="Username")
    password: Optional[str] = Field(None, description="Password")
    api_key: Optional[str] = Field(None, description="API key")
    client_id: Optional[str] = Field(None, description="Client ID")
    client_secret: Optional[str] = Field(None, description="Client secret")
    grant_type: Optional[str] = Field(None, description="OAuth2 grant type")


class AuthResponse(BaseModel):
    """Authentication response model."""
    
    success: bool = Field(..., description="Authentication success")
    token: Optional[AuthToken] = Field(None, description="Authentication token")
    error: Optional[str] = Field(None, description="Error message")
    error_code: Optional[str] = Field(None, description="Error code")


class ServiceAuth(BaseModel):
    """Service-to-service authentication model."""
    
    service_name: str = Field(..., description="Service name")
    api_key: str = Field(..., description="Service API key")
    permissions: list[str] = Field(default_factory=list, description="Service permissions")
    expires_at: Optional[datetime] = Field(None, description="API key expiration")
    is_active: bool = Field(default=True, description="Whether API key is active")
