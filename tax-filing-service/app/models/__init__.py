"""
Data models for the Tax Filing Service.
"""

from .base import BaseModel, TimestampMixin
from .tax_submission import (
    TaxSubmission,
    TaxSubmissionCreate,
    TaxSubmissionUpdate,
    TaxSubmissionResponse,
    SubmissionStatus,
    SubmissionStatusEnum,
)
from .province import (
    ProvinceConfig,
    ProvinceConfigCreate,
    ProvinceConfigUpdate,
    ProvinceInfo,
    SupportedProvince,
)
from .validation import (
    ValidationResult,
    ValidationError,
    FieldValidation,
)
from .auth import (
    APIKeyAuth,
    AuthToken,
    AuthRequest,
)

__all__ = [
    # Base models
    "BaseModel",
    "TimestampMixin",
    
    # Tax submission models
    "TaxSubmission",
    "TaxSubmissionCreate",
    "TaxSubmissionUpdate",
    "TaxSubmissionResponse",
    "SubmissionStatus",
    "SubmissionStatusEnum",
    
    # Province models
    "ProvinceConfig",
    "ProvinceConfigCreate",
    "ProvinceConfigUpdate",
    "ProvinceInfo",
    "SupportedProvince",
    
    # Validation models
    "ValidationResult",
    "ValidationError",
    "FieldValidation",
    
    # Auth models
    "APIKeyAuth",
    "AuthToken",
    "AuthRequest",
]
