"""
Province configuration and information models.
"""

from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import Field, HttpUrl, field_validator

from .base import BaseModel, TimestampMixin


class ProvinceStatus(str, Enum):
    """Province integration status."""
    
    ACTIVE = "active"
    INACTIVE = "inactive"
    MAINTENANCE = "maintenance"
    DEPRECATED = "deprecated"


class AuthMethod(str, Enum):
    """Authentication method for province integration."""
    
    API_KEY = "api_key"
    USERNAME_PASSWORD = "username_password"
    CERTIFICATE = "certificate"
    OAUTH2 = "oauth2"


class ProvinceConfigBase(BaseModel):
    """Base province configuration model."""
    
    code: str = Field(..., description="Province code (e.g., 'BJ' for Beijing)")
    name: str = Field(..., description="Province name")
    name_en: Optional[str] = Field(None, description="Province name in English")
    base_url: HttpUrl = Field(..., description="Base URL for province tax bureau API")
    status: ProvinceStatus = Field(default=ProvinceStatus.ACTIVE, description="Integration status")
    auth_method: AuthMethod = Field(..., description="Authentication method")
    timeout: int = Field(default=30, ge=1, le=300, description="Request timeout in seconds")
    max_retries: int = Field(default=3, ge=0, le=10, description="Maximum retry attempts")
    retry_delay: float = Field(default=1.0, ge=0.1, le=60.0, description="Retry delay in seconds")
    
    @field_validator("code")
    @classmethod
    def validate_code(cls, v):
        """Validate province code format."""
        if not v.isupper() or len(v) != 2:
            raise ValueError("Province code must be 2 uppercase letters")
        return v


class ProvinceConfigCreate(ProvinceConfigBase):
    """Model for creating province configuration."""
    
    auth_config: Dict[str, Any] = Field(..., description="Authentication configuration")
    api_endpoints: Dict[str, str] = Field(..., description="API endpoint mappings")
    data_mappings: Optional[Dict[str, Any]] = Field(None, description="Data field mappings")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Province-specific validation rules")


class ProvinceConfigUpdate(BaseModel):
    """Model for updating province configuration."""
    
    name: Optional[str] = None
    name_en: Optional[str] = None
    base_url: Optional[HttpUrl] = None
    status: Optional[ProvinceStatus] = None
    auth_method: Optional[AuthMethod] = None
    timeout: Optional[int] = Field(None, ge=1, le=300)
    max_retries: Optional[int] = Field(None, ge=0, le=10)
    retry_delay: Optional[float] = Field(None, ge=0.1, le=60.0)
    auth_config: Optional[Dict[str, Any]] = None
    api_endpoints: Optional[Dict[str, str]] = None
    data_mappings: Optional[Dict[str, Any]] = None
    validation_rules: Optional[Dict[str, Any]] = None


class ProvinceConfig(ProvinceConfigBase, TimestampMixin):
    """Complete province configuration model."""
    
    auth_config: Dict[str, Any] = Field(..., description="Authentication configuration")
    api_endpoints: Dict[str, str] = Field(..., description="API endpoint mappings")
    data_mappings: Optional[Dict[str, Any]] = Field(None, description="Data field mappings")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Province-specific validation rules")
    last_health_check: Optional[str] = Field(None, description="Last health check timestamp")
    health_status: Optional[str] = Field(None, description="Health check status")


class ProvinceInfo(BaseModel):
    """Basic province information for public API."""
    
    code: str = Field(..., description="Province code")
    name: str = Field(..., description="Province name")
    name_en: Optional[str] = Field(None, description="Province name in English")
    status: ProvinceStatus = Field(..., description="Integration status")
    supported_tax_types: List[str] = Field(..., description="Supported tax types")
    features: List[str] = Field(default_factory=list, description="Supported features")


class SupportedProvince(BaseModel):
    """Supported province summary."""
    
    code: str = Field(..., description="Province code")
    name: str = Field(..., description="Province name")
    status: ProvinceStatus = Field(..., description="Integration status")
    last_updated: Optional[str] = Field(None, description="Last configuration update")


class ProvinceHealthCheck(BaseModel):
    """Province health check result."""
    
    province_code: str = Field(..., description="Province code")
    status: str = Field(..., description="Health status")
    response_time: Optional[float] = Field(None, description="Response time in seconds")
    last_check: str = Field(..., description="Last check timestamp")
    error_message: Optional[str] = Field(None, description="Error message if unhealthy")


class ProvinceCapabilities(BaseModel):
    """Province integration capabilities."""
    
    province_code: str = Field(..., description="Province code")
    supported_operations: List[str] = Field(..., description="Supported operations")
    supported_tax_types: List[str] = Field(..., description="Supported tax types")
    batch_submission: bool = Field(default=False, description="Supports batch submission")
    real_time_status: bool = Field(default=False, description="Supports real-time status checking")
    file_upload: bool = Field(default=False, description="Supports file upload")
    digital_signature: bool = Field(default=False, description="Requires digital signature")


class ProvinceStatistics(BaseModel):
    """Province integration statistics."""
    
    province_code: str = Field(..., description="Province code")
    total_submissions: int = Field(default=0, description="Total submissions")
    successful_submissions: int = Field(default=0, description="Successful submissions")
    failed_submissions: int = Field(default=0, description="Failed submissions")
    average_processing_time: Optional[float] = Field(None, description="Average processing time in minutes")
    last_submission: Optional[str] = Field(None, description="Last submission timestamp")
    uptime_percentage: Optional[float] = Field(None, description="Uptime percentage")
