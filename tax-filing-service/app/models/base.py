"""
Base models and mixins for the Tax Filing Service.
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4

from pydantic import BaseModel as PydanticBaseModel, Field, ConfigDict


class BaseModel(PydanticBaseModel):
    """Base model with common configuration."""

    model_config = ConfigDict(
        # Enable ORM mode for SQLAlchemy integration
        from_attributes=True,
        # Use enum values instead of enum names
        use_enum_values=True,
        # Validate assignment
        validate_assignment=True,
        # Allow population by field name
        populate_by_name=True,
    )


class TimestampMixin(BaseModel):
    """Mixin for models that need timestamp fields."""
    
    created_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default_factory=datetime.utcnow)


class UUIDMixin(BaseModel):
    """Mixin for models that need UUID primary key."""
    
    id: UUID = Field(default_factory=uuid4)


class ResponseModel(BaseModel):
    """Base response model."""
    
    success: bool = True
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ErrorResponse(BaseModel):
    """Error response model."""
    
    success: bool = False
    error: dict
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class PaginationParams(BaseModel):
    """Pagination parameters."""
    
    page: int = Field(default=1, ge=1, description="Page number")
    size: int = Field(default=20, ge=1, le=100, description="Page size")
    
    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.size


class PaginatedResponse(ResponseModel):
    """Paginated response model."""
    
    data: list
    pagination: dict
    
    @classmethod
    def create(
        cls,
        data: list,
        total: int,
        page: int,
        size: int,
        message: Optional[str] = None,
    ):
        """Create a paginated response."""
        total_pages = (total + size - 1) // size
        
        return cls(
            data=data,
            message=message,
            pagination={
                "page": page,
                "size": size,
                "total": total,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1,
            }
        )
