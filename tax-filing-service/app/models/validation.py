"""
Validation related data models.
"""

from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import Field

from .base import BaseModel


class ValidationLevel(str, Enum):
    """Validation level enumeration."""
    
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class FieldValidation(BaseModel):
    """Individual field validation result."""
    
    field_name: str = Field(..., description="Field name")
    field_path: str = Field(..., description="Field path (e.g., 'company_info.tax_id')")
    level: ValidationLevel = Field(..., description="Validation level")
    message: str = Field(..., description="Validation message")
    expected_value: Optional[Any] = Field(None, description="Expected value")
    actual_value: Optional[Any] = Field(None, description="Actual value")
    rule_name: Optional[str] = Field(None, description="Validation rule name")


class ValidationError(BaseModel):
    """Validation error details."""
    
    code: str = Field(..., description="Error code")
    message: str = Field(..., description="Error message")
    field: Optional[str] = Field(None, description="Field name")
    value: Optional[Any] = Field(None, description="Invalid value")
    constraint: Optional[str] = Field(None, description="Constraint that was violated")


class ValidationResult(BaseModel):
    """Complete validation result."""
    
    is_valid: bool = Field(..., description="Overall validation result")
    province_code: str = Field(..., description="Province code")
    errors: List[FieldValidation] = Field(default_factory=list, description="Validation errors")
    warnings: List[FieldValidation] = Field(default_factory=list, description="Validation warnings")
    info: List[FieldValidation] = Field(default_factory=list, description="Validation info messages")
    summary: Dict[str, int] = Field(default_factory=dict, description="Validation summary")
    
    def add_error(self, field_name: str, message: str, **kwargs):
        """Add a validation error."""
        self.errors.append(
            FieldValidation(
                field_name=field_name,
                field_path=kwargs.get("field_path", field_name),
                level=ValidationLevel.ERROR,
                message=message,
                **{k: v for k, v in kwargs.items() if k != "field_path"}
            )
        )
        self.is_valid = False
    
    def add_warning(self, field_name: str, message: str, **kwargs):
        """Add a validation warning."""
        self.warnings.append(
            FieldValidation(
                field_name=field_name,
                field_path=kwargs.get("field_path", field_name),
                level=ValidationLevel.WARNING,
                message=message,
                **{k: v for k, v in kwargs.items() if k != "field_path"}
            )
        )
    
    def add_info(self, field_name: str, message: str, **kwargs):
        """Add a validation info message."""
        self.info.append(
            FieldValidation(
                field_name=field_name,
                field_path=kwargs.get("field_path", field_name),
                level=ValidationLevel.INFO,
                message=message,
                **{k: v for k, v in kwargs.items() if k != "field_path"}
            )
        )
    
    def update_summary(self):
        """Update validation summary."""
        self.summary = {
            "total_errors": len(self.errors),
            "total_warnings": len(self.warnings),
            "total_info": len(self.info),
            "total_issues": len(self.errors) + len(self.warnings) + len(self.info),
        }


class ValidationRequest(BaseModel):
    """Validation request model."""
    
    province_code: str = Field(..., description="Province code")
    data: Dict[str, Any] = Field(..., description="Data to validate")
    validation_level: ValidationLevel = Field(
        default=ValidationLevel.ERROR,
        description="Minimum validation level to report"
    )
    strict_mode: bool = Field(
        default=False,
        description="Whether to use strict validation mode"
    )


class ValidationRule(BaseModel):
    """Validation rule definition."""
    
    name: str = Field(..., description="Rule name")
    field_path: str = Field(..., description="Field path to validate")
    rule_type: str = Field(..., description="Rule type (required, format, range, etc.)")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Rule parameters")
    error_message: str = Field(..., description="Error message template")
    level: ValidationLevel = Field(default=ValidationLevel.ERROR, description="Validation level")
    enabled: bool = Field(default=True, description="Whether rule is enabled")


class ValidationRuleSet(BaseModel):
    """Set of validation rules for a province."""
    
    province_code: str = Field(..., description="Province code")
    rules: List[ValidationRule] = Field(..., description="Validation rules")
    version: str = Field(..., description="Rule set version")
    description: Optional[str] = Field(None, description="Rule set description")


class BatchValidationRequest(BaseModel):
    """Batch validation request model."""
    
    province_code: str = Field(..., description="Province code")
    submissions: List[Dict[str, Any]] = Field(..., description="List of submissions to validate")
    validation_level: ValidationLevel = Field(
        default=ValidationLevel.ERROR,
        description="Minimum validation level to report"
    )
    stop_on_first_error: bool = Field(
        default=False,
        description="Whether to stop validation on first error"
    )


class BatchValidationResponse(BaseModel):
    """Batch validation response model."""
    
    province_code: str = Field(..., description="Province code")
    total_submissions: int = Field(..., description="Total number of submissions")
    valid_submissions: int = Field(..., description="Number of valid submissions")
    invalid_submissions: int = Field(..., description="Number of invalid submissions")
    results: List[ValidationResult] = Field(..., description="Individual validation results")
    summary: Dict[str, Any] = Field(..., description="Overall validation summary")
