# Python税务申报服务开发规范

## 概述

本文档定义了Python税务申报服务项目的开发规范，包括代码风格、项目结构、依赖管理、测试规范、代码质量检查等方面的标准。

## 技术栈

### 核心框架
- **Web框架**: FastAPI 0.104+
- **ORM**: SQLAlchemy 2.0+
- **数据库**: MySQL 8.0+ / PostgreSQL 13+
- **缓存**: Redis 6.0+
- **任务队列**: Celery 5.3+
- **消息队列**: RocketMQ / RabbitMQ
- **HTTP客户端**: httpx / aiohttp

### 开发工具
- **Python版本**: 3.11+
- **包管理**: Poetry 1.6+
- **代码格式化**: Black 23.0+
- **代码检查**: Ruff 0.1+
- **类型检查**: mypy 1.6+
- **测试框架**: pytest 7.4+
- **文档生成**: Sphinx 7.0+

## 项目结构

```
python-tax-service/
├── app/                          # 应用主目录
│   ├── __init__.py
│   ├── main.py                   # FastAPI应用入口
│   ├── config/                   # 配置模块
│   │   ├── __init__.py
│   │   ├── settings.py           # 应用配置
│   │   ├── database.py           # 数据库配置
│   │   ├── redis.py              # Redis配置
│   │   └── logging.py            # 日志配置
│   ├── core/                     # 核心模块
│   │   ├── __init__.py
│   │   ├── dependencies.py       # 依赖注入
│   │   ├── security.py           # 安全相关
│   │   ├── exceptions.py         # 异常定义
│   │   └── middleware.py         # 中间件
│   ├── models/                   # 数据模型
│   │   ├── __init__.py
│   │   ├── base.py               # 基础模型
│   │   ├── submission.py         # 申报模型
│   │   ├── province.py           # 省份模型
│   │   └── audit.py              # 审计模型
│   ├── schemas/                  # Pydantic模式
│   │   ├── __init__.py
│   │   ├── base.py               # 基础模式
│   │   ├── submission.py         # 申报模式
│   │   ├── province.py           # 省份模式
│   │   └── response.py           # 响应模式
│   ├── services/                 # 业务服务
│   │   ├── __init__.py
│   │   ├── base.py               # 基础服务
│   │   ├── submission.py         # 申报服务
│   │   ├── province.py           # 省份服务
│   │   ├── validation.py         # 验证服务
│   │   └── notification.py       # 通知服务
│   ├── repositories/             # 数据访问层
│   │   ├── __init__.py
│   │   ├── base.py               # 基础仓库
│   │   ├── submission.py         # 申报仓库
│   │   └── province.py           # 省份仓库
│   ├── api/                      # API路由
│   │   ├── __init__.py
│   │   ├── v1/                   # API版本1
│   │   │   ├── __init__.py
│   │   │   ├── router.py         # 路由汇总
│   │   │   ├── submissions.py    # 申报接口
│   │   │   ├── provinces.py      # 省份接口
│   │   │   └── health.py         # 健康检查
│   │   └── deps.py               # API依赖
│   ├── adapters/                 # 省份适配器
│   │   ├── __init__.py
│   │   ├── base.py               # 基础适配器
│   │   ├── beijing.py            # 北京适配器
│   │   ├── shanghai.py           # 上海适配器
│   │   └── guangdong.py          # 广东适配器
│   ├── tasks/                    # 异步任务
│   │   ├── __init__.py
│   │   ├── celery_app.py         # Celery应用
│   │   ├── submission.py         # 申报任务
│   │   └── notification.py       # 通知任务
│   ├── utils/                    # 工具模块
│   │   ├── __init__.py
│   │   ├── datetime.py           # 时间工具
│   │   ├── encryption.py         # 加密工具
│   │   ├── http.py               # HTTP工具
│   │   └── validators.py         # 验证工具
│   └── tests/                    # 测试目录
│       ├── __init__.py
│       ├── conftest.py           # pytest配置
│       ├── unit/                 # 单元测试
│       ├── integration/          # 集成测试
│       └── fixtures/             # 测试数据
├── migrations/                   # 数据库迁移
│   └── alembic/
├── docs/                         # 文档目录
│   ├── api.md                    # API文档
│   ├── deployment.md             # 部署文档
│   └── development.md            # 开发文档
├── scripts/                      # 脚本目录
│   ├── start.sh                  # 启动脚本
│   ├── test.sh                   # 测试脚本
│   └── lint.sh                   # 代码检查脚本
├── docker/                       # Docker配置
│   ├── Dockerfile
│   ├── docker-compose.yml
│   └── docker-compose.dev.yml
├── .env.example                  # 环境变量示例
├── .gitignore                    # Git忽略文件
├── .pre-commit-config.yaml       # pre-commit配置
├── pyproject.toml                # 项目配置
├── poetry.lock                   # 依赖锁定文件
├── README.md                     # 项目说明
└── CHANGELOG.md                  # 变更日志
```

## 代码风格规范

### 1. 命名规范

#### 变量和函数
```python
# 使用snake_case
user_name = "张三"
tax_amount = 1000.00

def calculate_tax_amount(taxable_income: float) -> float:
    """计算税额"""
    return taxable_income * 0.13
```

#### 类名
```python
# 使用PascalCase
class TaxSubmissionService:
    """税务申报服务"""
    pass

class BeijingTaxAdapter:
    """北京税务适配器"""
    pass
```

#### 常量
```python
# 使用UPPER_SNAKE_CASE
MAX_RETRY_TIMES = 3
DEFAULT_TIMEOUT = 30
TAX_RATE_VAT = 0.13
```

#### 模块和包
```python
# 使用snake_case
import tax_submission
from services import validation_service
```

### 2. 代码格式化

使用Black进行代码格式化：

```python
# 正确的格式
def submit_tax_return(
    submission_data: dict,
    province_code: str,
    timeout: int = 30,
) -> dict:
    """提交税务申报"""
    result = external_api.submit(
        data=submission_data,
        province=province_code,
        timeout=timeout,
    )
    return result

# 字典和列表格式化
tax_data = {
    "tax_type": "增值税",
    "taxable_amount": 100000.00,
    "tax_rate": 0.13,
    "tax_amount": 13000.00,
}

provinces = [
    "北京",
    "上海", 
    "广东",
    "江苏",
]
```

### 3. 类型注解

所有函数和方法必须包含类型注解：

```python
from typing import Optional, List, Dict, Any
from datetime import datetime

def process_submission(
    submission_id: str,
    data: Dict[str, Any],
    retry_count: int = 0,
) -> Optional[Dict[str, Any]]:
    """处理税务申报"""
    pass

class TaxSubmission:
    def __init__(
        self,
        submission_id: str,
        enterprise_id: str,
        tax_data: List[Dict[str, Any]],
        created_at: Optional[datetime] = None,
    ) -> None:
        self.submission_id = submission_id
        self.enterprise_id = enterprise_id
        self.tax_data = tax_data
        self.created_at = created_at or datetime.now()
```

### 4. 文档字符串

使用Google风格的文档字符串：

```python
def calculate_vat_amount(
    taxable_amount: float,
    tax_rate: float = 0.13,
    deductions: float = 0.0,
) -> float:
    """计算增值税金额
    
    Args:
        taxable_amount: 应税金额
        tax_rate: 税率，默认13%
        deductions: 扣除额，默认0
        
    Returns:
        计算后的增值税金额
        
    Raises:
        ValueError: 当应税金额为负数时
        
    Examples:
        >>> calculate_vat_amount(100000.0)
        13000.0
        >>> calculate_vat_amount(100000.0, 0.09)
        9000.0
    """
    if taxable_amount < 0:
        raise ValueError("应税金额不能为负数")
    
    return (taxable_amount - deductions) * tax_rate
```

## 依赖管理

### 1. Poetry配置

使用Poetry管理项目依赖：

```toml
# pyproject.toml
[tool.poetry]
name = "python-tax-service"
version = "1.0.0"
description = "Python税务申报服务"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.0"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
sqlalchemy = "^2.0.0"
alembic = "^1.12.0"
pydantic = {extras = ["email"], version = "^2.4.0"}
pydantic-settings = "^2.0.0"
redis = "^5.0.0"
celery = "^5.3.0"
httpx = "^0.25.0"
python-multipart = "^0.0.6"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
pymysql = "^1.1.0"
cryptography = "^41.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
black = "^23.0.0"
ruff = "^0.1.0"
mypy = "^1.6.0"
pre-commit = "^3.5.0"
httpx = "^0.25.0"

[tool.poetry.group.docs.dependencies]
sphinx = "^7.0.0"
sphinx-rtd-theme = "^1.3.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
```

### 2. 依赖分组

```bash
# 安装生产依赖
poetry install --only=main

# 安装开发依赖
poetry install --with=dev

# 安装所有依赖
poetry install

# 添加新依赖
poetry add fastapi
poetry add --group dev pytest

# 更新依赖
poetry update
```

## 配置管理

### 1. 设置模块

```python
# app/config/settings.py
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    """应用配置"""
    
    # 应用配置
    app_name: str = "Python税务申报服务"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 数据库配置
    database_url: str
    database_pool_size: int = 10
    database_max_overflow: int = 20
    
    # Redis配置
    redis_url: str = "redis://localhost:6379/0"
    redis_pool_size: int = 10
    
    # API配置
    api_prefix: str = "/api/v1"
    cors_origins: list[str] = ["*"]
    
    # 安全配置
    secret_key: str
    access_token_expire_minutes: int = 30
    
    # 外部服务配置
    go_backend_url: str = "http://localhost:8081"
    go_backend_timeout: int = 30
    
    # 日志配置
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

settings = Settings()
```

### 2. 环境变量

```bash
# .env.example
# 应用配置
APP_NAME=Python税务申报服务
APP_VERSION=1.0.0
DEBUG=false

# 数据库配置
DATABASE_URL=mysql+pymysql://user:password@localhost:3306/tax_db
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_POOL_SIZE=10

# API配置
API_PREFIX=/api/v1
CORS_ORIGINS=["http://localhost:3000","http://localhost:8080"]

# 安全配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 外部服务配置
GO_BACKEND_URL=http://localhost:8081
GO_BACKEND_TIMEOUT=30

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/var/log/tax-service/app.log
```

## 数据库规范

### 1. 模型定义

```python
# app/models/base.py
from sqlalchemy import Column, String, DateTime, func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.mysql import CHAR

Base = declarative_base()

class BaseModel(Base):
    """基础模型"""
    __abstract__ = True
    
    id = Column(CHAR(50), primary_key=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
```

```python
# app/models/submission.py
from sqlalchemy import Column, String, Text, Integer, Decimal, Enum, DateTime
from sqlalchemy.dialects.mysql import JSON
from .base import BaseModel

class TaxSubmission(BaseModel):
    """税务申报模型"""
    __tablename__ = "tax_submissions"
    
    enterprise_id = Column(String(50), nullable=False, index=True)
    province_code = Column(String(10), nullable=False, index=True)
    submission_type = Column(Enum("manual", "batch", "scheduled"), nullable=False)
    status = Column(Enum("pending", "processing", "submitted", "accepted", "rejected", "failed"), 
                   nullable=False, default="pending", index=True)
    
    # 公司信息
    company_name = Column(String(200), nullable=False)
    tax_id = Column(String(50), nullable=False)
    
    # 税务数据
    tax_data = Column(JSON, nullable=False)
    total_tax_amount = Column(Decimal(15, 2), nullable=False, default=0)
    
    # 处理信息
    external_id = Column(String(100), nullable=True, index=True)
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, nullable=False, default=0)
    
    # 时间信息
    submitted_at = Column(DateTime, nullable=True)
    processed_at = Column(DateTime, nullable=True)
```

### 2. 仓库模式

```python
# app/repositories/base.py
from typing import Generic, TypeVar, Type, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy.ext.declarative import DeclarativeMeta

ModelType = TypeVar("ModelType", bound=DeclarativeMeta)

class BaseRepository(Generic[ModelType]):
    """基础仓库"""
    
    def __init__(self, model: Type[ModelType], db: Session):
        self.model = model
        self.db = db
    
    def get(self, id: str) -> Optional[ModelType]:
        """根据ID获取记录"""
        return self.db.query(self.model).filter(self.model.id == id).first()
    
    def get_multi(self, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """获取多条记录"""
        return self.db.query(self.model).offset(skip).limit(limit).all()
    
    def create(self, obj_in: dict) -> ModelType:
        """创建记录"""
        db_obj = self.model(**obj_in)
        self.db.add(db_obj)
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj
    
    def update(self, db_obj: ModelType, obj_in: dict) -> ModelType:
        """更新记录"""
        for field, value in obj_in.items():
            setattr(db_obj, field, value)
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj
    
    def delete(self, id: str) -> bool:
        """删除记录"""
        obj = self.get(id)
        if obj:
            self.db.delete(obj)
            self.db.commit()
            return True
        return False
```

## API设计规范

### 1. 路由定义

```python
# app/api/v1/submissions.py
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from app.core.dependencies import get_db
from app.schemas.submission import SubmissionCreate, SubmissionResponse
from app.services.submission import SubmissionService

router = APIRouter(prefix="/submissions", tags=["税务申报"])

@router.post("/", response_model=SubmissionResponse, status_code=status.HTTP_201_CREATED)
async def create_submission(
    submission_data: SubmissionCreate,
    db: Session = Depends(get_db),
) -> SubmissionResponse:
    """创建税务申报
    
    Args:
        submission_data: 申报数据
        db: 数据库会话
        
    Returns:
        创建的申报记录
        
    Raises:
        HTTPException: 当创建失败时
    """
    service = SubmissionService(db)
    try:
        submission = await service.create_submission(submission_data)
        return SubmissionResponse.from_orm(submission)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建申报失败"
        )

@router.get("/{submission_id}", response_model=SubmissionResponse)
async def get_submission(
    submission_id: str,
    db: Session = Depends(get_db),
) -> SubmissionResponse:
    """获取申报详情"""
    service = SubmissionService(db)
    submission = await service.get_submission(submission_id)
    if not submission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="申报记录不存在"
        )
    return SubmissionResponse.from_orm(submission)
```

### 2. 响应模式

```python
# app/schemas/response.py
from pydantic import BaseModel
from typing import Optional, Any, Generic, TypeVar

DataType = TypeVar("DataType")

class BaseResponse(BaseModel):
    """基础响应模式"""
    status: str = "success"
    message: str = ""
    
class SuccessResponse(BaseResponse, Generic[DataType]):
    """成功响应模式"""
    data: Optional[DataType] = None

class ErrorResponse(BaseResponse):
    """错误响应模式"""
    status: str = "error"
    error_code: Optional[str] = None
    details: Optional[dict] = None

class PagedResponse(BaseResponse, Generic[DataType]):
    """分页响应模式"""
    data: list[DataType]
    total: int
    page: int
    page_size: int
    total_pages: int
```

## 测试规范

### 1. 测试结构

```python
# app/tests/conftest.py
import pytest
import asyncio
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from app.main import app
from app.core.dependencies import get_db
from app.models.base import Base

# 测试数据库
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="function")
def db():
    """创建测试数据库"""
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)

@pytest.fixture
def client():
    """创建测试客户端"""
    with TestClient(app) as c:
        yield c
```

### 2. 单元测试

```python
# app/tests/unit/test_submission_service.py
import pytest
from unittest.mock import Mock, patch
from app.services.submission import SubmissionService
from app.schemas.submission import SubmissionCreate

class TestSubmissionService:
    """申报服务测试"""
    
    @pytest.fixture
    def mock_db(self):
        """模拟数据库"""
        return Mock()
    
    @pytest.fixture
    def service(self, mock_db):
        """创建服务实例"""
        return SubmissionService(mock_db)
    
    @pytest.mark.asyncio
    async def test_create_submission_success(self, service, mock_db):
        """测试创建申报成功"""
        # 准备测试数据
        submission_data = SubmissionCreate(
            enterprise_id="test_enterprise",
            province_code="BJ",
            company_name="测试公司",
            tax_id="*********",
            tax_data=[{
                "tax_type": "增值税",
                "tax_amount": 1000.0
            }]
        )
        
        # 模拟数据库操作
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        # 执行测试
        result = await service.create_submission(submission_data)
        
        # 验证结果
        assert result is not None
        assert result.enterprise_id == "test_enterprise"
        assert result.province_code == "BJ"
        
        # 验证数据库调用
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_submission_invalid_data(self, service):
        """测试创建申报数据无效"""
        # 准备无效数据
        submission_data = SubmissionCreate(
            enterprise_id="",  # 无效的企业ID
            province_code="BJ",
            company_name="测试公司",
            tax_id="*********",
            tax_data=[]
        )
        
        # 执行测试并验证异常
        with pytest.raises(ValueError, match="企业ID不能为空"):
            await service.create_submission(submission_data)
```

### 3. 集成测试

```python
# app/tests/integration/test_submission_api.py
import pytest
from fastapi.testclient import TestClient

class TestSubmissionAPI:
    """申报API集成测试"""
    
    def test_create_submission_success(self, client: TestClient, db):
        """测试创建申报API成功"""
        # 准备测试数据
        submission_data = {
            "enterprise_id": "test_enterprise",
            "province_code": "BJ",
            "company_name": "测试公司",
            "tax_id": "*********",
            "tax_data": [{
                "tax_type": "增值税",
                "tax_amount": 1000.0
            }]
        }
        
        # 发送请求
        response = client.post("/api/v1/submissions/", json=submission_data)
        
        # 验证响应
        assert response.status_code == 201
        data = response.json()
        assert data["status"] == "success"
        assert data["data"]["enterprise_id"] == "test_enterprise"
        assert data["data"]["province_code"] == "BJ"
    
    def test_get_submission_not_found(self, client: TestClient):
        """测试获取不存在的申报"""
        response = client.get("/api/v1/submissions/nonexistent")
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "申报记录不存在"
```

## 代码质量检查

### 1. 工具配置

```toml
# pyproject.toml

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["app/tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "app/tests/*",
    "app/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
]
```

### 2. Pre-commit配置

```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict

  - repo: https://github.com/psf/black
    rev: 23.9.1
    hooks:
      - id: black
        language_version: python3.11

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.1.0
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.6.1
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
```

### 3. 脚本工具

```bash
#!/bin/bash
# scripts/lint.sh

echo "Running code quality checks..."

echo "1. Running Black..."
poetry run black app/ --check

echo "2. Running Ruff..."
poetry run ruff app/

echo "3. Running MyPy..."
poetry run mypy app/

echo "4. Running tests..."
poetry run pytest app/tests/ -v --cov=app --cov-report=html

echo "Code quality checks completed!"
```

```bash
#!/bin/bash
# scripts/format.sh

echo "Formatting code..."

echo "1. Running Black..."
poetry run black app/

echo "2. Running Ruff with auto-fix..."
poetry run ruff app/ --fix

echo "Code formatting completed!"
```

## 部署规范

### 1. Docker配置

```dockerfile
# docker/Dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 安装Poetry
RUN pip install poetry

# 复制依赖文件
COPY ../python-tax-service/pyproject.toml poetry.lock ./

# 配置Poetry
RUN poetry config virtualenvs.create false

# 安装依赖
RUN poetry install --only=main

# 复制应用代码
COPY ../python-tax-service/app ./app/

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app
RUN chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. 环境配置

```yaml
# docker/docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql+pymysql://root:password@db:3306/tax_db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ../logs:/app/logs

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: tax_db
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

## 总结

本开发规范涵盖了Python税务申报服务项目的各个方面，包括：

1. **项目结构**: 清晰的目录组织和模块划分
2. **代码风格**: 统一的命名规范和格式化标准
3. **依赖管理**: 使用Poetry进行现代化的依赖管理
4. **配置管理**: 基于Pydantic的类型安全配置
5. **数据库规范**: SQLAlchemy模型和仓库模式
6. **API设计**: FastAPI最佳实践和响应模式
7. **测试规范**: 完整的单元测试和集成测试
8. **代码质量**: 自动化的代码检查和格式化
9. **部署规范**: Docker化的部署方案

遵循这些规范可以确保代码质量、提高开发效率、便于团队协作和项目维护。
