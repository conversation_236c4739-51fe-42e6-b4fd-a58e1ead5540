version: '3.8'

services:
  tax-filing-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tax-filing-service
    ports:
      - "8082:8082"
    environment:
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8082
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-in-production}
      - API_KEY=${API_KEY:-your-api-key-change-in-production}
      - ALLOWED_HOSTS=localhost,127.0.0.1,tax-filing-service
      - GO_BACKEND_URL=${GO_BACKEND_URL:-http://host.docker.internal:8081}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FILE=logs/tax-filing-service.log
      - PROVINCE_CONFIG_PATH=config/provinces.json
      # Beijing Tax Bureau Settings
      - BEIJING_TAX_URL=${BEIJING_TAX_URL:-https://etax.beijing.chinatax.gov.cn:8443/}
      - BEIJING_TAX_USERNAME=${BEIJING_TAX_USERNAME:-demo_user}
      - BEIJING_TAX_PASSWORD=${BEIJING_TAX_PASSWORD:-demo_password}
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./data:/app/data
    networks:
      - tax-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.tax-filing.rule=Host(`tax-filing.local`)"
      - "traefik.http.services.tax-filing.loadbalancer.server.port=8082"

  redis:
    image: redis:7-alpine
    container_name: tax-filing-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tax-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Optional: PostgreSQL database for storing configurations and status
  postgres:
    image: postgres:15-alpine
    container_name: tax-filing-postgres
    environment:
      - POSTGRES_DB=tax_filing_service
      - POSTGRES_USER=tax_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-tax_password_change_in_production}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - tax-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U tax_user -d tax_filing_service"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Optional: Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: tax-filing-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - tax-network
    restart: unless-stopped
    depends_on:
      - tax-filing-service

  # Optional: Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: tax-filing-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - tax-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  # Optional: Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: tax-filing-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - tax-network
    restart: unless-stopped
    depends_on:
      - prometheus

networks:
  tax-network:
    driver: bridge
    name: tax-network

volumes:
  redis_data:
    name: tax-filing-redis-data
  postgres_data:
    name: tax-filing-postgres-data
  prometheus_data:
    name: tax-filing-prometheus-data
  grafana_data:
    name: tax-filing-grafana-data
