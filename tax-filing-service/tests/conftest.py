"""
Test configuration and fixtures.
"""

import asyncio
import os
import tempfile
from typing import As<PERSON><PERSON>enerator, Generator
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.core.config import Settings
from app.models.province import ProvinceConfig, ProvinceStatus, AuthMethod
from app.models.tax_submission import (
    TaxSubmission,
    TaxSubmissionCreate,
    CompanyInfo,
    TaxPeriod,
    TaxData,
    TaxType,
    SubmissionStatusEnum,
)
from app.services.province_manager import ProvinceManagerService
from app.services.validation import ValidationService
from app.services.tax_submission import TaxSubmissionService
from app.services.notification import NotificationService
from main import create_application


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_settings():
    """Test settings configuration."""
    with tempfile.TemporaryDirectory() as temp_dir:
        config_path = os.path.join(temp_dir, "test_provinces.json")
        
        yield Settings(
            DEBUG=True,
            SECRET_KEY="test-secret-key",
            API_KEY="test-api-key",
            ALLOWED_HOSTS=["testserver"],
            DATABASE_URL=None,
            GO_BACKEND_URL="http://test-backend:8081",
            LOG_LEVEL="DEBUG",
            PROVINCE_CONFIG_PATH=config_path,
        )


@pytest.fixture
def test_province_config():
    """Test province configuration."""
    return ProvinceConfig(
        code="BJ",
        name="北京市测试",
        name_en="Beijing Test",
        base_url="https://test.beijing.chinatax.gov.cn/",
        status=ProvinceStatus.ACTIVE,
        auth_method=AuthMethod.USERNAME_PASSWORD,
        timeout=30,
        max_retries=3,
        retry_delay=1.0,
        auth_config={
            "username": "test_user",
            "password": "test_password",
            "client_id": "test_client",
        },
        api_endpoints={
            "auth": "api/v1/auth/login",
            "submit": "api/v1/tax/submit",
            "status": "api/v1/tax/status",
            "validate": "api/v1/tax/validate",
            "health": "api/v1/system/health",
        },
        data_mappings={
            "tax_types": {
                "vat": "01",
                "income_tax": "02",
            }
        },
        validation_rules={
            "tax_id_length": 18,
            "max_tax_rate": 0.17,
        },
    )


@pytest.fixture
def test_company_info():
    """Test company information."""
    return CompanyInfo(
        company_name="测试公司有限责任公司",
        tax_id="123456789012345678",
        registration_number="110000000000001",
        legal_representative="张三",
        address="北京市朝阳区测试街道123号",
        phone="13800138000",
        email="<EMAIL>",
    )


@pytest.fixture
def test_tax_period():
    """Test tax period."""
    return TaxPeriod(
        year=2024,
        month=1,
        quarter=None,
        period_type="monthly",
    )


@pytest.fixture
def test_tax_data():
    """Test tax data."""
    return [
        TaxData(
            tax_type=TaxType.VAT,
            taxable_amount=100000.0,
            tax_rate=0.13,
            tax_amount=13000.0,
            deductions=1000.0,
            credits=500.0,
            final_amount=11500.0,
        )
    ]


@pytest.fixture
def test_submission_create(test_company_info, test_tax_period, test_tax_data):
    """Test tax submission create data."""
    return TaxSubmissionCreate(
        province_code="BJ",
        company_info=test_company_info,
        tax_period=test_tax_period,
        tax_data=test_tax_data,
        notes="测试提交",
    )


@pytest.fixture
def test_submission(test_submission_create):
    """Test tax submission."""
    return TaxSubmission(
        **test_submission_create.model_dump(),
        status=SubmissionStatusEnum.PENDING,
    )


@pytest.fixture
def mock_province_manager():
    """Mock province manager service."""
    manager = MagicMock(spec=ProvinceManagerService)
    manager.get_province_config = AsyncMock()
    manager.list_provinces = AsyncMock()
    manager.create_province_config = AsyncMock()
    manager.update_province_config = AsyncMock()
    manager.delete_province_config = AsyncMock()
    manager.get_province_info = AsyncMock()
    manager.health_check_province = AsyncMock()
    manager.get_province_capabilities = AsyncMock()
    manager.update_statistics = MagicMock()
    manager.get_statistics = MagicMock()
    return manager


@pytest.fixture
def mock_validation_service():
    """Mock validation service."""
    service = MagicMock(spec=ValidationService)
    service.validate_submission = AsyncMock()
    service.validate_data = AsyncMock()
    service.batch_validate = AsyncMock()
    return service


@pytest.fixture
def mock_tax_submission_service():
    """Mock tax submission service."""
    service = MagicMock(spec=TaxSubmissionService)
    service.submit_tax_return = AsyncMock()
    service.get_submission_status = AsyncMock()
    service.batch_submit = AsyncMock()
    service.retry_failed_submission = AsyncMock()
    service.cancel_submission = AsyncMock()
    service.get_submission = MagicMock()
    service.list_submissions = MagicMock()
    return service


@pytest.fixture
def mock_notification_service():
    """Mock notification service."""
    service = MagicMock(spec=NotificationService)
    service.notify_submission_created = AsyncMock()
    service.notify_submission_status_change = AsyncMock()
    service.notify_validation_failed = AsyncMock()
    service.notify_system_error = AsyncMock()
    service.send_batch_notification = AsyncMock()
    service.test_notification_channels = AsyncMock()
    return service


@pytest.fixture
def app(test_settings):
    """FastAPI test application."""
    # Override settings for testing
    from app.core.config import get_settings
    
    def get_test_settings():
        return test_settings
    
    # Create app with test settings
    app = create_application()
    app.dependency_overrides[get_settings] = get_test_settings
    
    return app


@pytest.fixture
def client(app):
    """Test client."""
    return TestClient(app)


@pytest.fixture
async def async_client(app) -> AsyncGenerator[AsyncClient, None]:
    """Async test client."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def auth_headers():
    """Authentication headers for testing."""
    return {
        "Authorization": "Bearer test-api-key",
        "Content-Type": "application/json",
    }


@pytest.fixture
def mock_httpx_client():
    """Mock httpx client for external API calls."""
    client = AsyncMock()
    client.request = AsyncMock()
    client.aclose = AsyncMock()
    return client


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """Setup test environment variables."""
    monkeypatch.setenv("DEBUG", "true")
    monkeypatch.setenv("SECRET_KEY", "test-secret-key")
    monkeypatch.setenv("API_KEY", "test-api-key")
    monkeypatch.setenv("LOG_LEVEL", "DEBUG")


class MockResponse:
    """Mock HTTP response for testing."""
    
    def __init__(self, json_data, status_code=200, text=""):
        self.json_data = json_data
        self.status_code = status_code
        self.text = text
    
    def json(self):
        return self.json_data
    
    def raise_for_status(self):
        if self.status_code >= 400:
            raise Exception(f"HTTP {self.status_code}")


@pytest.fixture
def mock_successful_response():
    """Mock successful HTTP response."""
    return MockResponse({
        "success": True,
        "message": "Operation successful",
        "data": {"id": "test-id"},
    })


@pytest.fixture
def mock_error_response():
    """Mock error HTTP response."""
    return MockResponse(
        {"success": False, "error": "Test error"},
        status_code=400,
        text="Bad Request"
    )
