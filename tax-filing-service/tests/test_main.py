"""
Tests for the main application.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient


def test_health_check(client: TestClient):
    """Test basic health check endpoint."""
    response = client.get("/health")
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "tax-filing-service"


def test_api_health_check(client: TestClient):
    """Test API health check endpoint."""
    response = client.get("/api/health")
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "tax-filing-service"
    assert "supported_provinces" in data


def test_api_detailed_health_check(client: TestClient):
    """Test detailed health check endpoint."""
    response = client.get("/api/health/detailed")
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] in ["healthy", "degraded"]
    assert "provinces" in data
    assert "notifications" in data
    assert "integrations" in data


def test_readiness_check(client: TestClient):
    """Test readiness check endpoint."""
    response = client.get("/api/health/readiness")
    
    assert response.status_code == 200
    data = response.json()
    assert "ready" in data
    assert "timestamp" in data


def test_liveness_check(client: TestClient):
    """Test liveness check endpoint."""
    response = client.get("/api/health/liveness")
    
    assert response.status_code == 200
    data = response.json()
    assert data["alive"] is True
    assert "timestamp" in data


def test_docs_endpoint_in_debug_mode(client: TestClient):
    """Test that docs are available in debug mode."""
    response = client.get("/docs")
    
    # Should be available in debug mode
    assert response.status_code == 200


def test_openapi_endpoint_in_debug_mode(client: TestClient):
    """Test that OpenAPI spec is available in debug mode."""
    response = client.get("/openapi.json")
    
    # Should be available in debug mode
    assert response.status_code == 200
    data = response.json()
    assert "openapi" in data
    assert data["info"]["title"] == "Tax Filing Service"


def test_cors_headers(client: TestClient):
    """Test CORS headers are present."""
    response = client.options("/api/health")
    
    # CORS headers should be present
    assert "access-control-allow-origin" in response.headers


def test_invalid_endpoint(client: TestClient):
    """Test invalid endpoint returns 404."""
    response = client.get("/invalid-endpoint")
    
    assert response.status_code == 404


def test_api_prefix_required(client: TestClient):
    """Test that API endpoints require /api prefix."""
    response = client.get("/tax/submit")
    
    # Should return 404 without /api prefix
    assert response.status_code == 404


@pytest.mark.asyncio
async def test_application_startup():
    """Test application startup process."""
    from main import create_application
    
    app = create_application()
    
    # Check that app is created successfully
    assert app is not None
    assert app.title == "Tax Filing Service"
    assert app.version == "1.0.0"


def test_environment_configuration(test_settings):
    """Test environment configuration."""
    assert test_settings.DEBUG is True
    assert test_settings.SECRET_KEY == "test-secret-key"
    assert test_settings.API_KEY == "test-api-key"
    assert test_settings.PORT == 8082
    assert test_settings.LOG_LEVEL == "DEBUG"
