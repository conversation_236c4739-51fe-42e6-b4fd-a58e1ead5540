# Tax Filing Service

一个完整的报税中间件服务，作为主税务管理系统和各省级电子税务局之间的桥梁。

## 项目概述

Tax Filing Service 是一个基于 FastAPI 的微服务，提供以下核心功能：

- 🏛️ **多省级税务局集成** - 支持多个省级电子税务局 API 集成
- 📊 **标准化数据处理** - 统一的税务数据格式和验证
- 🔄 **异步处理** - 高性能的异步请求处理
- 🛡️ **安全认证** - API 密钥和 JWT 认证机制
- 📝 **数据验证** - 全面的业务规则和省份特定验证
- 📈 **监控和日志** - 结构化日志和健康检查
- 🔄 **重试机制** - 智能的错误重试和恢复

## 技术栈

- **语言**: Python 3.11+
- **框架**: FastAPI
- **HTTP 客户端**: httpx
- **数据验证**: Pydantic v2
- **日志**: loguru
- **测试**: pytest
- **包管理**: Poetry
- **代码质量**: Black, Ruff, MyPy
- **文档**: 自动生成的 OpenAPI/Swagger 文档

## 快速开始

### 环境要求

- Python 3.11 或更高版本
- Poetry 1.6+

### 安装

1. 克隆项目
```bash
git clone <repository-url>
cd tax-filing-service
```

2. 安装依赖
```bash
# 安装生产依赖
poetry install

# 安装开发依赖
poetry install --with=dev

# 安装所有依赖组
poetry install --with=dev,docs,monitoring
```

4. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，设置必要的配置
```

5. 启动服务
```bash
# 使用Poetry运行
poetry run python main.py

# 或使用Makefile
make run
```

服务将在 `http://localhost:8082` 启动。

### Docker 部署

```bash
# 构建镜像
docker build -t tax-filing-service .

# 运行容器
docker run -p 8082:8082 --env-file .env tax-filing-service
```

## API 文档

启动服务后，可以通过以下地址访问 API 文档：

- **Swagger UI**: http://localhost:8082/docs
- **ReDoc**: http://localhost:8082/redoc
- **OpenAPI JSON**: http://localhost:8082/openapi.json

## 核心 API 端点

### 税务申报

- `POST /api/tax/submit` - 提交税务申报
- `GET /api/tax/status/{submission_id}` - 查询申报状态
- `POST /api/tax/batch-submit` - 批量提交申报
- `POST /api/tax/retry/{submission_id}` - 重试失败的申报
- `DELETE /api/tax/cancel/{submission_id}` - 取消申报

### 省份管理

- `GET /api/provinces` - 获取支持的省份列表
- `GET /api/provinces/{province_code}` - 获取省份详细信息
- `GET /api/provinces/{province_code}/health` - 省份健康检查
- `GET /api/provinces/{province_code}/capabilities` - 省份功能特性

### 数据验证

- `POST /api/validation/validate-submission` - 验证完整申报数据
- `POST /api/validation/validate-data` - 验证任意数据
- `POST /api/validation/batch-validate` - 批量验证

### 健康检查

- `GET /health` - 基础健康检查
- `GET /api/health/detailed` - 详细健康检查
- `GET /api/health/readiness` - 就绪检查
- `GET /api/health/liveness` - 存活检查

## 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `DEBUG` | 调试模式 | `false` |
| `HOST` | 服务主机 | `0.0.0.0` |
| `PORT` | 服务端口 | `8082` |
| `SECRET_KEY` | JWT 密钥 | 必须设置 |
| `API_KEY` | API 认证密钥 | 必须设置 |
| `GO_BACKEND_URL` | Go 后端服务地址 | `http://localhost:8081` |
| `LOG_LEVEL` | 日志级别 | `INFO` |

### 省份配置

省份配置存储在 `config/provinces.json` 文件中，包含：

- 省份基本信息（代码、名称、URL）
- 认证配置
- API 端点映射
- 数据字段映射
- 验证规则

## 开发指南

### 项目结构

```
tax-filing-service/
├── app/
│   ├── api/                 # API 路由和端点
│   ├── core/                # 核心配置和工具
│   ├── integrations/        # 省级税务局集成
│   ├── models/              # 数据模型
│   ├── services/            # 业务逻辑服务
│   └── utils/               # 工具函数
├── config/                  # 配置文件
├── tests/                   # 测试用例
├── scripts/                 # 脚本文件
├── logs/                    # 日志文件
├── pyproject.toml          # 项目配置和依赖
├── main.py                  # 应用入口
└── README.md               # 项目文档
```

### 添加新省份集成

1. 在 `app/integrations/` 目录下创建新的省份集成类
2. 继承 `BaseProvinceIntegration` 基类
3. 实现必要的抽象方法
4. 在 `ProvinceIntegrationFactory` 中注册新集成
5. 添加省份配置到 `config/provinces.json`

### 运行测试

```bash
# 运行所有测试
poetry run pytest

# 运行特定测试文件
poetry run pytest tests/test_main.py

# 生成覆盖率报告
poetry run pytest --cov=app --cov-report=html

# 使用Makefile
make test
make test-verbose
```

### 代码质量

```bash
# 代码格式化
poetry run black app/ tests/

# 代码检查和自动修复
poetry run ruff check app/ tests/ --fix

# 类型检查
poetry run mypy app/

# 运行所有代码质量检查
./scripts/lint.sh

# 格式化代码
./scripts/format.sh

# 使用Makefile
make format
make lint
```

## 部署

### 生产环境配置

1. 设置环境变量：
   - `DEBUG=false`
   - 设置强密码的 `SECRET_KEY` 和 `API_KEY`
   - 配置正确的 `ALLOWED_HOSTS`
   - 设置生产数据库 URL（如果使用）

2. 配置省份认证信息：
   - 更新 `config/provinces.json` 中的认证配置
   - 使用环境变量存储敏感信息

3. 设置日志：
   - 配置 `LOG_FILE` 路径
   - 设置适当的 `LOG_LEVEL`

### Docker Compose

```yaml
version: '3.8'
services:
  tax-filing-service:
    build: .
    ports:
      - "8082:8082"
    environment:
      - DEBUG=false
      - SECRET_KEY=${SECRET_KEY}
      - API_KEY=${API_KEY}
      - GO_BACKEND_URL=http://go-backend:8081
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    depends_on:
      - go-backend
```

### Kubernetes

参考 `k8s/` 目录中的 Kubernetes 配置文件。

## 监控和日志

### 日志格式

服务使用结构化日志，包含：
- 时间戳
- 日志级别
- 模块名称
- 消息内容
- 上下文信息

### 健康检查

- `/health` - 基础健康状态
- `/api/health/detailed` - 详细健康信息，包括省份状态
- `/api/health/readiness` - Kubernetes 就绪探针
- `/api/health/liveness` - Kubernetes 存活探针

### 指标监控

服务提供以下监控指标：
- 请求计数和响应时间
- 省份集成状态
- 错误率和重试次数
- 系统资源使用情况

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查环境变量配置
   - 确认端口未被占用
   - 查看日志文件

2. **省份集成失败**
   - 检查网络连接
   - 验证认证配置
   - 查看省份健康检查状态

3. **验证错误**
   - 检查数据格式
   - 查看验证规则配置
   - 确认省份特定要求

### 日志分析

```bash
# 查看实时日志
tail -f logs/tax-filing-service.log

# 搜索错误日志
grep "ERROR" logs/tax-filing-service.log

# 分析特定省份的日志
grep "BJ" logs/tax-filing-service.log
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/new-feature`)
3. 提交更改 (`git commit -am 'Add new feature'`)
4. 推送到分支 (`git push origin feature/new-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目 Issues: [GitHub Issues](https://github.com/your-org/tax-filing-service/issues)
- 邮箱: <EMAIL>

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持北京税务局集成
- 基础税务申报功能
- API 认证和验证
- 健康检查和监控
