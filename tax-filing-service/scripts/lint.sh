#!/bin/bash
# Code quality checks script

set -e

echo "🔍 Running code quality checks..."

echo "1. <PERSON> Black (code formatting check)..."
poetry run black app/ tests/ --check

echo "2. Running Ruff (linting)..."
poetry run ruff check app/ tests/

echo "3. Running MyPy (type checking)..."
poetry run mypy app/

echo "4. Running tests with coverage..."
poetry run pytest tests/ -v --cov=app --cov-report=term-missing --cov-report=html

echo "✅ All code quality checks completed successfully!"
