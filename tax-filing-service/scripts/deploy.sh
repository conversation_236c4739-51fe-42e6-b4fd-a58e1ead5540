#!/bin/bash

# Tax Filing Service Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_NAME="tax-filing-service"
IMAGE_NAME="tax-filing-service"
CONTAINER_NAME="tax-filing-service"
PORT=8082

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check .env file
    if [ ! -f .env ]; then
        log_warning ".env file not found, copying from .env.example"
        cp .env.example .env
        log_warning "Please edit .env file with your configuration"
    fi
    
    log_success "Requirements check passed"
}

build_image() {
    log_info "Building Docker image..."
    
    docker build -t $IMAGE_NAME:latest .
    
    if [ $? -eq 0 ]; then
        log_success "Docker image built successfully"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
}

run_tests() {
    log_info "Running tests..."
    
    # Create test container
    docker run --rm \
        -v $(pwd):/app \
        -w /app \
        $IMAGE_NAME:latest \
        python -m pytest tests/ -v --cov=app --cov-report=term-missing
    
    if [ $? -eq 0 ]; then
        log_success "All tests passed"
    else
        log_error "Tests failed"
        exit 1
    fi
}

deploy_service() {
    log_info "Deploying service..."
    
    # Stop existing containers
    docker-compose down
    
    # Start services
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        log_success "Service deployed successfully"
    else
        log_error "Failed to deploy service"
        exit 1
    fi
}

check_health() {
    log_info "Checking service health..."
    
    # Wait for service to start
    sleep 10
    
    # Check health endpoint
    for i in {1..30}; do
        if curl -f http://localhost:$PORT/health > /dev/null 2>&1; then
            log_success "Service is healthy"
            return 0
        fi
        
        log_info "Waiting for service to be ready... ($i/30)"
        sleep 2
    done
    
    log_error "Service health check failed"
    return 1
}

show_status() {
    log_info "Service status:"
    docker-compose ps
    
    log_info "Service logs (last 20 lines):"
    docker-compose logs --tail=20 $SERVICE_NAME
}

cleanup() {
    log_info "Cleaning up..."
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    log_success "Cleanup completed"
}

backup_data() {
    log_info "Creating backup..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $BACKUP_DIR
    
    # Backup configuration
    cp -r config $BACKUP_DIR/
    
    # Backup logs
    if [ -d logs ]; then
        cp -r logs $BACKUP_DIR/
    fi
    
    # Backup database (if using PostgreSQL)
    if docker-compose ps postgres | grep -q "Up"; then
        docker-compose exec -T postgres pg_dump -U tax_user tax_filing_service > $BACKUP_DIR/database.sql
    fi
    
    log_success "Backup created at $BACKUP_DIR"
}

restore_backup() {
    if [ -z "$1" ]; then
        log_error "Please specify backup directory"
        exit 1
    fi
    
    BACKUP_DIR=$1
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_error "Backup directory not found: $BACKUP_DIR"
        exit 1
    fi
    
    log_info "Restoring from backup: $BACKUP_DIR"
    
    # Stop services
    docker-compose down
    
    # Restore configuration
    if [ -d "$BACKUP_DIR/config" ]; then
        cp -r $BACKUP_DIR/config/* config/
    fi
    
    # Restore database
    if [ -f "$BACKUP_DIR/database.sql" ]; then
        docker-compose up -d postgres
        sleep 10
        docker-compose exec -T postgres psql -U tax_user -d tax_filing_service < $BACKUP_DIR/database.sql
    fi
    
    # Start services
    docker-compose up -d
    
    log_success "Backup restored successfully"
}

show_help() {
    echo "Tax Filing Service Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  deploy      Full deployment (build, test, deploy)"
    echo "  build       Build Docker image"
    echo "  test        Run tests"
    echo "  start       Start services"
    echo "  stop        Stop services"
    echo "  restart     Restart services"
    echo "  status      Show service status"
    echo "  logs        Show service logs"
    echo "  health      Check service health"
    echo "  backup      Create backup"
    echo "  restore     Restore from backup"
    echo "  cleanup     Clean up unused resources"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 deploy                    # Full deployment"
    echo "  $0 restore backups/20240101  # Restore from specific backup"
}

# Main script
case "$1" in
    deploy)
        check_requirements
        build_image
        run_tests
        deploy_service
        check_health
        show_status
        ;;
    build)
        check_requirements
        build_image
        ;;
    test)
        check_requirements
        run_tests
        ;;
    start)
        docker-compose up -d
        log_success "Services started"
        ;;
    stop)
        docker-compose down
        log_success "Services stopped"
        ;;
    restart)
        docker-compose restart
        log_success "Services restarted"
        ;;
    status)
        show_status
        ;;
    logs)
        docker-compose logs -f $SERVICE_NAME
        ;;
    health)
        check_health
        ;;
    backup)
        backup_data
        ;;
    restore)
        restore_backup $2
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
