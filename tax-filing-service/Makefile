# Tax Filing Service Makefile

.PHONY: help install dev test lint format clean build run docker-build docker-run deploy

# Default target
help:
	@echo "Tax Filing Service - Available commands:"
	@echo ""
	@echo "Development:"
	@echo "  install     Install dependencies"
	@echo "  dev         Install development dependencies"
	@echo "  run         Run the service locally"
	@echo "  test        Run tests"
	@echo "  lint        Run linting"
	@echo "  format      Format code"
	@echo "  clean       Clean up generated files"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build    Build Docker image"
	@echo "  docker-run      Run Docker container"
	@echo "  docker-test     Run tests in Docker"
	@echo ""
	@echo "Deployment:"
	@echo "  deploy      Deploy using docker-compose"
	@echo "  deploy-prod Deploy to production"
	@echo ""

# Development
install:
	poetry install

dev: install
	poetry install --with=dev

run:
	poetry run python main.py

test:
	poetry run pytest

test-verbose:
	poetry run pytest -v --cov=app --cov-report=html

lint:
	poetry run ruff check app/ tests/
	poetry run mypy app/

format:
	poetry run black app/ tests/
	poetry run ruff check app/ tests/ --fix

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf dist/
	rm -rf build/
	rm -rf *.egg-info/

# Docker
docker-build:
	docker build -t tax-filing-service:latest .

docker-run: docker-build
	docker run -p 8082:8082 --env-file .env tax-filing-service:latest

docker-test: docker-build
	docker run --rm -v $(PWD):/app -w /app tax-filing-service:latest pytest

# Deployment
deploy:
	./scripts/deploy.sh deploy

deploy-prod:
	./scripts/deploy.sh deploy
	./scripts/deploy.sh health

# Database (if using)
db-init:
	docker-compose up -d postgres
	sleep 5
	docker-compose exec postgres psql -U tax_user -d tax_filing_service -f /docker-entrypoint-initdb.d/init.sql

db-backup:
	./scripts/deploy.sh backup

db-restore:
	@echo "Usage: make db-restore BACKUP_DIR=backups/20240101_120000"
	@if [ -n "$(BACKUP_DIR)" ]; then \
		./scripts/deploy.sh restore $(BACKUP_DIR); \
	fi

# Monitoring
logs:
	docker-compose logs -f tax-filing-service

status:
	docker-compose ps

health:
	curl -f http://localhost:8082/health || echo "Service is not healthy"

# Security
security-check:
	pip install safety bandit
	safety check
	bandit -r app/

# Documentation
docs:
	@echo "API documentation available at:"
	@echo "  Swagger UI: http://localhost:8082/docs"
	@echo "  ReDoc: http://localhost:8082/redoc"

# Setup development environment
setup-dev: dev
	cp .env.example .env
	@echo "Development environment setup complete!"
	@echo "Please edit .env file with your configuration."

# CI/CD helpers
ci-test: install test lint

ci-build: docker-build docker-test

# Performance testing
load-test:
	@echo "Running load tests..."
	@echo "Install locust first: pip install locust"
	@echo "Then run: locust -f tests/load_test.py --host=http://localhost:8082"
