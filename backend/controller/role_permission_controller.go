// Package controller provides HTTP controllers for the tax management system.
// This file contains role and permission management API handlers.
package controller

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"backend/service"
	"backend/util"
)

// RolePermissionController 角色权限管理处理器
type RolePermissionController struct {
	rolePermissionService service.RolePermissionService
	permissionService     *service.PermissionService
}

// NewRolePermissionController 创建角色权限管理处理器
func NewRolePermissionController(rolePermissionService service.RolePermissionService, permissionService *service.PermissionService) *RolePermissionController {
	return &RolePermissionController{
		rolePermissionService: rolePermissionService,
		permissionService:     permissionService,
	}
}

// GetRoles 获取角色列表
// GET /roles?enterprise_id=xxx
func (h *RolePermissionController) GetRoles(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 获取企业ID参数（可选）
	enterpriseID := c.Query("enterprise_id")

	// 如果指定了企业ID，检查企业级权限
	if enterpriseID != "" {
		// 检查用户是否是企业成员（所有者、管理员或有权限的成员）
		hasPermission, err := h.permissionService.CheckEnterprisePermission(c.Request.Context(), userID.(string), enterpriseID, "user", "view")
		if err != nil {
			util.InternalServerError(c, "权限检查失败", err)
			return
		}
		if !hasPermission {
			util.Forbidden(c, "没有查看该企业角色的权限")
			return
		}
	}
	// 注意：如果没有指定企业ID，我们仍然返回角色列表，但会在服务层进行过滤

	// 解析分页参数
	page := 1
	pageSize := 10

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if pageSizeStr := c.Query("pageSize"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	// 获取角色列表
	result, err := h.rolePermissionService.GetRolesByContext(c.Request.Context(), page, pageSize, enterpriseID)
	if err != nil {
		util.InternalServerError(c, "获取角色列表失败", err)
		return
	}

	util.Success(c, result, "获取角色列表成功")
}

// GetEnterpriseRoles 获取企业可用角色列表
// GET /enterprises/:id/roles
func (h *RolePermissionController) GetEnterpriseRoles(c *gin.Context) {
	enterpriseID := c.Param("id")
	if enterpriseID == "" {
		util.BadRequest(c, "企业ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查企业级权限
	hasPermission, err := h.permissionService.CheckEnterprisePermission(c.Request.Context(), userID.(string), enterpriseID, "user", "view")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有查看该企业角色的权限")
		return
	}

	// 解析分页参数
	page := 1
	pageSize := 50 // 角色列表通常不会太多，设置较大的默认值

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if pageSizeStr := c.Query("pageSize"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	// 获取企业角色列表
	result, err := h.rolePermissionService.GetRolesByContext(c.Request.Context(), page, pageSize, enterpriseID)
	if err != nil {
		util.InternalServerError(c, "获取企业角色列表失败", err)
		return
	}

	util.Success(c, result, "获取企业角色列表成功")
}

// GetRoleByID 根据ID获取角色
// GET /roles/:id
func (h *RolePermissionController) GetRoleByID(c *gin.Context) {
	roleID := c.Param("id")
	if roleID == "" {
		util.BadRequest(c, "角色ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "role", "view")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有查看角色的权限")
		return
	}

	// 获取角色信息
	role, err := h.rolePermissionService.GetRoleByID(c.Request.Context(), roleID)
	if err != nil {
		util.BadRequest(c, "获取角色失败", err)
		return
	}

	util.Success(c, role, "获取角色成功")
}

// CreateRole 创建角色
// POST /roles
func (h *RolePermissionController) CreateRole(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "role", "create")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有创建角色的权限")
		return
	}

	var req service.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "请求参数错误", err)
		return
	}

	// 创建角色
	role, err := h.rolePermissionService.CreateRole(c.Request.Context(), req)
	if err != nil {
		util.BadRequest(c, "创建角色失败", err)
		return
	}

	util.Success(c, role, "创建角色成功")
}

// UpdateRole 更新角色
// PUT /roles/:id
func (h *RolePermissionController) UpdateRole(c *gin.Context) {
	roleID := c.Param("id")
	if roleID == "" {
		util.BadRequest(c, "角色ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "role", "update")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有更新角色的权限")
		return
	}

	var req service.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "请求参数错误", err)
		return
	}

	// 更新角色
	role, err := h.rolePermissionService.UpdateRole(c.Request.Context(), roleID, req)
	if err != nil {
		util.BadRequest(c, "更新角色失败", err)
		return
	}

	util.Success(c, role, "更新角色成功")
}

// DeleteRole 删除角色
// DELETE /roles/:id
func (h *RolePermissionController) DeleteRole(c *gin.Context) {
	roleID := c.Param("id")
	if roleID == "" {
		util.BadRequest(c, "角色ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "role", "delete")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有删除角色的权限")
		return
	}

	// 删除角色
	err = h.rolePermissionService.DeleteRole(c.Request.Context(), roleID)
	if err != nil {
		util.BadRequest(c, "删除角色失败", err)
		return
	}

	util.Success(c, nil, "删除角色成功")
}

// GetPermissions 获取权限列表
// GET /permissions
func (h *RolePermissionController) GetPermissions(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 暂时跳过权限检查
	_ = userID

	// 获取权限列表
	permissions, err := h.rolePermissionService.GetPermissions(c.Request.Context())
	if err != nil {
		util.InternalServerError(c, "获取权限列表失败", err)
		return
	}

	util.Success(c, permissions, "获取权限列表成功")
}

// GetRolePermissions 获取角色权限
// GET /roles/:id/permissions
func (h *RolePermissionController) GetRolePermissions(c *gin.Context) {
	roleID := c.Param("id")
	if roleID == "" {
		util.BadRequest(c, "角色ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "role", "view")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有查看角色权限的权限")
		return
	}

	// 获取角色权限
	permissions, err := h.rolePermissionService.GetPermissionsByRole(c.Request.Context(), roleID)
	if err != nil {
		util.BadRequest(c, "获取角色权限失败", err)
		return
	}

	util.Success(c, permissions, "获取角色权限成功")
}

// AssignRolePermissions 分配角色权限
// POST /roles/:id/permissions
func (h *RolePermissionController) AssignRolePermissions(c *gin.Context) {
	roleID := c.Param("id")
	if roleID == "" {
		util.BadRequest(c, "角色ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "role", "assign_permission")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有分配角色权限的权限")
		return
	}

	var req struct {
		PermissionIDs []string `json:"permission_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "请求参数错误", err)
		return
	}

	// 分配权限
	err = h.rolePermissionService.AssignPermissionsToRole(c.Request.Context(), roleID, req.PermissionIDs)
	if err != nil {
		util.BadRequest(c, "分配角色权限失败", err)
		return
	}

	util.Success(c, nil, "分配角色权限成功")
}

// RevokeRolePermissions 撤销角色权限
// DELETE /roles/:id/permissions
func (h *RolePermissionController) RevokeRolePermissions(c *gin.Context) {
	roleID := c.Param("id")
	if roleID == "" {
		util.BadRequest(c, "角色ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "role", "revoke_permission")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有撤销角色权限的权限")
		return
	}

	var req struct {
		PermissionIDs []string `json:"permission_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "请求参数错误", err)
		return
	}

	// 撤销权限
	err = h.rolePermissionService.RevokePermissionsFromRole(c.Request.Context(), roleID, req.PermissionIDs)
	if err != nil {
		util.BadRequest(c, "撤销角色权限失败", err)
		return
	}

	util.Success(c, nil, "撤销角色权限成功")
}
