// Package controller provides HTTP controllers for the tax management system.
// It includes controllers for tax type management with CRUD operations.
package controller

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"backend/model"
	"backend/service"
	"backend/util"
)

// TaxTypeController handles tax type related HTTP requests
type TaxTypeController struct {
	taxConfigService *service.TaxConfigService
}

// NewTaxTypeController creates a new tax type handler instance
func NewTaxTypeController(taxConfigService *service.TaxConfigService) *TaxTypeController {
	return &TaxTypeController{
		taxConfigService: taxConfigService,
	}
}

// GetTaxTypes handles GET /tax-types - retrieves tax type list with pagination and filters
func (h *TaxTypeController) GetTaxTypes(c *gin.Context) {
	// 解析查询参数
	page := 1
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}

	pageSize := 10
	if ps := c.Query("pageSize"); ps != "" {
		if parsed, err := strconv.Atoi(ps); err == nil && parsed > 0 {
			pageSize = parsed
		}
	}

	// 构建过滤条件
	filters := make(map[string]interface{})
	if category := c.Query("category"); category != "" {
		filters["category"] = category
	}
	if isActive := c.Query("is_active"); isActive != "" {
		if active, err := strconv.ParseBool(isActive); err == nil {
			filters["is_active"] = active
		}
	}

	// 获取税种列表
	taxTypes, err := h.taxConfigService.ListTaxTypes(c.Request.Context(), filters)
	if err != nil {
		util.InternalServerError(c, "获取税种列表失败", err)
		return
	}

	// 计算分页
	total := int64(len(taxTypes))
	start := (page - 1) * pageSize
	end := start + pageSize
	if end > len(taxTypes) {
		end = len(taxTypes)
	}
	if start > len(taxTypes) {
		start = len(taxTypes)
	}

	paginatedTaxTypes := taxTypes[start:end]

	// 使用统一的分页响应格式
	util.SuccessWithPagination(c, paginatedTaxTypes, total, page, pageSize, "获取税种列表成功")
}

// GetTaxTypeByID handles GET /tax-types/:id - retrieves tax type by ID
func (h *TaxTypeController) GetTaxTypeByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "税种ID不能为空", nil)
		return
	}

	taxType, err := h.taxConfigService.GetTaxType(c.Request.Context(), id)
	if err != nil {
		util.NotFound(c, "税种不存在", err)
		return
	}

	util.Success(c, taxType, "获取税种详情成功")
}

// CreateTaxType handles POST /tax-types - creates a new tax type
func (h *TaxTypeController) CreateTaxType(c *gin.Context) {
	var req model.TaxType
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	// 验证必填字段
	if req.Name == "" {
		util.BadRequest(c, "税种名称不能为空", nil)
		return
	}
	if req.Code == "" {
		util.BadRequest(c, "税种编码不能为空", nil)
		return
	}

	taxType, err := h.taxConfigService.CreateTaxType(c.Request.Context(), req)
	if err != nil {
		util.BadRequest(c, "创建税种失败", err)
		return
	}

	util.Success(c, taxType, "创建税种成功")
}

// UpdateTaxType handles PUT /tax-types/:id - updates tax type
func (h *TaxTypeController) UpdateTaxType(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "税种ID不能为空", nil)
		return
	}

	var req model.TaxType
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	taxType, err := h.taxConfigService.UpdateTaxType(c.Request.Context(), id, req)
	if err != nil {
		util.BadRequest(c, "更新税种失败", err)
		return
	}

	util.Success(c, taxType, "更新税种成功")
}

// DeleteTaxType handles DELETE /tax-types/:id - deletes tax type
func (h *TaxTypeController) DeleteTaxType(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "税种ID不能为空", nil)
		return
	}

	// 软删除：将税种设置为非活跃状态
	req := model.TaxType{IsActive: false}
	_, err := h.taxConfigService.UpdateTaxType(c.Request.Context(), id, req)
	if err != nil {
		util.BadRequest(c, "删除税种失败", err)
		return
	}

	util.Success(c, nil, "删除税种成功")
}

// BatchUpdateTaxTypeStatus handles POST /tax-types/batch/status - batch update tax type status
func (h *TaxTypeController) BatchUpdateTaxTypeStatus(c *gin.Context) {
	var req struct {
		IDs      []string `json:"ids" binding:"required"`
		IsActive bool     `json:"isActive"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	if len(req.IDs) == 0 {
		util.BadRequest(c, "税种ID列表不能为空", nil)
		return
	}

	err := h.taxConfigService.BatchUpdateTaxTypeStatus(c.Request.Context(), req.IDs, req.IsActive)
	if err != nil {
		util.BadRequest(c, "批量更新税种状态失败", err)
		return
	}

	action := "启用"
	if !req.IsActive {
		action = "禁用"
	}
	util.Success(c, nil, fmt.Sprintf("批量%s税种成功", action))
}

// BatchDeleteTaxTypes handles POST /tax-types/batch/delete - batch delete tax types
func (h *TaxTypeController) BatchDeleteTaxTypes(c *gin.Context) {
	var req struct {
		IDs []string `json:"ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	if len(req.IDs) == 0 {
		util.BadRequest(c, "税种ID列表不能为空", nil)
		return
	}

	err := h.taxConfigService.BatchDeleteTaxTypes(c.Request.Context(), req.IDs)
	if err != nil {
		util.BadRequest(c, "批量删除税种失败", err)
		return
	}

	util.Success(c, nil, "批量删除税种成功")
}

// ExportTaxTypes handles GET /tax-types/export - export tax types to Excel
func (h *TaxTypeController) ExportTaxTypes(c *gin.Context) {
	// Parse query parameters for filtering
	filters := make(map[string]interface{})

	if category := c.Query("category"); category != "" {
		filters["category"] = category
	}
	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		if isActive := isActiveStr == "true"; isActiveStr == "true" || isActiveStr == "false" {
			filters["is_active"] = isActive
		}
	}
	if keyword := c.Query("keyword"); keyword != "" {
		filters["keyword"] = keyword
	}

	taxTypes, err := h.taxConfigService.ListTaxTypes(c.Request.Context(), filters)
	if err != nil {
		util.InternalServerError(c, "获取税种列表失败", err)
		return
	}

	// Set response headers for file download
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=tax_types_%s.xlsx",
		time.Now().Format("20060102_150405")))

	// For now, return JSON data (in production, you would generate Excel file)
	util.Success(c, taxTypes, "导出税种数据成功")
}

// ImportTaxTypes handles POST /tax-types/import - import tax types from Excel
func (h *TaxTypeController) ImportTaxTypes(c *gin.Context) {
	// Get uploaded file
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		util.BadRequest(c, "获取上传文件失败", err)
		return
	}
	defer file.Close()

	// Validate file type
	if !strings.HasSuffix(strings.ToLower(header.Filename), ".xlsx") &&
		!strings.HasSuffix(strings.ToLower(header.Filename), ".xls") {
		util.BadRequest(c, "只支持Excel文件格式(.xlsx, .xls)", nil)
		return
	}

	// For now, return success message (in production, you would parse Excel and import data)
	response := map[string]interface{}{
		"fileName":      header.Filename,
		"fileSize":      header.Size,
		"importedCount": 0,
		"message":       "导入功能开发中，需要集成Excel解析库",
	}

	util.Success(c, response, "文件上传成功")
}

// GetTaxRules handles GET /tax-types/:id/rules - get tax rules for a tax type
func (h *TaxTypeController) GetTaxRules(c *gin.Context) {
	taxTypeID := c.Param("id")
	if taxTypeID == "" {
		util.BadRequest(c, "税种ID不能为空", nil)
		return
	}

	rules, err := h.taxConfigService.GetTaxRules(c.Request.Context(), taxTypeID)
	if err != nil {
		util.InternalServerError(c, "获取税则列表失败", err)
		return
	}

	util.Success(c, rules, "获取税则列表成功")
}

// CreateTaxRule handles POST /tax-types/:id/rules - create a new tax rule
func (h *TaxTypeController) CreateTaxRule(c *gin.Context) {
	taxTypeID := c.Param("id")
	if taxTypeID == "" {
		util.BadRequest(c, "税种ID不能为空", nil)
		return
	}

	var req model.TaxRule
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	// 验证必填字段
	if req.Name == "" {
		util.BadRequest(c, "规则名称不能为空", nil)
		return
	}

	rule, err := h.taxConfigService.CreateTaxRule(c.Request.Context(), taxTypeID, req)
	if err != nil {
		util.BadRequest(c, "创建税则失败", err)
		return
	}

	util.Success(c, rule, "创建税则成功")
}

// UpdateTaxRule handles PUT /tax-types/:id/rules/:ruleId - update a tax rule
func (h *TaxTypeController) UpdateTaxRule(c *gin.Context) {
	ruleID := c.Param("ruleId")
	if ruleID == "" {
		util.BadRequest(c, "规则ID不能为空", nil)
		return
	}

	var req model.TaxRule
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	rule, err := h.taxConfigService.UpdateTaxRule(c.Request.Context(), ruleID, req)
	if err != nil {
		util.BadRequest(c, "更新税则失败", err)
		return
	}

	util.Success(c, rule, "更新税则成功")
}

// DeleteTaxRule handles DELETE /tax-types/:id/rules/:ruleId - delete a tax rule
func (h *TaxTypeController) DeleteTaxRule(c *gin.Context) {
	ruleID := c.Param("ruleId")
	if ruleID == "" {
		util.BadRequest(c, "规则ID不能为空", nil)
		return
	}

	// For now, we'll implement soft delete by updating the rule
	// In a full implementation, you might want to add a DeleteTaxRule method to the service
	req := model.TaxRule{
		ExpiryDate: &time.Time{}, // Set expiry date to mark as deleted
	}

	_, err := h.taxConfigService.UpdateTaxRule(c.Request.Context(), ruleID, req)
	if err != nil {
		util.BadRequest(c, "删除税则失败", err)
		return
	}

	util.Success(c, nil, "删除税则成功")
}
