// Package controller provides HTTP controllers for the tax management system.
// It includes controllers for invoice item management with CRUD operations.
package controller

import (
	"github.com/gin-gonic/gin"

	"backend/service"
	"backend/util"
)

// InvoiceItemController handles invoice item-related HTTP requests including creation,
// retrieval, updates, deletion, and batch operations.
type InvoiceItemController struct {
	invoiceItemService service.InvoiceItemService
}

// NewInvoiceItemController creates a new invoice item handler instance
func NewInvoiceItemController(invoiceItemService service.InvoiceItemService) *InvoiceItemController {
	return &InvoiceItemController{
		invoiceItemService: invoiceItemService,
	}
}

// CreateInvoiceItem handles POST /invoice-items - creates a new invoice item
func (h *InvoiceItemController) CreateInvoiceItem(c *gin.Context) {
	var req service.CreateInvoiceItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	item, err := h.invoiceItemService.CreateInvoiceItem(c.Request.Context(), req)
	if err != nil {
		util.BadRequest(c, "创建发票明细失败", err)
		return
	}

	util.Success(c, item, "创建发票明细成功")
}

// GetInvoiceItemByID handles GET /invoice-items/:id - retrieves invoice item by ID
func (h *InvoiceItemController) GetInvoiceItemByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "发票明细ID不能为空")
		return
	}

	item, err := h.invoiceItemService.GetInvoiceItemByID(c.Request.Context(), id)
	if err != nil {
		if err == util.ErrInvoiceItemNotFound {
			util.NotFound(c, "发票明细不存在", err)
		} else {
			util.InternalServerError(c, "获取发票明细失败", err)
		}
		return
	}

	util.Success(c, item, "获取发票明细成功")
}

// GetInvoiceItemsByInvoiceID handles GET /invoices/:id/items - retrieves all items for an invoice
func (h *InvoiceItemController) GetInvoiceItemsByInvoiceID(c *gin.Context) {
	invoiceID := c.Param("id")
	if invoiceID == "" {
		util.BadRequest(c, "发票ID不能为空")
		return
	}

	items, err := h.invoiceItemService.GetInvoiceItemsByInvoiceID(c.Request.Context(), invoiceID)
	if err != nil {
		util.InternalServerError(c, "获取发票明细列表失败", err)
		return
	}

	util.Success(c, items, "获取发票明细列表成功")
}

// UpdateInvoiceItem handles PUT /invoice-items/:id - updates invoice item information
func (h *InvoiceItemController) UpdateInvoiceItem(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "发票明细ID不能为空")
		return
	}

	var req service.UpdateInvoiceItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	// Set the ID from URL parameter
	req.ID = id

	item, err := h.invoiceItemService.UpdateInvoiceItem(c.Request.Context(), id, req)
	if err != nil {
		if err == util.ErrInvoiceItemNotFound {
			util.NotFound(c, "发票明细不存在", err)
		} else {
			util.BadRequest(c, "更新发票明细失败", err)
		}
		return
	}

	util.Success(c, item, "更新发票明细成功")
}

// DeleteInvoiceItem handles DELETE /invoice-items/:id - deletes invoice item by ID
func (h *InvoiceItemController) DeleteInvoiceItem(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "发票明细ID不能为空")
		return
	}

	err := h.invoiceItemService.DeleteInvoiceItem(c.Request.Context(), id)
	if err != nil {
		if err == util.ErrInvoiceItemNotFound {
			util.NotFound(c, "发票明细不存在", err)
		} else {
			util.InternalServerError(c, "删除发票明细失败", err)
		}
		return
	}

	util.Success(c, nil, "删除发票明细成功")
}

// BatchCreateInvoiceItems handles POST /invoices/:id/items/batch - batch creates invoice items
func (h *InvoiceItemController) BatchCreateInvoiceItems(c *gin.Context) {
	invoiceID := c.Param("id")
	if invoiceID == "" {
		util.BadRequest(c, "发票ID不能为空")
		return
	}

	var req struct {
		Items []service.CreateInvoiceItemRequest `json:"items" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	items, err := h.invoiceItemService.BatchCreateInvoiceItems(c.Request.Context(), invoiceID, req.Items)
	if err != nil {
		util.BadRequest(c, "批量创建发票明细失败", err)
		return
	}

	util.Success(c, items, "批量创建发票明细成功")
}

// BatchUpdateInvoiceItems handles PUT /invoices/:id/items/batch - batch updates invoice items
func (h *InvoiceItemController) BatchUpdateInvoiceItems(c *gin.Context) {
	invoiceID := c.Param("id")
	if invoiceID == "" {
		util.BadRequest(c, "发票ID不能为空")
		return
	}

	var req struct {
		Items []service.UpdateInvoiceItemRequest `json:"items" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	items, err := h.invoiceItemService.BatchUpdateInvoiceItems(c.Request.Context(), invoiceID, req.Items)
	if err != nil {
		util.BadRequest(c, "批量更新发票明细失败", err)
		return
	}

	util.Success(c, items, "批量更新发票明细成功")
}

// BatchDeleteInvoiceItems handles DELETE /invoice-items/batch - batch deletes invoice items
func (h *InvoiceItemController) BatchDeleteInvoiceItems(c *gin.Context) {
	var req struct {
		ItemIDs []string `json:"item_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	err := h.invoiceItemService.BatchDeleteInvoiceItems(c.Request.Context(), req.ItemIDs)
	if err != nil {
		util.InternalServerError(c, "批量删除发票明细失败", err)
		return
	}

	util.Success(c, nil, "批量删除发票明细成功")
}
