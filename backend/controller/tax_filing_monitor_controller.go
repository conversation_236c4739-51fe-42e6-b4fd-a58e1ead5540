package controller

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"backend/model"
	"backend/service"
	"backend/util"
)

// TaxFilingMonitorController 监控控制器
type TaxFilingMonitorController struct {
	monitorService  *service.TaxFilingMonitorService
	syncService     *service.TaxFilingSyncService
	callbackService *service.TaxFilingCallbackService
	logger          *zap.Logger
}

// NewTaxFilingMonitorController 创建监控控制器
func NewTaxFilingMonitorController(
	monitorService *service.TaxFilingMonitorService,
	syncService *service.TaxFilingSyncService,
	callbackService *service.TaxFilingCallbackService,
	logger *zap.Logger,
) *TaxFilingMonitorController {
	return &TaxFilingMonitorController{
		monitorService:  monitorService,
		syncService:     syncService,
		callbackService: callbackService,
		logger:          logger,
	}
}

// GetSystemHealth 获取系统健康状态
// @Summary 获取系统健康状态
// @Description 获取税务申报系统的整体健康状态
// @Tags 监控
// @Accept json
// @Produce json
// @Success 200 {object} util.Response{data=model.SystemHealthResponse}
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/monitor/health [get]
func (c *TaxFilingMonitorController) GetSystemHealth(ctx *gin.Context) {
	// 获取系统健康状态
	health, err := c.monitorService.GetSystemHealth(ctx.Request.Context())
	if err != nil {
		c.logger.Error("Failed to get system health", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to get system health", err)
		return
	}

	util.SuccessResponse(ctx, "System health retrieved successfully", health)
}

// GetSystemMetrics 获取系统指标
// @Summary 获取系统指标
// @Description 获取税务申报系统的性能指标
// @Tags 监控
// @Accept json
// @Produce json
// @Success 200 {object} util.Response{data=model.SystemMetrics}
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/monitor/metrics [get]
func (c *TaxFilingMonitorController) GetSystemMetrics(ctx *gin.Context) {
	// 获取系统指标
	metrics, err := c.monitorService.GetSystemMetrics(ctx.Request.Context())
	if err != nil {
		c.logger.Error("Failed to get system metrics", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to get system metrics", err)
		return
	}

	util.SuccessResponse(ctx, "System metrics retrieved successfully", metrics)
}

// GetSyncServiceStatus 获取同步服务状态
// @Summary 获取同步服务状态
// @Description 获取税务申报同步服务的运行状态
// @Tags 监控
// @Accept json
// @Produce json
// @Success 200 {object} util.Response{data=model.SyncServiceStatus}
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/monitor/sync-status [get]
func (c *TaxFilingMonitorController) GetSyncServiceStatus(ctx *gin.Context) {
	// 获取同步服务状态
	status := &model.SyncServiceStatus{
		IsRunning:    c.syncService.IsRunning(),
		LastSyncTime: nil, // 这里应该从服务中获取实际的最后同步时间
	}

	// 获取同步统计信息
	if stats, err := c.syncService.GetSyncStatistics(ctx.Request.Context()); err == nil {
		status.PendingCount = int(stats.PendingCount)
		status.ProcessingCount = int(stats.ProcessingCount)
		status.SubmittedCount = int(stats.SubmittedCount)
		status.SyncInterval = stats.SyncInterval
	}

	util.SuccessResponse(ctx, "Sync service status retrieved successfully", status)
}

// GetCallbackServiceStatus 获取回调服务状态
// @Summary 获取回调服务状态
// @Description 获取税务申报回调服务的运行状态
// @Tags 监控
// @Accept json
// @Produce json
// @Success 200 {object} util.Response{data=model.CallbackServiceStatus}
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/monitor/callback-status [get]
func (c *TaxFilingMonitorController) GetCallbackServiceStatus(ctx *gin.Context) {
	// 获取回调服务状态
	status := &model.CallbackServiceStatus{
		IsRunning: c.callbackService.IsRunning(),
	}

	// 获取回调统计信息
	if stats, err := c.callbackService.GetCallbackStatistics(ctx.Request.Context()); err == nil {
		status.PendingCount = int(stats.PendingCount)
		status.SentCount = int(stats.SentCount)
		status.FailedCount = int(stats.FailedCount)
	}

	util.SuccessResponse(ctx, "Callback service status retrieved successfully", status)
}

// GetAlerts 获取系统告警
// @Summary 获取系统告警
// @Description 获取税务申报系统的告警信息
// @Tags 监控
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param level query string false "告警级别过滤"
// @Param status query string false "告警状态过滤"
// @Success 200 {object} util.Response{data=util.PagedResponse{list=[]model.SystemAlert}}
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/monitor/alerts [get]
func (c *TaxFilingMonitorController) GetAlerts(ctx *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	level := ctx.Query("level")
	status := ctx.Query("status")

	query := &model.AlertQuery{
		Page:     page,
		PageSize: pageSize,
	}

	if level != "" {
		alertLevel := model.AlertLevel(level)
		query.Level = &alertLevel
	}
	if status != "" {
		alertStatus := model.AlertStatus(status)
		query.Status = &alertStatus
	}

	// 获取告警列表
	alerts, total, err := c.monitorService.GetAlerts(ctx.Request.Context(), query)
	if err != nil {
		c.logger.Error("Failed to get alerts", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to get alerts", err)
		return
	}

	// 构建响应
	response := util.PaginatedResponse{
		Data:       alerts,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
	}

	util.SuccessResponse(ctx, "Alerts retrieved successfully", response)
}

// AcknowledgeAlert 确认告警
// @Summary 确认告警
// @Description 确认系统告警
// @Tags 监控
// @Accept json
// @Produce json
// @Param id path string true "告警ID"
// @Success 200 {object} util.Response
// @Failure 400 {object} util.Response
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/monitor/alerts/{id}/acknowledge [post]
func (c *TaxFilingMonitorController) AcknowledgeAlert(ctx *gin.Context) {
	alertID := ctx.Param("id")
	if alertID == "" {
		util.BadRequest(ctx, "Alert ID is required")
		return
	}

	// 确认告警
	err := c.monitorService.AcknowledgeAlert(ctx.Request.Context(), alertID)
	if err != nil {
		c.logger.Error("Failed to acknowledge alert", zap.Error(err), zap.String("alert_id", alertID))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to acknowledge alert", err)
		return
	}

	util.SuccessResponse(ctx, "Alert acknowledged successfully", nil)
}

// GetPerformanceMetrics 获取性能指标
// @Summary 获取性能指标
// @Description 获取税务申报系统的性能指标数据
// @Tags 监控
// @Accept json
// @Produce json
// @Param start_time query string false "开始时间 (RFC3339格式)"
// @Param end_time query string false "结束时间 (RFC3339格式)"
// @Param metrics_type query string false "指标类型过滤"
// @Success 200 {object} util.Response{data=[]model.TaxFilingMetricsResponse}
// @Failure 400 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/monitor/performance [get]
func (c *TaxFilingMonitorController) GetPerformanceMetrics(ctx *gin.Context) {
	startTime := ctx.Query("start_time")
	endTime := ctx.Query("end_time")
	metricsType := ctx.Query("metrics_type")

	query := &model.TaxFilingMetricsQuery{
		Page:     1,
		PageSize: 1000, // 获取更多数据用于图表展示
	}

	if metricsType != "" {
		mType := model.TaxFilingMetricsType(metricsType)
		query.MetricsType = &mType
	}

	// 解析时间参数
	if startTime != "" {
		if parsedTime, err := util.ParseTime(startTime); err == nil {
			query.StartTime = &parsedTime
		}
	}
	if endTime != "" {
		if parsedTime, err := util.ParseTime(endTime); err == nil {
			query.EndTime = &parsedTime
		}
	}

	// 获取性能指标
	perfQuery := &model.PerformanceQuery{}
	if query.StartTime != nil {
		perfQuery.StartTime = *query.StartTime
	}
	if query.EndTime != nil {
		perfQuery.EndTime = *query.EndTime
	}
	metrics, err := c.monitorService.GetPerformanceMetrics(ctx.Request.Context(), perfQuery)
	if err != nil {
		c.logger.Error("Failed to get performance metrics", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to get performance metrics", err)
		return
	}

	util.SuccessResponse(ctx, "Performance metrics retrieved successfully", metrics)
}

// GetAuditLogs 获取审计日志
// @Summary 获取审计日志
// @Description 获取税务申报系统的审计日志
// @Tags 监控
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param action query string false "操作类型过滤"
// @Param operator_id query string false "操作者ID过滤"
// @Param start_time query string false "开始时间 (RFC3339格式)"
// @Param end_time query string false "结束时间 (RFC3339格式)"
// @Success 200 {object} util.Response{data=util.PagedResponse{list=[]model.TaxFilingAuditLogResponse}}
// @Failure 400 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/monitor/audit-logs [get]
func (c *TaxFilingMonitorController) GetAuditLogs(ctx *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	action := ctx.Query("action")
	operatorID := ctx.Query("operator_id")
	startTime := ctx.Query("start_time")
	endTime := ctx.Query("end_time")

	query := &model.TaxFilingAuditLogQuery{
		Page:     page,
		PageSize: pageSize,
	}

	if action != "" {
		auditAction := model.TaxFilingAuditAction(action)
		query.Action = &auditAction
	}
	if operatorID != "" {
		query.OperatorID = &operatorID
	}

	// 解析时间参数
	if startTime != "" {
		if parsedTime, err := util.ParseTime(startTime); err == nil {
			query.StartTime = &parsedTime
		}
	}
	if endTime != "" {
		if parsedTime, err := util.ParseTime(endTime); err == nil {
			query.EndTime = &parsedTime
		}
	}

	// TODO: 实现审计日志获取
	logs := []interface{}{}
	total := int64(0)

	// 构建响应
	response := util.PaginatedResponse{
		Data:       logs,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
	}

	util.SuccessResponse(ctx, "Audit logs retrieved successfully", response)
}

// StartSyncService 启动同步服务
// @Summary 启动同步服务
// @Description 手动启动税务申报同步服务
// @Tags 监控
// @Accept json
// @Produce json
// @Success 200 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/monitor/sync-service/start [post]
func (c *TaxFilingMonitorController) StartSyncService(ctx *gin.Context) {
	err := c.syncService.Start(ctx.Request.Context())
	if err != nil {
		c.logger.Error("Failed to start sync service", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to start sync service", err)
		return
	}

	util.SuccessResponse(ctx, "Sync service started successfully", nil)
}

// StopSyncService 停止同步服务
// @Summary 停止同步服务
// @Description 手动停止税务申报同步服务
// @Tags 监控
// @Accept json
// @Produce json
// @Success 200 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/monitor/sync-service/stop [post]
func (c *TaxFilingMonitorController) StopSyncService(ctx *gin.Context) {
	err := c.syncService.Stop()
	if err != nil {
		c.logger.Error("Failed to stop sync service", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to stop sync service", err)
		return
	}

	util.SuccessResponse(ctx, "Sync service stopped successfully", nil)
}

// StartCallbackService 启动回调服务
// @Summary 启动回调服务
// @Description 手动启动税务申报回调服务
// @Tags 监控
// @Accept json
// @Produce json
// @Success 200 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/monitor/callback-service/start [post]
func (c *TaxFilingMonitorController) StartCallbackService(ctx *gin.Context) {
	err := c.callbackService.Start(ctx.Request.Context())
	if err != nil {
		c.logger.Error("Failed to start callback service", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to start callback service", err)
		return
	}

	util.SuccessResponse(ctx, "Callback service started successfully", nil)
}

// StopCallbackService 停止回调服务
// @Summary 停止回调服务
// @Description 手动停止税务申报回调服务
// @Tags 监控
// @Accept json
// @Produce json
// @Success 200 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/monitor/callback-service/stop [post]
func (c *TaxFilingMonitorController) StopCallbackService(ctx *gin.Context) {
	err := c.callbackService.Stop()
	if err != nil {
		c.logger.Error("Failed to stop callback service", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to stop callback service", err)
		return
	}

	util.SuccessResponse(ctx, "Callback service stopped successfully", nil)
}
