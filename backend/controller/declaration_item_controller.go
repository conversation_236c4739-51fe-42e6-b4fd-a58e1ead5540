// Package controller provides HTTP controllers for the tax management system.
// It includes controllers for declaration item management with CRUD operations.
package controller

import (
	"github.com/gin-gonic/gin"

	"backend/service"
	"backend/util"
)

// DeclarationItemController handles declaration item-related HTTP requests including creation,
// retrieval, updates, deletion, and batch operations.
type DeclarationItemController struct {
	declarationItemService service.DeclarationItemService
}

// NewDeclarationItemController creates a new declaration item handler instance
func NewDeclarationItemController(declarationItemService service.DeclarationItemService) *DeclarationItemController {
	return &DeclarationItemController{
		declarationItemService: declarationItemService,
	}
}

// CreateDeclarationItem handles POST /declaration-items - creates a new declaration item
func (h *DeclarationItemController) CreateDeclarationItem(c *gin.Context) {
	var req service.CreateDeclarationItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	item, err := h.declarationItemService.CreateDeclarationItem(c.Request.Context(), req)
	if err != nil {
		util.BadRequest(c, "创建申报项失败", err)
		return
	}

	util.Success(c, item, "创建申报项成功")
}

// GetDeclarationItemByID handles GET /declaration-items/:id - retrieves declaration item by ID
func (h *DeclarationItemController) GetDeclarationItemByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "申报项ID不能为空")
		return
	}

	item, err := h.declarationItemService.GetDeclarationItemByID(c.Request.Context(), id)
	if err != nil {
		if err == util.ErrDeclarationItemNotFound {
			util.NotFound(c, "申报项不存在", err)
		} else {
			util.InternalServerError(c, "获取申报项失败", err)
		}
		return
	}

	util.Success(c, item, "获取申报项成功")
}

// GetDeclarationItemsByDeclarationID handles GET /declarations/:id/items - retrieves all items for a declaration
func (h *DeclarationItemController) GetDeclarationItemsByDeclarationID(c *gin.Context) {
	declarationID := c.Param("id")
	if declarationID == "" {
		util.BadRequest(c, "申报ID不能为空")
		return
	}

	items, err := h.declarationItemService.GetDeclarationItemsByDeclarationID(c.Request.Context(), declarationID)
	if err != nil {
		util.InternalServerError(c, "获取申报项列表失败", err)
		return
	}

	util.Success(c, items, "获取申报项列表成功")
}

// UpdateDeclarationItem handles PUT /declaration-items/:id - updates declaration item information
func (h *DeclarationItemController) UpdateDeclarationItem(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "申报项ID不能为空")
		return
	}

	var req service.UpdateDeclarationItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	// Set the ID from URL parameter
	req.ID = id

	item, err := h.declarationItemService.UpdateDeclarationItem(c.Request.Context(), id, req)
	if err != nil {
		if err == util.ErrDeclarationItemNotFound {
			util.NotFound(c, "申报项不存在", err)
		} else {
			util.BadRequest(c, "更新申报项失败", err)
		}
		return
	}

	util.Success(c, item, "更新申报项成功")
}

// DeleteDeclarationItem handles DELETE /declaration-items/:id - deletes declaration item by ID
func (h *DeclarationItemController) DeleteDeclarationItem(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "申报项ID不能为空")
		return
	}

	err := h.declarationItemService.DeleteDeclarationItem(c.Request.Context(), id)
	if err != nil {
		if err == util.ErrDeclarationItemNotFound {
			util.NotFound(c, "申报项不存在", err)
		} else {
			util.InternalServerError(c, "删除申报项失败", err)
		}
		return
	}

	util.Success(c, nil, "删除申报项成功")
}

// BatchCreateDeclarationItems handles POST /declarations/:id/items/batch - batch creates declaration items
func (h *DeclarationItemController) BatchCreateDeclarationItems(c *gin.Context) {
	declarationID := c.Param("id")
	if declarationID == "" {
		util.BadRequest(c, "申报ID不能为空")
		return
	}

	var req struct {
		Items []service.CreateDeclarationItemRequest `json:"items" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	items, err := h.declarationItemService.BatchCreateDeclarationItems(c.Request.Context(), declarationID, req.Items)
	if err != nil {
		util.BadRequest(c, "批量创建申报项失败", err)
		return
	}

	util.Success(c, items, "批量创建申报项成功")
}

// BatchUpdateDeclarationItems handles PUT /declarations/:id/items/batch - batch updates declaration items
func (h *DeclarationItemController) BatchUpdateDeclarationItems(c *gin.Context) {
	declarationID := c.Param("id")
	if declarationID == "" {
		util.BadRequest(c, "申报ID不能为空")
		return
	}

	var req struct {
		Items []service.UpdateDeclarationItemRequest `json:"items" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	items, err := h.declarationItemService.BatchUpdateDeclarationItems(c.Request.Context(), declarationID, req.Items)
	if err != nil {
		util.BadRequest(c, "批量更新申报项失败", err)
		return
	}

	util.Success(c, items, "批量更新申报项成功")
}

// BatchDeleteDeclarationItems handles DELETE /declaration-items/batch - batch deletes declaration items
func (h *DeclarationItemController) BatchDeleteDeclarationItems(c *gin.Context) {
	var req struct {
		ItemIDs []string `json:"item_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	err := h.declarationItemService.BatchDeleteDeclarationItems(c.Request.Context(), req.ItemIDs)
	if err != nil {
		util.InternalServerError(c, "批量删除申报项失败", err)
		return
	}

	util.Success(c, nil, "批量删除申报项成功")
}

// GetDeclarationTotals handles GET /declarations/:id/totals - retrieves declaration totals
func (h *DeclarationItemController) GetDeclarationTotals(c *gin.Context) {
	declarationID := c.Param("id")
	if declarationID == "" {
		util.BadRequest(c, "申报ID不能为空")
		return
	}

	totals, err := h.declarationItemService.CalculateDeclarationTotals(c.Request.Context(), declarationID)
	if err != nil {
		util.InternalServerError(c, "计算申报汇总失败", err)
		return
	}

	util.Success(c, totals, "获取申报汇总成功")
}
