// Package controller provides HTTP controllers for the tax management system.
// It includes controllers for authentication, enterprise management, declarations, invoices, and file uploads.
package controller

import (
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"backend/util"
)

// UploadController handles file upload related HTTP requests including
// document uploads, image processing, and file validation for the tax management system.
type UploadController struct {
	uploadPath string // 文件上传路径
	maxSize    int64  // 最大文件大小限制
}

// NewUploadController creates a new upload handler instance with default configuration.
// It initializes the upload directory and sets default file size limits.
func NewUploadController() *UploadController {
	uploadPath := "./uploads"
	// 确保上传目录存在
	if err := os.MkdirAll(uploadPath, 0755); err != nil {
		panic(fmt.Sprintf("创建上传目录失败: %v", err))
	}

	return &UploadController{
		uploadPath: uploadPath,
		maxSize:    10 << 20, // 10MB
	}
}

// UploadFile handles POST /upload - processes file upload requests
func (h *UploadController) UploadFile(c *gin.Context) {
	// 获取用户ID
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 解析multipart表单
	err := c.Request.ParseMultipartForm(h.maxSize)
	if err != nil {
		util.BadRequest(c, "文件大小超过限制", err)
		return
	}

	// 获取文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		util.BadRequest(c, "获取文件失败", err)
		return
	}
	defer file.Close()

	// 验证文件类型
	fileType := c.PostForm("type")
	if !h.isValidFileType(header.Filename, fileType) {
		util.BadRequest(c, "不支持的文件类型")
		return
	}

	// 验证文件大小
	if header.Size > h.maxSize {
		util.BadRequest(c, "文件大小超过限制")
		return
	}

	// 生成文件名
	ext := filepath.Ext(header.Filename)
	filename := fmt.Sprintf("%s_%d%s", uuid.New().String(), time.Now().Unix(), ext)

	// 创建目标目录
	targetDir := filepath.Join(h.uploadPath, fileType)
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		util.InternalServerError(c, "创建目录失败", err)
		return
	}

	// 保存文件
	targetPath := filepath.Join(targetDir, filename)
	if err := h.saveFile(file, targetPath); err != nil {
		util.InternalServerError(c, "保存文件失败", err)
		return
	}

	// 返回文件信息
	fileURL := fmt.Sprintf("/uploads/%s/%s", fileType, filename)
	response := map[string]interface{}{
		"id":       uuid.New().String(),
		"filename": header.Filename,
		"url":      fileURL,
		"size":     header.Size,
		"type":     fileType,
		"uploadAt": time.Now(),
	}

	util.Success(c, response, "文件上传成功")
}

// UploadAvatar handles POST /upload/avatar - processes avatar upload requests
func (h *UploadController) UploadAvatar(c *gin.Context) {
	// 获取用户ID
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 解析multipart表单
	err := c.Request.ParseMultipartForm(2 << 20) // 2MB for avatar
	if err != nil {
		util.BadRequest(c, "文件大小超过限制", err)
		return
	}

	// 获取文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		util.BadRequest(c, "获取文件失败", err)
		return
	}
	defer file.Close()

	// 验证是否为图片
	if !h.isImageFile(header.Filename) {
		util.BadRequest(c, "只能上传图片文件")
		return
	}

	// 验证文件大小
	if header.Size > 2<<20 { // 2MB
		util.BadRequest(c, "头像文件大小不能超过2MB")
		return
	}

	// 生成文件名
	ext := filepath.Ext(header.Filename)
	filename := fmt.Sprintf("avatar_%s_%d%s", userID, time.Now().Unix(), ext)

	// 创建头像目录
	avatarDir := filepath.Join(h.uploadPath, "avatars")
	if err := os.MkdirAll(avatarDir, 0755); err != nil {
		util.InternalServerError(c, "创建目录失败", err)
		return
	}

	// 保存文件
	targetPath := filepath.Join(avatarDir, filename)
	if err := h.saveFile(file, targetPath); err != nil {
		util.InternalServerError(c, "保存文件失败", err)
		return
	}

	// 返回头像URL
	avatarURL := fmt.Sprintf("/uploads/avatars/%s", filename)
	response := map[string]interface{}{
		"url":      avatarURL,
		"filename": header.Filename,
		"size":     header.Size,
	}

	util.Success(c, response, "头像上传成功")
}

// DeleteFile handles DELETE /upload/:id - deletes a file by ID
func (h *UploadController) DeleteFile(c *gin.Context) {
	fileID := c.Param("id")
	if fileID == "" {
		util.BadRequest(c, "文件ID不能为空")
		return
	}

	// TODO: Replace with actual service call to query and delete file
	util.Success(c, nil, "文件删除成功")
}

// GetFileInfo handles GET /upload/:id - retrieves file information by ID
func (h *UploadController) GetFileInfo(c *gin.Context) {
	fileID := c.Param("id")
	if fileID == "" {
		util.BadRequest(c, "文件ID不能为空")
		return
	}

	// TODO: Replace with actual service call to query file information
	response := map[string]interface{}{
		"id":       fileID,
		"filename": "example.jpg",
		"url":      "/uploads/example.jpg",
		"size":     1024,
		"type":     "image",
	}

	util.Success(c, response, "获取文件信息成功")
}

// saveFile saves uploaded file to the specified destination path
func (h *UploadController) saveFile(src multipart.File, dst string) error {
	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	_, err = io.Copy(out, src)
	return err
}

// isValidFileType validates file type based on extension and category
func (h *UploadController) isValidFileType(filename, fileType string) bool {
	ext := strings.ToLower(filepath.Ext(filename))

	switch fileType {
	case "avatar", "image":
		return ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".gif"
	case "document":
		return ext == ".pdf" || ext == ".doc" || ext == ".docx" || ext == ".xls" || ext == ".xlsx"
	case "archive":
		return ext == ".zip" || ext == ".rar" || ext == ".7z"
	default:
		return false
	}
}

// isImageFile validates if the file is an image based on its extension
func (h *UploadController) isImageFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".gif"
}
