// Package controller provides HTTP controllers for the tax management system.
// It includes controllers for authentication, enterprise management, declarations, invoices, and file uploads.
package controller

import (
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"backend/service"
	"backend/util"
)

// EnterpriseController handles enterprise-related HTTP requests including creation,
// retrieval, updates, deletion, and enterprise statistics management.
type EnterpriseController struct {
	enterpriseService service.EnterpriseService
}

// NewEnterpriseController creates a new enterprise handler instance with the provided service.
// It returns a configured EnterpriseController ready to handle HTTP requests.
func NewEnterpriseController(enterpriseService service.EnterpriseService) *EnterpriseController {
	return &EnterpriseController{
		enterpriseService: enterpriseService,
	}
}

// CreateEnterprise handles POST /enterprises - creates a new enterprise
func (h *EnterpriseController) CreateEnterprise(c *gin.Context) {
	var req service.CreateEnterpriseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	// 从JWT中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "无法获取用户信息")
		return
	}
	req.UserID = userID.(string)

	enterprise, err := h.enterpriseService.CreateEnterprise(c.Request.Context(), req)
	if err != nil {
		util.BadRequest(c, util.MsgEnterpriseCreateFailed, err)
		return
	}

	util.Success(c, enterprise, util.MsgEnterpriseCreated)
}

// GetEnterprises handles GET /enterprises - retrieves enterprise list with pagination and filters
func (h *EnterpriseController) GetEnterprises(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "无法获取用户信息")
		return
	}

	var req service.GetEnterprisesRequest
	req.UserID = userID.(string)

	// 解析查询参数
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}
	if req.Page <= 0 {
		req.Page = 1
	}

	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	req.Keyword = c.Query("keyword")
	req.Industry = c.Query("industry")
	req.Status = c.Query("status")

	result, err := h.enterpriseService.GetEnterprises(c.Request.Context(), req)
	if err != nil {
		util.InternalServerError(c, "获取企业列表失败", err)
		return
	}

	// 使用统一的分页响应格式
	util.SuccessWithPagination(c, result.Items, result.Total, result.Page, result.PageSize, "获取企业列表成功")
}

// GetEnterpriseByID handles GET /enterprises/:id - retrieves enterprise by ID
func (h *EnterpriseController) GetEnterpriseByID(c *gin.Context) {
	id := c.Param("id")

	// 验证企业ID格式
	if id == "" || id == "undefined" || id == "null" {
		util.BadRequest(c, util.MsgBadRequest)
		return
	}

	// 从JWT中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "无法获取用户信息")
		return
	}

	enterprise, err := h.enterpriseService.GetEnterpriseByID(c.Request.Context(), id, userID.(string))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			util.BadRequest(c, "企业不存在")
		} else {
			util.InternalServerError(c, "获取企业信息失败", err)
		}
		return
	}

	util.Success(c, enterprise, "获取企业信息成功")
}

// UpdateEnterprise handles PUT /enterprises/:id - updates enterprise information
func (h *EnterpriseController) UpdateEnterprise(c *gin.Context) {
	id := c.Param("id")
	if id == "" || id == "undefined" || id == "null" {
		util.BadRequest(c, "企业ID无效")
		return
	}

	var req service.UpdateEnterpriseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	// 从JWT中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "无法获取用户信息")
		return
	}
	req.UserID = userID.(string)

	enterprise, err := h.enterpriseService.UpdateEnterprise(c.Request.Context(), id, req)
	if err != nil {
		util.BadRequest(c, "更新企业信息失败", err)
		return
	}

	util.Success(c, enterprise, "更新企业信息成功")
}

// DeleteEnterprise handles DELETE /enterprises/:id - deletes enterprise by ID
func (h *EnterpriseController) DeleteEnterprise(c *gin.Context) {
	id := c.Param("id")
	if id == "" || id == "undefined" || id == "null" {
		util.BadRequest(c, "企业ID无效")
		return
	}

	// 从JWT中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "无法获取用户信息")
		return
	}

	err := h.enterpriseService.DeleteEnterprise(c.Request.Context(), id, userID.(string))
	if err != nil {
		util.BadRequest(c, "删除企业失败", err)
		return
	}

	util.Success(c, nil, "删除企业成功")
}

// GetEnterpriseStats handles GET /enterprises/stats - retrieves enterprise statistics
func (h *EnterpriseController) GetEnterpriseStats(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "无法获取用户信息")
		return
	}

	stats, err := h.enterpriseService.GetEnterpriseStats(c.Request.Context(), userID.(string))
	if err != nil {
		util.InternalServerError(c, "获取企业统计信息失败", err)
		return
	}

	util.Success(c, stats, "获取企业统计信息成功")
}

// GetEnterpriseDetailStats handles GET /enterprises/:id/stats - retrieves enterprise detail statistics
func (h *EnterpriseController) GetEnterpriseDetailStats(c *gin.Context) {
	id := c.Param("id")
	if id == "" || id == "undefined" || id == "null" {
		util.BadRequest(c, "企业ID无效")
		return
	}

	// 从JWT中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "无法获取用户信息")
		return
	}

	stats, err := h.enterpriseService.GetEnterpriseDetailStats(c.Request.Context(), id, userID.(string))
	if err != nil {
		if err.Error() == "企业不存在" {
			util.BadRequest(c, "企业不存在")
		} else {
			util.InternalServerError(c, "获取企业详情统计信息失败", err)
		}
		return
	}

	util.Success(c, stats, "获取企业详情统计信息成功")
}

// GetEnterpriseDeclarations handles GET /enterprises/:id/declarations - retrieves enterprise declarations
func (h *EnterpriseController) GetEnterpriseDeclarations(c *gin.Context) {
	id := c.Param("id")
	if id == "" || id == "undefined" || id == "null" {
		util.BadRequest(c, "企业ID无效")
		return
	}

	// 从JWT中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "无法获取用户信息")
		return
	}

	// 获取limit参数，默认为10
	limit := 10
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	declarations, err := h.enterpriseService.GetEnterpriseDeclarations(c.Request.Context(), id, userID.(string), limit)
	if err != nil {
		if err.Error() == "企业不存在" {
			util.BadRequest(c, "企业不存在")
		} else {
			util.InternalServerError(c, "获取企业申报记录失败", err)
		}
		return
	}

	util.Success(c, declarations, "获取企业申报记录成功")
}
