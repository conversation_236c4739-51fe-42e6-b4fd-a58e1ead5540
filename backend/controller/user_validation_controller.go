// Package controller provides HTTP controllers for the tax management system.
// This file implements user validation endpoints for ensuring data uniqueness and integrity.
package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"backend/service"
	"backend/util"
)

// UserValidationController handles user validation requests
type UserValidationController struct {
	validationService *service.UserValidationService
}

// NewUserValidationController creates a new user validation handler
func NewUserValidationController(validationService *service.UserValidationService) *UserValidationController {
	return &UserValidationController{
		validationService: validationService,
	}
}

// ValidateUserName handles POST /users/validate-username - validates if a username is available
func (h *UserValidationController) ValidateUserName(c *gin.Context) {
	var req service.ValidateUserNameRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "无效的请求参数", err)
		return
	}

	response, err := h.validationService.ValidateUserName(c.Request.Context(), req)
	if err != nil {
		util.InternalServerError(c, "验证用户名失败", err)
		return
	}

	if response.Valid {
		util.Success(c, response, "用户名可用")
	} else {
		c.JSON(http.StatusOK, util.APIResponse{
			Code:    http.StatusConflict,
			Message: response.Message,
			Data:    response,
		})
	}
}

// ValidateEmail handles POST /users/validate-email - validates if an email is available
func (h *UserValidationController) ValidateEmail(c *gin.Context) {
	var req service.ValidateEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "无效的请求参数", err)
		return
	}

	response, err := h.validationService.ValidateEmail(c.Request.Context(), req)
	if err != nil {
		util.InternalServerError(c, "验证邮箱失败", err)
		return
	}

	if response.Valid {
		util.Success(c, response, "邮箱可用")
	} else {
		c.JSON(http.StatusOK, util.APIResponse{
			Code:    http.StatusConflict,
			Message: response.Message,
			Data:    response,
		})
	}
}

// ValidatePhone handles POST /users/validate-phone - validates if a phone number is available
func (h *UserValidationController) ValidatePhone(c *gin.Context) {
	var req service.ValidatePhoneRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "无效的请求参数", err)
		return
	}

	response, err := h.validationService.ValidatePhone(c.Request.Context(), req)
	if err != nil {
		util.InternalServerError(c, "验证手机号失败", err)
		return
	}

	if response.Valid {
		util.Success(c, response, "手机号可用")
	} else {
		c.JSON(http.StatusOK, util.APIResponse{
			Code:    http.StatusConflict,
			Message: response.Message,
			Data:    response,
		})
	}
}
