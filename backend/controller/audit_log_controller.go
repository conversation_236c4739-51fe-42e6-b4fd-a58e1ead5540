// Package controller provides HTTP controllers for the tax management system.
// It includes controllers for audit log management operations.
package controller

import (
	"errors"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"backend/service"
	"backend/util"
)

// AuditLogController handles audit log-related HTTP requests including creation,
// retrieval, deletion, and audit log management operations.
type AuditLogController struct {
	auditLogService service.AuditLogService
}

// NewAuditLogController creates a new audit log handler instance
func NewAuditLogController(auditLogService service.AuditLogService) *AuditLogController {
	return &AuditLogController{
		auditLogService: auditLogService,
	}
}

// CreateAuditLog handles POST /audit-logs - creates a new audit log
func (h *AuditLogController) CreateAuditLog(c *gin.Context) {
	var req service.CreateAuditLogRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	auditLog, err := h.auditLogService.CreateAuditLog(c.Request.Context(), req)
	if err != nil {
		util.BadRequest(c, "创建审计日志失败", err)
		return
	}

	util.Success(c, auditLog, "创建审计日志成功")
}

// GetAuditLogs handles GET /audit-logs - retrieves audit logs with pagination and filtering
func (h *AuditLogController) GetAuditLogs(c *gin.Context) {
	var req service.GetAuditLogsRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}
	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}

	req.UserID = c.Query("userId")
	req.Action = c.Query("action")
	req.ResourceType = c.Query("resourceType")
	req.ResourceID = c.Query("resourceId")
	req.Status = c.Query("status")
	req.Module = c.Query("module")
	req.EnterpriseID = c.Query("enterpriseId")
	req.RiskLevel = c.Query("riskLevel")
	req.SortBy = c.Query("sortBy")
	req.SortOrder = c.Query("sortOrder")

	if startDate := c.Query("startDate"); startDate != "" {
		if t, err := time.Parse("2006-01-02", startDate); err == nil {
			req.StartDate = t
		}
	}

	if endDate := c.Query("endDate"); endDate != "" {
		if t, err := time.Parse("2006-01-02", endDate); err == nil {
			req.EndDate = t
		}
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	result, err := h.auditLogService.GetAuditLogs(c.Request.Context(), req)
	if err != nil {
		util.InternalServerError(c, "获取审计日志列表失败", err)
		return
	}

	util.Success(c, result, "获取审计日志列表成功")
}

// GetAuditLogByID handles GET /audit-logs/:id - retrieves audit log by ID
func (h *AuditLogController) GetAuditLogByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" || id == "undefined" || id == "null" {
		util.BadRequest(c, "审计日志ID无效")
		return
	}

	auditLog, err := h.auditLogService.GetAuditLogByID(c.Request.Context(), id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			util.BadRequest(c, "审计日志不存在")
		} else {
			util.InternalServerError(c, "获取审计日志详情失败", err)
		}
		return
	}

	util.Success(c, auditLog, "获取审计日志详情成功")
}

// DeleteAuditLog handles DELETE /audit-logs/:id - deletes audit log by ID
func (h *AuditLogController) DeleteAuditLog(c *gin.Context) {
	id := c.Param("id")
	if id == "" || id == "undefined" || id == "null" {
		util.BadRequest(c, "审计日志ID无效")
		return
	}

	err := h.auditLogService.DeleteAuditLog(c.Request.Context(), id)
	if err != nil {
		util.BadRequest(c, "删除审计日志失败", err)
		return
	}

	util.Success(c, nil, "删除审计日志成功")
}

// GetAuditLogsByUser handles GET /audit-logs/user/:userId - retrieves audit logs for a specific user
func (h *AuditLogController) GetAuditLogsByUser(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" || userID == "undefined" || userID == "null" {
		util.BadRequest(c, "用户ID无效")
		return
	}

	var req service.GetAuditLogsRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}
	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}

	req.SortBy = c.Query("sortBy")
	req.SortOrder = c.Query("sortOrder")

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	result, err := h.auditLogService.GetAuditLogsByUser(c.Request.Context(), userID, req)
	if err != nil {
		util.InternalServerError(c, "获取用户审计日志失败", err)
		return
	}

	util.Success(c, result, "获取用户审计日志成功")
}

// GetAuditLogsByResource handles GET /audit-logs/resource/:resourceType/:resourceId - retrieves audit logs for a specific resource
func (h *AuditLogController) GetAuditLogsByResource(c *gin.Context) {
	resourceType := c.Param("resourceType")
	resourceID := c.Param("resourceId")

	if resourceType == "" || resourceID == "" {
		util.BadRequest(c, "资源类型和资源ID不能为空")
		return
	}

	var req service.GetAuditLogsRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}
	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}

	req.SortBy = c.Query("sortBy")
	req.SortOrder = c.Query("sortOrder")

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	result, err := h.auditLogService.GetAuditLogsByResource(c.Request.Context(), resourceType, resourceID, req)
	if err != nil {
		util.InternalServerError(c, "获取资源审计日志失败", err)
		return
	}

	util.Success(c, result, "获取资源审计日志成功")
}

// CleanupOldLogs handles POST /audit-logs/cleanup - cleans up old audit logs
func (h *AuditLogController) CleanupOldLogs(c *gin.Context) {
	var req struct {
		RetentionDays int `json:"retentionDays" binding:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	err := h.auditLogService.CleanupOldLogs(c.Request.Context(), req.RetentionDays)
	if err != nil {
		util.BadRequest(c, "清理旧审计日志失败", err)
		return
	}

	util.Success(c, nil, "清理旧审计日志成功")
}

// GetAuditLogStats handles GET /audit-logs/stats - retrieves audit log statistics
func (h *AuditLogController) GetAuditLogStats(c *gin.Context) {
	var req service.GetAuditLogStatsRequest

	if startDate := c.Query("startDate"); startDate != "" {
		if t, err := time.Parse("2006-01-02", startDate); err == nil {
			req.StartDate = t
		}
	}

	if endDate := c.Query("endDate"); endDate != "" {
		if t, err := time.Parse("2006-01-02", endDate); err == nil {
			req.EndDate = t
		}
	}

	req.EnterpriseID = c.Query("enterpriseId")
	req.UserID = c.Query("userId")

	stats, err := h.auditLogService.GetAuditLogStats(c.Request.Context(), req)
	if err != nil {
		util.InternalServerError(c, "获取审计日志统计失败", err)
		return
	}

	util.Success(c, stats, "获取审计日志统计成功")
}

// GetMyAuditLogs handles GET /audit-logs/my - gets audit logs for current user
func (h *AuditLogController) GetMyAuditLogs(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	var req service.GetAuditLogsRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}
	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}

	req.SortBy = c.Query("sortBy")
	req.SortOrder = c.Query("sortOrder")

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	result, err := h.auditLogService.GetAuditLogsByUser(c.Request.Context(), userID, req)
	if err != nil {
		util.InternalServerError(c, "获取我的审计日志失败", err)
		return
	}

	util.Success(c, result, "获取我的审计日志成功")
}

// ExportAuditLogs handles GET /audit-logs/export - exports audit logs to file
func (h *AuditLogController) ExportAuditLogs(c *gin.Context) {
	// TODO: Implement audit log export functionality
	// This could export to CSV, Excel, or JSON format

	util.Success(c, gin.H{
		"message": "审计日志导出功能待实现",
		"format":  c.Query("format"),
	}, "导出请求已接收")
}
