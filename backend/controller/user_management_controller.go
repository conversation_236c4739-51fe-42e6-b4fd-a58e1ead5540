// Package controller provides HTTP controllers for the tax management system.
// It includes user management, role assignment, permission control, and enterprise user operations.
package controller

import (
	"context"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"backend/model"
	"backend/service"
	"backend/util"
)

// UserManagementController handles user management operations including user invitation,
// role assignment, permission management, and enterprise user relationships.
type UserManagementController struct {
	userManagementService *service.UserManagementService
	permissionService     *service.PermissionService
}

// NewUserManagementController creates a new user management handler with the provided services.
// It returns a configured UserManagementController ready to handle HTTP requests.
func NewUserManagementController(userManagementService *service.UserManagementService, permissionService *service.PermissionService) *UserManagementController {
	return &UserManagementController{
		userManagementService: userManagementService,
		permissionService:     permissionService,
	}
}

// GetEnterpriseUsers 获取企业用户列表
// GET /enterprises/:id/users
func (h *UserManagementController) GetEnterpriseUsers(c *gin.Context) {
	enterpriseID := c.Param("id")
	if enterpriseID == "" {
		util.BadRequest(c, "企业ID不能为空")
		return
	}

	// 如果传入的是数字ID，转换为字符串ID
	if actualID, err := h.resolveEnterpriseID(c.Request.Context(), enterpriseID); err == nil {
		enterpriseID = actualID
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查企业级权限
	hasPermission, err := h.permissionService.CheckEnterprisePermission(c.Request.Context(), userID.(string), enterpriseID, "user", "view")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有查看该企业用户的权限")
		return
	}

	// 解析分页参数
	page := 1
	pageSize := 10

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if pageSizeStr := c.Query("pageSize"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	// 获取用户列表
	result, err := h.userManagementService.GetEnterpriseUsers(c.Request.Context(), enterpriseID, page, pageSize)
	if err != nil {
		util.InternalServerError(c, "获取用户列表失败", err)
		return
	}

	util.Success(c, result, "获取用户列表成功")
}

// InviteUser 邀请用户加入企业
// POST /enterprises/:id/users
func (h *UserManagementController) InviteUser(c *gin.Context) {
	enterpriseID := c.Param("id")
	if enterpriseID == "" {
		util.BadRequest(c, "企业ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查企业级权限
	hasPermission, err := h.permissionService.CheckEnterprisePermission(c.Request.Context(), userID.(string), enterpriseID, "user", "create")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有邀请用户的权限")
		return
	}

	var req model.InviteUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "请求参数错误", err)
		return
	}

	// 邀请用户
	enterpriseUser, err := h.userManagementService.InviteUserToEnterprise(c.Request.Context(), req, enterpriseID, userID.(string))
	if err != nil {
		util.BadRequest(c, "邀请用户失败", err)
		return
	}

	util.Success(c, enterpriseUser, "邀请用户成功")
}

// RemoveUser 从企业中移除用户
// DELETE /enterprises/:id/users/:userId
func (h *UserManagementController) RemoveUser(c *gin.Context) {
	enterpriseID := c.Param("id")
	targetUserID := c.Param("userId")

	if enterpriseID == "" || targetUserID == "" {
		util.BadRequest(c, "企业ID和用户ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查企业级权限
	hasPermission, err := h.permissionService.CheckEnterprisePermission(c.Request.Context(), userID.(string), enterpriseID, "user", "delete")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有移除用户的权限")
		return
	}

	// 移除用户
	err = h.userManagementService.RemoveUserFromEnterprise(c.Request.Context(), enterpriseID, targetUserID, userID.(string))
	if err != nil {
		util.BadRequest(c, "移除用户失败", err)
		return
	}

	util.Success(c, nil, "移除用户成功")
}

// UpdateUserRole 更新用户角色
// PUT /enterprises/:id/users/:userId/role
func (h *UserManagementController) UpdateUserRole(c *gin.Context) {
	enterpriseID := c.Param("id")
	targetUserID := c.Param("userId")

	if enterpriseID == "" || targetUserID == "" {
		util.BadRequest(c, "企业ID和用户ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查企业级权限
	hasPermission, err := h.permissionService.CheckEnterprisePermission(c.Request.Context(), userID.(string), enterpriseID, "user", "manage")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有分配角色的权限")
		return
	}

	var req struct {
		RoleID string `json:"role_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "请求参数错误", err)
		return
	}

	// 更新用户角色
	err = h.userManagementService.UpdateUserRole(c.Request.Context(), enterpriseID, targetUserID, req.RoleID, userID.(string))
	if err != nil {
		util.BadRequest(c, "更新用户角色失败", err)
		return
	}

	util.Success(c, nil, "更新用户角色成功")
}

// TransferOwnership 转让企业所有权
// POST /enterprises/:id/transfer-ownership
func (h *UserManagementController) TransferOwnership(c *gin.Context) {
	enterpriseID := c.Param("id")
	if enterpriseID == "" {
		util.BadRequest(c, "企业ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查是否为企业所有者（只有所有者才能转让所有权）
	isOwner, err := h.permissionService.CheckEnterpriseOwnership(c.Request.Context(), userID.(string), enterpriseID)
	if err != nil {
		util.InternalServerError(c, "所有权检查失败", err)
		return
	}
	if !isOwner {
		util.Forbidden(c, "只有企业所有者才能转让所有权")
		return
	}

	var req model.TransferOwnershipRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "请求参数错误", err)
		return
	}

	// 转让所有权
	err = h.userManagementService.TransferOwnership(c.Request.Context(), enterpriseID, userID.(string), req)
	if err != nil {
		util.BadRequest(c, "转让企业所有权失败", err)
		return
	}

	util.Success(c, nil, "转让企业所有权成功")
}

// CheckOwnerUniqueness 检查用户是否已经拥有企业
// GET /users/:userId/check-ownership?role_code=owner
func (h *UserManagementController) CheckOwnerUniqueness(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		util.BadRequest(c, "用户ID不能为空")
		return
	}

	// 获取当前用户ID
	_, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 获取目标角色代码和企业ID
	roleCode := c.Query("role_code")
	enterpriseID := c.Query("enterprise_id")

	// 只有在分配Owner角色时才检查企业级所有权唯一性
	if roleCode == "owner" && enterpriseID != "" {
		err := h.userManagementService.ValidateEnterpriseOwnerUniqueness(c.Request.Context(), enterpriseID, userID)
		if err != nil {
			util.Success(c, map[string]interface{}{
				"has_enterprise": true,
				"can_be_owner":   false,
				"message":        err.Error(),
			}, "企业已有所有者")
			return
		}

		util.Success(c, map[string]interface{}{
			"has_enterprise": false,
			"can_be_owner":   true,
			"message":        "用户可以成为该企业的所有者",
		}, "检查完成")
		return
	}

	// 对于非Owner角色或未指定企业，用户可以加入
	util.Success(c, map[string]interface{}{
		"has_enterprise": false,
		"can_join":       true,
		"message":        "用户可以加入企业",
	}, "检查完成")
}

// GetUserPermissions 获取用户权限列表
// GET /users/:userId/permissions
func (h *UserManagementController) GetUserPermissions(c *gin.Context) {
	targetUserID := c.Param("userId")
	if targetUserID == "" {
		util.BadRequest(c, "用户ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "permission", "view")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有查看权限的权限")
		return
	}

	// 获取用户权限
	permissions, err := h.permissionService.GetUserPermissions(c.Request.Context(), targetUserID)
	if err != nil {
		util.InternalServerError(c, "获取用户权限失败", err)
		return
	}

	util.Success(c, permissions, "获取用户权限成功")
}

// GetUserRoles 获取用户角色列表
// GET /users/:userId/roles
func (h *UserManagementController) GetUserRoles(c *gin.Context) {
	targetUserID := c.Param("userId")
	if targetUserID == "" {
		util.BadRequest(c, "用户ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "role", "view")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有查看角色的权限")
		return
	}

	// 获取用户角色
	roles, err := h.permissionService.GetUserRoles(c.Request.Context(), targetUserID)
	if err != nil {
		util.InternalServerError(c, "获取用户角色失败", err)
		return
	}

	util.Success(c, roles, "获取用户角色成功")
}

// SearchUsers 搜索用户（用于邀请功能）
// GET /users/search
func (h *UserManagementController) SearchUsers(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "user", "view")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有搜索用户的权限")
		return
	}

	keyword := c.Query("keyword")
	if keyword == "" {
		util.BadRequest(c, "搜索关键词不能为空")
		return
	}

	// 解析分页参数
	page := 1
	pageSize := 10

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if pageSizeStr := c.Query("pageSize"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 50 {
			pageSize = ps
		}
	}

	// 搜索用户
	result, err := h.userManagementService.SearchUsers(c.Request.Context(), keyword, page, pageSize)
	if err != nil {
		util.InternalServerError(c, "搜索用户失败", err)
		return
	}

	util.Success(c, result, "搜索用户成功")
}

// GetEnterpriseUsersV2 获取企业用户列表（增强版）
// GET /enterprises/:id/users/v2
func (h *UserManagementController) GetEnterpriseUsersV2(c *gin.Context) {
	enterpriseID := c.Param("id")
	if enterpriseID == "" {
		util.BadRequest(c, "企业ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "user", "read")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有查看用户的权限")
		return
	}

	// 解析请求参数
	var req model.EnterpriseUserListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.BadRequest(c, "请求参数错误", err)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 获取用户列表
	result, err := h.userManagementService.GetEnterpriseUsersV2(c.Request.Context(), enterpriseID, req)
	if err != nil {
		util.InternalServerError(c, "获取用户列表失败", err)
		return
	}

	util.Success(c, result, "获取用户列表成功")
}

// AssignRole 分配角色
// PUT /enterprises/:id/users/:userId/role/v2
func (h *UserManagementController) AssignRole(c *gin.Context) {
	enterpriseID := c.Param("id")
	targetUserID := c.Param("userId")

	if enterpriseID == "" || targetUserID == "" {
		util.BadRequest(c, "企业ID和用户ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查企业级权限
	hasPermission, err := h.permissionService.CheckEnterprisePermission(c.Request.Context(), userID.(string), enterpriseID, "user", "manage")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有分配角色的权限")
		return
	}

	var req model.AssignRoleRequest
	req.UserID = targetUserID
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "请求参数错误", err)
		return
	}

	// 分配角色
	err = h.userManagementService.AssignRole(c.Request.Context(), enterpriseID, req, userID.(string))
	if err != nil {
		util.BadRequest(c, "分配角色失败", err)
		return
	}

	util.Success(c, nil, "分配角色成功")
}

// RemoveUserV2 移除用户（增强版）
// DELETE /enterprises/:id/users/:userId/v2
func (h *UserManagementController) RemoveUserV2(c *gin.Context) {
	enterpriseID := c.Param("id")
	targetUserID := c.Param("userId")

	if enterpriseID == "" || targetUserID == "" {
		util.BadRequest(c, "企业ID和用户ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查企业级权限
	hasPermission, err := h.permissionService.CheckEnterprisePermission(c.Request.Context(), userID.(string), enterpriseID, "user", "delete")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有移除用户的权限")
		return
	}

	var req model.RemoveUserRequest
	req.UserID = targetUserID
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "请求参数错误", err)
		return
	}

	// 移除用户
	err = h.userManagementService.RemoveUser(c.Request.Context(), enterpriseID, req, userID.(string))
	if err != nil {
		util.BadRequest(c, "移除用户失败", err)
		return
	}

	util.Success(c, nil, "移除用户成功")
}

// GrantSpecialPermission 授予特殊权限
// POST /enterprises/:id/users/:userId/permissions
func (h *UserManagementController) GrantSpecialPermission(c *gin.Context) {
	enterpriseID := c.Param("id")
	targetUserID := c.Param("userId")

	if enterpriseID == "" || targetUserID == "" {
		util.BadRequest(c, "企业ID和用户ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查企业级权限
	hasPermission, err := h.permissionService.CheckEnterprisePermission(c.Request.Context(), userID.(string), enterpriseID, "permission", "assign")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有授予权限的权限")
		return
	}

	var req model.GrantPermissionRequest
	req.UserID = targetUserID
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "请求参数错误", err)
		return
	}

	// 授予特殊权限
	err = h.userManagementService.GrantSpecialPermission(c.Request.Context(), enterpriseID, req, userID.(string))
	if err != nil {
		util.BadRequest(c, "授予特殊权限失败", err)
		return
	}

	util.Success(c, nil, "授予特殊权限成功")
}

// RevokeSpecialPermission 撤销特殊权限
// DELETE /enterprises/:id/users/:userId/permissions/:permissionId
func (h *UserManagementController) RevokeSpecialPermission(c *gin.Context) {
	enterpriseID := c.Param("id")
	targetUserID := c.Param("userId")
	permissionID := c.Param("permissionId")

	if enterpriseID == "" || targetUserID == "" || permissionID == "" {
		util.BadRequest(c, "企业ID、用户ID和权限ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查企业级权限
	hasPermission, err := h.permissionService.CheckEnterprisePermission(c.Request.Context(), userID.(string), enterpriseID, "permission", "revoke")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有撤销权限的权限")
		return
	}

	// 获取撤销原因
	reason := c.Query("reason")

	// 撤销特殊权限
	err = h.userManagementService.RevokeSpecialPermission(c.Request.Context(), enterpriseID, targetUserID, permissionID, userID.(string), reason)
	if err != nil {
		util.BadRequest(c, "撤销特殊权限失败", err)
		return
	}

	util.Success(c, nil, "撤销特殊权限成功")
}

// GetUserPermissionsV2 获取用户权限汇总（增强版）
// GET /enterprises/:id/users/:userId/permissions/v2
func (h *UserManagementController) GetUserPermissionsV2(c *gin.Context) {
	enterpriseID := c.Param("id")
	targetUserID := c.Param("userId")

	if enterpriseID == "" || targetUserID == "" {
		util.BadRequest(c, "企业ID和用户ID不能为空")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "permission", "read")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有查看权限的权限")
		return
	}

	// 获取用户权限汇总
	permissions, err := h.userManagementService.GetUserPermissions(c.Request.Context(), enterpriseID, targetUserID)
	if err != nil {
		util.InternalServerError(c, "获取用户权限失败", err)
		return
	}

	util.Success(c, permissions, "获取用户权限成功")
}

// ValidateOwnerUniqueness 验证Owner唯一性约束
// GET /users/:userId/validate-owner-uniqueness
func (h *UserManagementController) ValidateOwnerUniqueness(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		util.BadRequest(c, "用户ID不能为空")
		return
	}

	// 获取当前用户ID
	_, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 验证Owner唯一性约束
	err := h.userManagementService.ValidateOwnerUniqueness(c.Request.Context(), userID)
	if err != nil {
		util.Success(c, map[string]interface{}{
			"valid":   false,
			"message": err.Error(),
		}, "验证完成")
		return
	}

	util.Success(c, map[string]interface{}{
		"valid":   true,
		"message": "用户可以成为企业所有者",
	}, "验证完成")
}

// ValidateUserInvitation 验证用户邀请的有效性
// GET /users/:userId/validate-invitation?role_id=xxx&enterprise_id=xxx
func (h *UserManagementController) ValidateUserInvitation(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		util.BadRequest(c, "用户ID不能为空")
		return
	}

	roleID := c.Query("role_id")
	enterpriseID := c.Query("enterprise_id")

	if roleID == "" {
		util.BadRequest(c, "角色ID不能为空")
		return
	}

	if enterpriseID == "" {
		util.BadRequest(c, "企业ID不能为空")
		return
	}

	// 获取当前用户ID
	_, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 验证用户邀请的有效性
	result, err := h.userManagementService.ValidateUserInvitation(c.Request.Context(), userID, roleID, enterpriseID)
	if err != nil {
		util.BadRequest(c, "验证失败", err)
		return
	}

	util.Success(c, result, "验证完成")
}

// GetCurrentUserEnterprise 获取当前用户的企业信息
// GET /users/current/enterprise
func (h *UserManagementController) GetCurrentUserEnterprise(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 获取用户企业信息
	enterprise, err := h.userManagementService.GetUserEnterprise(c.Request.Context(), userID.(string))
	if err != nil {
		util.InternalServerError(c, "获取用户企业信息失败", err)
		return
	}

	if enterprise == nil {
		util.Success(c, map[string]interface{}{
			"has_enterprise": false,
			"message":        "用户未加入任何企业",
		}, "获取用户企业信息成功")
		return
	}

	util.Success(c, map[string]interface{}{
		"has_enterprise": true,
		"enterprise":     enterprise,
	}, "获取用户企业信息成功")
}

// GetOwnerEnterprises 获取当前用户拥有的企业列表（OWNER专用）
// GET /users/current/owned-enterprises
func (h *UserManagementController) GetOwnerEnterprises(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 获取用户拥有的企业列表
	enterprises, err := h.userManagementService.GetOwnerEnterprises(c.Request.Context(), userID.(string))
	if err != nil {
		util.InternalServerError(c, "获取拥有的企业列表失败", err)
		return
	}

	util.Success(c, map[string]interface{}{
		"enterprises": enterprises,
		"count":       len(enterprises),
	}, "获取拥有的企业列表成功")
}

// GetAllUsersForOwner 获取OWNER用户可管理的所有用户（跨企业）
// GET /users/owner/all
func (h *UserManagementController) GetAllUsersForOwner(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查用户是否有查看用户的基本权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "user", "view")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有查看用户的权限")
		return
	}

	// 解析分页参数
	page := 1
	pageSize := 10

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if pageSizeStr := c.Query("pageSize"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	// 获取搜索参数
	keyword := c.Query("keyword")
	roleID := c.Query("roleId")
	status := c.Query("status")
	enterpriseID := c.Query("enterpriseId") // 可选：筛选特定企业

	// 获取OWNER用户可管理的所有用户
	result, err := h.userManagementService.GetAllUsersForOwner(c.Request.Context(), userID.(string), page, pageSize, keyword, roleID, status, enterpriseID)
	if err != nil {
		util.InternalServerError(c, "获取用户列表失败", err)
		return
	}

	util.Success(c, result, "获取用户列表成功")
}

// resolveEnterpriseID 解析企业ID，支持数字ID到字符串ID的转换
func (h *UserManagementController) resolveEnterpriseID(ctx context.Context, enterpriseID string) (string, error) {
	// 如果已经是字符串格式的ID（以ent_开头），直接返回
	if strings.HasPrefix(enterpriseID, "ent_") {
		return enterpriseID, nil
	}

	// 尝试将输入解析为数字ID
	numericID, err := strconv.Atoi(enterpriseID)
	if err != nil {
		// 不是数字，返回原始ID
		return enterpriseID, nil
	}

	// 查询数据库获取对应的字符串ID
	actualID, err := h.userManagementService.ResolveEnterpriseID(ctx, numericID)
	if err != nil {
		return enterpriseID, err
	}

	return actualID, nil
}

// CheckAndFixOwnerConsistency 检查并修复Owner数据一致性
// POST /admin/check-owner-consistency
func (h *UserManagementController) CheckAndFixOwnerConsistency(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 检查系统管理权限
	hasPermission, err := h.permissionService.CheckPermission(c.Request.Context(), userID.(string), "system", "admin")
	if err != nil {
		util.InternalServerError(c, "权限检查失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "没有系统管理权限")
		return
	}

	// 检查并修复Owner数据一致性
	err = h.userManagementService.CheckAndFixOwnerConsistency(c.Request.Context())
	if err != nil {
		util.InternalServerError(c, "检查并修复Owner数据一致性失败", err)
		return
	}

	util.Success(c, nil, "检查并修复Owner数据一致性成功")
}
