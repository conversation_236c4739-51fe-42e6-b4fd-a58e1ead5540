package controller

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"backend/model"
	"backend/service"
	"backend/util"
)

// TaxFilingController 税务申报控制器
type TaxFilingController struct {
	taxFilingService      *service.TaxFilingService
	taxFilingBatchService *service.TaxFilingBatchService
	logger                *zap.Logger
}

// NewTaxFilingController 创建税务申报控制器
func NewTaxFilingController(
	taxFilingService *service.TaxFilingService,
	taxFilingBatchService *service.TaxFilingBatchService,
	logger *zap.Logger,
) *TaxFilingController {
	return &TaxFilingController{
		taxFilingService:      taxFilingService,
		taxFilingBatchService: taxFilingBatchService,
		logger:                logger,
	}
}

// CreateSubmission 创建税务申报
// @Summary 创建税务申报
// @Description 创建新的税务申报记录
// @Tags 税务申报
// @Accept json
// @Produce json
// @Param request body model.TaxFilingSubmissionCreateRequest true "申报创建请求"
// @Success 200 {object} util.Response{data=model.TaxFilingSubmissionResponse}
// @Failure 400 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/submissions [post]
func (c *TaxFilingController) CreateSubmission(ctx *gin.Context) {
	var req model.TaxFilingSubmissionCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Error("Invalid request body", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// 创建申报记录
	submission, err := c.taxFilingService.CreateSubmission(ctx.Request.Context(), &req)
	if err != nil {
		c.logger.Error("Failed to create submission", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create submission", err)
		return
	}

	// 转换为响应格式
	response := &model.TaxFilingSubmissionResponse{
		TaxFilingSubmission: *submission,
	}
	response.StatusText = response.GetStatusText()

	util.SuccessResponse(ctx, "Submission created successfully", response)
}

// SubmitToTaxBureau 提交到税务局
// @Summary 提交申报到税务局
// @Description 将申报记录提交到对应的省级税务局
// @Tags 税务申报
// @Accept json
// @Produce json
// @Param id path string true "申报ID"
// @Success 200 {object} util.Response
// @Failure 400 {object} util.Response
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/submissions/{id}/submit [post]
func (c *TaxFilingController) SubmitToTaxBureau(ctx *gin.Context) {
	submissionID := ctx.Param("id")
	if submissionID == "" {
		util.BadRequest(ctx, "Submission ID is required")
		return
	}

	// 提交到税务局
	if err := c.taxFilingService.SubmitToTaxBureau(ctx.Request.Context(), submissionID); err != nil {
		c.logger.Error("Failed to submit to tax bureau", zap.Error(err), zap.String("submission_id", submissionID))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to submit to tax bureau", err)
		return
	}

	util.SuccessResponse(ctx, "Submission submitted to tax bureau successfully", nil)
}

// GetSubmission 获取申报详情
// @Summary 获取申报详情
// @Description 根据ID获取申报记录详情
// @Tags 税务申报
// @Accept json
// @Produce json
// @Param id path string true "申报ID"
// @Success 200 {object} util.Response{data=model.TaxFilingSubmissionResponse}
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/submissions/{id} [get]
func (c *TaxFilingController) GetSubmission(ctx *gin.Context) {
	submissionID := ctx.Param("id")
	if submissionID == "" {
		util.BadRequest(ctx, "Submission ID is required")
		return
	}

	// 获取申报记录
	submission, err := c.taxFilingService.GetSubmissionByID(ctx.Request.Context(), submissionID)
	if err != nil {
		c.logger.Error("Failed to get submission", zap.Error(err), zap.String("submission_id", submissionID))
		util.ErrorResponse(ctx, http.StatusNotFound, "Submission not found", err)
		return
	}

	// 转换为响应格式
	response := &model.TaxFilingSubmissionResponse{
		TaxFilingSubmission: *submission,
	}
	response.StatusText = response.GetStatusText()

	util.SuccessResponse(ctx, "Submission retrieved successfully", response)
}

// UpdateSubmissionStatus 更新申报状态
// @Summary 更新申报状态
// @Description 更新申报记录的状态
// @Tags 税务申报
// @Accept json
// @Produce json
// @Param id path string true "申报ID"
// @Param request body model.TaxFilingSubmissionUpdateRequest true "状态更新请求"
// @Success 200 {object} util.Response
// @Failure 400 {object} util.Response
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/submissions/{id}/status [put]
func (c *TaxFilingController) UpdateSubmissionStatus(ctx *gin.Context) {
	submissionID := ctx.Param("id")
	if submissionID == "" {
		util.BadRequest(ctx, "Submission ID is required")
		return
	}

	var req model.TaxFilingSubmissionUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Error("Invalid request body", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// 更新状态
	if req.Status != nil {
		reason := "手动状态更新"
		if err := c.taxFilingService.UpdateSubmissionStatus(ctx.Request.Context(), submissionID, *req.Status, reason, req.ErrorMessage); err != nil {
			c.logger.Error("Failed to update submission status", zap.Error(err))
			util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update status", err)
			return
		}
	}

	util.SuccessResponse(ctx, "Submission status updated successfully", nil)
}

// SyncSubmissionStatus 同步申报状态
// @Summary 同步申报状态
// @Description 从税务局同步申报状态
// @Tags 税务申报
// @Accept json
// @Produce json
// @Param id path string true "申报ID"
// @Success 200 {object} util.Response
// @Failure 400 {object} util.Response
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/submissions/{id}/sync [post]
func (c *TaxFilingController) SyncSubmissionStatus(ctx *gin.Context) {
	submissionID := ctx.Param("id")
	if submissionID == "" {
		util.BadRequest(ctx, "Submission ID is required")
		return
	}

	// 同步状态
	if err := c.taxFilingService.SyncSubmissionStatus(ctx.Request.Context(), submissionID); err != nil {
		c.logger.Error("Failed to sync submission status", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to sync status", err)
		return
	}

	util.SuccessResponse(ctx, "Submission status synced successfully", nil)
}

// RetrySubmission 重试申报
// @Summary 重试申报
// @Description 重试失败的申报
// @Tags 税务申报
// @Accept json
// @Produce json
// @Param id path string true "申报ID"
// @Success 200 {object} util.Response
// @Failure 400 {object} util.Response
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/submissions/{id}/retry [post]
func (c *TaxFilingController) RetrySubmission(ctx *gin.Context) {
	submissionID := ctx.Param("id")
	if submissionID == "" {
		util.BadRequest(ctx, "Submission ID is required")
		return
	}

	// 重试申报
	if err := c.taxFilingService.RetrySubmission(ctx.Request.Context(), submissionID); err != nil {
		c.logger.Error("Failed to retry submission", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to retry submission", err)
		return
	}

	util.SuccessResponse(ctx, "Submission retry initiated successfully", nil)
}

// CancelSubmission 取消申报
// @Summary 取消申报
// @Description 取消待处理或处理中的申报
// @Tags 税务申报
// @Accept json
// @Produce json
// @Param id path string true "申报ID"
// @Success 200 {object} util.Response
// @Failure 400 {object} util.Response
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/submissions/{id}/cancel [post]
func (c *TaxFilingController) CancelSubmission(ctx *gin.Context) {
	submissionID := ctx.Param("id")
	if submissionID == "" {
		util.BadRequest(ctx, "Submission ID is required")
		return
	}

	// 取消申报
	if err := c.taxFilingService.CancelSubmission(ctx.Request.Context(), submissionID); err != nil {
		c.logger.Error("Failed to cancel submission", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to cancel submission", err)
		return
	}

	util.SuccessResponse(ctx, "Submission cancelled successfully", nil)
}

// ListSubmissions 获取申报列表
// @Summary 获取申报列表
// @Description 分页获取申报记录列表
// @Tags 税务申报
// @Accept json
// @Produce json
// @Param enterprise_id query string false "企业ID"
// @Param province_code query string false "省份代码"
// @Param status query string false "申报状态"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Success 200 {object} util.Response{data=util.PaginatedResponse}
// @Failure 400 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/submissions [get]
func (c *TaxFilingController) ListSubmissions(ctx *gin.Context) {
	// 解析查询参数
	enterpriseID := ctx.Query("enterprise_id")
	provinceCode := ctx.Query("province_code")
	statusStr := ctx.Query("status")

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 构建查询条件
	query := c.taxFilingService.GetDB().Model(&model.TaxFilingSubmission{}).
		Preload("Enterprise").
		Preload("Province")

	if enterpriseID != "" {
		query = query.Where("enterprise_id = ?", enterpriseID)
	}
	if provinceCode != "" {
		query = query.Where("province_code = ?", provinceCode)
	}
	if statusStr != "" {
		query = query.Where("status = ?", statusStr)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.logger.Error("Failed to count submissions", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to count submissions", err)
		return
	}

	// 获取数据
	var submissions []model.TaxFilingSubmission
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&submissions).Error; err != nil {
		c.logger.Error("Failed to get submissions", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to get submissions", err)
		return
	}

	// 转换为响应格式
	var responseList []model.TaxFilingSubmissionResponse
	for _, submission := range submissions {
		response := model.TaxFilingSubmissionResponse{
			TaxFilingSubmission: submission,
		}
		response.StatusText = response.GetStatusText()
		responseList = append(responseList, response)
	}

	// 构建分页响应
	paginatedResponse := util.PaginatedResponse{
		Data:       responseList,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
	}

	util.SuccessResponse(ctx, "Submissions retrieved successfully", paginatedResponse)
}

// CreateBatch 创建批次
// @Summary 创建申报批次
// @Description 创建新的申报批次
// @Tags 批次申报
// @Accept json
// @Produce json
// @Param request body model.TaxFilingBatchCreateRequest true "批次创建请求"
// @Success 200 {object} util.Response{data=model.TaxFilingBatchResponse}
// @Failure 400 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/batches [post]
func (c *TaxFilingController) CreateBatch(ctx *gin.Context) {
	var req model.TaxFilingBatchCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Error("Invalid request body", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// 创建批次
	batch, err := c.taxFilingBatchService.CreateBatch(ctx.Request.Context(), &req)
	if err != nil {
		c.logger.Error("Failed to create batch", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create batch", err)
		return
	}

	// 转换为响应格式
	response := &model.TaxFilingBatchResponse{
		TaxFilingBatch: *batch,
	}
	response.StatusText = response.GetStatusText()
	response.SuccessRate = response.GetSuccessRate()
	response.FailureRate = response.GetFailureRate()

	util.SuccessResponse(ctx, "Batch created successfully", response)
}

// ProcessBatch 处理批次
// @Summary 处理申报批次
// @Description 开始处理申报批次中的所有申报
// @Tags 批次申报
// @Accept json
// @Produce json
// @Param id path string true "批次ID"
// @Success 200 {object} util.Response
// @Failure 400 {object} util.Response
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/batches/{id}/process [post]
func (c *TaxFilingController) ProcessBatch(ctx *gin.Context) {
	batchID := ctx.Param("id")
	if batchID == "" {
		util.BadRequest(ctx, "Batch ID is required")
		return
	}

	// 处理批次
	if err := c.taxFilingBatchService.ProcessBatch(ctx.Request.Context(), batchID); err != nil {
		c.logger.Error("Failed to process batch", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to process batch", err)
		return
	}

	util.SuccessResponse(ctx, "Batch processing started successfully", nil)
}

// GetBatch 获取批次详情
// @Summary 获取批次详情
// @Description 根据ID获取批次详情
// @Tags 批次申报
// @Accept json
// @Produce json
// @Param id path string true "批次ID"
// @Success 200 {object} util.Response{data=model.TaxFilingBatchResponse}
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/batches/{id} [get]
func (c *TaxFilingController) GetBatch(ctx *gin.Context) {
	batchID := ctx.Param("id")
	if batchID == "" {
		util.BadRequest(ctx, "Batch ID is required")
		return
	}

	// 获取批次
	batch, err := c.taxFilingBatchService.GetBatchByID(ctx.Request.Context(), batchID)
	if err != nil {
		c.logger.Error("Failed to get batch", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusNotFound, "Batch not found", err)
		return
	}

	// 转换为响应格式
	response := &model.TaxFilingBatchResponse{
		TaxFilingBatch: *batch,
	}
	response.StatusText = response.GetStatusText()
	response.SuccessRate = response.GetSuccessRate()
	response.FailureRate = response.GetFailureRate()
	response.ProcessingTime = response.GetProcessingTime()

	util.SuccessResponse(ctx, "Batch retrieved successfully", response)
}
