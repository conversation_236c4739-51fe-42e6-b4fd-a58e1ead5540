package controller

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"backend/service"
	"backend/util"
)

// InvoiceController handles invoice-related HTTP requests
type InvoiceController struct {
	invoiceService service.InvoiceService
}

// NewInvoiceController creates a new invoice handler
func NewInvoiceController(invoiceService service.InvoiceService) *InvoiceController {
	return &InvoiceController{
		invoiceService: invoiceService,
	}
}

// CreateInvoice 创建发票
func (h *InvoiceController) CreateInvoice(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	var req service.CreateInvoiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	// 验证用户是否有权限操作该企业的发票
	if req.EnterpriseID != "" {
		hasPermission, err := h.invoiceService.CheckEnterprisePermission(c.Request.Context(), userID, req.EnterpriseID)
		if err != nil {
			util.InternalServerError(c, "权限验证失败", err)
			return
		}
		if !hasPermission {
			util.Forbidden(c, "无权限操作该企业的发票")
			return
		}
	}

	invoice, err := h.invoiceService.CreateInvoice(c.Request.Context(), req)
	if err != nil {
		util.BadRequest(c, "创建发票失败", err)
		return
	}

	util.Success(c, invoice, "创建发票成功")
}

// GetInvoices 获取发票列表
func (h *InvoiceController) GetInvoices(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	var req service.GetInvoicesRequest

	// 解析查询参数
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}
	if req.Page <= 0 {
		req.Page = 1
	}

	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	req.Keyword = c.Query("keyword")
	req.Type = c.Query("type")
	req.Status = c.Query("status")
	req.EnterpriseID = c.Query("enterpriseId")

	// 如果指定了企业ID，验证权限并使用企业级查询
	if req.EnterpriseID != "" {
		hasPermission, err := h.invoiceService.CheckEnterprisePermission(c.Request.Context(), userID, req.EnterpriseID)
		if err != nil {
			util.InternalServerError(c, "权限验证失败", err)
			return
		}
		if !hasPermission {
			util.Forbidden(c, "无权限查看该企业的发票")
			return
		}

		result, err := h.invoiceService.GetInvoicesByEnterprise(c.Request.Context(), req.EnterpriseID, req)
		if err != nil {
			util.InternalServerError(c, "获取发票列表失败", err)
			return
		}
		util.SuccessWithPagination(c, result.Items, result.Total, result.Page, result.PageSize, "获取发票列表成功")
		return
	}

	// 如果没有指定企业ID，获取用户有权限的所有企业的发票
	authorizedEnterprises, err := h.invoiceService.GetUserAuthorizedEnterprises(c.Request.Context(), userID)
	if err != nil {
		util.InternalServerError(c, "获取用户权限失败", err)
		return
	}

	if len(authorizedEnterprises) == 0 {
		// 用户没有关联任何企业，返回空结果
		util.SuccessWithPagination(c, []interface{}{}, 0, req.Page, req.PageSize, "获取发票列表成功")
		return
	}

	// 解析其他查询参数
	if minAmount := c.Query("minAmount"); minAmount != "" {
		if ma, err := strconv.ParseFloat(minAmount, 64); err == nil {
			req.MinAmount = ma
		}
	}

	if maxAmount := c.Query("maxAmount"); maxAmount != "" {
		if ma, err := strconv.ParseFloat(maxAmount, 64); err == nil {
			req.MaxAmount = ma
		}
	}

	if startDate := c.Query("startDate"); startDate != "" {
		if sd, err := time.Parse("2006-01-02", startDate); err == nil {
			req.StartDate = sd
		}
	}

	if endDate := c.Query("endDate"); endDate != "" {
		if ed, err := time.Parse("2006-01-02", endDate); err == nil {
			req.EndDate = ed
		}
	}

	// 使用用户权限筛选的发票查询
	result, err := h.invoiceService.GetInvoicesByUserPermission(c.Request.Context(), userID, req)
	if err != nil {
		util.InternalServerError(c, "获取发票列表失败", err)
		return
	}

	util.SuccessWithPagination(c, result.Items, result.Total, result.Page, result.PageSize, "获取发票列表成功")
}

// GetInvoiceByID 根据ID获取发票
func (h *InvoiceController) GetInvoiceByID(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "发票ID不能为空")
		return
	}

	invoice, err := h.invoiceService.GetInvoiceByID(c.Request.Context(), id)
	if err != nil {
		util.NotFound(c, "发票不存在", err)
		return
	}

	// 验证用户是否有权限查看该发票
	if invoice.EnterpriseID != "" {
		hasPermission, err := h.invoiceService.CheckEnterprisePermission(c.Request.Context(), userID, invoice.EnterpriseID)
		if err != nil {
			util.InternalServerError(c, "权限验证失败", err)
			return
		}
		if !hasPermission {
			util.Forbidden(c, "无权限查看该发票")
			return
		}
	}

	util.Success(c, invoice, "获取发票信息成功")
}

// UpdateInvoice 更新发票信息
func (h *InvoiceController) UpdateInvoice(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "发票ID不能为空")
		return
	}

	// 先获取发票信息以验证权限
	existingInvoice, err := h.invoiceService.GetInvoiceByID(c.Request.Context(), id)
	if err != nil {
		util.NotFound(c, "发票不存在", err)
		return
	}

	// 验证用户是否有权限修改该发票
	if existingInvoice.EnterpriseID != "" {
		hasPermission, err := h.invoiceService.CheckEnterprisePermission(c.Request.Context(), userID, existingInvoice.EnterpriseID)
		if err != nil {
			util.InternalServerError(c, "权限验证失败", err)
			return
		}
		if !hasPermission {
			util.Forbidden(c, "无权限修改该发票")
			return
		}
	}

	var req service.UpdateInvoiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	invoice, err := h.invoiceService.UpdateInvoice(c.Request.Context(), id, req)
	if err != nil {
		util.BadRequest(c, "更新发票信息失败", err)
		return
	}

	util.Success(c, invoice, "更新发票信息成功")
}

// DeleteInvoice 删除发票
func (h *InvoiceController) DeleteInvoice(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "发票ID不能为空")
		return
	}

	// 先获取发票信息以验证权限
	existingInvoice, err := h.invoiceService.GetInvoiceByID(c.Request.Context(), id)
	if err != nil {
		util.NotFound(c, "发票不存在", err)
		return
	}

	// 验证用户是否有权限删除该发票
	if existingInvoice.EnterpriseID != "" {
		hasPermission, err := h.invoiceService.CheckEnterprisePermission(c.Request.Context(), userID, existingInvoice.EnterpriseID)
		if err != nil {
			util.InternalServerError(c, "权限验证失败", err)
			return
		}
		if !hasPermission {
			util.Forbidden(c, "无权限删除该发票")
			return
		}
	}

	err = h.invoiceService.DeleteInvoice(c.Request.Context(), id)
	if err != nil {
		util.BadRequest(c, "删除发票失败", err)
		return
	}

	util.Success(c, nil, "删除发票成功")
}

// BatchDeleteInvoices 批量删除发票
func (h *InvoiceController) BatchDeleteInvoices(c *gin.Context) {
	var req struct {
		InvoiceIDs []string `json:"invoiceIds" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	// 批量删除
	for _, id := range req.InvoiceIDs {
		if err := h.invoiceService.DeleteInvoice(c.Request.Context(), id); err != nil {
			util.BadRequest(c, "批量删除发票失败", err)
			return
		}
	}

	util.Success(c, nil, "批量删除发票成功")
}

// UploadInvoice 上传发票附件
func (h *InvoiceController) UploadInvoice(c *gin.Context) {
	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		util.BadRequest(c, "获取文件失败", err)
		return
	}
	defer file.Close()

	// 从JWT中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		util.Unauthorized(c, "无法获取用户信息")
		return
	}

	// 验证文件类型
	allowedTypes := []string{".pdf", ".jpg", ".jpeg", ".png"}
	fileName := header.Filename
	isValidType := false
	for _, allowedType := range allowedTypes {
		if strings.HasSuffix(strings.ToLower(fileName), allowedType) {
			isValidType = true
			break
		}
	}

	if !isValidType {
		util.BadRequest(c, "只支持PDF、JPG、JPEG、PNG格式")
		return
	}

	// 这里应该实现文件保存逻辑，为了演示返回基本信息
	response := map[string]interface{}{
		"fileName": fileName,
		"fileSize": header.Size,
		"userID":   userID,
		"message":  "文件上传成功，需要进一步实现文件保存和识别逻辑",
	}

	util.Success(c, response, "上传发票成功")
}

// GetInvoiceStats 获取发票统计信息
func (h *InvoiceController) GetInvoiceStats(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 使用基于用户权限的统计方法
	stats, err := h.invoiceService.GetInvoiceStatsByUserPermission(c.Request.Context(), userID)
	if err != nil {
		util.InternalServerError(c, "获取发票统计信息失败", err)
		return
	}

	util.Success(c, stats, "获取发票统计信息成功")
}

// GetInvoicesByDeclarationPeriod 获取指定申报期间的发票
func (h *InvoiceController) GetInvoicesByDeclarationPeriod(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	enterpriseID := c.Param("enterpriseId")
	period := c.Param("period")

	if enterpriseID == "" || period == "" {
		util.BadRequest(c, "企业ID和申报期间不能为空")
		return
	}

	invoices, err := h.invoiceService.GetInvoicesByDeclarationPeriod(c.Request.Context(), enterpriseID, period)
	if err != nil {
		util.InternalServerError(c, "获取发票列表失败", err)
		return
	}

	util.Success(c, invoices, "获取发票列表成功")
}

// UpdateInvoiceAuthenticationStatus 更新发票认证状态
func (h *InvoiceController) UpdateInvoiceAuthenticationStatus(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "发票ID不能为空")
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	err := h.invoiceService.UpdateInvoiceAuthenticationStatus(c.Request.Context(), id, req.Status)
	if err != nil {
		util.BadRequest(c, "更新认证状态失败", err)
		return
	}

	util.Success(c, nil, "更新认证状态成功")
}

// UpdateInvoiceDeclarationStatus 更新发票申报状态
func (h *InvoiceController) UpdateInvoiceDeclarationStatus(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "发票ID不能为空")
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
		Period string `json:"period"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	err := h.invoiceService.UpdateInvoiceDeclarationStatus(c.Request.Context(), id, req.Status, req.Period)
	if err != nil {
		util.BadRequest(c, "更新申报状态失败", err)
		return
	}

	util.Success(c, nil, "更新申报状态成功")
}

// GetInvoiceTaxSummary 获取发票税务汇总
func (h *InvoiceController) GetInvoiceTaxSummary(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	enterpriseID := c.Param("enterpriseId")
	period := c.Query("period")

	if enterpriseID == "" {
		util.BadRequest(c, "企业ID不能为空")
		return
	}

	summary, err := h.invoiceService.GetInvoiceTaxSummary(c.Request.Context(), enterpriseID, period)
	if err != nil {
		util.InternalServerError(c, "获取税务汇总失败", err)
		return
	}

	util.Success(c, summary, "获取税务汇总成功")
}

// ScanInvoice 扫描识别发票
func (h *InvoiceController) ScanInvoice(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		util.BadRequest(c, "获取文件失败", err)
		return
	}
	defer file.Close()

	// 验证文件类型
	allowedTypes := []string{".pdf", ".jpg", ".jpeg", ".png"}
	fileName := header.Filename
	isValidType := false
	for _, allowedType := range allowedTypes {
		if strings.HasSuffix(strings.ToLower(fileName), allowedType) {
			isValidType = true
			break
		}
	}

	if !isValidType {
		util.BadRequest(c, "只支持PDF、JPG、JPEG、PNG格式")
		return
	}

	// 验证文件大小 (10MB)
	if header.Size > 10<<20 {
		util.BadRequest(c, "文件大小不能超过10MB")
		return
	}

	// 解析设置参数
	settingsStr := c.PostForm("settings")
	var settings service.OCRSettings
	if settingsStr != "" {
		if err := json.Unmarshal([]byte(settingsStr), &settings); err != nil {
			util.BadRequest(c, "设置参数格式错误", err)
			return
		}
	}

	// 获取企业ID
	enterpriseID := c.PostForm("enterpriseId")
	if enterpriseID == "" {
		util.BadRequest(c, "企业ID不能为空")
		return
	}

	// 验证用户是否有权限操作该企业
	hasPermission, err := h.invoiceService.CheckEnterprisePermission(c.Request.Context(), userID, enterpriseID)
	if err != nil {
		util.InternalServerError(c, "权限验证失败", err)
		return
	}
	if !hasPermission {
		util.Forbidden(c, "无权限操作该企业的发票")
		return
	}

	// 构建扫描请求
	scanReq := service.ScanInvoiceRequest{
		EnterpriseID: enterpriseID,
		Settings:     settings,
	}

	// 调用扫描服务
	result, err := h.invoiceService.ScanInvoice(c.Request.Context(), file, fileName, scanReq)
	if err != nil {
		util.InternalServerError(c, "发票扫描失败", err)
		return
	}

	if !result.Success {
		util.BadRequest(c, result.Message)
		return
	}

	util.Success(c, result, "发票扫描成功")
}
