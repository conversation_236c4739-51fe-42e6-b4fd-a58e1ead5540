// Package controller provides HTTP controllers for the tax management system.
// It includes controllers for authentication, enterprise management, declarations, invoices, and file uploads.
package controller

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"backend/model"
	"backend/service"
	"backend/util"
)

// DeclarationCreateRequest defines the request body for creating a declaration
type DeclarationCreateRequest struct {
	EnterpriseID string `json:"enterpriseId" binding:"required"`
	TaxTypeID    string `json:"taxTypeId" binding:"required"`
	Notes        string `json:"notes"`
}

// DeclarationUpdateRequest defines the request body for updating a declaration
type DeclarationUpdateRequest struct {
	TaxableIncome float64 `json:"taxableIncome"`
	TaxAmount     float64 `json:"taxPayable"`
	Notes         string  `json:"notes"`
}

// DeclarationController handles declaration-related HTTP requests
type DeclarationController struct {
	declarationService service.DeclarationService
}

// NewDeclarationController creates a new declaration handler
func NewDeclarationController(declarationService service.DeclarationService) *DeclarationController {
	return &DeclarationController{
		declarationService: declarationService,
	}
}

// GetDeclarations handles GET /declarations - retrieves declaration list with pagination and filtering
func (h *DeclarationController) GetDeclarations(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	req := service.GetDeclarationsRequest{
		EnterpriseID: c.Query("enterprise_id"),
		TaxTypeID:    c.Query("tax_type_id"),
		Status:       c.Query("status"),
		Page:         page,
		PageSize:     pageSize,
		SortBy:       c.DefaultQuery("sort_by", "created_at"),
		SortOrder:    c.DefaultQuery("sort_order", "desc"),
	}

	// Parse year
	if yearStr := c.Query("year"); yearStr != "" {
		if year, err := strconv.Atoi(yearStr); err == nil {
			req.Year = year
		}
	}

	// Parse month
	if monthStr := c.Query("month"); monthStr != "" {
		if month, err := strconv.Atoi(monthStr); err == nil {
			req.Month = &month
		}
	}

	// Parse date range
	if startDateStr := c.Query("start_date"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			req.StartDate = startDate
		}
	}
	if endDateStr := c.Query("end_date"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			req.EndDate = endDate
		}
	}

	result, err := h.declarationService.GetDeclarations(c.Request.Context(), req)
	if err != nil {
		util.InternalServerError(c, "获取申报列表失败", err)
		return
	}

	util.Success(c, result, "获取申报列表成功")
}

// GetDeclarationTemplates handles GET /declarations/templates - retrieves declaration template list
func (h *DeclarationController) GetDeclarationTemplates(c *gin.Context) {
	// TODO: Replace with actual service call
	templates := []model.DeclarationTemplate{
		{
			ID:          "1",
			TaxTypeID:   "vat",
			Name:        "增值税申报模板",
			Description: "适用于一般纳税人增值税申报",
			Structure:   `[{"name":"销售额","type":"number","required":true},{"name":"进项税额","type":"number","required":true}]`,
			IsActive:    true,
		},
		{
			ID:          "2",
			TaxTypeID:   "cit",
			Name:        "企业所得税申报模板",
			Description: "适用于企业所得税季度申报",
			Structure:   `[{"name":"营业收入","type":"number","required":true},{"name":"营业成本","type":"number","required":true}]`,
			IsActive:    true,
		},
		{
			ID:          "3",
			TaxTypeID:   "pit",
			Name:        "个人所得税申报模板",
			Description: "适用于个人所得税月度申报",
			Structure:   `[{"name":"工资薪金","type":"number","required":true},{"name":"专项扣除","type":"number","required":false}]`,
			IsActive:    true,
		},
	}

	util.Success(c, templates, "获取申报模板成功")
}

// CreateDeclaration handles POST /declarations - creates a new declaration
func (h *DeclarationController) CreateDeclaration(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	var req service.CreateDeclarationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "请求参数错误", err)
		return
	}

	declaration, err := h.declarationService.CreateDeclaration(c.Request.Context(), req)
	if err != nil {
		util.BadRequest(c, "创建申报失败", err)
		return
	}

	util.Success(c, declaration, "创建申报成功")
}

// GetDeclarationByID handles GET /declarations/:id - retrieves declaration details by ID
func (h *DeclarationController) GetDeclarationByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "申报ID不能为空")
		return
	}

	declaration, err := h.declarationService.GetDeclarationByID(c.Request.Context(), id)
	if err != nil {
		util.BadRequest(c, "获取申报详情失败", err)
		return
	}

	util.Success(c, declaration, "获取申报详情成功")
}

// UpdateDeclaration handles PUT /declarations/:id - updates declaration information
func (h *DeclarationController) UpdateDeclaration(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "申报ID不能为空")
		return
	}

	var req service.UpdateDeclarationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "请求参数错误", err)
		return
	}

	declaration, err := h.declarationService.UpdateDeclaration(c.Request.Context(), id, req)
	if err != nil {
		util.BadRequest(c, "更新申报失败", err)
		return
	}

	util.Success(c, declaration, "更新申报成功")
}

// DeleteDeclaration handles DELETE /declarations/:id - deletes a declaration
func (h *DeclarationController) DeleteDeclaration(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "申报ID不能为空")
		return
	}

	err := h.declarationService.DeleteDeclaration(c.Request.Context(), id)
	if err != nil {
		util.BadRequest(c, "删除申报失败", err)
		return
	}

	util.Success(c, nil, "删除申报成功")
}

// SubmitDeclaration handles POST /declarations/:id/submit - submits a declaration
func (h *DeclarationController) SubmitDeclaration(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "申报ID不能为空")
		return
	}

	declaration, err := h.declarationService.SubmitDeclaration(c.Request.Context(), id)
	if err != nil {
		util.BadRequest(c, "提交申报失败", err)
		return
	}

	util.Success(c, declaration, "提交申报成功")
}

// GetDeclarationStats handles GET /declarations/stats - retrieves declaration statistics
func (h *DeclarationController) GetDeclarationStats(c *gin.Context) {
	stats, err := h.declarationService.GetDeclarationStats(c.Request.Context())
	if err != nil {
		util.InternalServerError(c, "获取申报统计信息失败", err)
		return
	}

	util.Success(c, stats, "获取申报统计信息成功")
}

// GetDeclarationCalendar handles GET /declarations/calendar - retrieves declaration calendar data
func (h *DeclarationController) GetDeclarationCalendar(c *gin.Context) {
	// 获取查询参数
	_ = c.Query("year")  // 暂时不使用，避免编译错误
	_ = c.Query("month") // 暂时不使用，避免编译错误

	// 模拟日历数据
	calendarData := []map[string]interface{}{
		{
			"id":             "1",
			"enterpriseId":   "1",
			"enterpriseName": "测试企业A",
			"taxTypeId":      "vat",
			"taxTypeName":    "增值税",
			"deadline":       "2025-01-15T23:59:59Z",
			"status":         "pending",
			"period":         "2024-12",
		},
		{
			"id":             "2",
			"enterpriseId":   "2",
			"enterpriseName": "测试企业B",
			"taxTypeId":      "cit",
			"taxTypeName":    "企业所得税",
			"deadline":       "2025-01-31T23:59:59Z",
			"status":         "submitted",
			"period":         "2024-Q4",
		},
		{
			"id":             "3",
			"enterpriseId":   "1",
			"enterpriseName": "测试企业A",
			"taxTypeId":      "individual",
			"taxTypeName":    "个人所得税",
			"deadline":       "2025-02-15T23:59:59Z",
			"status":         "draft",
			"period":         "2025-01",
		},
	}

	util.Success(c, calendarData, "获取申报日历成功")
}
