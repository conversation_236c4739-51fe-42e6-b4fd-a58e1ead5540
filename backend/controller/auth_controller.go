// Package controller provides HTTP controllers for the tax management system.
// It includes authentication, enterprise management, invoice processing, and file upload endpoints.
package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"backend/config"
	"backend/model"
	"backend/service"
	"backend/util"
)

// AuthController handles authentication API requests including login, registration,
// password management, and user profile operations.
type AuthController struct {
	authService *service.AuthService
	config      *config.Config
}

// NewAuthController creates a new authentication handler with the provided service and configuration.
// It returns a configured AuthController instance ready to handle HTTP requests.
func NewAuthController(authService *service.AuthService, cfg *config.Config) *AuthController {
	return &AuthController{
		authService: authService,
		config:      cfg,
	}
}

// Login handles POST /auth/login - authenticates user credentials
func (h *AuthController) Login(c *gin.Context) {
	var req model.LoginRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		util.BadRequest(c, "无效的请求参数", err)
		return
	}

	authResponse, err := h.authService.Login(c.Request.Context(), req)
	if err != nil {
		switch err {
		case util.ErrInvalidCredentials:
			util.Unauthorized(c, "邮箱或密码错误", err)
		case util.ErrUserInactive:
			util.Forbidden(c, "账户已被禁用", err)
		default:
			util.InternalServerError(c, "登录失败，请稍后再试", err)
		}
		return
	}

	util.Success(c, authResponse, "登录成功")
}

// Register handles POST /auth/register - creates a new user account
func (h *AuthController) Register(c *gin.Context) {
	var req model.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "无效的请求参数", err)
		return
	}

	authResponse, err := h.authService.Register(c.Request.Context(), req)
	if err != nil {
		switch err {
		case util.ErrUserExists:
			util.ErrorResponse(c, http.StatusConflict, "该手机号已被注册", err)
		default:
			util.InternalServerError(c, "注册失败，请稍后再试", err)
		}
		return
	}

	c.JSON(http.StatusCreated, util.APIResponse{
		Code:    http.StatusCreated,
		Message: "注册成功",
		Data:    authResponse,
	})
}

// RefreshToken handles POST /auth/refresh - refreshes access token using refresh token
func (h *AuthController) RefreshToken(c *gin.Context) {
	var req model.TokenRefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "无效的请求参数", err)
		return
	}

	authResponse, err := h.authService.RefreshToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		switch err {
		case util.ErrInvalidToken:
			util.Unauthorized(c, "无效的刷新令牌", err)
		case util.ErrTokenExpired:
			util.Unauthorized(c, "刷新令牌已过期", err)
		case util.ErrUserNotFound:
			util.NotFound(c, "用户不存在", err)
		case util.ErrUserInactive:
			util.Forbidden(c, "账户已被禁用", err)
		default:
			util.InternalServerError(c, "令牌刷新失败", err)
		}
		return
	}

	util.Success(c, authResponse, "令牌刷新成功")
}

// ChangePassword handles PUT /auth/password - changes user password
func (h *AuthController) ChangePassword(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	var req model.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "无效的请求参数", err)
		return
	}

	err := h.authService.ChangePassword(c.Request.Context(), userID, req)
	if err != nil {
		switch err {
		case util.ErrInvalidCredentials:
			util.BadRequest(c, "当前密码错误", err)
		case util.ErrUserNotFound:
			util.NotFound(c, "用户不存在", err)
		default:
			util.InternalServerError(c, "密码修改失败", err)
		}
		return
	}

	util.Success(c, nil, "密码修改成功")
}

// GetProfile handles GET /auth/profile - retrieves current user profile
func (h *AuthController) GetProfile(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	user, err := h.authService.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		switch err {
		case util.ErrUserNotFound:
			util.NotFound(c, "用户不存在", err)
		default:
			util.InternalServerError(c, "获取用户信息失败", err)
		}
		return
	}

	// 构建用户响应，确保字段映射正确
	userResponse := gin.H{
		"id":                 user.ID,
		"user_name":          user.UserName,
		"email":              user.Email,
		"phone":              user.Phone,
		"department":         user.Department,
		"position":           user.Position,
		"avatar":             user.Avatar,
		"is_active":          user.IsActive,
		"email_verified":     user.EmailVerified,
		"phone_verified":     user.PhoneVerified,
		"preferred_lang":     user.PreferredLang,
		"two_factor_enabled": user.TwoFactorEnabled,
		"last_login_at":      user.LastLoginAt,
		"last_login_ip":      user.LastLoginIP,
		"created_at":         user.CreatedAt,
		"updated_at":         user.UpdatedAt,
	}

	// 添加企业信息
	if user.Enterprise != nil {
		userResponse["enterprise"] = gin.H{
			"id":         user.Enterprise.ID,
			"numeric_id": user.Enterprise.NumericID,
			"name":       user.Enterprise.Name,
			"owner_id":   user.Enterprise.OwnerID,
		}
		userResponse["enterprise_id"] = user.Enterprise.ID
		userResponse["enterprise_numeric_id"] = user.Enterprise.NumericID
	} else {
		userResponse["enterprise"] = nil
		userResponse["enterprise_id"] = nil
		userResponse["enterprise_numeric_id"] = nil
	}

	util.Success(c, userResponse, "获取用户信息成功")
}

// UpdateProfile handles PUT /auth/profile - updates current user profile
func (h *AuthController) UpdateProfile(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	var req model.UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "无效的请求参数", err)
		return
	}

	user, err := h.authService.UpdateProfile(c.Request.Context(), userID, req)
	if err != nil {
		switch err {
		case util.ErrUserNotFound:
			util.NotFound(c, "用户不存在", err)
		default:
			util.InternalServerError(c, "更新用户信息失败", err)
		}
		return
	}

	// 构建用户响应，确保字段映射正确
	userResponse := gin.H{
		"id":                 user.ID,
		"user_name":          user.UserName,
		"email":              user.Email,
		"phone":              user.Phone,
		"department":         user.Department,
		"position":           user.Position,
		"avatar":             user.Avatar,
		"is_active":          user.IsActive,
		"email_verified":     user.EmailVerified,
		"phone_verified":     user.PhoneVerified,
		"preferred_lang":     user.PreferredLang,
		"two_factor_enabled": user.TwoFactorEnabled,
		"last_login_at":      user.LastLoginAt,
		"last_login_ip":      user.LastLoginIP,
		"created_at":         user.CreatedAt,
		"updated_at":         user.UpdatedAt,
	}

	util.Success(c, userResponse, "更新用户信息成功")
}

// Logout handles POST /auth/logout - logs out user (client-side token removal)
func (h *AuthController) Logout(c *gin.Context) {
	util.Success(c, nil, "登出成功")
}

// ForgotPassword handles POST /auth/forgot-password - initiates password reset process
func (h *AuthController) ForgotPassword(c *gin.Context) {
	var req model.ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "无效的请求参数", err)
		return
	}

	// Validate that either email or phone is provided
	if req.Email == "" && req.Phone == "" {
		util.BadRequest(c, "邮箱或手机号必须提供其中一个", nil)
		return
	}

	err := h.authService.ForgotPassword(c.Request.Context(), req)
	if err != nil {
		util.InternalServerError(c, "密码重置请求失败，请稍后再试", err)
		return
	}

	util.Success(c, nil, "密码重置邮件已发送，请查收")
}

// ResetPassword handles POST /auth/reset-password - resets user password with token
func (h *AuthController) ResetPassword(c *gin.Context) {
	var req model.ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "无效的请求参数", err)
		return
	}

	err := h.authService.ResetPassword(c.Request.Context(), req)
	if err != nil {
		switch err.Error() {
		case "无效或已过期的重置令牌":
			util.BadRequest(c, "重置令牌无效或已过期", err)
		case "账户已被禁用":
			util.Forbidden(c, "账户已被禁用", err)
		default:
			util.InternalServerError(c, "密码重置失败，请稍后再试", err)
		}
		return
	}

	util.Success(c, nil, "密码重置成功")
}
