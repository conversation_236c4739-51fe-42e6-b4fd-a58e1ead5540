// Package controller provides HTTP controllers for the tax management system.
// It includes controllers for tax rule management operations.
package controller

import (
	"errors"
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"backend/model"
	"backend/service"
	"backend/util"
)

// TaxRuleController handles tax rule-related HTTP requests including creation,
// retrieval, updates, deletion, and tax rule management operations.
type TaxRuleController struct {
	taxRuleService service.TaxRuleService
}

// NewTaxRuleController creates a new tax rule handler instance
func NewTaxRuleController(taxRuleService service.TaxRuleService) *TaxRuleController {
	return &TaxRuleController{
		taxRuleService: taxRuleService,
	}
}

// CreateTaxRule handles POST /tax-rules - creates a new tax rule
func (h *TaxRuleController) CreateTaxRule(c *gin.Context) {
	var req service.CreateTaxRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	taxRule, err := h.taxRuleService.CreateTaxRule(c.Request.Context(), req)
	if err != nil {
		util.BadRequest(c, "创建税则失败", err)
		return
	}

	util.Success(c, taxRule, "创建税则成功")
}

// GetTaxRules handles GET /tax-rules - retrieves tax rules with pagination and filtering
func (h *TaxRuleController) GetTaxRules(c *gin.Context) {
	var req service.GetTaxRulesRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}
	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}

	req.TaxTypeID = c.Query("taxTypeId")
	req.Name = c.Query("name")
	req.SortBy = c.Query("sortBy")
	req.SortOrder = c.Query("sortOrder")

	if isActive := c.Query("isActive"); isActive != "" {
		if active, err := strconv.ParseBool(isActive); err == nil {
			req.IsActive = &active
		}
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	result, err := h.taxRuleService.GetTaxRules(c.Request.Context(), req)
	if err != nil {
		util.InternalServerError(c, "获取税则列表失败", err)
		return
	}

	util.Success(c, result, "获取税则列表成功")
}

// GetTaxRuleByID handles GET /tax-rules/:id - retrieves tax rule by ID
func (h *TaxRuleController) GetTaxRuleByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" || id == "undefined" || id == "null" {
		util.BadRequest(c, "税则ID无效")
		return
	}

	taxRule, err := h.taxRuleService.GetTaxRuleByID(c.Request.Context(), id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			util.BadRequest(c, "税则不存在")
		} else {
			util.InternalServerError(c, "获取税则详情失败", err)
		}
		return
	}

	util.Success(c, taxRule, "获取税则详情成功")
}

// UpdateTaxRule handles PUT /tax-rules/:id - updates tax rule information
func (h *TaxRuleController) UpdateTaxRule(c *gin.Context) {
	id := c.Param("id")
	if id == "" || id == "undefined" || id == "null" {
		util.BadRequest(c, "税则ID无效")
		return
	}

	var req service.UpdateTaxRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	taxRule, err := h.taxRuleService.UpdateTaxRule(c.Request.Context(), id, req)
	if err != nil {
		util.BadRequest(c, "更新税则失败", err)
		return
	}

	util.Success(c, taxRule, "更新税则成功")
}

// DeleteTaxRule handles DELETE /tax-rules/:id - deletes tax rule by ID
func (h *TaxRuleController) DeleteTaxRule(c *gin.Context) {
	id := c.Param("id")
	if id == "" || id == "undefined" || id == "null" {
		util.BadRequest(c, "税则ID无效")
		return
	}

	err := h.taxRuleService.DeleteTaxRule(c.Request.Context(), id)
	if err != nil {
		util.BadRequest(c, "删除税则失败", err)
		return
	}

	util.Success(c, nil, "删除税则成功")
}

// GetTaxRulesByTaxType handles GET /tax-types/:id/rules - retrieves all tax rules for a specific tax type
func (h *TaxRuleController) GetTaxRulesByTaxType(c *gin.Context) {
	taxTypeID := c.Param("id")
	if taxTypeID == "" || taxTypeID == "undefined" || taxTypeID == "null" {
		util.BadRequest(c, "税种ID无效")
		return
	}

	taxRules, err := h.taxRuleService.GetTaxRulesByTaxType(c.Request.Context(), taxTypeID)
	if err != nil {
		util.InternalServerError(c, "获取税种规则失败", err)
		return
	}

	util.Success(c, taxRules, "获取税种规则成功")
}

// ValidateTaxRule handles POST /tax-rules/validate - validates a tax rule
func (h *TaxRuleController) ValidateTaxRule(c *gin.Context) {
	var req service.CreateTaxRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	// Create a temporary tax rule for validation
	taxRule := &model.TaxRule{
		TaxTypeID:     req.TaxTypeID,
		Name:          req.Name,
		Description:   req.Description,
		Condition:     req.Condition,
		Formula:       req.Formula,
		EffectiveDate: req.EffectiveDate,
		ExpiryDate:    req.ExpiryDate,
	}

	// Set parameters if provided
	if req.Parameters != nil {
		if err := taxRule.SetParameters(req.Parameters); err != nil {
			util.BadRequest(c, "设置规则参数失败", err)
			return
		}
	}

	err := h.taxRuleService.ValidateTaxRule(c.Request.Context(), taxRule)
	if err != nil {
		util.BadRequest(c, "税则验证失败", err)
		return
	}

	util.Success(c, gin.H{"valid": true}, "税则验证通过")
}

// GetTaxRuleStats handles GET /tax-rules/stats - retrieves tax rule statistics
func (h *TaxRuleController) GetTaxRuleStats(c *gin.Context) {
	// TODO: Implement tax rule statistics
	// This could include:
	// - Total number of rules
	// - Rules by tax type
	// - Active vs inactive rules
	// - Recently created rules

	stats := gin.H{
		"total":       0,
		"active":      0,
		"inactive":    0,
		"byTaxType":   []gin.H{},
		"recentRules": []gin.H{},
	}

	util.Success(c, stats, "获取税则统计信息成功")
}

// BatchDeleteTaxRules handles DELETE /tax-rules/batch - batch deletes tax rules
func (h *TaxRuleController) BatchDeleteTaxRules(c *gin.Context) {
	var req struct {
		IDs []string `json:"ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	var errors []string
	var successCount int

	for _, id := range req.IDs {
		if err := h.taxRuleService.DeleteTaxRule(c.Request.Context(), id); err != nil {
			errors = append(errors, fmt.Sprintf("删除税则 %s 失败: %v", id, err))
		} else {
			successCount++
		}
	}

	result := gin.H{
		"successCount": successCount,
		"totalCount":   len(req.IDs),
		"errors":       errors,
	}

	if len(errors) > 0 {
		util.Success(c, result, fmt.Sprintf("批量删除完成，成功 %d 个，失败 %d 个", successCount, len(errors)))
	} else {
		util.Success(c, result, "批量删除税则成功")
	}
}

// ExportTaxRules handles GET /tax-rules/export - exports tax rules to file
func (h *TaxRuleController) ExportTaxRules(c *gin.Context) {
	// TODO: Implement tax rule export functionality
	// This could export to CSV, Excel, or JSON format

	util.Success(c, gin.H{
		"message": "税则导出功能待实现",
		"format":  c.Query("format"),
	}, "导出请求已接收")
}

// ImportTaxRules handles POST /tax-rules/import - imports tax rules from file
func (h *TaxRuleController) ImportTaxRules(c *gin.Context) {
	// TODO: Implement tax rule import functionality
	// This could import from CSV, Excel, or JSON format

	util.Success(c, gin.H{
		"message": "税则导入功能待实现",
	}, "导入请求已接收")
}
