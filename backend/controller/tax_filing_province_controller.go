package controller

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"backend/model"
	"backend/service"
	"backend/util"
)

// TaxFilingProvinceController 省份管理控制器
type TaxFilingProvinceController struct {
	provinceService *service.TaxFilingProvinceService
	logger          *zap.Logger
}

// NewTaxFilingProvinceController 创建省份管理控制器
func NewTaxFilingProvinceController(
	provinceService *service.TaxFilingProvinceService,
	logger *zap.Logger,
) *TaxFilingProvinceController {
	return &TaxFilingProvinceController{
		provinceService: provinceService,
		logger:          logger,
	}
}

// ListProvinces 获取省份列表
// @Summary 获取省份列表
// @Description 分页获取省份配置列表
// @Tags 省份管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param status query string false "状态过滤"
// @Success 200 {object} util.Response{data=util.PagedResponse{list=[]model.TaxFilingProvinceResponse}}
// @Failure 400 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/provinces [get]
func (c *TaxFilingProvinceController) ListProvinces(ctx *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	status := ctx.Query("status")

	query := &model.TaxFilingProvinceQuery{
		Page:     page,
		PageSize: pageSize,
	}

	if status != "" {
		provinceStatus := model.TaxFilingProvinceStatus(status)
		query.Status = &provinceStatus
	}

	// 查询省份列表
	provinces, total, err := c.provinceService.ListProvinces(ctx.Request.Context(), query)
	if err != nil {
		c.logger.Error("Failed to list provinces", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to list provinces", err)
		return
	}

	// 构建响应
	response := util.PaginatedResponse{
		Data:       provinces,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
	}

	util.SuccessResponse(ctx, "Provinces retrieved successfully", response)
}

// GetProvince 获取省份详情
// @Summary 获取省份详情
// @Description 根据省份代码获取省份配置详情
// @Tags 省份管理
// @Accept json
// @Produce json
// @Param code path string true "省份代码"
// @Success 200 {object} util.Response{data=model.TaxFilingProvinceResponse}
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/provinces/{code} [get]
func (c *TaxFilingProvinceController) GetProvince(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		util.BadRequest(ctx, "Province code is required")
		return
	}

	// 查询省份详情
	province, err := c.provinceService.GetProvince(ctx.Request.Context(), code)
	if err != nil {
		c.logger.Error("Failed to get province", zap.Error(err), zap.String("code", code))
		util.ErrorResponse(ctx, http.StatusNotFound, "Province not found", err)
		return
	}

	util.SuccessResponse(ctx, "Province retrieved successfully", province)
}

// CreateProvince 创建省份配置
// @Summary 创建省份配置
// @Description 创建新的省份配置
// @Tags 省份管理
// @Accept json
// @Produce json
// @Param request body model.TaxFilingProvinceCreateRequest true "省份创建请求"
// @Success 200 {object} util.Response{data=model.TaxFilingProvinceResponse}
// @Failure 400 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/provinces [post]
func (c *TaxFilingProvinceController) CreateProvince(ctx *gin.Context) {
	var req model.TaxFilingProvinceCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Error("Invalid create province request", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request", err)
		return
	}

	// 创建省份配置
	province, err := c.provinceService.CreateProvince(ctx.Request.Context(), &req)
	if err != nil {
		c.logger.Error("Failed to create province", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create province", err)
		return
	}

	util.SuccessResponse(ctx, "Province created successfully", province)
}

// UpdateProvince 更新省份配置
// @Summary 更新省份配置
// @Description 更新省份配置信息
// @Tags 省份管理
// @Accept json
// @Produce json
// @Param code path string true "省份代码"
// @Param request body model.TaxFilingProvinceUpdateRequest true "省份更新请求"
// @Success 200 {object} util.Response{data=model.TaxFilingProvinceResponse}
// @Failure 400 {object} util.Response
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/provinces/{code} [put]
func (c *TaxFilingProvinceController) UpdateProvince(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		util.BadRequest(ctx, "Province code is required")
		return
	}

	var req model.TaxFilingProvinceUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Error("Invalid update province request", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request", err)
		return
	}

	// 更新省份配置
	province, err := c.provinceService.UpdateProvince(ctx.Request.Context(), code, &req)
	if err != nil {
		c.logger.Error("Failed to update province", zap.Error(err), zap.String("code", code))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update province", err)
		return
	}

	util.SuccessResponse(ctx, "Province updated successfully", province)
}

// DeleteProvince 删除省份配置
// @Summary 删除省份配置
// @Description 删除省份配置（软删除）
// @Tags 省份管理
// @Accept json
// @Produce json
// @Param code path string true "省份代码"
// @Success 200 {object} util.Response
// @Failure 400 {object} util.Response
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/provinces/{code} [delete]
func (c *TaxFilingProvinceController) DeleteProvince(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		util.BadRequest(ctx, "Province code is required")
		return
	}

	// 删除省份配置
	err := c.provinceService.DeleteProvince(ctx.Request.Context(), code)
	if err != nil {
		c.logger.Error("Failed to delete province", zap.Error(err), zap.String("code", code))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to delete province", err)
		return
	}

	util.SuccessResponse(ctx, "Province deleted successfully", nil)
}

// UpdateProvinceStatus 更新省份状态
// @Summary 更新省份状态
// @Description 更新省份服务状态
// @Tags 省份管理
// @Accept json
// @Produce json
// @Param code path string true "省份代码"
// @Param request body model.ProvinceStatusUpdateRequest true "状态更新请求"
// @Success 200 {object} util.Response
// @Failure 400 {object} util.Response
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/provinces/{code}/status [put]
func (c *TaxFilingProvinceController) UpdateProvinceStatus(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		util.BadRequest(ctx, "Province code is required")
		return
	}

	var req model.ProvinceStatusUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Error("Invalid status update request", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request", err)
		return
	}

	// 更新省份状态
	statusReq := &model.ProvinceStatusUpdateRequest{
		Status: req.Status,
	}
	err := c.provinceService.UpdateProvinceStatus(ctx.Request.Context(), code, statusReq)
	if err != nil {
		c.logger.Error("Failed to update province status", zap.Error(err), zap.String("code", code))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update status", err)
		return
	}

	util.SuccessResponse(ctx, "Province status updated successfully", nil)
}

// CheckProvinceHealth 检查省份服务健康状态
// @Summary 检查省份服务健康状态
// @Description 检查指定省份的税务服务健康状态
// @Tags 省份管理
// @Accept json
// @Produce json
// @Param code path string true "省份代码"
// @Success 200 {object} util.Response{data=model.ProvinceHealthResponse}
// @Failure 400 {object} util.Response
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/provinces/{code}/health [get]
func (c *TaxFilingProvinceController) CheckProvinceHealth(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		util.BadRequest(ctx, "Province code is required")
		return
	}

	// 检查省份健康状态
	health, err := c.provinceService.HealthCheck(ctx.Request.Context(), code)
	if err != nil {
		c.logger.Error("Failed to check province health", zap.Error(err), zap.String("code", code))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to check health", err)
		return
	}

	util.SuccessResponse(ctx, "Province health checked successfully", health)
}

// GetProvinceStatistics 获取省份统计信息
// @Summary 获取省份统计信息
// @Description 获取省份的申报统计信息
// @Tags 省份管理
// @Accept json
// @Produce json
// @Param code path string true "省份代码"
// @Param start_date query string false "开始日期 (YYYY-MM-DD)"
// @Param end_date query string false "结束日期 (YYYY-MM-DD)"
// @Success 200 {object} util.Response{data=model.ProvinceStatistics}
// @Failure 400 {object} util.Response
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/provinces/{code}/statistics [get]
func (c *TaxFilingProvinceController) GetProvinceStatistics(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		util.BadRequest(ctx, "Province code is required")
		return
	}

	startDate := ctx.Query("start_date")
	endDate := ctx.Query("end_date")

	// TODO: 实现省份统计信息获取
	statistics := map[string]interface{}{
		"code":       code,
		"start_date": startDate,
		"end_date":   endDate,
		"message":    "Statistics feature not implemented yet",
	}

	util.SuccessResponse(ctx, "Province statistics retrieved successfully", statistics)
}

// TestProvinceConnection 测试省份服务连接
// @Summary 测试省份服务连接
// @Description 测试与指定省份税务服务的连接
// @Tags 省份管理
// @Accept json
// @Produce json
// @Param code path string true "省份代码"
// @Success 200 {object} util.Response{data=model.ConnectionTestResult}
// @Failure 400 {object} util.Response
// @Failure 404 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/provinces/{code}/test [post]
func (c *TaxFilingProvinceController) TestProvinceConnection(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		util.BadRequest(ctx, "Province code is required")
		return
	}

	// TODO: 实现省份连接测试
	result := map[string]interface{}{
		"code":    code,
		"status":  "success",
		"message": "Connection test feature not implemented yet",
	}

	util.SuccessResponse(ctx, "Province connection tested successfully", result)
}
