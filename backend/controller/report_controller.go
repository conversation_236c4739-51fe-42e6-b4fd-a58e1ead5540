// Package controller provides HTTP controllers for the tax management system.
// This file implements report management handlers including CRUD operations,
// report generation, and template management.
package controller

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"backend/service"
	"backend/util"
)

// ReportController handles report-related HTTP requests
type ReportController struct {
	reportService *service.ReportService
}

// NewReportController creates a new report handler instance
func NewReportController(reportService *service.ReportService) *ReportController {
	return &ReportController{
		reportService: reportService,
	}
}

// CreateReportTemplate handles POST /report-templates - creates a new report template
func (h *ReportController) CreateReportTemplate(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	var req service.CreateReportTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	template, err := h.reportService.CreateReportTemplate(c.Request.Context(), req, userID)
	if err != nil {
		util.BadRequest(c, "创建报表模板失败", err)
		return
	}

	util.Success(c, template, "创建报表模板成功")
}

// GetReportTemplates handles GET /report-templates - retrieves report template list with pagination and filters
func (h *ReportController) GetReportTemplates(c *gin.Context) {
	// Parse query parameters
	page := 1
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}

	pageSize := 10
	if ps := c.Query("pageSize"); ps != "" {
		if parsed, err := strconv.Atoi(ps); err == nil && parsed > 0 && parsed <= 100 {
			pageSize = parsed
		}
	}

	filter := service.ReportTemplateFilter{
		Page:     page,
		PageSize: pageSize,
		Type:     c.Query("type"),
		Keyword:  c.Query("keyword"),
	}

	templates, total, err := h.reportService.GetReportTemplates(c.Request.Context(), filter)
	if err != nil {
		util.InternalServerError(c, "获取报表模板列表失败", err)
		return
	}

	result := map[string]interface{}{
		"list":     templates,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	}

	util.Success(c, result, "获取报表模板列表成功")
}

// GetReportTemplateByID handles GET /report-templates/:id - retrieves report template by ID
func (h *ReportController) GetReportTemplateByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "报表模板ID不能为空")
		return
	}

	template, err := h.reportService.GetReportTemplateByID(c.Request.Context(), id)
	if err != nil {
		util.NotFound(c, "报表模板不存在", err)
		return
	}

	util.Success(c, template, "获取报表模板详情成功")
}

// UpdateReportTemplate handles PUT /report-templates/:id - updates report template
func (h *ReportController) UpdateReportTemplate(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "报表模板ID不能为空")
		return
	}

	var req service.UpdateReportTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	template, err := h.reportService.UpdateReportTemplate(c.Request.Context(), id, req, userID)
	if err != nil {
		util.BadRequest(c, "更新报表模板失败", err)
		return
	}

	util.Success(c, template, "更新报表模板成功")
}

// DeleteReportTemplate handles DELETE /report-templates/:id - deletes report template
func (h *ReportController) DeleteReportTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "报表模板ID不能为空")
		return
	}

	if err := h.reportService.DeleteReportTemplate(c.Request.Context(), id); err != nil {
		util.BadRequest(c, "删除报表模板失败", err)
		return
	}

	util.Success(c, nil, "删除报表模板成功")
}

// BatchDeleteReportTemplates handles POST /report-templates/batch/delete - batch delete report templates
func (h *ReportController) BatchDeleteReportTemplates(c *gin.Context) {
	var req struct {
		IDs []string `json:"ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	if err := h.reportService.BatchDeleteReportTemplates(c.Request.Context(), req.IDs); err != nil {
		util.BadRequest(c, "批量删除报表模板失败", err)
		return
	}

	util.Success(c, nil, "批量删除报表模板成功")
}

// GetReportTemplatesByType handles GET /report-templates/by-type - retrieves report templates by type
func (h *ReportController) GetReportTemplatesByType(c *gin.Context) {
	templateType := c.Query("type")

	templates, err := h.reportService.GetReportTemplatesByType(c.Request.Context(), templateType)
	if err != nil {
		util.InternalServerError(c, "获取报表模板失败", err)
		return
	}

	util.Success(c, templates, "获取报表模板成功")
}

// GenerateReport handles POST /reports/generate - generates a report
func (h *ReportController) GenerateReport(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	var req service.GenerateReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	// Set default format if not provided
	if req.Format == "" {
		req.Format = "json"
	}

	report, err := h.reportService.GenerateReport(c.Request.Context(), req, userID)
	if err != nil {
		util.BadRequest(c, "生成报表失败", err)
		return
	}

	util.Success(c, report, "生成报表成功")
}

// GetReportTypes handles GET /reports/types - retrieves available report types
func (h *ReportController) GetReportTypes(c *gin.Context) {
	types := []map[string]interface{}{
		{
			"value":       "declaration",
			"label":       "申报表",
			"description": "税务申报相关报表",
		},
		{
			"value":       "invoice",
			"label":       "发票报表",
			"description": "发票管理相关报表",
		},
		{
			"value":       "tax",
			"label":       "税务报表",
			"description": "税务计算和分析报表",
		},
		{
			"value":       "financial",
			"label":       "财务报表",
			"description": "财务数据相关报表",
		},
		{
			"value":       "statistics",
			"label":       "统计报表",
			"description": "数据统计和分析报表",
		},
		{
			"value":       "custom",
			"label":       "自定义报表",
			"description": "用户自定义报表模板",
		},
	}

	util.Success(c, types, "获取报表类型成功")
}

// GetReportFormats handles GET /reports/formats - retrieves available report formats
func (h *ReportController) GetReportFormats(c *gin.Context) {
	formats := []map[string]interface{}{
		{
			"value":     "excel",
			"label":     "Excel格式",
			"extension": ".xlsx",
			"mime_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
		},
		{
			"value":     "pdf",
			"label":     "PDF格式",
			"extension": ".pdf",
			"mime_type": "application/pdf",
		},
		{
			"value":     "html",
			"label":     "HTML格式",
			"extension": ".html",
			"mime_type": "text/html",
		},
		{
			"value":     "csv",
			"label":     "CSV格式",
			"extension": ".csv",
			"mime_type": "text/csv",
		},
		{
			"value":     "json",
			"label":     "JSON格式",
			"extension": ".json",
			"mime_type": "application/json",
		},
	}

	util.Success(c, formats, "获取报表格式成功")
}
