// Package controller provides HTTP controllers for the tax management system.
// This file implements tax policy management handlers including CRUD operations,
// policy search, and policy statistics.
package controller

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"backend/service"
	"backend/util"
)

// TaxPolicyController handles tax policy-related HTTP requests
type TaxPolicyController struct {
	taxPolicyService *service.TaxPolicyService
}

// NewTaxPolicyController creates a new tax policy handler instance
func NewTaxPolicyController(taxPolicyService *service.TaxPolicyService) *TaxPolicyController {
	return &TaxPolicyController{
		taxPolicyService: taxPolicyService,
	}
}

// CreateTaxPolicy handles POST /tax-policies - creates a new tax policy
func (h *TaxPolicyController) CreateTaxPolicy(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	var req service.CreateTaxPolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	policy, err := h.taxPolicyService.CreateTaxPolicy(c.Request.Context(), req, userID)
	if err != nil {
		util.BadRequest(c, "创建税务政策失败", err)
		return
	}

	util.Success(c, policy, "创建税务政策成功")
}

// GetTaxPolicies handles GET /tax-policies - retrieves tax policy list with pagination and filters
func (h *TaxPolicyController) GetTaxPolicies(c *gin.Context) {
	// Parse query parameters
	page := 1
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}

	pageSize := 10
	if ps := c.Query("pageSize"); ps != "" {
		if parsed, err := strconv.Atoi(ps); err == nil && parsed > 0 && parsed <= 100 {
			pageSize = parsed
		}
	}

	filter := service.TaxPolicyFilter{
		Page:             page,
		PageSize:         pageSize,
		Category:         c.Query("category"),
		Status:           c.Query("status"),
		IssuingAuthority: c.Query("issuing_authority"),
		PriorityLevel:    c.Query("priority_level"),
		Keyword:          c.Query("keyword"),
		EffectiveFrom:    c.Query("effective_from"),
		EffectiveTo:      c.Query("effective_to"),
	}

	policies, total, err := h.taxPolicyService.GetTaxPolicies(c.Request.Context(), filter)
	if err != nil {
		util.InternalServerError(c, "获取税务政策列表失败", err)
		return
	}

	result := map[string]interface{}{
		"list":     policies,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	}

	util.Success(c, result, "获取税务政策列表成功")
}

// GetTaxPolicyByID handles GET /tax-policies/:id - retrieves tax policy by ID
func (h *TaxPolicyController) GetTaxPolicyByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "税务政策ID不能为空")
		return
	}

	policy, err := h.taxPolicyService.GetTaxPolicyByID(c.Request.Context(), id)
	if err != nil {
		util.NotFound(c, "税务政策不存在", err)
		return
	}

	util.Success(c, policy, "获取税务政策详情成功")
}

// UpdateTaxPolicy handles PUT /tax-policies/:id - updates tax policy
func (h *TaxPolicyController) UpdateTaxPolicy(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "税务政策ID不能为空")
		return
	}

	var req service.UpdateTaxPolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	policy, err := h.taxPolicyService.UpdateTaxPolicy(c.Request.Context(), id, req)
	if err != nil {
		util.BadRequest(c, "更新税务政策失败", err)
		return
	}

	util.Success(c, policy, "更新税务政策成功")
}

// DeleteTaxPolicy handles DELETE /tax-policies/:id - deletes tax policy
func (h *TaxPolicyController) DeleteTaxPolicy(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "税务政策ID不能为空")
		return
	}

	if err := h.taxPolicyService.DeleteTaxPolicy(c.Request.Context(), id); err != nil {
		util.BadRequest(c, "删除税务政策失败", err)
		return
	}

	util.Success(c, nil, "删除税务政策成功")
}

// BatchDeleteTaxPolicies handles POST /tax-policies/batch/delete - batch delete tax policies
func (h *TaxPolicyController) BatchDeleteTaxPolicies(c *gin.Context) {
	var req struct {
		IDs []string `json:"ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	if err := h.taxPolicyService.BatchDeleteTaxPolicies(c.Request.Context(), req.IDs); err != nil {
		util.BadRequest(c, "批量删除税务政策失败", err)
		return
	}

	util.Success(c, nil, "批量删除税务政策成功")
}

// UpdateTaxPolicyStatus handles PUT /tax-policies/:id/status - updates tax policy status
func (h *TaxPolicyController) UpdateTaxPolicyStatus(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		util.BadRequest(c, "税务政策ID不能为空")
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	if err := h.taxPolicyService.UpdateTaxPolicyStatus(c.Request.Context(), id, req.Status); err != nil {
		util.BadRequest(c, "更新税务政策状态失败", err)
		return
	}

	util.Success(c, nil, "更新税务政策状态成功")
}

// BatchUpdateTaxPolicyStatus handles POST /tax-policies/batch/status - batch update tax policy status
func (h *TaxPolicyController) BatchUpdateTaxPolicyStatus(c *gin.Context) {
	var req struct {
		IDs    []string `json:"ids" binding:"required"`
		Status string   `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	if err := h.taxPolicyService.BatchUpdateTaxPolicyStatus(c.Request.Context(), req.IDs, req.Status); err != nil {
		util.BadRequest(c, "批量更新税务政策状态失败", err)
		return
	}

	util.Success(c, nil, "批量更新税务政策状态成功")
}

// GetTaxPolicyStats handles GET /tax-policies/stats - retrieves tax policy statistics
func (h *TaxPolicyController) GetTaxPolicyStats(c *gin.Context) {
	stats, err := h.taxPolicyService.GetTaxPolicyStats(c.Request.Context())
	if err != nil {
		util.InternalServerError(c, "获取税务政策统计信息失败", err)
		return
	}

	util.Success(c, stats, "获取税务政策统计信息成功")
}

// SearchTaxPolicies handles GET /tax-policies/search - searches tax policies
func (h *TaxPolicyController) SearchTaxPolicies(c *gin.Context) {
	keyword := c.Query("keyword")
	limit := 10
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 50 {
			limit = parsed
		}
	}

	policies, err := h.taxPolicyService.SearchTaxPolicies(c.Request.Context(), keyword, limit)
	if err != nil {
		util.InternalServerError(c, "搜索税务政策失败", err)
		return
	}

	util.Success(c, policies, "搜索税务政策成功")
}

// GetEffectiveTaxPolicies handles GET /tax-policies/effective - retrieves effective tax policies
func (h *TaxPolicyController) GetEffectiveTaxPolicies(c *gin.Context) {
	category := c.Query("category")

	policies, err := h.taxPolicyService.GetEffectiveTaxPolicies(c.Request.Context(), category)
	if err != nil {
		util.InternalServerError(c, "获取有效税务政策失败", err)
		return
	}

	util.Success(c, policies, "获取有效税务政策成功")
}
