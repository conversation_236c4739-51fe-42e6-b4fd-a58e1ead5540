// Package controller provides HTTP controllers for the tax management system.
// It includes controllers for notification management operations.
package controller

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"backend/model"
	"backend/service"
	"backend/util"
)

// NotificationController handles notification-related HTTP requests including creation,
// retrieval, updates, deletion, and notification management operations.
type NotificationController struct {
	notificationService service.NotificationService
}

// NewNotificationController creates a new notification handler instance
func NewNotificationController(notificationService service.NotificationService) *NotificationController {
	return &NotificationController{
		notificationService: notificationService,
	}
}

// CreateNotification handles POST /notifications - creates a new notification
func (h *NotificationController) CreateNotification(c *gin.Context) {
	var req model.NotificationCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	notification, err := h.notificationService.CreateNotification(c.Request.Context(), req)
	if err != nil {
		util.BadRequest(c, "创建通知失败", err)
		return
	}

	util.Success(c, notification, "创建通知成功")
}

// BatchCreateNotifications handles POST /notifications/batch - creates multiple notifications
func (h *NotificationController) BatchCreateNotifications(c *gin.Context) {
	var req model.NotificationBatchCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	notifications, err := h.notificationService.BatchCreateNotifications(c.Request.Context(), req)
	if err != nil {
		util.BadRequest(c, "批量创建通知失败", err)
		return
	}

	util.Success(c, notifications, "批量创建通知成功")
}

// GetNotifications handles GET /notifications - retrieves notifications with pagination and filtering
func (h *NotificationController) GetNotifications(c *gin.Context) {
	var req service.GetNotificationsRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}
	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}

	req.RecipientID = c.Query("recipientId")
	req.Type = c.Query("type")
	req.SortBy = c.Query("sortBy")
	req.SortOrder = c.Query("sortOrder")

	if isRead := c.Query("isRead"); isRead != "" {
		if read, err := strconv.ParseBool(isRead); err == nil {
			req.IsRead = &read
		}
	}

	if createdStart := c.Query("createdStart"); createdStart != "" {
		if t, err := time.Parse("2006-01-02", createdStart); err == nil {
			req.CreatedStart = &t
		}
	}

	if createdEnd := c.Query("createdEnd"); createdEnd != "" {
		if t, err := time.Parse("2006-01-02", createdEnd); err == nil {
			req.CreatedEnd = &t
		}
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	result, err := h.notificationService.GetNotifications(c.Request.Context(), req)
	if err != nil {
		util.InternalServerError(c, "获取通知列表失败", err)
		return
	}

	util.Success(c, result, "获取通知列表成功")
}

// GetNotificationByID handles GET /notifications/:id - retrieves notification by ID
func (h *NotificationController) GetNotificationByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" || id == "undefined" || id == "null" {
		util.BadRequest(c, "通知ID无效")
		return
	}

	notification, err := h.notificationService.GetNotificationByID(c.Request.Context(), id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			util.BadRequest(c, "通知不存在")
		} else {
			util.InternalServerError(c, "获取通知详情失败", err)
		}
		return
	}

	util.Success(c, notification, "获取通知详情成功")
}

// UpdateNotification handles PUT /notifications/:id - updates notification information
func (h *NotificationController) UpdateNotification(c *gin.Context) {
	id := c.Param("id")
	if id == "" || id == "undefined" || id == "null" {
		util.BadRequest(c, "通知ID无效")
		return
	}

	var req service.UpdateNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	notification, err := h.notificationService.UpdateNotification(c.Request.Context(), id, req)
	if err != nil {
		util.BadRequest(c, "更新通知失败", err)
		return
	}

	util.Success(c, notification, "更新通知成功")
}

// DeleteNotification handles DELETE /notifications/:id - deletes notification by ID
func (h *NotificationController) DeleteNotification(c *gin.Context) {
	id := c.Param("id")
	if id == "" || id == "undefined" || id == "null" {
		util.BadRequest(c, "通知ID无效")
		return
	}

	err := h.notificationService.DeleteNotification(c.Request.Context(), id)
	if err != nil {
		util.BadRequest(c, "删除通知失败", err)
		return
	}

	util.Success(c, nil, "删除通知成功")
}

// MarkAsRead handles PUT /notifications/mark-read - marks notifications as read
func (h *NotificationController) MarkAsRead(c *gin.Context) {
	var req model.MarkAsReadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	err := h.notificationService.MarkAsRead(c.Request.Context(), req)
	if err != nil {
		util.BadRequest(c, "标记通知为已读失败", err)
		return
	}

	util.Success(c, nil, "标记通知为已读成功")
}

// GetNotificationCount handles GET /notifications/count - gets notification count for current user
func (h *NotificationController) GetNotificationCount(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	// Allow querying for specific recipient if provided and user has permission
	recipientID := c.Query("recipientId")
	if recipientID == "" {
		recipientID = userID
	}

	// TODO: Add permission check if querying for other users

	count, err := h.notificationService.GetNotificationCount(c.Request.Context(), recipientID)
	if err != nil {
		util.InternalServerError(c, "获取通知计数失败", err)
		return
	}

	util.Success(c, count, "获取通知计数成功")
}

// GetUnreadNotifications handles GET /notifications/unread - gets unread notifications for current user
func (h *NotificationController) GetUnreadNotifications(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	limit := 10
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	notifications, err := h.notificationService.GetUnreadNotifications(c.Request.Context(), userID, limit)
	if err != nil {
		util.InternalServerError(c, "获取未读通知失败", err)
		return
	}

	util.Success(c, notifications, "获取未读通知成功")
}

// DeleteReadNotifications handles DELETE /notifications/read - deletes all read notifications for current user
func (h *NotificationController) DeleteReadNotifications(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	err := h.notificationService.DeleteReadNotifications(c.Request.Context(), userID)
	if err != nil {
		util.BadRequest(c, "删除已读通知失败", err)
		return
	}

	util.Success(c, nil, "删除已读通知成功")
}

// GetMyNotifications handles GET /notifications/my - gets notifications for current user
func (h *NotificationController) GetMyNotifications(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		util.Unauthorized(c, "用户未认证")
		return
	}

	var req service.GetNotificationsRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}
	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}

	req.RecipientID = userID // Force to current user
	req.Type = c.Query("type")
	req.SortBy = c.Query("sortBy")
	req.SortOrder = c.Query("sortOrder")

	if isRead := c.Query("isRead"); isRead != "" {
		if read, err := strconv.ParseBool(isRead); err == nil {
			req.IsRead = &read
		}
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	result, err := h.notificationService.GetNotifications(c.Request.Context(), req)
	if err != nil {
		util.InternalServerError(c, "获取我的通知失败", err)
		return
	}

	util.Success(c, result, "获取我的通知成功")
}

// BatchDeleteNotifications handles DELETE /notifications/batch - batch deletes notifications
func (h *NotificationController) BatchDeleteNotifications(c *gin.Context) {
	var req struct {
		IDs []string `json:"ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.BadRequest(c, "参数错误", err)
		return
	}

	var errors []string
	var successCount int

	for _, id := range req.IDs {
		if err := h.notificationService.DeleteNotification(c.Request.Context(), id); err != nil {
			errors = append(errors, fmt.Sprintf("删除通知 %s 失败: %v", id, err))
		} else {
			successCount++
		}
	}

	result := gin.H{
		"successCount": successCount,
		"totalCount":   len(req.IDs),
		"errors":       errors,
	}

	if len(errors) > 0 {
		util.Success(c, result, fmt.Sprintf("批量删除完成，成功 %d 个，失败 %d 个", successCount, len(errors)))
	} else {
		util.Success(c, result, "批量删除通知成功")
	}
}
