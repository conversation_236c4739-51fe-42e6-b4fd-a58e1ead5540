package controller

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"backend/model"
	"backend/service"
	"backend/util"
)

// TaxFilingWebhookController 税务申报Webhook控制器
type TaxFilingWebhookController struct {
	taxFilingService      *service.TaxFilingService
	taxFilingBatchService *service.TaxFilingBatchService
	logger                *zap.Logger
}

// NewTaxFilingWebhookController 创建Webhook控制器
func NewTaxFilingWebhookController(
	taxFilingService *service.TaxFilingService,
	taxFilingBatchService *service.TaxFilingBatchService,
	logger *zap.Logger,
) *TaxFilingWebhookController {
	return &TaxFilingWebhookController{
		taxFilingService:      taxFilingService,
		taxFilingBatchService: taxFilingBatchService,
		logger:                logger,
	}
}

// WebhookRequest Webhook请求结构
type WebhookRequest struct {
	Type      string                 `json:"type" binding:"required"` // 通知类型
	Data      map[string]interface{} `json:"data" binding:"required"` // 通知数据
	Service   string                 `json:"service"`                 // 服务名称
	Timestamp string                 `json:"timestamp"`               // 时间戳
	Signature string                 `json:"signature"`               // 签名
}

// HandleGeneralWebhook 处理通用Webhook
// @Summary 处理通用Webhook
// @Description 接收来自Python税务申报服务的通用Webhook通知
// @Tags Webhook
// @Accept json
// @Produce json
// @Param request body WebhookRequest true "Webhook请求"
// @Success 200 {object} util.Response
// @Failure 400 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/webhook [post]
func (c *TaxFilingWebhookController) HandleGeneralWebhook(ctx *gin.Context) {
	var req WebhookRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Error("Invalid webhook request", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusBadRequest, "Invalid webhook request", err)
		return
	}

	c.logger.Info("Received webhook",
		zap.String("type", req.Type),
		zap.String("service", req.Service),
		zap.String("timestamp", req.Timestamp),
	)

	// 根据类型分发处理
	switch req.Type {
	case "submission_created":
		c.handleSubmissionCreated(ctx, req.Data)
	case "submission_submitted":
		c.handleSubmissionSubmitted(ctx, req.Data)
	case "submission_accepted":
		c.handleSubmissionAccepted(ctx, req.Data)
	case "submission_rejected":
		c.handleSubmissionRejected(ctx, req.Data)
	case "submission_failed":
		c.handleSubmissionFailed(ctx, req.Data)
	case "validation_failed":
		c.handleValidationFailed(ctx, req.Data)
	case "system_error":
		c.handleSystemError(ctx, req.Data)
	case "batch_notification":
		c.handleBatchNotification(ctx, req.Data)
	default:
		c.logger.Warn("Unknown webhook type", zap.String("type", req.Type))
		util.ErrorResponse(ctx, http.StatusBadRequest, "Unknown webhook type", fmt.Errorf("unknown type: %s", req.Type))
		return
	}

	util.SuccessResponse(ctx, "Webhook processed successfully", nil)
}

// StatusUpdateRequest 状态更新请求
type StatusUpdateRequest struct {
	SubmissionID string                          `json:"submission_id"`
	ExternalID   *string                         `json:"external_id,omitempty"`
	Status       model.TaxFilingSubmissionStatus `json:"status"`
	Message      string                          `json:"message"`
	ErrorMessage *string                         `json:"error_message,omitempty"`
	ProcessedAt  *string                         `json:"processed_at,omitempty"`
	Progress     map[string]interface{}          `json:"progress,omitempty"`
}

// BatchUpdateRequest 批次更新请求
type BatchUpdateRequest struct {
	BatchID               string  `json:"batch_id"`
	Status                string  `json:"status"`
	TotalSubmissions      int     `json:"total_submissions"`
	SuccessfulSubmissions int     `json:"successful_submissions"`
	FailedSubmissions     int     `json:"failed_submissions"`
	ProcessingSubmissions int     `json:"processing_submissions"`
	ProgressPercentage    float64 `json:"progress_percentage"`
	Message               string  `json:"message"`
}

// HandleWebhook 处理通用Webhook
// @Summary 处理税务申报Webhook
// @Description 接收来自Python税务申报服务的Webhook通知
// @Tags Webhook
// @Accept json
// @Produce json
// @Param request body WebhookRequest true "Webhook请求"
// @Success 200 {object} util.Response
// @Failure 400 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/webhook [post]
func (c *TaxFilingWebhookController) HandleWebhook(ctx *gin.Context) {
	var req WebhookRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Error("Invalid webhook request", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusBadRequest, "Invalid webhook request", err)
		return
	}

	c.logger.Info("Received webhook",
		zap.String("type", req.Type),
		zap.String("service", req.Service),
		zap.String("timestamp", req.Timestamp),
	)

	// 根据通知类型处理
	switch req.Type {
	case "submission_created":
		c.handleSubmissionCreated(ctx, req.Data)
	case "submission_submitted":
		c.handleSubmissionSubmitted(ctx, req.Data)
	case "submission_accepted":
		c.handleSubmissionAccepted(ctx, req.Data)
	case "submission_rejected":
		c.handleSubmissionRejected(ctx, req.Data)
	case "submission_failed":
		c.handleSubmissionFailed(ctx, req.Data)
	case "validation_failed":
		c.handleValidationFailed(ctx, req.Data)
	case "system_error":
		c.handleSystemError(ctx, req.Data)
	case "batch_notification":
		c.handleBatchNotification(ctx, req.Data)
	default:
		c.logger.Warn("Unknown webhook type", zap.String("type", req.Type))
		util.ErrorResponse(ctx, http.StatusBadRequest, "Unknown webhook type", fmt.Errorf("unknown type: %s", req.Type))
		return
	}

	util.SuccessResponse(ctx, "Webhook processed successfully", nil)
}

// HandleStatusUpdate 处理状态更新
// @Summary 处理申报状态更新
// @Description 接收申报状态更新通知
// @Tags Webhook
// @Accept json
// @Produce json
// @Param request body StatusUpdateRequest true "状态更新请求"
// @Success 200 {object} util.Response
// @Failure 400 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/webhook/status [post]
func (c *TaxFilingWebhookController) HandleStatusUpdate(ctx *gin.Context) {
	var req StatusUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Error("Invalid status update request", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusBadRequest, "Invalid status update request", err)
		return
	}

	c.logger.Info("Received status update",
		zap.String("submission_id", req.SubmissionID),
		zap.String("status", string(req.Status)),
		zap.String("message", req.Message),
	)

	// 更新申报状态
	if err := c.taxFilingService.UpdateSubmissionStatus(
		ctx.Request.Context(),
		req.SubmissionID,
		req.Status,
		req.Message,
		req.ErrorMessage,
	); err != nil {
		c.logger.Error("Failed to update submission status", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update status", err)
		return
	}

	util.SuccessResponse(ctx, "Status updated successfully", nil)
}

// HandleBatchUpdate 处理批次更新
// @Summary 处理批次更新
// @Description 接收批次状态更新通知
// @Tags Webhook
// @Accept json
// @Produce json
// @Param request body BatchUpdateRequest true "批次更新请求"
// @Success 200 {object} util.Response
// @Failure 400 {object} util.Response
// @Failure 500 {object} util.Response
// @Router /api/tax-filing/webhook/batch [post]
func (c *TaxFilingWebhookController) HandleBatchUpdate(ctx *gin.Context) {
	var req BatchUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Error("Invalid batch update request", zap.Error(err))
		util.ErrorResponse(ctx, http.StatusBadRequest, "Invalid batch update request", err)
		return
	}

	c.logger.Info("Received batch update",
		zap.String("batch_id", req.BatchID),
		zap.String("status", req.Status),
		zap.Float64("progress", req.ProgressPercentage),
	)

	// 这里可以添加批次状态更新逻辑
	// 目前批次状态主要由内部服务管理，Webhook主要用于监控和通知

	util.SuccessResponse(ctx, "Batch update processed successfully", nil)
}

// handleSubmissionCreated 处理申报创建通知
func (c *TaxFilingWebhookController) handleSubmissionCreated(ctx *gin.Context, data map[string]interface{}) {
	submissionID, ok := data["submission_id"].(string)
	if !ok {
		c.logger.Error("Invalid submission_id in webhook data")
		return
	}

	c.logger.Info("Submission created notification received", zap.String("submission_id", submissionID))
	// 这里可以添加额外的处理逻辑，比如发送通知等
}

// handleSubmissionSubmitted 处理申报提交通知
func (c *TaxFilingWebhookController) handleSubmissionSubmitted(ctx *gin.Context, data map[string]interface{}) {
	submissionID, ok := data["submission_id"].(string)
	if !ok {
		c.logger.Error("Invalid submission_id in webhook data")
		return
	}

	// 更新状态为已提交
	if err := c.taxFilingService.UpdateSubmissionStatus(
		ctx.Request.Context(),
		submissionID,
		model.TaxFilingStatusSubmitted,
		"申报已提交到税务局",
		nil,
	); err != nil {
		c.logger.Error("Failed to update submission to submitted status", zap.Error(err))
	}
}

// handleSubmissionAccepted 处理申报接受通知
func (c *TaxFilingWebhookController) handleSubmissionAccepted(ctx *gin.Context, data map[string]interface{}) {
	submissionID, ok := data["submission_id"].(string)
	if !ok {
		c.logger.Error("Invalid submission_id in webhook data")
		return
	}

	// 更新状态为已接受
	if err := c.taxFilingService.UpdateSubmissionStatus(
		ctx.Request.Context(),
		submissionID,
		model.TaxFilingStatusAccepted,
		"申报已被税务局接受",
		nil,
	); err != nil {
		c.logger.Error("Failed to update submission to accepted status", zap.Error(err))
	}
}

// handleSubmissionRejected 处理申报拒绝通知
func (c *TaxFilingWebhookController) handleSubmissionRejected(ctx *gin.Context, data map[string]interface{}) {
	submissionID, ok := data["submission_id"].(string)
	if !ok {
		c.logger.Error("Invalid submission_id in webhook data")
		return
	}

	errorMessage := "申报被税务局拒绝"
	if msg, exists := data["error_message"].(string); exists && msg != "" {
		errorMessage = msg
	}

	// 更新状态为已拒绝
	if err := c.taxFilingService.UpdateSubmissionStatus(
		ctx.Request.Context(),
		submissionID,
		model.TaxFilingStatusRejected,
		"申报被税务局拒绝",
		&errorMessage,
	); err != nil {
		c.logger.Error("Failed to update submission to rejected status", zap.Error(err))
	}
}

// handleSubmissionFailed 处理申报失败通知
func (c *TaxFilingWebhookController) handleSubmissionFailed(ctx *gin.Context, data map[string]interface{}) {
	submissionID, ok := data["submission_id"].(string)
	if !ok {
		c.logger.Error("Invalid submission_id in webhook data")
		return
	}

	errorMessage := "申报处理失败"
	if msg, exists := data["error_message"].(string); exists && msg != "" {
		errorMessage = msg
	}

	// 更新状态为失败
	if err := c.taxFilingService.UpdateSubmissionStatus(
		ctx.Request.Context(),
		submissionID,
		model.TaxFilingStatusFailed,
		"申报处理失败",
		&errorMessage,
	); err != nil {
		c.logger.Error("Failed to update submission to failed status", zap.Error(err))
	}
}

// handleValidationFailed 处理验证失败通知
func (c *TaxFilingWebhookController) handleValidationFailed(ctx *gin.Context, data map[string]interface{}) {
	submissionID, _ := data["submission_id"].(string)
	provinceCode, _ := data["province_code"].(string)

	c.logger.Warn("Validation failed notification received",
		zap.String("submission_id", submissionID),
		zap.String("province_code", provinceCode),
	)

	// 如果有submission_id，更新状态
	if submissionID != "" {
		errorMessage := "数据验证失败"
		if msg, exists := data["error_message"].(string); exists && msg != "" {
			errorMessage = msg
		}

		if err := c.taxFilingService.UpdateSubmissionStatus(
			ctx.Request.Context(),
			submissionID,
			model.TaxFilingStatusFailed,
			"数据验证失败",
			&errorMessage,
		); err != nil {
			c.logger.Error("Failed to update submission status for validation failure", zap.Error(err))
		}
	}
}

// handleSystemError 处理系统错误通知
func (c *TaxFilingWebhookController) handleSystemError(ctx *gin.Context, data map[string]interface{}) {
	errorType, _ := data["error_type"].(string)
	errorMessage, _ := data["error_message"].(string)

	c.logger.Error("System error notification received",
		zap.String("error_type", errorType),
		zap.String("error_message", errorMessage),
	)

	// 这里可以添加系统错误处理逻辑，比如发送告警等
}

// handleBatchNotification 处理批次通知
func (c *TaxFilingWebhookController) handleBatchNotification(ctx *gin.Context, data map[string]interface{}) {
	batchID, _ := data["batch_id"].(string)
	notificationType, _ := data["type"].(string)

	c.logger.Info("Batch notification received",
		zap.String("batch_id", batchID),
		zap.String("notification_type", notificationType),
	)

	// 这里可以添加批次通知处理逻辑
}
