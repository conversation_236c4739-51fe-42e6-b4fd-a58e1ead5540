# 数据库模式更新说明

## 概述
本文档记录了对 `backend/sql/smeasy_tax.sql` 文件的修正，确保SQL表结构与Go模型定义保持一致。

## 修正的问题

### 1. 字段名不一致问题

#### tax_policies 表
**修正前：**
- `policy_title` → **修正为：** `title`
- `policy_category` → **修正为：** `category`
- `policy_status` → **修正为：** `status`
- `policy_keywords` → **修正为：** `keywords`

**修正后的字段类型：**
- `category`: 改为 `enum('tax_law','tax_policy','tax_notice','tax_guidance')`
- `status`: 改为 `enum('draft','effective','expired','revoked')`
- `priority_level`: 改为 `enum('low','normal','high')`
- JSON字段改为VARCHAR类型以简化处理

#### tax_inspections 表
**修正前：**
- `inspection_status` → **修正为：** `status`

#### tax_risk_assessments 表
**修正前：**
- `assessment_status` → **修正为：** `status`

#### tax_payments 表
**修正前：**
- `payment_status` → **修正为：** `status`

### 2. 新增缺失的表

#### report_templates 表
```sql
CREATE TABLE `report_templates` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '模板名称',
  `description` text COMMENT '模板描述',
  `type` enum('declaration','invoice','tax','financial','statistics','custom') NOT NULL COMMENT '模板类型',
  `content` text NOT NULL COMMENT '模板内容',
  `parameters` text COMMENT '模板参数配置',
  `created_by` varchar(36) COMMENT '创建人ID',
  `updated_by` varchar(36) COMMENT '更新人ID',
  `created_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间'
);
```

#### workflow_definitions 表
```sql
CREATE TABLE `workflow_definitions` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '工作流名称',
  `description` text COMMENT '工作流描述',
  `type` varchar(100) NOT NULL COMMENT '工作流类型',
  `definition` json NOT NULL COMMENT '工作流定义',
  `version` int NOT NULL DEFAULT '1' COMMENT '版本号',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `created_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间'
);
```

#### workflow_instances 表
```sql
CREATE TABLE `workflow_instances` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `workflow_definition_id` varchar(36) NOT NULL COMMENT '工作流定义ID',
  `related_entity_type` varchar(100) NOT NULL COMMENT '关联实体类型',
  `related_entity_id` varchar(36) NOT NULL COMMENT '关联实体ID',
  `status` varchar(50) NOT NULL COMMENT '实例状态',
  `current_state` varchar(100) NOT NULL COMMENT '当前状态节点',
  `context` json COMMENT '上下文数据',
  `started_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '开始时间',
  `ended_at` datetime(3) COMMENT '结束时间',
  `created_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间'
);
```

### 3. 索引修正
- 更新了所有相关的索引名称以匹配新的字段名
- 确保外键约束正确引用

### 4. 示例数据更新
- 更新了 `tax_policies` 表的示例数据以匹配新的字段结构
- 添加了 `report_templates` 和 `workflow_definitions` 表的示例数据

## 验证步骤

1. **编译验证：** 后端Go代码编译成功
2. **模型一致性：** 所有Go模型字段与SQL表字段完全匹配
3. **外键约束：** 所有外键关系正确设置
4. **索引优化：** 关键查询字段都有适当的索引

## 影响范围

- ✅ 税务政策管理模块
- ✅ 税务稽查管理模块  
- ✅ 税务风险评估模块
- ✅ 税务缴费管理模块
- ✅ 报表模板管理模块
- ✅ 工作流管理模块

## 注意事项

1. **数据迁移：** 如果现有数据库已有数据，需要执行相应的ALTER TABLE语句进行字段重命名
2. **应用更新：** 确保前端和后端代码使用正确的字段名
3. **测试验证：** 建议在测试环境先验证所有CRUD操作正常

## 迁移脚本示例

如果需要从旧结构迁移到新结构，可以使用以下SQL语句：

```sql
-- 税务政策表字段重命名
ALTER TABLE tax_policies 
  CHANGE COLUMN policy_title title varchar(255) NOT NULL COMMENT '政策标题',
  CHANGE COLUMN policy_category category enum('tax_law','tax_policy','tax_notice','tax_guidance') NOT NULL COMMENT '政策分类',
  CHANGE COLUMN policy_status status enum('draft','effective','expired','revoked') DEFAULT 'draft' COMMENT '状态',
  CHANGE COLUMN policy_keywords keywords varchar(500) COMMENT '关键词';

-- 税务稽查表字段重命名  
ALTER TABLE tax_inspections 
  CHANGE COLUMN inspection_status status enum('planned','ongoing','suspended','completed','closed') DEFAULT 'planned' COMMENT '稽查状态';

-- 税务风险评估表字段重命名
ALTER TABLE tax_risk_assessments 
  CHANGE COLUMN assessment_status status enum('draft','completed','reviewed','archived') DEFAULT 'draft' COMMENT '状态';

-- 税务缴费表字段重命名
ALTER TABLE tax_payments 
  CHANGE COLUMN payment_status status enum('pending','completed','failed','cancelled','refunded') DEFAULT 'pending' COMMENT '缴款状态';
```

## 税务申报系统集成 (2025-07-17)

### 新增表结构

#### tax_filing_submissions 表
- 存储税务申报的完整信息
- 包含企业信息、申报数据、状态跟踪等
- 支持单个和批量申报
- 包含重试机制和错误处理

#### tax_filing_status_history 表
- 记录申报状态的所有变更
- 便于追踪申报流程和问题排查
- 包含操作人、时间、原因等详细信息

#### tax_filing_provinces 表
- 存储各省税务局的配置信息
- 包含认证方式、API端点、数据映射等
- 支持健康检查和统计信息

#### tax_filing_batches 表
- 管理批量申报任务
- 跟踪批次进度和统计信息

#### tax_filing_callbacks 表
- 处理申报状态变更的回调通知
- 支持重试机制和错误处理

### 系统配置项
- 添加了税务申报服务相关的系统配置
- 包含服务URL、API密钥、超时设置等

## 完成状态

✅ **已完成** - 所有表结构已与Go模型定义保持一致，可以正常使用GORM进行数据库操作。
✅ **税务申报系统** - 新增税务申报相关表结构，支持完整的申报流程管理。
