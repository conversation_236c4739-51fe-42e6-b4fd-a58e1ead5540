#!/bin/bash

# 迁移handler到controller的脚本
# 将api目录中的handler文件迁移到controller目录，并重命名为controller

echo "开始迁移handler文件到controller目录..."

# 定义文件映射
declare -A file_mapping=(
    ["audit_log_handler.go"]="audit_log_controller.go"
    ["declaration_handler.go"]="declaration_controller.go"
    ["declaration_item_handler.go"]="declaration_item_controller.go"
    ["invoice_handler.go"]="invoice_controller.go"
    ["invoice_item_handler.go"]="invoice_item_controller.go"
    ["notification_handler.go"]="notification_controller.go"
    ["report_handler.go"]="report_controller.go"
    ["role_permission_handler.go"]="role_permission_controller.go"
    ["tax_policy_handler.go"]="tax_policy_controller.go"
    ["tax_rule_handler.go"]="tax_rule_controller.go"
    ["tax_type_handler.go"]="tax_type_controller.go"
    ["upload_handler.go"]="upload_controller.go"
    ["user_validation_handler.go"]="user_validation_controller.go"
)

# 迁移文件
for handler_file in "${!file_mapping[@]}"; do
    controller_file="${file_mapping[$handler_file]}"
    
    if [ -f "api/$handler_file" ]; then
        echo "迁移 $handler_file -> $controller_file"
        
        # 复制文件到controller目录
        cp "api/$handler_file" "controller/$controller_file"
        
        # 修改包名
        sed -i '' 's/package api/package controller/g' "controller/$controller_file"
        
        # 修改Handler为Controller
        sed -i '' 's/Handler/Controller/g' "controller/$controller_file"
        sed -i '' 's/NewAuth/NewAuth/g' "controller/$controller_file"
        sed -i '' 's/NewEnterprise/NewEnterprise/g' "controller/$controller_file"
        
        echo "完成迁移: $controller_file"
    else
        echo "警告: 文件不存在 api/$handler_file"
    fi
done

echo "Handler迁移完成！"
echo "请手动检查并修复可能的编译错误。"
