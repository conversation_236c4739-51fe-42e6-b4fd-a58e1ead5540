// Package service provides business logic for the tax management system.
// It includes services for declaration item management with CRUD operations.
package service

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// DeclarationItemService defines the interface for declaration item operations
type DeclarationItemService interface {
	CreateDeclarationItem(ctx context.Context, req CreateDeclarationItemRequest) (*model.DeclarationItem, error)
	GetDeclarationItemByID(ctx context.Context, id string) (*model.DeclarationItem, error)
	GetDeclarationItemsByDeclarationID(ctx context.Context, declarationID string) ([]model.DeclarationItem, error)
	UpdateDeclarationItem(ctx context.Context, id string, req UpdateDeclarationItemRequest) (*model.DeclarationItem, error)
	DeleteDeclarationItem(ctx context.Context, id string) error
	BatchCreateDeclarationItems(ctx context.Context, declarationID string, items []CreateDeclarationItemRequest) ([]model.DeclarationItem, error)
	BatchUpdateDeclarationItems(ctx context.Context, declarationID string, items []UpdateDeclarationItemRequest) ([]model.DeclarationItem, error)
	BatchDeleteDeclarationItems(ctx context.Context, itemIDs []string) error
	CalculateDeclarationTotals(ctx context.Context, declarationID string) (*DeclarationTotalsResponse, error)
}

type declarationItemService struct {
	db *gorm.DB
}

// NewDeclarationItemService creates a new declaration item service instance
func NewDeclarationItemService(db *gorm.DB) DeclarationItemService {
	return &declarationItemService{db: db}
}

// CreateDeclarationItemRequest 创建申报项请求
type CreateDeclarationItemRequest struct {
	DeclarationID string  `json:"declaration_id" binding:"required"`
	TaxTypeID     *string `json:"tax_type_id"`
	ItemType      string  `json:"item_type" binding:"required"`
	ItemCode      string  `json:"item_code"`
	ItemName      string  `json:"item_name" binding:"required"`
	Description   string  `json:"description"`
	Amount        float64 `json:"amount" binding:"required"`
	TaxableAmount float64 `json:"taxable_amount"`
	TaxRate       float64 `json:"tax_rate"`
	Category      string  `json:"category"`
	SubCategory   string  `json:"sub_category"`
	LineNumber    int     `json:"line_number"`
	IsRequired    bool    `json:"is_required"`
	IsCalculated  bool    `json:"is_calculated"`
	Formula       string  `json:"formula"`
	Remarks       string  `json:"remarks"`
}

// UpdateDeclarationItemRequest 更新申报项请求
type UpdateDeclarationItemRequest struct {
	ID            string  `json:"id" binding:"required"`
	TaxTypeID     *string `json:"tax_type_id"`
	ItemType      string  `json:"item_type" binding:"required"`
	ItemCode      string  `json:"item_code"`
	ItemName      string  `json:"item_name" binding:"required"`
	Description   string  `json:"description"`
	Amount        float64 `json:"amount" binding:"required"`
	TaxableAmount float64 `json:"taxable_amount"`
	TaxRate       float64 `json:"tax_rate"`
	Category      string  `json:"category"`
	SubCategory   string  `json:"sub_category"`
	LineNumber    int     `json:"line_number"`
	IsRequired    bool    `json:"is_required"`
	IsCalculated  bool    `json:"is_calculated"`
	Formula       string  `json:"formula"`
	Remarks       string  `json:"remarks"`
}

// DeclarationTotalsResponse 申报汇总响应
type DeclarationTotalsResponse struct {
	DeclarationID    string  `json:"declaration_id"`
	TotalAmount      float64 `json:"total_amount"`
	TotalTaxAmount   float64 `json:"total_tax_amount"`
	TotalDeduction   float64 `json:"total_deduction"`
	TotalCredit      float64 `json:"total_credit"`
	TotalPayment     float64 `json:"total_payment"`
	NetTaxDue        float64 `json:"net_tax_due"`
	ItemCount        int     `json:"item_count"`
	RequiredItemsSet bool    `json:"required_items_set"`
}

// CreateDeclarationItem 创建申报项
func (s *declarationItemService) CreateDeclarationItem(ctx context.Context, req CreateDeclarationItemRequest) (*model.DeclarationItem, error) {
	// 验证申报是否存在
	var declaration model.Declaration
	if err := s.db.WithContext(ctx).First(&declaration, "id = ?", req.DeclarationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrDeclarationNotFound
		}
		return nil, fmt.Errorf("查询申报失败: %w", err)
	}

	// 创建申报项
	item := &model.DeclarationItem{
		ID:            util.GenerateID(),
		DeclarationID: req.DeclarationID,
		TaxTypeID:     req.TaxTypeID,
		ItemType:      req.ItemType,
		ItemCode:      req.ItemCode,
		ItemName:      req.ItemName,
		Description:   req.Description,
		Amount:        util.DecimalFromFloat(req.Amount),
		TaxableAmount: util.DecimalFromFloat(req.TaxableAmount),
		TaxRate:       util.DecimalFromFloat(req.TaxRate),
		Category:      req.Category,
		SubCategory:   req.SubCategory,
		LineNumber:    req.LineNumber,
		IsRequired:    req.IsRequired,
		IsCalculated:  req.IsCalculated,
		Formula:       req.Formula,
		Remarks:       req.Remarks,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// 计算税额
	item.CalculateTaxAmount()

	// 验证数据
	if err := item.Validate(); err != nil {
		return nil, err
	}

	// 保存到数据库
	if err := s.db.WithContext(ctx).Create(item).Error; err != nil {
		return nil, fmt.Errorf("创建申报项失败: %w", err)
	}

	return item, nil
}

// GetDeclarationItemByID 根据ID获取申报项
func (s *declarationItemService) GetDeclarationItemByID(ctx context.Context, id string) (*model.DeclarationItem, error) {
	var item model.DeclarationItem
	if err := s.db.WithContext(ctx).Preload("Declaration").Preload("TaxType").First(&item, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrDeclarationItemNotFound
		}
		return nil, fmt.Errorf("查询申报项失败: %w", err)
	}

	return &item, nil
}

// GetDeclarationItemsByDeclarationID 根据申报ID获取所有申报项
func (s *declarationItemService) GetDeclarationItemsByDeclarationID(ctx context.Context, declarationID string) ([]model.DeclarationItem, error) {
	var items []model.DeclarationItem
	if err := s.db.WithContext(ctx).
		Preload("TaxType").
		Where("declaration_id = ?", declarationID).
		Order("line_number ASC, created_at ASC").
		Find(&items).Error; err != nil {
		return nil, fmt.Errorf("查询申报项失败: %w", err)
	}

	return items, nil
}

// UpdateDeclarationItem 更新申报项
func (s *declarationItemService) UpdateDeclarationItem(ctx context.Context, id string, req UpdateDeclarationItemRequest) (*model.DeclarationItem, error) {
	// 查询现有记录
	var item model.DeclarationItem
	if err := s.db.WithContext(ctx).First(&item, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrDeclarationItemNotFound
		}
		return nil, fmt.Errorf("查询申报项失败: %w", err)
	}

	// 更新字段
	item.TaxTypeID = req.TaxTypeID
	item.ItemType = req.ItemType
	item.ItemCode = req.ItemCode
	item.ItemName = req.ItemName
	item.Description = req.Description
	item.Amount = util.DecimalFromFloat(req.Amount)
	item.TaxableAmount = util.DecimalFromFloat(req.TaxableAmount)
	item.TaxRate = util.DecimalFromFloat(req.TaxRate)
	item.Category = req.Category
	item.SubCategory = req.SubCategory
	item.LineNumber = req.LineNumber
	item.IsRequired = req.IsRequired
	item.IsCalculated = req.IsCalculated
	item.Formula = req.Formula
	item.Remarks = req.Remarks
	item.UpdatedAt = time.Now()

	// 重新计算税额
	item.CalculateTaxAmount()

	// 验证数据
	if err := item.Validate(); err != nil {
		return nil, err
	}

	// 保存更新
	if err := s.db.WithContext(ctx).Save(&item).Error; err != nil {
		return nil, fmt.Errorf("更新申报项失败: %w", err)
	}

	return &item, nil
}

// DeleteDeclarationItem 删除申报项
func (s *declarationItemService) DeleteDeclarationItem(ctx context.Context, id string) error {
	result := s.db.WithContext(ctx).Delete(&model.DeclarationItem{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("删除申报项失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return util.ErrDeclarationItemNotFound
	}
	return nil
}

// BatchCreateDeclarationItems 批量创建申报项
func (s *declarationItemService) BatchCreateDeclarationItems(ctx context.Context, declarationID string, items []CreateDeclarationItemRequest) ([]model.DeclarationItem, error) {
	// 验证申报是否存在
	var declaration model.Declaration
	if err := s.db.WithContext(ctx).First(&declaration, "id = ?", declarationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrDeclarationNotFound
		}
		return nil, fmt.Errorf("查询申报失败: %w", err)
	}

	var declarationItems []model.DeclarationItem
	for _, req := range items {
		item := model.DeclarationItem{
			ID:            util.GenerateID(),
			DeclarationID: declarationID,
			TaxTypeID:     req.TaxTypeID,
			ItemType:      req.ItemType,
			ItemCode:      req.ItemCode,
			ItemName:      req.ItemName,
			Description:   req.Description,
			Amount:        util.DecimalFromFloat(req.Amount),
			TaxableAmount: util.DecimalFromFloat(req.TaxableAmount),
			TaxRate:       util.DecimalFromFloat(req.TaxRate),
			Category:      req.Category,
			SubCategory:   req.SubCategory,
			LineNumber:    req.LineNumber,
			IsRequired:    req.IsRequired,
			IsCalculated:  req.IsCalculated,
			Formula:       req.Formula,
			Remarks:       req.Remarks,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}

		// 计算税额
		item.CalculateTaxAmount()

		// 验证数据
		if err := item.Validate(); err != nil {
			return nil, fmt.Errorf("验证申报项失败 [%s]: %w", item.ItemName, err)
		}

		declarationItems = append(declarationItems, item)
	}

	// 批量插入
	if err := s.db.WithContext(ctx).Create(&declarationItems).Error; err != nil {
		return nil, fmt.Errorf("批量创建申报项失败: %w", err)
	}

	return declarationItems, nil
}

// BatchUpdateDeclarationItems 批量更新申报项
func (s *declarationItemService) BatchUpdateDeclarationItems(ctx context.Context, declarationID string, items []UpdateDeclarationItemRequest) ([]model.DeclarationItem, error) {
	// 验证申报是否存在
	var declaration model.Declaration
	if err := s.db.WithContext(ctx).First(&declaration, "id = ?", declarationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrDeclarationNotFound
		}
		return nil, fmt.Errorf("查询申报失败: %w", err)
	}

	var updatedItems []model.DeclarationItem
	for _, req := range items {
		// 查询现有记录
		var item model.DeclarationItem
		if err := s.db.WithContext(ctx).First(&item, "id = ? AND declaration_id = ?", req.ID, declarationID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, fmt.Errorf("申报项不存在: %s", req.ID)
			}
			return nil, fmt.Errorf("查询申报项失败: %w", err)
		}

		// 更新字段
		item.TaxTypeID = req.TaxTypeID
		item.ItemType = req.ItemType
		item.ItemCode = req.ItemCode
		item.ItemName = req.ItemName
		item.Description = req.Description
		item.Amount = util.DecimalFromFloat(req.Amount)
		item.TaxableAmount = util.DecimalFromFloat(req.TaxableAmount)
		item.TaxRate = util.DecimalFromFloat(req.TaxRate)
		item.Category = req.Category
		item.SubCategory = req.SubCategory
		item.LineNumber = req.LineNumber
		item.IsRequired = req.IsRequired
		item.IsCalculated = req.IsCalculated
		item.Formula = req.Formula
		item.Remarks = req.Remarks
		item.UpdatedAt = time.Now()

		// 重新计算税额
		item.CalculateTaxAmount()

		// 验证数据
		if err := item.Validate(); err != nil {
			return nil, fmt.Errorf("验证申报项失败 [%s]: %w", item.ItemName, err)
		}

		updatedItems = append(updatedItems, item)
	}

	// 批量更新
	for _, item := range updatedItems {
		if err := s.db.WithContext(ctx).Save(&item).Error; err != nil {
			return nil, fmt.Errorf("批量更新申报项失败: %w", err)
		}
	}

	return updatedItems, nil
}

// BatchDeleteDeclarationItems 批量删除申报项
func (s *declarationItemService) BatchDeleteDeclarationItems(ctx context.Context, itemIDs []string) error {
	if len(itemIDs) == 0 {
		return nil
	}

	result := s.db.WithContext(ctx).Delete(&model.DeclarationItem{}, "id IN ?", itemIDs)
	if result.Error != nil {
		return fmt.Errorf("批量删除申报项失败: %w", result.Error)
	}

	return nil
}

// CalculateDeclarationTotals 计算申报汇总
func (s *declarationItemService) CalculateDeclarationTotals(ctx context.Context, declarationID string) (*DeclarationTotalsResponse, error) {
	items, err := s.GetDeclarationItemsByDeclarationID(ctx, declarationID)
	if err != nil {
		return nil, err
	}

	totals := &DeclarationTotalsResponse{
		DeclarationID:    declarationID,
		ItemCount:        len(items),
		RequiredItemsSet: true,
	}

	for _, item := range items {
		amount, _ := item.Amount.Float64()
		taxAmount, _ := item.TaxAmount.Float64()

		totals.TotalAmount += amount
		totals.TotalTaxAmount += taxAmount

		switch item.ItemType {
		case model.DeclarationItemTypeDeduction:
			totals.TotalDeduction += amount
		case model.DeclarationItemTypeCredit:
			totals.TotalCredit += amount
		case model.DeclarationItemTypePayment:
			totals.TotalPayment += amount
		}

		// 检查必填项是否已设置
		if item.IsRequired && item.Amount.IsZero() {
			totals.RequiredItemsSet = false
		}
	}

	// 计算应纳税额
	totals.NetTaxDue = totals.TotalTaxAmount - totals.TotalCredit - totals.TotalPayment

	return totals, nil
}
