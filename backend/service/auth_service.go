// Package service provides business logic layer for the tax management system.
// It implements authentication, enterprise management, invoice processing, and other core business operations.
package service

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"backend/config"
	"backend/model"
	"backend/util"
)

// AuthService provides authentication and authorization services including
// user login, registration, token management, and password operations.
type AuthService struct {
	db           *gorm.DB
	config       *config.Config
	emailService EmailService
}

// getStringValue 辅助函数：从字符串指针获取字符串值，nil时返回空字符串
func getStringValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// NewAuthService creates a new authentication service with the provided database and configuration.
// It returns a configured AuthService instance ready to handle authentication operations.
func NewAuthService(db *gorm.DB, cfg *config.Config) *AuthService {
	emailService := NewEmailService(*cfg)

	return &AuthService{
		db:           db,
		config:       cfg,
		emailService: emailService,
	}
}

// Login authenticates a user and returns access and refresh tokens
func (s *AuthService) Login(ctx context.Context, req model.LoginRequest) (*model.AuthResponse, error) {
	// Find user by phone
	var user model.User
	err := s.db.Where("phone = ?", req.Phone).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, util.ErrInvalidCredentials
		}
		return nil, err
	}

	// Check if user is active
	if !user.IsActive {
		return nil, util.ErrUserInactive
	}

	// Verify password
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password))
	if err != nil {
		return nil, util.ErrInvalidCredentials
	}

	// Generate tokens
	accessToken, accessExpiresAt, err := s.generateAccessToken(&user)
	if err != nil {
		return nil, err
	}

	refreshToken, refreshExpiresAt, err := s.generateRefreshToken(&user)
	if err != nil {
		return nil, err
	}

	// 构建用户响应，确保字段映射正确
	userResponse := gin.H{
		"id":                 user.ID,
		"user_name":          user.UserName,
		"email":              user.Email,
		"phone":              user.Phone,
		"department":         user.Department,
		"position":           user.Position,
		"avatar":             user.Avatar,
		"is_active":          user.IsActive,
		"email_verified":     user.EmailVerified,
		"phone_verified":     user.PhoneVerified,
		"preferred_lang":     user.PreferredLang,
		"two_factor_enabled": user.TwoFactorEnabled,
		"last_login_at":      user.LastLoginAt,
		"last_login_ip":      user.LastLoginIP,
		"created_at":         user.CreatedAt,
		"updated_at":         user.UpdatedAt,
	}

	return &model.AuthResponse{
		User:                  userResponse,
		AccessToken:           accessToken,
		RefreshToken:          refreshToken,
		AccessTokenExpiresAt:  accessExpiresAt,
		RefreshTokenExpiresAt: refreshExpiresAt,
	}, nil
}

// Register creates a new user account
func (s *AuthService) Register(ctx context.Context, req model.RegisterRequest) (*model.AuthResponse, error) {
	// Check if phone already exists
	var existingUser model.User
	err := s.db.Where("phone = ?", req.Phone).First(&existingUser).Error
	if err == nil {
		return nil, util.ErrUserExists
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// Check if email already exists (if provided)
	if req.Email != "" {
		err := s.db.Where("email = ?", req.Email).First(&existingUser).Error
		if err == nil {
			return nil, errors.New("邮箱已存在")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// Create new user
	preferredLanguage := "zh-CN"

	// Generate user_name from email prefix or use provided name
	userName := req.Name
	if userName == "" {
		// If no name provided, generate from email prefix
		if emailParts := strings.Split(req.Email, "@"); len(emailParts) > 0 {
			userName = emailParts[0]
		} else {
			userName = "user_" + util.GenerateID()[:8]
		}
	}

	// 处理邮箱字段 - 如果为空字符串则设为NULL以避免唯一约束冲突
	var email *string
	if req.Email != "" {
		email = &req.Email
	}

	newUser := model.User{
		ID:            util.GenerateID(),
		UserName:      userName,
		Email:         email, // 使用指针类型，空字符串时为nil
		PasswordHash:  string(hashedPassword),
		Phone:         req.Phone,
		IsActive:      true,
		PreferredLang: preferredLanguage,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// Save user to database
	err = s.db.Create(&newUser).Error
	if err != nil {
		return nil, err
	}

	// Generate tokens
	accessToken, accessExpiresAt, err := s.generateAccessToken(&newUser)
	if err != nil {
		return nil, err
	}

	refreshToken, refreshExpiresAt, err := s.generateRefreshToken(&newUser)
	if err != nil {
		return nil, err
	}

	// 构建用户响应，确保字段映射正确
	userResponse := gin.H{
		"id":                 newUser.ID,
		"user_name":          newUser.UserName,
		"email":              newUser.Email,
		"phone":              newUser.Phone,
		"department":         newUser.Department,
		"position":           newUser.Position,
		"avatar":             newUser.Avatar,
		"is_active":          newUser.IsActive,
		"email_verified":     newUser.EmailVerified,
		"phone_verified":     newUser.PhoneVerified,
		"preferred_lang":     newUser.PreferredLang,
		"two_factor_enabled": newUser.TwoFactorEnabled,
		"last_login_at":      newUser.LastLoginAt,
		"last_login_ip":      newUser.LastLoginIP,
		"created_at":         newUser.CreatedAt,
		"updated_at":         newUser.UpdatedAt,
	}

	return &model.AuthResponse{
		User:                  userResponse,
		AccessToken:           accessToken,
		RefreshToken:          refreshToken,
		AccessTokenExpiresAt:  accessExpiresAt,
		RefreshTokenExpiresAt: refreshExpiresAt,
	}, nil
}

// VerifyAccessToken verifies an access token and returns the claims
func (s *AuthService) VerifyAccessToken(ctx context.Context, accessToken string) (jwt.MapClaims, error) {
	return s.verifyToken(accessToken, model.TokenTypeAccess)
}

// RefreshToken refreshes an access token using a refresh token
func (s *AuthService) RefreshToken(ctx context.Context, refreshToken string) (*model.AuthResponse, error) {
	// Verify refresh token
	claims, err := s.verifyToken(refreshToken, model.TokenTypeRefresh)
	if err != nil {
		return nil, err
	}

	// Get user from database
	userID, ok := claims["id"].(string)
	if !ok {
		return nil, util.ErrInvalidToken
	}

	var user model.User
	err = s.db.Where("id = ?", userID).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, util.ErrUserNotFound
		}
		return nil, err
	}

	// Check if user is still active
	if !user.IsActive {
		return nil, util.ErrUserInactive
	}

	// Generate new tokens
	accessToken, accessExpiresAt, err := s.generateAccessToken(&user)
	if err != nil {
		return nil, err
	}

	newRefreshToken, refreshExpiresAt, err := s.generateRefreshToken(&user)
	if err != nil {
		return nil, err
	}

	return &model.AuthResponse{
		User: model.UserResponse{
			ID:          user.ID,
			UserName:    user.UserName,
			Email:       getStringValue(user.Email),
			PhoneNumber: &user.Phone,
			FullName:    user.UserName,
			AvatarURL:   &user.Avatar,
			RoleID:      "",
			Status:      "active",
			CreatedAt:   user.CreatedAt,
		},
		AccessToken:           accessToken,
		RefreshToken:          newRefreshToken,
		AccessTokenExpiresAt:  accessExpiresAt,
		RefreshTokenExpiresAt: refreshExpiresAt,
	}, nil
}

// GetUserByID retrieves a user by ID
func (s *AuthService) GetUserByID(ctx context.Context, userID string) (*model.User, error) {
	var user model.User
	err := s.db.WithContext(ctx).
		Preload("Enterprise").
		Where("id = ?", userID).
		First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, util.ErrUserNotFound
		}
		return nil, err
	}
	return &user, nil
}

// ChangePassword changes a user's password
func (s *AuthService) ChangePassword(ctx context.Context, userID string, req model.ChangePasswordRequest) error {
	// Get user
	user, err := s.GetUserByID(ctx, userID)
	if err != nil {
		return err
	}

	// Verify old password
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.OldPassword))
	if err != nil {
		return util.ErrInvalidCredentials
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// Update password
	return s.db.Model(user).Update("password_hash", string(hashedPassword)).Error
}

// UpdateProfile updates a user's profile information
func (s *AuthService) UpdateProfile(ctx context.Context, userID string, req model.UpdateProfileRequest) (*model.User, error) {
	// Get user
	user, err := s.GetUserByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Update fields
	user.UpdatedAt = time.Now()

	// 处理用户名更新（支持新旧字段名）
	if req.UserName != "" {
		user.UserName = req.UserName
	} else if req.Name != "" {
		user.UserName = req.Name
	}

	// 更新其他字段
	if req.Phone != "" {
		user.Phone = req.Phone
	}
	if req.Department != "" {
		user.Department = req.Department
	}
	if req.Position != "" {
		user.Position = req.Position
	}
	if req.PreferredLang != "" {
		user.PreferredLang = req.PreferredLang
	}
	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}
	if req.TwoFactorEnabled != nil {
		user.TwoFactorEnabled = *req.TwoFactorEnabled
	}

	// Update user
	err = s.db.Save(user).Error
	if err != nil {
		return nil, err
	}

	// Return updated user
	return s.GetUserByID(ctx, userID)
}

// generateAccessToken generates a new access token for a user
func (s *AuthService) generateAccessToken(user *model.User) (string, time.Time, error) {
	expiresAt := time.Now().Add(time.Duration(s.config.Auth.AccessTokenDuration) * time.Minute)
	claims := jwt.MapClaims{
		"id":        user.ID,
		"email":     user.Email,
		"full_name": user.UserName,
		"role_id":   "",
		"type":      model.TokenTypeAccess,
		"exp":       expiresAt.Unix(),
		"iat":       time.Now().Unix(),
		"iss":       "tax-system",
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(s.config.Auth.JWTSecret))
	if err != nil {
		return "", time.Time{}, err
	}

	return tokenString, expiresAt, nil
}

// generateRefreshToken generates a new refresh token for a user
func (s *AuthService) generateRefreshToken(user *model.User) (string, time.Time, error) {
	expiresAt := time.Now().Add(time.Duration(s.config.Auth.RefreshTokenDuration) * 24 * time.Hour)
	claims := jwt.MapClaims{
		"id":   user.ID,
		"type": model.TokenTypeRefresh,
		"exp":  expiresAt.Unix(),
		"iat":  time.Now().Unix(),
		"iss":  "tax-system",
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(s.config.Auth.JWTSecret))
	if err != nil {
		return "", time.Time{}, err
	}

	return tokenString, expiresAt, nil
}

// verifyToken verifies a JWT token and returns the claims
func (s *AuthService) verifyToken(tokenString string, tokenType string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, util.ErrInvalidToken
		}
		return []byte(s.config.Auth.JWTSecret), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, util.ErrInvalidToken
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, util.ErrInvalidToken
	}

	// Check token type
	if claimType, ok := claims["type"].(string); !ok || claimType != tokenType {
		return nil, util.ErrInvalidToken
	}

	// Check expiration
	if exp, ok := claims["exp"].(float64); ok {
		if time.Now().Unix() > int64(exp) {
			return nil, util.ErrTokenExpired
		}
	}

	return claims, nil
}

// ForgotPassword initiates the password reset process by generating a reset token
func (s *AuthService) ForgotPassword(ctx context.Context, req model.ForgotPasswordRequest) error {
	// Validate that either email or phone is provided
	if req.Email == "" && req.Phone == "" {
		return errors.New("邮箱或手机号必须提供其中一个")
	}

	// Find user by email or phone
	var user model.User
	var err error

	if req.Email != "" {
		err = s.db.WithContext(ctx).Where("email = ?", req.Email).First(&user).Error
	} else {
		err = s.db.WithContext(ctx).Where("phone = ?", req.Phone).First(&user).Error
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Don't reveal whether user exists or not for security
			return nil
		}
		return err
	}

	// Check if user is active
	if !user.IsActive {
		return errors.New("账户已被禁用")
	}

	// Generate reset token
	token, err := s.generateResetToken()
	if err != nil {
		return err
	}

	// Set token expiry (24 hours from now)
	expiry := time.Now().Add(24 * time.Hour)

	// Update user with reset token and expiry
	err = s.db.WithContext(ctx).Model(&user).Updates(map[string]interface{}{
		"reset_token":  token,
		"reset_expiry": expiry,
	}).Error

	if err != nil {
		return err
	}

	// Send password reset email if user has email
	if user.Email != nil && *user.Email != "" {
		err = s.emailService.SendPasswordResetEmail(ctx, *user.Email, token, user.UserName)
		if err != nil {
			// Log the error but don't fail the request for security reasons
			// In production, you might want to log this to a monitoring system
			// log.Printf("Failed to send password reset email to %s: %v", user.Email, err)
		}
	}

	// TODO: Send SMS if user provided phone number and no email
	// This would require implementing SMS service integration

	return nil
}

// ResetPassword resets the user's password using a valid reset token
func (s *AuthService) ResetPassword(ctx context.Context, req model.ResetPasswordRequest) error {
	// Find user by reset token
	var user model.User
	err := s.db.WithContext(ctx).Where("reset_token = ? AND reset_expiry > ?", req.Token, time.Now()).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("无效或已过期的重置令牌")
		}
		return err
	}

	// Check if user is active
	if !user.IsActive {
		return errors.New("账户已被禁用")
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// Update password and clear reset token
	now := time.Now()
	err = s.db.WithContext(ctx).Model(&user).Updates(map[string]interface{}{
		"password_hash":       string(hashedPassword),
		"reset_token":         nil,
		"reset_expiry":        nil,
		"password_changed_at": now,
		"updated_at":          now,
	}).Error

	return err
}

// generateResetToken generates a secure random token for password reset
func (s *AuthService) generateResetToken() (string, error) {
	bytes := make([]byte, 32)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
