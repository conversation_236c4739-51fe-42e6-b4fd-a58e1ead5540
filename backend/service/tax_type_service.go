package service

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// TaxTypeService 税种服务接口
type TaxTypeService interface {
	CreateTaxType(ctx context.Context, req *model.TaxTypeCreateRequest) (*model.TaxType, error)
	GetTaxTypes(ctx context.Context, req *model.TaxTypeQuery) (*PaginatedResponse, error)
	GetTaxTypeByID(ctx context.Context, id string) (*model.TaxType, error)
	GetTaxTypeByCode(ctx context.Context, code string) (*model.TaxType, error)
	UpdateTaxType(ctx context.Context, id string, req *model.TaxTypeUpdateRequest) (*model.TaxType, error)
	DeleteTaxType(ctx context.Context, id string) error
	GetTaxTypeOptions(ctx context.Context) ([]*model.TaxTypeOption, error)
	GetTaxTypeStats(ctx context.Context) (*model.TaxTypeStats, error)
}

type taxTypeService struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewTaxTypeService 创建税种服务
func NewTaxTypeService(db *gorm.DB, logger *zap.Logger) TaxTypeService {
	return &taxTypeService{
		db:     db,
		logger: logger,
	}
}

// CreateTaxType 创建税种
func (s *taxTypeService) CreateTaxType(ctx context.Context, req *model.TaxTypeCreateRequest) (*model.TaxType, error) {
	// 检查税种编码是否已存在
	var existingTaxType model.TaxType
	if err := s.db.WithContext(ctx).Where("code = ?", req.Code).First(&existingTaxType).Error; err == nil {
		return nil, fmt.Errorf("税种编码 %s 已存在", req.Code)
	} else if err != gorm.ErrRecordNotFound {
		s.logger.Error("检查税种编码失败", zap.Error(err))
		return nil, err
	}

	taxType := &model.TaxType{
		ID:          util.GenerateID(),
		Code:        req.Code,
		Name:        req.Name,
		Category:    req.Category,
		Description: req.Description,
		IsActive:    true,
	}

	if err := s.db.WithContext(ctx).Create(taxType).Error; err != nil {
		s.logger.Error("创建税种失败", zap.Error(err))
		return nil, err
	}

	s.logger.Info("税种创建成功", zap.String("id", taxType.ID), zap.String("code", taxType.Code))
	return taxType, nil
}

// GetTaxTypes 获取税种列表
func (s *taxTypeService) GetTaxTypes(ctx context.Context, req *model.TaxTypeQuery) (*PaginatedResponse, error) {
	var taxTypes []model.TaxType
	var total int64

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	query := s.db.WithContext(ctx).Model(&model.TaxType{})

	// 添加过滤条件
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("name LIKE ? OR code LIKE ? OR description LIKE ?", keyword, keyword, keyword)
	}
	if req.Category != "" {
		query = query.Where("category = ?", req.Category)
	}
	if req.IsActive != nil {
		query = query.Where("is_active = ?", *req.IsActive)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		s.logger.Error("获取税种总数失败", zap.Error(err))
		return nil, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&taxTypes).Error; err != nil {
		s.logger.Error("获取税种列表失败", zap.Error(err))
		return nil, err
	}

	// 计算总页数
	pages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &PaginatedResponse{
		Items:    taxTypes,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Pages:    pages,
	}, nil
}

// GetTaxTypeByID 根据ID获取税种
func (s *taxTypeService) GetTaxTypeByID(ctx context.Context, id string) (*model.TaxType, error) {
	var taxType model.TaxType
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&taxType).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("税种不存在")
		}
		s.logger.Error("获取税种失败", zap.Error(err))
		return nil, err
	}

	return &taxType, nil
}

// GetTaxTypeByCode 根据编码获取税种
func (s *taxTypeService) GetTaxTypeByCode(ctx context.Context, code string) (*model.TaxType, error) {
	var taxType model.TaxType
	if err := s.db.WithContext(ctx).Where("code = ?", code).First(&taxType).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("税种不存在")
		}
		s.logger.Error("获取税种失败", zap.Error(err))
		return nil, err
	}

	return &taxType, nil
}

// UpdateTaxType 更新税种
func (s *taxTypeService) UpdateTaxType(ctx context.Context, id string, req *model.TaxTypeUpdateRequest) (*model.TaxType, error) {
	var taxType model.TaxType
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&taxType).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("税种不存在")
		}
		s.logger.Error("获取税种失败", zap.Error(err))
		return nil, err
	}

	// 更新字段
	if req.Name != "" {
		taxType.Name = req.Name
	}
	if req.Category != "" {
		taxType.Category = req.Category
	}
	if req.Description != "" {
		taxType.Description = req.Description
	}
	if req.IsActive != nil {
		taxType.IsActive = *req.IsActive
	}

	if err := s.db.WithContext(ctx).Save(&taxType).Error; err != nil {
		s.logger.Error("更新税种失败", zap.Error(err))
		return nil, err
	}

	s.logger.Info("税种更新成功", zap.String("id", taxType.ID))
	return &taxType, nil
}

// DeleteTaxType 删除税种
func (s *taxTypeService) DeleteTaxType(ctx context.Context, id string) error {
	result := s.db.WithContext(ctx).Where("id = ?", id).Delete(&model.TaxType{})
	if result.Error != nil {
		s.logger.Error("删除税种失败", zap.Error(result.Error))
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("税种不存在")
	}

	s.logger.Info("税种删除成功", zap.String("id", id))
	return nil
}

// GetTaxTypeOptions 获取税种选项（用于下拉选择）
func (s *taxTypeService) GetTaxTypeOptions(ctx context.Context) ([]*model.TaxTypeOption, error) {
	var taxTypes []model.TaxType
	if err := s.db.WithContext(ctx).Where("is_active = ?", true).Order("name ASC").Find(&taxTypes).Error; err != nil {
		s.logger.Error("获取税种选项失败", zap.Error(err))
		return nil, err
	}

	options := make([]*model.TaxTypeOption, len(taxTypes))
	for i, taxType := range taxTypes {
		options[i] = &model.TaxTypeOption{
			Value: taxType.Code,
			Label: taxType.Name,
		}
	}

	return options, nil
}

// GetTaxTypeStats 获取税种统计
func (s *taxTypeService) GetTaxTypeStats(ctx context.Context) (*model.TaxTypeStats, error) {
	var stats model.TaxTypeStats

	// 总数
	if err := s.db.WithContext(ctx).Model(&model.TaxType{}).Count(&stats.Total).Error; err != nil {
		s.logger.Error("获取税种总数失败", zap.Error(err))
		return nil, err
	}

	// 启用数量
	if err := s.db.WithContext(ctx).Model(&model.TaxType{}).Where("is_active = ?", true).Count(&stats.Active).Error; err != nil {
		s.logger.Error("获取启用税种数量失败", zap.Error(err))
		return nil, err
	}

	// 禁用数量
	stats.Inactive = stats.Total - stats.Active

	return &stats, nil
}
