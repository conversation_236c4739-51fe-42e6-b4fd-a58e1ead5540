package service

import (
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"time"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	ocr20191230 "github.com/alibabacloud-go/ocr-20191230/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/shopspring/decimal"

	"backend/config"
)

// OCRService 定义OCR服务接口
type OCRService interface {
	// RecognizeInvoice 识别发票
	RecognizeInvoice(ctx context.Context, file multipart.File, filename string, settings OCRSettings) (*OCRResult, error)
	// RecognizeMixedInvoices 识别混贴发票
	RecognizeMixedInvoices(ctx context.Context, file multipart.File, filename string, settings OCRSettings) (*OCRResult, error)
}

// OCRSettings OCR识别设置
type OCRSettings struct {
	InvoiceType  string `json:"invoiceType"`  // 发票类型
	Accuracy     string `json:"accuracy"`     // 识别精度: fast, accurate
	AutoCorrect  bool   `json:"autoCorrect"`  // 自动纠错
	ExtractItems bool   `json:"extractItems"` // 提取商品明细
}

// OCRResult OCR识别结果
type OCRResult struct {
	// 基本信息
	InvoiceNumber    string          `json:"invoiceNumber"`    // 发票号码
	InvoiceCode      string          `json:"invoiceCode"`      // 发票代码
	InvoiceType      string          `json:"invoiceType"`      // 发票类型
	IssueDate        time.Time       `json:"issueDate"`        // 开票日期
	SellerName       string          `json:"sellerName"`       // 销售方名称
	SellerTaxNumber  string          `json:"sellerTaxNumber"`  // 销售方税号
	BuyerName        string          `json:"buyerName"`        // 购买方名称
	BuyerTaxNumber   string          `json:"buyerTaxNumber"`   // 购买方税号
	TotalAmount      decimal.Decimal `json:"totalAmount"`      // 合计金额
	TaxAmount        decimal.Decimal `json:"taxAmount"`        // 合计税额
	TotalWithTax     decimal.Decimal `json:"totalWithTax"`     // 价税合计
	VerificationCode string          `json:"verificationCode"` // 校验码

	// 置信度信息
	Confidence map[string]float64 `json:"confidence"` // 各字段置信度

	// 商品明细
	Items []OCRInvoiceItem `json:"items"` // 发票明细

	// 原始响应
	RawResponse map[string]interface{} `json:"rawResponse"` // 原始API响应
}

// OCRInvoiceItem OCR识别的发票明细
type OCRInvoiceItem struct {
	Name          string          `json:"name"`          // 商品名称
	Specification string          `json:"specification"` // 规格型号
	Unit          string          `json:"unit"`          // 单位
	Quantity      decimal.Decimal `json:"quantity"`      // 数量
	UnitPrice     decimal.Decimal `json:"unitPrice"`     // 单价
	Amount        decimal.Decimal `json:"amount"`        // 金额
	TaxRate       decimal.Decimal `json:"taxRate"`       // 税率
	TaxAmount     decimal.Decimal `json:"taxAmount"`     // 税额
}

// ocrService OCR服务实现
type ocrService struct {
	client *ocr20191230.Client
	config *config.Config
}

// NewOCRService 创建OCR服务实例
func NewOCRService(cfg *config.Config) (OCRService, error) {
	// 创建配置
	ocrConfig := &openapi.Config{
		AccessKeyId:     tea.String(cfg.Aliyun.AccessKeyID),
		AccessKeySecret: tea.String(cfg.Aliyun.AccessKeySecret),
		Endpoint:        tea.String(cfg.Aliyun.OCR.Endpoint),
	}

	// 创建客户端
	client, err := ocr20191230.NewClient(ocrConfig)
	if err != nil {
		return nil, fmt.Errorf("创建OCR客户端失败: %w", err)
	}

	return &ocrService{
		client: client,
		config: cfg,
	}, nil
}

// RecognizeInvoice 识别单张发票
func (s *ocrService) RecognizeInvoice(ctx context.Context, file multipart.File, filename string, settings OCRSettings) (*OCRResult, error) {
	// 读取文件内容
	_, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}

	// TODO: 实际的阿里云OCR API调用
	// 目前返回模拟数据用于测试
	return s.createMockInvoiceResult(filename), nil
}

// RecognizeMixedInvoices 识别混贴发票
func (s *ocrService) RecognizeMixedInvoices(ctx context.Context, file multipart.File, filename string, settings OCRSettings) (*OCRResult, error) {
	// 读取文件内容
	_, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}

	// TODO: 实际的阿里云OCR API调用
	// 目前返回模拟数据用于测试
	return s.createMockInvoiceResult(filename), nil
}

// createMockInvoiceResult 创建模拟的发票识别结果
func (s *ocrService) createMockInvoiceResult(filename string) *OCRResult {
	return &OCRResult{
		InvoiceNumber:    "12345678",
		InvoiceCode:      "*********",
		InvoiceType:      "增值税专用发票",
		IssueDate:        time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
		SellerName:       "测试销售方有限公司",
		SellerTaxNumber:  "91110000123456789X",
		BuyerName:        "测试购买方有限公司",
		BuyerTaxNumber:   "91110000*********Y",
		TotalAmount:      decimal.NewFromFloat(1000.00),
		TaxAmount:        decimal.NewFromFloat(130.00),
		TotalWithTax:     decimal.NewFromFloat(1130.00),
		VerificationCode: "12345",
		Confidence: map[string]float64{
			"invoiceNumber": 0.95,
			"sellerName":    0.92,
			"totalAmount":   0.98,
		},
		Items: []OCRInvoiceItem{
			{
				Name:      "测试商品A",
				Quantity:  decimal.NewFromInt(2),
				UnitPrice: decimal.NewFromFloat(500.00),
				Amount:    decimal.NewFromFloat(1000.00),
				TaxRate:   decimal.NewFromFloat(0.13),
				TaxAmount: decimal.NewFromFloat(130.00),
			},
		},
		RawResponse: map[string]interface{}{
			"filename": filename,
			"mock":     true,
		},
	}
}
