package service

import (
	"context"
	"errors"
	"fmt"
	"mime/multipart"
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// CreateInvoiceRequest defines the request body for creating an invoice
type CreateInvoiceRequest struct {
	InvoiceNumber   string    `json:"invoiceNumber" binding:"required"`
	InvoiceCode     string    `json:"invoiceCode"`
	Type            string    `json:"type" binding:"required"`
	IssueDate       time.Time `json:"issueDate" binding:"required"`
	SellerName      string    `json:"sellerName" binding:"required"`      // 对应前端 sellerName
	SellerTaxNumber string    `json:"sellerTaxNumber" binding:"required"` // 对应前端 sellerTaxNumber
	BuyerName       string    `json:"buyerName" binding:"required"`       // 对应前端 buyerName
	BuyerTaxNumber  string    `json:"buyerTaxNumber" binding:"required"`  // 对应前端 buyerTaxNumber
	TotalAmount     float64   `json:"totalAmount" binding:"required"`
	TaxAmount       float64   `json:"taxAmount" binding:"required"`
	EnterpriseID    string    `json:"enterpriseId" binding:"required"`
	Remarks         string    `json:"remarks"`
}

// ScanInvoiceRequest defines the request for scanning an invoice
type ScanInvoiceRequest struct {
	EnterpriseID string      `json:"enterpriseId" binding:"required"`
	Settings     OCRSettings `json:"settings"`
}

// ScanInvoiceResponse defines the response for scanning an invoice
type ScanInvoiceResponse struct {
	InvoiceID string     `json:"invoiceId"`
	OCRResult *OCRResult `json:"ocrResult"`
	Success   bool       `json:"success"`
	Message   string     `json:"message"`
}

// InvoiceService defines the interface for invoice business logic
type InvoiceService interface {
	CreateInvoice(ctx context.Context, req CreateInvoiceRequest) (*model.Invoice, error)
	GetInvoices(ctx context.Context, req GetInvoicesRequest) (*PaginatedResponse, error)
	GetInvoiceByID(ctx context.Context, id string) (*model.Invoice, error)
	UpdateInvoice(ctx context.Context, id string, req UpdateInvoiceRequest) (*model.Invoice, error)
	DeleteInvoice(ctx context.Context, id string) error
	GetInvoiceStats(ctx context.Context) (*InvoiceStatsResponse, error)
	GetInvoicesByEnterprise(ctx context.Context, enterpriseID string, req GetInvoicesRequest) (*PaginatedResponse, error)
	GetInvoicesByDeclarationPeriod(ctx context.Context, enterpriseID, period string) ([]model.Invoice, error)
	UpdateInvoiceAuthenticationStatus(ctx context.Context, id string, status string) error
	UpdateInvoiceDeclarationStatus(ctx context.Context, id string, status string, period string) error
	GetInvoiceTaxSummary(ctx context.Context, enterpriseID, period string) (*InvoiceTaxSummaryResponse, error)
	CheckEnterprisePermission(ctx context.Context, userID, enterpriseID string) (bool, error)
	GetUserAuthorizedEnterprises(ctx context.Context, userID string) ([]string, error)
	GetInvoicesByUserPermission(ctx context.Context, userID string, req GetInvoicesRequest) (*PaginatedResponse, error)
	GetInvoiceStatsByUserPermission(ctx context.Context, userID string) (*InvoiceStatsResponse, error)
	// OCR related methods
	ScanInvoice(ctx context.Context, file multipart.File, filename string, req ScanInvoiceRequest) (*ScanInvoiceResponse, error)
}

type invoiceService struct {
	db         *gorm.DB
	ocrService OCRService
}

// NewInvoiceService creates a new invoice service instance
func NewInvoiceService(db *gorm.DB, ocrService OCRService) InvoiceService {
	return &invoiceService{
		db:         db,
		ocrService: ocrService,
	}
}

// CreateInvoice 创建发票
func (s *invoiceService) CreateInvoice(ctx context.Context, req CreateInvoiceRequest) (*model.Invoice, error) {
	// 检查发票号码是否已存在
	var existingInvoice model.Invoice
	result := s.db.WithContext(ctx).Where("invoice_number = ?", req.InvoiceNumber).First(&existingInvoice)
	if result.Error == nil {
		return nil, util.ErrInvoiceExists
	}

	invoice := model.Invoice{
		ID:              util.GenerateID(),
		InvoiceNumber:   req.InvoiceNumber,
		InvoiceCode:     req.InvoiceCode,
		Type:            req.Type,
		IssueDate:       req.IssueDate,
		SellerName:      req.SellerName,      // 修正字段映射
		SellerTaxNumber: req.SellerTaxNumber, // 修正字段映射
		BuyerName:       req.BuyerName,       // 修正字段映射
		BuyerTaxNumber:  req.BuyerTaxNumber,  // 修正字段映射
		TotalAmount:     decimal.NewFromFloat(req.TotalAmount),
		TotalTax:        decimal.NewFromFloat(req.TaxAmount),
		EnterpriseID:    req.EnterpriseID,
		Remarks:         req.Remarks,
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	if err := s.db.WithContext(ctx).Create(&invoice).Error; err != nil {
		return nil, err
	}

	return &invoice, nil
}

// GetInvoices 获取发票列表
func (s *invoiceService) GetInvoices(ctx context.Context, req GetInvoicesRequest) (*PaginatedResponse, error) {
	var invoices []model.Invoice
	var total int64

	query := s.db.WithContext(ctx).Model(&model.Invoice{})

	// 添加筛选条件
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("invoice_number LIKE ? OR seller_name LIKE ? OR buyer_name LIKE ?", keyword, keyword, keyword)
	}
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.EnterpriseID != "" {
		query = query.Where("enterprise_id = ?", req.EnterpriseID)
	}
	if req.MinAmount > 0 {
		query = query.Where("total_amount >= ?", req.MinAmount)
	}
	if req.MaxAmount > 0 {
		query = query.Where("total_amount <= ?", req.MaxAmount)
	}
	if !req.StartDate.IsZero() && !req.EndDate.IsZero() {
		query = query.Where("issue_date BETWEEN ? AND ?", req.StartDate, req.EndDate)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 分页查询，预加载企业信息
	offset := (req.Page - 1) * req.PageSize
	if err := query.Preload("Enterprise").Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&invoices).Error; err != nil {
		return nil, err
	}

	// 计算总页数
	pages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &PaginatedResponse{
		Items:    invoices,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Pages:    pages,
	}, nil
}

// GetInvoiceByID 根据ID获取发票
func (s *invoiceService) GetInvoiceByID(ctx context.Context, id string) (*model.Invoice, error) {
	var invoice model.Invoice
	query := s.db.WithContext(ctx).Where("id = ?", id)

	if err := query.First(&invoice).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, util.ErrInvoiceNotFound
		}
		return nil, err
	}

	return &invoice, nil
}

// UpdateInvoice 更新发票信息
func (s *invoiceService) UpdateInvoice(ctx context.Context, id string, req UpdateInvoiceRequest) (*model.Invoice, error) {
	var invoice model.Invoice
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&invoice).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, util.ErrInvoiceNotFound
		}
		return nil, err
	}

	// 更新字段
	updateFields := map[string]interface{}{
		"updated_at": time.Now(),
	}

	if req.Type != "" {
		updateFields["type"] = req.Type
	}
	if !req.IssueDate.IsZero() {
		updateFields["issue_date"] = req.IssueDate
	}
	if req.IssuerName != "" {
		updateFields["issuer_name"] = req.IssuerName
	}
	if req.IssuerTaxID != "" {
		updateFields["issuer_tax_id"] = req.IssuerTaxID
	}
	if req.RecipientName != "" {
		updateFields["recipient_name"] = req.RecipientName
	}
	if req.RecipientTaxID != "" {
		updateFields["recipient_tax_id"] = req.RecipientTaxID
	}
	if req.TotalAmount > 0 {
		updateFields["total_amount"] = req.TotalAmount
	}
	if req.TaxAmount > 0 {
		updateFields["tax_amount"] = req.TaxAmount
	}
	if req.Status != "" {
		updateFields["status"] = req.Status
	}

	if err := s.db.Model(&invoice).Updates(updateFields).Error; err != nil {
		return nil, err
	}

	// 重新查询更新后的数据
	if err := s.db.Where("id = ?", id).First(&invoice).Error; err != nil {
		return nil, err
	}

	return &invoice, nil
}

// DeleteInvoice 删除发票
func (s *invoiceService) DeleteInvoice(ctx context.Context, id string) error {
	result := s.db.WithContext(ctx).Where("id = ?", id).Delete(&model.Invoice{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return util.ErrInvoiceNotFound
	}
	return nil
}

// GetInvoiceStats 获取发票统计信息
func (s *invoiceService) GetInvoiceStats(ctx context.Context) (*InvoiceStatsResponse, error) {
	stats := &InvoiceStatsResponse{}

	// 获取总发票数
	if err := s.db.WithContext(ctx).Model(&model.Invoice{}).Count(&stats.Total).Error; err != nil {
		return nil, err
	}

	// 按状态统计
	var statusStats []struct {
		Status string
		Count  int64
	}
	if err := s.db.WithContext(ctx).Model(&model.Invoice{}).
		Select("invoice_status as status, count(*) as count").
		Group("invoice_status").
		Scan(&statusStats).Error; err != nil {
		return nil, err
	}

	// 处理状态统计
	for _, stat := range statusStats {
		switch stat.Status {
		case "issued":
			stats.Verified = stat.Count
		case "draft", "pending":
			stats.Pending = stat.Count
		case "cancelled":
			stats.Cancelled = stat.Count
		}
	}

	// 计算总金额和税额
	var amountStats struct {
		TotalAmount float64
		TotalTax    float64
	}
	if err := s.db.WithContext(ctx).Model(&model.Invoice{}).
		Select("COALESCE(SUM(total_amount_with_tax), 0) as total_amount, COALESCE(SUM(total_tax), 0) as total_tax").
		Where("invoice_status = ?", "issued").
		Scan(&amountStats).Error; err != nil {
		return nil, err
	}
	stats.TotalAmount = amountStats.TotalAmount
	stats.TotalTax = amountStats.TotalTax

	// 本月发票数量
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	if err := s.db.WithContext(ctx).Model(&model.Invoice{}).
		Where("created_at >= ?", monthStart).
		Count(&stats.ThisMonth).Error; err != nil {
		return nil, err
	}

	// 今日发票数量
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	if err := s.db.WithContext(ctx).Model(&model.Invoice{}).
		Where("created_at >= ?", todayStart).
		Count(&stats.TodayCount).Error; err != nil {
		return nil, err
	}

	// 本月税额
	var monthlyTaxStats struct {
		MonthlyTax float64
	}
	if err := s.db.WithContext(ctx).Model(&model.Invoice{}).
		Select("COALESCE(SUM(total_tax), 0) as monthly_tax").
		Where("created_at >= ? AND invoice_status = ?", monthStart, "issued").
		Scan(&monthlyTaxStats).Error; err != nil {
		return nil, err
	}
	stats.MonthlyTax = monthlyTaxStats.MonthlyTax

	return stats, nil
}

// GetInvoicesByEnterprise 获取企业的发票列表
func (s *invoiceService) GetInvoicesByEnterprise(ctx context.Context, enterpriseID string, req GetInvoicesRequest) (*PaginatedResponse, error) {
	var invoices []model.Invoice
	var total int64

	query := s.db.WithContext(ctx).Model(&model.Invoice{}).Where("enterprise_id = ?", enterpriseID)

	// 添加筛选条件
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.Keyword != "" {
		query = query.Where("invoice_number LIKE ? OR buyer_name LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if !req.StartDate.IsZero() {
		query = query.Where("issue_date >= ?", req.StartDate)
	}
	if !req.EndDate.IsZero() {
		query = query.Where("issue_date <= ?", req.EndDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Preload("Enterprise").Preload("Items.TaxType").
		Order("created_at DESC").Offset(offset).Limit(req.PageSize).
		Find(&invoices).Error; err != nil {
		return nil, err
	}

	// 计算总页数
	pages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &PaginatedResponse{
		Items:    invoices,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Pages:    pages,
	}, nil
}

// GetInvoicesByDeclarationPeriod 获取指定申报期间的发票
func (s *invoiceService) GetInvoicesByDeclarationPeriod(ctx context.Context, enterpriseID, period string) ([]model.Invoice, error) {
	var invoices []model.Invoice

	if err := s.db.WithContext(ctx).
		Where("enterprise_id = ? AND declaration_period = ?", enterpriseID, period).
		Preload("Enterprise").
		Preload("Items.TaxType").
		Find(&invoices).Error; err != nil {
		return nil, err
	}

	return invoices, nil
}

// UpdateInvoiceAuthenticationStatus 更新发票认证状态
func (s *invoiceService) UpdateInvoiceAuthenticationStatus(ctx context.Context, id string, status string) error {
	updates := map[string]interface{}{
		"authentication_status": status,
		"updated_at":            time.Now(),
	}

	if status == model.AuthStatusAuthenticated {
		updates["authentication_date"] = time.Now()
	}

	return s.db.WithContext(ctx).Model(&model.Invoice{}).
		Where("id = ?", id).
		Updates(updates).Error
}

// UpdateInvoiceDeclarationStatus 更新发票申报状态
func (s *invoiceService) UpdateInvoiceDeclarationStatus(ctx context.Context, id string, status string, period string) error {
	updates := map[string]interface{}{
		"declaration_status": status,
		"updated_at":         time.Now(),
	}

	if period != "" {
		updates["declaration_period"] = period
	}

	return s.db.WithContext(ctx).Model(&model.Invoice{}).
		Where("id = ?", id).
		Updates(updates).Error
}

// GetInvoiceTaxSummary 获取发票税务汇总
func (s *invoiceService) GetInvoiceTaxSummary(ctx context.Context, enterpriseID, period string) (*InvoiceTaxSummaryResponse, error) {
	summary := &InvoiceTaxSummaryResponse{
		Period:       period,
		EnterpriseID: enterpriseID,
	}

	// 基础查询条件
	baseQuery := s.db.WithContext(ctx).Model(&model.Invoice{}).
		Where("enterprise_id = ?", enterpriseID)

	if period != "" {
		baseQuery = baseQuery.Where("declaration_period = ?", period)
	}

	// 获取总发票数量
	if err := baseQuery.Count(&summary.TotalInvoiceCount).Error; err != nil {
		return nil, err
	}

	// 获取总金额和税额
	var amountSummary struct {
		TotalAmount        decimal.Decimal
		TotalTax           decimal.Decimal
		TotalAmountWithTax decimal.Decimal
	}

	if err := baseQuery.Select(
		"COALESCE(SUM(total_amount), 0) as total_amount, " +
			"COALESCE(SUM(total_tax), 0) as total_tax, " +
			"COALESCE(SUM(total_amount_with_tax), 0) as total_amount_with_tax").
		Scan(&amountSummary).Error; err != nil {
		return nil, err
	}

	summary.TotalAmount = amountSummary.TotalAmount
	summary.TotalTax = amountSummary.TotalTax
	summary.TotalAmountWithTax = amountSummary.TotalAmountWithTax

	// 获取认证状态统计
	if err := baseQuery.Where("authentication_status = ?", model.AuthStatusAuthenticated).
		Count(&summary.AuthenticatedCount).Error; err != nil {
		return nil, err
	}

	// 获取未申报数量
	if err := baseQuery.Where("declaration_status = ?", model.DeclStatusUndeclared).
		Count(&summary.UndeclaredCount).Error; err != nil {
		return nil, err
	}

	// 获取按税种汇总的数据
	taxTypeSummary, err := s.getInvoiceTaxTypeSummary(ctx, enterpriseID, period)
	if err != nil {
		return nil, err
	}
	summary.TaxTypeSummary = taxTypeSummary

	return summary, nil
}

// getInvoiceTaxTypeSummary 获取按税种汇总的发票数据
func (s *invoiceService) getInvoiceTaxTypeSummary(ctx context.Context, enterpriseID, period string) ([]InvoiceTaxTypeSummary, error) {
	var results []InvoiceTaxTypeSummary

	query := `
		SELECT
			tt.id as tax_type_id,
			tt.tax_name as tax_type_name,
			tt.tax_code as tax_type_code,
			COUNT(DISTINCT i.id) as invoice_count,
			COALESCE(SUM(ii.amount), 0) as total_amount,
			COALESCE(SUM(ii.tax_amount), 0) as total_tax,
			COALESCE(AVG(ii.tax_rate), 0) as average_tax_rate
		FROM invoices i
		JOIN invoice_items ii ON i.id = ii.invoice_id
		LEFT JOIN tax_types tt ON ii.tax_type_id = tt.id
		WHERE i.enterprise_id = ?
	`

	args := []interface{}{enterpriseID}

	if period != "" {
		query += " AND i.declaration_period = ?"
		args = append(args, period)
	}

	query += " GROUP BY tt.id, tt.tax_name, tt.tax_code ORDER BY total_amount DESC"

	if err := s.db.WithContext(ctx).Raw(query, args...).Scan(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

// CheckEnterprisePermission 检查用户是否有权限操作指定企业的发票
func (s *invoiceService) CheckEnterprisePermission(ctx context.Context, userID, enterpriseID string) (bool, error) {
	// 检查用户是否属于该企业
	var user model.User
	if err := s.db.WithContext(ctx).Where("id = ?", userID).First(&user).Error; err != nil {
		return false, fmt.Errorf("查询用户失败: %w", err)
	}

	// 如果用户属于该企业，则有权限
	if user.EnterpriseID != nil && *user.EnterpriseID == enterpriseID {
		return true, nil
	}

	// 检查用户是否是该企业的所有者（修正字段名从user_id到owner_id）
	var enterprise model.Enterprise
	if err := s.db.WithContext(ctx).Where("id = ? AND owner_id = ?", enterpriseID, userID).First(&enterprise).Error; err == nil {
		return true, nil
	}

	// 检查用户是否在enterprise_users表中有该企业的访问权限
	var enterpriseUser model.EnterpriseUser
	if err := s.db.WithContext(ctx).Where("enterprise_id = ? AND user_id = ? AND status = ?",
		enterpriseID, userID, model.EnterpriseUserStatusActive).First(&enterpriseUser).Error; err == nil {
		return true, nil
	}

	// 如果以上检查都失败，返回无权限
	return false, nil
}

// GetUserAuthorizedEnterprises 获取用户有权限的企业列表
func (s *invoiceService) GetUserAuthorizedEnterprises(ctx context.Context, userID string) ([]string, error) {
	var enterpriseIDs []string

	// 获取用户信息
	var user model.User
	if err := s.db.WithContext(ctx).Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 如果用户属于某个企业，添加该企业ID
	if user.EnterpriseID != nil {
		enterpriseIDs = append(enterpriseIDs, *user.EnterpriseID)
	}

	// 查找用户拥有的企业（修复：使用 owner_id 而不是 user_id）
	var ownedEnterprises []model.Enterprise
	if err := s.db.WithContext(ctx).Where("owner_id = ?", userID).Find(&ownedEnterprises).Error; err != nil {
		return nil, fmt.Errorf("查询用户拥有的企业失败: %w", err)
	}

	// 添加用户拥有的企业ID（避免重复）
	for _, enterprise := range ownedEnterprises {
		found := false
		for _, existingID := range enterpriseIDs {
			if existingID == enterprise.ID {
				found = true
				break
			}
		}
		if !found {
			enterpriseIDs = append(enterpriseIDs, enterprise.ID)
		}
	}

	// 查找用户通过 EnterpriseUser 关联的企业
	var enterpriseUsers []model.EnterpriseUser
	if err := s.db.WithContext(ctx).Where("user_id = ? AND status = ?", userID, "active").Find(&enterpriseUsers).Error; err != nil {
		return nil, fmt.Errorf("查询用户关联的企业失败: %w", err)
	}

	// 添加关联企业ID（避免重复）
	for _, enterpriseUser := range enterpriseUsers {
		found := false
		for _, existingID := range enterpriseIDs {
			if existingID == enterpriseUser.EnterpriseID {
				found = true
				break
			}
		}
		if !found {
			enterpriseIDs = append(enterpriseIDs, enterpriseUser.EnterpriseID)
		}
	}

	return enterpriseIDs, nil
}

// GetInvoicesByUserPermission 根据用户权限获取发票列表
func (s *invoiceService) GetInvoicesByUserPermission(ctx context.Context, userID string, req GetInvoicesRequest) (*PaginatedResponse, error) {
	// 获取用户有权限的企业列表
	authorizedEnterprises, err := s.GetUserAuthorizedEnterprises(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户权限失败: %w", err)
	}

	if len(authorizedEnterprises) == 0 {
		// 用户没有关联任何企业，返回空结果
		return &PaginatedResponse{
			Items:    []model.Invoice{},
			Total:    0,
			Page:     req.Page,
			PageSize: req.PageSize,
			Pages:    0,
		}, nil
	}

	var invoices []model.Invoice
	var total int64

	query := s.db.WithContext(ctx).Model(&model.Invoice{})

	// 限制只查询用户有权限的企业的发票
	query = query.Where("enterprise_id IN ?", authorizedEnterprises)

	// 添加其他筛选条件
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("invoice_number LIKE ? OR seller_name LIKE ? OR buyer_name LIKE ?", keyword, keyword, keyword)
	}
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.EnterpriseID != "" {
		// 确保指定的企业ID在用户权限范围内
		hasPermission := false
		for _, enterpriseID := range authorizedEnterprises {
			if enterpriseID == req.EnterpriseID {
				hasPermission = true
				break
			}
		}
		if hasPermission {
			query = query.Where("enterprise_id = ?", req.EnterpriseID)
		} else {
			// 用户无权限查看指定企业的发票，返回空结果
			return &PaginatedResponse{
				Items:    []model.Invoice{},
				Total:    0,
				Page:     req.Page,
				PageSize: req.PageSize,
				Pages:    0,
			}, nil
		}
	}
	if req.MinAmount > 0 {
		query = query.Where("total_amount_with_tax >= ?", req.MinAmount)
	}
	if req.MaxAmount > 0 {
		query = query.Where("total_amount_with_tax <= ?", req.MaxAmount)
	}
	if !req.StartDate.IsZero() && !req.EndDate.IsZero() {
		query = query.Where("issue_date BETWEEN ? AND ?", req.StartDate, req.EndDate)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("计算发票总数失败: %w", err)
	}

	// 分页查询，预加载企业信息
	offset := (req.Page - 1) * req.PageSize
	if err := query.Preload("Enterprise").Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&invoices).Error; err != nil {
		return nil, fmt.Errorf("查询发票列表失败: %w", err)
	}

	// 计算总页数
	pages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &PaginatedResponse{
		Items:    invoices,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Pages:    pages,
	}, nil
}

// GetInvoiceStatsByUserPermission 根据用户权限获取发票统计信息
func (s *invoiceService) GetInvoiceStatsByUserPermission(ctx context.Context, userID string) (*InvoiceStatsResponse, error) {
	// 获取用户有权限的企业列表
	authorizedEnterprises, err := s.GetUserAuthorizedEnterprises(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户权限失败: %w", err)
	}

	stats := &InvoiceStatsResponse{}

	if len(authorizedEnterprises) == 0 {
		// 用户没有关联任何企业，返回空统计
		return stats, nil
	}

	// 基础查询条件：限制在用户有权限的企业范围内
	baseQuery := s.db.WithContext(ctx).Model(&model.Invoice{}).Where("enterprise_id IN ?", authorizedEnterprises)

	// 获取总发票数
	if err := baseQuery.Count(&stats.Total).Error; err != nil {
		return nil, fmt.Errorf("获取发票总数失败: %w", err)
	}

	// 按状态统计
	var statusStats []struct {
		Status string
		Count  int64
	}
	if err := baseQuery.Select("invoice_status as status, count(*) as count").
		Group("invoice_status").
		Scan(&statusStats).Error; err != nil {
		return nil, fmt.Errorf("获取状态统计失败: %w", err)
	}

	// 处理状态统计
	for _, stat := range statusStats {
		switch stat.Status {
		case "issued":
			stats.Verified = stat.Count
		case "draft", "pending":
			stats.Pending = stat.Count
		case "cancelled":
			stats.Cancelled = stat.Count
		}
	}

	// 计算总金额和税额
	var amountStats struct {
		TotalAmount float64
		TotalTax    float64
	}
	if err := baseQuery.Select("COALESCE(SUM(total_amount_with_tax), 0) as total_amount, COALESCE(SUM(total_tax), 0) as total_tax").
		Where("invoice_status = ?", "issued").
		Scan(&amountStats).Error; err != nil {
		return nil, fmt.Errorf("获取金额统计失败: %w", err)
	}
	stats.TotalAmount = amountStats.TotalAmount
	stats.TotalTax = amountStats.TotalTax

	// 本月发票数量
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	if err := baseQuery.Where("created_at >= ?", monthStart).
		Count(&stats.ThisMonth).Error; err != nil {
		return nil, fmt.Errorf("获取本月统计失败: %w", err)
	}

	// 今日发票数量
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	if err := baseQuery.Where("created_at >= ?", todayStart).
		Count(&stats.TodayCount).Error; err != nil {
		return nil, fmt.Errorf("获取今日统计失败: %w", err)
	}

	// 本月税额
	var monthlyTaxStats struct {
		MonthlyTax float64
	}
	if err := baseQuery.Select("COALESCE(SUM(total_tax), 0) as monthly_tax").
		Where("created_at >= ? AND invoice_status = ?", monthStart, "issued").
		Scan(&monthlyTaxStats).Error; err != nil {
		return nil, fmt.Errorf("获取本月税额统计失败: %w", err)
	}
	stats.MonthlyTax = monthlyTaxStats.MonthlyTax

	return stats, nil
}

// UpdateInvoiceRequest defines the request body for updating an invoice
type UpdateInvoiceRequest struct {
	ID             string    `json:"-"`
	Type           string    `json:"type,omitempty"`
	IssueDate      time.Time `json:"issueDate,omitempty"`
	IssuerName     string    `json:"issuerName,omitempty"`
	IssuerTaxID    string    `json:"issuerTaxId,omitempty"`
	RecipientName  string    `json:"recipientName,omitempty"`
	RecipientTaxID string    `json:"recipientTaxId,omitempty"`
	TotalAmount    float64   `json:"totalAmount,omitempty"`
	TaxAmount      float64   `json:"taxAmount,omitempty"`
	Status         string    `json:"status,omitempty"`
}

type GetInvoicesRequest struct {
	Page         int       `json:"page" form:"page"`
	PageSize     int       `json:"pageSize" form:"pageSize"`
	Keyword      string    `json:"keyword" form:"keyword"`
	Type         string    `json:"type" form:"type"`
	Status       string    `json:"status" form:"status"`
	EnterpriseID string    `json:"enterpriseId" form:"enterpriseId"`
	MinAmount    float64   `json:"minAmount" form:"minAmount"`
	MaxAmount    float64   `json:"maxAmount" form:"maxAmount"`
	StartDate    time.Time `json:"startDate" form:"startDate"`
	EndDate      time.Time `json:"endDate" form:"endDate"`
}

// InvoiceStatsResponse defines the response structure for invoice statistics
type InvoiceStatsResponse struct {
	Total       int64   `json:"total"`
	Verified    int64   `json:"verified"`
	Pending     int64   `json:"pending"`
	Cancelled   int64   `json:"cancelled"`
	TotalAmount float64 `json:"totalAmount"`
	TotalTax    float64 `json:"totalTax"`
	ThisMonth   int64   `json:"thisMonth"`
	TodayCount  int64   `json:"todayCount"`
	MonthlyTax  float64 `json:"monthlyTax"`
}

// InvoiceTaxSummaryResponse 发票税务汇总响应
type InvoiceTaxSummaryResponse struct {
	Period             string                  `json:"period"`
	EnterpriseID       string                  `json:"enterprise_id"`
	TotalInvoiceCount  int64                   `json:"total_invoice_count"`
	TotalAmount        decimal.Decimal         `json:"total_amount"`
	TotalTax           decimal.Decimal         `json:"total_tax"`
	TotalAmountWithTax decimal.Decimal         `json:"total_amount_with_tax"`
	AuthenticatedCount int64                   `json:"authenticated_count"`
	UndeclaredCount    int64                   `json:"undeclared_count"`
	TaxTypeSummary     []InvoiceTaxTypeSummary `json:"tax_type_summary"`
}

// InvoiceTaxTypeSummary 按税种汇总
type InvoiceTaxTypeSummary struct {
	TaxTypeID      string          `json:"tax_type_id"`
	TaxTypeName    string          `json:"tax_type_name"`
	TaxTypeCode    string          `json:"tax_type_code"`
	InvoiceCount   int64           `json:"invoice_count"`
	TotalAmount    decimal.Decimal `json:"total_amount"`
	TotalTax       decimal.Decimal `json:"total_tax"`
	AverageTaxRate decimal.Decimal `json:"average_tax_rate"`
}

// ScanInvoice 扫描识别发票
func (s *invoiceService) ScanInvoice(ctx context.Context, file multipart.File, filename string, req ScanInvoiceRequest) (*ScanInvoiceResponse, error) {
	// 调用OCR服务识别发票
	ocrResult, err := s.ocrService.RecognizeMixedInvoices(ctx, file, filename, req.Settings)
	if err != nil {
		return &ScanInvoiceResponse{
			Success: false,
			Message: fmt.Sprintf("OCR识别失败: %v", err),
		}, nil
	}

	// 将OCR结果转换为发票数据并保存
	invoice, err := s.createInvoiceFromOCR(ctx, ocrResult, req.EnterpriseID)
	if err != nil {
		return &ScanInvoiceResponse{
			Success:   false,
			Message:   fmt.Sprintf("保存发票失败: %v", err),
			OCRResult: ocrResult,
		}, nil
	}

	return &ScanInvoiceResponse{
		InvoiceID: invoice.ID,
		OCRResult: ocrResult,
		Success:   true,
		Message:   "发票识别成功",
	}, nil
}

// createInvoiceFromOCR 从OCR结果创建发票记录
func (s *invoiceService) createInvoiceFromOCR(ctx context.Context, ocrResult *OCRResult, enterpriseID string) (*model.Invoice, error) {
	// 计算价税合计
	totalWithTax := ocrResult.TotalAmount.Add(ocrResult.TaxAmount)
	if !ocrResult.TotalWithTax.IsZero() {
		totalWithTax = ocrResult.TotalWithTax
	}

	invoice := model.Invoice{
		ID:                   util.GenerateID(),
		EnterpriseID:         enterpriseID,
		InvoiceNumber:        ocrResult.InvoiceNumber,
		InvoiceCode:          ocrResult.InvoiceCode,
		Type:                 s.mapInvoiceType(ocrResult.InvoiceType),
		Status:               model.InvoiceStatusDraft,
		AuthenticationStatus: model.AuthStatusPending,
		DeclarationStatus:    model.DeclStatusUndeclared,
		IssueDate:            ocrResult.IssueDate,
		BuyerName:            ocrResult.BuyerName,
		BuyerTaxNumber:       ocrResult.BuyerTaxNumber,
		SellerName:           ocrResult.SellerName,
		SellerTaxNumber:      ocrResult.SellerTaxNumber,
		TotalAmount:          ocrResult.TotalAmount,
		TotalTax:             ocrResult.TaxAmount,
		TotalAmountWithTax:   totalWithTax,
		VerificationCode:     ocrResult.VerificationCode,
		InvoiceSource:        model.InvoiceSourceScan,
		CurrencyCode:         "CNY",
		ExchangeRate:         decimal.NewFromInt(1),
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}

	if err := s.db.WithContext(ctx).Create(&invoice).Error; err != nil {
		return nil, fmt.Errorf("创建发票记录失败: %w", err)
	}

	return &invoice, nil
}

// mapInvoiceType 映射发票类型
func (s *invoiceService) mapInvoiceType(ocrType string) string {
	// 根据OCR识别的发票类型映射到系统定义的类型
	switch ocrType {
	case "增值税专用发票":
		return model.InvoiceTypeSpecial
	case "增值税普通发票":
		return model.InvoiceTypeOrdinary
	case "增值税电子发票":
		return model.InvoiceTypeElectronic
	case "增值税卷票":
		return model.InvoiceTypeRoll
	default:
		return model.InvoiceTypeOrdinary // 默认为普通发票
	}
}
