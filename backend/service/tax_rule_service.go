// Package service provides business logic layer for the tax management system.
// It implements tax rule management operations including CRUD operations and business validations.
package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// TaxRuleService defines the interface for tax rule business logic
type TaxRuleService interface {
	CreateTaxRule(ctx context.Context, req CreateTaxRuleRequest) (*model.TaxRule, error)
	GetTaxRules(ctx context.Context, req GetTaxRulesRequest) (*PaginatedResponse, error)
	GetTaxRuleByID(ctx context.Context, id string) (*model.TaxRule, error)
	UpdateTaxRule(ctx context.Context, id string, req UpdateTaxRuleRequest) (*model.TaxRule, error)
	DeleteTaxRule(ctx context.Context, id string) error
	GetTaxRulesByTaxType(ctx context.Context, taxTypeID string) ([]model.TaxRule, error)
	ValidateTaxRule(ctx context.Context, rule *model.TaxRule) error
}

type taxRuleService struct {
	db *gorm.DB
}

// NewTaxRuleService creates a new tax rule service instance
func NewTaxRuleService(db *gorm.DB) TaxRuleService {
	return &taxRuleService{db: db}
}

// CreateTaxRuleRequest represents a request to create a tax rule
type CreateTaxRuleRequest struct {
	TaxTypeID     string                 `json:"taxTypeId" binding:"required"`
	Name          string                 `json:"name" binding:"required"`
	Description   *string                `json:"description"`
	Condition     *string                `json:"condition"`
	Formula       *string                `json:"formula"`
	EffectiveDate time.Time              `json:"effectiveDate" binding:"required"`
	ExpiryDate    *time.Time             `json:"expiryDate"`
	Parameters    map[string]interface{} `json:"parameters"`
}

// UpdateTaxRuleRequest represents a request to update a tax rule
type UpdateTaxRuleRequest struct {
	Name          *string                `json:"name"`
	Description   *string                `json:"description"`
	Condition     *string                `json:"condition"`
	Formula       *string                `json:"formula"`
	EffectiveDate *time.Time             `json:"effectiveDate"`
	ExpiryDate    *time.Time             `json:"expiryDate"`
	Parameters    map[string]interface{} `json:"parameters"`
}

// GetTaxRulesRequest represents a request to get tax rules with pagination and filtering
type GetTaxRulesRequest struct {
	TaxTypeID string `json:"taxTypeId"`
	Name      string `json:"name"`
	IsActive  *bool  `json:"isActive"`
	Page      int    `json:"page" binding:"min=1"`
	PageSize  int    `json:"pageSize" binding:"min=1,max=100"`
	SortBy    string `json:"sortBy"`
	SortOrder string `json:"sortOrder"`
}

// CreateTaxRule creates a new tax rule
func (s *taxRuleService) CreateTaxRule(ctx context.Context, req CreateTaxRuleRequest) (*model.TaxRule, error) {
	// Validate tax type exists
	var taxType model.TaxType
	if err := s.db.WithContext(ctx).Where("id = ?", req.TaxTypeID).First(&taxType).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("税种不存在")
		}
		return nil, fmt.Errorf("验证税种失败: %w", err)
	}

	// Check if rule name already exists for this tax type
	var existingRule model.TaxRule
	result := s.db.WithContext(ctx).Where("tax_type_id = ? AND name = ?", req.TaxTypeID, req.Name).First(&existingRule)
	if result.Error == nil {
		return nil, fmt.Errorf("该税种下已存在同名规则")
	}

	taxRule := model.TaxRule{
		ID:            util.GenerateID(),
		TaxTypeID:     req.TaxTypeID,
		Name:          req.Name,
		Description:   req.Description,
		Condition:     req.Condition,
		Formula:       req.Formula,
		EffectiveDate: req.EffectiveDate,
		ExpiryDate:    req.ExpiryDate,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// Set parameters if provided
	if req.Parameters != nil {
		if err := taxRule.SetParameters(req.Parameters); err != nil {
			return nil, fmt.Errorf("设置规则参数失败: %w", err)
		}
	}

	// Validate the rule
	if err := s.ValidateTaxRule(ctx, &taxRule); err != nil {
		return nil, err
	}

	if err := s.db.WithContext(ctx).Create(&taxRule).Error; err != nil {
		return nil, fmt.Errorf("创建税则失败: %w", err)
	}

	// Load the tax type for response
	if err := s.db.WithContext(ctx).Preload("TaxType").Where("id = ?", taxRule.ID).First(&taxRule).Error; err != nil {
		return nil, fmt.Errorf("获取创建的税则失败: %w", err)
	}

	return &taxRule, nil
}

// GetTaxRules retrieves tax rules with pagination and filtering
func (s *taxRuleService) GetTaxRules(ctx context.Context, req GetTaxRulesRequest) (*PaginatedResponse, error) {
	var taxRules []model.TaxRule
	var total int64

	// Build query
	query := s.db.WithContext(ctx).Model(&model.TaxRule{})

	// Apply filters
	if req.TaxTypeID != "" {
		query = query.Where("tax_type_id = ?", req.TaxTypeID)
	}
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.IsActive != nil {
		now := time.Now()
		if *req.IsActive {
			query = query.Where("effective_date <= ? AND (expiry_date IS NULL OR expiry_date > ?)", now, now)
		} else {
			query = query.Where("effective_date > ? OR (expiry_date IS NOT NULL AND expiry_date <= ?)", now, now)
		}
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取税则总数失败: %w", err)
	}

	// Apply pagination and sorting
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize

	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	orderBy := fmt.Sprintf("%s %s", req.SortBy, req.SortOrder)
	if err := query.Preload("TaxType").
		Order(orderBy).Offset(offset).Limit(req.PageSize).
		Find(&taxRules).Error; err != nil {
		return nil, fmt.Errorf("获取税则列表失败: %w", err)
	}

	// 计算总页数
	pages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &PaginatedResponse{
		Items:    taxRules,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Pages:    pages,
	}, nil
}

// GetTaxRuleByID retrieves a tax rule by ID
func (s *taxRuleService) GetTaxRuleByID(ctx context.Context, id string) (*model.TaxRule, error) {
	var taxRule model.TaxRule

	if err := s.db.WithContext(ctx).
		Preload("TaxType").
		Where("id = ?", id).
		First(&taxRule).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("税则不存在")
		}
		return nil, fmt.Errorf("获取税则详情失败: %w", err)
	}

	return &taxRule, nil
}

// UpdateTaxRule updates an existing tax rule
func (s *taxRuleService) UpdateTaxRule(ctx context.Context, id string, req UpdateTaxRuleRequest) (*model.TaxRule, error) {
	var taxRule model.TaxRule

	// Check if tax rule exists
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&taxRule).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("税则不存在")
		}
		return nil, fmt.Errorf("查找税则失败: %w", err)
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if req.Name != nil {
		// Check if new name conflicts with existing rules for the same tax type
		var existingRule model.TaxRule
		result := s.db.WithContext(ctx).Where("tax_type_id = ? AND name = ? AND id != ?", taxRule.TaxTypeID, *req.Name, id).First(&existingRule)
		if result.Error == nil {
			return nil, fmt.Errorf("该税种下已存在同名规则")
		}
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Condition != nil {
		updates["condition"] = *req.Condition
	}
	if req.Formula != nil {
		updates["formula"] = *req.Formula
	}
	if req.EffectiveDate != nil {
		updates["effective_date"] = *req.EffectiveDate
	}
	if req.ExpiryDate != nil {
		updates["expiry_date"] = *req.ExpiryDate
	}
	if req.Parameters != nil {
		if err := taxRule.SetParameters(req.Parameters); err != nil {
			return nil, fmt.Errorf("设置规则参数失败: %w", err)
		}
		updates["parameters"] = taxRule.Parameters
	}

	updates["updated_at"] = time.Now()

	// Perform update
	if err := s.db.WithContext(ctx).Model(&taxRule).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("更新税则失败: %w", err)
	}

	// Reload with associations
	if err := s.db.WithContext(ctx).Preload("TaxType").Where("id = ?", id).First(&taxRule).Error; err != nil {
		return nil, fmt.Errorf("获取更新后的税则失败: %w", err)
	}

	// Validate the updated rule
	if err := s.ValidateTaxRule(ctx, &taxRule); err != nil {
		return nil, err
	}

	return &taxRule, nil
}

// DeleteTaxRule deletes a tax rule by ID
func (s *taxRuleService) DeleteTaxRule(ctx context.Context, id string) error {
	var taxRule model.TaxRule

	// Check if tax rule exists
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&taxRule).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("税则不存在")
		}
		return fmt.Errorf("查找税则失败: %w", err)
	}

	// TODO: Add business logic to check if rule is being used in calculations
	// For now, we'll allow deletion

	if err := s.db.WithContext(ctx).Delete(&taxRule).Error; err != nil {
		return fmt.Errorf("删除税则失败: %w", err)
	}

	return nil
}

// GetTaxRulesByTaxType retrieves all tax rules for a specific tax type
func (s *taxRuleService) GetTaxRulesByTaxType(ctx context.Context, taxTypeID string) ([]model.TaxRule, error) {
	var taxRules []model.TaxRule

	if err := s.db.WithContext(ctx).
		Preload("TaxType").
		Where("tax_type_id = ?", taxTypeID).
		Order("effective_date DESC, created_at DESC").
		Find(&taxRules).Error; err != nil {
		return nil, fmt.Errorf("获取税种规则失败: %w", err)
	}

	return taxRules, nil
}

// ValidateTaxRule validates a tax rule for business logic consistency
func (s *taxRuleService) ValidateTaxRule(ctx context.Context, rule *model.TaxRule) error {
	// Check effective date is not in the past (for new rules)
	if rule.EffectiveDate.Before(time.Now().AddDate(0, 0, -1)) {
		return fmt.Errorf("生效日期不能早于今天")
	}

	// Check expiry date is after effective date
	if rule.ExpiryDate != nil && rule.ExpiryDate.Before(rule.EffectiveDate) {
		return fmt.Errorf("失效日期不能早于生效日期")
	}

	// TODO: Add more business validations as needed
	// - Validate formula syntax
	// - Check for conflicting rules
	// - Validate parameters structure

	return nil
}
