package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"backend/config"
	"backend/model"
)

// TaxConfigService handles tax configuration management
type TaxConfigService struct {
	db     *gorm.DB
	config *config.Config
}

// NewTaxConfigService creates a new tax configuration service
func NewTaxConfigService(db *gorm.DB, cfg *config.Config) *TaxConfigService {
	return &TaxConfigService{
		db:     db,
		config: cfg,
	}
}

// CreateTaxType creates a new tax type with intelligent defaults
func (s *TaxConfigService) CreateTaxType(ctx context.Context, req model.TaxType) (*model.TaxType, error) {
	// Validate tax type code uniqueness
	var existing model.TaxType
	if err := s.db.Where("code = ?", req.Code).First(&existing).Error; err == nil {
		return nil, errors.New("税种编码已存在")
	}

	// Validate tax type name uniqueness
	if err := s.db.Where("name = ?", req.Name).First(&existing).Error; err == nil {
		return nil, errors.New("税种名称已存在")
	}

	taxType := &model.TaxType{
		ID:              uuid.New().String(),
		Code:            req.Code,
		Name:            req.Name,
		Description:     req.Description,
		Category:        req.Category,
		RateType:        req.RateType,
		TaxRate:         req.TaxRate,
		TaxRateOptions:  req.TaxRateOptions,
		FilingFrequency: req.FilingFrequency,
		FilingDay:       req.FilingDay,
		PaymentDeadline: req.PaymentDeadline,
		LegalBasis:      req.LegalBasis,
		TaxAuthority:    req.TaxAuthority,
		IsActive:        true, // Default to active
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	if err := s.db.Create(taxType).Error; err != nil {
		return nil, fmt.Errorf("创建税种失败: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, "tax_type_created", taxType.ID, fmt.Sprintf("税种 %s 已创建", taxType.Name))

	return taxType, nil
}

// GetTaxType retrieves a tax type by ID
func (s *TaxConfigService) GetTaxType(ctx context.Context, id string) (*model.TaxType, error) {
	var taxType model.TaxType
	if err := s.db.First(&taxType, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("税种不存在")
		}
		return nil, fmt.Errorf("获取税种信息失败: %w", err)
	}
	return &taxType, nil
}

// UpdateTaxType updates tax type configuration
func (s *TaxConfigService) UpdateTaxType(ctx context.Context, id string, req model.TaxType) (*model.TaxType, error) {
	var taxType model.TaxType
	if err := s.db.First(&taxType, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("税种不存在")
		}
		return nil, fmt.Errorf("获取税种信息失败: %w", err)
	}

	// Check for code uniqueness if code is being updated
	if req.Code != "" && req.Code != taxType.Code {
		var existing model.TaxType
		if err := s.db.Where("code = ? AND id != ?", req.Code, id).First(&existing).Error; err == nil {
			return nil, errors.New("税种编码已存在")
		}
	}

	// Check for name uniqueness if name is being updated
	if req.Name != "" && req.Name != taxType.Name {
		var existing model.TaxType
		if err := s.db.Where("name = ? AND id != ?", req.Name, id).First(&existing).Error; err == nil {
			return nil, errors.New("税种名称已存在")
		}
	}

	// Update fields
	if req.Code != "" {
		taxType.Code = req.Code
	}
	if req.Name != "" {
		taxType.Name = req.Name
	}
	if req.Description != "" {
		taxType.Description = req.Description
	}
	if req.Category != "" {
		taxType.Category = req.Category
	}
	if req.RateType != "" {
		taxType.RateType = req.RateType
	}
	if !req.TaxRate.IsZero() {
		taxType.TaxRate = req.TaxRate
	}
	if req.TaxRateOptions != "" {
		taxType.TaxRateOptions = req.TaxRateOptions
	}
	if req.FilingFrequency != "" {
		taxType.FilingFrequency = req.FilingFrequency
	}
	if req.FilingDay != nil {
		taxType.FilingDay = req.FilingDay
	}
	if req.PaymentDeadline != nil {
		taxType.PaymentDeadline = req.PaymentDeadline
	}
	if req.LegalBasis != "" {
		taxType.LegalBasis = req.LegalBasis
	}
	if req.TaxAuthority != "" {
		taxType.TaxAuthority = req.TaxAuthority
	}
	// Handle IsActive field explicitly
	taxType.IsActive = req.IsActive
	taxType.UpdatedAt = time.Now()

	if err := s.db.Save(&taxType).Error; err != nil {
		return nil, fmt.Errorf("更新税种失败: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, "tax_type_updated", taxType.ID, fmt.Sprintf("税种 %s 已更新", taxType.Name))

	return &taxType, nil
}

// ListTaxTypes lists all tax types with filtering and pagination
func (s *TaxConfigService) ListTaxTypes(ctx context.Context, filters map[string]interface{}) ([]model.TaxType, error) {
	var taxTypes []model.TaxType
	query := s.db.Model(&model.TaxType{})

	// Apply filters
	if category, ok := filters["category"].(string); ok && category != "" {
		query = query.Where("category = ?", category)
	}
	if isActive, ok := filters["is_active"].(bool); ok {
		query = query.Where("is_active = ?", isActive)
	}
	if rateType, ok := filters["rate_type"].(string); ok && rateType != "" {
		query = query.Where("rate_type = ?", rateType)
	}
	if filingFrequency, ok := filters["filing_frequency"].(string); ok && filingFrequency != "" {
		query = query.Where("filing_frequency = ?", filingFrequency)
	}
	if keyword, ok := filters["keyword"].(string); ok && keyword != "" {
		query = query.Where("name LIKE ? OR code LIKE ? OR description LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	if err := query.Order("created_at DESC").Find(&taxTypes).Error; err != nil {
		return nil, fmt.Errorf("获取税种列表失败: %w", err)
	}

	return taxTypes, nil
}

// BatchUpdateTaxTypeStatus updates multiple tax types' active status
func (s *TaxConfigService) BatchUpdateTaxTypeStatus(ctx context.Context, ids []string, isActive bool) error {
	if len(ids) == 0 {
		return errors.New("税种ID列表不能为空")
	}

	result := s.db.Model(&model.TaxType{}).
		Where("id IN ?", ids).
		Updates(map[string]interface{}{
			"is_active":  isActive,
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		return fmt.Errorf("批量更新税种状态失败: %w", result.Error)
	}

	// Create audit logs
	action := "tax_type_enabled"
	if !isActive {
		action = "tax_type_disabled"
	}

	for _, id := range ids {
		s.createAuditLog(ctx, action, id, fmt.Sprintf("批量%s税种",
			map[bool]string{true: "启用", false: "禁用"}[isActive]))
	}

	return nil
}

// BatchDeleteTaxTypes soft deletes multiple tax types
func (s *TaxConfigService) BatchDeleteTaxTypes(ctx context.Context, ids []string) error {
	if len(ids) == 0 {
		return errors.New("税种ID列表不能为空")
	}

	// Soft delete by setting is_active to false
	result := s.db.Model(&model.TaxType{}).
		Where("id IN ?", ids).
		Updates(map[string]interface{}{
			"is_active":  false,
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		return fmt.Errorf("批量删除税种失败: %w", result.Error)
	}

	// Create audit logs
	for _, id := range ids {
		s.createAuditLog(ctx, "tax_type_deleted", id, "批量删除税种")
	}

	return nil
}

// CreateTaxRule creates a new tax calculation rule
func (s *TaxConfigService) CreateTaxRule(ctx context.Context, taxTypeID string, req model.TaxRule) (*model.TaxRule, error) {
	// Verify tax type exists
	var taxType model.TaxType
	if err := s.db.First(&taxType, "id = ?", taxTypeID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("税种不存在")
		}
		return nil, fmt.Errorf("获取税种信息失败: %w", err)
	}

	taxRule := &model.TaxRule{
		ID:            uuid.New().String(),
		TaxTypeID:     taxTypeID,
		Name:          req.Name,
		Description:   req.Description,
		Condition:     req.Condition,
		Formula:       req.Formula,
		EffectiveDate: req.EffectiveDate,
		ExpiryDate:    req.ExpiryDate,
		Parameters:    req.Parameters,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := s.db.Create(taxRule).Error; err != nil {
		return nil, fmt.Errorf("创建税则失败: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, "tax_rule_created", taxRule.ID, fmt.Sprintf("税则 %s 已创建", taxRule.Name))

	return taxRule, nil
}

// UpdateTaxRule updates an existing tax rule
func (s *TaxConfigService) UpdateTaxRule(ctx context.Context, id string, req model.TaxRule) (*model.TaxRule, error) {
	var taxRule model.TaxRule
	if err := s.db.First(&taxRule, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("税则不存在")
		}
		return nil, fmt.Errorf("获取税则信息失败: %w", err)
	}

	// Update fields
	if req.Name != "" {
		taxRule.Name = req.Name
	}
	if req.Description != nil {
		taxRule.Description = req.Description
	}
	if req.Condition != nil {
		taxRule.Condition = req.Condition
	}
	if req.Formula != nil {
		taxRule.Formula = req.Formula
	}
	if !req.EffectiveDate.IsZero() {
		taxRule.EffectiveDate = req.EffectiveDate
	}
	if req.ExpiryDate != nil {
		taxRule.ExpiryDate = req.ExpiryDate
	}
	if req.Parameters != nil {
		taxRule.Parameters = req.Parameters
	}
	taxRule.UpdatedAt = time.Now()

	if err := s.db.Save(&taxRule).Error; err != nil {
		return nil, fmt.Errorf("更新税则失败: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, "tax_rule_updated", taxRule.ID, fmt.Sprintf("税则 %s 已更新", taxRule.Name))

	return &taxRule, nil
}

// GetTaxRules retrieves tax rules for a tax type
func (s *TaxConfigService) GetTaxRules(ctx context.Context, taxTypeID string) ([]model.TaxRule, error) {
	var taxRules []model.TaxRule
	if err := s.db.Where("tax_type_id = ?", taxTypeID).Order("priority DESC, created_at DESC").Find(&taxRules).Error; err != nil {
		return nil, fmt.Errorf("获取税则列表失败: %w", err)
	}
	return taxRules, nil
}

// EnableTaxTypeForEnterprise enables a tax type for a specific enterprise
func (s *TaxConfigService) EnableTaxTypeForEnterprise(ctx context.Context, enterpriseID, taxTypeID string, customRate *float64) error {
	// Verify enterprise and tax type exist
	var enterprise model.Enterprise
	if err := s.db.First(&enterprise, "id = ?", enterpriseID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("企业不存在")
		}
		return fmt.Errorf("获取企业信息失败: %w", err)
	}

	var taxType model.TaxType
	if err := s.db.First(&taxType, "id = ?", taxTypeID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("税种不存在")
		}
		return fmt.Errorf("获取税种信息失败: %w", err)
	}

	// Check if already configured
	var existing model.EnterpriseTaxConfig
	err := s.db.Where("enterprise_id = ? AND tax_type_id = ?", enterpriseID, taxTypeID).First(&existing).Error
	if err == nil {
		// Update existing configuration
		existing.IsActive = true
		if customRate != nil {
			existing.TaxRate = decimal.NewFromFloat(*customRate)
		}
		existing.StartDate = time.Now()
		existing.UpdatedAt = time.Now()

		if err := s.db.Save(&existing).Error; err != nil {
			return fmt.Errorf("更新税种配置失败: %w", err)
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		// Create new configuration
		config := &model.EnterpriseTaxConfig{
			ID:           uuid.New().String(),
			EnterpriseID: enterpriseID,
			TaxTypeID:    taxTypeID,
			IsActive:     true,
			StartDate:    time.Now(),
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}
		if customRate != nil {
			config.TaxRate = decimal.NewFromFloat(*customRate)
		}

		if err := s.db.Create(config).Error; err != nil {
			return fmt.Errorf("创建税种配置失败: %w", err)
		}
	} else {
		return fmt.Errorf("检查税种配置失败: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, "tax_type_enabled", enterpriseID,
		fmt.Sprintf("为企业启用税种 %s", taxType.Name))

	return nil
}

// DisableTaxTypeForEnterprise disables a tax type for a specific enterprise
func (s *TaxConfigService) DisableTaxTypeForEnterprise(ctx context.Context, enterpriseID, taxTypeID string) error {
	var config model.EnterpriseTaxConfig
	if err := s.db.Where("enterprise_id = ? AND tax_type_id = ?", enterpriseID, taxTypeID).First(&config).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("税种配置不存在")
		}
		return fmt.Errorf("获取税种配置失败: %w", err)
	}

	config.IsActive = false
	now := time.Now()
	config.EndDate = &now
	config.UpdatedAt = now

	if err := s.db.Save(&config).Error; err != nil {
		return fmt.Errorf("禁用税种失败: %w", err)
	}

	// Get tax type name for audit log
	var taxType model.TaxType
	s.db.First(&taxType, "id = ?", taxTypeID)

	// Create audit log
	s.createAuditLog(ctx, "tax_type_disabled", enterpriseID,
		fmt.Sprintf("为企业禁用税种 %s", taxType.Name))

	return nil
}

// GetEnterpriseTaxConfig retrieves tax configuration for an enterprise
func (s *TaxConfigService) GetEnterpriseTaxConfig(ctx context.Context, enterpriseID string) ([]model.EnterpriseTaxConfig, error) {
	var configs []model.EnterpriseTaxConfig
	if err := s.db.Where("enterprise_id = ?", enterpriseID).Find(&configs).Error; err != nil {
		return nil, fmt.Errorf("获取企业税种配置失败: %w", err)
	}
	return configs, nil
}

// createAuditLog creates an audit log entry
func (s *TaxConfigService) createAuditLog(ctx context.Context, action, entityID, description string) {
	auditLog := &model.AuditLog{
		ID:           uuid.New().String(),
		ResourceType: "tax_config",
		ResourceID:   entityID,
		Action:       action,
		Description:  description,
		CreatedAt:    time.Now(),
	}
	s.db.Create(auditLog)
}
