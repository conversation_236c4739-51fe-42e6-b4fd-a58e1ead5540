// Package service provides business logic layer for the tax management system.
// It implements notification management operations including CRUD operations and business validations.
package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// NotificationService defines the interface for notification business logic
type NotificationService interface {
	CreateNotification(ctx context.Context, req model.NotificationCreateRequest) (*model.Notification, error)
	BatchCreateNotifications(ctx context.Context, req model.NotificationBatchCreateRequest) ([]model.Notification, error)
	GetNotifications(ctx context.Context, req GetNotificationsRequest) (*PaginatedResponse, error)
	GetNotificationByID(ctx context.Context, id string) (*model.Notification, error)
	UpdateNotification(ctx context.Context, id string, req UpdateNotificationRequest) (*model.Notification, error)
	DeleteNotification(ctx context.Context, id string) error
	MarkAsRead(ctx context.Context, req model.MarkAsReadRequest) error
	GetNotificationCount(ctx context.Context, recipientID string) (*model.NotificationCountResponse, error)
	GetUnreadNotifications(ctx context.Context, recipientID string, limit int) ([]model.Notification, error)
	DeleteReadNotifications(ctx context.Context, recipientID string) error
}

type notificationService struct {
	db *gorm.DB
}

// NewNotificationService creates a new notification service instance
func NewNotificationService(db *gorm.DB) NotificationService {
	return &notificationService{db: db}
}

// GetNotificationsRequest represents a request to get notifications with pagination and filtering
type GetNotificationsRequest struct {
	RecipientID  string     `json:"recipientId"`
	Type         string     `json:"type"`
	IsRead       *bool      `json:"isRead"`
	CreatedStart *time.Time `json:"createdStart"`
	CreatedEnd   *time.Time `json:"createdEnd"`
	Page         int        `json:"page" binding:"min=1"`
	PageSize     int        `json:"pageSize" binding:"min=1,max=100"`
	SortBy       string     `json:"sortBy"`
	SortOrder    string     `json:"sortOrder"`
}

// UpdateNotificationRequest represents a request to update a notification
type UpdateNotificationRequest struct {
	Title   *string `json:"title"`
	Content *string `json:"content"`
	IsRead  *bool   `json:"isRead"`
}

// CreateNotification creates a new notification
func (s *notificationService) CreateNotification(ctx context.Context, req model.NotificationCreateRequest) (*model.Notification, error) {
	// Validate recipient exists
	var user model.User
	if err := s.db.WithContext(ctx).Where("id = ?", req.RecipientID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("接收者用户不存在")
		}
		return nil, fmt.Errorf("验证接收者失败: %w", err)
	}

	notification := model.Notification{
		ID:                util.GenerateID(),
		RecipientID:       req.RecipientID,
		Type:              req.Type,
		Title:             req.Title,
		Content:           req.Content,
		IsRead:            false,
		RelatedEntityType: req.RelatedEntityType,
		RelatedEntityID:   req.RelatedEntityID,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	if err := s.db.WithContext(ctx).Create(&notification).Error; err != nil {
		return nil, fmt.Errorf("创建通知失败: %w", err)
	}

	// Load the recipient for response
	if err := s.db.WithContext(ctx).Preload("Recipient").Where("id = ?", notification.ID).First(&notification).Error; err != nil {
		return nil, fmt.Errorf("获取创建的通知失败: %w", err)
	}

	return &notification, nil
}

// BatchCreateNotifications creates multiple notifications for different recipients
func (s *notificationService) BatchCreateNotifications(ctx context.Context, req model.NotificationBatchCreateRequest) ([]model.Notification, error) {
	if len(req.RecipientIDs) == 0 {
		return nil, fmt.Errorf("接收者ID列表不能为空")
	}

	// Validate all recipients exist
	var userCount int64
	if err := s.db.WithContext(ctx).Model(&model.User{}).Where("id IN ?", req.RecipientIDs).Count(&userCount).Error; err != nil {
		return nil, fmt.Errorf("验证接收者失败: %w", err)
	}
	if userCount != int64(len(req.RecipientIDs)) {
		return nil, fmt.Errorf("部分接收者用户不存在")
	}

	var notifications []model.Notification
	now := time.Now()

	// Create notifications for each recipient
	for _, recipientID := range req.RecipientIDs {
		notification := model.Notification{
			ID:                util.GenerateID(),
			RecipientID:       recipientID,
			Type:              req.Type,
			Title:             req.Title,
			Content:           req.Content,
			IsRead:            false,
			RelatedEntityType: req.RelatedEntityType,
			RelatedEntityID:   req.RelatedEntityID,
			CreatedAt:         now,
			UpdatedAt:         now,
		}
		notifications = append(notifications, notification)
	}

	// Batch insert
	if err := s.db.WithContext(ctx).Create(&notifications).Error; err != nil {
		return nil, fmt.Errorf("批量创建通知失败: %w", err)
	}

	return notifications, nil
}

// GetNotifications retrieves notifications with pagination and filtering
func (s *notificationService) GetNotifications(ctx context.Context, req GetNotificationsRequest) (*PaginatedResponse, error) {
	var notifications []model.Notification
	var total int64

	// Build query
	query := s.db.WithContext(ctx).Model(&model.Notification{})

	// Apply filters
	if req.RecipientID != "" {
		query = query.Where("recipient_id = ?", req.RecipientID)
	}
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}
	if req.IsRead != nil {
		query = query.Where("is_read = ?", *req.IsRead)
	}
	if req.CreatedStart != nil {
		query = query.Where("created_at >= ?", *req.CreatedStart)
	}
	if req.CreatedEnd != nil {
		query = query.Where("created_at <= ?", *req.CreatedEnd)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取通知总数失败: %w", err)
	}

	// Apply pagination and sorting
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize

	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	orderBy := fmt.Sprintf("%s %s", req.SortBy, req.SortOrder)
	if err := query.Preload("Recipient").
		Order(orderBy).Offset(offset).Limit(req.PageSize).
		Find(&notifications).Error; err != nil {
		return nil, fmt.Errorf("获取通知列表失败: %w", err)
	}

	// 计算总页数
	pages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &PaginatedResponse{
		Items:    notifications,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Pages:    pages,
	}, nil
}

// GetNotificationByID retrieves a notification by ID
func (s *notificationService) GetNotificationByID(ctx context.Context, id string) (*model.Notification, error) {
	var notification model.Notification

	if err := s.db.WithContext(ctx).
		Preload("Recipient").
		Where("id = ?", id).
		First(&notification).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("通知不存在")
		}
		return nil, fmt.Errorf("获取通知详情失败: %w", err)
	}

	return &notification, nil
}

// UpdateNotification updates an existing notification
func (s *notificationService) UpdateNotification(ctx context.Context, id string, req UpdateNotificationRequest) (*model.Notification, error) {
	var notification model.Notification

	// Check if notification exists
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&notification).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("通知不存在")
		}
		return nil, fmt.Errorf("查找通知失败: %w", err)
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if req.Title != nil {
		updates["title"] = *req.Title
	}
	if req.Content != nil {
		updates["content"] = *req.Content
	}
	if req.IsRead != nil {
		updates["is_read"] = *req.IsRead
		if *req.IsRead && notification.ReadAt == nil {
			now := time.Now()
			updates["read_at"] = &now
		}
	}

	updates["updated_at"] = time.Now()

	// Perform update
	if err := s.db.WithContext(ctx).Model(&notification).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("更新通知失败: %w", err)
	}

	// Reload with associations
	if err := s.db.WithContext(ctx).Preload("Recipient").Where("id = ?", id).First(&notification).Error; err != nil {
		return nil, fmt.Errorf("获取更新后的通知失败: %w", err)
	}

	return &notification, nil
}

// DeleteNotification deletes a notification by ID
func (s *notificationService) DeleteNotification(ctx context.Context, id string) error {
	var notification model.Notification

	// Check if notification exists
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&notification).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("通知不存在")
		}
		return fmt.Errorf("查找通知失败: %w", err)
	}

	if err := s.db.WithContext(ctx).Delete(&notification).Error; err != nil {
		return fmt.Errorf("删除通知失败: %w", err)
	}

	return nil
}

// MarkAsRead marks notifications as read
func (s *notificationService) MarkAsRead(ctx context.Context, req model.MarkAsReadRequest) error {
	if len(req.IDs) == 0 {
		return fmt.Errorf("通知ID列表不能为空")
	}

	now := time.Now()
	updates := map[string]interface{}{
		"is_read":    true,
		"read_at":    &now,
		"updated_at": now,
	}

	if err := s.db.WithContext(ctx).Model(&model.Notification{}).
		Where("id IN ?", req.IDs).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("标记通知为已读失败: %w", err)
	}

	return nil
}

// GetNotificationCount gets notification count for a recipient
func (s *notificationService) GetNotificationCount(ctx context.Context, recipientID string) (*model.NotificationCountResponse, error) {
	var total, unread int64

	// Get total count
	if err := s.db.WithContext(ctx).Model(&model.Notification{}).
		Where("recipient_id = ?", recipientID).
		Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取通知总数失败: %w", err)
	}

	// Get unread count
	if err := s.db.WithContext(ctx).Model(&model.Notification{}).
		Where("recipient_id = ? AND is_read = ?", recipientID, false).
		Count(&unread).Error; err != nil {
		return nil, fmt.Errorf("获取未读通知数失败: %w", err)
	}

	return &model.NotificationCountResponse{
		Total:  total,
		Unread: unread,
	}, nil
}

// GetUnreadNotifications gets unread notifications for a recipient
func (s *notificationService) GetUnreadNotifications(ctx context.Context, recipientID string, limit int) ([]model.Notification, error) {
	var notifications []model.Notification

	if limit <= 0 {
		limit = 10
	}

	if err := s.db.WithContext(ctx).
		Where("recipient_id = ? AND is_read = ?", recipientID, false).
		Order("created_at DESC").
		Limit(limit).
		Find(&notifications).Error; err != nil {
		return nil, fmt.Errorf("获取未读通知失败: %w", err)
	}

	return notifications, nil
}

// DeleteReadNotifications deletes all read notifications for a recipient
func (s *notificationService) DeleteReadNotifications(ctx context.Context, recipientID string) error {
	if err := s.db.WithContext(ctx).
		Where("recipient_id = ? AND is_read = ?", recipientID, true).
		Delete(&model.Notification{}).Error; err != nil {
		return fmt.Errorf("删除已读通知失败: %w", err)
	}

	return nil
}
