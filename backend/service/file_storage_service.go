package service

import (
	"crypto/md5"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"backend/model"
	"backend/util"

	"gorm.io/gorm"
)

// FileStorageService provides file storage and management functionality
type FileStorageService struct {
	db          *gorm.DB
	storagePath string
	baseURL     string
}

// NewFileStorageService creates a new file storage service instance
func NewFileStorageService(db *gorm.DB, storagePath, baseURL string) *FileStorageService {
	return &FileStorageService{
		db:          db,
		storagePath: storagePath,
		baseURL:     baseURL,
	}
}

// UploadFile uploads a file and stores it with metadata
func (s *FileStorageService) UploadFile(file *multipart.FileHeader, req *model.FileUploadRequest, userID string) (*model.FileUploadResponse, error) {
	// Open the uploaded file
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer src.Close()

	// Calculate file MD5 hash
	hash := md5.New()
	if _, err := io.Copy(hash, src); err != nil {
		return nil, fmt.Errorf("failed to calculate file hash: %w", err)
	}
	md5Hash := fmt.Sprintf("%x", hash.Sum(nil))

	// Reset file pointer
	src.Seek(0, 0)

	// Check if file already exists
	var existingFile model.FileStorage
	if err := s.db.Where("md5_hash = ?", md5Hash).First(&existingFile).Error; err == nil {
		// File already exists, return existing file info
		return &model.FileUploadResponse{
			ID:           existingFile.ID,
			OriginalName: existingFile.OriginalName,
			FileSize:     existingFile.FileSize,
			MimeType:     existingFile.MimeType,
			DownloadURL:  s.generateDownloadURL(existingFile.ID),
		}, nil
	}

	// Generate file ID and storage path
	fileID := util.GenerateID()
	fileExt := filepath.Ext(file.Filename)
	storedName := fmt.Sprintf("%s%s", fileID, fileExt)

	// Create directory structure by date
	dateDir := time.Now().Format("2006/01/02")
	fullDir := filepath.Join(s.storagePath, dateDir)
	if err := os.MkdirAll(fullDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create storage directory: %w", err)
	}

	// Complete file path
	fullPath := filepath.Join(fullDir, storedName)
	relativePath := filepath.Join(dateDir, storedName)

	// Save file to disk
	dst, err := os.Create(fullPath)
	if err != nil {
		return nil, fmt.Errorf("failed to create file: %w", err)
	}
	defer dst.Close()

	if _, err := io.Copy(dst, src); err != nil {
		return nil, fmt.Errorf("failed to save file: %w", err)
	}

	// Create file record
	fileRecord := &model.FileStorage{
		ID:           fileID,
		OriginalName: file.Filename,
		StoredName:   storedName,
		FilePath:     relativePath,
		FileSize:     file.Size,
		MimeType:     file.Header.Get("Content-Type"),
		MD5Hash:      md5Hash,
		UploadedBy:   userID,
		EnterpriseID: req.EnterpriseID,
		BusinessType: req.BusinessType,
		BusinessID:   req.BusinessID,
		IsPublic:     req.IsPublic,
	}

	if err := s.db.Create(fileRecord).Error; err != nil {
		// Delete the saved file
		os.Remove(fullPath)
		return nil, fmt.Errorf("failed to save file record: %w", err)
	}

	return &model.FileUploadResponse{
		ID:           fileRecord.ID,
		OriginalName: fileRecord.OriginalName,
		FileSize:     fileRecord.FileSize,
		MimeType:     fileRecord.MimeType,
		DownloadURL:  s.generateDownloadURL(fileRecord.ID),
	}, nil
}

// GetFileList retrieves a paginated list of files with optional filters
func (s *FileStorageService) GetFileList(req *model.FileListRequest) (*model.FileListResponse, error) {
	var files []model.FileStorage
	var total int64

	// Build query
	query := s.db.Model(&model.FileStorage{})

	// Add filter conditions
	if req.BusinessType != "" {
		query = query.Where("business_type = ?", req.BusinessType)
	}
	if req.BusinessID != "" {
		query = query.Where("business_id = ?", req.BusinessID)
	}
	if req.EnterpriseID != "" {
		query = query.Where("enterprise_id = ?", req.EnterpriseID)
	}
	if req.MimeType != "" {
		query = query.Where("mime_type LIKE ?", req.MimeType+"%")
	}
	if req.Keyword != "" {
		query = query.Where("original_name LIKE ?", "%"+req.Keyword+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to get file count: %w", err)
	}

	// Paginated query
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).
		Order("created_at DESC").Find(&files).Error; err != nil {
		return nil, fmt.Errorf("failed to query file list: %w", err)
	}

	// Convert to response format
	fileInfos := make([]model.FileInfo, len(files))
	for i, file := range files {
		fileInfos[i] = model.FileInfo{
			ID:            file.ID,
			OriginalName:  file.OriginalName,
			FileSize:      file.FileSize,
			MimeType:      file.MimeType,
			UploadedBy:    file.UploadedBy,
			EnterpriseID:  file.EnterpriseID,
			BusinessType:  file.BusinessType,
			BusinessID:    file.BusinessID,
			IsPublic:      file.IsPublic,
			DownloadCount: file.DownloadCount,
			DownloadURL:   s.generateDownloadURL(file.ID),
			CreatedAt:     file.CreatedAt,
		}
	}

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &model.FileListResponse{
		Files:      fileInfos,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetFileByID retrieves file information by ID
func (s *FileStorageService) GetFileByID(fileID string) (*model.FileStorage, error) {
	var file model.FileStorage
	if err := s.db.Where("id = ?", fileID).First(&file).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("file not found")
		}
		return nil, fmt.Errorf("failed to query file: %w", err)
	}
	return &file, nil
}

// DownloadFile downloads a file and returns the file path
func (s *FileStorageService) DownloadFile(fileID string) (string, error) {
	file, err := s.GetFileByID(fileID)
	if err != nil {
		return "", err
	}

	// Build complete file path
	fullPath := filepath.Join(s.storagePath, file.FilePath)

	// Check if file exists on disk
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return "", fmt.Errorf("file does not exist on disk")
	}

	// Update download count
	s.db.Model(&model.FileStorage{}).Where("id = ?", fileID).
		UpdateColumn("download_count", gorm.Expr("download_count + 1"))

	return fullPath, nil
}

// DeleteFile deletes a file (soft delete)
func (s *FileStorageService) DeleteFile(fileID, userID string) error {
	file, err := s.GetFileByID(fileID)
	if err != nil {
		return err
	}

	// Check permissions (only uploader or admin can delete)
	// More complex permission checks can be added based on business requirements
	if file.UploadedBy != userID {
		return fmt.Errorf("no permission to delete this file")
	}

	// Soft delete file record
	if err := s.db.Delete(&model.FileStorage{}, "id = ?", fileID).Error; err != nil {
		return fmt.Errorf("failed to delete file record: %w", err)
	}

	// Optional: delete physical file (depends on business requirements)
	// fullPath := filepath.Join(s.storagePath, file.FilePath)
	// os.Remove(fullPath)

	return nil
}

// generateDownloadURL generates a download URL for the file
func (s *FileStorageService) generateDownloadURL(fileID string) string {
	return fmt.Sprintf("%s/api/files/%s/download", strings.TrimRight(s.baseURL, "/"), fileID)
}

// GetFilesByBusinessID retrieves files by business type and ID
func (s *FileStorageService) GetFilesByBusinessID(businessType, businessID string) ([]model.FileStorage, error) {
	var files []model.FileStorage
	if err := s.db.Where("business_type = ? AND business_id = ?", businessType, businessID).
		Order("created_at DESC").Find(&files).Error; err != nil {
		return nil, fmt.Errorf("failed to query business files: %w", err)
	}
	return files, nil
}

// CleanupOrphanedFiles cleans up orphaned files (scheduled task)
func (s *FileStorageService) CleanupOrphanedFiles() error {
	// Find soft-deleted files older than 30 days
	var deletedFiles []model.FileStorage
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)

	if err := s.db.Unscoped().Where("deleted_at IS NOT NULL AND deleted_at < ?", thirtyDaysAgo).
		Find(&deletedFiles).Error; err != nil {
		return fmt.Errorf("failed to query deleted files: %w", err)
	}

	// Delete physical files and database records
	for _, file := range deletedFiles {
		fullPath := filepath.Join(s.storagePath, file.FilePath)
		if err := os.Remove(fullPath); err != nil {
			// Log error but continue processing other files
			fmt.Printf("Failed to delete physical file: %s, error: %v\n", fullPath, err)
		}

		// Permanently delete database record
		if err := s.db.Unscoped().Delete(&model.FileStorage{}, "id = ?", file.ID).Error; err != nil {
			fmt.Printf("Failed to permanently delete file record: %s, error: %v\n", file.ID, err)
		}
	}

	return nil
}
