package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"backend/client"
	"backend/model"
)

// TaxFilingSyncService 税务申报同步服务
type TaxFilingSyncService struct {
	db                  *gorm.DB
	logger              *zap.Logger
	taxFilingClient     *client.TaxFilingClient
	taxFilingService    *TaxFilingService
	redisService        *RedisService
	notificationService *TaxFilingNotificationService

	// 同步控制
	syncInterval time.Duration
	stopChan     chan struct{}
	wg           sync.WaitGroup
	isRunning    bool
	mutex        sync.RWMutex
}

// SyncConfig 同步配置
type SyncConfig struct {
	Enabled    bool          `json:"enabled"`
	Interval   time.Duration `json:"interval"`
	BatchSize  int           `json:"batch_size"`
	MaxRetries int           `json:"max_retries"`
	RetryDelay time.Duration `json:"retry_delay"`
	Timeout    time.Duration `json:"timeout"`
}

// NewTaxFilingSyncService 创建税务申报同步服务
func NewTaxFilingSyncService(
	db *gorm.DB,
	logger *zap.Logger,
	taxFilingClient *client.TaxFilingClient,
	taxFilingService *TaxFilingService,
	redisService *RedisService,
	notificationService *TaxFilingNotificationService,
) *TaxFilingSyncService {
	return &TaxFilingSyncService{
		db:                  db,
		logger:              logger,
		taxFilingClient:     taxFilingClient,
		taxFilingService:    taxFilingService,
		redisService:        redisService,
		notificationService: notificationService,
		syncInterval:        5 * time.Minute, // 默认5分钟同步一次
		stopChan:            make(chan struct{}),
	}
}

// Start 启动同步服务
func (s *TaxFilingSyncService) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("sync service is already running")
	}

	// 获取同步配置
	config, err := s.getSyncConfig(ctx)
	if err != nil {
		s.logger.Error("Failed to get sync config", zap.Error(err))
		return err
	}

	if !config.Enabled {
		s.logger.Info("Sync service is disabled")
		return nil
	}

	s.syncInterval = config.Interval
	s.isRunning = true

	// 启动同步协程
	s.wg.Add(1)
	go s.syncLoop(ctx, config)

	s.logger.Info("Tax filing sync service started",
		zap.Duration("interval", s.syncInterval),
		zap.Int("batch_size", config.BatchSize),
	)

	return nil
}

// Stop 停止同步服务
func (s *TaxFilingSyncService) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return fmt.Errorf("sync service is not running")
	}

	close(s.stopChan)
	s.wg.Wait()
	s.isRunning = false

	s.logger.Info("Tax filing sync service stopped")
	return nil
}

// IsRunning 检查同步服务是否运行中
func (s *TaxFilingSyncService) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

// syncLoop 同步循环
func (s *TaxFilingSyncService) syncLoop(ctx context.Context, config *SyncConfig) {
	defer s.wg.Done()

	ticker := time.NewTicker(s.syncInterval)
	defer ticker.Stop()

	s.logger.Info("Sync loop started")

	for {
		select {
		case <-s.stopChan:
			s.logger.Info("Sync loop stopped")
			return
		case <-ticker.C:
			s.performSync(ctx, config)
		}
	}
}

// performSync 执行同步
func (s *TaxFilingSyncService) performSync(ctx context.Context, config *SyncConfig) {
	s.logger.Debug("Starting sync cycle")

	// 同步待处理的申报
	if err := s.syncPendingSubmissions(ctx, config); err != nil {
		s.logger.Error("Failed to sync pending submissions", zap.Error(err))
	}

	// 同步处理中的申报
	if err := s.syncProcessingSubmissions(ctx, config); err != nil {
		s.logger.Error("Failed to sync processing submissions", zap.Error(err))
	}

	// 同步已提交的申报
	if err := s.syncSubmittedSubmissions(ctx, config); err != nil {
		s.logger.Error("Failed to sync submitted submissions", zap.Error(err))
	}

	// 清理过期的锁
	if err := s.cleanupExpiredLocks(ctx); err != nil {
		s.logger.Error("Failed to cleanup expired locks", zap.Error(err))
	}

	s.logger.Debug("Sync cycle completed")
}

// syncPendingSubmissions 同步待处理的申报
func (s *TaxFilingSyncService) syncPendingSubmissions(ctx context.Context, config *SyncConfig) error {
	// 获取待处理的申报（超过一定时间未处理的）
	var submissions []model.TaxFilingSubmission
	cutoffTime := time.Now().Add(-10 * time.Minute) // 10分钟前创建但未处理的

	if err := s.db.Where("status = ? AND created_at < ? AND next_retry_at IS NULL OR next_retry_at <= ?",
		model.TaxFilingStatusPending, cutoffTime, time.Now()).
		Limit(config.BatchSize).
		Find(&submissions).Error; err != nil {
		return fmt.Errorf("failed to get pending submissions: %w", err)
	}

	if len(submissions) == 0 {
		return nil
	}

	s.logger.Info("Found pending submissions to sync", zap.Int("count", len(submissions)))

	// 并发处理
	semaphore := make(chan struct{}, 5) // 限制并发数
	var wg sync.WaitGroup

	for _, submission := range submissions {
		wg.Add(1)
		go func(sub model.TaxFilingSubmission) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 获取分布式锁
			lockKey := fmt.Sprintf("submission_sync_%s", sub.ID)
			acquired, err := s.redisService.AcquireLock(ctx, lockKey, 5*time.Minute)
			if err != nil || !acquired {
				return
			}
			defer s.redisService.ReleaseLock(ctx, lockKey)

			// 自动提交到税务局
			if err := s.taxFilingService.SubmitToTaxBureau(ctx, sub.ID); err != nil {
				s.logger.Error("Failed to auto-submit pending submission",
					zap.Error(err),
					zap.String("submission_id", sub.ID),
				)
			}
		}(submission)
	}

	wg.Wait()
	return nil
}

// syncProcessingSubmissions 同步处理中的申报
func (s *TaxFilingSyncService) syncProcessingSubmissions(ctx context.Context, config *SyncConfig) error {
	// 获取处理中的申报
	var submissions []model.TaxFilingSubmission
	if err := s.db.Where("status = ? AND external_id IS NOT NULL",
		model.TaxFilingStatusProcessing).
		Limit(config.BatchSize).
		Find(&submissions).Error; err != nil {
		return fmt.Errorf("failed to get processing submissions: %w", err)
	}

	if len(submissions) == 0 {
		return nil
	}

	s.logger.Debug("Syncing processing submissions", zap.Int("count", len(submissions)))

	// 并发同步状态
	semaphore := make(chan struct{}, 10) // 限制并发数
	var wg sync.WaitGroup

	for _, submission := range submissions {
		wg.Add(1)
		go func(sub model.TaxFilingSubmission) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 获取分布式锁
			lockKey := fmt.Sprintf("submission_sync_%s", sub.ID)
			acquired, err := s.redisService.AcquireLock(ctx, lockKey, 2*time.Minute)
			if err != nil || !acquired {
				return
			}
			defer s.redisService.ReleaseLock(ctx, lockKey)

			// 同步状态
			if err := s.taxFilingService.SyncSubmissionStatus(ctx, sub.ID); err != nil {
				s.logger.Error("Failed to sync submission status",
					zap.Error(err),
					zap.String("submission_id", sub.ID),
				)
			}
		}(submission)
	}

	wg.Wait()
	return nil
}

// syncSubmittedSubmissions 同步已提交的申报
func (s *TaxFilingSyncService) syncSubmittedSubmissions(ctx context.Context, config *SyncConfig) error {
	// 获取已提交但未完成的申报（超过一定时间的）
	var submissions []model.TaxFilingSubmission
	cutoffTime := time.Now().Add(-30 * time.Minute) // 30分钟前提交但未完成的

	if err := s.db.Where("status = ? AND submitted_at < ? AND external_id IS NOT NULL",
		model.TaxFilingStatusSubmitted, cutoffTime).
		Limit(config.BatchSize).
		Find(&submissions).Error; err != nil {
		return fmt.Errorf("failed to get submitted submissions: %w", err)
	}

	if len(submissions) == 0 {
		return nil
	}

	s.logger.Debug("Syncing submitted submissions", zap.Int("count", len(submissions)))

	// 并发同步状态
	semaphore := make(chan struct{}, 10)
	var wg sync.WaitGroup

	for _, submission := range submissions {
		wg.Add(1)
		go func(sub model.TaxFilingSubmission) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 获取分布式锁
			lockKey := fmt.Sprintf("submission_sync_%s", sub.ID)
			acquired, err := s.redisService.AcquireLock(ctx, lockKey, 2*time.Minute)
			if err != nil || !acquired {
				return
			}
			defer s.redisService.ReleaseLock(ctx, lockKey)

			// 同步状态
			if err := s.taxFilingService.SyncSubmissionStatus(ctx, sub.ID); err != nil {
				s.logger.Error("Failed to sync submitted submission status",
					zap.Error(err),
					zap.String("submission_id", sub.ID),
				)
			}
		}(submission)
	}

	wg.Wait()
	return nil
}

// SyncSubmissionManually 手动同步单个申报
func (s *TaxFilingSyncService) SyncSubmissionManually(ctx context.Context, submissionID string) error {
	// 获取分布式锁
	lockKey := fmt.Sprintf("submission_sync_%s", submissionID)
	acquired, err := s.redisService.AcquireLock(ctx, lockKey, 5*time.Minute)
	if err != nil {
		return fmt.Errorf("failed to acquire lock: %w", err)
	}
	if !acquired {
		return fmt.Errorf("submission is being synced by another process")
	}
	defer s.redisService.ReleaseLock(ctx, lockKey)

	// 同步状态
	if err := s.taxFilingService.SyncSubmissionStatus(ctx, submissionID); err != nil {
		s.logger.Error("Failed to manually sync submission status",
			zap.Error(err),
			zap.String("submission_id", submissionID),
		)
		return err
	}

	s.logger.Info("Submission synced manually", zap.String("submission_id", submissionID))
	return nil
}

// SyncBatchManually 手动同步批次中的所有申报
func (s *TaxFilingSyncService) SyncBatchManually(ctx context.Context, batchID string) error {
	// 获取批次中的所有申报
	var submissions []model.TaxFilingSubmission
	if err := s.db.Where("batch_id = ? AND external_id IS NOT NULL", batchID).
		Find(&submissions).Error; err != nil {
		return fmt.Errorf("failed to get batch submissions: %w", err)
	}

	if len(submissions) == 0 {
		return fmt.Errorf("no submissions found in batch")
	}

	s.logger.Info("Manually syncing batch submissions",
		zap.String("batch_id", batchID),
		zap.Int("count", len(submissions)),
	)

	// 并发同步
	semaphore := make(chan struct{}, 5)
	var wg sync.WaitGroup
	var errors []error
	var mutex sync.Mutex

	for _, submission := range submissions {
		wg.Add(1)
		go func(sub model.TaxFilingSubmission) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			if err := s.SyncSubmissionManually(ctx, sub.ID); err != nil {
				mutex.Lock()
				errors = append(errors, err)
				mutex.Unlock()
			}
		}(submission)
	}

	wg.Wait()

	if len(errors) > 0 {
		return fmt.Errorf("failed to sync %d submissions", len(errors))
	}

	return nil
}

// cleanupExpiredLocks 清理过期的锁
func (s *TaxFilingSyncService) cleanupExpiredLocks(ctx context.Context) error {
	// Redis会自动清理过期的键，这里主要是记录日志
	s.logger.Debug("Cleanup expired locks completed")
	return nil
}

// getSyncConfig 获取同步配置
func (s *TaxFilingSyncService) getSyncConfig(ctx context.Context) (*SyncConfig, error) {
	// 先从缓存获取
	var config SyncConfig
	if err := s.redisService.GetConfig(ctx, "tax_filing_sync", &config); err == nil {
		return &config, nil
	}

	// 返回默认配置
	config = SyncConfig{
		Enabled:    true,
		Interval:   5 * time.Minute,
		BatchSize:  50,
		MaxRetries: 3,
		RetryDelay: time.Minute,
		Timeout:    30 * time.Second,
	}

	// 缓存配置
	if err := s.redisService.SetConfig(ctx, "tax_filing_sync", config); err != nil {
		s.logger.Warn("Failed to cache sync config", zap.Error(err))
	}

	return &config, nil
}

// UpdateSyncConfig 更新同步配置
func (s *TaxFilingSyncService) UpdateSyncConfig(ctx context.Context, config *SyncConfig) error {
	// 更新缓存
	if err := s.redisService.SetConfig(ctx, "tax_filing_sync", config); err != nil {
		s.logger.Error("Failed to update sync config", zap.Error(err))
		return err
	}

	// 如果服务正在运行，重启以应用新配置
	if s.IsRunning() {
		s.logger.Info("Restarting sync service with new config")
		if err := s.Stop(); err != nil {
			return err
		}
		return s.Start(ctx)
	}

	s.logger.Info("Sync config updated successfully")
	return nil
}

// GetSyncStatistics 获取同步统计信息
func (s *TaxFilingSyncService) GetSyncStatistics(ctx context.Context) (*SyncStatistics, error) {
	stats := &SyncStatistics{
		IsRunning:    s.IsRunning(),
		LastSyncTime: time.Now(), // 这里应该从缓存或数据库获取实际的最后同步时间
	}

	// 获取待同步的申报数量
	var pendingCount, processingCount, submittedCount int64

	s.db.Model(&model.TaxFilingSubmission{}).
		Where("status = ?", model.TaxFilingStatusPending).
		Count(&pendingCount)

	s.db.Model(&model.TaxFilingSubmission{}).
		Where("status = ?", model.TaxFilingStatusProcessing).
		Count(&processingCount)

	s.db.Model(&model.TaxFilingSubmission{}).
		Where("status = ?", model.TaxFilingStatusSubmitted).
		Count(&submittedCount)

	stats.PendingCount = pendingCount
	stats.ProcessingCount = processingCount
	stats.SubmittedCount = submittedCount

	return stats, nil
}

// SyncStatistics 同步统计信息
type SyncStatistics struct {
	IsRunning       bool      `json:"is_running"`
	LastSyncTime    time.Time `json:"last_sync_time"`
	PendingCount    int64     `json:"pending_count"`
	ProcessingCount int64     `json:"processing_count"`
	SubmittedCount  int64     `json:"submitted_count"`
	SyncInterval    string    `json:"sync_interval"`
}
