package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"backend/config"
	"backend/model"
)

// PermissionService handles permission and role management
type PermissionService struct {
	db     *gorm.DB
	config *config.Config
}

// NewPermissionService creates a new permission service
func NewPermissionService(db *gorm.DB, cfg *config.Config) *PermissionService {
	return &PermissionService{
		db:     db,
		config: cfg,
	}
}

// CreateRole creates a new role with specified permissions
func (s *PermissionService) CreateRole(ctx context.Context, req model.RoleCreateRequest) (*model.Role, error) {
	// Check if role code already exists
	var existing model.Role
	if err := s.db.Where("code = ?", req.Code).First(&existing).Error; err == nil {
		return nil, errors.New("角色代码已存在")
	}

	role := &model.Role{
		ID:          uuid.New().String(),
		Code:        req.Code,
		Name:        req.Name,
		Description: req.Description,
		Type:        req.Type,
		Level:       req.Level,
		IsActive:    req.IsActive,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.db.Create(role).Error; err != nil {
		return nil, fmt.Errorf("创建角色失败: %w", err)
	}

	// Assign permissions to role if provided
	if len(req.PermissionIDs) > 0 {
		for _, permissionID := range req.PermissionIDs {
			rolePermission := &model.RolePermission{
				ID:           uuid.New().String(),
				RoleID:       role.ID,
				PermissionID: permissionID,
				CreatedAt:    time.Now(),
			}
			s.db.Create(rolePermission)
		}
	}

	// Create audit log
	s.createAuditLog(ctx, "role_created", role.ID, fmt.Sprintf("角色 %s 已创建", role.Name))

	return role, nil
}

// UpdateRole updates an existing role
func (s *PermissionService) UpdateRole(ctx context.Context, id string, req model.RoleUpdateRequest) (*model.Role, error) {
	var role model.Role
	if err := s.db.First(&role, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("角色不存在")
		}
		return nil, fmt.Errorf("获取角色信息失败: %w", err)
	}

	// Update fields
	if req.Name != "" {
		role.Name = req.Name
	}
	if req.Description != "" {
		role.Description = req.Description
	}
	if req.Type != "" {
		role.Type = req.Type
	}
	if req.Level != nil {
		role.Level = *req.Level
	}
	if req.IsActive != nil {
		role.IsActive = *req.IsActive
	}
	role.UpdatedAt = time.Now()

	if err := s.db.Save(&role).Error; err != nil {
		return nil, fmt.Errorf("更新角色失败: %w", err)
	}

	// Update role permissions if provided
	if req.PermissionIDs != nil {
		// Delete existing permissions
		s.db.Where("role_id = ?", role.ID).Delete(&model.RolePermission{})

		// Add new permissions
		for _, permissionID := range req.PermissionIDs {
			rolePermission := &model.RolePermission{
				ID:           uuid.New().String(),
				RoleID:       role.ID,
				PermissionID: permissionID,
				CreatedAt:    time.Now(),
			}
			s.db.Create(rolePermission)
		}
	}

	// Create audit log
	s.createAuditLog(ctx, "role_updated", role.ID, fmt.Sprintf("角色 %s 已更新", role.Name))

	return &role, nil
}

// AssignRoleToUser assigns a role to a user
func (s *PermissionService) AssignRoleToUser(ctx context.Context, userID, roleID string, assignedBy string) error {
	// Verify user and role exist
	var user model.User
	if err := s.db.First(&user, "id = ?", userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("获取用户信息失败: %w", err)
	}

	var role model.Role
	if err := s.db.First(&role, "id = ?", roleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return fmt.Errorf("获取角色信息失败: %w", err)
	}

	// Check if assignment already exists
	var existing model.UserRole
	err := s.db.Where("user_id = ? AND role_id = ?", userID, roleID).First(&existing).Error
	if err == nil {
		return errors.New("用户已拥有该角色")
	}

	// Create user role assignment
	userRole := &model.UserRole{
		ID:         uuid.New().String(),
		UserID:     userID,
		RoleID:     roleID,
		AssignedBy: assignedBy,
		AssignedAt: time.Now(),
		IsActive:   true,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	if err := s.db.Create(userRole).Error; err != nil {
		return fmt.Errorf("分配角色失败: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, "role_assigned", userID,
		fmt.Sprintf("用户 %s 被分配角色 %s", user.UserName, role.Name))

	return nil
}

// RevokeRoleFromUser revokes a role from a user
func (s *PermissionService) RevokeRoleFromUser(ctx context.Context, userID, roleID string, revokedBy string) error {
	var userRole model.UserRole
	if err := s.db.Where("user_id = ? AND role_id = ? AND is_active = ?",
		userID, roleID, true).First(&userRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户角色分配不存在")
		}
		return fmt.Errorf("获取用户角色信息失败: %w", err)
	}

	// Soft delete - mark as inactive
	userRole.IsActive = false
	userRole.RevokedBy = revokedBy
	now := time.Now()
	userRole.RevokedAt = &now
	userRole.UpdatedAt = now

	if err := s.db.Save(&userRole).Error; err != nil {
		return fmt.Errorf("撤销角色失败: %w", err)
	}

	// Get user and role info for audit log
	var user model.User
	var role model.Role
	s.db.First(&user, "id = ?", userID)
	s.db.First(&role, "id = ?", roleID)

	// Create audit log
	s.createAuditLog(ctx, "role_revoked", userID,
		fmt.Sprintf("用户 %s 的角色 %s 已被撤销", user.UserName, role.Name))

	return nil
}

// CheckPermission checks if a user has a specific permission
func (s *PermissionService) CheckPermission(ctx context.Context, userID string, resource string, action string) (bool, error) {
	// 构建权限代码
	permissionCode := resource + "." + action

	// 获取用户在所有企业中的活跃角色
	var enterpriseUsers []model.EnterpriseUser
	if err := s.db.WithContext(ctx).Where("user_id = ? AND status = ?", userID, model.EnterpriseUserStatusActive).Find(&enterpriseUsers).Error; err != nil {
		return false, fmt.Errorf("获取用户企业关联失败: %w", err)
	}

	if len(enterpriseUsers) == 0 {
		return false, nil // No enterprise associations
	}

	// 获取所有角色ID
	var roleIDs []string
	for _, eu := range enterpriseUsers {
		roleIDs = append(roleIDs, eu.RoleID)
	}

	// 检查是否有任何角色拥有所需权限
	var count int64
	err := s.db.WithContext(ctx).Table("role_permissions rp").
		Joins("JOIN permissions p ON rp.permission_id = p.id").
		Where("rp.role_id IN ? AND p.permission_code = ?", roleIDs, permissionCode).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("检查权限失败: %w", err)
	}

	return count > 0, nil
}

// CheckEnterprisePermission 检查用户在特定企业的权限
func (s *PermissionService) CheckEnterprisePermission(ctx context.Context, userID, enterpriseID, resource, action string) (bool, error) {
	// 首先检查是否为企业所有者（所有者拥有所有权限）
	if enterpriseID != "" {
		isOwner, err := s.checkEnterpriseOwnership(ctx, userID, enterpriseID)
		if err != nil {
			return false, err
		}
		if isOwner {
			return true, nil
		}
	}

	// 构建权限代码
	permissionCode := resource + "." + action

	// 检查角色权限
	hasRolePermission, err := s.checkRolePermission(ctx, userID, enterpriseID, permissionCode)
	if err != nil {
		return false, err
	}
	if hasRolePermission {
		return true, nil
	}

	// 检查用户特殊权限
	hasUserPermission, err := s.checkUserSpecialPermission(ctx, userID, enterpriseID, permissionCode)
	if err != nil {
		return false, err
	}

	return hasUserPermission, nil
}

// CheckEnterpriseOwnership 检查企业所有权（公共方法）
func (s *PermissionService) CheckEnterpriseOwnership(ctx context.Context, userID, enterpriseID string) (bool, error) {
	return s.checkEnterpriseOwnership(ctx, userID, enterpriseID)
}

// checkEnterpriseOwnership 检查企业所有权
func (s *PermissionService) checkEnterpriseOwnership(ctx context.Context, userID, enterpriseID string) (bool, error) {
	var enterprise model.Enterprise
	err := s.db.WithContext(ctx).Where("id = ? AND owner_id = ?", enterpriseID, userID).First(&enterprise).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// checkRolePermission 检查角色权限
func (s *PermissionService) checkRolePermission(ctx context.Context, userID, enterpriseID, permissionCode string) (bool, error) {
	var count int64
	query := s.db.WithContext(ctx).Model(&model.EnterpriseUser{}).
		Joins("JOIN role_permissions ON enterprise_users.role_id = role_permissions.role_id").
		Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
		Where("enterprise_users.user_id = ? AND permissions.permission_code = ? AND enterprise_users.status = ?",
			userID, permissionCode, model.EnterpriseUserStatusActive)

	if enterpriseID != "" {
		query = query.Where("enterprise_users.enterprise_id = ?", enterpriseID)
	}

	err := query.Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// checkUserSpecialPermission 检查用户特殊权限
func (s *PermissionService) checkUserSpecialPermission(ctx context.Context, userID, enterpriseID, permissionCode string) (bool, error) {
	var count int64
	query := s.db.WithContext(ctx).Model(&model.UserPermission{}).
		Joins("JOIN permissions ON user_permissions.permission_id = permissions.id").
		Where("user_permissions.user_id = ? AND permissions.permission_code = ? AND user_permissions.is_active = ?",
			userID, permissionCode, true).
		Where("(user_permissions.expires_at IS NULL OR user_permissions.expires_at > NOW())")

	if enterpriseID != "" {
		query = query.Where("user_permissions.enterprise_id = ?", enterpriseID)
	}

	err := query.Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// GetUserPermissions gets all permissions for a user
func (s *PermissionService) GetUserPermissions(ctx context.Context, userID string) ([]model.Permission, error) {
	var permissions []model.Permission

	// Get user's active roles
	var userRoles []model.UserRole
	if err := s.db.Where("user_id = ? AND is_active = ?", userID, true).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("获取用户角色失败: %w", err)
	}

	if len(userRoles) == 0 {
		return permissions, nil // No roles assigned
	}

	// Get role IDs
	var roleIDs []string
	for _, userRole := range userRoles {
		roleIDs = append(roleIDs, userRole.RoleID)
	}

	// Get distinct permissions from all roles
	err := s.db.Table("permissions p").
		Joins("JOIN role_permissions rp ON p.id = rp.permission_id").
		Where("rp.role_id IN ?", roleIDs).
		Distinct("p.*").
		Find(&permissions).Error

	if err != nil {
		return nil, fmt.Errorf("获取用户权限失败: %w", err)
	}

	return permissions, nil
}

// GetUserRoles gets all active roles for a user
func (s *PermissionService) GetUserRoles(ctx context.Context, userID string) ([]model.Role, error) {
	var roles []model.Role

	err := s.db.Table("roles r").
		Joins("JOIN user_roles ur ON r.id = ur.role_id").
		Where("ur.user_id = ? AND ur.is_active = ?", userID, true).
		Find(&roles).Error

	if err != nil {
		return nil, fmt.Errorf("获取用户角色失败: %w", err)
	}

	return roles, nil
}

// CreatePermission creates a new permission
func (s *PermissionService) CreatePermission(ctx context.Context, req model.PermissionCreateRequest) (*model.Permission, error) {
	// Check if permission already exists
	var existing model.Permission
	if err := s.db.Where("resource = ? AND action = ?", req.Resource, req.Action).First(&existing).Error; err == nil {
		return nil, errors.New("权限已存在")
	}

	permission := &model.Permission{
		ID:          uuid.New().String(),
		Code:        req.Code,
		Name:        req.Name,
		Description: req.Description,
		Resource:    req.Resource,
		Action:      req.Action,
		Type:        req.Type,
		IsActive:    req.IsActive,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.db.Create(permission).Error; err != nil {
		return nil, fmt.Errorf("创建权限失败: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, "permission_created", permission.ID,
		fmt.Sprintf("权限 %s 已创建", permission.Name))

	return permission, nil
}

// ListRoles lists all roles with optional filtering
func (s *PermissionService) ListRoles(ctx context.Context, filters map[string]interface{}) ([]model.Role, error) {
	var roles []model.Role
	query := s.db.Model(&model.Role{})

	// Apply filters
	if roleType, ok := filters["type"].(string); ok && roleType != "" {
		query = query.Where("type = ?", roleType)
	}
	if isActive, ok := filters["is_active"].(bool); ok {
		query = query.Where("is_active = ?", isActive)
	}
	if level, ok := filters["level"].(int); ok && level > 0 {
		query = query.Where("level = ?", level)
	}

	if err := query.Order("level ASC, created_at DESC").Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("获取角色列表失败: %w", err)
	}

	return roles, nil
}

// ListPermissions lists all permissions with optional filtering
func (s *PermissionService) ListPermissions(ctx context.Context, filters map[string]interface{}) ([]model.Permission, error) {
	var permissions []model.Permission
	query := s.db.Model(&model.Permission{})

	// Apply filters
	if resource, ok := filters["resource"].(string); ok && resource != "" {
		query = query.Where("resource = ?", resource)
	}
	if permType, ok := filters["type"].(string); ok && permType != "" {
		query = query.Where("type = ?", permType)
	}
	if isActive, ok := filters["is_active"].(bool); ok {
		query = query.Where("is_active = ?", isActive)
	}

	if err := query.Order("resource ASC, action ASC").Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("获取权限列表失败: %w", err)
	}

	return permissions, nil
}

// GetRolePermissions gets all permissions assigned to a role
func (s *PermissionService) GetRolePermissions(ctx context.Context, roleID string) ([]model.Permission, error) {
	var permissions []model.Permission

	err := s.db.Table("permissions p").
		Joins("JOIN role_permissions rp ON p.id = rp.permission_id").
		Where("rp.role_id = ?", roleID).
		Find(&permissions).Error

	if err != nil {
		return nil, fmt.Errorf("获取角色权限失败: %w", err)
	}

	return permissions, nil
}

// ValidateAccessControl validates field-level access control
func (s *PermissionService) ValidateAccessControl(ctx context.Context, userID string, resource string, field string, operation string) (bool, error) {
	// Check basic permission first
	hasPermission, err := s.CheckPermission(ctx, userID, resource, operation)
	if err != nil {
		return false, err
	}

	if !hasPermission {
		return false, nil
	}

	// Check field-level permissions
	fieldPermissionCode := fmt.Sprintf("%s.%s.%s", resource, field, operation)

	var count int64
	err = s.db.Table("permissions p").
		Joins("JOIN role_permissions rp ON p.id = rp.permission_id").
		Joins("JOIN user_roles ur ON rp.role_id = ur.role_id").
		Where("ur.user_id = ? AND ur.is_active = ? AND p.permission_code = ?", userID, true, fieldPermissionCode).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("检查字段级权限失败: %w", err)
	}

	return count > 0, nil
}

// InitializeDefaultRoles initializes default roles and permissions
func (s *PermissionService) InitializeDefaultRoles(ctx context.Context) error {
	// Create default permissions
	defaultPermissions := s.getDefaultPermissions()
	for _, perm := range defaultPermissions {
		var existing model.Permission
		if err := s.db.Where("code = ?", perm.Code).First(&existing).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				perm.ID = uuid.New().String()
				perm.CreatedAt = time.Now()
				perm.UpdatedAt = time.Now()
				s.db.Create(&perm)
			}
		}
	}

	// Create default roles
	defaultRoles := s.getDefaultRoles()
	for _, role := range defaultRoles {
		var existing model.Role
		if err := s.db.Where("code = ?", role.Code).First(&existing).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				role.ID = uuid.New().String()
				role.CreatedAt = time.Now()
				role.UpdatedAt = time.Now()
				s.db.Create(&role)

				// Assign default permissions to role
				s.assignDefaultPermissionsToRole(role.Code, role.ID)
			}
		}
	}

	return nil
}

// Helper methods

func (s *PermissionService) getDefaultPermissions() []model.Permission {
	return []model.Permission{
		// Enterprise management permissions
		{Code: "enterprise.create", Name: "创建企业", Resource: "enterprise", Action: "create", Type: "operation", IsActive: true},
		{Code: "enterprise.read", Name: "查看企业", Resource: "enterprise", Action: "read", Type: "operation", IsActive: true},
		{Code: "enterprise.update", Name: "更新企业", Resource: "enterprise", Action: "update", Type: "operation", IsActive: true},
		{Code: "enterprise.delete", Name: "删除企业", Resource: "enterprise", Action: "delete", Type: "operation", IsActive: true},

		// Tax configuration permissions
		{Code: "tax_config.create", Name: "创建税种配置", Resource: "tax_config", Action: "create", Type: "operation", IsActive: true},
		{Code: "tax_config.read", Name: "查看税种配置", Resource: "tax_config", Action: "read", Type: "operation", IsActive: true},
		{Code: "tax_config.update", Name: "更新税种配置", Resource: "tax_config", Action: "update", Type: "operation", IsActive: true},
		{Code: "tax_config.delete", Name: "删除税种配置", Resource: "tax_config", Action: "delete", Type: "operation", IsActive: true},

		// Invoice management permissions
		{Code: "invoice.create", Name: "创建发票", Resource: "invoice", Action: "create", Type: "operation", IsActive: true},
		{Code: "invoice.read", Name: "查看发票", Resource: "invoice", Action: "read", Type: "operation", IsActive: true},
		{Code: "invoice.update", Name: "更新发票", Resource: "invoice", Action: "update", Type: "operation", IsActive: true},
		{Code: "invoice.delete", Name: "删除发票", Resource: "invoice", Action: "delete", Type: "operation", IsActive: true},
		{Code: "invoice.verify", Name: "验证发票", Resource: "invoice", Action: "verify", Type: "operation", IsActive: true},

		// Declaration management permissions
		{Code: "declaration.create", Name: "创建申报", Resource: "declaration", Action: "create", Type: "operation", IsActive: true},
		{Code: "declaration.read", Name: "查看申报", Resource: "declaration", Action: "read", Type: "operation", IsActive: true},
		{Code: "declaration.update", Name: "更新申报", Resource: "declaration", Action: "update", Type: "operation", IsActive: true},
		{Code: "declaration.submit", Name: "提交申报", Resource: "declaration", Action: "submit", Type: "operation", IsActive: true},
		{Code: "declaration.approve", Name: "审批申报", Resource: "declaration", Action: "approve", Type: "operation", IsActive: true},

		// User management permissions
		{Code: "user.create", Name: "创建用户", Resource: "user", Action: "create", Type: "operation", IsActive: true},
		{Code: "user.read", Name: "查看用户", Resource: "user", Action: "read", Type: "operation", IsActive: true},
		{Code: "user.update", Name: "更新用户", Resource: "user", Action: "update", Type: "operation", IsActive: true},
		{Code: "user.delete", Name: "删除用户", Resource: "user", Action: "delete", Type: "operation", IsActive: true},

		// System permissions
		{Code: "system.admin", Name: "系统管理", Resource: "system", Action: "admin", Type: "operation", IsActive: true},
		{Code: "system.config", Name: "系统配置", Resource: "system", Action: "config", Type: "operation", IsActive: true},
	}
}

func (s *PermissionService) getDefaultRoles() []model.Role {
	return []model.Role{
		{Code: "super_admin", Name: "超级管理员", Description: "系统超级管理员，拥有所有权限", Type: "system", Level: 1, IsActive: true},
		{Code: "admin", Name: "管理员", Description: "系统管理员，拥有大部分管理权限", Type: "system", Level: 2, IsActive: true},
		{Code: "legal_representative", Name: "法人代表", Description: "企业法人代表，拥有企业最高权限", Type: "enterprise", Level: 1, IsActive: true},
		{Code: "financial_manager", Name: "财务经理", Description: "财务经理，拥有财务相关权限", Type: "enterprise", Level: 2, IsActive: true},
		{Code: "tax_officer", Name: "办税员", Description: "办税员，拥有税务申报相关权限", Type: "enterprise", Level: 3, IsActive: true},
		{Code: "accountant", Name: "会计", Description: "会计，拥有基础财务权限", Type: "enterprise", Level: 4, IsActive: true},
		{Code: "viewer", Name: "查看者", Description: "只读权限，可查看相关信息", Type: "enterprise", Level: 5, IsActive: true},
	}
}

func (s *PermissionService) assignDefaultPermissionsToRole(roleCode, roleID string) {
	permissionMap := map[string][]string{
		"super_admin": {
			"enterprise.create", "enterprise.read", "enterprise.update", "enterprise.delete",
			"tax_config.create", "tax_config.read", "tax_config.update", "tax_config.delete",
			"invoice.create", "invoice.read", "invoice.update", "invoice.delete", "invoice.verify",
			"declaration.create", "declaration.read", "declaration.update", "declaration.submit", "declaration.approve",
			"user.create", "user.read", "user.update", "user.delete",
			"system.admin", "system.config",
		},
		"admin": {
			"enterprise.read", "enterprise.update",
			"tax_config.read", "tax_config.update",
			"invoice.read", "invoice.update", "invoice.verify",
			"declaration.read", "declaration.update", "declaration.approve",
			"user.read", "user.update",
		},
		"legal_representative": {
			"enterprise.read", "enterprise.update",
			"tax_config.read", "tax_config.update",
			"invoice.read", "invoice.update", "invoice.verify",
			"declaration.read", "declaration.update", "declaration.submit", "declaration.approve",
			"user.read",
		},
		"financial_manager": {
			"enterprise.read",
			"tax_config.read",
			"invoice.create", "invoice.read", "invoice.update", "invoice.verify",
			"declaration.create", "declaration.read", "declaration.update", "declaration.submit",
		},
		"tax_officer": {
			"enterprise.read",
			"tax_config.read",
			"invoice.read", "invoice.verify",
			"declaration.create", "declaration.read", "declaration.update", "declaration.submit",
		},
		"accountant": {
			"enterprise.read",
			"invoice.create", "invoice.read", "invoice.update",
			"declaration.read",
		},
		"viewer": {
			"enterprise.read",
			"invoice.read",
			"declaration.read",
		},
	}

	if permCodes, exists := permissionMap[roleCode]; exists {
		for _, permCode := range permCodes {
			var permission model.Permission
			if err := s.db.Where("code = ?", permCode).First(&permission).Error; err == nil {
				rolePermission := &model.RolePermission{
					ID:           uuid.New().String(),
					RoleID:       roleID,
					PermissionID: permission.ID,
					CreatedAt:    time.Now(),
				}
				s.db.Create(rolePermission)
			}
		}
	}
}

// createAuditLog creates an audit log entry
func (s *PermissionService) createAuditLog(ctx context.Context, action, entityID, description string) {
	auditLog := &model.AuditLog{
		ID:           uuid.New().String(),
		ResourceType: "permission",
		ResourceID:   entityID,
		Action:       action,
		Description:  description,
		CreatedAt:    time.Now(),
	}
	s.db.Create(auditLog)
}
