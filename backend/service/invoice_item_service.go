// Package service provides business logic for the tax management system.
// It includes services for invoice item management with CRUD operations.
package service

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// InvoiceItemService defines the interface for invoice item operations
type InvoiceItemService interface {
	CreateInvoiceItem(ctx context.Context, req CreateInvoiceItemRequest) (*model.InvoiceItem, error)
	GetInvoiceItemByID(ctx context.Context, id string) (*model.InvoiceItem, error)
	GetInvoiceItemsByInvoiceID(ctx context.Context, invoiceID string) ([]model.InvoiceItem, error)
	UpdateInvoiceItem(ctx context.Context, id string, req UpdateInvoiceItemRequest) (*model.InvoiceItem, error)
	DeleteInvoiceItem(ctx context.Context, id string) error
	BatchCreateInvoiceItems(ctx context.Context, invoiceID string, items []CreateInvoiceItemRequest) ([]model.InvoiceItem, error)
	BatchUpdateInvoiceItems(ctx context.Context, invoiceID string, items []UpdateInvoiceItemRequest) ([]model.InvoiceItem, error)
	BatchDeleteInvoiceItems(ctx context.Context, itemIDs []string) error
}

type invoiceItemService struct {
	db *gorm.DB
}

// NewInvoiceItemService creates a new invoice item service instance
func NewInvoiceItemService(db *gorm.DB) InvoiceItemService {
	return &invoiceItemService{db: db}
}

// CreateInvoiceItemRequest 创建发票明细请求
type CreateInvoiceItemRequest struct {
	InvoiceID     string  `json:"invoice_id" binding:"required"`
	TaxTypeID     *string `json:"tax_type_id"`
	ItemName      string  `json:"item_name" binding:"required"`
	Specification string  `json:"specification"`
	Unit          string  `json:"unit"`
	Quantity      float64 `json:"quantity" binding:"required,gt=0"`
	UnitPrice     float64 `json:"unit_price" binding:"required,gte=0"`
	TaxRate       float64 `json:"tax_rate" binding:"required,gte=0,lte=1"`
	TaxCategory   string  `json:"tax_category"`
	Remarks       string  `json:"remarks"`
}

// UpdateInvoiceItemRequest 更新发票明细请求
type UpdateInvoiceItemRequest struct {
	ID            string  `json:"id" binding:"required"`
	TaxTypeID     *string `json:"tax_type_id"`
	ItemName      string  `json:"item_name" binding:"required"`
	Specification string  `json:"specification"`
	Unit          string  `json:"unit"`
	Quantity      float64 `json:"quantity" binding:"required,gt=0"`
	UnitPrice     float64 `json:"unit_price" binding:"required,gte=0"`
	TaxRate       float64 `json:"tax_rate" binding:"required,gte=0,lte=1"`
	TaxCategory   string  `json:"tax_category"`
	Remarks       string  `json:"remarks"`
}

// CreateInvoiceItem 创建发票明细
func (s *invoiceItemService) CreateInvoiceItem(ctx context.Context, req CreateInvoiceItemRequest) (*model.InvoiceItem, error) {
	// 验证发票是否存在
	var invoice model.Invoice
	if err := s.db.WithContext(ctx).First(&invoice, "id = ?", req.InvoiceID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrInvoiceNotFound
		}
		return nil, fmt.Errorf("查询发票失败: %w", err)
	}

	// 创建发票明细
	item := &model.InvoiceItem{
		ID:            util.GenerateID(),
		InvoiceID:     req.InvoiceID,
		TaxTypeID:     req.TaxTypeID,
		ItemName:      req.ItemName,
		Specification: req.Specification,
		Unit:          req.Unit,
		Quantity:      util.DecimalFromFloat(req.Quantity),
		UnitPrice:     util.DecimalFromFloat(req.UnitPrice),
		TaxRate:       util.DecimalFromFloat(req.TaxRate),
		TaxCategory:   req.TaxCategory,
		Remarks:       req.Remarks,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// 计算金额
	item.CalculateAmounts()

	// 验证数据
	if err := item.Validate(); err != nil {
		return nil, err
	}

	// 保存到数据库
	if err := s.db.WithContext(ctx).Create(item).Error; err != nil {
		return nil, fmt.Errorf("创建发票明细失败: %w", err)
	}

	return item, nil
}

// GetInvoiceItemByID 根据ID获取发票明细
func (s *invoiceItemService) GetInvoiceItemByID(ctx context.Context, id string) (*model.InvoiceItem, error) {
	var item model.InvoiceItem
	if err := s.db.WithContext(ctx).Preload("Invoice").Preload("TaxType").First(&item, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrInvoiceItemNotFound
		}
		return nil, fmt.Errorf("查询发票明细失败: %w", err)
	}

	return &item, nil
}

// GetInvoiceItemsByInvoiceID 根据发票ID获取所有明细
func (s *invoiceItemService) GetInvoiceItemsByInvoiceID(ctx context.Context, invoiceID string) ([]model.InvoiceItem, error) {
	var items []model.InvoiceItem
	if err := s.db.WithContext(ctx).
		Preload("TaxType").
		Where("invoice_id = ?", invoiceID).
		Order("created_at ASC").
		Find(&items).Error; err != nil {
		return nil, fmt.Errorf("查询发票明细失败: %w", err)
	}

	return items, nil
}

// UpdateInvoiceItem 更新发票明细
func (s *invoiceItemService) UpdateInvoiceItem(ctx context.Context, id string, req UpdateInvoiceItemRequest) (*model.InvoiceItem, error) {
	// 查询现有记录
	var item model.InvoiceItem
	if err := s.db.WithContext(ctx).First(&item, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrInvoiceItemNotFound
		}
		return nil, fmt.Errorf("查询发票明细失败: %w", err)
	}

	// 更新字段
	item.TaxTypeID = req.TaxTypeID
	item.ItemName = req.ItemName
	item.Specification = req.Specification
	item.Unit = req.Unit
	item.Quantity = util.DecimalFromFloat(req.Quantity)
	item.UnitPrice = util.DecimalFromFloat(req.UnitPrice)
	item.TaxRate = util.DecimalFromFloat(req.TaxRate)
	item.TaxCategory = req.TaxCategory
	item.Remarks = req.Remarks
	item.UpdatedAt = time.Now()

	// 重新计算金额
	item.CalculateAmounts()

	// 验证数据
	if err := item.Validate(); err != nil {
		return nil, err
	}

	// 保存更新
	if err := s.db.WithContext(ctx).Save(&item).Error; err != nil {
		return nil, fmt.Errorf("更新发票明细失败: %w", err)
	}

	return &item, nil
}

// DeleteInvoiceItem 删除发票明细
func (s *invoiceItemService) DeleteInvoiceItem(ctx context.Context, id string) error {
	result := s.db.WithContext(ctx).Delete(&model.InvoiceItem{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("删除发票明细失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return util.ErrInvoiceItemNotFound
	}
	return nil
}

// BatchCreateInvoiceItems 批量创建发票明细
func (s *invoiceItemService) BatchCreateInvoiceItems(ctx context.Context, invoiceID string, items []CreateInvoiceItemRequest) ([]model.InvoiceItem, error) {
	// 验证发票是否存在
	var invoice model.Invoice
	if err := s.db.WithContext(ctx).First(&invoice, "id = ?", invoiceID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrInvoiceNotFound
		}
		return nil, fmt.Errorf("查询发票失败: %w", err)
	}

	var invoiceItems []model.InvoiceItem
	for _, req := range items {
		item := model.InvoiceItem{
			ID:            util.GenerateID(),
			InvoiceID:     invoiceID,
			TaxTypeID:     req.TaxTypeID,
			ItemName:      req.ItemName,
			Specification: req.Specification,
			Unit:          req.Unit,
			Quantity:      util.DecimalFromFloat(req.Quantity),
			UnitPrice:     util.DecimalFromFloat(req.UnitPrice),
			TaxRate:       util.DecimalFromFloat(req.TaxRate),
			TaxCategory:   req.TaxCategory,
			Remarks:       req.Remarks,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}

		// 计算金额
		item.CalculateAmounts()

		// 验证数据
		if err := item.Validate(); err != nil {
			return nil, fmt.Errorf("验证发票明细失败 [%s]: %w", item.ItemName, err)
		}

		invoiceItems = append(invoiceItems, item)
	}

	// 批量插入
	if err := s.db.WithContext(ctx).Create(&invoiceItems).Error; err != nil {
		return nil, fmt.Errorf("批量创建发票明细失败: %w", err)
	}

	return invoiceItems, nil
}

// BatchUpdateInvoiceItems 批量更新发票明细
func (s *invoiceItemService) BatchUpdateInvoiceItems(ctx context.Context, invoiceID string, items []UpdateInvoiceItemRequest) ([]model.InvoiceItem, error) {
	// 验证发票是否存在
	var invoice model.Invoice
	if err := s.db.WithContext(ctx).First(&invoice, "id = ?", invoiceID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrInvoiceNotFound
		}
		return nil, fmt.Errorf("查询发票失败: %w", err)
	}

	var updatedItems []model.InvoiceItem
	for _, req := range items {
		// 查询现有记录
		var item model.InvoiceItem
		if err := s.db.WithContext(ctx).First(&item, "id = ? AND invoice_id = ?", req.ID, invoiceID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, fmt.Errorf("发票明细不存在: %s", req.ID)
			}
			return nil, fmt.Errorf("查询发票明细失败: %w", err)
		}

		// 更新字段
		item.TaxTypeID = req.TaxTypeID
		item.ItemName = req.ItemName
		item.Specification = req.Specification
		item.Unit = req.Unit
		item.Quantity = util.DecimalFromFloat(req.Quantity)
		item.UnitPrice = util.DecimalFromFloat(req.UnitPrice)
		item.TaxRate = util.DecimalFromFloat(req.TaxRate)
		item.TaxCategory = req.TaxCategory
		item.Remarks = req.Remarks
		item.UpdatedAt = time.Now()

		// 重新计算金额
		item.CalculateAmounts()

		// 验证数据
		if err := item.Validate(); err != nil {
			return nil, fmt.Errorf("验证发票明细失败 [%s]: %w", item.ItemName, err)
		}

		updatedItems = append(updatedItems, item)
	}

	// 批量更新
	for _, item := range updatedItems {
		if err := s.db.WithContext(ctx).Save(&item).Error; err != nil {
			return nil, fmt.Errorf("批量更新发票明细失败: %w", err)
		}
	}

	return updatedItems, nil
}

// BatchDeleteInvoiceItems 批量删除发票明细
func (s *invoiceItemService) BatchDeleteInvoiceItems(ctx context.Context, itemIDs []string) error {
	if len(itemIDs) == 0 {
		return nil
	}

	result := s.db.WithContext(ctx).Delete(&model.InvoiceItem{}, "id IN ?", itemIDs)
	if result.Error != nil {
		return fmt.Errorf("批量删除发票明细失败: %w", result.Error)
	}

	return nil
}
