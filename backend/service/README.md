# 业务服务模块

## 概述
业务服务模块是系统的核心，实现所有业务逻辑处理。采用领域驱动设计（DDD）思想，将业务逻辑按领域划分，确保代码的可维护性和可扩展性。

## 目录结构
```
service/
├── user/       # 用户服务
│   ├── user.go    # 用户管理
│   └── auth.go    # 认证相关
├── company/    # 公司服务
│   ├── company.go # 公司管理
│   └── staff.go   # 员工管理
├── tax/        # 税务服务
│   ├── declare.go # 税务申报
│   ├── invoice.go # 发票管理
│   └── policy.go  # 税收政策
└── common/     # 公共服务
    ├── cache.go   # 缓存服务
    └── notify.go  # 通知服务
```

## 核心服务

### 用户服务
1. 用户管理
   - 注册和登录
   - 个人信息管理
   - 权限控制

2. 认证服务
   - 身份验证
   - 会话管理
   - 安全策略

### 公司服务
1. 公司管理
   - 公司信息维护
   - 资质管理
   - 部门组织

2. 员工管理
   - 员工信息
   - 职位管理
   - 权限分配

### 税务服务
1. 税务申报
   - 申报表生成
   - 数据校验
   - 提交管理

2. 发票管理
   - 发票录入
   - 发票验证
   - 统计分析

3. 税收政策
   - 政策查询
   - 优惠计算
   - 风险提示

## 设计原则
1. 单一职责原则（SRP）
2. 依赖注入原则（DIP）
3. 接口隔离原则（ISP）
4. 开闭原则（OCP）

## 使用示例
```go
// service/user/user.go
type UserService struct {
    repo   repository.UserRepository
    cache  *redis.Client
    logger *zap.Logger
}

func NewUserService(repo repository.UserRepository, cache *redis.Client, logger *zap.Logger) *UserService {
    return &UserService{
        repo:   repo,
        cache:  cache,
        logger: logger,
    }
}

func (s *UserService) Register(ctx context.Context, req dto.RegisterRequest) error {
    // 参数验证
    if err := req.Validate(); err != nil {
        return err
    }

    // 检查用户是否存在
    exists, err := s.repo.ExistsByUsername(ctx, req.Username)
    if err != nil {
        return err
    }
    if exists {
        return errors.New("用户名已存在")
    }

    // 创建用户
    user := &model.User{
        Username: req.Username,
        Password: crypto.HashPassword(req.Password),
        Role:     "user",
    }

    return s.repo.Create(ctx, user)
}

// service/tax/declare.go
type TaxDeclareService struct {
    repo      repository.TaxRepository
    validator *validator.Validator
    notifier  common.NotifyService
}

func (s *TaxDeclareService) SubmitDeclaration(ctx context.Context, req dto.TaxDeclareRequest) error {
    // 数据验证
    if err := s.validator.Validate(req); err != nil {
        return err
    }

    // 生成申报单
    declaration := &model.TaxDeclaration{
        CompanyID:  req.CompanyID,
        Period:     req.Period,
        TaxType:    req.TaxType,
        Amount:     req.Amount,
        Status:     "pending",
        SubmitTime: time.Now(),
    }

    // 保存申报记录
    if err := s.repo.Create(ctx, declaration); err != nil {
        return err
    }

    // 发送通知
    return s.notifier.SendDeclarationNotice(ctx, declaration)
}
```

## 最佳实践
1. 业务逻辑分层
   - 请求验证
   - 业务处理
   - 结果返回

2. 错误处理
   - 统一错误定义
   - 错误包装和转换
   - 日志记录

3. 事务管理
   - 事务边界划分
   - 一致性保证
   - 异常回滚

4. 缓存策略
   - 缓存预热
   - 失效更新
   - 并发控制

## 性能优化
1. 并发处理
   - 使用goroutine
   - 控制并发数
   - 超时处理

2. 数据缓存
   - 多级缓存
   - 缓存更新
   - 缓存清理

3. 批量处理
   - 批量查询
   - 批量更新
   - 异步处理