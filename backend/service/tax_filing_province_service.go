// Package service provides business logic services for the tax management system.
// This file contains the tax filing province service for managing province configurations.
package service

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// TaxFilingProvinceService 税务申报省份服务
type TaxFilingProvinceService struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewTaxFilingProvinceService 创建税务申报省份服务
func NewTaxFilingProvinceService(db *gorm.DB, logger *zap.Logger) *TaxFilingProvinceService {
	return &TaxFilingProvinceService{
		db:     db,
		logger: logger,
	}
}

// CreateProvince 创建省份配置
func (s *TaxFilingProvinceService) CreateProvince(ctx context.Context, req *model.TaxFilingProvinceCreateRequest) (*model.TaxFilingProvince, error) {
	// 检查省份代码是否已存在
	var existing model.TaxFilingProvince
	if err := s.db.Where("code = ?", req.Code).First(&existing).Error; err == nil {
		return nil, util.NewDuplicateError("Province", "code", req.Code, nil)
	}

	province := &model.TaxFilingProvince{
		ID:              util.GenerateID(),
		Code:            req.Code,
		Name:            req.Name,
		NameEn:          req.NameEn,
		BaseURL:         req.BaseURL,
		Status:          req.Status,
		AuthMethod:      req.AuthMethod,
		Timeout:         req.Timeout,
		MaxRetries:      req.MaxRetries,
		RetryDelay:      req.RetryDelay,
		AuthConfig:      req.AuthConfig,
		APIEndpoints:    req.APIEndpoints,
		DataMappings:    req.DataMappings,
		ValidationRules: req.ValidationRules,
	}

	if err := s.db.Create(province).Error; err != nil {
		s.logger.Error("Failed to create province", zap.Error(err))
		return nil, util.NewInternalError("Failed to create province", err)
	}

	s.logger.Info("Province created successfully", zap.String("code", province.Code))
	return province, nil
}

// GetProvince 获取省份配置
func (s *TaxFilingProvinceService) GetProvince(ctx context.Context, code string) (*model.TaxFilingProvince, error) {
	var province model.TaxFilingProvince
	if err := s.db.Where("code = ?", code).First(&province).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.NewNotFoundError("Province", code, err)
		}
		s.logger.Error("Failed to get province", zap.Error(err))
		return nil, util.NewInternalError("Failed to get province", err)
	}

	return &province, nil
}

// UpdateProvince 更新省份配置
func (s *TaxFilingProvinceService) UpdateProvince(ctx context.Context, code string, req *model.TaxFilingProvinceUpdateRequest) (*model.TaxFilingProvince, error) {
	var province model.TaxFilingProvince
	if err := s.db.Where("code = ?", code).First(&province).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.NewNotFoundError("Province", code, err)
		}
		return nil, util.NewInternalError("Failed to find province", err)
	}

	// 更新字段
	if req.Name != nil {
		province.Name = *req.Name
	}
	if req.NameEn != nil {
		province.NameEn = req.NameEn
	}
	if req.BaseURL != nil {
		province.BaseURL = *req.BaseURL
	}
	if req.Status != nil {
		province.Status = *req.Status
	}
	if req.AuthMethod != nil {
		province.AuthMethod = *req.AuthMethod
	}
	if req.Timeout != nil {
		province.Timeout = *req.Timeout
	}
	if req.MaxRetries != nil {
		province.MaxRetries = *req.MaxRetries
	}
	if req.RetryDelay != nil {
		province.RetryDelay = *req.RetryDelay
	}

	if err := s.db.Save(&province).Error; err != nil {
		s.logger.Error("Failed to update province", zap.Error(err))
		return nil, util.NewInternalError("Failed to update province", err)
	}

	s.logger.Info("Province updated successfully", zap.String("code", province.Code))
	return &province, nil
}

// DeleteProvince 删除省份配置
func (s *TaxFilingProvinceService) DeleteProvince(ctx context.Context, code string) error {
	result := s.db.Where("code = ?", code).Delete(&model.TaxFilingProvince{})
	if result.Error != nil {
		s.logger.Error("Failed to delete province", zap.Error(result.Error))
		return util.NewInternalError("Failed to delete province", result.Error)
	}

	if result.RowsAffected == 0 {
		return util.NewNotFoundError("Province", code, nil)
	}

	s.logger.Info("Province deleted successfully", zap.String("code", code))
	return nil
}

// ListProvinces 获取省份配置列表
func (s *TaxFilingProvinceService) ListProvinces(ctx context.Context, query *model.TaxFilingProvinceQuery) ([]*model.TaxFilingProvince, int64, error) {
	var provinces []*model.TaxFilingProvince
	var total int64

	db := s.db.Model(&model.TaxFilingProvince{})

	// 应用过滤条件
	if query.Status != nil {
		db = db.Where("status = ?", *query.Status)
	}
	if query.Keyword != "" {
		db = db.Where("name LIKE ? OR code LIKE ?", "%"+query.Keyword+"%", "%"+query.Keyword+"%")
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		s.logger.Error("Failed to count provinces", zap.Error(err))
		return nil, 0, util.NewInternalError("Failed to count provinces", err)
	}

	// 应用排序和分页
	offset := (query.Page - 1) * query.PageSize
	if err := db.Order(fmt.Sprintf("%s %s", query.OrderBy, query.Order)).
		Offset(offset).Limit(query.PageSize).Find(&provinces).Error; err != nil {
		s.logger.Error("Failed to list provinces", zap.Error(err))
		return nil, 0, util.NewInternalError("Failed to list provinces", err)
	}

	return provinces, total, nil
}

// UpdateProvinceStatus 更新省份状态
func (s *TaxFilingProvinceService) UpdateProvinceStatus(ctx context.Context, code string, req *model.ProvinceStatusUpdateRequest) error {
	result := s.db.Model(&model.TaxFilingProvince{}).
		Where("code = ?", code).
		Update("status", req.Status)

	if result.Error != nil {
		s.logger.Error("Failed to update province status", zap.Error(result.Error))
		return util.NewInternalError("Failed to update province status", result.Error)
	}

	if result.RowsAffected == 0 {
		return util.NewNotFoundError("Province", code, nil)
	}

	s.logger.Info("Province status updated successfully", 
		zap.String("code", code), 
		zap.String("status", string(req.Status)))
	return nil
}

// GetActiveProvinces 获取激活的省份配置
func (s *TaxFilingProvinceService) GetActiveProvinces(ctx context.Context) ([]*model.TaxFilingProvince, error) {
	var provinces []*model.TaxFilingProvince
	if err := s.db.Where("status = ?", model.TaxFilingProvinceStatusActive).Find(&provinces).Error; err != nil {
		s.logger.Error("Failed to get active provinces", zap.Error(err))
		return nil, util.NewInternalError("Failed to get active provinces", err)
	}

	return provinces, nil
}

// HealthCheck 检查省份服务健康状态
func (s *TaxFilingProvinceService) HealthCheck(ctx context.Context, code string) (*model.TaxFilingProvinceHealthCheck, error) {
	province, err := s.GetProvince(ctx, code)
	if err != nil {
		return nil, err
	}

	// TODO: 实现实际的健康检查逻辑
	// 这里应该向省份的API发送健康检查请求

	healthCheck := &model.TaxFilingProvinceHealthCheck{
		ProvinceCode: province.Code,
		ProvinceName: province.Name,
		Status:       "healthy",
		IsHealthy:    true,
	}

	return healthCheck, nil
}
