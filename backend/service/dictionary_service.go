// Package service provides business logic layer for the tax management system.
// It implements authentication, enterprise management, invoice processing, and other core business operations.
package service

import (
	"fmt"
	"sort"

	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// DictionaryService provides data dictionary management functionality for the tax management system.
// It handles creation, retrieval, and management of system data dictionaries.
type DictionaryService struct {
	db *gorm.DB
}

// NewDictionaryService creates a new dictionary service instance with the provided database connection.
// It returns a configured DictionaryService ready to handle dictionary operations.
func NewDictionaryService(db *gorm.DB) *DictionaryService {
	return &DictionaryService{db: db}
}

// CreateDictionary creates a new data dictionary entry
func (s *DictionaryService) CreateDictionary(req *model.DictionaryCreateRequest) (*model.DataDictionary, error) {
	// 检查代码是否已存在
	var count int64
	if err := s.db.Model(&model.DataDictionary{}).
		Where("category = ? AND code = ?", req.Category, req.Code).
		Count(&count).Error; err != nil {
		return nil, fmt.Errorf("检查字典代码失败: %w", err)
	}

	if count > 0 {
		return nil, fmt.Errorf("字典代码已存在: %s.%s", req.Category, req.Code)
	}

	// 创建字典记录
	dictionary := &model.DataDictionary{
		ID:          util.GenerateID(),
		Category:    req.Category,
		DictCode:    req.Code,
		DictName:    req.Name,
		DictValue:   req.Value,
		SortOrder:   req.SortOrder,
		IsActive:    true,
		Description: req.Description,
	}

	if err := s.db.Create(dictionary).Error; err != nil {
		return nil, fmt.Errorf("创建数据字典失败: %w", err)
	}

	return dictionary, nil
}

// UpdateDictionary updates an existing data dictionary entry
func (s *DictionaryService) UpdateDictionary(id string, req *model.DictionaryUpdateRequest) (*model.DataDictionary, error) {
	var dictionary model.DataDictionary
	if err := s.db.Where("id = ?", id).First(&dictionary).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("数据字典不存在")
		}
		return nil, fmt.Errorf("查询数据字典失败: %w", err)
	}

	// 更新字段
	dictionary.DictName = req.Name
	dictionary.DictValue = req.Value
	dictionary.SortOrder = req.SortOrder
	dictionary.IsActive = req.IsActive
	dictionary.Description = req.Description

	if err := s.db.Save(&dictionary).Error; err != nil {
		return nil, fmt.Errorf("更新数据字典失败: %w", err)
	}

	return &dictionary, nil
}

// GetDictionaryList retrieves data dictionary list with pagination and filters
func (s *DictionaryService) GetDictionaryList(req *model.DictionaryListRequest) (*model.DictionaryListResponse, error) {
	var dictionaries []model.DataDictionary
	var total int64

	// 构建查询
	query := s.db.Model(&model.DataDictionary{})

	// 添加过滤条件
	if req.Category != nil && *req.Category != "" {
		query = query.Where("category = ?", *req.Category)
	}
	if req.DictCode != nil && *req.DictCode != "" {
		query = query.Where("dict_code = ?", *req.DictCode)
	}
	if req.DictName != nil && *req.DictName != "" {
		query = query.Where("dict_name LIKE ?", "%"+*req.DictName+"%")
	}
	if req.IsActive != nil {
		query = query.Where("is_active = ?", *req.IsActive)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取字典总数失败: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).
		Order("category, sort_order, code").Find(&dictionaries).Error; err != nil {
		return nil, fmt.Errorf("查询字典列表失败: %w", err)
	}

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &model.DictionaryListResponse{
		Dictionaries: dictionaries,
		Total:        total,
		Page:         req.Page,
		PageSize:     req.PageSize,
		TotalPages:   totalPages,
	}, nil
}

// GetDictionaryByCategory retrieves dictionary entries by category
func (s *DictionaryService) GetDictionaryByCategory(category string) ([]model.DataDictionary, error) {
	var dictionaries []model.DataDictionary
	if err := s.db.Where("category = ? AND is_active = ?", category, true).
		Order("sort_order, code").Find(&dictionaries).Error; err != nil {
		return nil, fmt.Errorf("查询分类字典失败: %w", err)
	}
	return dictionaries, nil
}

// GetDictionaryTree retrieves dictionary entries as a tree structure
func (s *DictionaryService) GetDictionaryTree(category string) ([]*model.DictionaryTreeNode, error) {
	var dictionaries []model.DataDictionary
	if err := s.db.Where("category = ? AND is_active = ?", category, true).
		Order("sort_order, code").Find(&dictionaries).Error; err != nil {
		return nil, fmt.Errorf("查询字典树失败: %w", err)
	}

	// 构建树结构
	nodeMap := make(map[string]*model.DictionaryTreeNode)
	var rootNodes []*model.DictionaryTreeNode

	// 创建所有节点
	for _, dict := range dictionaries {
		node := &model.DictionaryTreeNode{
			ID:          dict.ID,
			Category:    dict.Category,
			Code:        dict.DictCode,
			Name:        dict.DictName,
			Value:       dict.DictValue,
			ParentCode:  "", // 需要根据ParentID查找ParentCode
			SortOrder:   dict.SortOrder,
			IsActive:    dict.IsActive,
			Description: dict.Description,
			Children:    []*model.DictionaryTreeNode{},
		}
		nodeMap[dict.DictCode] = node // Use DictCode for mapping
	}

	// 构建父子关系
	for _, dict := range dictionaries {
		node := nodeMap[dict.DictCode]
		if dict.ParentID == nil {
			// 根节点
			rootNodes = append(rootNodes, node)
		} else {
			// 子节点 - 需要找到父节点的Code
			for _, parentDict := range dictionaries {
				if parentDict.ID == *dict.ParentID {
					if parent, exists := nodeMap[parentDict.DictCode]; exists {
						parent.Children = append(parent.Children, node)
					}
					break
				}
			}
		}
	}

	// 排序
	s.sortTreeNodes(rootNodes)

	return rootNodes, nil
}

// sortTreeNodes recursively sorts tree nodes by sort order and code
func (s *DictionaryService) sortTreeNodes(nodes []*model.DictionaryTreeNode) {
	sort.Slice(nodes, func(i, j int) bool {
		if nodes[i].SortOrder != nodes[j].SortOrder {
			return nodes[i].SortOrder < nodes[j].SortOrder
		}
		return nodes[i].Code < nodes[j].Code
	})

	for _, node := range nodes {
		if len(node.Children) > 0 {
			s.sortTreeNodes(node.Children)
		}
	}
}

// GetDictionaryValue retrieves dictionary value by category and code
func (s *DictionaryService) GetDictionaryValue(category, code string) (string, error) {
	var dictionary model.DataDictionary
	if err := s.db.Where("category = ? AND code = ? AND is_active = ?", category, code, true).
		First(&dictionary).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return "", fmt.Errorf("字典项不存在: %s.%s", category, code)
		}
		return "", fmt.Errorf("查询字典值失败: %w", err)
	}
	return dictionary.DictValue, nil
}

// DeleteDictionary deletes a data dictionary entry by ID
func (s *DictionaryService) DeleteDictionary(id string) error {
	var dictionary model.DataDictionary
	if err := s.db.Where("id = ?", id).First(&dictionary).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("数据字典不存在")
		}
		return fmt.Errorf("查询数据字典失败: %w", err)
	}

	// 检查是否有子字典
	var childCount int64
	if err := s.db.Model(&model.DataDictionary{}).
		Where("parent_id = ?", dictionary.ID).
		Count(&childCount).Error; err != nil {
		return fmt.Errorf("检查子字典失败: %w", err)
	}

	if childCount > 0 {
		return fmt.Errorf("存在子字典，无法删除")
	}

	if err := s.db.Delete(&dictionary).Error; err != nil {
		return fmt.Errorf("删除数据字典失败: %w", err)
	}

	return nil
}
