package service

import (
	"context"
	"mime/multipart"
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"backend/config"
)

// MockOCRService 模拟OCR服务
type MockOCRService struct {
	mock.Mock
}

func (m *MockOCRService) RecognizeInvoice(ctx context.Context, file multipart.File, filename string, settings OCRSettings) (*OCRResult, error) {
	args := m.Called(ctx, file, filename, settings)
	return args.Get(0).(*OCRResult), args.Error(1)
}

func (m *MockOCRService) RecognizeMixedInvoices(ctx context.Context, file multipart.File, filename string, settings OCRSettings) (*OCRResult, error) {
	args := m.Called(ctx, file, filename, settings)
	return args.Get(0).(*OCRResult), args.Error(1)
}

func TestNewOCRService(t *testing.T) {
	tests := []struct {
		name    string
		config  *config.Config
		wantErr bool
	}{
		{
			name: "valid config",
			config: &config.Config{
				Aliyun: config.AliyunConfig{
					AccessKeyID:     "test-key-id",
					AccessKeySecret: "test-key-secret",
					RegionID:        "cn-hangzhou",
					OCR: config.OCRConfig{
						Endpoint: "ocr.cn-hangzhou.aliyuncs.com",
						Timeout:  30,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "empty access key",
			config: &config.Config{
				Aliyun: config.AliyunConfig{
					AccessKeyID:     "",
					AccessKeySecret: "test-key-secret",
					RegionID:        "cn-hangzhou",
					OCR: config.OCRConfig{
						Endpoint: "ocr.cn-hangzhou.aliyuncs.com",
						Timeout:  30,
					},
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := NewOCRService(tt.config)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, service)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, service)
			}
		})
	}
}

func TestOCRService_RecognizeInvoice(t *testing.T) {
	mockService := &MockOCRService{}
	ctx := context.Background()
	file := &MockFile{content: "test file content"}
	filename := "test.jpg"
	settings := OCRSettings{
		InvoiceType: "vat",
		Accuracy:    "accurate",
		AutoCorrect: true,
	}

	expectedResult := &OCRResult{
		InvoiceNumber:   "12345678",
		InvoiceCode:     "*********",
		InvoiceType:     "增值税专用发票",
		IssueDate:       time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
		SellerName:      "测试销售方",
		SellerTaxNumber: "91110000123456789X",
		BuyerName:       "测试购买方",
		BuyerTaxNumber:  "91110000*********Y",
		TotalAmount:     decimal.NewFromFloat(1000.00),
		TaxAmount:       decimal.NewFromFloat(130.00),
		TotalWithTax:    decimal.NewFromFloat(1130.00),
		Confidence: map[string]float64{
			"invoiceNumber": 0.95,
			"sellerName":    0.92,
			"totalAmount":   0.98,
		},
	}

	mockService.On("RecognizeInvoice", ctx, file, filename, settings).Return(expectedResult, nil)

	result, err := mockService.RecognizeInvoice(ctx, file, filename, settings)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, expectedResult.InvoiceNumber, result.InvoiceNumber)
	assert.Equal(t, expectedResult.SellerName, result.SellerName)
	assert.Equal(t, expectedResult.TotalAmount, result.TotalAmount)
	assert.Equal(t, expectedResult.Confidence["invoiceNumber"], result.Confidence["invoiceNumber"])

	mockService.AssertExpectations(t)
}

func TestOCRService_RecognizeMixedInvoices(t *testing.T) {
	mockService := &MockOCRService{}
	ctx := context.Background()
	file := &MockFile{content: "test mixed invoices content"}
	filename := "mixed_invoices.pdf"
	settings := OCRSettings{
		InvoiceType:  "mixed",
		Accuracy:     "fast",
		ExtractItems: true,
	}

	expectedResult := &OCRResult{
		InvoiceNumber: "MX123456",
		InvoiceCode:   "MX987654",
		InvoiceType:   "混贴发票",
		IssueDate:     time.Date(2024, 2, 20, 0, 0, 0, 0, time.UTC),
		SellerName:    "混贴销售方",
		TotalAmount:   decimal.NewFromFloat(2500.00),
		TaxAmount:     decimal.NewFromFloat(325.00),
		TotalWithTax:  decimal.NewFromFloat(2825.00),
		Items: []OCRInvoiceItem{
			{
				Name:      "商品A",
				Quantity:  decimal.NewFromInt(2),
				UnitPrice: decimal.NewFromFloat(500.00),
				Amount:    decimal.NewFromFloat(1000.00),
				TaxRate:   decimal.NewFromFloat(0.13),
			},
			{
				Name:      "商品B",
				Quantity:  decimal.NewFromInt(3),
				UnitPrice: decimal.NewFromFloat(500.00),
				Amount:    decimal.NewFromFloat(1500.00),
				TaxRate:   decimal.NewFromFloat(0.13),
			},
		},
		Confidence: map[string]float64{
			"invoiceNumber": 0.88,
			"totalAmount":   0.94,
		},
	}

	mockService.On("RecognizeMixedInvoices", ctx, file, filename, settings).Return(expectedResult, nil)

	result, err := mockService.RecognizeMixedInvoices(ctx, file, filename, settings)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, expectedResult.InvoiceNumber, result.InvoiceNumber)
	assert.Equal(t, expectedResult.InvoiceType, result.InvoiceType)
	assert.Equal(t, len(expectedResult.Items), len(result.Items))
	assert.Equal(t, expectedResult.Items[0].Name, result.Items[0].Name)
	assert.Equal(t, expectedResult.Items[0].Amount, result.Items[0].Amount)

	mockService.AssertExpectations(t)
}

func TestOCRSettings_Validation(t *testing.T) {
	tests := []struct {
		name     string
		settings OCRSettings
		valid    bool
	}{
		{
			name: "valid settings",
			settings: OCRSettings{
				InvoiceType:  "vat",
				Accuracy:     "accurate",
				AutoCorrect:  true,
				ExtractItems: false,
			},
			valid: true,
		},
		{
			name: "empty invoice type",
			settings: OCRSettings{
				InvoiceType:  "",
				Accuracy:     "fast",
				AutoCorrect:  false,
				ExtractItems: true,
			},
			valid: true, // 空类型应该被允许，使用默认值
		},
		{
			name: "invalid accuracy",
			settings: OCRSettings{
				InvoiceType:  "ordinary",
				Accuracy:     "invalid",
				AutoCorrect:  true,
				ExtractItems: false,
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 这里可以添加设置验证逻辑
			if tt.settings.Accuracy != "" && tt.settings.Accuracy != "fast" && tt.settings.Accuracy != "accurate" {
				assert.False(t, tt.valid)
			} else {
				assert.True(t, tt.valid)
			}
		})
	}
}

func TestOCRResult_Validation(t *testing.T) {
	result := &OCRResult{
		InvoiceNumber: "12345678",
		TotalAmount:   decimal.NewFromFloat(1000.00),
		TaxAmount:     decimal.NewFromFloat(130.00),
		TotalWithTax:  decimal.NewFromFloat(1130.00),
		Confidence: map[string]float64{
			"invoiceNumber": 0.95,
			"totalAmount":   0.98,
		},
	}

	// 验证金额计算
	expectedTotal := result.TotalAmount.Add(result.TaxAmount)
	assert.True(t, result.TotalWithTax.Equal(expectedTotal), "价税合计应该等于金额加税额")

	// 验证置信度范围
	for field, confidence := range result.Confidence {
		assert.True(t, confidence >= 0.0 && confidence <= 1.0,
			"字段 %s 的置信度 %f 应该在 0.0 到 1.0 之间", field, confidence)
	}

	// 验证发票号码不为空
	assert.NotEmpty(t, result.InvoiceNumber, "发票号码不应为空")
}
