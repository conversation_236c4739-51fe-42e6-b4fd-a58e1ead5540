// Package service provides business logic layer for the tax management system.
// It contains service interfaces and implementations for handling business operations.
package service

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// EnterpriseService defines the interface for enterprise business logic
type EnterpriseService interface {
	CreateEnterprise(ctx context.Context, req CreateEnterpriseRequest) (*model.Enterprise, error)
	GetEnterprises(ctx context.Context, req GetEnterprisesRequest) (*PaginatedResponse, error)
	GetEnterpriseByID(ctx context.Context, id string, userID string) (*model.Enterprise, error)
	UpdateEnterprise(ctx context.Context, id string, req UpdateEnterpriseRequest) (*model.Enterprise, error)
	DeleteEnterprise(ctx context.Context, id string, userID string) error
	GetEnterpriseStats(ctx context.Context, userID string) (*EnterpriseStats, error)
	GetEnterpriseDetailStats(ctx context.Context, enterpriseID string, userID string) (*EnterpriseDetailStats, error)
	GetEnterpriseDeclarations(ctx context.Context, enterpriseID string, userID string, limit int) ([]model.Declaration, error)
}

type enterpriseService struct {
	db *gorm.DB
}

// NewEnterpriseService creates a new enterprise service instance
func NewEnterpriseService(db *gorm.DB) EnterpriseService {
	return &enterpriseService{db: db}
}

// CreateEnterprise creates a new enterprise
func (s *enterpriseService) CreateEnterprise(ctx context.Context, req CreateEnterpriseRequest) (*model.Enterprise, error) {
	// Check if unified social credit code already exists
	var existingEnterprise model.Enterprise
	result := s.db.WithContext(ctx).Where("unified_social_credit_code = ?", req.CreditCode).First(&existingEnterprise)
	if result.Error == nil {
		return nil, util.ErrEnterpriseExists
	}

	// 注释：移除用户只能拥有一个企业的限制
	// 现在允许一个用户拥有多个企业，但每个企业只能有一个所有者

	// Convert RegisteredCapital from string to float64
	var registeredCapital float64
	if req.RegisteredCapital != "" {
		if parsed, err := strconv.ParseFloat(req.RegisteredCapital, 64); err == nil {
			registeredCapital = parsed
		}
	}

	// 生成唯一的营业执照号码（如果没有提供）
	businessLicenseNumber := ""
	if req.CreditCode != "" {
		// 使用统一社会信用代码作为营业执照号码的基础
		businessLicenseNumber = req.CreditCode
	}

	// 处理注册日期，优先使用 establishmentDate，其次使用 registrationDate
	var registeredDate *time.Time
	if req.EstablishmentDate.Valid && !req.EstablishmentDate.Time.IsZero() {
		registeredDate = &req.EstablishmentDate.Time
	} else if req.RegistrationDate.Valid && !req.RegistrationDate.Time.IsZero() {
		registeredDate = &req.RegistrationDate.Time
	}

	enterprise := model.Enterprise{
		ID:                      util.GenerateID(),
		OwnerID:                 &req.UserID,
		Name:                    req.Name,
		UnifiedSocialCreditCode: req.CreditCode,
		TaxpayerType:            req.TaxpayerType,
		LegalRepresentative:     req.LegalRepresentative,
		IndustryCode:            req.Industry,
		BusinessScope:           req.BusinessScope,
		RegisteredCapital:       decimal.NewFromFloat(registeredCapital),
		RegisteredDate:          registeredDate,
		ContactPerson:           req.ContactPerson,
		ContactPhone:            req.Phone,
		ContactEmail:            req.Email,
		RegisteredAddress:       req.Address,
		BusinessLicenseNumber:   businessLicenseNumber,
		Status:                  "active",
		CreatedAt:               time.Now(),
		UpdatedAt:               time.Now(),
	}

	// 在事务中创建企业并确保数据一致性
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 创建企业
		if err := tx.Create(&enterprise).Error; err != nil {
			return err
		}

		// 确保Owner数据一致性 - 创建对应的enterprise_users记录
		// 首先获取默认管理员角色
		var defaultRole model.Role
		if err := tx.Where("role_code = ? OR role_name LIKE ?", "admin", "%管理员%").First(&defaultRole).Error; err != nil {
			// 如果没有找到管理员角色，创建一个默认角色
			defaultRole = model.Role{
				ID:          util.GenerateID(),
				Name:        "企业管理员",
				Code:        "enterprise_admin",
				Description: "企业管理员角色",
				Type:        model.RoleTypeSystem,
				IsActive:    true,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			}
			if err := tx.Create(&defaultRole).Error; err != nil {
				return fmt.Errorf("创建默认管理员角色失败: %w", err)
			}
		}

		// 创建企业所有者的用户关联记录
		enterpriseUser := &model.EnterpriseUser{
			ID:           util.GenerateID(),
			EnterpriseID: enterprise.ID,
			UserID:       req.UserID,
			RoleID:       defaultRole.ID,
			Status:       "active",
			JoinedAt:     time.Now(),
			IsOwner:      true,
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}

		if err := tx.Create(enterpriseUser).Error; err != nil {
			return fmt.Errorf("创建企业所有者关联记录失败: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &enterprise, nil
}

// GetEnterprises retrieves a paginated list of enterprises with optional filters
func (s *enterpriseService) GetEnterprises(ctx context.Context, req GetEnterprisesRequest) (*PaginatedResponse, error) {
	var enterprises []model.Enterprise
	var total int64

	query := s.db.WithContext(ctx).Model(&model.Enterprise{})

	// Add filter conditions
	if req.UserID != "" {
		query = query.Where("owner_id = ?", req.UserID)
	}
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("name LIKE ? OR unified_social_credit_code LIKE ?", keyword, keyword)
	}
	if req.Industry != "" {
		query = query.Where("industry = ?", req.Industry)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// Calculate total count
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// Paginated query
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&enterprises).Error; err != nil {
		return nil, err
	}

	// 计算总页数
	pages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &PaginatedResponse{
		Items:    enterprises, // 统一使用items字段
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Pages:    pages,
	}, nil
}

// GetEnterpriseByID retrieves an enterprise by its ID with optional user filtering
func (s *enterpriseService) GetEnterpriseByID(ctx context.Context, id string, userID string) (*model.Enterprise, error) {
	var enterprise model.Enterprise
	query := s.db.WithContext(ctx).Where("id = ?", id)

	// 添加用户权限过滤
	if userID != "" {
		query = query.Where("owner_id = ?", userID)
	}

	if err := query.First(&enterprise).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, util.ErrEnterpriseNotFound
		}
		return nil, err
	}

	return &enterprise, nil
}

// UpdateEnterprise updates enterprise information by ID
func (s *enterpriseService) UpdateEnterprise(ctx context.Context, id string, req UpdateEnterpriseRequest) (*model.Enterprise, error) {
	var enterprise model.Enterprise
	query := s.db.WithContext(ctx).Where("id = ?", id)

	// 添加用户权限检查
	if req.UserID != "" {
		query = query.Where("owner_id = ?", req.UserID)
	}

	if err := query.First(&enterprise).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, util.ErrEnterpriseNotFound
		}
		return nil, err
	}

	// Check for duplicate credit code if it's being modified
	if req.CreditCode != "" && req.CreditCode != enterprise.UnifiedSocialCreditCode {
		var existingEnterprise model.Enterprise
		result := s.db.WithContext(ctx).Where("unified_social_credit_code = ? AND id != ?", req.CreditCode, id).First(&existingEnterprise)
		if result.Error == nil {
			return nil, util.ErrInvalidCreditCode
		}
	}

	// Update fields
	updateFields := map[string]interface{}{
		"updated_at": time.Now(),
	}

	if req.Name != "" {
		updateFields["name"] = req.Name
	}
	if req.CreditCode != "" {
		updateFields["unified_social_credit_code"] = req.CreditCode
	}
	if req.TaxpayerType != "" {
		updateFields["taxpayer_type"] = req.TaxpayerType
	}
	if req.LegalRepresentative != "" {
		updateFields["legal_representative"] = req.LegalRepresentative
	}
	if req.Industry != "" {
		updateFields["industry"] = req.Industry
	}
	if req.BusinessScope != "" {
		updateFields["business_scope"] = req.BusinessScope
	}
	if req.ContactPerson != "" {
		updateFields["contact_person"] = req.ContactPerson
	}
	if req.Phone != "" {
		updateFields["contact_phone"] = req.Phone
	}
	if req.Email != "" {
		updateFields["contact_email"] = req.Email
	}
	if req.Address != "" {
		updateFields["registered_address"] = req.Address
	}
	if req.Status != "" {
		updateFields["status"] = req.Status
	}

	if err := s.db.WithContext(ctx).Model(&enterprise).Updates(updateFields).Error; err != nil {
		return nil, err
	}

	// Re-query the updated data
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&enterprise).Error; err != nil {
		return nil, err
	}

	return &enterprise, nil
}

// DeleteEnterprise deletes an enterprise by ID for the specified user
func (s *enterpriseService) DeleteEnterprise(ctx context.Context, id string, userID string) error {
	result := s.db.WithContext(ctx).Where("id = ? AND owner_id = ?", id, userID).Delete(&model.Enterprise{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return util.ErrEnterpriseNotFound
	}
	return nil
}

// GetEnterpriseStats retrieves enterprise statistics for the specified user
func (s *enterpriseService) GetEnterpriseStats(ctx context.Context, userID string) (*EnterpriseStats, error) {
	var stats EnterpriseStats

	// Total enterprise count
	if err := s.db.WithContext(ctx).Model(&model.Enterprise{}).Where("owner_id = ?", userID).Count(&stats.Total).Error; err != nil {
		return nil, err
	}

	// Active enterprise count
	if err := s.db.WithContext(ctx).Model(&model.Enterprise{}).Where("owner_id = ? AND status = ?", userID, "active").Count(&stats.Active).Error; err != nil {
		return nil, err
	}

	// Pending enterprise count
	if err := s.db.WithContext(ctx).Model(&model.Enterprise{}).Where("owner_id = ? AND status = ?", userID, "pending").Count(&stats.Pending).Error; err != nil {
		return nil, err
	}

	// New enterprises this month
	now := time.Now()
	firstDay := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	if err := s.db.WithContext(ctx).Model(&model.Enterprise{}).Where("owner_id = ? AND created_at >= ?", userID, firstDay).Count(&stats.ThisMonth).Error; err != nil {
		return nil, err
	}

	return &stats, nil
}

// GetEnterpriseDetailStats 获取企业详情页统计信息
func (s *enterpriseService) GetEnterpriseDetailStats(ctx context.Context, enterpriseID string, userID string) (*EnterpriseDetailStats, error) {
	// 验证用户是否有权限访问该企业
	_, err := s.GetEnterpriseByID(ctx, enterpriseID, userID)
	if err != nil {
		if err.Error() == "enterprise not found" {
			return nil, fmt.Errorf("企业不存在")
		}
		return nil, err
	}

	stats := &EnterpriseDetailStats{}
	now := time.Now()

	// 总申报次数
	if err := s.db.WithContext(ctx).Model(&model.Declaration{}).
		Where("enterprise_id = ?", enterpriseID).
		Count(&stats.TotalDeclarations).Error; err != nil {
		return nil, err
	}

	// 总税额
	var totalTaxAmount struct {
		TotalTax float64
	}
	if err := s.db.WithContext(ctx).Model(&model.Declaration{}).
		Select("COALESCE(SUM(tax_payable), 0) as total_tax").
		Where("enterprise_id = ? AND declaration_status IN ?", enterpriseID, []string{"approved", "submitted"}).
		Scan(&totalTaxAmount).Error; err != nil {
		return nil, err
	}
	stats.TotalTaxAmount = totalTaxAmount.TotalTax

	// 本月统计
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	if err := s.db.WithContext(ctx).Model(&model.Declaration{}).
		Where("enterprise_id = ? AND created_at >= ?", enterpriseID, monthStart).
		Count(&stats.MonthlyDeclarations).Error; err != nil {
		return nil, err
	}

	var monthlyTaxAmount struct {
		MonthlyTax float64
	}
	if err := s.db.WithContext(ctx).Model(&model.Declaration{}).
		Select("COALESCE(SUM(tax_payable), 0) as monthly_tax").
		Where("enterprise_id = ? AND created_at >= ? AND declaration_status IN ?", enterpriseID, monthStart, []string{"approved", "submitted"}).
		Scan(&monthlyTaxAmount).Error; err != nil {
		return nil, err
	}
	stats.MonthlyTaxAmount = monthlyTaxAmount.MonthlyTax

	// 本年统计
	yearStart := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	if err := s.db.WithContext(ctx).Model(&model.Declaration{}).
		Where("enterprise_id = ? AND created_at >= ?", enterpriseID, yearStart).
		Count(&stats.YearlyDeclarations).Error; err != nil {
		return nil, err
	}

	var yearlyTaxAmount struct {
		YearlyTax float64
	}
	if err := s.db.WithContext(ctx).Model(&model.Declaration{}).
		Select("COALESCE(SUM(tax_payable), 0) as yearly_tax").
		Where("enterprise_id = ? AND created_at >= ? AND declaration_status IN ?", enterpriseID, yearStart, []string{"approved", "submitted"}).
		Scan(&yearlyTaxAmount).Error; err != nil {
		return nil, err
	}
	stats.YearlyTaxAmount = yearlyTaxAmount.YearlyTax

	return stats, nil
}

// GetEnterpriseDeclarations 获取企业最近的申报记录
func (s *enterpriseService) GetEnterpriseDeclarations(ctx context.Context, enterpriseID string, userID string, limit int) ([]model.Declaration, error) {
	// 验证用户是否有权限访问该企业
	_, err := s.GetEnterpriseByID(ctx, enterpriseID, userID)
	if err != nil {
		if err.Error() == "enterprise not found" {
			return nil, fmt.Errorf("企业不存在")
		}
		return nil, err
	}

	var declarations []model.Declaration
	query := s.db.WithContext(ctx).
		Where("enterprise_id = ?", enterpriseID).
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&declarations).Error; err != nil {
		return nil, err
	}

	return declarations, nil
}

// Request and Response structures
// CustomTime 自定义时间类型，用于处理空字符串
type CustomTime struct {
	time.Time
	Valid bool
}

func (ct *CustomTime) UnmarshalJSON(data []byte) error {
	str := string(data)
	if str == "null" || str == `""` || str == "" {
		ct.Valid = false
		return nil
	}

	// 移除引号
	if len(str) >= 2 && str[0] == '"' && str[len(str)-1] == '"' {
		str = str[1 : len(str)-1]
	}

	if str == "" {
		ct.Valid = false
		return nil
	}

	t, err := time.Parse(time.RFC3339, str)
	if err != nil {
		ct.Valid = false
		return nil
	}

	ct.Time = t
	ct.Valid = true
	return nil
}

type CreateEnterpriseRequest struct {
	Name                string     `json:"name" binding:"required"`
	CreditCode          string     `json:"creditCode" binding:"required"`
	TaxpayerType        string     `json:"taxpayerType" binding:"required"`
	LegalRepresentative string     `json:"legalRepresentative"`
	Industry            string     `json:"industry"`
	BusinessScope       string     `json:"businessScope"`
	RegisteredCapital   string     `json:"registeredCapital"`
	RegistrationDate    CustomTime `json:"registrationDate"`
	EstablishmentDate   CustomTime `json:"establishmentDate"`
	ContactPerson       string     `json:"contactPerson"`
	Phone               string     `json:"phone"`
	Email               string     `json:"email"`
	Address             string     `json:"address"`
	UserID              string     `json:"userId"`
}

type UpdateEnterpriseRequest struct {
	Name                string `json:"name"`
	CreditCode          string `json:"creditCode"`
	TaxpayerType        string `json:"taxpayerType"`
	LegalRepresentative string `json:"legalRepresentative"`
	Industry            string `json:"industry"`
	BusinessScope       string `json:"businessScope"`
	ContactPerson       string `json:"contactPerson"`
	Phone               string `json:"phone"`
	Email               string `json:"email"`
	Address             string `json:"address"`
	Status              string `json:"status"`
	UserID              string `json:"userId"`
}

type GetEnterprisesRequest struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"pageSize" form:"pageSize"`
	Keyword  string `json:"keyword" form:"keyword"`
	Industry string `json:"industry" form:"industry"`
	Status   string `json:"status" form:"status"`
	UserID   string `json:"userId"`
}

type PaginatedResponse struct {
	Items    interface{} `json:"items"` // 统一使用items字段
	Total    int64       `json:"total"`
	Page     int         `json:"page"`
	PageSize int         `json:"pageSize"`
	Pages    int         `json:"pages"` // 总页数
}

type EnterpriseStats struct {
	Total     int64 `json:"total"`
	Active    int64 `json:"active"`
	Pending   int64 `json:"pending"`
	ThisMonth int64 `json:"thisMonth"`
}

// EnterpriseDetailStats 企业详情页统计信息
type EnterpriseDetailStats struct {
	TotalDeclarations   int64   `json:"totalDeclarations"`   // 总申报次数
	TotalTaxAmount      float64 `json:"totalTaxAmount"`      // 总税额
	MonthlyDeclarations int64   `json:"monthlyDeclarations"` // 本月申报次数
	MonthlyTaxAmount    float64 `json:"monthlyTaxAmount"`    // 本月税额
	YearlyDeclarations  int64   `json:"yearlyDeclarations"`  // 本年申报次数
	YearlyTaxAmount     float64 `json:"yearlyTaxAmount"`     // 本年税额
}
