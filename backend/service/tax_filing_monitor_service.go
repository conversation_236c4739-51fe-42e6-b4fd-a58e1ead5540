package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"backend/client"
	"backend/model"
)

// TaxFilingMonitorService 税务申报监控服务
type TaxFilingMonitorService struct {
	db                  *gorm.DB
	logger              *zap.Logger
	redisService        *RedisService
	taxFilingClient     *client.TaxFilingClient
	notificationService *TaxFilingNotificationService

	// 监控控制
	stopChan  chan struct{}
	wg        sync.WaitGroup
	isRunning bool
	mutex     sync.RWMutex
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	Enabled             bool            `json:"enabled"`
	CheckInterval       time.Duration   `json:"check_interval"`
	HealthCheckInterval time.Duration   `json:"health_check_interval"`
	AlertThresholds     AlertThresholds `json:"alert_thresholds"`
	RetentionDays       int             `json:"retention_days"`
}

// AlertThresholds 告警阈值
type AlertThresholds struct {
	FailureRate           float64 `json:"failure_rate"`            // 失败率阈值 (%)
	ProcessingTimeMinutes int     `json:"processing_time_minutes"` // 处理时间阈值 (分钟)
	PendingTimeMinutes    int     `json:"pending_time_minutes"`    // 待处理时间阈值 (分钟)
	QueueSize             int     `json:"queue_size"`              // 队列大小阈值
}

// NewTaxFilingMonitorService 创建监控服务
func NewTaxFilingMonitorService(
	db *gorm.DB,
	logger *zap.Logger,
	redisService *RedisService,
	taxFilingClient *client.TaxFilingClient,
	notificationService *TaxFilingNotificationService,
) *TaxFilingMonitorService {
	return &TaxFilingMonitorService{
		db:                  db,
		logger:              logger,
		redisService:        redisService,
		taxFilingClient:     taxFilingClient,
		notificationService: notificationService,
		stopChan:            make(chan struct{}),
	}
}

// Start 启动监控服务
func (s *TaxFilingMonitorService) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("monitor service is already running")
	}

	// 获取监控配置
	config, err := s.getMonitorConfig(ctx)
	if err != nil {
		s.logger.Error("Failed to get monitor config", zap.Error(err))
		return err
	}

	if !config.Enabled {
		s.logger.Info("Monitor service is disabled")
		return nil
	}

	s.isRunning = true

	// 启动监控协程
	s.wg.Add(3)
	go s.systemMonitorLoop(ctx, config)
	go s.healthCheckLoop(ctx, config)
	go s.cleanupLoop(ctx, config)

	s.logger.Info("Tax filing monitor service started",
		zap.Duration("check_interval", config.CheckInterval),
		zap.Duration("health_check_interval", config.HealthCheckInterval),
	)

	return nil
}

// Stop 停止监控服务
func (s *TaxFilingMonitorService) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return fmt.Errorf("monitor service is not running")
	}

	close(s.stopChan)
	s.wg.Wait()
	s.isRunning = false

	s.logger.Info("Tax filing monitor service stopped")
	return nil
}

// IsRunning 检查监控服务是否运行中
func (s *TaxFilingMonitorService) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

// systemMonitorLoop 系统监控循环
func (s *TaxFilingMonitorService) systemMonitorLoop(ctx context.Context, config *MonitorConfig) {
	defer s.wg.Done()

	ticker := time.NewTicker(config.CheckInterval)
	defer ticker.Stop()

	s.logger.Info("System monitor loop started")

	for {
		select {
		case <-s.stopChan:
			s.logger.Info("System monitor loop stopped")
			return
		case <-ticker.C:
			s.performSystemCheck(ctx, config)
		}
	}
}

// healthCheckLoop 健康检查循环
func (s *TaxFilingMonitorService) healthCheckLoop(ctx context.Context, config *MonitorConfig) {
	defer s.wg.Done()

	ticker := time.NewTicker(config.HealthCheckInterval)
	defer ticker.Stop()

	s.logger.Info("Health check loop started")

	for {
		select {
		case <-s.stopChan:
			s.logger.Info("Health check loop stopped")
			return
		case <-ticker.C:
			s.performHealthCheck(ctx, config)
		}
	}
}

// cleanupLoop 清理循环
func (s *TaxFilingMonitorService) cleanupLoop(ctx context.Context, config *MonitorConfig) {
	defer s.wg.Done()

	ticker := time.NewTicker(24 * time.Hour) // 每天清理一次
	defer ticker.Stop()

	s.logger.Info("Cleanup loop started")

	for {
		select {
		case <-s.stopChan:
			s.logger.Info("Cleanup loop stopped")
			return
		case <-ticker.C:
			s.performCleanup(ctx, config)
		}
	}
}

// performSystemCheck 执行系统检查
func (s *TaxFilingMonitorService) performSystemCheck(ctx context.Context, config *MonitorConfig) {
	s.logger.Debug("Performing system check")

	// 检查失败率
	if err := s.checkFailureRate(ctx, config); err != nil {
		s.logger.Error("Failed to check failure rate", zap.Error(err))
	}

	// 检查处理时间
	if err := s.checkProcessingTime(ctx, config); err != nil {
		s.logger.Error("Failed to check processing time", zap.Error(err))
	}

	// 检查待处理队列
	if err := s.checkPendingQueue(ctx, config); err != nil {
		s.logger.Error("Failed to check pending queue", zap.Error(err))
	}

	// 检查长时间未完成的申报
	if err := s.checkStuckSubmissions(ctx, config); err != nil {
		s.logger.Error("Failed to check stuck submissions", zap.Error(err))
	}

	// 更新系统指标
	if err := s.updateSystemMetrics(ctx); err != nil {
		s.logger.Error("Failed to update system metrics", zap.Error(err))
	}
}

// checkFailureRate 检查失败率
func (s *TaxFilingMonitorService) checkFailureRate(ctx context.Context, config *MonitorConfig) error {
	// 获取最近1小时的申报统计
	since := time.Now().Add(-time.Hour)

	var total, failed int64
	s.db.Model(&model.TaxFilingSubmission{}).
		Where("created_at >= ?", since).
		Count(&total)

	s.db.Model(&model.TaxFilingSubmission{}).
		Where("created_at >= ? AND status = ?", since, model.TaxFilingStatusFailed).
		Count(&failed)

	if total == 0 {
		return nil
	}

	failureRate := float64(failed) / float64(total) * 100

	// 检查是否超过阈值
	if failureRate > config.AlertThresholds.FailureRate {
		alert := model.CreateAlert(
			"error",
			"高失败率告警",
			fmt.Sprintf("最近1小时申报失败率为 %.2f%%，超过阈值 %.2f%%", failureRate, config.AlertThresholds.FailureRate),
			4,
		)

		s.logger.Warn("High failure rate detected",
			zap.Float64("failure_rate", failureRate),
			zap.Float64("threshold", config.AlertThresholds.FailureRate),
		)

		// 发送告警通知
		go s.notificationService.SendSystemAlertNotification(ctx, alert.Type, alert.Title, alert.Message)
	}

	// 缓存失败率指标
	s.redisService.SetConfig(ctx, "metrics:failure_rate", failureRate)

	return nil
}

// checkProcessingTime 检查处理时间
func (s *TaxFilingMonitorService) checkProcessingTime(ctx context.Context, config *MonitorConfig) error {
	// 获取处理中时间过长的申报
	threshold := time.Now().Add(-time.Duration(config.AlertThresholds.ProcessingTimeMinutes) * time.Minute)

	var longProcessingSubmissions []model.TaxFilingSubmission
	if err := s.db.Where("status = ? AND submitted_at < ?",
		model.TaxFilingStatusProcessing, threshold).
		Find(&longProcessingSubmissions).Error; err != nil {
		return err
	}

	if len(longProcessingSubmissions) > 0 {
		alert := model.CreateAlert(
			"warning",
			"处理时间过长告警",
			fmt.Sprintf("发现 %d 个申报处理时间超过 %d 分钟",
				len(longProcessingSubmissions), config.AlertThresholds.ProcessingTimeMinutes),
			3,
		)

		s.logger.Warn("Long processing time detected",
			zap.Int("count", len(longProcessingSubmissions)),
			zap.Int("threshold_minutes", config.AlertThresholds.ProcessingTimeMinutes),
		)

		// 发送告警通知
		go s.notificationService.SendSystemAlertNotification(ctx, alert.Type, alert.Title, alert.Message)
	}

	return nil
}

// checkPendingQueue 检查待处理队列
func (s *TaxFilingMonitorService) checkPendingQueue(ctx context.Context, config *MonitorConfig) error {
	// 获取待处理申报数量
	var pendingCount int64
	s.db.Model(&model.TaxFilingSubmission{}).
		Where("status = ?", model.TaxFilingStatusPending).
		Count(&pendingCount)

	// 检查是否超过阈值
	if int(pendingCount) > config.AlertThresholds.QueueSize {
		alert := model.CreateAlert(
			"warning",
			"待处理队列过长告警",
			fmt.Sprintf("待处理申报数量为 %d，超过阈值 %d", pendingCount, config.AlertThresholds.QueueSize),
			3,
		)

		s.logger.Warn("Large pending queue detected",
			zap.Int64("pending_count", pendingCount),
			zap.Int("threshold", config.AlertThresholds.QueueSize),
		)

		// 发送告警通知
		go s.notificationService.SendSystemAlertNotification(ctx, alert.Type, alert.Title, alert.Message)
	}

	// 缓存队列大小指标
	s.redisService.SetConfig(ctx, "metrics:pending_queue_size", pendingCount)

	return nil
}

// checkStuckSubmissions 检查卡住的申报
func (s *TaxFilingMonitorService) checkStuckSubmissions(ctx context.Context, config *MonitorConfig) error {
	// 获取长时间待处理的申报
	threshold := time.Now().Add(-time.Duration(config.AlertThresholds.PendingTimeMinutes) * time.Minute)

	var stuckSubmissions []model.TaxFilingSubmission
	if err := s.db.Where("status = ? AND created_at < ?",
		model.TaxFilingStatusPending, threshold).
		Find(&stuckSubmissions).Error; err != nil {
		return err
	}

	if len(stuckSubmissions) > 0 {
		alert := model.CreateAlert(
			"error",
			"申报卡住告警",
			fmt.Sprintf("发现 %d 个申报长时间未处理，可能存在系统问题", len(stuckSubmissions)),
			4,
		)

		s.logger.Error("Stuck submissions detected",
			zap.Int("count", len(stuckSubmissions)),
			zap.Int("threshold_minutes", config.AlertThresholds.PendingTimeMinutes),
		)

		// 发送告警通知
		go s.notificationService.SendSystemAlertNotification(ctx, alert.Type, alert.Title, alert.Message)
	}

	return nil
}

// performHealthCheck 执行健康检查
func (s *TaxFilingMonitorService) performHealthCheck(ctx context.Context, config *MonitorConfig) {
	s.logger.Debug("Performing health check")

	// 检查Python服务健康状态
	if err := s.checkPythonServiceHealth(ctx); err != nil {
		s.logger.Error("Python service health check failed", zap.Error(err))
	}

	// 检查省份服务健康状态
	if err := s.checkProvinceServicesHealth(ctx); err != nil {
		s.logger.Error("Province services health check failed", zap.Error(err))
	}

	// 检查数据库连接
	if err := s.checkDatabaseHealth(ctx); err != nil {
		s.logger.Error("Database health check failed", zap.Error(err))
	}

	// 检查Redis连接
	if err := s.checkRedisHealth(ctx); err != nil {
		s.logger.Error("Redis health check failed", zap.Error(err))
	}
}

// checkPythonServiceHealth 检查Python服务健康状态
func (s *TaxFilingMonitorService) checkPythonServiceHealth(ctx context.Context) error {
	healthResp, err := s.taxFilingClient.HealthCheck(ctx)
	if err != nil {
		// 发送告警
		go s.notificationService.SendSystemAlertNotification(ctx,
			"error",
			"Python税务申报服务不可用",
			fmt.Sprintf("健康检查失败: %v", err))
		return err
	}

	if healthResp.Status != "healthy" {
		// 发送告警
		go s.notificationService.SendSystemAlertNotification(ctx,
			"warning",
			"Python税务申报服务状态异常",
			fmt.Sprintf("服务状态: %s", healthResp.Status))
	}

	// 缓存健康状态
	s.redisService.SetConfig(ctx, "health:python_service", healthResp)

	return nil
}

// checkProvinceServicesHealth 检查省份服务健康状态
func (s *TaxFilingMonitorService) checkProvinceServicesHealth(ctx context.Context) error {
	// 获取所有激活的省份
	var provinces []model.TaxFilingProvince
	if err := s.db.Where("status = ?", model.TaxFilingProvinceStatusActive).Find(&provinces).Error; err != nil {
		return err
	}

	for _, province := range provinces {
		// 这里可以添加省份特定的健康检查逻辑
		// 暂时只更新最后检查时间
		now := time.Now()
		s.db.Model(&province).Updates(map[string]interface{}{
			"last_health_check": now,
			"health_status":     "checking",
		})
	}

	return nil
}

// checkDatabaseHealth 检查数据库健康状态
func (s *TaxFilingMonitorService) checkDatabaseHealth(ctx context.Context) error {
	sqlDB, err := s.db.DB()
	if err != nil {
		return err
	}

	if err := sqlDB.Ping(); err != nil {
		// 发送告警
		go s.notificationService.SendSystemAlertNotification(ctx,
			"error",
			"数据库连接失败",
			fmt.Sprintf("数据库健康检查失败: %v", err))
		return err
	}

	// 缓存健康状态
	s.redisService.SetConfig(ctx, "health:database", "healthy")

	return nil
}

// checkRedisHealth 检查Redis健康状态
func (s *TaxFilingMonitorService) checkRedisHealth(ctx context.Context) error {
	if err := s.redisService.Ping(ctx); err != nil {
		// 发送告警
		go s.notificationService.SendSystemAlertNotification(ctx,
			"error",
			"Redis连接失败",
			fmt.Sprintf("Redis健康检查失败: %v", err))
		return err
	}

	// 缓存健康状态
	s.redisService.SetConfig(ctx, "health:redis", "healthy")

	return nil
}

// updateSystemMetrics 更新系统指标
func (s *TaxFilingMonitorService) updateSystemMetrics(ctx context.Context) error {
	// 获取各种统计数据
	metrics := &SystemMetrics{
		Timestamp: time.Now(),
	}

	// 申报状态统计
	s.db.Model(&model.TaxFilingSubmission{}).
		Where("status = ?", model.TaxFilingStatusPending).
		Count(&metrics.PendingSubmissions)

	s.db.Model(&model.TaxFilingSubmission{}).
		Where("status = ?", model.TaxFilingStatusProcessing).
		Count(&metrics.ProcessingSubmissions)

	s.db.Model(&model.TaxFilingSubmission{}).
		Where("status = ?", model.TaxFilingStatusAccepted).
		Count(&metrics.CompletedSubmissions)

	s.db.Model(&model.TaxFilingSubmission{}).
		Where("status = ?", model.TaxFilingStatusFailed).
		Count(&metrics.FailedSubmissions)

	// 批次统计
	s.db.Model(&model.TaxFilingBatch{}).
		Where("status = ?", model.TaxFilingBatchStatusProcessing).
		Count(&metrics.ActiveBatches)

	// 回调统计
	s.db.Model(&model.TaxFilingCallback{}).
		Where("status = ?", model.TaxFilingCallbackStatusPending).
		Count(&metrics.PendingCallbacks)

	// 缓存指标
	s.redisService.SetConfig(ctx, "metrics:system", metrics)

	return nil
}

// performCleanup 执行清理
func (s *TaxFilingMonitorService) performCleanup(ctx context.Context, config *MonitorConfig) {
	s.logger.Info("Performing cleanup")

	// 清理过期的状态历史记录
	cutoffTime := time.Now().AddDate(0, 0, -config.RetentionDays)

	result := s.db.Where("created_at < ?", cutoffTime).Delete(&model.TaxFilingStatusHistory{})
	if result.Error != nil {
		s.logger.Error("Failed to cleanup status history", zap.Error(result.Error))
	} else {
		s.logger.Info("Cleaned up status history records", zap.Int64("count", result.RowsAffected))
	}

	// 清理已完成的回调记录
	result = s.db.Where("created_at < ? AND status = ?", cutoffTime, model.TaxFilingCallbackStatusSent).
		Delete(&model.TaxFilingCallback{})
	if result.Error != nil {
		s.logger.Error("Failed to cleanup callbacks", zap.Error(result.Error))
	} else {
		s.logger.Info("Cleaned up callback records", zap.Int64("count", result.RowsAffected))
	}
}

// getMonitorConfig 获取监控配置
func (s *TaxFilingMonitorService) getMonitorConfig(ctx context.Context) (*MonitorConfig, error) {
	// 先从缓存获取
	var config MonitorConfig
	if err := s.redisService.GetConfig(ctx, "tax_filing_monitor", &config); err == nil {
		return &config, nil
	}

	// 返回默认配置
	config = MonitorConfig{
		Enabled:             true,
		CheckInterval:       2 * time.Minute,
		HealthCheckInterval: 5 * time.Minute,
		AlertThresholds: AlertThresholds{
			FailureRate:           10.0, // 10%
			ProcessingTimeMinutes: 30,   // 30分钟
			PendingTimeMinutes:    15,   // 15分钟
			QueueSize:             100,  // 100个
		},
		RetentionDays: 30, // 30天
	}

	// 缓存配置
	if err := s.redisService.SetConfig(ctx, "tax_filing_monitor", config); err != nil {
		s.logger.Warn("Failed to cache monitor config", zap.Error(err))
	}

	return &config, nil
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	Timestamp             time.Time `json:"timestamp"`
	PendingSubmissions    int64     `json:"pending_submissions"`
	ProcessingSubmissions int64     `json:"processing_submissions"`
	CompletedSubmissions  int64     `json:"completed_submissions"`
	FailedSubmissions     int64     `json:"failed_submissions"`
	ActiveBatches         int64     `json:"active_batches"`
	PendingCallbacks      int64     `json:"pending_callbacks"`
}

// GetSystemHealth 获取系统健康状态
func (s *TaxFilingMonitorService) GetSystemHealth(ctx context.Context) (*model.SystemHealth, error) {
	health := &model.SystemHealth{
		Status:    "healthy",
		Version:   "1.0.0",
		Timestamp: time.Now(),
		Uptime:    "24h",
		Services:  make(map[string]interface{}),
		Database: model.ServiceHealth{
			Status:       "healthy",
			ResponseTime: 10.5,
			LastCheck:    time.Now(),
		},
		Redis: model.ServiceHealth{
			Status:       "healthy",
			ResponseTime: 2.1,
			LastCheck:    time.Now(),
		},
		MessageQueue: model.ServiceHealth{
			Status:       "healthy",
			ResponseTime: 5.3,
			LastCheck:    time.Now(),
		},
	}

	return health, nil
}

// GetSystemMetrics 获取系统指标
func (s *TaxFilingMonitorService) GetSystemMetrics(ctx context.Context) (*model.SystemMetrics, error) {
	metrics := &model.SystemMetrics{
		Timestamp: time.Now(),
		CPU: model.CPUMetrics{
			Usage:     45.2,
			LoadAvg1:  1.2,
			LoadAvg5:  1.5,
			LoadAvg15: 1.8,
		},
		Memory: model.MemoryMetrics{
			Total:     **********, // 8GB
			Used:      **********, // ~3.6GB
			Available: **********, // ~4.4GB
			Usage:     45.0,
		},
		Disk: model.DiskMetrics{
			Total: 107374182400, // 100GB
			Used:  53687091200,  // 50GB
			Free:  53687091200,  // 50GB
			Usage: 50.0,
		},
		Requests: model.RequestMetrics{
			Total:       1000,
			Success:     950,
			Failed:      50,
			AvgResponse: 120.5,
		},
		Database: model.DatabaseMetrics{
			Connections: 10,
			Queries:     5000,
			SlowQueries: 5,
			AvgResponse: 25.3,
		},
		TaxSubmissions: model.TaxSubmissionMetrics{
			Total:      500,
			Pending:    10,
			Success:    450,
			Failed:     30,
			Processing: 10,
		},
	}

	return metrics, nil
}

// GetAlerts 获取告警列表
func (s *TaxFilingMonitorService) GetAlerts(ctx context.Context, query *model.AlertQuery) ([]*model.Alert, int64, error) {
	var alerts []*model.Alert
	var total int64

	db := s.db.Model(&model.Alert{})

	// 应用过滤条件
	if query.Level != nil {
		db = db.Where("level = ?", *query.Level)
	}
	if query.Status != nil {
		db = db.Where("status = ?", *query.Status)
	}
	if query.Source != "" {
		db = db.Where("source = ?", query.Source)
	}
	if query.Category != "" {
		db = db.Where("category = ?", query.Category)
	}
	if query.Keyword != "" {
		db = db.Where("title LIKE ? OR description LIKE ?", "%"+query.Keyword+"%", "%"+query.Keyword+"%")
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		s.logger.Error("Failed to count alerts", zap.Error(err))
		return nil, 0, err
	}

	// 应用排序和分页
	offset := (query.Page - 1) * query.PageSize
	if err := db.Order(fmt.Sprintf("%s %s", query.OrderBy, query.Order)).
		Offset(offset).Limit(query.PageSize).Find(&alerts).Error; err != nil {
		s.logger.Error("Failed to list alerts", zap.Error(err))
		return nil, 0, err
	}

	return alerts, total, nil
}

// AcknowledgeAlert 确认告警
func (s *TaxFilingMonitorService) AcknowledgeAlert(ctx context.Context, alertID string) error {
	now := time.Now()
	result := s.db.Model(&model.Alert{}).
		Where("id = ? AND status = ?", alertID, model.AlertStatusActive).
		Updates(map[string]interface{}{
			"status":          model.AlertStatusAcknowledged,
			"acknowledged_at": &now,
			"acknowledged_by": "system", // TODO: 从context获取用户信息
			"updated_at":      now,
		})

	if result.Error != nil {
		s.logger.Error("Failed to acknowledge alert", zap.Error(result.Error))
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("alert not found or already acknowledged")
	}

	s.logger.Info("Alert acknowledged successfully", zap.String("alert_id", alertID))
	return nil
}

// GetPerformanceMetrics 获取性能指标
func (s *TaxFilingMonitorService) GetPerformanceMetrics(ctx context.Context, query *model.PerformanceQuery) (*model.PerformanceMetrics, error) {
	metrics := &model.PerformanceMetrics{
		Timestamp:         time.Now(),
		APIResponseTime:   120.5,
		APIThroughput:     100.0,
		APIErrorRate:      5.0,
		DBResponseTime:    25.3,
		DBConnections:     10,
		DBSlowQueries:     2,
		SubmissionRate:    50.0,
		SuccessRate:       95.0,
		AvgProcessingTime: 300.0,
	}

	return metrics, nil
}
