// Package service provides business logic layer for the tax management system.
// This file implements user validation services for ensuring data uniqueness and integrity.
package service

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"backend/model"
)

// UserValidationService provides user validation functionality
type UserValidationService struct {
	db *gorm.DB
}

// NewUserValidationService creates a new user validation service
func NewUserValidationService(db *gorm.DB) *UserValidationService {
	return &UserValidationService{
		db: db,
	}
}

// ValidateUserNameRequest represents a user name validation request
type ValidateUserNameRequest struct {
	UserName      string `json:"user_name" binding:"required"`
	ExcludeUserID string `json:"exclude_user_id,omitempty"`
}

// ValidateEmailRequest represents an email validation request
type ValidateEmailRequest struct {
	Email         string `json:"email" binding:"required,email"`
	ExcludeUserID string `json:"exclude_user_id,omitempty"`
}

// ValidatePhoneRequest represents a phone validation request
type ValidatePhoneRequest struct {
	Phone         string `json:"phone" binding:"required"`
	ExcludeUserID string `json:"exclude_user_id,omitempty"`
}

// ValidationResponse represents a validation response
type ValidationResponse struct {
	Valid   bool   `json:"valid"`
	Message string `json:"message,omitempty"`
}

// ValidateUserName checks if a user name is available
func (s *UserValidationService) ValidateUserName(ctx context.Context, req ValidateUserNameRequest) (*ValidationResponse, error) {
	if req.UserName == "" {
		return &ValidationResponse{
			Valid:   false,
			Message: "用户名不能为空",
		}, nil
	}

	// 检查用户名长度
	if len(req.UserName) < 2 || len(req.UserName) > 20 {
		return &ValidationResponse{
			Valid:   false,
			Message: "用户名长度必须在2-20个字符之间",
		}, nil
	}

	// 构建查询
	query := s.db.WithContext(ctx).Model(&model.User{}).Where("user_name = ?", req.UserName)

	// 如果提供了排除的用户ID，则排除该用户
	if req.ExcludeUserID != "" {
		query = query.Where("id != ?", req.ExcludeUserID)
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return nil, fmt.Errorf("验证用户名失败: %w", err)
	}

	if count > 0 {
		return &ValidationResponse{
			Valid:   false,
			Message: "用户名已存在",
		}, nil
	}

	return &ValidationResponse{
		Valid: true,
	}, nil
}

// ValidateEmail checks if an email is available
func (s *UserValidationService) ValidateEmail(ctx context.Context, req ValidateEmailRequest) (*ValidationResponse, error) {
	if req.Email == "" {
		return &ValidationResponse{
			Valid:   false,
			Message: "邮箱不能为空",
		}, nil
	}

	// 构建查询
	query := s.db.WithContext(ctx).Model(&model.User{}).Where("email = ?", req.Email)

	// 如果提供了排除的用户ID，则排除该用户
	if req.ExcludeUserID != "" {
		query = query.Where("id != ?", req.ExcludeUserID)
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return nil, fmt.Errorf("验证邮箱失败: %w", err)
	}

	if count > 0 {
		return &ValidationResponse{
			Valid:   false,
			Message: "邮箱已存在",
		}, nil
	}

	return &ValidationResponse{
		Valid: true,
	}, nil
}

// ValidatePhone checks if a phone number is available
func (s *UserValidationService) ValidatePhone(ctx context.Context, req ValidatePhoneRequest) (*ValidationResponse, error) {
	if req.Phone == "" {
		return &ValidationResponse{
			Valid:   false,
			Message: "手机号不能为空",
		}, nil
	}

	// 验证手机号格式
	if len(req.Phone) != 11 || req.Phone[0] != '1' {
		return &ValidationResponse{
			Valid:   false,
			Message: "手机号格式不正确",
		}, nil
	}

	// 构建查询
	query := s.db.WithContext(ctx).Model(&model.User{}).Where("phone = ?", req.Phone)

	// 如果提供了排除的用户ID，则排除该用户
	if req.ExcludeUserID != "" {
		query = query.Where("id != ?", req.ExcludeUserID)
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return nil, fmt.Errorf("验证手机号失败: %w", err)
	}

	if count > 0 {
		return &ValidationResponse{
			Valid:   false,
			Message: "手机号已存在",
		}, nil
	}

	return &ValidationResponse{
		Valid: true,
	}, nil
}
