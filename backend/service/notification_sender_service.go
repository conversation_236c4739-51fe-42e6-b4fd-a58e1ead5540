package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// NotificationSenderService 通知发送服务
type NotificationSenderService struct {
	logger     *zap.Logger
	httpClient *http.Client
}

// EmailNotification 邮件通知结构
type EmailNotification struct {
	To      string `json:"to"`
	Subject string `json:"subject"`
	Content string `json:"content"`
	Type    string `json:"type"`
}

// SMSNotification 短信通知结构
type SMSNotification struct {
	Phone   string `json:"phone"`
	Content string `json:"content"`
	Type    string `json:"type"`
}

// WebhookNotification Webhook通知结构
type WebhookNotification struct {
	URL     string                 `json:"url"`
	Method  string                 `json:"method"`
	Headers map[string]string      `json:"headers"`
	Data    map[string]interface{} `json:"data"`
}

// NewNotificationSenderService 创建通知发送服务
func NewNotificationSenderService(logger *zap.Logger) *NotificationSenderService {
	return &NotificationSenderService{
		logger: logger,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// SendEmailNotification 发送邮件通知
func (s *NotificationSenderService) SendEmailNotification(ctx context.Context, to, subject, content string) error {
	s.logger.Info("Sending email notification",
		zap.String("to", to),
		zap.String("subject", subject),
	)

	notification := EmailNotification{
		To:      to,
		Subject: subject,
		Content: content,
		Type:    "tax_filing",
	}

	// 这里可以集成实际的邮件服务，如SendGrid、阿里云邮件推送等
	// 目前只是记录日志
	s.logger.Info("Email notification prepared",
		zap.String("to", notification.To),
		zap.String("subject", notification.Subject),
		zap.Int("content_length", len(notification.Content)),
	)

	// 模拟发送邮件
	time.Sleep(100 * time.Millisecond)

	s.logger.Info("Email notification sent successfully",
		zap.String("to", to),
	)

	return nil
}

// SendSMSNotification 发送短信通知
func (s *NotificationSenderService) SendSMSNotification(ctx context.Context, phone, content string) error {
	s.logger.Info("Sending SMS notification",
		zap.String("phone", phone),
		zap.Int("content_length", len(content)),
	)

	notification := SMSNotification{
		Phone:   phone,
		Content: content,
		Type:    "tax_filing",
	}

	// 这里可以集成实际的短信服务，如阿里云短信、腾讯云短信等
	// 目前只是记录日志
	s.logger.Info("SMS notification prepared",
		zap.String("phone", notification.Phone),
		zap.Int("content_length", len(notification.Content)),
	)

	// 模拟发送短信
	time.Sleep(100 * time.Millisecond)

	s.logger.Info("SMS notification sent successfully",
		zap.String("phone", phone),
	)

	return nil
}

// SendWebhookNotification 发送Webhook通知
func (s *NotificationSenderService) SendWebhookNotification(ctx context.Context, url string, data map[string]interface{}) error {
	s.logger.Info("Sending webhook notification",
		zap.String("url", url),
	)

	notification := WebhookNotification{
		URL:    url,
		Method: "POST",
		Headers: map[string]string{
			"Content-Type": "application/json",
			"User-Agent":   "TaxSystem/1.0",
		},
		Data: data,
	}

	// 准备请求数据
	jsonData, err := json.Marshal(notification.Data)
	if err != nil {
		s.logger.Error("Failed to marshal webhook data", zap.Error(err))
		return fmt.Errorf("failed to marshal webhook data: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, notification.Method, notification.URL, bytes.NewBuffer(jsonData))
	if err != nil {
		s.logger.Error("Failed to create webhook request", zap.Error(err))
		return fmt.Errorf("failed to create webhook request: %w", err)
	}

	// 设置请求头
	for key, value := range notification.Headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		s.logger.Error("Failed to send webhook request", zap.Error(err))
		return fmt.Errorf("failed to send webhook request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		s.logger.Error("Webhook request failed",
			zap.String("url", url),
			zap.Int("status_code", resp.StatusCode),
		)
		return fmt.Errorf("webhook request failed with status code: %d", resp.StatusCode)
	}

	s.logger.Info("Webhook notification sent successfully",
		zap.String("url", url),
		zap.Int("status_code", resp.StatusCode),
	)

	return nil
}

// SendBatchNotifications 批量发送通知
func (s *NotificationSenderService) SendBatchNotifications(ctx context.Context, notifications []interface{}) error {
	s.logger.Info("Sending batch notifications",
		zap.Int("count", len(notifications)),
	)

	var errors []error

	for i, notification := range notifications {
		switch n := notification.(type) {
		case EmailNotification:
			if err := s.SendEmailNotification(ctx, n.To, n.Subject, n.Content); err != nil {
				s.logger.Error("Failed to send email notification in batch",
					zap.Int("index", i),
					zap.Error(err),
				)
				errors = append(errors, err)
			}
		case SMSNotification:
			if err := s.SendSMSNotification(ctx, n.Phone, n.Content); err != nil {
				s.logger.Error("Failed to send SMS notification in batch",
					zap.Int("index", i),
					zap.Error(err),
				)
				errors = append(errors, err)
			}
		case WebhookNotification:
			if err := s.SendWebhookNotification(ctx, n.URL, n.Data); err != nil {
				s.logger.Error("Failed to send webhook notification in batch",
					zap.Int("index", i),
					zap.Error(err),
				)
				errors = append(errors, err)
			}
		default:
			s.logger.Warn("Unknown notification type in batch",
				zap.Int("index", i),
				zap.String("type", fmt.Sprintf("%T", n)),
			)
		}
	}

	if len(errors) > 0 {
		s.logger.Error("Some notifications failed in batch",
			zap.Int("failed_count", len(errors)),
			zap.Int("total_count", len(notifications)),
		)
		return fmt.Errorf("failed to send %d out of %d notifications", len(errors), len(notifications))
	}

	s.logger.Info("All batch notifications sent successfully",
		zap.Int("count", len(notifications)),
	)

	return nil
}

// ValidateEmailAddress 验证邮箱地址
func (s *NotificationSenderService) ValidateEmailAddress(email string) bool {
	// 简单的邮箱验证
	if len(email) < 5 || !contains(email, "@") || !contains(email, ".") {
		return false
	}
	return true
}

// ValidatePhoneNumber 验证手机号
func (s *NotificationSenderService) ValidatePhoneNumber(phone string) bool {
	// 简单的手机号验证（中国大陆）
	if len(phone) != 11 {
		return false
	}
	if phone[0] != '1' {
		return false
	}
	return true
}

// ValidateWebhookURL 验证Webhook URL
func (s *NotificationSenderService) ValidateWebhookURL(url string) bool {
	// 简单的URL验证
	if len(url) < 10 {
		return false
	}
	if !contains(url, "http://") && !contains(url, "https://") {
		return false
	}
	return true
}

// GetNotificationStatus 获取通知状态（模拟）
func (s *NotificationSenderService) GetNotificationStatus(notificationID string) (string, error) {
	s.logger.Info("Getting notification status",
		zap.String("notification_id", notificationID),
	)

	// 模拟状态查询
	statuses := []string{"sent", "delivered", "failed", "pending"}
	status := statuses[len(notificationID)%len(statuses)]

	s.logger.Info("Notification status retrieved",
		zap.String("notification_id", notificationID),
		zap.String("status", status),
	)

	return status, nil
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			containsInMiddle(s, substr))))
}

// containsInMiddle 检查字符串中间是否包含子字符串
func containsInMiddle(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
