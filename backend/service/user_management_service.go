package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"backend/config"
	"backend/model"
	"backend/util"
)

// UserManagementService 用户管理服务
type UserManagementService struct {
	db     *gorm.DB
	config *config.Config
}

// NewUserManagementService 创建用户管理服务
func NewUserManagementService(db *gorm.DB, cfg *config.Config) *UserManagementService {
	return &UserManagementService{
		db:     db,
		config: cfg,
	}
}

// GetEnterpriseUsers 获取企业用户列表
func (s *UserManagementService) GetEnterpriseUsers(ctx context.Context, enterpriseID string, page, pageSize int) (*PaginatedEnterpriseUsers, error) {
	var users []model.EnterpriseUser
	var total int64

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 查询总数
	if err := s.db.Model(&model.EnterpriseUser{}).
		Where("enterprise_id = ?", enterpriseID).
		Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取用户总数失败: %w", err)
	}

	// 查询用户列表
	if err := s.db.Preload("User").Preload("Role").
		Where("enterprise_id = ?", enterpriseID).
		Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&users).Error; err != nil {
		return nil, fmt.Errorf("获取用户列表失败: %w", err)
	}

	// 转换为响应格式
	var userResponses []model.EnterpriseUserResponse
	for _, user := range users {
		response := model.EnterpriseUserResponse{
			ID:           user.ID,
			EnterpriseID: user.EnterpriseID,
			UserID:       user.UserID,
			RoleID:       user.RoleID,
			Status:       user.Status,
			JoinedAt:     user.JoinedAt,
			IsOwner:      user.IsOwner,
			CreatedAt:    user.CreatedAt,
		}

		if user.User != nil {
			response.UserName = user.User.UserName
			if user.User.Email != nil {
				response.Email = *user.User.Email
			}
			response.Phone = user.User.Phone
		}

		if user.Role != nil {
			response.RoleName = user.Role.Name
		}

		userResponses = append(userResponses, response)
	}

	return &PaginatedEnterpriseUsers{
		Users: userResponses,
		Total: total,
		Page:  page,
		Limit: pageSize,
	}, nil
}

// InviteUserToEnterprise 邀请用户加入企业
func (s *UserManagementService) InviteUserToEnterprise(ctx context.Context, req model.InviteUserRequest, enterpriseID, inviterID string) (*model.EnterpriseUser, error) {
	// 检查企业是否存在
	var enterprise model.Enterprise
	if err := s.db.First(&enterprise, "id = ?", enterpriseID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("企业不存在")
		}
		return nil, fmt.Errorf("查询企业失败: %w", err)
	}

	// 检查用户是否存在
	var user model.User
	if err := s.db.First(&user, "id = ?", req.UserID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 检查角色是否存在
	var role model.Role
	if err := s.db.First(&role, "id = ?", req.RoleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("角色不存在")
		}
		return nil, fmt.Errorf("查询角色失败: %w", err)
	}

	// 检查是否尝试分配Owner角色
	if role.Code == "owner" {
		return nil, errors.New("不能通过邀请方式分配Owner角色，请使用转让所有权功能")
	}

	// 检查用户是否已经在企业中
	var existing model.EnterpriseUser
	err := s.db.Where("enterprise_id = ? AND user_id = ?", enterpriseID, req.UserID).First(&existing).Error
	if err == nil {
		return nil, errors.New("用户已在企业中")
	}

	// 只有在分配Owner角色时才检查企业级Owner唯一性约束
	if role.Code == "owner" {
		if err := s.ValidateEnterpriseOwnerUniqueness(ctx, enterpriseID, req.UserID); err != nil {
			return nil, err
		}
	}

	// 创建企业用户关联
	enterpriseUser := &model.EnterpriseUser{
		ID:           util.GenerateID(),
		EnterpriseID: enterpriseID,
		UserID:       req.UserID,
		RoleID:       req.RoleID,
		Status:       model.EnterpriseUserStatusActive,
		JoinedAt:     time.Now(),
		InvitedBy:    &inviterID,
		IsOwner:      false,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.db.Create(enterpriseUser).Error; err != nil {
		return nil, fmt.Errorf("创建企业用户关联失败: %w", err)
	}

	// 记录操作日志
	s.logPermissionOperation(ctx, inviterID, req.UserID, enterpriseID,
		model.OperationTypeUserInvite, "enterprise_user", enterpriseUser.ID,
		nil, enterpriseUser, req.Description)

	return enterpriseUser, nil
}

// RemoveUserFromEnterprise 从企业中移除用户
func (s *UserManagementService) RemoveUserFromEnterprise(ctx context.Context, enterpriseID, userID, operatorID string) error {
	// 检查用户是否在企业中
	var enterpriseUser model.EnterpriseUser
	if err := s.db.Where("enterprise_id = ? AND user_id = ?", enterpriseID, userID).First(&enterpriseUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不在企业中")
		}
		return fmt.Errorf("查询企业用户关联失败: %w", err)
	}

	// 不能移除企业所有者
	if enterpriseUser.IsOwner {
		return errors.New("不能移除企业所有者")
	}

	// 删除企业用户关联
	if err := s.db.Delete(&enterpriseUser).Error; err != nil {
		return fmt.Errorf("删除企业用户关联失败: %w", err)
	}

	// 记录操作日志
	s.logPermissionOperation(ctx, operatorID, userID, enterpriseID,
		model.OperationTypeUserRemove, "enterprise_user", enterpriseUser.ID,
		enterpriseUser, nil, "从企业中移除用户")

	return nil
}

// UpdateUserRole 更新用户角色
func (s *UserManagementService) UpdateUserRole(ctx context.Context, enterpriseID, userID, newRoleID, operatorID string) error {
	// 检查用户是否在企业中
	var enterpriseUser model.EnterpriseUser
	if err := s.db.Where("enterprise_id = ? AND user_id = ?", enterpriseID, userID).First(&enterpriseUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不在企业中")
		}
		return fmt.Errorf("查询企业用户关联失败: %w", err)
	}

	// 检查新角色是否存在
	var role model.Role
	if err := s.db.First(&role, "id = ?", newRoleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return fmt.Errorf("查询角色失败: %w", err)
	}

	oldRoleID := enterpriseUser.RoleID

	// 更新角色
	enterpriseUser.RoleID = newRoleID
	enterpriseUser.UpdatedAt = time.Now()

	if err := s.db.Save(&enterpriseUser).Error; err != nil {
		return fmt.Errorf("更新用户角色失败: %w", err)
	}

	// 记录操作日志
	s.logPermissionOperation(ctx, operatorID, userID, enterpriseID,
		model.OperationTypeRoleAssign, "role", newRoleID,
		map[string]string{"old_role_id": oldRoleID},
		map[string]string{"new_role_id": newRoleID},
		"更新用户角色")

	return nil
}

// TransferOwnership 转让企业所有权
func (s *UserManagementService) TransferOwnership(ctx context.Context, enterpriseID, currentOwnerID string, req model.TransferOwnershipRequest) error {
	// 验证当前用户密码
	var currentOwner model.User
	if err := s.db.First(&currentOwner, "id = ?", currentOwnerID).Error; err != nil {
		return fmt.Errorf("获取当前所有者信息失败: %w", err)
	}

	if err := bcrypt.CompareHashAndPassword([]byte(currentOwner.PasswordHash), []byte(req.Password)); err != nil {
		return errors.New("密码验证失败")
	}

	// 验证当前用户确实是企业所有者
	var currentEnterprise model.Enterprise
	if err := s.db.Where("id = ? AND owner_id = ?", enterpriseID, currentOwnerID).First(&currentEnterprise).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("您不是该企业的所有者")
		}
		return fmt.Errorf("验证企业所有权失败: %w", err)
	}

	// 注释：移除新所有者只能拥有一个企业的限制
	// 现在允许一个用户拥有多个企业

	// 检查新所有者是否在企业中
	var newOwnerUser model.EnterpriseUser
	if err := s.db.Where("enterprise_id = ? AND user_id = ?", enterpriseID, req.NewOwnerID).First(&newOwnerUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("新所有者不在企业中")
		}
		return fmt.Errorf("查询新所有者失败: %w", err)
	}

	// 开始事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 更新企业所有者
		if err := tx.Model(&model.Enterprise{}).Where("id = ?", enterpriseID).Update("owner_id", req.NewOwnerID).Error; err != nil {
			return fmt.Errorf("更新企业所有者失败: %w", err)
		}

		// 更新原所有者的企业用户关联
		if err := tx.Model(&model.EnterpriseUser{}).
			Where("enterprise_id = ? AND user_id = ?", enterpriseID, currentOwnerID).
			Update("is_owner", false).Error; err != nil {
			return fmt.Errorf("更新原所有者状态失败: %w", err)
		}

		// 更新新所有者的企业用户关联
		if err := tx.Model(&model.EnterpriseUser{}).
			Where("enterprise_id = ? AND user_id = ?", enterpriseID, req.NewOwnerID).
			Update("is_owner", true).Error; err != nil {
			return fmt.Errorf("更新新所有者状态失败: %w", err)
		}

		// 记录操作日志
		s.logPermissionOperationWithTx(ctx, tx, currentOwnerID, req.NewOwnerID, enterpriseID,
			model.OperationTypeOwnerTransfer, "enterprise", enterpriseID,
			map[string]string{"old_owner_id": currentOwnerID},
			map[string]string{"new_owner_id": req.NewOwnerID},
			req.Reason)

		return nil
	})
}

// ValidateOwnerUniqueness 验证Owner唯一性约束（针对特定企业）
func (s *UserManagementService) ValidateOwnerUniqueness(ctx context.Context, userID string) error {
	// 注释：现在允许一个用户拥有多个企业
	// 这个方法现在主要用于验证用户是否存在，而不是限制所有权

	// 检查用户是否存在
	var user model.User
	if err := s.db.WithContext(ctx).Where("id = ?", userID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("用户不存在")
		}
		return fmt.Errorf("检查用户失败: %w", err)
	}

	return nil
}

// ValidateEnterpriseOwnerUniqueness 验证企业所有者唯一性（确保一个企业只有一个所有者）
func (s *UserManagementService) ValidateEnterpriseOwnerUniqueness(ctx context.Context, enterpriseID, newOwnerID string) error {
	// 检查企业是否已经有其他所有者
	var existingOwner model.EnterpriseUser
	err := s.db.WithContext(ctx).Where("enterprise_id = ? AND is_owner = ? AND user_id != ?",
		enterpriseID, true, newOwnerID).First(&existingOwner).Error

	if err == nil {
		return fmt.Errorf("企业已经有所有者，一个企业只能有一个所有者")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("检查企业所有者失败: %w", err)
	}

	return nil
}

// ValidateUserInvitation 验证用户邀请的有效性
func (s *UserManagementService) ValidateUserInvitation(ctx context.Context, userID, roleID, enterpriseID string) (map[string]interface{}, error) {
	// 检查用户是否存在
	var user model.User
	if err := s.db.WithContext(ctx).First(&user, "id = ?", userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 检查角色是否存在
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, "id = ?", roleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("角色不存在")
		}
		return nil, fmt.Errorf("查询角色失败: %w", err)
	}

	// 检查企业是否存在
	var enterprise model.Enterprise
	if err := s.db.WithContext(ctx).First(&enterprise, "id = ?", enterpriseID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("企业不存在")
		}
		return nil, fmt.Errorf("查询企业失败: %w", err)
	}

	// 检查用户是否已经在该企业中
	var existing model.EnterpriseUser
	err := s.db.WithContext(ctx).Where("enterprise_id = ? AND user_id = ?", enterpriseID, userID).First(&existing).Error
	if err == nil {
		return map[string]interface{}{
			"can_invite":      false,
			"reason":          "user_already_in_enterprise",
			"message":         "用户已在该企业中",
			"user_name":       user.UserName,
			"enterprise_name": enterprise.Name,
		}, nil
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("检查用户企业关联失败: %w", err)
	}

	// 如果是Owner角色，检查Owner唯一性约束
	if role.Code == "owner" {
		if err := s.ValidateOwnerUniqueness(ctx, userID); err != nil {
			return map[string]interface{}{
				"can_invite":      false,
				"reason":          "owner_uniqueness_violation",
				"message":         err.Error(),
				"user_name":       user.UserName,
				"enterprise_name": enterprise.Name,
				"role_name":       role.Name,
			}, nil
		}
	}

	// 验证通过，可以邀请
	return map[string]interface{}{
		"can_invite":      true,
		"reason":          "validation_passed",
		"message":         fmt.Sprintf("可以邀请用户 %s 以 %s 角色加入企业 %s", user.UserName, role.Name, enterprise.Name),
		"user_name":       user.UserName,
		"enterprise_name": enterprise.Name,
		"role_name":       role.Name,
	}, nil
}

// GetUserEnterprise 获取用户的企业信息
func (s *UserManagementService) GetUserEnterprise(ctx context.Context, userID string) (*model.Enterprise, error) {
	var user model.User
	err := s.db.WithContext(ctx).
		Preload("Enterprise").
		First(&user, "id = ?", userID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 如果用户有直接关联的企业，返回该企业
	if user.EnterpriseID != nil && user.Enterprise != nil {
		return user.Enterprise, nil
	}

	// 如果用户没有直接关联的企业，查找用户作为所有者的企业
	var enterprise model.Enterprise
	err = s.db.WithContext(ctx).
		Where("owner_id = ?", userID).
		First(&enterprise).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 用户没有企业
		}
		return nil, fmt.Errorf("查询用户企业失败: %w", err)
	}

	return &enterprise, nil
}

// GetOwnerEnterprises 获取用户拥有的企业列表（OWNER专用）
func (s *UserManagementService) GetOwnerEnterprises(ctx context.Context, userID string) ([]model.Enterprise, error) {
	var enterprises []model.Enterprise
	err := s.db.WithContext(ctx).
		Where("owner_id = ?", userID).
		Find(&enterprises).Error
	if err != nil {
		return nil, fmt.Errorf("查询用户拥有的企业失败: %w", err)
	}
	return enterprises, nil
}

// GetAllUsersForOwner 获取OWNER用户可管理的所有用户（跨企业）
func (s *UserManagementService) GetAllUsersForOwner(ctx context.Context, ownerID string, page, pageSize int, keyword, roleID, status, enterpriseID string) (*PaginatedEnterpriseUsers, error) {
	// 首先获取OWNER拥有的所有企业
	ownedEnterprises, err := s.GetOwnerEnterprises(ctx, ownerID)
	if err != nil {
		return nil, err
	}

	if len(ownedEnterprises) == 0 {
		return &PaginatedEnterpriseUsers{
			Users: []model.EnterpriseUserResponse{},
			Total: 0,
			Page:  page,
			Limit: pageSize,
		}, nil
	}

	// 提取企业ID列表
	var enterpriseIDs []string
	for _, enterprise := range ownedEnterprises {
		enterpriseIDs = append(enterpriseIDs, enterprise.ID)
	}

	// 如果指定了特定企业ID，验证OWNER是否拥有该企业
	if enterpriseID != "" {
		found := false
		for _, id := range enterpriseIDs {
			if id == enterpriseID {
				found = true
				break
			}
		}
		if !found {
			return nil, errors.New("您没有权限访问该企业的用户")
		}
		enterpriseIDs = []string{enterpriseID} // 只查询指定企业
	}

	var users []model.EnterpriseUser
	var total int64

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 构建查询
	query := s.db.WithContext(ctx).Model(&model.EnterpriseUser{}).
		Where("enterprise_id IN ?", enterpriseIDs)

	// 添加过滤条件
	if status != "" {
		query = query.Where("status = ?", status)
	}

	if roleID != "" {
		query = query.Where("role_id = ?", roleID)
	}

	// 关键词搜索
	if keyword != "" {
		searchPattern := "%" + keyword + "%"
		query = query.Joins("LEFT JOIN users ON enterprise_users.user_id = users.id").
			Where("users.user_name LIKE ? OR users.email LIKE ? OR users.phone LIKE ?",
				searchPattern, searchPattern, searchPattern)
	}

	// 查询总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取用户总数失败: %w", err)
	}

	// 查询用户列表
	if err := query.Preload("User").Preload("Role").Preload("Enterprise").Preload("InvitedByUser").
		Order("enterprise_id ASC, is_owner DESC, created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&users).Error; err != nil {
		return nil, fmt.Errorf("获取用户列表失败: %w", err)
	}

	// 转换为响应格式
	var userResponses []model.EnterpriseUserResponse
	for _, user := range users {
		response := model.EnterpriseUserResponse{
			ID:           user.ID,
			EnterpriseID: user.EnterpriseID,
			UserID:       user.UserID,
			RoleID:       user.RoleID,
			Status:       user.Status,
			JoinedAt:     user.JoinedAt,
			IsOwner:      user.IsOwner,
			CreatedAt:    user.CreatedAt,
		}

		if user.User != nil {
			response.UserName = user.User.UserName
			if user.User.Email != nil {
				response.Email = *user.User.Email
			}
			response.Phone = user.User.Phone
		}

		if user.Role != nil {
			response.RoleName = user.Role.Name
		}

		userResponses = append(userResponses, response)
	}

	return &PaginatedEnterpriseUsers{
		Users: userResponses,
		Total: total,
		Page:  page,
		Limit: pageSize,
	}, nil
}

// ResolveEnterpriseID 根据数字ID解析企业的字符串ID
func (s *UserManagementService) ResolveEnterpriseID(ctx context.Context, numericID int) (string, error) {
	var enterprise model.Enterprise
	err := s.db.WithContext(ctx).
		Where("numeric_id = ?", numericID).
		First(&enterprise).Error
	if err != nil {
		return "", err
	}
	return enterprise.ID, nil
}

// EnsureOwnerConsistency 确保enterprises表和enterprise_users表的Owner数据一致性
func (s *UserManagementService) EnsureOwnerConsistency(ctx context.Context, enterpriseID, ownerID string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 确保enterprise_users表中存在对应的记录
		var enterpriseUser model.EnterpriseUser
		err := tx.Where("enterprise_id = ? AND user_id = ?", enterpriseID, ownerID).First(&enterpriseUser).Error

		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果不存在，需要创建一个默认的Owner记录
			// 首先获取一个默认角色（通常是管理员角色）
			var defaultRole model.Role
			if err := tx.Where("code = ? OR name LIKE ?", "admin", "%管理员%").First(&defaultRole).Error; err != nil {
				return fmt.Errorf("获取默认管理员角色失败: %w", err)
			}

			// 创建企业用户关联记录
			newEnterpriseUser := &model.EnterpriseUser{
				ID:           util.GenerateID(),
				EnterpriseID: enterpriseID,
				UserID:       ownerID,
				RoleID:       defaultRole.ID,
				Status:       model.EnterpriseUserStatusActive,
				JoinedAt:     time.Now(),
				IsOwner:      true,
				CreatedAt:    time.Now(),
				UpdatedAt:    time.Now(),
			}

			if err := tx.Create(newEnterpriseUser).Error; err != nil {
				return fmt.Errorf("创建企业所有者关联记录失败: %w", err)
			}
		} else if err != nil {
			return fmt.Errorf("查询企业用户关联失败: %w", err)
		} else {
			// 如果存在，确保is_owner字段为true
			if !enterpriseUser.IsOwner {
				if err := tx.Model(&enterpriseUser).Update("is_owner", true).Error; err != nil {
					return fmt.Errorf("更新企业所有者状态失败: %w", err)
				}
			}
		}

		return nil
	})
}

// CheckAndFixOwnerConsistency 检查并修复Owner数据一致性
func (s *UserManagementService) CheckAndFixOwnerConsistency(ctx context.Context) error {
	// 查找所有企业
	var enterprises []model.Enterprise
	if err := s.db.WithContext(ctx).Find(&enterprises).Error; err != nil {
		return fmt.Errorf("查询企业列表失败: %w", err)
	}

	for _, enterprise := range enterprises {
		if enterprise.OwnerID != nil {
			// 确保每个企业的所有者在enterprise_users表中有对应记录
			if err := s.EnsureOwnerConsistency(ctx, enterprise.ID, *enterprise.OwnerID); err != nil {
				return fmt.Errorf("修复企业 %s 的所有者一致性失败: %w", enterprise.ID, err)
			}
		}
	}

	return nil
}

// logPermissionOperation 记录权限操作日志
func (s *UserManagementService) logPermissionOperation(ctx context.Context, userID, targetUserID, enterpriseID, operationType, resourceType, resourceID string, oldValue, newValue interface{}, reason string) {
	s.logPermissionOperationWithTx(ctx, s.db, userID, targetUserID, enterpriseID, operationType, resourceType, resourceID, oldValue, newValue, reason)
}

// logPermissionOperationWithTx 使用事务记录权限操作日志
func (s *UserManagementService) logPermissionOperationWithTx(ctx context.Context, tx *gorm.DB, userID, targetUserID, enterpriseID, operationType, resourceType, resourceID string, oldValue, newValue interface{}, reason string) {
	log := &model.PermissionLog{
		ID:            util.GenerateID(),
		UserID:        userID,
		TargetUserID:  &targetUserID,
		EnterpriseID:  &enterpriseID,
		OperationType: operationType,
		ResourceType:  resourceType,
		ResourceID:    resourceID,
		OldValue:      oldValue,
		NewValue:      newValue,
		Reason:        reason,
		CreatedAt:     time.Now(),
	}

	tx.Create(log)
}

// SearchUsers 搜索用户（用于邀请功能）
func (s *UserManagementService) SearchUsers(ctx context.Context, keyword string, page, pageSize int) (*PaginatedUsers, error) {
	var users []model.User
	var total int64

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 构建搜索查询
	query := s.db.Model(&model.User{}).Where("is_active = ?", true)

	// 添加搜索条件
	if keyword != "" {
		searchPattern := "%" + keyword + "%"
		query = query.Where("user_name LIKE ? OR email LIKE ? OR phone LIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	// 查询总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取用户总数失败: %w", err)
	}

	// 查询用户列表
	if err := query.Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&users).Error; err != nil {
		return nil, fmt.Errorf("搜索用户失败: %w", err)
	}

	// 转换为响应格式
	var userResponses []model.UserSearchResponse
	for _, user := range users {
		email := ""
		if user.Email != nil {
			email = *user.Email
		}
		response := model.UserSearchResponse{
			ID:         user.ID,
			UserName:   user.UserName,
			Email:      email,
			Phone:      user.Phone,
			Avatar:     user.Avatar,
			Department: user.Department,
			Position:   user.Position,
			IsActive:   user.IsActive,
			CreatedAt:  user.CreatedAt,
		}
		userResponses = append(userResponses, response)
	}

	return &PaginatedUsers{
		Users: userResponses,
		Total: total,
		Page:  page,
		Limit: pageSize,
	}, nil
}

// GetEnterpriseUsersV2 获取企业用户列表（增强版）
func (s *UserManagementService) GetEnterpriseUsersV2(ctx context.Context, enterpriseID string, req model.EnterpriseUserListRequest) (*model.EnterpriseUserListResponseV2, error) {
	var users []model.EnterpriseUser
	var total int64

	// 计算偏移量
	offset := (req.Page - 1) * req.PageSize

	// 构建查询
	query := s.db.WithContext(ctx).Model(&model.EnterpriseUser{}).
		Where("enterprise_id = ?", enterpriseID)

	// 添加过滤条件
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	if req.RoleID != "" {
		query = query.Where("role_id = ?", req.RoleID)
	}

	if !req.IncludeOwner {
		query = query.Where("is_owner = ?", false)
	}

	// 关键词搜索
	if req.Keyword != "" {
		searchPattern := "%" + req.Keyword + "%"
		query = query.Joins("LEFT JOIN users ON enterprise_users.user_id = users.id").
			Where("users.user_name LIKE ? OR users.email LIKE ? OR users.phone LIKE ?",
				searchPattern, searchPattern, searchPattern)
	}

	// 查询总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取用户总数失败: %w", err)
	}

	// 查询用户列表
	if err := query.Preload("User").Preload("Role").Preload("InvitedByUser").
		Order("is_owner DESC, created_at DESC").
		Offset(offset).Limit(req.PageSize).
		Find(&users).Error; err != nil {
		return nil, fmt.Errorf("获取用户列表失败: %w", err)
	}

	// 转换为响应格式
	var userResponses []model.EnterpriseUserDetailResponse
	for _, user := range users {
		response := model.EnterpriseUserDetailResponse{
			ID:           user.ID,
			EnterpriseID: user.EnterpriseID,
			UserID:       user.UserID,
			RoleID:       user.RoleID,
			Status:       user.Status,
			IsOwner:      user.IsOwner,
			JoinedAt:     user.JoinedAt,
			InvitedBy:    user.InvitedBy,
			CreatedAt:    user.CreatedAt,
			UpdatedAt:    user.UpdatedAt,
		}

		if user.User != nil {
			response.UserName = user.User.UserName
			if user.User.Email != nil {
				response.Email = *user.User.Email
			}
			response.Phone = user.User.Phone
			response.Department = user.User.Department
			response.Position = user.User.Position
		}

		if user.Role != nil {
			response.RoleName = user.Role.Name
		}

		if user.InvitedByUser != nil {
			invitedByName := user.InvitedByUser.UserName
			response.InvitedByName = &invitedByName
		}

		userResponses = append(userResponses, response)
	}

	return &model.EnterpriseUserListResponseV2{
		Users:    userResponses,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// AssignRole 分配角色
func (s *UserManagementService) AssignRole(ctx context.Context, enterpriseID string, req model.AssignRoleRequest, operatorID string) error {
	// 检查用户是否在企业中
	var enterpriseUser model.EnterpriseUser
	if err := s.db.Where("enterprise_id = ? AND user_id = ?", enterpriseID, req.UserID).First(&enterpriseUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不在企业中")
		}
		return fmt.Errorf("查询企业用户关联失败: %w", err)
	}

	// 检查新角色是否存在
	var role model.Role
	if err := s.db.First(&role, "id = ?", req.RoleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return fmt.Errorf("查询角色失败: %w", err)
	}

	// 检查是否尝试分配Owner角色
	if role.Code == "owner" {
		return errors.New("不能直接分配Owner角色，请使用转让所有权功能")
	}

	// 不能修改Owner的角色
	if enterpriseUser.IsOwner {
		return errors.New("不能修改企业所有者的角色")
	}

	oldRoleID := enterpriseUser.RoleID

	// 更新角色
	enterpriseUser.RoleID = req.RoleID
	enterpriseUser.UpdatedAt = time.Now()

	if err := s.db.Save(&enterpriseUser).Error; err != nil {
		return fmt.Errorf("更新用户角色失败: %w", err)
	}

	// 记录操作日志
	s.logPermissionOperation(ctx, operatorID, req.UserID, enterpriseID,
		model.OperationTypeRoleAssign, "role", req.RoleID,
		map[string]string{"old_role_id": oldRoleID},
		map[string]string{"new_role_id": req.RoleID},
		req.Reason)

	return nil
}

// RemoveUser 移除用户
func (s *UserManagementService) RemoveUser(ctx context.Context, enterpriseID string, req model.RemoveUserRequest, operatorID string) error {
	// 检查用户是否在企业中
	var enterpriseUser model.EnterpriseUser
	if err := s.db.Where("enterprise_id = ? AND user_id = ?", enterpriseID, req.UserID).First(&enterpriseUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不在企业中")
		}
		return fmt.Errorf("查询企业用户关联失败: %w", err)
	}

	// 不能移除企业所有者
	if enterpriseUser.IsOwner {
		return errors.New("不能移除企业所有者，请先转让所有权")
	}

	// 删除企业用户关联
	if err := s.db.Delete(&enterpriseUser).Error; err != nil {
		return fmt.Errorf("删除企业用户关联失败: %w", err)
	}

	// 记录操作日志
	s.logPermissionOperation(ctx, operatorID, req.UserID, enterpriseID,
		model.OperationTypeUserRemove, "enterprise_user", enterpriseUser.ID,
		enterpriseUser, nil, req.Reason)

	return nil
}

// GrantSpecialPermission 授予特殊权限
func (s *UserManagementService) GrantSpecialPermission(ctx context.Context, enterpriseID string, req model.GrantPermissionRequest, operatorID string) error {
	// 检查用户是否在企业中
	var enterpriseUser model.EnterpriseUser
	if err := s.db.Where("enterprise_id = ? AND user_id = ?", enterpriseID, req.UserID).First(&enterpriseUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不在企业中")
		}
		return fmt.Errorf("查询企业用户关联失败: %w", err)
	}

	// 检查权限是否存在
	var permission model.Permission
	if err := s.db.First(&permission, "id = ?", req.PermissionID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("权限不存在")
		}
		return fmt.Errorf("查询权限失败: %w", err)
	}

	// 检查是否已经有该权限
	var existing model.UserPermission
	err := s.db.Where("user_id = ? AND permission_id = ? AND enterprise_id = ?",
		req.UserID, req.PermissionID, enterpriseID).First(&existing).Error
	if err == nil {
		return errors.New("用户已拥有该权限")
	}

	// 创建用户特殊权限
	userPermission := &model.UserPermission{
		ID:           util.GenerateID(),
		UserID:       req.UserID,
		PermissionID: req.PermissionID,
		EnterpriseID: &enterpriseID,
		GrantedBy:    operatorID,
		GrantedAt:    time.Now(),
		ExpiresAt:    req.ExpiresAt,
		IsActive:     true,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.db.Create(userPermission).Error; err != nil {
		return fmt.Errorf("创建用户特殊权限失败: %w", err)
	}

	// 记录操作日志
	s.logPermissionOperation(ctx, operatorID, req.UserID, enterpriseID,
		model.OperationTypePermissionGrant, "permission", req.PermissionID,
		nil, userPermission, req.Reason)

	return nil
}

// RevokeSpecialPermission 撤销特殊权限
func (s *UserManagementService) RevokeSpecialPermission(ctx context.Context, enterpriseID, userID, permissionID, operatorID, reason string) error {
	// 查找用户特殊权限
	var userPermission model.UserPermission
	if err := s.db.Where("user_id = ? AND permission_id = ? AND enterprise_id = ?",
		userID, permissionID, enterpriseID).First(&userPermission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户特殊权限不存在")
		}
		return fmt.Errorf("查询用户特殊权限失败: %w", err)
	}

	// 删除用户特殊权限
	if err := s.db.Delete(&userPermission).Error; err != nil {
		return fmt.Errorf("删除用户特殊权限失败: %w", err)
	}

	// 记录操作日志
	s.logPermissionOperation(ctx, operatorID, userID, enterpriseID,
		model.OperationTypePermissionRevoke, "permission", permissionID,
		userPermission, nil, reason)

	return nil
}

// GetUserPermissions 获取用户权限汇总
func (s *UserManagementService) GetUserPermissions(ctx context.Context, enterpriseID, userID string) (*model.UserPermissionSummaryResponse, error) {
	// 检查用户是否在企业中
	var enterpriseUser model.EnterpriseUser
	if err := s.db.Preload("User").Preload("Role").
		Where("enterprise_id = ? AND user_id = ?", enterpriseID, userID).
		First(&enterpriseUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不在企业中")
		}
		return nil, fmt.Errorf("查询企业用户关联失败: %w", err)
	}

	response := &model.UserPermissionSummaryResponse{
		UserID: userID,
		RoleID: enterpriseUser.RoleID,
	}

	if enterpriseUser.User != nil {
		response.UserName = enterpriseUser.User.UserName
	}

	if enterpriseUser.Role != nil {
		response.RoleName = enterpriseUser.Role.Name

		// 获取角色权限
		var rolePermissions []model.Permission
		if err := s.db.Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
			Where("role_permissions.role_id = ?", enterpriseUser.RoleID).
			Find(&rolePermissions).Error; err != nil {
			return nil, fmt.Errorf("查询角色权限失败: %w", err)
		}

		for _, perm := range rolePermissions {
			response.RolePermissions = append(response.RolePermissions, model.PermissionSummary{
				ID:          perm.ID,
				Code:        perm.Code,
				Name:        perm.Name,
				Description: perm.Description,
				Module:      perm.Module,
				Resource:    perm.Resource,
				Action:      perm.Action,
				Type:        perm.Type,
			})
			response.EffectivePermissions = append(response.EffectivePermissions, perm.Code)
		}
	}

	// 获取用户特殊权限
	var userPermissions []model.UserPermission
	if err := s.db.Preload("Permission").Preload("GrantedByUser").
		Where("user_id = ? AND enterprise_id = ? AND is_active = ?", userID, enterpriseID, true).
		Find(&userPermissions).Error; err != nil {
		return nil, fmt.Errorf("查询用户特殊权限失败: %w", err)
	}

	for _, userPerm := range userPermissions {
		// 检查权限是否过期
		if userPerm.ExpiresAt != nil && userPerm.ExpiresAt.Before(time.Now()) {
			continue
		}

		detail := model.UserPermissionDetail{
			ID:           userPerm.ID,
			PermissionID: userPerm.PermissionID,
			GrantedBy:    userPerm.GrantedBy,
			GrantedAt:    userPerm.GrantedAt,
			ExpiresAt:    userPerm.ExpiresAt,
			IsActive:     userPerm.IsActive,
		}

		if userPerm.Permission != nil {
			detail.Permission = model.PermissionSummary{
				ID:          userPerm.Permission.ID,
				Code:        userPerm.Permission.Code,
				Name:        userPerm.Permission.Name,
				Description: userPerm.Permission.Description,
				Module:      userPerm.Permission.Module,
				Resource:    userPerm.Permission.Resource,
				Action:      userPerm.Permission.Action,
				Type:        userPerm.Permission.Type,
			}
			// 添加到有效权限列表（如果不重复）
			found := false
			for _, existing := range response.EffectivePermissions {
				if existing == userPerm.Permission.Code {
					found = true
					break
				}
			}
			if !found {
				response.EffectivePermissions = append(response.EffectivePermissions, userPerm.Permission.Code)
			}
		}

		if userPerm.GrantedByUser != nil {
			detail.GrantedByName = userPerm.GrantedByUser.UserName
		}

		response.SpecialPermissions = append(response.SpecialPermissions, detail)
	}

	return response, nil
}

// PaginatedEnterpriseUsers 分页企业用户响应
type PaginatedEnterpriseUsers struct {
	Users []model.EnterpriseUserResponse `json:"users"`
	Total int64                          `json:"total"`
	Page  int                            `json:"page"`
	Limit int                            `json:"limit"`
}

// PaginatedUsers 分页用户响应
type PaginatedUsers struct {
	Users []model.UserSearchResponse `json:"users"`
	Total int64                      `json:"total"`
	Page  int                        `json:"page"`
	Limit int                        `json:"limit"`
}

// PaginatedSearchUsers 分页搜索用户响应
type PaginatedSearchUsers struct {
	Users []model.SearchUserResponse `json:"users"`
	Total int64                      `json:"total"`
	Page  int                        `json:"page"`
	Limit int                        `json:"limit"`
}
