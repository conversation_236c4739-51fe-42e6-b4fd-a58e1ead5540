package service

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"backend/client"
	"backend/model"
)

// TaxFilingService 税务申报服务
type TaxFilingService struct {
	db                  *gorm.DB
	logger              *zap.Logger
	taxFilingClient     *client.TaxFilingClient
	notificationService *TaxFilingNotificationService
	redisService        *RedisService
}

// NewTaxFilingService 创建税务申报服务
func NewTaxFilingService(
	db *gorm.DB,
	logger *zap.Logger,
	taxFilingClient *client.TaxFilingClient,
	notificationService *TaxFilingNotificationService,
	redisService *RedisService,
) *TaxFilingService {
	return &TaxFilingService{
		db:                  db,
		logger:              logger,
		taxFilingClient:     taxFilingClient,
		notificationService: notificationService,
		redisService:        redisService,
	}
}

// GetDB 获取数据库连接（用于控制器中的查询构建）
func (s *TaxFilingService) GetDB() *gorm.DB {
	return s.db
}

// CreateSubmission 创建税务申报
func (s *TaxFilingService) CreateSubmission(ctx context.Context, req *model.TaxFilingSubmissionCreateRequest) (*model.TaxFilingSubmission, error) {
	// 验证企业是否存在
	var enterprise model.Enterprise
	if err := s.db.First(&enterprise, "id = ?", req.EnterpriseID).Error; err != nil {
		s.logger.Error("Enterprise not found", zap.Error(err), zap.String("enterprise_id", req.EnterpriseID))
		return nil, fmt.Errorf("enterprise not found: %w", err)
	}

	// 验证省份配置是否存在且激活
	var province model.TaxFilingProvince
	if err := s.db.First(&province, "code = ? AND status = ?", req.ProvinceCode, model.TaxFilingProvinceStatusActive).Error; err != nil {
		s.logger.Error("Province not found or inactive", zap.Error(err), zap.String("province_code", req.ProvinceCode))
		return nil, fmt.Errorf("province not found or inactive: %w", err)
	}

	// 创建申报记录
	submission := &model.TaxFilingSubmission{
		ID:                  model.GenerateID(),
		EnterpriseID:        req.EnterpriseID,
		ProvinceCode:        req.ProvinceCode,
		ProvinceName:        province.Name,
		SubmissionType:      req.SubmissionType,
		BatchID:             req.BatchID,
		Priority:            req.Priority,
		CompanyName:         req.CompanyName,
		TaxID:               req.TaxID,
		RegistrationNumber:  req.RegistrationNumber,
		LegalRepresentative: req.LegalRepresentative,
		CompanyAddress:      req.CompanyAddress,
		ContactPhone:        req.ContactPhone,
		ContactEmail:        req.ContactEmail,
		TaxYear:             req.TaxYear,
		TaxMonth:            req.TaxMonth,
		TaxQuarter:          req.TaxQuarter,
		PeriodType:          req.PeriodType,
		TaxData:             req.TaxData,
		Notes:               req.Notes,
		AdditionalData:      req.AdditionalData,
		Status:              model.TaxFilingStatusPending,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}

	// 计算总金额
	submission.UpdateTotals()

	// 生成参考号
	refNumber := model.GenerateReferenceNumber(req.ProvinceCode)
	submission.ReferenceNumber = &refNumber

	// 开始数据库事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 保存申报记录
	if err := tx.Create(submission).Error; err != nil {
		tx.Rollback()
		s.logger.Error("Failed to create submission", zap.Error(err))
		return nil, fmt.Errorf("failed to create submission: %w", err)
	}

	// 创建状态历史记录
	history := model.CreateSystemStatusHistory(
		submission.ID,
		nil,
		model.TaxFilingStatusPending,
		"申报记录已创建",
	)
	if err := tx.Create(history).Error; err != nil {
		tx.Rollback()
		s.logger.Error("Failed to create status history", zap.Error(err))
		return nil, fmt.Errorf("failed to create status history: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		s.logger.Error("Failed to commit transaction", zap.Error(err))
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 缓存申报记录
	if err := s.redisService.CacheSubmission(ctx, submission); err != nil {
		s.logger.Warn("Failed to cache submission", zap.Error(err), zap.String("submission_id", submission.ID))
	}

	s.logger.Info("Tax submission created successfully",
		zap.String("submission_id", submission.ID),
		zap.String("enterprise_id", req.EnterpriseID),
		zap.String("province_code", req.ProvinceCode),
	)

	return submission, nil
}

// SubmitToTaxBureau 提交到税务局
func (s *TaxFilingService) SubmitToTaxBureau(ctx context.Context, submissionID string) error {
	// 获取申报记录
	submission, err := s.GetSubmissionByID(ctx, submissionID)
	if err != nil {
		return fmt.Errorf("failed to get submission: %w", err)
	}

	// 检查状态
	if submission.Status != model.TaxFilingStatusPending {
		return fmt.Errorf("submission status is not pending: %s", submission.Status)
	}

	// 更新状态为处理中
	if err := s.UpdateSubmissionStatus(ctx, submissionID, model.TaxFilingStatusProcessing, "开始提交到税务局", nil); err != nil {
		return fmt.Errorf("failed to update status to processing: %w", err)
	}

	// 构建请求数据
	request := s.buildTaxSubmissionRequest(submission)

	// 调用Python服务
	response, err := s.taxFilingClient.SubmitTaxReturn(ctx, request)
	if err != nil {
		// 更新状态为失败
		errorMsg := err.Error()
		if updateErr := s.UpdateSubmissionStatus(ctx, submissionID, model.TaxFilingStatusFailed, "提交失败", &errorMsg); updateErr != nil {
			s.logger.Error("Failed to update status to failed", zap.Error(updateErr))
		}
		return fmt.Errorf("failed to submit to tax bureau: %w", err)
	}

	// 更新申报记录
	updates := map[string]interface{}{
		"external_id":  response.ExternalID,
		"status":       model.TaxFilingSubmissionStatus(response.Status),
		"submitted_at": time.Now(),
		"updated_at":   time.Now(),
	}

	if response.EstimatedProcessingTime != nil {
		updates["estimated_processing_time"] = *response.EstimatedProcessingTime
	}

	if err := s.db.Model(&model.TaxFilingSubmission{}).Where("id = ?", submissionID).Updates(updates).Error; err != nil {
		s.logger.Error("Failed to update submission after successful submit", zap.Error(err))
		return fmt.Errorf("failed to update submission: %w", err)
	}

	// 创建状态历史
	if err := s.CreateStatusHistory(ctx, submissionID, model.TaxFilingStatusProcessing, model.TaxFilingSubmissionStatus(response.Status), response.Message, nil); err != nil {
		s.logger.Error("Failed to create status history", zap.Error(err))
	}

	// 更新缓存
	if err := s.redisService.InvalidateSubmissionCache(ctx, submissionID); err != nil {
		s.logger.Warn("Failed to invalidate cache", zap.Error(err))
	}

	// 发送通知
	go func() {
		if err := s.notificationService.SendSubmissionStatusNotification(ctx, submissionID, string(response.Status)); err != nil {
			s.logger.Error("Failed to send notification", zap.Error(err))
		}
	}()

	s.logger.Info("Tax submission submitted successfully",
		zap.String("submission_id", submissionID),
		zap.String("external_id", *response.ExternalID),
		zap.String("status", response.Status),
	)

	return nil
}

// GetSubmissionByID 根据ID获取申报记录
func (s *TaxFilingService) GetSubmissionByID(ctx context.Context, submissionID string) (*model.TaxFilingSubmission, error) {
	// 先从缓存获取
	if submission, err := s.redisService.GetCachedSubmission(ctx, submissionID); err == nil && submission != nil {
		return submission, nil
	}

	// 从数据库获取
	var submission model.TaxFilingSubmission
	if err := s.db.Preload("Enterprise").Preload("Province").First(&submission, "id = ?", submissionID).Error; err != nil {
		s.logger.Error("Failed to get submission", zap.Error(err), zap.String("submission_id", submissionID))
		return nil, fmt.Errorf("submission not found: %w", err)
	}

	// 缓存结果
	if err := s.redisService.CacheSubmission(ctx, &submission); err != nil {
		s.logger.Warn("Failed to cache submission", zap.Error(err))
	}

	return &submission, nil
}

// UpdateSubmissionStatus 更新申报状态
func (s *TaxFilingService) UpdateSubmissionStatus(ctx context.Context, submissionID string, newStatus model.TaxFilingSubmissionStatus, reason string, errorMessage *string) error {
	// 获取当前状态
	var currentSubmission model.TaxFilingSubmission
	if err := s.db.First(&currentSubmission, "id = ?", submissionID).Error; err != nil {
		return fmt.Errorf("submission not found: %w", err)
	}

	// 检查状态转换是否合法
	if !model.CanTransitionTo(currentSubmission.Status, newStatus) {
		return fmt.Errorf("invalid status transition from %s to %s", currentSubmission.Status, newStatus)
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新申报状态
	updates := map[string]interface{}{
		"status":     newStatus,
		"updated_at": time.Now(),
	}

	if errorMessage != nil {
		updates["error_message"] = *errorMessage
	}

	// 如果是完成状态，设置处理完成时间
	if newStatus == model.TaxFilingStatusAccepted || newStatus == model.TaxFilingStatusRejected {
		updates["processed_at"] = time.Now()
	}

	if err := tx.Model(&model.TaxFilingSubmission{}).Where("id = ?", submissionID).Updates(updates).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update submission status: %w", err)
	}

	// 创建状态历史
	history := model.CreateSystemStatusHistory(
		submissionID,
		&currentSubmission.Status,
		newStatus,
		reason,
	)
	if errorMessage != nil {
		history.ErrorMessage = errorMessage
	}

	if err := tx.Create(history).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create status history: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 更新缓存
	if err := s.redisService.InvalidateSubmissionCache(ctx, submissionID); err != nil {
		s.logger.Warn("Failed to invalidate cache", zap.Error(err))
	}

	s.logger.Info("Submission status updated",
		zap.String("submission_id", submissionID),
		zap.String("from_status", string(currentSubmission.Status)),
		zap.String("to_status", string(newStatus)),
		zap.String("reason", reason),
	)

	return nil
}

// SyncSubmissionStatus 同步申报状态
func (s *TaxFilingService) SyncSubmissionStatus(ctx context.Context, submissionID string) error {
	// 获取申报记录
	submission, err := s.GetSubmissionByID(ctx, submissionID)
	if err != nil {
		return fmt.Errorf("failed to get submission: %w", err)
	}

	// 检查是否有外部ID
	if submission.ExternalID == nil {
		return fmt.Errorf("submission has no external ID")
	}

	// 调用Python服务获取状态
	statusResponse, err := s.taxFilingClient.GetSubmissionStatus(ctx, *submission.ExternalID)
	if err != nil {
		s.logger.Error("Failed to get status from tax filing service", zap.Error(err))
		return fmt.Errorf("failed to get status: %w", err)
	}

	// 检查状态是否有变化
	newStatus := model.TaxFilingSubmissionStatus(statusResponse.Status)
	if submission.Status == newStatus {
		return nil // 状态没有变化
	}

	// 更新状态
	reason := "状态同步更新"
	var errorMessage *string
	if statusResponse.ErrorMessage != nil {
		errorMessage = statusResponse.ErrorMessage
	}

	if err := s.UpdateSubmissionStatus(ctx, submissionID, newStatus, reason, errorMessage); err != nil {
		return fmt.Errorf("failed to update status: %w", err)
	}

	// 如果有处理完成时间，更新它
	if statusResponse.ProcessedAt != nil {
		if err := s.db.Model(&model.TaxFilingSubmission{}).Where("id = ?", submissionID).Update("processed_at", *statusResponse.ProcessedAt).Error; err != nil {
			s.logger.Error("Failed to update processed_at", zap.Error(err))
		}
	}

	return nil
}

// CreateStatusHistory 创建状态历史记录
func (s *TaxFilingService) CreateStatusHistory(ctx context.Context, submissionID string, fromStatus, toStatus model.TaxFilingSubmissionStatus, reason string, errorMessage *string) error {
	history := model.CreateSystemStatusHistory(
		submissionID,
		&fromStatus,
		toStatus,
		reason,
	)
	if errorMessage != nil {
		history.ErrorMessage = errorMessage
	}

	if err := s.db.Create(history).Error; err != nil {
		return fmt.Errorf("failed to create status history: %w", err)
	}

	return nil
}

// buildTaxSubmissionRequest 构建税务申报请求
func (s *TaxFilingService) buildTaxSubmissionRequest(submission *model.TaxFilingSubmission) *client.TaxSubmissionRequest {
	// 转换公司信息
	companyInfo := client.CompanyInfo{
		CompanyName:         submission.CompanyName,
		TaxID:               submission.TaxID,
		RegistrationNumber:  submission.RegistrationNumber,
		LegalRepresentative: submission.LegalRepresentative,
		Address:             submission.CompanyAddress,
		Phone:               submission.ContactPhone,
		Email:               submission.ContactEmail,
	}

	// 转换税务期间
	taxPeriod := client.TaxPeriod{
		Year:       submission.TaxYear,
		Month:      submission.TaxMonth,
		Quarter:    submission.TaxQuarter,
		PeriodType: string(submission.PeriodType),
	}

	// 转换税务数据
	var taxData []client.TaxData
	for _, td := range submission.TaxData {
		taxData = append(taxData, client.TaxData{
			TaxType:       td.TaxType,
			TaxableAmount: td.TaxableAmount,
			TaxRate:       td.TaxRate,
			TaxAmount:     td.TaxAmount,
			Deductions:    td.Deductions,
			Credits:       td.Credits,
			FinalAmount:   td.FinalAmount,
		})
	}

	return &client.TaxSubmissionRequest{
		ProvinceCode:   submission.ProvinceCode,
		CompanyInfo:    companyInfo,
		TaxPeriod:      taxPeriod,
		TaxData:        taxData,
		Notes:          submission.Notes,
		AdditionalData: submission.AdditionalData,
	}
}

// RetrySubmission 重试申报
func (s *TaxFilingService) RetrySubmission(ctx context.Context, submissionID string) error {
	// 获取申报记录
	submission, err := s.GetSubmissionByID(ctx, submissionID)
	if err != nil {
		return fmt.Errorf("failed to get submission: %w", err)
	}

	// 检查是否可以重试
	if !submission.CanRetry() {
		return fmt.Errorf("submission cannot be retried")
	}

	// 增加重试次数
	if err := s.db.Model(&model.TaxFilingSubmission{}).Where("id = ?", submissionID).Updates(map[string]interface{}{
		"retry_count":   submission.RetryCount + 1,
		"status":        model.TaxFilingStatusPending,
		"error_message": nil,
		"updated_at":    time.Now(),
	}).Error; err != nil {
		return fmt.Errorf("failed to update retry count: %w", err)
	}

	// 重新提交
	return s.SubmitToTaxBureau(ctx, submissionID)
}

// CancelSubmission 取消申报
func (s *TaxFilingService) CancelSubmission(ctx context.Context, submissionID string) error {
	// 获取申报记录
	submission, err := s.GetSubmissionByID(ctx, submissionID)
	if err != nil {
		return fmt.Errorf("failed to get submission: %w", err)
	}

	// 检查状态是否可以取消
	if model.IsTerminalStatus(submission.Status) {
		return fmt.Errorf("submission cannot be cancelled in status: %s", submission.Status)
	}

	// 如果已经提交到外部系统，尝试取消
	if submission.ExternalID != nil {
		if err := s.taxFilingClient.CancelSubmission(ctx, *submission.ExternalID); err != nil {
			s.logger.Warn("Failed to cancel submission in external system", zap.Error(err))
			// 继续执行本地取消
		}
	}

	// 更新状态为已取消
	return s.UpdateSubmissionStatus(ctx, submissionID, model.TaxFilingStatusCancelled, "用户取消申报", nil)
}
