// Package service provides business logic layer for the tax management system.
// It implements authentication, enterprise management, invoice processing, and other core business operations.
package service

import (
	"fmt"
	"time"

	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// OperationLogService provides operation logging functionality for the tax management system.
// It handles creation, retrieval, and management of operation logs for audit purposes.
type OperationLogService struct {
	db *gorm.DB
}

// NewOperationLogService creates a new operation log service instance with the provided database connection.
// It returns a configured OperationLogService ready to handle operation logging operations.
func NewOperationLogService(db *gorm.DB) *OperationLogService {
	return &OperationLogService{db: db}
}

// CreateOperationLog 创建操作日志
func (s *OperationLogService) CreateOperationLog(log *model.OperationLog) error {
	if log.ID == "" {
		log.ID = util.GenerateID()
	}

	if err := s.db.Create(log).Error; err != nil {
		return fmt.Errorf("创建操作日志失败: %w", err)
	}

	return nil
}

// LogOperation 记录操作日志（便捷方法）
func (s *OperationLogService) LogOperation(
	userID, userName, enterpriseID, module, operation, resourceType, resourceID string,
	requestMethod, requestURL string,
	requestParams map[string]interface{},
	responseStatus int,
	responseMessage, ipAddress, userAgent string,
	executionTime int,
	errorMessage string,
) error {
	log := &model.OperationLog{
		ID:             util.GenerateID(),
		UserID:         userID,
		UserName:       userName,
		EnterpriseID:   enterpriseID,
		Module:         module,
		Operation:      operation,
		ResourceType:   resourceType,
		ResourceID:     resourceID,
		RequestMethod:  requestMethod,
		RequestURL:     requestURL,
		RequestParams:  requestParams,
		ResponseStatus: responseStatus,
		ResponseMsg:    responseMessage,
		IPAddress:      ipAddress,
		UserAgent:      userAgent,
		ExecutionTime:  executionTime,
		ErrorMessage:   errorMessage,
		CreatedAt:      time.Now(),
	}

	return s.CreateOperationLog(log)
}

// GetOperationLogList 获取操作日志列表
func (s *OperationLogService) GetOperationLogList(filter *model.OperationLogFilter) (*model.OperationLogListResponse, error) {
	var logs []model.OperationLog
	var total int64

	// 构建查询
	query := s.db.Model(&model.OperationLog{})

	// 添加过滤条件
	if filter.UserID != "" {
		query = query.Where("user_id = ?", filter.UserID)
	}
	if filter.EnterpriseID != "" {
		query = query.Where("enterprise_id = ?", filter.EnterpriseID)
	}
	if filter.Module != "" {
		query = query.Where("module = ?", filter.Module)
	}
	if filter.Operation != "" {
		query = query.Where("operation = ?", filter.Operation)
	}
	if filter.ResourceType != "" {
		query = query.Where("resource_type = ?", filter.ResourceType)
	}
	if filter.ResourceID != "" {
		query = query.Where("resource_id = ?", filter.ResourceID)
	}
	if filter.IPAddress != "" {
		query = query.Where("ip_address = ?", filter.IPAddress)
	}
	if !filter.StartDate.IsZero() {
		query = query.Where("created_at >= ?", filter.StartDate)
	}
	if !filter.EndDate.IsZero() {
		query = query.Where("created_at <= ?", filter.EndDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取日志总数失败: %w", err)
	}

	// 分页查询
	offset := (filter.Page - 1) * filter.PageSize
	if err := query.Offset(offset).Limit(filter.PageSize).
		Order("created_at DESC").Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("查询日志列表失败: %w", err)
	}

	totalPages := int((total + int64(filter.PageSize) - 1) / int64(filter.PageSize))

	return &model.OperationLogListResponse{
		Logs:       logs,
		Total:      total,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetUserOperationStats 获取用户操作统计
func (s *OperationLogService) GetUserOperationStats(userID string, days int) (map[string]interface{}, error) {
	startDate := time.Now().AddDate(0, 0, -days)

	// 总操作次数
	var totalCount int64
	if err := s.db.Model(&model.OperationLog{}).
		Where("user_id = ? AND created_at >= ?", userID, startDate).
		Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("获取总操作次数失败: %w", err)
	}

	// 按模块统计
	var moduleStats []struct {
		Module string `json:"module"`
		Count  int64  `json:"count"`
	}
	if err := s.db.Model(&model.OperationLog{}).
		Select("module, COUNT(*) as count").
		Where("user_id = ? AND created_at >= ?", userID, startDate).
		Group("module").
		Order("count DESC").
		Scan(&moduleStats).Error; err != nil {
		return nil, fmt.Errorf("获取模块统计失败: %w", err)
	}

	// 按操作类型统计
	var operationStats []struct {
		Operation string `json:"operation"`
		Count     int64  `json:"count"`
	}
	if err := s.db.Model(&model.OperationLog{}).
		Select("operation, COUNT(*) as count").
		Where("user_id = ? AND created_at >= ?", userID, startDate).
		Group("operation").
		Order("count DESC").
		Scan(&operationStats).Error; err != nil {
		return nil, fmt.Errorf("获取操作统计失败: %w", err)
	}

	return map[string]interface{}{
		"totalCount":     totalCount,
		"moduleStats":    moduleStats,
		"operationStats": operationStats,
	}, nil
}

// CleanupOldLogs 清理旧日志（定期任务）
func (s *OperationLogService) CleanupOldLogs(retentionDays int) error {
	cutoffDate := time.Now().AddDate(0, 0, -retentionDays)

	result := s.db.Where("created_at < ?", cutoffDate).Delete(&model.OperationLog{})
	if result.Error != nil {
		return fmt.Errorf("清理旧日志失败: %w", result.Error)
	}

	fmt.Printf("清理了 %d 条旧日志记录\n", result.RowsAffected)
	return nil
}

// GetResourceOperationHistory 获取资源操作历史
func (s *OperationLogService) GetResourceOperationHistory(resourceType, resourceID string, limit int) ([]model.OperationLog, error) {
	var logs []model.OperationLog

	query := s.db.Where("resource_type = ? AND resource_id = ?", resourceType, resourceID).
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("获取资源操作历史失败: %w", err)
	}

	return logs, nil
}
