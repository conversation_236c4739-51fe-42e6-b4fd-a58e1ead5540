// Package service provides business logic layer for the tax management system.
// It implements audit log management operations including CRUD operations and business validations.
package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// AuditLogService defines the interface for audit log business logic
type AuditLogService interface {
	CreateAuditLog(ctx context.Context, req CreateAuditLogRequest) (*model.AuditLog, error)
	GetAuditLogs(ctx context.Context, req GetAuditLogsRequest) (*model.AuditLogListResponse, error)
	GetAuditLogByID(ctx context.Context, id string) (*model.AuditLog, error)
	DeleteAuditLog(ctx context.Context, id string) error
	GetAuditLogsByUser(ctx context.Context, userID string, req GetAuditLogsRequest) (*model.AuditLogListResponse, error)
	GetAuditLogsByResource(ctx context.Context, resourceType, resourceID string, req GetAuditLogsRequest) (*model.AuditLogListResponse, error)
	CleanupOldLogs(ctx context.Context, retentionDays int) error
	GetAuditLogStats(ctx context.Context, req GetAuditLogStatsRequest) (*AuditLogStatsResponse, error)
}

type auditLogService struct {
	db *gorm.DB
}

// NewAuditLogService creates a new audit log service instance
func NewAuditLogService(db *gorm.DB) AuditLogService {
	return &auditLogService{db: db}
}

// CreateAuditLogRequest represents a request to create an audit log
type CreateAuditLogRequest struct {
	UserID        string                 `json:"userId" binding:"required"`
	UserName      string                 `json:"userName" binding:"required"`
	Action        string                 `json:"action" binding:"required"`
	ResourceType  string                 `json:"resourceType" binding:"required"`
	ResourceID    string                 `json:"resourceId" binding:"required"`
	Description   string                 `json:"description"`
	IPAddress     string                 `json:"ipAddress"`
	UserAgent     string                 `json:"userAgent"`
	BeforeState   map[string]interface{} `json:"beforeState"`
	AfterState    map[string]interface{} `json:"afterState"`
	Status        string                 `json:"status"`
	ErrorMessage  string                 `json:"errorMessage"`
	Module        string                 `json:"module"`
	Duration      int64                  `json:"duration"`
	EnterpriseID  string                 `json:"enterpriseId"`
	RiskLevel     string                 `json:"riskLevel"`
	SensitiveData bool                   `json:"sensitiveData"`
}

// GetAuditLogsRequest represents a request to get audit logs with pagination and filtering
type GetAuditLogsRequest struct {
	UserID       string    `json:"userId"`
	Action       string    `json:"action"`
	ResourceType string    `json:"resourceType"`
	ResourceID   string    `json:"resourceId"`
	StartDate    time.Time `json:"startDate"`
	EndDate      time.Time `json:"endDate"`
	Status       string    `json:"status"`
	Module       string    `json:"module"`
	EnterpriseID string    `json:"enterpriseId"`
	RiskLevel    string    `json:"riskLevel"`
	Page         int       `json:"page" binding:"min=1"`
	PageSize     int       `json:"pageSize" binding:"min=1,max=100"`
	SortBy       string    `json:"sortBy"`
	SortOrder    string    `json:"sortOrder"`
}

// GetAuditLogStatsRequest represents a request to get audit log statistics
type GetAuditLogStatsRequest struct {
	StartDate    time.Time `json:"startDate"`
	EndDate      time.Time `json:"endDate"`
	EnterpriseID string    `json:"enterpriseId"`
	UserID       string    `json:"userId"`
}

// AuditLogStatsResponse represents audit log statistics
type AuditLogStatsResponse struct {
	TotalLogs       int64                `json:"totalLogs"`
	LogsByAction    []ActionStatistic    `json:"logsByAction"`
	LogsByModule    []ModuleStatistic    `json:"logsByModule"`
	LogsByRiskLevel []RiskLevelStatistic `json:"logsByRiskLevel"`
	LogsByStatus    []StatusStatistic    `json:"logsByStatus"`
	TopUsers        []UserStatistic      `json:"topUsers"`
	DailyStats      []DailyLogStatistic  `json:"dailyStats"`
}

type ActionStatistic struct {
	Action string `json:"action"`
	Count  int64  `json:"count"`
}

type ModuleStatistic struct {
	Module string `json:"module"`
	Count  int64  `json:"count"`
}

type RiskLevelStatistic struct {
	RiskLevel string `json:"riskLevel"`
	Count     int64  `json:"count"`
}

type StatusStatistic struct {
	Status string `json:"status"`
	Count  int64  `json:"count"`
}

type UserStatistic struct {
	UserID   string `json:"userId"`
	UserName string `json:"userName"`
	Count    int64  `json:"count"`
}

type DailyLogStatistic struct {
	Date  string `json:"date"`
	Count int64  `json:"count"`
}

// CreateAuditLog creates a new audit log entry
func (s *auditLogService) CreateAuditLog(ctx context.Context, req CreateAuditLogRequest) (*model.AuditLog, error) {
	auditLog := model.AuditLog{
		ID:            util.GenerateID(),
		UserID:        req.UserID,
		UserName:      req.UserName,
		Action:        req.Action,
		ResourceType:  req.ResourceType,
		ResourceID:    req.ResourceID,
		Description:   req.Description,
		IPAddress:     req.IPAddress,
		UserAgent:     req.UserAgent,
		Status:        req.Status,
		ErrorMessage:  req.ErrorMessage,
		Module:        req.Module,
		Duration:      req.Duration,
		EnterpriseID:  req.EnterpriseID,
		RiskLevel:     req.RiskLevel,
		SensitiveData: req.SensitiveData,
		IsDeleted:     false,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// Set default values
	if auditLog.Status == "" {
		auditLog.Status = "success"
	}
	if auditLog.RiskLevel == "" {
		auditLog.RiskLevel = "low"
	}

	// Convert state maps to JSON strings
	if req.BeforeState != nil {
		if beforeStateJSON, err := util.ToJSONString(req.BeforeState); err == nil {
			auditLog.BeforeState = beforeStateJSON
		}
	}
	if req.AfterState != nil {
		if afterStateJSON, err := util.ToJSONString(req.AfterState); err == nil {
			auditLog.AfterState = afterStateJSON
		}
	}

	if err := s.db.WithContext(ctx).Create(&auditLog).Error; err != nil {
		return nil, fmt.Errorf("创建审计日志失败: %w", err)
	}

	return &auditLog, nil
}

// GetAuditLogs retrieves audit logs with pagination and filtering
func (s *auditLogService) GetAuditLogs(ctx context.Context, req GetAuditLogsRequest) (*model.AuditLogListResponse, error) {
	var auditLogs []model.AuditLog
	var total int64

	// Build query
	query := s.db.WithContext(ctx).Model(&model.AuditLog{}).Where("is_deleted = ?", false)

	// Apply filters
	if req.UserID != "" {
		query = query.Where("user_id = ?", req.UserID)
	}
	if req.Action != "" {
		query = query.Where("action = ?", req.Action)
	}
	if req.ResourceType != "" {
		query = query.Where("resource_type = ?", req.ResourceType)
	}
	if req.ResourceID != "" {
		query = query.Where("resource_id = ?", req.ResourceID)
	}
	if !req.StartDate.IsZero() {
		query = query.Where("created_at >= ?", req.StartDate)
	}
	if !req.EndDate.IsZero() {
		query = query.Where("created_at <= ?", req.EndDate)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.Module != "" {
		query = query.Where("module = ?", req.Module)
	}
	if req.EnterpriseID != "" {
		query = query.Where("enterprise_id = ?", req.EnterpriseID)
	}
	if req.RiskLevel != "" {
		query = query.Where("risk_level = ?", req.RiskLevel)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取审计日志总数失败: %w", err)
	}

	// Apply pagination and sorting
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize

	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	orderBy := fmt.Sprintf("%s %s", req.SortBy, req.SortOrder)
	if err := query.Order(orderBy).Offset(offset).Limit(req.PageSize).Find(&auditLogs).Error; err != nil {
		return nil, fmt.Errorf("获取审计日志列表失败: %w", err)
	}

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &model.AuditLogListResponse{
		Logs:       auditLogs,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetAuditLogByID retrieves an audit log by ID
func (s *auditLogService) GetAuditLogByID(ctx context.Context, id string) (*model.AuditLog, error) {
	var auditLog model.AuditLog

	if err := s.db.WithContext(ctx).
		Where("id = ? AND is_deleted = ?", id, false).
		First(&auditLog).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("审计日志不存在")
		}
		return nil, fmt.Errorf("获取审计日志详情失败: %w", err)
	}

	return &auditLog, nil
}

// DeleteAuditLog soft deletes an audit log by ID
func (s *auditLogService) DeleteAuditLog(ctx context.Context, id string) error {
	var auditLog model.AuditLog

	// Check if audit log exists
	if err := s.db.WithContext(ctx).Where("id = ? AND is_deleted = ?", id, false).First(&auditLog).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("审计日志不存在")
		}
		return fmt.Errorf("查找审计日志失败: %w", err)
	}

	// Soft delete
	now := time.Now()
	updates := map[string]interface{}{
		"is_deleted": true,
		"deleted_at": &now,
		"updated_at": now,
	}

	if err := s.db.WithContext(ctx).Model(&auditLog).Updates(updates).Error; err != nil {
		return fmt.Errorf("删除审计日志失败: %w", err)
	}

	return nil
}

// GetAuditLogsByUser retrieves audit logs for a specific user
func (s *auditLogService) GetAuditLogsByUser(ctx context.Context, userID string, req GetAuditLogsRequest) (*model.AuditLogListResponse, error) {
	req.UserID = userID
	return s.GetAuditLogs(ctx, req)
}

// GetAuditLogsByResource retrieves audit logs for a specific resource
func (s *auditLogService) GetAuditLogsByResource(ctx context.Context, resourceType, resourceID string, req GetAuditLogsRequest) (*model.AuditLogListResponse, error) {
	req.ResourceType = resourceType
	req.ResourceID = resourceID
	return s.GetAuditLogs(ctx, req)
}

// CleanupOldLogs removes audit logs older than the specified retention period
func (s *auditLogService) CleanupOldLogs(ctx context.Context, retentionDays int) error {
	if retentionDays <= 0 {
		return fmt.Errorf("保留天数必须大于0")
	}

	cutoffDate := time.Now().AddDate(0, 0, -retentionDays)

	result := s.db.WithContext(ctx).
		Where("created_at < ? AND is_deleted = ?", cutoffDate, false).
		Delete(&model.AuditLog{})

	if result.Error != nil {
		return fmt.Errorf("清理旧审计日志失败: %w", result.Error)
	}

	return nil
}

// GetAuditLogStats retrieves audit log statistics
func (s *auditLogService) GetAuditLogStats(ctx context.Context, req GetAuditLogStatsRequest) (*AuditLogStatsResponse, error) {
	stats := &AuditLogStatsResponse{}

	// Build base query
	query := s.db.WithContext(ctx).Model(&model.AuditLog{}).Where("is_deleted = ?", false)

	// Apply filters
	if !req.StartDate.IsZero() {
		query = query.Where("created_at >= ?", req.StartDate)
	}
	if !req.EndDate.IsZero() {
		query = query.Where("created_at <= ?", req.EndDate)
	}
	if req.EnterpriseID != "" {
		query = query.Where("enterprise_id = ?", req.EnterpriseID)
	}
	if req.UserID != "" {
		query = query.Where("user_id = ?", req.UserID)
	}

	// Get total count
	if err := query.Count(&stats.TotalLogs).Error; err != nil {
		return nil, fmt.Errorf("获取审计日志总数失败: %w", err)
	}

	// Get statistics by action
	var actionStats []ActionStatistic
	if err := query.Select("action, count(*) as count").
		Group("action").
		Order("count DESC").
		Scan(&actionStats).Error; err != nil {
		return nil, fmt.Errorf("获取操作统计失败: %w", err)
	}
	stats.LogsByAction = actionStats

	// Get statistics by module
	var moduleStats []ModuleStatistic
	if err := query.Select("module, count(*) as count").
		Group("module").
		Order("count DESC").
		Scan(&moduleStats).Error; err != nil {
		return nil, fmt.Errorf("获取模块统计失败: %w", err)
	}
	stats.LogsByModule = moduleStats

	// Get statistics by risk level
	var riskStats []RiskLevelStatistic
	if err := query.Select("risk_level, count(*) as count").
		Group("risk_level").
		Order("count DESC").
		Scan(&riskStats).Error; err != nil {
		return nil, fmt.Errorf("获取风险等级统计失败: %w", err)
	}
	stats.LogsByRiskLevel = riskStats

	// Get statistics by status
	var statusStats []StatusStatistic
	if err := query.Select("status, count(*) as count").
		Group("status").
		Order("count DESC").
		Scan(&statusStats).Error; err != nil {
		return nil, fmt.Errorf("获取状态统计失败: %w", err)
	}
	stats.LogsByStatus = statusStats

	// Get top users
	var userStats []UserStatistic
	if err := query.Select("user_id, user_name, count(*) as count").
		Group("user_id, user_name").
		Order("count DESC").
		Limit(10).
		Scan(&userStats).Error; err != nil {
		return nil, fmt.Errorf("获取用户统计失败: %w", err)
	}
	stats.TopUsers = userStats

	// Get daily statistics (last 30 days)
	var dailyStats []DailyLogStatistic
	if err := query.Select("DATE(created_at) as date, count(*) as count").
		Where("created_at >= ?", time.Now().AddDate(0, 0, -30)).
		Group("DATE(created_at)").
		Order("date DESC").
		Scan(&dailyStats).Error; err != nil {
		return nil, fmt.Errorf("获取每日统计失败: %w", err)
	}
	stats.DailyStats = dailyStats

	return stats, nil
}
