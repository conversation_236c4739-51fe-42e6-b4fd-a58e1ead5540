package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"backend/model"
)

// TaxFilingCallbackService 税务申报回调服务
type TaxFilingCallbackService struct {
	db           *gorm.DB
	logger       *zap.Logger
	redisService *RedisService
	httpClient   *http.Client

	// 回调处理控制
	stopChan  chan struct{}
	wg        sync.WaitGroup
	isRunning bool
	mutex     sync.RWMutex
}

// CallbackConfig 回调配置
type CallbackConfig struct {
	Enabled         bool          `json:"enabled"`
	ProcessInterval time.Duration `json:"process_interval"`
	BatchSize       int           `json:"batch_size"`
	MaxRetries      int           `json:"max_retries"`
	Timeout         time.Duration `json:"timeout"`
	RetryDelay      time.Duration `json:"retry_delay"`
}

// NewTaxFilingCallbackService 创建回调服务
func NewTaxFilingCallbackService(
	db *gorm.DB,
	logger *zap.Logger,
	redisService *RedisService,
) *TaxFilingCallbackService {
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	return &TaxFilingCallbackService{
		db:           db,
		logger:       logger,
		redisService: redisService,
		httpClient:   httpClient,
		stopChan:     make(chan struct{}),
	}
}

// Start 启动回调处理服务
func (s *TaxFilingCallbackService) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("callback service is already running")
	}

	// 获取回调配置
	config, err := s.getCallbackConfig(ctx)
	if err != nil {
		s.logger.Error("Failed to get callback config", zap.Error(err))
		return err
	}

	if !config.Enabled {
		s.logger.Info("Callback service is disabled")
		return nil
	}

	s.isRunning = true

	// 启动回调处理协程
	s.wg.Add(1)
	go s.processCallbackLoop(ctx, config)

	s.logger.Info("Tax filing callback service started",
		zap.Duration("process_interval", config.ProcessInterval),
		zap.Int("batch_size", config.BatchSize),
	)

	return nil
}

// Stop 停止回调处理服务
func (s *TaxFilingCallbackService) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return fmt.Errorf("callback service is not running")
	}

	close(s.stopChan)
	s.wg.Wait()
	s.isRunning = false

	s.logger.Info("Tax filing callback service stopped")
	return nil
}

// IsRunning 检查回调服务是否运行中
func (s *TaxFilingCallbackService) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

// CreateCallback 创建回调记录
func (s *TaxFilingCallbackService) CreateCallback(ctx context.Context, req *model.TaxFilingCallbackCreateRequest) (*model.TaxFilingCallback, error) {
	callback := &model.TaxFilingCallback{
		ID:              model.GenerateID(),
		SubmissionID:    req.SubmissionID,
		CallbackType:    req.CallbackType,
		CallbackURL:     req.CallbackURL,
		CallbackMethod:  req.CallbackMethod,
		CallbackHeaders: req.CallbackHeaders,
		CallbackPayload: req.CallbackPayload,
		Status:          model.TaxFilingCallbackStatusPending,
		MaxRetries:      req.MaxRetries,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	if callback.CallbackMethod == "" {
		callback.CallbackMethod = "POST"
	}
	if callback.MaxRetries == 0 {
		callback.MaxRetries = 3
	}

	// 保存回调记录
	if err := s.db.Create(callback).Error; err != nil {
		s.logger.Error("Failed to create callback", zap.Error(err))
		return nil, fmt.Errorf("failed to create callback: %w", err)
	}

	s.logger.Info("Callback created successfully",
		zap.String("callback_id", callback.ID),
		zap.String("submission_id", req.SubmissionID),
		zap.String("type", string(req.CallbackType)),
	)

	return callback, nil
}

// processCallbackLoop 回调处理循环
func (s *TaxFilingCallbackService) processCallbackLoop(ctx context.Context, config *CallbackConfig) {
	defer s.wg.Done()

	ticker := time.NewTicker(config.ProcessInterval)
	defer ticker.Stop()

	s.logger.Info("Callback processing loop started")

	for {
		select {
		case <-s.stopChan:
			s.logger.Info("Callback processing loop stopped")
			return
		case <-ticker.C:
			s.processPendingCallbacks(ctx, config)
		}
	}
}

// processPendingCallbacks 处理待发送的回调
func (s *TaxFilingCallbackService) processPendingCallbacks(ctx context.Context, config *CallbackConfig) {
	// 获取待发送的回调
	var callbacks []model.TaxFilingCallback
	if err := s.db.Where("status = ? OR (status = ? AND next_retry_at <= ?)",
		model.TaxFilingCallbackStatusPending,
		model.TaxFilingCallbackStatusFailed,
		time.Now()).
		Limit(config.BatchSize).
		Find(&callbacks).Error; err != nil {
		s.logger.Error("Failed to get pending callbacks", zap.Error(err))
		return
	}

	if len(callbacks) == 0 {
		return
	}

	s.logger.Debug("Processing pending callbacks", zap.Int("count", len(callbacks)))

	// 并发处理回调
	semaphore := make(chan struct{}, 5) // 限制并发数
	var wg sync.WaitGroup

	for _, callback := range callbacks {
		wg.Add(1)
		go func(cb model.TaxFilingCallback) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			s.processCallback(ctx, &cb, config)
		}(callback)
	}

	wg.Wait()
}

// processCallback 处理单个回调
func (s *TaxFilingCallbackService) processCallback(ctx context.Context, callback *model.TaxFilingCallback, config *CallbackConfig) {
	// 获取分布式锁
	lockKey := fmt.Sprintf("callback_process_%s", callback.ID)
	acquired, err := s.redisService.AcquireLock(ctx, lockKey, 2*time.Minute)
	if err != nil || !acquired {
		return
	}
	defer s.redisService.ReleaseLock(ctx, lockKey)

	s.logger.Debug("Processing callback",
		zap.String("callback_id", callback.ID),
		zap.String("type", string(callback.CallbackType)),
		zap.Int("retry_count", callback.RetryCount),
	)

	// 发送回调
	err = s.sendCallback(ctx, callback, config)
	if err != nil {
		s.handleCallbackFailure(ctx, callback, err)
	} else {
		s.handleCallbackSuccess(ctx, callback)
	}
}

// sendCallback 发送回调
func (s *TaxFilingCallbackService) sendCallback(ctx context.Context, callback *model.TaxFilingCallback, config *CallbackConfig) error {
	if callback.CallbackURL == nil || *callback.CallbackURL == "" {
		return fmt.Errorf("callback URL is empty")
	}

	// 构建请求
	var reqBody []byte
	var err error

	if len(callback.CallbackPayload) > 0 {
		reqBody, err = json.Marshal(callback.CallbackPayload)
		if err != nil {
			return fmt.Errorf("failed to marshal callback payload: %w", err)
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, callback.CallbackMethod, *callback.CallbackURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "TaxFilingService/1.0")
	req.Header.Set("X-Callback-ID", callback.ID)
	req.Header.Set("X-Callback-Type", string(callback.CallbackType))

	// 添加自定义头部
	for key, value := range callback.CallbackHeaders {
		req.Header.Set(key, value)
	}

	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send callback: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody := make([]byte, 1024) // 限制响应大小
	n, _ := resp.Body.Read(respBody)
	responseBody := string(respBody[:n])

	// 检查响应状态
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		// 成功
		callback.MarkAsSent(resp.StatusCode, responseBody)
		return nil
	} else {
		// 失败
		return fmt.Errorf("callback failed with status %d: %s", resp.StatusCode, responseBody)
	}
}

// handleCallbackSuccess 处理回调成功
func (s *TaxFilingCallbackService) handleCallbackSuccess(ctx context.Context, callback *model.TaxFilingCallback) {
	// 更新数据库
	if err := s.db.Model(callback).Updates(map[string]interface{}{
		"status":          callback.Status,
		"response_status": callback.ResponseStatus,
		"response_body":   callback.ResponseBody,
		"last_attempt_at": callback.LastAttemptAt,
		"updated_at":      callback.UpdatedAt,
	}).Error; err != nil {
		s.logger.Error("Failed to update callback success status", zap.Error(err))
	}

	s.logger.Info("Callback sent successfully",
		zap.String("callback_id", callback.ID),
		zap.String("submission_id", callback.SubmissionID),
		zap.Int("response_status", *callback.ResponseStatus),
	)
}

// handleCallbackFailure 处理回调失败
func (s *TaxFilingCallbackService) handleCallbackFailure(ctx context.Context, callback *model.TaxFilingCallback, err error) {
	// 标记为失败
	responseStatus := 0
	responseBody := ""
	callback.MarkAsFailed(err.Error(), &responseStatus, &responseBody)

	// 更新数据库
	if updateErr := s.db.Model(callback).Updates(map[string]interface{}{
		"status":          callback.Status,
		"retry_count":     callback.RetryCount,
		"error_message":   callback.ErrorMessage,
		"response_status": callback.ResponseStatus,
		"response_body":   callback.ResponseBody,
		"next_retry_at":   callback.NextRetryAt,
		"last_attempt_at": callback.LastAttemptAt,
		"updated_at":      callback.UpdatedAt,
	}).Error; updateErr != nil {
		s.logger.Error("Failed to update callback failure status", zap.Error(updateErr))
	}

	s.logger.Error("Callback failed",
		zap.String("callback_id", callback.ID),
		zap.String("submission_id", callback.SubmissionID),
		zap.Error(err),
		zap.Int("retry_count", callback.RetryCount),
		zap.Bool("can_retry", callback.CanRetry()),
	)
}

// RetryCallback 重试回调
func (s *TaxFilingCallbackService) RetryCallback(ctx context.Context, callbackID string) error {
	var callback model.TaxFilingCallback
	if err := s.db.First(&callback, "id = ?", callbackID).Error; err != nil {
		return fmt.Errorf("callback not found: %w", err)
	}

	if !callback.CanRetry() {
		return fmt.Errorf("callback cannot be retried")
	}

	// 重置状态
	callback.Status = model.TaxFilingCallbackStatusPending
	callback.NextRetryAt = nil
	callback.ErrorMessage = nil

	if err := s.db.Save(&callback).Error; err != nil {
		return fmt.Errorf("failed to reset callback status: %w", err)
	}

	s.logger.Info("Callback reset for retry", zap.String("callback_id", callbackID))
	return nil
}

// CancelCallback 取消回调
func (s *TaxFilingCallbackService) CancelCallback(ctx context.Context, callbackID string) error {
	if err := s.db.Model(&model.TaxFilingCallback{}).
		Where("id = ?", callbackID).
		Update("status", model.TaxFilingCallbackStatusCancelled).Error; err != nil {
		return fmt.Errorf("failed to cancel callback: %w", err)
	}

	s.logger.Info("Callback cancelled", zap.String("callback_id", callbackID))
	return nil
}

// getCallbackConfig 获取回调配置
func (s *TaxFilingCallbackService) getCallbackConfig(ctx context.Context) (*CallbackConfig, error) {
	// 先从缓存获取
	var config CallbackConfig
	if err := s.redisService.GetConfig(ctx, "tax_filing_callback", &config); err == nil {
		return &config, nil
	}

	// 返回默认配置
	config = CallbackConfig{
		Enabled:         true,
		ProcessInterval: 30 * time.Second,
		BatchSize:       20,
		MaxRetries:      3,
		Timeout:         30 * time.Second,
		RetryDelay:      time.Minute,
	}

	// 缓存配置
	if err := s.redisService.SetConfig(ctx, "tax_filing_callback", config); err != nil {
		s.logger.Warn("Failed to cache callback config", zap.Error(err))
	}

	return &config, nil
}

// GetCallbackStatistics 获取回调统计信息
func (s *TaxFilingCallbackService) GetCallbackStatistics(ctx context.Context) (*CallbackStatistics, error) {
	stats := &CallbackStatistics{
		IsRunning: s.IsRunning(),
	}

	// 获取各状态的回调数量
	var pendingCount, sentCount, failedCount, cancelledCount int64

	s.db.Model(&model.TaxFilingCallback{}).
		Where("status = ?", model.TaxFilingCallbackStatusPending).
		Count(&pendingCount)

	s.db.Model(&model.TaxFilingCallback{}).
		Where("status = ?", model.TaxFilingCallbackStatusSent).
		Count(&sentCount)

	s.db.Model(&model.TaxFilingCallback{}).
		Where("status = ?", model.TaxFilingCallbackStatusFailed).
		Count(&failedCount)

	s.db.Model(&model.TaxFilingCallback{}).
		Where("status = ?", model.TaxFilingCallbackStatusCancelled).
		Count(&cancelledCount)

	stats.PendingCount = pendingCount
	stats.SentCount = sentCount
	stats.FailedCount = failedCount
	stats.CancelledCount = cancelledCount

	// 计算成功率
	total := sentCount + failedCount
	if total > 0 {
		stats.SuccessRate = float64(sentCount) / float64(total) * 100
	}

	return stats, nil
}

// CallbackStatistics 回调统计信息
type CallbackStatistics struct {
	IsRunning      bool    `json:"is_running"`
	PendingCount   int64   `json:"pending_count"`
	SentCount      int64   `json:"sent_count"`
	FailedCount    int64   `json:"failed_count"`
	CancelledCount int64   `json:"cancelled_count"`
	SuccessRate    float64 `json:"success_rate"`
}
