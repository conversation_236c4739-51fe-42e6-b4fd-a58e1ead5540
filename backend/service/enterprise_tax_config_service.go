// Package service provides business logic for the tax management system.
// It includes services for enterprise tax configuration management with CRUD operations.
package service

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// EnterpriseTaxConfigService defines the interface for enterprise tax config operations
type EnterpriseTaxConfigService interface {
	CreateEnterpriseTaxConfig(ctx context.Context, req CreateEnterpriseTaxConfigRequest) (*model.EnterpriseTaxConfig, error)
	GetEnterpriseTaxConfigByID(ctx context.Context, id string) (*model.EnterpriseTaxConfig, error)
	GetEnterpriseTaxConfigsByEnterpriseID(ctx context.Context, enterpriseID string) ([]model.EnterpriseTaxConfig, error)
	GetActiveEnterpriseTaxConfigs(ctx context.Context, enterpriseID string) ([]model.EnterpriseTaxConfig, error)
	UpdateEnterpriseTaxConfig(ctx context.Context, id string, req UpdateEnterpriseTaxConfigRequest) (*model.EnterpriseTaxConfig, error)
	DeleteEnterpriseTaxConfig(ctx context.Context, id string) error
	ApproveEnterpriseTaxConfig(ctx context.Context, id string, approverID string) error
	RejectEnterpriseTaxConfig(ctx context.Context, id string, approverID string, reason string) error
	DeactivateEnterpriseTaxConfig(ctx context.Context, id string) error
	GetEnterpriseTaxConfigsByTaxType(ctx context.Context, enterpriseID, taxTypeID string) ([]model.EnterpriseTaxConfig, error)
}

type enterpriseTaxConfigService struct {
	db *gorm.DB
}

// NewEnterpriseTaxConfigService creates a new enterprise tax config service instance
func NewEnterpriseTaxConfigService(db *gorm.DB) EnterpriseTaxConfigService {
	return &enterpriseTaxConfigService{db: db}
}

// CreateEnterpriseTaxConfigRequest 创建企业税务配置请求
type CreateEnterpriseTaxConfigRequest struct {
	EnterpriseID       string  `json:"enterprise_id" binding:"required"`
	TaxTypeID          string  `json:"tax_type_id" binding:"required"`
	TaxpayerType       string  `json:"taxpayer_type" binding:"required,oneof=general small"`
	TaxRate            float64 `json:"tax_rate" binding:"required,gte=0,lte=1"`
	CalculationMethod  string  `json:"calculation_method" binding:"required"`
	FilingFrequency    string  `json:"filing_frequency" binding:"required,oneof=monthly quarterly annually"`
	StartDate          string  `json:"start_date" binding:"required"`
	EndDate            *string `json:"end_date"`
	PreferentialPolicy string  `json:"preferential_policy"`
	DeductionItems     string  `json:"deduction_items"`
	ExemptionAmount    float64 `json:"exemption_amount"`
	ThresholdAmount    float64 `json:"threshold_amount"`
	SpecialProvisions  string  `json:"special_provisions"`
	Remarks            string  `json:"remarks"`
}

// UpdateEnterpriseTaxConfigRequest 更新企业税务配置请求
type UpdateEnterpriseTaxConfigRequest struct {
	TaxpayerType       string  `json:"taxpayer_type" binding:"required,oneof=general small"`
	TaxRate            float64 `json:"tax_rate" binding:"required,gte=0,lte=1"`
	CalculationMethod  string  `json:"calculation_method" binding:"required"`
	FilingFrequency    string  `json:"filing_frequency" binding:"required,oneof=monthly quarterly annually"`
	StartDate          string  `json:"start_date" binding:"required"`
	EndDate            *string `json:"end_date"`
	PreferentialPolicy string  `json:"preferential_policy"`
	DeductionItems     string  `json:"deduction_items"`
	ExemptionAmount    float64 `json:"exemption_amount"`
	ThresholdAmount    float64 `json:"threshold_amount"`
	SpecialProvisions  string  `json:"special_provisions"`
	Remarks            string  `json:"remarks"`
}

// CreateEnterpriseTaxConfig 创建企业税务配置
func (s *enterpriseTaxConfigService) CreateEnterpriseTaxConfig(ctx context.Context, req CreateEnterpriseTaxConfigRequest) (*model.EnterpriseTaxConfig, error) {
	// 验证企业是否存在
	var enterprise model.Enterprise
	if err := s.db.WithContext(ctx).First(&enterprise, "id = ?", req.EnterpriseID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrEnterpriseNotFound
		}
		return nil, fmt.Errorf("查询企业失败: %w", err)
	}

	// 验证税种是否存在
	var taxType model.TaxType
	if err := s.db.WithContext(ctx).First(&taxType, "id = ?", req.TaxTypeID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrTaxTypeNotFound
		}
		return nil, fmt.Errorf("查询税种失败: %w", err)
	}

	// 解析日期
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return nil, fmt.Errorf("开始日期格式错误: %w", err)
	}

	var endDate *time.Time
	if req.EndDate != nil {
		parsedEndDate, err := time.Parse("2006-01-02", *req.EndDate)
		if err != nil {
			return nil, fmt.Errorf("结束日期格式错误: %w", err)
		}
		endDate = &parsedEndDate
	}

	// 创建企业税务配置
	config := &model.EnterpriseTaxConfig{
		ID:                 util.GenerateID(),
		EnterpriseID:       req.EnterpriseID,
		TaxTypeID:          req.TaxTypeID,
		TaxpayerType:       req.TaxpayerType,
		TaxRate:            util.DecimalFromFloat(req.TaxRate),
		CalculationMethod:  req.CalculationMethod,
		FilingFrequency:    req.FilingFrequency,
		StartDate:          startDate,
		EndDate:            endDate,
		IsActive:           true,
		PreferentialPolicy: req.PreferentialPolicy,
		DeductionItems:     req.DeductionItems,
		ExemptionAmount:    util.DecimalFromFloat(req.ExemptionAmount),
		ThresholdAmount:    util.DecimalFromFloat(req.ThresholdAmount),
		SpecialProvisions:  req.SpecialProvisions,
		ApprovalStatus:     model.ApprovalStatusPending,
		Remarks:            req.Remarks,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	// 验证数据
	if err := config.Validate(); err != nil {
		return nil, err
	}

	// 保存到数据库
	if err := s.db.WithContext(ctx).Create(config).Error; err != nil {
		return nil, fmt.Errorf("创建企业税务配置失败: %w", err)
	}

	return config, nil
}

// GetEnterpriseTaxConfigByID 根据ID获取企业税务配置
func (s *enterpriseTaxConfigService) GetEnterpriseTaxConfigByID(ctx context.Context, id string) (*model.EnterpriseTaxConfig, error) {
	var config model.EnterpriseTaxConfig
	if err := s.db.WithContext(ctx).
		Preload("Enterprise").
		Preload("TaxType").
		Preload("Approver").
		First(&config, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrEnterpriseTaxConfigNotFound
		}
		return nil, fmt.Errorf("查询企业税务配置失败: %w", err)
	}

	return &config, nil
}

// GetEnterpriseTaxConfigsByEnterpriseID 根据企业ID获取所有税务配置
func (s *enterpriseTaxConfigService) GetEnterpriseTaxConfigsByEnterpriseID(ctx context.Context, enterpriseID string) ([]model.EnterpriseTaxConfig, error) {
	var configs []model.EnterpriseTaxConfig
	if err := s.db.WithContext(ctx).
		Preload("TaxType").
		Where("enterprise_id = ?", enterpriseID).
		Order("created_at DESC").
		Find(&configs).Error; err != nil {
		return nil, fmt.Errorf("查询企业税务配置失败: %w", err)
	}

	return configs, nil
}

// GetActiveEnterpriseTaxConfigs 获取企业的有效税务配置
func (s *enterpriseTaxConfigService) GetActiveEnterpriseTaxConfigs(ctx context.Context, enterpriseID string) ([]model.EnterpriseTaxConfig, error) {
	var configs []model.EnterpriseTaxConfig
	now := time.Now()

	if err := s.db.WithContext(ctx).
		Preload("TaxType").
		Where("enterprise_id = ? AND is_active = ? AND approval_status = ? AND start_date <= ? AND (end_date IS NULL OR end_date >= ?)",
			enterpriseID, true, model.ApprovalStatusApproved, now, now).
		Order("created_at DESC").
		Find(&configs).Error; err != nil {
		return nil, fmt.Errorf("查询有效企业税务配置失败: %w", err)
	}

	return configs, nil
}

// UpdateEnterpriseTaxConfig 更新企业税务配置
func (s *enterpriseTaxConfigService) UpdateEnterpriseTaxConfig(ctx context.Context, id string, req UpdateEnterpriseTaxConfigRequest) (*model.EnterpriseTaxConfig, error) {
	// 查询现有记录
	var config model.EnterpriseTaxConfig
	if err := s.db.WithContext(ctx).First(&config, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, util.ErrEnterpriseTaxConfigNotFound
		}
		return nil, fmt.Errorf("查询企业税务配置失败: %w", err)
	}

	// 解析日期
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return nil, fmt.Errorf("开始日期格式错误: %w", err)
	}

	var endDate *time.Time
	if req.EndDate != nil {
		parsedEndDate, err := time.Parse("2006-01-02", *req.EndDate)
		if err != nil {
			return nil, fmt.Errorf("结束日期格式错误: %w", err)
		}
		endDate = &parsedEndDate
	}

	// 更新字段
	config.TaxpayerType = req.TaxpayerType
	config.TaxRate = util.DecimalFromFloat(req.TaxRate)
	config.CalculationMethod = req.CalculationMethod
	config.FilingFrequency = req.FilingFrequency
	config.StartDate = startDate
	config.EndDate = endDate
	config.PreferentialPolicy = req.PreferentialPolicy
	config.DeductionItems = req.DeductionItems
	config.ExemptionAmount = util.DecimalFromFloat(req.ExemptionAmount)
	config.ThresholdAmount = util.DecimalFromFloat(req.ThresholdAmount)
	config.SpecialProvisions = req.SpecialProvisions
	config.Remarks = req.Remarks
	config.UpdatedAt = time.Now()

	// 重置审批状态
	config.ApprovalStatus = model.ApprovalStatusPending
	config.ApprovedBy = nil
	config.ApprovedAt = nil

	// 验证数据
	if err := config.Validate(); err != nil {
		return nil, err
	}

	// 保存更新
	if err := s.db.WithContext(ctx).Save(&config).Error; err != nil {
		return nil, fmt.Errorf("更新企业税务配置失败: %w", err)
	}

	return &config, nil
}

// DeleteEnterpriseTaxConfig 删除企业税务配置
func (s *enterpriseTaxConfigService) DeleteEnterpriseTaxConfig(ctx context.Context, id string) error {
	result := s.db.WithContext(ctx).Delete(&model.EnterpriseTaxConfig{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("删除企业税务配置失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return util.ErrEnterpriseTaxConfigNotFound
	}
	return nil
}

// ApproveEnterpriseTaxConfig 审批企业税务配置
func (s *enterpriseTaxConfigService) ApproveEnterpriseTaxConfig(ctx context.Context, id string, approverID string) error {
	var config model.EnterpriseTaxConfig
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return util.ErrEnterpriseTaxConfigNotFound
		}
		return fmt.Errorf("查询企业税务配置失败: %w", err)
	}

	// 检查当前状态
	if config.ApprovalStatus != model.ApprovalStatusPending {
		return fmt.Errorf("配置状态不允许审批")
	}

	// 更新审批信息
	now := time.Now()
	config.ApprovalStatus = model.ApprovalStatusApproved
	config.ApprovedBy = &approverID
	config.ApprovedAt = &now
	config.UpdatedAt = now

	if err := s.db.WithContext(ctx).Save(&config).Error; err != nil {
		return fmt.Errorf("审批企业税务配置失败: %w", err)
	}

	return nil
}

// RejectEnterpriseTaxConfig 拒绝企业税务配置
func (s *enterpriseTaxConfigService) RejectEnterpriseTaxConfig(ctx context.Context, id string, approverID string, reason string) error {
	var config model.EnterpriseTaxConfig
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return util.ErrEnterpriseTaxConfigNotFound
		}
		return fmt.Errorf("查询企业税务配置失败: %w", err)
	}

	// 检查当前状态
	if config.ApprovalStatus != model.ApprovalStatusPending {
		return fmt.Errorf("配置状态不允许拒绝")
	}

	// 更新审批信息
	now := time.Now()
	config.ApprovalStatus = model.ApprovalStatusRejected
	config.ApprovedBy = &approverID
	config.ApprovedAt = &now
	config.Remarks = reason
	config.UpdatedAt = now

	if err := s.db.WithContext(ctx).Save(&config).Error; err != nil {
		return fmt.Errorf("拒绝企业税务配置失败: %w", err)
	}

	return nil
}

// DeactivateEnterpriseTaxConfig 停用企业税务配置
func (s *enterpriseTaxConfigService) DeactivateEnterpriseTaxConfig(ctx context.Context, id string) error {
	var config model.EnterpriseTaxConfig
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return util.ErrEnterpriseTaxConfigNotFound
		}
		return fmt.Errorf("查询企业税务配置失败: %w", err)
	}

	// 设置结束日期为当前时间
	now := time.Now()
	config.EndDate = &now
	config.UpdatedAt = now

	if err := s.db.WithContext(ctx).Save(&config).Error; err != nil {
		return fmt.Errorf("停用企业税务配置失败: %w", err)
	}

	return nil
}

// GetEnterpriseTaxConfigsByTaxType 根据税种获取企业税务配置
func (s *enterpriseTaxConfigService) GetEnterpriseTaxConfigsByTaxType(ctx context.Context, enterpriseID, taxTypeID string) ([]model.EnterpriseTaxConfig, error) {
	var configs []model.EnterpriseTaxConfig

	if err := s.db.WithContext(ctx).
		Where("enterprise_id = ? AND tax_type_id = ?", enterpriseID, taxTypeID).
		Order("created_at DESC").
		Find(&configs).Error; err != nil {
		return nil, fmt.Errorf("查询企业税务配置失败: %w", err)
	}

	return configs, nil
}
