package service

import (
	"context"
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"backend/model"
)

// MockFile 模拟文件
type MockFile struct {
	content string
	pos     int64
}

func (m *MockFile) Read(p []byte) (n int, err error) {
	if m.pos >= int64(len(m.content)) {
		return 0, nil
	}
	n = copy(p, m.content[m.pos:])
	m.pos += int64(n)
	return n, nil
}

func (m *MockFile) ReadAt(p []byte, off int64) (n int, err error) {
	if off >= int64(len(m.content)) {
		return 0, nil
	}
	n = copy(p, m.content[off:])
	return n, nil
}

func (m *MockFile) Seek(offset int64, whence int) (int64, error) {
	switch whence {
	case 0:
		m.pos = offset
	case 1:
		m.pos += offset
	case 2:
		m.pos = int64(len(m.content)) + offset
	}
	return m.pos, nil
}

func (m *MockFile) Close() error {
	return nil
}

func setupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// 创建简化的发票表用于测试
	db.Exec(`CREATE TABLE invoices (
		id TEXT PRIMARY KEY,
		enterprise_id TEXT,
		invoice_number TEXT,
		invoice_code TEXT,
		invoice_type TEXT,
		invoice_status TEXT,
		authentication_status TEXT,
		declaration_status TEXT,
		issue_date DATETIME,
		authentication_date DATETIME,
		declaration_period TEXT,
		buyer_name TEXT,
		buyer_tax_number TEXT,
		buyer_address TEXT,
		buyer_bank_account TEXT,
		seller_name TEXT,
		seller_tax_number TEXT,
		seller_address TEXT,
		seller_bank_account TEXT,
		total_amount DECIMAL(15,2),
		total_tax DECIMAL(15,2),
		total_amount_with_tax DECIMAL(15,2),
		remarks TEXT,
		drawer TEXT,
		reviewer TEXT,
		payee TEXT,
		machine_number TEXT,
		original_invoice_id TEXT,
		red_flush_reason TEXT,
		verification_code TEXT,
		qr_code TEXT,
		print_times INTEGER,
		last_print_time DATETIME,
		submit_time DATETIME,
		approval_time DATETIME,
		cancel_time DATETIME,
		cancel_reason TEXT,
		invoice_source TEXT,
		currency_code TEXT,
		exchange_rate DECIMAL(10,4),
		created_at DATETIME,
		updated_at DATETIME,
		deleted_at DATETIME
	)`)

	return db
}

func TestInvoiceService_ScanInvoice(t *testing.T) {
	db := setupTestDB()
	mockOCRService := &MockOCRService{}
	invoiceService := &invoiceService{
		db:         db,
		ocrService: mockOCRService,
	}

	ctx := context.Background()
	file := &MockFile{content: "test invoice image"}
	filename := "invoice.jpg"
	enterpriseID := "test-enterprise-123"

	scanReq := ScanInvoiceRequest{
		EnterpriseID: enterpriseID,
		Settings: OCRSettings{
			InvoiceType:  "vat",
			Accuracy:     "accurate",
			AutoCorrect:  true,
			ExtractItems: false,
		},
	}

	// 模拟OCR识别结果
	ocrResult := &OCRResult{
		InvoiceNumber:    "12345678",
		InvoiceCode:      "*********",
		InvoiceType:      "增值税专用发票",
		IssueDate:        time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
		SellerName:       "测试销售方有限公司",
		SellerTaxNumber:  "91110000*********X",
		BuyerName:        "测试购买方有限公司",
		BuyerTaxNumber:   "91110000*********Y",
		TotalAmount:      decimal.NewFromFloat(1000.00),
		TaxAmount:        decimal.NewFromFloat(130.00),
		TotalWithTax:     decimal.NewFromFloat(1130.00),
		VerificationCode: "12345",
		Confidence: map[string]float64{
			"invoiceNumber": 0.95,
			"sellerName":    0.92,
			"totalAmount":   0.98,
		},
	}

	mockOCRService.On("RecognizeMixedInvoices", ctx, file, filename, scanReq.Settings).Return(ocrResult, nil)

	// 执行扫描
	result, err := invoiceService.ScanInvoice(ctx, file, filename, scanReq)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.True(t, result.Success)
	assert.Equal(t, "发票识别成功", result.Message)
	assert.NotEmpty(t, result.InvoiceID)
	assert.Equal(t, ocrResult, result.OCRResult)

	// 验证数据库中的发票记录
	var invoice model.Invoice
	err = db.Where("id = ?", result.InvoiceID).First(&invoice).Error
	assert.NoError(t, err)
	assert.Equal(t, enterpriseID, invoice.EnterpriseID)
	assert.Equal(t, ocrResult.InvoiceNumber, invoice.InvoiceNumber)
	assert.Equal(t, ocrResult.InvoiceCode, invoice.InvoiceCode)
	assert.Equal(t, ocrResult.SellerName, invoice.SellerName)
	assert.Equal(t, ocrResult.BuyerName, invoice.BuyerName)
	assert.True(t, ocrResult.TotalAmount.Equal(invoice.TotalAmount))
	assert.True(t, ocrResult.TaxAmount.Equal(invoice.TotalTax))
	assert.True(t, ocrResult.TotalWithTax.Equal(invoice.TotalAmountWithTax))
	assert.Equal(t, model.InvoiceSourceScan, invoice.InvoiceSource)
	assert.Equal(t, model.InvoiceStatusDraft, invoice.Status)

	mockOCRService.AssertExpectations(t)
}

func TestInvoiceService_ScanInvoice_OCRError(t *testing.T) {
	db := setupTestDB()
	mockOCRService := &MockOCRService{}
	invoiceService := &invoiceService{
		db:         db,
		ocrService: mockOCRService,
	}

	ctx := context.Background()
	file := &MockFile{content: "invalid image"}
	filename := "invalid.jpg"
	enterpriseID := "test-enterprise-123"

	scanReq := ScanInvoiceRequest{
		EnterpriseID: enterpriseID,
		Settings: OCRSettings{
			InvoiceType: "vat",
			Accuracy:    "fast",
		},
	}

	// 模拟OCR识别失败
	mockOCRService.On("RecognizeMixedInvoices", ctx, file, filename, scanReq.Settings).Return((*OCRResult)(nil), assert.AnError)

	// 执行扫描
	result, err := invoiceService.ScanInvoice(ctx, file, filename, scanReq)

	// 验证结果
	assert.NoError(t, err) // 服务层不应该返回错误，而是在响应中标记失败
	assert.NotNil(t, result)
	assert.False(t, result.Success)
	assert.Contains(t, result.Message, "OCR识别失败")
	assert.Empty(t, result.InvoiceID)
	assert.Nil(t, result.OCRResult)

	mockOCRService.AssertExpectations(t)
}

func TestInvoiceService_createInvoiceFromOCR(t *testing.T) {
	db := setupTestDB()
	invoiceService := &invoiceService{db: db}

	ctx := context.Background()
	enterpriseID := "test-enterprise-456"

	ocrResult := &OCRResult{
		InvoiceNumber:    "87654321",
		InvoiceCode:      "*********",
		InvoiceType:      "增值税普通发票",
		IssueDate:        time.Date(2024, 2, 20, 0, 0, 0, 0, time.UTC),
		SellerName:       "普通发票销售方",
		SellerTaxNumber:  "91220000*********A",
		BuyerName:        "普通发票购买方",
		BuyerTaxNumber:   "91220000*********B",
		TotalAmount:      decimal.NewFromFloat(2000.00),
		TaxAmount:        decimal.NewFromFloat(260.00),
		TotalWithTax:     decimal.NewFromFloat(2260.00),
		VerificationCode: "67890",
	}

	// 执行创建
	invoice, err := invoiceService.createInvoiceFromOCR(ctx, ocrResult, enterpriseID)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, invoice)
	assert.NotEmpty(t, invoice.ID)
	assert.Equal(t, enterpriseID, invoice.EnterpriseID)
	assert.Equal(t, ocrResult.InvoiceNumber, invoice.InvoiceNumber)
	assert.Equal(t, ocrResult.InvoiceCode, invoice.InvoiceCode)
	assert.Equal(t, model.InvoiceTypeOrdinary, invoice.Type) // 映射后的类型
	assert.Equal(t, model.InvoiceStatusDraft, invoice.Status)
	assert.Equal(t, model.AuthStatusPending, invoice.AuthenticationStatus)
	assert.Equal(t, model.DeclStatusUndeclared, invoice.DeclarationStatus)
	assert.Equal(t, model.InvoiceSourceScan, invoice.InvoiceSource)
	assert.Equal(t, "CNY", invoice.CurrencyCode)
	assert.True(t, decimal.NewFromInt(1).Equal(invoice.ExchangeRate))

	// 验证数据库中的记录
	var dbInvoice model.Invoice
	err = db.Where("id = ?", invoice.ID).First(&dbInvoice).Error
	assert.NoError(t, err)
	assert.Equal(t, invoice.ID, dbInvoice.ID)
	assert.Equal(t, invoice.InvoiceNumber, dbInvoice.InvoiceNumber)
}

func TestInvoiceService_mapInvoiceType(t *testing.T) {
	invoiceService := &invoiceService{}

	tests := []struct {
		name     string
		ocrType  string
		expected string
	}{
		{
			name:     "增值税专用发票",
			ocrType:  "增值税专用发票",
			expected: model.InvoiceTypeSpecial,
		},
		{
			name:     "增值税普通发票",
			ocrType:  "增值税普通发票",
			expected: model.InvoiceTypeOrdinary,
		},
		{
			name:     "增值税电子发票",
			ocrType:  "增值税电子发票",
			expected: model.InvoiceTypeElectronic,
		},
		{
			name:     "增值税卷票",
			ocrType:  "增值税卷票",
			expected: model.InvoiceTypeRoll,
		},
		{
			name:     "未知类型",
			ocrType:  "未知发票类型",
			expected: model.InvoiceTypeOrdinary, // 默认为普通发票
		},
		{
			name:     "空类型",
			ocrType:  "",
			expected: model.InvoiceTypeOrdinary, // 默认为普通发票
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := invoiceService.mapInvoiceType(tt.ocrType)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestScanInvoiceRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		request ScanInvoiceRequest
		valid   bool
	}{
		{
			name: "valid request",
			request: ScanInvoiceRequest{
				EnterpriseID: "enterprise-123",
				Settings: OCRSettings{
					InvoiceType: "vat",
					Accuracy:    "accurate",
				},
			},
			valid: true,
		},
		{
			name: "empty enterprise ID",
			request: ScanInvoiceRequest{
				EnterpriseID: "",
				Settings: OCRSettings{
					InvoiceType: "vat",
					Accuracy:    "fast",
				},
			},
			valid: false,
		},
		{
			name: "valid with default settings",
			request: ScanInvoiceRequest{
				EnterpriseID: "enterprise-456",
				Settings:     OCRSettings{}, // 空设置应该使用默认值
			},
			valid: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.request.EnterpriseID == "" {
				assert.False(t, tt.valid)
			} else {
				assert.True(t, tt.valid)
			}
		})
	}
}

func TestScanInvoiceResponse_Structure(t *testing.T) {
	response := &ScanInvoiceResponse{
		InvoiceID: "invoice-123",
		OCRResult: &OCRResult{
			InvoiceNumber: "12345678",
			TotalAmount:   decimal.NewFromFloat(1000.00),
		},
		Success: true,
		Message: "识别成功",
	}

	assert.NotEmpty(t, response.InvoiceID)
	assert.NotNil(t, response.OCRResult)
	assert.True(t, response.Success)
	assert.Equal(t, "识别成功", response.Message)
	assert.Equal(t, "12345678", response.OCRResult.InvoiceNumber)
	assert.True(t, decimal.NewFromFloat(1000.00).Equal(response.OCRResult.TotalAmount))
}
