package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/apache/rocketmq-client-go/v2/primitive"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"backend/mq"
)

// TaxFilingMQService 税务申报消息队列服务
type TaxFilingMQService struct {
	db                    *gorm.DB
	logger                *zap.Logger
	mqClient              *mq.RocketMQClient
	taxFilingService      *TaxFilingService
	taxFilingBatchService *TaxFilingBatchService
	notificationService   *TaxFilingNotificationService
}

// 消息主题定义
const (
	TopicTaxFilingSubmission   = "tax_filing_submission"
	TopicTaxFilingBatch        = "tax_filing_batch"
	TopicTaxFilingSync         = "tax_filing_sync"
	TopicTaxFilingCallback     = "tax_filing_callback"
	TopicTaxFilingNotification = "tax_filing_notification"
	TopicTaxFilingDeadLetter   = "tax_filing_dead_letter"
)

// 消息标签定义
const (
	TagSubmissionCreate    = "submission_create"
	TagSubmissionSubmit    = "submission_submit"
	TagSubmissionRetry     = "submission_retry"
	TagSubmissionCancel    = "submission_cancel"
	TagBatchProcess        = "batch_process"
	TagBatchComplete       = "batch_complete"
	TagSyncStatus          = "sync_status"
	TagSyncBatch           = "sync_batch"
	TagCallbackSend        = "callback_send"
	TagCallbackRetry       = "callback_retry"
	TagNotificationEmail   = "notification_email"
	TagNotificationSMS     = "notification_sms"
	TagNotificationWebhook = "notification_webhook"
)

// 消息结构定义
type SubmissionMessage struct {
	SubmissionID string                 `json:"submission_id"`
	Action       string                 `json:"action"`
	Data         map[string]interface{} `json:"data"`
	Timestamp    time.Time              `json:"timestamp"`
	RetryCount   int                    `json:"retry_count"`
}

type BatchMessage struct {
	BatchID    string                 `json:"batch_id"`
	Action     string                 `json:"action"`
	Data       map[string]interface{} `json:"data"`
	Timestamp  time.Time              `json:"timestamp"`
	RetryCount int                    `json:"retry_count"`
}

type SyncMessage struct {
	Type       string                 `json:"type"` // submission, batch
	IDs        []string               `json:"ids"`
	Data       map[string]interface{} `json:"data"`
	Timestamp  time.Time              `json:"timestamp"`
	RetryCount int                    `json:"retry_count"`
}

type CallbackMessage struct {
	CallbackID string                 `json:"callback_id"`
	Action     string                 `json:"action"`
	Data       map[string]interface{} `json:"data"`
	Timestamp  time.Time              `json:"timestamp"`
	RetryCount int                    `json:"retry_count"`
}

type NotificationMessage struct {
	Type      string                 `json:"type"`
	Target    string                 `json:"target"`
	Subject   string                 `json:"subject"`
	Content   string                 `json:"content"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
}

// NewTaxFilingMQService 创建税务申报消息队列服务
func NewTaxFilingMQService(
	db *gorm.DB,
	logger *zap.Logger,
	mqClient *mq.RocketMQClient,
	taxFilingService *TaxFilingService,
	taxFilingBatchService *TaxFilingBatchService,
	notificationService *TaxFilingNotificationService,
) *TaxFilingMQService {
	return &TaxFilingMQService{
		db:                    db,
		logger:                logger,
		mqClient:              mqClient,
		taxFilingService:      taxFilingService,
		taxFilingBatchService: taxFilingBatchService,
		notificationService:   notificationService,
	}
}

// Start 启动消息队列服务
func (s *TaxFilingMQService) Start(ctx context.Context) error {
	// 启动MQ客户端
	if err := s.mqClient.Start(); err != nil {
		return fmt.Errorf("failed to start MQ client: %w", err)
	}

	// 订阅消息主题
	if err := s.subscribeTopics(); err != nil {
		return fmt.Errorf("failed to subscribe topics: %w", err)
	}

	s.logger.Info("Tax filing MQ service started successfully")
	return nil
}

// Stop 停止消息队列服务
func (s *TaxFilingMQService) Stop() error {
	if err := s.mqClient.Stop(); err != nil {
		return fmt.Errorf("failed to stop MQ client: %w", err)
	}

	s.logger.Info("Tax filing MQ service stopped successfully")
	return nil
}

// subscribeTopics 订阅消息主题
func (s *TaxFilingMQService) subscribeTopics() error {
	// 订阅申报消息
	if err := s.mqClient.Subscribe(TopicTaxFilingSubmission, "*", s.handleSubmissionMessage); err != nil {
		return fmt.Errorf("failed to subscribe submission topic: %w", err)
	}

	// 订阅批次消息
	if err := s.mqClient.Subscribe(TopicTaxFilingBatch, "*", s.handleBatchMessage); err != nil {
		return fmt.Errorf("failed to subscribe batch topic: %w", err)
	}

	// 订阅同步消息
	if err := s.mqClient.Subscribe(TopicTaxFilingSync, "*", s.handleSyncMessage); err != nil {
		return fmt.Errorf("failed to subscribe sync topic: %w", err)
	}

	// 订阅回调消息
	if err := s.mqClient.Subscribe(TopicTaxFilingCallback, "*", s.handleCallbackMessage); err != nil {
		return fmt.Errorf("failed to subscribe callback topic: %w", err)
	}

	// 订阅通知消息
	if err := s.mqClient.Subscribe(TopicTaxFilingNotification, "*", s.handleNotificationMessage); err != nil {
		return fmt.Errorf("failed to subscribe notification topic: %w", err)
	}

	return nil
}

// SendSubmissionMessage 发送申报消息
func (s *TaxFilingMQService) SendSubmissionMessage(ctx context.Context, submissionID, action string, data map[string]interface{}) error {
	message := &SubmissionMessage{
		SubmissionID: submissionID,
		Action:       action,
		Data:         data,
		Timestamp:    time.Now(),
		RetryCount:   0,
	}

	msg, err := mq.CreateMessage(TopicTaxFilingSubmission, getTagByAction(action), submissionID, message)
	if err != nil {
		return fmt.Errorf("failed to create submission message: %w", err)
	}

	// 设置消息属性
	msg.Properties["submission_id"] = submissionID
	msg.Properties["action"] = action

	_, err = s.mqClient.SendMessage(ctx, msg)
	if err != nil {
		s.logger.Error("Failed to send submission message",
			zap.String("submission_id", submissionID),
			zap.String("action", action),
			zap.Error(err))
		return err
	}

	s.logger.Debug("Submission message sent successfully",
		zap.String("submission_id", submissionID),
		zap.String("action", action))

	return nil
}

// SendBatchMessage 发送批次消息
func (s *TaxFilingMQService) SendBatchMessage(ctx context.Context, batchID, action string, data map[string]interface{}) error {
	message := &BatchMessage{
		BatchID:    batchID,
		Action:     action,
		Data:       data,
		Timestamp:  time.Now(),
		RetryCount: 0,
	}

	msg, err := mq.CreateMessage(TopicTaxFilingBatch, getTagByAction(action), batchID, message)
	if err != nil {
		return fmt.Errorf("failed to create batch message: %w", err)
	}

	// 设置消息属性
	msg.Properties["batch_id"] = batchID
	msg.Properties["action"] = action

	_, err = s.mqClient.SendMessage(ctx, msg)
	if err != nil {
		s.logger.Error("Failed to send batch message",
			zap.String("batch_id", batchID),
			zap.String("action", action),
			zap.Error(err))
		return err
	}

	s.logger.Debug("Batch message sent successfully",
		zap.String("batch_id", batchID),
		zap.String("action", action))

	return nil
}

// SendSyncMessage 发送同步消息
func (s *TaxFilingMQService) SendSyncMessage(ctx context.Context, syncType string, ids []string, data map[string]interface{}) error {
	message := &SyncMessage{
		Type:       syncType,
		IDs:        ids,
		Data:       data,
		Timestamp:  time.Now(),
		RetryCount: 0,
	}

	key := fmt.Sprintf("%s_%d", syncType, time.Now().UnixNano())
	msg, err := mq.CreateMessage(TopicTaxFilingSync, TagSyncStatus, key, message)
	if err != nil {
		return fmt.Errorf("failed to create sync message: %w", err)
	}

	// 设置消息属性
	msg.Properties["sync_type"] = syncType
	msg.Properties["count"] = fmt.Sprintf("%d", len(ids))

	_, err = s.mqClient.SendMessage(ctx, msg)
	if err != nil {
		s.logger.Error("Failed to send sync message",
			zap.String("sync_type", syncType),
			zap.Int("count", len(ids)),
			zap.Error(err))
		return err
	}

	s.logger.Debug("Sync message sent successfully",
		zap.String("sync_type", syncType),
		zap.Int("count", len(ids)))

	return nil
}

// SendCallbackMessage 发送回调消息
func (s *TaxFilingMQService) SendCallbackMessage(ctx context.Context, callbackID, action string, data map[string]interface{}) error {
	message := &CallbackMessage{
		CallbackID: callbackID,
		Action:     action,
		Data:       data,
		Timestamp:  time.Now(),
		RetryCount: 0,
	}

	msg, err := mq.CreateMessage(TopicTaxFilingCallback, getTagByAction(action), callbackID, message)
	if err != nil {
		return fmt.Errorf("failed to create callback message: %w", err)
	}

	// 设置消息属性
	msg.Properties["callback_id"] = callbackID
	msg.Properties["action"] = action

	_, err = s.mqClient.SendMessage(ctx, msg)
	if err != nil {
		s.logger.Error("Failed to send callback message",
			zap.String("callback_id", callbackID),
			zap.String("action", action),
			zap.Error(err))
		return err
	}

	s.logger.Debug("Callback message sent successfully",
		zap.String("callback_id", callbackID),
		zap.String("action", action))

	return nil
}

// SendNotificationMessage 发送通知消息
func (s *TaxFilingMQService) SendNotificationMessage(ctx context.Context, notificationType, target, subject, content string, data map[string]interface{}) error {
	message := &NotificationMessage{
		Type:      notificationType,
		Target:    target,
		Subject:   subject,
		Content:   content,
		Data:      data,
		Timestamp: time.Now(),
	}

	key := fmt.Sprintf("%s_%d", notificationType, time.Now().UnixNano())
	tag := getNotificationTag(notificationType)
	msg, err := mq.CreateMessage(TopicTaxFilingNotification, tag, key, message)
	if err != nil {
		return fmt.Errorf("failed to create notification message: %w", err)
	}

	// 设置消息属性
	msg.Properties["notification_type"] = notificationType
	msg.Properties["target"] = target

	_, err = s.mqClient.SendMessage(ctx, msg)
	if err != nil {
		s.logger.Error("Failed to send notification message",
			zap.String("type", notificationType),
			zap.String("target", target),
			zap.Error(err))
		return err
	}

	s.logger.Debug("Notification message sent successfully",
		zap.String("type", notificationType),
		zap.String("target", target))

	return nil
}

// handleSubmissionMessage 处理申报消息
func (s *TaxFilingMQService) handleSubmissionMessage(ctx context.Context, msg *primitive.MessageExt) error {
	var submissionMsg SubmissionMessage
	if err := json.Unmarshal(msg.Body, &submissionMsg); err != nil {
		s.logger.Error("Failed to unmarshal submission message", zap.Error(err))
		return err
	}

	s.logger.Debug("Processing submission message",
		zap.String("submission_id", submissionMsg.SubmissionID),
		zap.String("action", submissionMsg.Action))

	switch submissionMsg.Action {
	case "submit":
		return s.taxFilingService.SubmitToTaxBureau(ctx, submissionMsg.SubmissionID)
	case "retry":
		return s.taxFilingService.RetrySubmission(ctx, submissionMsg.SubmissionID)
	case "cancel":
		return s.taxFilingService.CancelSubmission(ctx, submissionMsg.SubmissionID)
	case "sync":
		return s.taxFilingService.SyncSubmissionStatus(ctx, submissionMsg.SubmissionID)
	default:
		s.logger.Warn("Unknown submission action", zap.String("action", submissionMsg.Action))
		return fmt.Errorf("unknown submission action: %s", submissionMsg.Action)
	}
}

// handleBatchMessage 处理批次消息
func (s *TaxFilingMQService) handleBatchMessage(ctx context.Context, msg *primitive.MessageExt) error {
	var batchMsg BatchMessage
	if err := json.Unmarshal(msg.Body, &batchMsg); err != nil {
		s.logger.Error("Failed to unmarshal batch message", zap.Error(err))
		return err
	}

	s.logger.Debug("Processing batch message",
		zap.String("batch_id", batchMsg.BatchID),
		zap.String("action", batchMsg.Action))

	switch batchMsg.Action {
	case "process":
		return s.taxFilingBatchService.ProcessBatch(ctx, batchMsg.BatchID)
	default:
		s.logger.Warn("Unknown batch action", zap.String("action", batchMsg.Action))
		return fmt.Errorf("unknown batch action: %s", batchMsg.Action)
	}
}

// handleSyncMessage 处理同步消息
func (s *TaxFilingMQService) handleSyncMessage(ctx context.Context, msg *primitive.MessageExt) error {
	var syncMsg SyncMessage
	if err := json.Unmarshal(msg.Body, &syncMsg); err != nil {
		s.logger.Error("Failed to unmarshal sync message", zap.Error(err))
		return err
	}

	s.logger.Debug("Processing sync message",
		zap.String("type", syncMsg.Type),
		zap.Int("count", len(syncMsg.IDs)))

	switch syncMsg.Type {
	case "submission":
		for _, id := range syncMsg.IDs {
			if err := s.taxFilingService.SyncSubmissionStatus(ctx, id); err != nil {
				s.logger.Error("Failed to sync submission status",
					zap.String("submission_id", id),
					zap.Error(err))
			}
		}
	case "batch":
		// 批次同步逻辑
		for _, id := range syncMsg.IDs {
			// 这里可以添加批次同步逻辑
			s.logger.Debug("Syncing batch", zap.String("batch_id", id))
		}
	default:
		s.logger.Warn("Unknown sync type", zap.String("type", syncMsg.Type))
		return fmt.Errorf("unknown sync type: %s", syncMsg.Type)
	}

	return nil
}

// handleCallbackMessage 处理回调消息
func (s *TaxFilingMQService) handleCallbackMessage(ctx context.Context, msg *primitive.MessageExt) error {
	var callbackMsg CallbackMessage
	if err := json.Unmarshal(msg.Body, &callbackMsg); err != nil {
		s.logger.Error("Failed to unmarshal callback message", zap.Error(err))
		return err
	}

	s.logger.Debug("Processing callback message",
		zap.String("callback_id", callbackMsg.CallbackID),
		zap.String("action", callbackMsg.Action))

	// 这里应该调用回调服务处理回调
	// return s.callbackService.ProcessCallback(ctx, callbackMsg.CallbackID)

	return nil
}

// handleNotificationMessage 处理通知消息
func (s *TaxFilingMQService) handleNotificationMessage(ctx context.Context, msg *primitive.MessageExt) error {
	var notificationMsg NotificationMessage
	if err := json.Unmarshal(msg.Body, &notificationMsg); err != nil {
		s.logger.Error("Failed to unmarshal notification message", zap.Error(err))
		return err
	}

	s.logger.Debug("Processing notification message",
		zap.String("type", notificationMsg.Type),
		zap.String("target", notificationMsg.Target))

	switch notificationMsg.Type {
	case "email":
		// 发送邮件通知 - 这里需要实现具体的邮件发送逻辑
		s.logger.Info("Email notification requested",
			zap.String("target", notificationMsg.Target),
			zap.String("subject", notificationMsg.Subject))
		return nil
	case "sms":
		// 发送短信通知 - 这里需要实现具体的短信发送逻辑
		s.logger.Info("SMS notification requested",
			zap.String("target", notificationMsg.Target),
			zap.String("content", notificationMsg.Content))
		return nil
	case "webhook":
		// 发送Webhook通知 - 这里需要实现具体的Webhook发送逻辑
		s.logger.Info("Webhook notification requested",
			zap.String("target", notificationMsg.Target))
		return nil
	default:
		s.logger.Warn("Unknown notification type", zap.String("type", notificationMsg.Type))
		return fmt.Errorf("unknown notification type: %s", notificationMsg.Type)
	}
}

// 辅助函数
func getTagByAction(action string) string {
	switch action {
	case "create":
		return TagSubmissionCreate
	case "submit":
		return TagSubmissionSubmit
	case "retry":
		return TagSubmissionRetry
	case "cancel":
		return TagSubmissionCancel
	case "process":
		return TagBatchProcess
	case "complete":
		return TagBatchComplete
	default:
		return "*"
	}
}

func getNotificationTag(notificationType string) string {
	switch notificationType {
	case "email":
		return TagNotificationEmail
	case "sms":
		return TagNotificationSMS
	case "webhook":
		return TagNotificationWebhook
	default:
		return "*"
	}
}

// IsRunning 检查服务是否运行中
func (s *TaxFilingMQService) IsRunning() bool {
	return s.mqClient.IsRunning()
}

// GetStatus 获取服务状态
func (s *TaxFilingMQService) GetStatus() map[string]interface{} {
	return map[string]interface{}{
		"is_running": s.IsRunning(),
		"producer":   s.mqClient.GetProducerStatus(),
		"consumer":   s.mqClient.GetConsumerStatus(),
	}
}
