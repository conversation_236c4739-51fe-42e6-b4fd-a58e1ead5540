// Package service provides business logic layer for the tax management system.
// It implements email service operations for sending emails using Aliyun Direct Mail API.
package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	"backend/config"
	"backend/model"
)

// EmailService defines the interface for email operations
type EmailService interface {
	// SendEmail sends an email with the given parameters
	SendEmail(ctx context.Context, to string, subject string, body string, isHTML bool) error

	// SendPasswordResetEmail sends a password reset email with a reset link
	SendPasswordResetEmail(ctx context.Context, to string, resetToken string, username string) error

	// SendWelcomeEmail sends a welcome email to a new user
	SendWelcomeEmail(ctx context.Context, to string, username string) error

	// SendNotificationEmail sends a notification email
	SendNotificationEmail(ctx context.Context, to string, notification model.Notification) error
}

// emailService implements the EmailService interface
type emailService struct {
	cfg config.Config
}

// NewEmailService creates a new email service instance
func NewEmailService(cfg config.Config) EmailService {
	return &emailService{
		cfg: cfg,
	}
}

// SendEmail sends an email using Aliyun Direct Mail API
func (s *emailService) SendEmail(ctx context.Context, to string, subject string, body string, isHTML bool) error {
	if s.cfg.Email.Provider == "aliyun" {
		return s.sendAliyunEmail(ctx, to, subject, body, isHTML)
	}

	// Fallback to SMTP if configured
	if s.cfg.Email.SMTPHost != "" {
		return s.sendSMTPEmail(ctx, to, subject, body, isHTML)
	}

	return fmt.Errorf("no email provider configured")
}

// SendPasswordResetEmail sends a password reset email with a reset link
func (s *emailService) SendPasswordResetEmail(ctx context.Context, to string, resetToken string, username string) error {
	subject := "【税易通】密码重置"

	// 构建重置链接
	resetLink := fmt.Sprintf("%s/reset-password?token=%s", s.getBaseURL(), resetToken)

	// 构建HTML邮件内容
	htmlBody := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>密码重置</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #1890ff; color: white; padding: 10px 20px; text-align: center; }
        .content { padding: 20px; border: 1px solid #ddd; border-top: none; }
        .button { display: inline-block; background-color: #1890ff; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; }
        .footer { margin-top: 20px; font-size: 12px; color: #999; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>密码重置请求</h2>
        </div>
        <div class="content">
            <p>尊敬的 %s：</p>
            <p>我们收到了您的密码重置请求。请点击下面的链接重置您的密码：</p>
            <p style="text-align: center;">
                <a href="%s" class="button">重置密码</a>
            </p>
            <p>或者复制以下链接到浏览器地址栏：</p>
            <p>%s</p>
            <p>此链接将在24小时后失效。如果您没有请求重置密码，请忽略此邮件。</p>
            <p>谢谢！</p>
            <p>税易通团队</p>
        </div>
        <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。如有问题，请联系客服。</p>
        </div>
    </div>
</body>
</html>
`, username, resetLink, resetLink)

	// 发送邮件
	return s.SendEmail(ctx, to, subject, htmlBody, true)
}

// SendWelcomeEmail sends a welcome email to a new user
func (s *emailService) SendWelcomeEmail(ctx context.Context, to string, username string) error {
	subject := "【税易通】欢迎加入"

	// 构建HTML邮件内容
	htmlBody := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>欢迎加入</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #1890ff; color: white; padding: 10px 20px; text-align: center; }
        .content { padding: 20px; border: 1px solid #ddd; border-top: none; }
        .button { display: inline-block; background-color: #1890ff; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; }
        .footer { margin-top: 20px; font-size: 12px; color: #999; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>欢迎加入税易通</h2>
        </div>
        <div class="content">
            <p>尊敬的 %s：</p>
            <p>欢迎加入税易通！您的账户已经成功创建。</p>
            <p>税易通是一个专业的税务管理系统，帮助您轻松管理企业税务。</p>
            <p>如果您有任何问题，请随时联系我们的客服团队。</p>
            <p>祝您使用愉快！</p>
            <p>税易通团队</p>
        </div>
        <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。如有问题，请联系客服。</p>
        </div>
    </div>
</body>
</html>
`, username)

	// 发送邮件
	return s.SendEmail(ctx, to, subject, htmlBody, true)
}

// SendNotificationEmail sends a notification email
func (s *emailService) SendNotificationEmail(ctx context.Context, to string, notification model.Notification) error {
	subject := fmt.Sprintf("【税易通】%s", notification.Title)

	// 构建HTML邮件内容
	htmlBody := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>%s</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #1890ff; color: white; padding: 10px 20px; text-align: center; }
        .content { padding: 20px; border: 1px solid #ddd; border-top: none; }
        .footer { margin-top: 20px; font-size: 12px; color: #999; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>%s</h2>
        </div>
        <div class="content">
            <p>%s</p>
        </div>
        <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。如有问题，请联系客服。</p>
        </div>
    </div>
</body>
</html>
`, notification.Title, notification.Title, notification.Content)

	// 发送邮件
	return s.SendEmail(ctx, to, subject, htmlBody, true)
}

// getBaseURL returns the base URL for the application
func (s *emailService) getBaseURL() string {
	// 根据环境返回不同的基础URL
	env := s.getEnvironment()

	switch env {
	case "prod", "production":
		return "https://your-domain.com"
	case "test", "testing":
		return "https://test.your-domain.com"
	default:
		return "http://localhost:8080"
	}
}

// getEnvironment returns the current environment
func (s *emailService) getEnvironment() string {
	env := s.cfg.App.Name
	if strings.Contains(strings.ToLower(env), "prod") {
		return "prod"
	} else if strings.Contains(strings.ToLower(env), "test") {
		return "test"
	}
	return "dev"
}

// sendAliyunEmail sends email using Aliyun Direct Mail API
func (s *emailService) sendAliyunEmail(ctx context.Context, to string, subject string, body string, isHTML bool) error {
	// 构建请求参数
	params := map[string]string{
		"Action":           "SingleSendMail",
		"Version":          "2015-11-23",
		"RegionId":         s.cfg.Email.RegionID,
		"Format":           "JSON",
		"Timestamp":        time.Now().UTC().Format("2006-01-02T15:04:05Z"),
		"SignatureMethod":  "HMAC-SHA1",
		"SignatureVersion": "1.0",
		"SignatureNonce":   s.generateNonce(),
		"AccessKeyId":      s.cfg.Email.AccessKeyID,
		"AccountName":      s.cfg.Email.FromAddress,
		"FromAlias":        s.cfg.Email.FromName,
		"ToAddress":        to,
		"Subject":          subject,
	}

	// 设置邮件内容
	if isHTML {
		params["HtmlBody"] = body
	} else {
		params["TextBody"] = body
	}

	// 设置回复地址
	if s.cfg.Email.ReplyToAddress != "" {
		params["ReplyToAddress"] = s.cfg.Email.ReplyToAddress
	}

	// 生成签名
	signature := s.generateSignature("POST", params)
	params["Signature"] = signature

	// 构建请求URL
	endpoint := fmt.Sprintf("https://%s", s.cfg.Email.Endpoint)

	// 构建请求体
	formData := url.Values{}
	for key, value := range params {
		formData.Set(key, value)
	}

	// 发送HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", endpoint, strings.NewReader(formData.Encode()))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(respBody, &result); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查是否成功
	if resp.StatusCode != http.StatusOK {
		errorCode, _ := result["Code"].(string)
		errorMessage, _ := result["Message"].(string)
		return fmt.Errorf("阿里云邮件发送失败: %s - %s", errorCode, errorMessage)
	}

	return nil
}

// generateNonce generates a random nonce for API request
func (s *emailService) generateNonce() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 16)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// generateSignature generates the signature for Aliyun API request
func (s *emailService) generateSignature(method string, params map[string]string) string {
	// 1. 对参数进行排序
	var keys []string
	for k := range params {
		if k != "Signature" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 2. 构建规范化查询字符串
	var queryParts []string
	for _, k := range keys {
		queryParts = append(queryParts, url.QueryEscape(k)+"="+url.QueryEscape(params[k]))
	}
	canonicalizedQueryString := strings.Join(queryParts, "&")

	// 3. 构建待签名字符串
	stringToSign := method + "&" + url.QueryEscape("/") + "&" + url.QueryEscape(canonicalizedQueryString)

	// 4. 计算签名
	key := s.cfg.Email.AccessKeySecret + "&"
	mac := hmac.New(sha1.New, []byte(key))
	mac.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	return signature
}

// sendSMTPEmail sends email using SMTP (fallback method)
func (s *emailService) sendSMTPEmail(ctx context.Context, to string, subject string, body string, isHTML bool) error {
	// TODO: 实现SMTP发送邮件的逻辑
	// 这里可以使用标准的SMTP库，如 gomail 或 net/smtp
	return fmt.Errorf("SMTP邮件发送功能尚未实现")
}
