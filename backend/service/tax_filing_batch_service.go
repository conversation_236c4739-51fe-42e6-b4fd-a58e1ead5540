package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"backend/client"
	"backend/model"
)

// TaxFilingBatchService 税务申报批次服务
type TaxFilingBatchService struct {
	db                  *gorm.DB
	logger              *zap.Logger
	taxFilingService    *TaxFilingService
	taxFilingClient     *client.TaxFilingClient
	notificationService *TaxFilingNotificationService
	redisService        *RedisService
}

// NewTaxFilingBatchService 创建批次服务
func NewTaxFilingBatchService(
	db *gorm.DB,
	logger *zap.Logger,
	taxFilingService *TaxFilingService,
	taxFilingClient *client.TaxFilingClient,
	notificationService *TaxFilingNotificationService,
	redisService *RedisService,
) *TaxFilingBatchService {
	return &TaxFilingBatchService{
		db:                  db,
		logger:              logger,
		taxFilingService:    taxFilingService,
		taxFilingClient:     taxFilingClient,
		notificationService: notificationService,
		redisService:        redisService,
	}
}

// CreateBatch 创建批次
func (s *TaxFilingBatchService) CreateBatch(ctx context.Context, req *model.TaxFilingBatchCreateRequest) (*model.TaxFilingBatch, error) {
	// 验证企业是否存在
	var enterprise model.Enterprise
	if err := s.db.First(&enterprise, "id = ?", req.EnterpriseID).Error; err != nil {
		s.logger.Error("Enterprise not found", zap.Error(err), zap.String("enterprise_id", req.EnterpriseID))
		return nil, fmt.Errorf("enterprise not found: %w", err)
	}

	// 验证省份配置是否存在且激活
	var province model.TaxFilingProvince
	if err := s.db.First(&province, "code = ? AND status = ?", req.ProvinceCode, model.TaxFilingProvinceStatusActive).Error; err != nil {
		s.logger.Error("Province not found or inactive", zap.Error(err), zap.String("province_code", req.ProvinceCode))
		return nil, fmt.Errorf("province not found or inactive: %w", err)
	}

	// 创建批次记录
	batch := &model.TaxFilingBatch{
		ID:           model.GenerateBatchID(),
		Name:         req.Name,
		Description:  req.Description,
		ProvinceCode: req.ProvinceCode,
		EnterpriseID: req.EnterpriseID,
		Status:       model.TaxFilingBatchStatusPending,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// 保存批次记录
	if err := s.db.Create(batch).Error; err != nil {
		s.logger.Error("Failed to create batch", zap.Error(err))
		return nil, fmt.Errorf("failed to create batch: %w", err)
	}

	s.logger.Info("Tax filing batch created successfully",
		zap.String("batch_id", batch.ID),
		zap.String("name", batch.Name),
		zap.String("enterprise_id", req.EnterpriseID),
		zap.String("province_code", req.ProvinceCode),
	)

	return batch, nil
}

// AddSubmissionsToBatch 添加申报到批次
func (s *TaxFilingBatchService) AddSubmissionsToBatch(ctx context.Context, batchID string, submissionRequests []model.TaxFilingSubmissionCreateRequest) ([]string, []error) {
	// 获取批次信息
	batch, err := s.GetBatchByID(ctx, batchID)
	if err != nil {
		return nil, []error{fmt.Errorf("failed to get batch: %w", err)}
	}

	// 检查批次状态
	if batch.Status != model.TaxFilingBatchStatusPending {
		return nil, []error{fmt.Errorf("batch is not in pending status: %s", batch.Status)}
	}

	var submissionIDs []string
	var errors []error
	var successCount int

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建申报记录
	for i, req := range submissionRequests {
		// 设置批次ID
		req.BatchID = &batchID
		req.SubmissionType = model.TaxFilingTypeBatch

		// 验证申报数据
		if err := s.validateSubmissionRequest(&req); err != nil {
			errors = append(errors, fmt.Errorf("submission %d validation failed: %w", i, err))
			continue
		}

		// 创建申报记录
		submission := &model.TaxFilingSubmission{
			ID:                  model.GenerateID(),
			EnterpriseID:        req.EnterpriseID,
			ProvinceCode:        req.ProvinceCode,
			ProvinceName:        batch.Province.Name,
			SubmissionType:      req.SubmissionType,
			BatchID:             req.BatchID,
			Priority:            req.Priority,
			CompanyName:         req.CompanyName,
			TaxID:               req.TaxID,
			RegistrationNumber:  req.RegistrationNumber,
			LegalRepresentative: req.LegalRepresentative,
			CompanyAddress:      req.CompanyAddress,
			ContactPhone:        req.ContactPhone,
			ContactEmail:        req.ContactEmail,
			TaxYear:             req.TaxYear,
			TaxMonth:            req.TaxMonth,
			TaxQuarter:          req.TaxQuarter,
			PeriodType:          req.PeriodType,
			TaxData:             req.TaxData,
			Notes:               req.Notes,
			AdditionalData:      req.AdditionalData,
			Status:              model.TaxFilingStatusPending,
			CreatedAt:           time.Now(),
			UpdatedAt:           time.Now(),
		}

		// 计算总金额
		submission.UpdateTotals()

		// 生成参考号
		refNumber := model.GenerateReferenceNumber(req.ProvinceCode)
		submission.ReferenceNumber = &refNumber

		// 保存申报记录
		if err := tx.Create(submission).Error; err != nil {
			errors = append(errors, fmt.Errorf("failed to create submission %d: %w", i, err))
			continue
		}

		submissionIDs = append(submissionIDs, submission.ID)
		successCount++
	}

	// 更新批次统计
	if err := tx.Model(&model.TaxFilingBatch{}).Where("id = ?", batchID).Updates(map[string]interface{}{
		"total_submissions": successCount,
		"updated_at":        time.Now(),
	}).Error; err != nil {
		tx.Rollback()
		return nil, []error{fmt.Errorf("failed to update batch statistics: %w", err)}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, []error{fmt.Errorf("failed to commit transaction: %w", err)}
	}

	s.logger.Info("Submissions added to batch",
		zap.String("batch_id", batchID),
		zap.Int("total_requests", len(submissionRequests)),
		zap.Int("successful", successCount),
		zap.Int("failed", len(errors)),
	)

	return submissionIDs, errors
}

// ProcessBatch 处理批次
func (s *TaxFilingBatchService) ProcessBatch(ctx context.Context, batchID string) error {
	// 获取批次信息
	batch, err := s.GetBatchByID(ctx, batchID)
	if err != nil {
		return fmt.Errorf("failed to get batch: %w", err)
	}

	// 检查批次状态
	if batch.Status != model.TaxFilingBatchStatusPending {
		return fmt.Errorf("batch is not in pending status: %s", batch.Status)
	}

	// 更新批次状态为处理中
	if err := s.updateBatchStatus(ctx, batchID, model.TaxFilingBatchStatusProcessing); err != nil {
		return fmt.Errorf("failed to update batch status: %w", err)
	}

	// 获取批次中的所有申报
	var submissions []model.TaxFilingSubmission
	if err := s.db.Where("batch_id = ? AND status = ?", batchID, model.TaxFilingStatusPending).Find(&submissions).Error; err != nil {
		return fmt.Errorf("failed to get batch submissions: %w", err)
	}

	if len(submissions) == 0 {
		// 没有待处理的申报，直接完成
		return s.updateBatchStatus(ctx, batchID, model.TaxFilingBatchStatusCompleted)
	}

	// 异步处理批次
	go s.processBatchAsync(context.Background(), batchID, submissions)

	return nil
}

// processBatchAsync 异步处理批次
func (s *TaxFilingBatchService) processBatchAsync(ctx context.Context, batchID string, submissions []model.TaxFilingSubmission) {
	s.logger.Info("Starting batch processing",
		zap.String("batch_id", batchID),
		zap.Int("submission_count", len(submissions)),
	)

	// 使用工作池并发处理
	const maxWorkers = 5
	submissionChan := make(chan model.TaxFilingSubmission, len(submissions))
	resultChan := make(chan BatchProcessResult, len(submissions))

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < maxWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			s.batchWorker(ctx, submissionChan, resultChan)
		}()
	}

	// 发送任务
	for _, submission := range submissions {
		submissionChan <- submission
	}
	close(submissionChan)

	// 等待所有工作完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	var successCount, failedCount int
	for result := range resultChan {
		if result.Success {
			successCount++
		} else {
			failedCount++
			s.logger.Error("Batch submission failed",
				zap.String("submission_id", result.SubmissionID),
				zap.Error(result.Error),
			)
		}

		// 更新批次进度
		s.updateBatchProgress(ctx, batchID, successCount, failedCount)
	}

	// 完成批次处理
	s.completeBatchProcessing(ctx, batchID, successCount, failedCount)
}

// batchWorker 批次工作协程
func (s *TaxFilingBatchService) batchWorker(ctx context.Context, submissionChan <-chan model.TaxFilingSubmission, resultChan chan<- BatchProcessResult) {
	for submission := range submissionChan {
		err := s.taxFilingService.SubmitToTaxBureau(ctx, submission.ID)
		resultChan <- BatchProcessResult{
			SubmissionID: submission.ID,
			Success:      err == nil,
			Error:        err,
		}

		// 添加延迟避免过于频繁的请求
		time.Sleep(100 * time.Millisecond)
	}
}

// BatchProcessResult 批次处理结果
type BatchProcessResult struct {
	SubmissionID string
	Success      bool
	Error        error
}

// updateBatchProgress 更新批次进度
func (s *TaxFilingBatchService) updateBatchProgress(ctx context.Context, batchID string, successCount, failedCount int) {
	updates := map[string]interface{}{
		"successful_submissions": successCount,
		"failed_submissions":     failedCount,
		"updated_at":             time.Now(),
	}

	if err := s.db.Model(&model.TaxFilingBatch{}).Where("id = ?", batchID).Updates(updates).Error; err != nil {
		s.logger.Error("Failed to update batch progress", zap.Error(err), zap.String("batch_id", batchID))
	}

	// 更新缓存
	if err := s.redisService.InvalidateBatchCache(ctx, batchID); err != nil {
		s.logger.Warn("Failed to invalidate batch cache", zap.Error(err))
	}
}

// completeBatchProcessing 完成批次处理
func (s *TaxFilingBatchService) completeBatchProcessing(ctx context.Context, batchID string, successCount, failedCount int) {
	// 确定最终状态
	var finalStatus model.TaxFilingBatchStatus
	if failedCount == 0 {
		finalStatus = model.TaxFilingBatchStatusCompleted
	} else if successCount == 0 {
		finalStatus = model.TaxFilingBatchStatusFailed
	} else {
		finalStatus = model.TaxFilingBatchStatusCompleted // 部分成功也算完成
	}

	// 更新批次状态
	updates := map[string]interface{}{
		"status":                 finalStatus,
		"successful_submissions": successCount,
		"failed_submissions":     failedCount,
		"completed_at":           time.Now(),
		"updated_at":             time.Now(),
	}

	if err := s.db.Model(&model.TaxFilingBatch{}).Where("id = ?", batchID).Updates(updates).Error; err != nil {
		s.logger.Error("Failed to complete batch processing", zap.Error(err), zap.String("batch_id", batchID))
		return
	}

	// 发送完成通知
	go func() {
		if err := s.notificationService.SendBatchCompletionNotification(ctx, batchID, successCount, failedCount); err != nil {
			s.logger.Error("Failed to send batch completion notification", zap.Error(err))
		}
	}()

	s.logger.Info("Batch processing completed",
		zap.String("batch_id", batchID),
		zap.String("final_status", string(finalStatus)),
		zap.Int("successful", successCount),
		zap.Int("failed", failedCount),
	)
}

// GetBatchByID 根据ID获取批次
func (s *TaxFilingBatchService) GetBatchByID(ctx context.Context, batchID string) (*model.TaxFilingBatch, error) {
	// 先从缓存获取
	if batch, err := s.redisService.GetCachedBatch(ctx, batchID); err == nil && batch != nil {
		return batch, nil
	}

	// 从数据库获取
	var batch model.TaxFilingBatch
	if err := s.db.Preload("Enterprise").Preload("Province").First(&batch, "id = ?", batchID).Error; err != nil {
		s.logger.Error("Failed to get batch", zap.Error(err), zap.String("batch_id", batchID))
		return nil, fmt.Errorf("batch not found: %w", err)
	}

	// 缓存结果
	if err := s.redisService.CacheBatch(ctx, &batch); err != nil {
		s.logger.Warn("Failed to cache batch", zap.Error(err))
	}

	return &batch, nil
}

// updateBatchStatus 更新批次状态
func (s *TaxFilingBatchService) updateBatchStatus(ctx context.Context, batchID string, status model.TaxFilingBatchStatus) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if status == model.TaxFilingBatchStatusProcessing {
		updates["started_at"] = time.Now()
	}

	if err := s.db.Model(&model.TaxFilingBatch{}).Where("id = ?", batchID).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update batch status: %w", err)
	}

	// 更新缓存
	if err := s.redisService.InvalidateBatchCache(ctx, batchID); err != nil {
		s.logger.Warn("Failed to invalidate batch cache", zap.Error(err))
	}

	return nil
}

// validateSubmissionRequest 验证申报请求
func (s *TaxFilingBatchService) validateSubmissionRequest(req *model.TaxFilingSubmissionCreateRequest) error {
	// 基本字段验证
	if req.EnterpriseID == "" {
		return fmt.Errorf("enterprise_id is required")
	}
	if req.ProvinceCode == "" {
		return fmt.Errorf("province_code is required")
	}
	if req.CompanyName == "" {
		return fmt.Errorf("company_name is required")
	}
	if req.TaxID == "" {
		return fmt.Errorf("tax_id is required")
	}
	if len(req.TaxData) == 0 {
		return fmt.Errorf("tax_data is required")
	}

	// 税务数据验证
	for i, taxData := range req.TaxData {
		if taxData.TaxableAmount < 0 {
			return fmt.Errorf("tax_data[%d].taxable_amount cannot be negative", i)
		}
		if taxData.TaxRate < 0 || taxData.TaxRate > 1 {
			return fmt.Errorf("tax_data[%d].tax_rate must be between 0 and 1", i)
		}
		if taxData.TaxAmount < 0 {
			return fmt.Errorf("tax_data[%d].tax_amount cannot be negative", i)
		}
	}

	return nil
}

// CancelBatch 取消批次
func (s *TaxFilingBatchService) CancelBatch(ctx context.Context, batchID string) error {
	// 获取批次信息
	batch, err := s.GetBatchByID(ctx, batchID)
	if err != nil {
		return fmt.Errorf("failed to get batch: %w", err)
	}

	// 检查批次状态
	if batch.IsCompleted() {
		return fmt.Errorf("batch cannot be cancelled in status: %s", batch.Status)
	}

	// 取消批次中的所有待处理申报
	if err := s.db.Model(&model.TaxFilingSubmission{}).
		Where("batch_id = ? AND status IN ?", batchID, []model.TaxFilingSubmissionStatus{
			model.TaxFilingStatusPending,
			model.TaxFilingStatusProcessing,
		}).
		Update("status", model.TaxFilingStatusCancelled).Error; err != nil {
		return fmt.Errorf("failed to cancel batch submissions: %w", err)
	}

	// 更新批次状态
	return s.updateBatchStatus(ctx, batchID, model.TaxFilingBatchStatusCancelled)
}
