package service

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"backend/model"
)

// TaxFilingNotificationService 税务申报通知服务
type TaxFilingNotificationService struct {
	db                  *gorm.DB
	logger              *zap.Logger
	redisService        *RedisService
	notificationService NotificationService
}

// TaxFilingNotificationConfig 税务申报通知配置
type TaxFilingNotificationConfig struct {
	EmailEnabled   bool   `json:"email_enabled"`
	SMSEnabled     bool   `json:"sms_enabled"`
	WebhookEnabled bool   `json:"webhook_enabled"`
	WebhookURL     string `json:"webhook_url"`
	EmailSMTPHost  string `json:"email_smtp_host"`
	EmailSMTPPort  int    `json:"email_smtp_port"`
	EmailUsername  string `json:"email_username"`
	EmailPassword  string `json:"email_password"`
	SMSAPIKey      string `json:"sms_api_key"`
	SMSAPISecret   string `json:"sms_api_secret"`
}

// NewTaxFilingNotificationService 创建税务申报通知服务
func NewTaxFilingNotificationService(
	db *gorm.DB,
	logger *zap.Logger,
	redisService *RedisService,
	notificationService NotificationService,
) *TaxFilingNotificationService {
	return &TaxFilingNotificationService{
		db:                  db,
		logger:              logger,
		redisService:        redisService,
		notificationService: notificationService,
	}
}

// SendSubmissionStatusNotification 发送申报状态通知
func (s *TaxFilingNotificationService) SendSubmissionStatusNotification(ctx context.Context, submissionID, status string) error {
	// 获取申报记录
	var submission model.TaxFilingSubmission
	if err := s.db.Preload("Enterprise").First(&submission, "id = ?", submissionID).Error; err != nil {
		s.logger.Error("Failed to get submission for notification", zap.Error(err))
		return err
	}

	// 构建通知内容
	title := fmt.Sprintf("申报状态更新 - %s", submission.CompanyName)
	content := s.buildSubmissionStatusContent(&submission, status)

	// 创建通知记录
	entityType := "tax_filing_submission"
	notification := model.NotificationCreateRequest{
		RecipientID:       submission.EnterpriseID,
		Type:              "tax_filing_status",
		Title:             title,
		Content:           content,
		RelatedEntityType: &entityType,
		RelatedEntityID:   &submissionID,
	}

	// 发送通知
	if _, err := s.notificationService.CreateNotification(ctx, notification); err != nil {
		s.logger.Error("Failed to create notification", zap.Error(err))
		return err
	}

	s.logger.Info("Submission status notification sent",
		zap.String("submission_id", submissionID),
		zap.String("status", status),
		zap.String("company", submission.CompanyName),
	)

	return nil
}

// SendBatchCompletionNotification 发送批次完成通知
func (s *TaxFilingNotificationService) SendBatchCompletionNotification(ctx context.Context, batchID string, successCount, failedCount int) error {
	// 获取批次记录
	var batch model.TaxFilingBatch
	if err := s.db.Preload("Enterprise").First(&batch, "id = ?", batchID).Error; err != nil {
		s.logger.Error("Failed to get batch for notification", zap.Error(err))
		return err
	}

	// 构建通知内容
	title := fmt.Sprintf("批次处理完成 - %s", batch.Name)
	content := s.buildBatchCompletionContent(&batch, successCount, failedCount)

	// 创建通知记录
	entityType := "tax_filing_batch"
	notification := model.NotificationCreateRequest{
		RecipientID:       batch.EnterpriseID,
		Type:              "batch_completion",
		Title:             title,
		Content:           content,
		RelatedEntityType: &entityType,
		RelatedEntityID:   &batchID,
	}

	// 发送通知
	if _, err := s.notificationService.CreateNotification(ctx, notification); err != nil {
		s.logger.Error("Failed to create batch completion notification", zap.Error(err))
		return err
	}

	s.logger.Info("Batch completion notification sent",
		zap.String("batch_id", batchID),
		zap.String("batch_name", batch.Name),
		zap.Int("success", successCount),
		zap.Int("failed", failedCount),
	)

	return nil
}

// SendSystemAlertNotification 发送系统告警通知
func (s *TaxFilingNotificationService) SendSystemAlertNotification(ctx context.Context, alertType, title, message string) error {
	// 获取系统管理员列表
	var admins []model.User
	if err := s.db.Where("role = ?", "admin").Find(&admins).Error; err != nil {
		s.logger.Error("Failed to get admin users", zap.Error(err))
		return err
	}

	// 为每个管理员创建通知
	for _, admin := range admins {
		entityType := "system_alert"
		notification := model.NotificationCreateRequest{
			RecipientID:       admin.ID,
			Type:              "system_alert",
			Title:             title,
			Content:           message,
			RelatedEntityType: &entityType,
		}

		if _, err := s.notificationService.CreateNotification(ctx, notification); err != nil {
			s.logger.Error("Failed to create system alert notification",
				zap.Error(err),
				zap.String("admin_id", admin.ID),
			)
		}
	}

	s.logger.Info("System alert notification sent",
		zap.String("alert_type", alertType),
		zap.String("title", title),
		zap.Int("admin_count", len(admins)),
	)

	return nil
}

// SendValidationFailureNotification 发送验证失败通知
func (s *TaxFilingNotificationService) SendValidationFailureNotification(ctx context.Context, submissionID, provinceCode string, errors []string) error {
	// 获取申报记录
	var submission model.TaxFilingSubmission
	if err := s.db.Preload("Enterprise").First(&submission, "id = ?", submissionID).Error; err != nil {
		s.logger.Error("Failed to get submission for validation notification", zap.Error(err))
		return err
	}

	// 构建通知内容
	title := "数据验证失败"
	content := s.buildValidationFailureContent(provinceCode, errors)

	// 创建通知记录
	entityType := "tax_filing_submission"
	notification := model.NotificationCreateRequest{
		RecipientID:       submission.EnterpriseID,
		Type:              "validation_failure",
		Title:             title,
		Content:           content,
		RelatedEntityType: &entityType,
		RelatedEntityID:   &submissionID,
	}

	// 发送通知
	if _, err := s.notificationService.CreateNotification(ctx, notification); err != nil {
		s.logger.Error("Failed to create validation failure notification", zap.Error(err))
		return err
	}

	s.logger.Info("Validation failure notification sent",
		zap.String("submission_id", submissionID),
		zap.String("province_code", provinceCode),
		zap.Int("error_count", len(errors)),
	)

	return nil
}

// SendProvinceMaintenanceNotification 发送省份维护通知
func (s *TaxFilingNotificationService) SendProvinceMaintenanceNotification(ctx context.Context, provinceCode, message string) error {
	// 获取使用该省份的所有企业
	var enterprises []model.Enterprise
	if err := s.db.Where("province = ?", provinceCode).Find(&enterprises).Error; err != nil {
		s.logger.Error("Failed to get enterprises for province maintenance notification", zap.Error(err))
		return err
	}

	title := fmt.Sprintf("省份维护通知 - %s", provinceCode)

	// 为每个企业创建通知
	for _, enterprise := range enterprises {
		entityType := "province_maintenance"
		notification := model.NotificationCreateRequest{
			RecipientID:       enterprise.ID,
			Type:              "province_maintenance",
			Title:             title,
			Content:           message,
			RelatedEntityType: &entityType,
			RelatedEntityID:   &provinceCode,
		}

		if _, err := s.notificationService.CreateNotification(ctx, notification); err != nil {
			s.logger.Error("Failed to create province maintenance notification",
				zap.Error(err),
				zap.String("enterprise_id", enterprise.ID),
			)
		}
	}

	s.logger.Info("Province maintenance notification sent",
		zap.String("province_code", provinceCode),
		zap.Int("enterprise_count", len(enterprises)),
	)

	return nil
}

// buildSubmissionStatusContent 构建申报状态通知内容
func (s *TaxFilingNotificationService) buildSubmissionStatusContent(submission *model.TaxFilingSubmission, status string) string {
	return fmt.Sprintf(`
申报状态更新通知

公司名称: %s
税务登记号: %s
申报期间: %d年%s
省份: %s
当前状态: %s
更新时间: %s

如有疑问，请联系系统管理员。
`,
		submission.CompanyName,
		submission.TaxID,
		submission.TaxYear,
		s.formatTaxPeriod(submission),
		submission.ProvinceName,
		status,
		time.Now().Format("2006-01-02 15:04:05"),
	)
}

// buildBatchCompletionContent 构建批次完成通知内容
func (s *TaxFilingNotificationService) buildBatchCompletionContent(batch *model.TaxFilingBatch, successCount, failedCount int) string {
	return fmt.Sprintf(`
批次处理完成通知

批次名称: %s
处理结果:
- 成功: %d 个申报
- 失败: %d 个申报
- 总计: %d 个申报

完成时间: %s

请登录系统查看详细结果。
`,
		batch.Name,
		successCount,
		failedCount,
		successCount+failedCount,
		time.Now().Format("2006-01-02 15:04:05"),
	)
}

// buildValidationFailureContent 构建验证失败通知内容
func (s *TaxFilingNotificationService) buildValidationFailureContent(provinceCode string, errors []string) string {
	content := fmt.Sprintf("省份 %s 的数据验证失败:\n\n", provinceCode)
	for i, err := range errors {
		content += fmt.Sprintf("%d. %s\n", i+1, err)
	}
	content += "\n请检查数据格式并重新提交。"
	return content
}

// formatTaxPeriod 格式化申报期间
func (s *TaxFilingNotificationService) formatTaxPeriod(submission *model.TaxFilingSubmission) string {
	switch submission.PeriodType {
	case model.TaxFilingPeriodMonthly:
		if submission.TaxMonth != nil {
			return fmt.Sprintf("%d月", *submission.TaxMonth)
		}
	case model.TaxFilingPeriodQuarterly:
		if submission.TaxQuarter != nil {
			return fmt.Sprintf("第%d季度", *submission.TaxQuarter)
		}
	case model.TaxFilingPeriodYearly:
		return "年度"
	}
	return "未知期间"
}

// GetNotificationConfig 获取通知配置
func (s *TaxFilingNotificationService) GetNotificationConfig(ctx context.Context) (*TaxFilingNotificationConfig, error) {
	// 先从缓存获取
	var config TaxFilingNotificationConfig
	if err := s.redisService.GetConfig(ctx, "tax_filing_notification", &config); err == nil {
		return &config, nil
	}

	// 从数据库或配置文件获取
	// 这里返回默认配置
	config = TaxFilingNotificationConfig{
		EmailEnabled:   true,
		SMSEnabled:     false,
		WebhookEnabled: true,
		WebhookURL:     "http://localhost:8081/api/tax-filing/webhook",
	}

	// 缓存配置
	if err := s.redisService.SetConfig(ctx, "tax_filing_notification", config); err != nil {
		s.logger.Warn("Failed to cache notification config", zap.Error(err))
	}

	return &config, nil
}

// UpdateNotificationConfig 更新通知配置
func (s *TaxFilingNotificationService) UpdateNotificationConfig(ctx context.Context, config *TaxFilingNotificationConfig) error {
	// 更新缓存
	if err := s.redisService.SetConfig(ctx, "tax_filing_notification", config); err != nil {
		s.logger.Error("Failed to update notification config", zap.Error(err))
		return err
	}

	s.logger.Info("Notification config updated successfully")
	return nil
}
