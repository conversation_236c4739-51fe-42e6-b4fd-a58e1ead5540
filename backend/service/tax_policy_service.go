// Package service provides business logic for the tax management system.
// This file implements tax policy management services including CRUD operations,
// policy search, and policy effectiveness management.
package service

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// TaxPolicyService provides tax policy management functionality
type TaxPolicyService struct {
	db *gorm.DB
}

// NewTaxPolicyService creates a new tax policy service instance
func NewTaxPolicyService(db *gorm.DB) *TaxPolicyService {
	return &TaxPolicyService{db: db}
}

// CreateTaxPolicyRequest represents a request to create a new tax policy
type CreateTaxPolicyRequest struct {
	PolicyNumber           string     `json:"policy_number" binding:"required"`
	Title                  string     `json:"title" binding:"required"`
	Category               string     `json:"category" binding:"required"`
	TaxTypes               string     `json:"tax_types"`
	ApplicableRegions      string     `json:"applicable_regions"`
	ApplicableIndustries   string     `json:"applicable_industries"`
	ApplicableEnterprises  string     `json:"applicable_enterprises"`
	PolicyContent          string     `json:"policy_content" binding:"required"`
	PolicySummary          string     `json:"policy_summary"`
	LegalBasis             string     `json:"legal_basis"`
	IssuingAuthority       string     `json:"issuing_authority" binding:"required"`
	IssueDate              time.Time  `json:"issue_date" binding:"required"`
	EffectiveDate          time.Time  `json:"effective_date" binding:"required"`
	ExpiryDate             *time.Time `json:"expiry_date"`
	PriorityLevel          string     `json:"priority_level"`
	ImpactAssessment       string     `json:"impact_assessment"`
	ImplementationGuidance string     `json:"implementation_guidance"`
	RelatedPolicies        string     `json:"related_policies"`
	Attachments            string     `json:"attachments"`
	Keywords               string     `json:"keywords"`
}

// UpdateTaxPolicyRequest represents a request to update a tax policy
type UpdateTaxPolicyRequest struct {
	Title                  string     `json:"title"`
	Category               string     `json:"category"`
	TaxTypes               string     `json:"tax_types"`
	ApplicableRegions      string     `json:"applicable_regions"`
	ApplicableIndustries   string     `json:"applicable_industries"`
	ApplicableEnterprises  string     `json:"applicable_enterprises"`
	PolicyContent          string     `json:"policy_content"`
	PolicySummary          string     `json:"policy_summary"`
	LegalBasis             string     `json:"legal_basis"`
	IssuingAuthority       string     `json:"issuing_authority"`
	IssueDate              *time.Time `json:"issue_date"`
	EffectiveDate          *time.Time `json:"effective_date"`
	ExpiryDate             *time.Time `json:"expiry_date"`
	Status                 string     `json:"status"`
	PriorityLevel          string     `json:"priority_level"`
	ImpactAssessment       string     `json:"impact_assessment"`
	ImplementationGuidance string     `json:"implementation_guidance"`
	RelatedPolicies        string     `json:"related_policies"`
	Attachments            string     `json:"attachments"`
	Keywords               string     `json:"keywords"`
}

// TaxPolicyFilter represents filters for tax policy queries
type TaxPolicyFilter struct {
	Page             int    `json:"page"`
	PageSize         int    `json:"page_size"`
	Category         string `json:"category"`
	Status           string `json:"status"`
	IssuingAuthority string `json:"issuing_authority"`
	PriorityLevel    string `json:"priority_level"`
	Keyword          string `json:"keyword"`
	EffectiveFrom    string `json:"effective_from"`
	EffectiveTo      string `json:"effective_to"`
}

// CreateTaxPolicy creates a new tax policy
func (s *TaxPolicyService) CreateTaxPolicy(ctx context.Context, req CreateTaxPolicyRequest, userID string) (*model.TaxPolicy, error) {
	// Check if policy number already exists
	var existingPolicy model.TaxPolicy
	if err := s.db.Where("policy_number = ?", req.PolicyNumber).First(&existingPolicy).Error; err == nil {
		return nil, fmt.Errorf("政策编号 %s 已存在", req.PolicyNumber)
	}

	policy := &model.TaxPolicy{
		ID:                     util.GenerateID(),
		PolicyNumber:           req.PolicyNumber,
		Title:                  req.Title,
		Category:               req.Category,
		TaxTypes:               req.TaxTypes,
		ApplicableRegions:      req.ApplicableRegions,
		ApplicableIndustries:   req.ApplicableIndustries,
		ApplicableEnterprises:  req.ApplicableEnterprises,
		PolicyContent:          req.PolicyContent,
		PolicySummary:          req.PolicySummary,
		LegalBasis:             req.LegalBasis,
		IssuingAuthority:       req.IssuingAuthority,
		IssueDate:              req.IssueDate,
		EffectiveDate:          req.EffectiveDate,
		ExpiryDate:             req.ExpiryDate,
		Status:                 "draft",
		PriorityLevel:          req.PriorityLevel,
		ImpactAssessment:       req.ImpactAssessment,
		ImplementationGuidance: req.ImplementationGuidance,
		RelatedPolicies:        req.RelatedPolicies,
		Attachments:            req.Attachments,
		Keywords:               req.Keywords,
		ViewCount:              0,
		CreatedBy:              userID,
		CreatedAt:              time.Now(),
		UpdatedAt:              time.Now(),
	}

	if policy.PriorityLevel == "" {
		policy.PriorityLevel = "normal"
	}

	if err := s.db.Create(policy).Error; err != nil {
		return nil, fmt.Errorf("创建税务政策失败: %w", err)
	}

	return policy, nil
}

// GetTaxPolicies retrieves tax policies with pagination and filtering
func (s *TaxPolicyService) GetTaxPolicies(ctx context.Context, filter TaxPolicyFilter) ([]model.TaxPolicy, int64, error) {
	var policies []model.TaxPolicy
	var total int64

	query := s.db.Model(&model.TaxPolicy{}).Preload("Creator")

	// Apply filters
	if filter.Category != "" {
		query = query.Where("category = ?", filter.Category)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.IssuingAuthority != "" {
		query = query.Where("issuing_authority LIKE ?", "%"+filter.IssuingAuthority+"%")
	}
	if filter.PriorityLevel != "" {
		query = query.Where("priority_level = ?", filter.PriorityLevel)
	}
	if filter.Keyword != "" {
		query = query.Where("title LIKE ? OR policy_content LIKE ? OR keywords LIKE ?",
			"%"+filter.Keyword+"%", "%"+filter.Keyword+"%", "%"+filter.Keyword+"%")
	}
	if filter.EffectiveFrom != "" {
		query = query.Where("effective_date >= ?", filter.EffectiveFrom)
	}
	if filter.EffectiveTo != "" {
		query = query.Where("effective_date <= ?", filter.EffectiveTo)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("统计税务政策数量失败: %w", err)
	}

	// Apply pagination
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	// Execute query
	if err := query.Order("created_at DESC").Find(&policies).Error; err != nil {
		return nil, 0, fmt.Errorf("查询税务政策失败: %w", err)
	}

	return policies, total, nil
}

// GetTaxPolicyByID retrieves a tax policy by ID
func (s *TaxPolicyService) GetTaxPolicyByID(ctx context.Context, id string) (*model.TaxPolicy, error) {
	var policy model.TaxPolicy
	if err := s.db.Preload("Creator").Where("id = ?", id).First(&policy).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("税务政策不存在")
		}
		return nil, fmt.Errorf("查询税务政策失败: %w", err)
	}

	// Increment view count
	s.db.Model(&policy).UpdateColumn("view_count", gorm.Expr("view_count + ?", 1))

	return &policy, nil
}

// UpdateTaxPolicy updates a tax policy
func (s *TaxPolicyService) UpdateTaxPolicy(ctx context.Context, id string, req UpdateTaxPolicyRequest) (*model.TaxPolicy, error) {
	var policy model.TaxPolicy
	if err := s.db.Where("id = ?", id).First(&policy).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("税务政策不存在")
		}
		return nil, fmt.Errorf("查询税务政策失败: %w", err)
	}

	// Update fields
	updates := make(map[string]interface{})
	if req.Title != "" {
		updates["title"] = req.Title
	}
	if req.Category != "" {
		updates["category"] = req.Category
	}
	if req.TaxTypes != "" {
		updates["tax_types"] = req.TaxTypes
	}
	if req.ApplicableRegions != "" {
		updates["applicable_regions"] = req.ApplicableRegions
	}
	if req.ApplicableIndustries != "" {
		updates["applicable_industries"] = req.ApplicableIndustries
	}
	if req.ApplicableEnterprises != "" {
		updates["applicable_enterprises"] = req.ApplicableEnterprises
	}
	if req.PolicyContent != "" {
		updates["policy_content"] = req.PolicyContent
	}
	if req.PolicySummary != "" {
		updates["policy_summary"] = req.PolicySummary
	}
	if req.LegalBasis != "" {
		updates["legal_basis"] = req.LegalBasis
	}
	if req.IssuingAuthority != "" {
		updates["issuing_authority"] = req.IssuingAuthority
	}
	if req.IssueDate != nil {
		updates["issue_date"] = *req.IssueDate
	}
	if req.EffectiveDate != nil {
		updates["effective_date"] = *req.EffectiveDate
	}
	if req.ExpiryDate != nil {
		updates["expiry_date"] = *req.ExpiryDate
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}
	if req.PriorityLevel != "" {
		updates["priority_level"] = req.PriorityLevel
	}
	if req.ImpactAssessment != "" {
		updates["impact_assessment"] = req.ImpactAssessment
	}
	if req.ImplementationGuidance != "" {
		updates["implementation_guidance"] = req.ImplementationGuidance
	}
	if req.RelatedPolicies != "" {
		updates["related_policies"] = req.RelatedPolicies
	}
	if req.Attachments != "" {
		updates["attachments"] = req.Attachments
	}
	if req.Keywords != "" {
		updates["keywords"] = req.Keywords
	}

	updates["updated_at"] = time.Now()

	if err := s.db.Model(&policy).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("更新税务政策失败: %w", err)
	}

	// Reload policy with associations
	if err := s.db.Preload("Creator").Where("id = ?", id).First(&policy).Error; err != nil {
		return nil, fmt.Errorf("重新加载税务政策失败: %w", err)
	}

	return &policy, nil
}

// DeleteTaxPolicy deletes a tax policy
func (s *TaxPolicyService) DeleteTaxPolicy(ctx context.Context, id string) error {
	result := s.db.Where("id = ?", id).Delete(&model.TaxPolicy{})
	if result.Error != nil {
		return fmt.Errorf("删除税务政策失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("税务政策不存在")
	}
	return nil
}

// BatchDeleteTaxPolicies deletes multiple tax policies
func (s *TaxPolicyService) BatchDeleteTaxPolicies(ctx context.Context, ids []string) error {
	if len(ids) == 0 {
		return fmt.Errorf("未提供要删除的政策ID")
	}

	result := s.db.Where("id IN ?", ids).Delete(&model.TaxPolicy{})
	if result.Error != nil {
		return fmt.Errorf("批量删除税务政策失败: %w", result.Error)
	}

	return nil
}

// UpdateTaxPolicyStatus updates the status of a tax policy
func (s *TaxPolicyService) UpdateTaxPolicyStatus(ctx context.Context, id string, status string) error {
	result := s.db.Model(&model.TaxPolicy{}).Where("id = ?", id).Update("status", status)
	if result.Error != nil {
		return fmt.Errorf("更新税务政策状态失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("税务政策不存在")
	}
	return nil
}

// BatchUpdateTaxPolicyStatus updates the status of multiple tax policies
func (s *TaxPolicyService) BatchUpdateTaxPolicyStatus(ctx context.Context, ids []string, status string) error {
	if len(ids) == 0 {
		return fmt.Errorf("未提供要更新的政策ID")
	}

	result := s.db.Model(&model.TaxPolicy{}).Where("id IN ?", ids).Update("status", status)
	if result.Error != nil {
		return fmt.Errorf("批量更新税务政策状态失败: %w", result.Error)
	}

	return nil
}

// GetTaxPolicyStats returns statistics about tax policies
func (s *TaxPolicyService) GetTaxPolicyStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total count
	var total int64
	if err := s.db.Model(&model.TaxPolicy{}).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("统计政策总数失败: %w", err)
	}
	stats["total"] = total

	// Count by status
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	if err := s.db.Model(&model.TaxPolicy{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Find(&statusStats).Error; err != nil {
		return nil, fmt.Errorf("统计政策状态分布失败: %w", err)
	}
	stats["by_status"] = statusStats

	// Count by category
	var categoryStats []struct {
		Category string `json:"category"`
		Count    int64  `json:"count"`
	}
	if err := s.db.Model(&model.TaxPolicy{}).
		Select("category, COUNT(*) as count").
		Group("category").
		Find(&categoryStats).Error; err != nil {
		return nil, fmt.Errorf("统计政策分类分布失败: %w", err)
	}
	stats["by_category"] = categoryStats

	// Count by priority level
	var priorityStats []struct {
		PriorityLevel string `json:"priority_level"`
		Count         int64  `json:"count"`
	}
	if err := s.db.Model(&model.TaxPolicy{}).
		Select("priority_level, COUNT(*) as count").
		Group("priority_level").
		Find(&priorityStats).Error; err != nil {
		return nil, fmt.Errorf("统计政策优先级分布失败: %w", err)
	}
	stats["by_priority"] = priorityStats

	// Recent policies (last 30 days)
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	var recentCount int64
	if err := s.db.Model(&model.TaxPolicy{}).
		Where("created_at >= ?", thirtyDaysAgo).
		Count(&recentCount).Error; err != nil {
		return nil, fmt.Errorf("统计近期政策数量失败: %w", err)
	}
	stats["recent_count"] = recentCount

	// Effective policies
	now := time.Now()
	var effectiveCount int64
	if err := s.db.Model(&model.TaxPolicy{}).
		Where("status = ? AND effective_date <= ? AND (expiry_date IS NULL OR expiry_date > ?)",
			"effective", now, now).
		Count(&effectiveCount).Error; err != nil {
		return nil, fmt.Errorf("统计有效政策数量失败: %w", err)
	}
	stats["effective_count"] = effectiveCount

	return stats, nil
}

// SearchTaxPolicies searches tax policies by keyword
func (s *TaxPolicyService) SearchTaxPolicies(ctx context.Context, keyword string, limit int) ([]model.TaxPolicy, error) {
	var policies []model.TaxPolicy

	if limit <= 0 {
		limit = 10
	}

	query := s.db.Model(&model.TaxPolicy{}).Preload("Creator")

	if keyword != "" {
		query = query.Where("title LIKE ? OR policy_content LIKE ? OR keywords LIKE ? OR policy_number LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	if err := query.Order("view_count DESC, created_at DESC").Limit(limit).Find(&policies).Error; err != nil {
		return nil, fmt.Errorf("搜索税务政策失败: %w", err)
	}

	return policies, nil
}

// GetEffectiveTaxPolicies returns currently effective tax policies
func (s *TaxPolicyService) GetEffectiveTaxPolicies(ctx context.Context, category string) ([]model.TaxPolicy, error) {
	var policies []model.TaxPolicy

	now := time.Now()
	query := s.db.Model(&model.TaxPolicy{}).Preload("Creator").
		Where("status = ? AND effective_date <= ? AND (expiry_date IS NULL OR expiry_date > ?)",
			"effective", now, now)

	if category != "" {
		query = query.Where("category = ?", category)
	}

	if err := query.Order("priority_level DESC, effective_date DESC").Find(&policies).Error; err != nil {
		return nil, fmt.Errorf("查询有效税务政策失败: %w", err)
	}

	return policies, nil
}
