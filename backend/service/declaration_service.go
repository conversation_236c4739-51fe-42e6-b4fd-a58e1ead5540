// Package service provides business logic layer for the tax management system.
// This file implements declaration services for managing tax declarations.
package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"backend/model"
)

// DeclarationService defines the interface for declaration business logic
type DeclarationService interface {
	GetDeclarationStats(ctx context.Context) (*DeclarationStatsResponse, error)
	GetDeclarations(ctx context.Context, req GetDeclarationsRequest) (*PaginatedResponse, error)
	GetDeclarationByID(ctx context.Context, id string) (*model.Declaration, error)
	CreateDeclaration(ctx context.Context, req CreateDeclarationRequest) (*model.Declaration, error)
	UpdateDeclaration(ctx context.Context, id string, req UpdateDeclarationRequest) (*model.Declaration, error)
	DeleteDeclaration(ctx context.Context, id string) error
	SubmitDeclaration(ctx context.Context, id string) (*model.Declaration, error)
}

// GetDeclarationsRequest 获取申报列表请求
type GetDeclarationsRequest struct {
	EnterpriseID string    `json:"enterprise_id" form:"enterprise_id"`
	TaxTypeID    string    `json:"tax_type_id" form:"tax_type_id"`
	Status       string    `json:"status" form:"status"`
	Year         int       `json:"year" form:"year"`
	Month        *int      `json:"month" form:"month"`
	StartDate    time.Time `json:"start_date" form:"start_date"`
	EndDate      time.Time `json:"end_date" form:"end_date"`
	Page         int       `json:"page" form:"page"`
	PageSize     int       `json:"page_size" form:"page_size"`
	SortBy       string    `json:"sort_by" form:"sort_by"`
	SortOrder    string    `json:"sort_order" form:"sort_order"`
}

// CreateDeclarationRequest 创建申报请求
type CreateDeclarationRequest struct {
	EnterpriseID    string          `json:"enterprise_id" binding:"required"`
	TaxTypeID       string          `json:"tax_type_id" binding:"required"`
	DeclarationDate time.Time       `json:"declaration_date" binding:"required"`
	DueDate         time.Time       `json:"due_date" binding:"required"`
	PeriodType      string          `json:"period_type" binding:"required"`
	Year            int             `json:"year" binding:"required"`
	Month           *int            `json:"month"`
	Quarter         *int            `json:"quarter"`
	Amount          decimal.Decimal `json:"amount"`
	TaxPeriodStart  time.Time       `json:"tax_period_start" binding:"required"`
	TaxPeriodEnd    time.Time       `json:"tax_period_end" binding:"required"`
}

// UpdateDeclarationRequest 更新申报请求
type UpdateDeclarationRequest struct {
	DeclarationDate time.Time       `json:"declaration_date"`
	DueDate         time.Time       `json:"due_date"`
	Amount          decimal.Decimal `json:"amount"`
	Status          string          `json:"status"`
}

// declarationService implements DeclarationService interface
type declarationService struct {
	db *gorm.DB
}

// NewDeclarationService creates a new declaration service
func NewDeclarationService(db *gorm.DB) DeclarationService {
	return &declarationService{
		db: db,
	}
}

// DeclarationStatsResponse defines the response structure for declaration statistics
type DeclarationStatsResponse struct {
	Total                 int64   `json:"total"`
	Pending               int64   `json:"pending"`
	Submitted             int64   `json:"submitted"`
	Approved              int64   `json:"approved"`
	Rejected              int64   `json:"rejected"`
	Completed             int64   `json:"completed"`
	TotalTaxAmount        float64 `json:"totalTaxAmount"`
	ThisMonth             int64   `json:"thisMonth"`
	ThisQuarter           int64   `json:"thisQuarter"`
	OverdueCount          int64   `json:"overdueCount"`
	AverageProcessingDays float64 `json:"averageProcessingDays"`
}

// GetDeclarationStats 获取申报统计信息
func (s *declarationService) GetDeclarationStats(ctx context.Context) (*DeclarationStatsResponse, error) {
	stats := &DeclarationStatsResponse{}

	// 获取总申报数
	if err := s.db.WithContext(ctx).Model(&model.Declaration{}).Count(&stats.Total).Error; err != nil {
		return nil, err
	}

	// 按状态统计
	var statusStats []struct {
		Status string
		Count  int64
	}
	if err := s.db.WithContext(ctx).Model(&model.Declaration{}).
		Select("declaration_status as status, count(*) as count").
		Group("declaration_status").
		Scan(&statusStats).Error; err != nil {
		return nil, err
	}

	// 处理状态统计
	for _, stat := range statusStats {
		switch stat.Status {
		case "submitted":
			stats.Pending = stat.Count
			stats.Submitted = stat.Count
		case "approved":
			stats.Approved = stat.Count
			stats.Completed += stat.Count
		case "rejected":
			stats.Rejected = stat.Count
		case "draft":
			// 草稿状态不计入pending，单独处理
		}
	}

	// 计算总税额
	var totalTaxAmount struct {
		TotalTax float64
	}
	if err := s.db.WithContext(ctx).Model(&model.Declaration{}).
		Select("COALESCE(SUM(tax_payable), 0) as total_tax").
		Where("declaration_status IN ?", []string{"approved", "submitted"}).
		Scan(&totalTaxAmount).Error; err != nil {
		return nil, err
	}
	stats.TotalTaxAmount = totalTaxAmount.TotalTax

	// 本月申报数量
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	if err := s.db.WithContext(ctx).Model(&model.Declaration{}).
		Where("created_at >= ?", monthStart).
		Count(&stats.ThisMonth).Error; err != nil {
		return nil, err
	}

	// 本季度申报数量
	var quarterStart time.Time
	switch now.Month() {
	case 1, 2, 3:
		quarterStart = time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	case 4, 5, 6:
		quarterStart = time.Date(now.Year(), 4, 1, 0, 0, 0, 0, now.Location())
	case 7, 8, 9:
		quarterStart = time.Date(now.Year(), 7, 1, 0, 0, 0, 0, now.Location())
	case 10, 11, 12:
		quarterStart = time.Date(now.Year(), 10, 1, 0, 0, 0, 0, now.Location())
	}

	if err := s.db.WithContext(ctx).Model(&model.Declaration{}).
		Where("created_at >= ?", quarterStart).
		Count(&stats.ThisQuarter).Error; err != nil {
		return nil, err
	}

	// 逾期申报数量（假设申报期限为每月15日）
	currentMonth := time.Date(now.Year(), now.Month(), 15, 23, 59, 59, 0, now.Location())
	if err := s.db.WithContext(ctx).Model(&model.Declaration{}).
		Where("created_at < ? AND declaration_status IN ?", currentMonth, []string{"draft"}).
		Count(&stats.OverdueCount).Error; err != nil {
		return nil, err
	}

	// 计算平均处理天数
	var avgProcessingDays struct {
		AvgDays float64
	}
	if err := s.db.WithContext(ctx).Model(&model.Declaration{}).
		Select("AVG(DATEDIFF(updated_at, created_at)) as avg_days").
		Where("declaration_status = ?", "approved").
		Scan(&avgProcessingDays).Error; err != nil {
		return nil, err
	}
	stats.AverageProcessingDays = avgProcessingDays.AvgDays

	return stats, nil
}

// GetDeclarations 获取申报列表
func (s *declarationService) GetDeclarations(ctx context.Context, req GetDeclarationsRequest) (*PaginatedResponse, error) {
	var declarations []model.Declaration
	var total int64

	query := s.db.WithContext(ctx).Model(&model.Declaration{})

	// 添加筛选条件
	if req.EnterpriseID != "" {
		query = query.Where("enterprise_id = ?", req.EnterpriseID)
	}
	if req.TaxTypeID != "" {
		query = query.Where("tax_type_id = ?", req.TaxTypeID)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.Year > 0 {
		query = query.Where("year = ?", req.Year)
	}
	if req.Month != nil && *req.Month > 0 {
		query = query.Where("month = ?", *req.Month)
	}
	if !req.StartDate.IsZero() {
		query = query.Where("declaration_date >= ?", req.StartDate)
	}
	if !req.EndDate.IsZero() {
		query = query.Where("declaration_date <= ?", req.EndDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取申报总数失败: %w", err)
	}

	// 分页和排序
	offset := (req.Page - 1) * req.PageSize
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	orderBy := fmt.Sprintf("%s %s", req.SortBy, req.SortOrder)
	if err := query.Preload("Enterprise").Preload("TaxType").
		Order(orderBy).Offset(offset).Limit(req.PageSize).
		Find(&declarations).Error; err != nil {
		return nil, fmt.Errorf("获取申报列表失败: %w", err)
	}

	// 计算总页数
	pages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &PaginatedResponse{
		Items:    declarations,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Pages:    pages,
	}, nil
}

// GetDeclarationByID 根据ID获取申报详情
func (s *declarationService) GetDeclarationByID(ctx context.Context, id string) (*model.Declaration, error) {
	var declaration model.Declaration

	if err := s.db.WithContext(ctx).
		Preload("Enterprise").
		Preload("TaxType").
		Where("id = ?", id).
		First(&declaration).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("申报记录不存在")
		}
		return nil, fmt.Errorf("获取申报详情失败: %w", err)
	}

	return &declaration, nil
}

// CreateDeclaration 创建申报
func (s *declarationService) CreateDeclaration(ctx context.Context, req CreateDeclarationRequest) (*model.Declaration, error) {
	// 验证企业是否存在
	var enterprise model.Enterprise
	if err := s.db.WithContext(ctx).Where("id = ?", req.EnterpriseID).First(&enterprise).Error; err != nil {
		return nil, fmt.Errorf("企业不存在")
	}

	// 验证税种是否存在
	var taxType model.TaxType
	if err := s.db.WithContext(ctx).Where("id = ?", req.TaxTypeID).First(&taxType).Error; err != nil {
		return nil, fmt.Errorf("税种不存在")
	}

	// 生成申报编号
	declarationNumber := s.generateDeclarationNumber(req.TaxTypeID, req.Year, req.Month, req.Quarter)

	// 创建申报记录
	declaration := &model.Declaration{
		ID:                uuid.New().String(),
		EnterpriseID:      req.EnterpriseID,
		TaxTypeID:         req.TaxTypeID,
		DeclarationNumber: declarationNumber,
		DeclarationDate:   req.DeclarationDate,
		DueDate:           req.DueDate,
		PeriodType:        req.PeriodType,
		Year:              req.Year,
		Month:             req.Month,
		Quarter:           req.Quarter,
		Status:            model.DeclarationStatusDraft,
		Amount:            req.Amount,
		TaxPeriodStart:    req.TaxPeriodStart,
		TaxPeriodEnd:      req.TaxPeriodEnd,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	if err := s.db.WithContext(ctx).Create(declaration).Error; err != nil {
		return nil, fmt.Errorf("创建申报失败: %w", err)
	}

	// 预加载关联数据
	if err := s.db.WithContext(ctx).
		Preload("Enterprise").
		Preload("TaxType").
		Where("id = ?", declaration.ID).
		First(declaration).Error; err != nil {
		return nil, fmt.Errorf("获取创建的申报详情失败: %w", err)
	}

	return declaration, nil
}

// UpdateDeclaration 更新申报
func (s *declarationService) UpdateDeclaration(ctx context.Context, id string, req UpdateDeclarationRequest) (*model.Declaration, error) {
	var declaration model.Declaration

	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&declaration).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("申报记录不存在")
		}
		return nil, fmt.Errorf("获取申报记录失败: %w", err)
	}

	// 检查申报状态，只有草稿状态才能更新
	if declaration.Status != model.DeclarationStatusDraft {
		return nil, fmt.Errorf("只有草稿状态的申报才能更新")
	}

	// 更新字段
	if !req.DeclarationDate.IsZero() {
		declaration.DeclarationDate = req.DeclarationDate
	}
	if !req.DueDate.IsZero() {
		declaration.DueDate = req.DueDate
	}
	if !req.Amount.IsZero() {
		declaration.Amount = req.Amount
	}
	if req.Status != "" {
		declaration.Status = req.Status
	}
	declaration.UpdatedAt = time.Now()

	if err := s.db.WithContext(ctx).Save(&declaration).Error; err != nil {
		return nil, fmt.Errorf("更新申报失败: %w", err)
	}

	// 预加载关联数据
	if err := s.db.WithContext(ctx).
		Preload("Enterprise").
		Preload("TaxType").
		Where("id = ?", declaration.ID).
		First(&declaration).Error; err != nil {
		return nil, fmt.Errorf("获取更新后的申报详情失败: %w", err)
	}

	return &declaration, nil
}

// DeleteDeclaration 删除申报
func (s *declarationService) DeleteDeclaration(ctx context.Context, id string) error {
	var declaration model.Declaration

	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&declaration).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("申报记录不存在")
		}
		return fmt.Errorf("获取申报记录失败: %w", err)
	}

	// 检查申报状态，只有草稿状态才能删除
	if declaration.Status != model.DeclarationStatusDraft {
		return fmt.Errorf("只有草稿状态的申报才能删除")
	}

	// 软删除
	if err := s.db.WithContext(ctx).Delete(&declaration).Error; err != nil {
		return fmt.Errorf("删除申报失败: %w", err)
	}

	return nil
}

// SubmitDeclaration 提交申报
func (s *declarationService) SubmitDeclaration(ctx context.Context, id string) (*model.Declaration, error) {
	var declaration model.Declaration

	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&declaration).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("申报记录不存在")
		}
		return nil, fmt.Errorf("获取申报记录失败: %w", err)
	}

	// 检查申报状态
	if declaration.Status != model.DeclarationStatusDraft {
		return nil, fmt.Errorf("只有草稿状态的申报才能提交")
	}

	// 更新状态为已提交
	declaration.Status = model.DeclarationStatusSubmitted
	declaration.UpdatedAt = time.Now()

	if err := s.db.WithContext(ctx).Save(&declaration).Error; err != nil {
		return nil, fmt.Errorf("提交申报失败: %w", err)
	}

	// 预加载关联数据
	if err := s.db.WithContext(ctx).
		Preload("Enterprise").
		Preload("TaxType").
		Where("id = ?", declaration.ID).
		First(&declaration).Error; err != nil {
		return nil, fmt.Errorf("获取提交后的申报详情失败: %w", err)
	}

	return &declaration, nil
}

// generateDeclarationNumber 生成申报编号
func (s *declarationService) generateDeclarationNumber(taxTypeID string, year int, month *int, quarter *int) string {
	var period string
	if month != nil {
		period = fmt.Sprintf("%04d%02d", year, *month)
	} else if quarter != nil {
		period = fmt.Sprintf("%04dQ%d", year, *quarter)
	} else {
		period = fmt.Sprintf("%04d", year)
	}

	timestamp := time.Now().Format("150405")
	return fmt.Sprintf("DECL-%s-%s-%s", taxTypeID[:8], period, timestamp)
}
