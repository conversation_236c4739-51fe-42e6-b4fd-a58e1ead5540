package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

// TestNotificationSenderService 通知发送服务测试
func TestNotificationSenderService(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	service := NewNotificationSenderService(logger)

	t.Run("SendEmailNotification", func(t *testing.T) {
		err := service.SendEmailNotification(
			context.Background(),
			"<EMAIL>",
			"测试邮件",
			"这是一封测试邮件",
		)
		assert.NoError(t, err)
	})

	t.Run("SendSMSNotification", func(t *testing.T) {
		err := service.SendSMSNotification(
			context.Background(),
			"13800138000",
			"这是一条测试短信",
		)
		assert.NoError(t, err)
	})

	t.Run("SendWebhookNotification", func(t *testing.T) {
		// 这个测试会失败，因为URL不存在，但我们可以测试参数验证
		data := map[string]interface{}{
			"message": "测试Webhook",
			"type":    "test",
		}

		err := service.SendWebhookNotification(
			context.Background(),
			"http://localhost:9999/webhook",
			data,
		)
		// 预期会失败，因为URL不存在
		assert.Error(t, err)
	})

	t.Run("ValidateEmailAddress", func(t *testing.T) {
		assert.True(t, service.ValidateEmailAddress("<EMAIL>"))
		assert.False(t, service.ValidateEmailAddress("invalid-email"))
		assert.False(t, service.ValidateEmailAddress(""))
	})

	t.Run("ValidatePhoneNumber", func(t *testing.T) {
		assert.True(t, service.ValidatePhoneNumber("13800138000"))
		assert.False(t, service.ValidatePhoneNumber("1234567890"))
		assert.False(t, service.ValidatePhoneNumber(""))
	})

	t.Run("ValidateWebhookURL", func(t *testing.T) {
		assert.True(t, service.ValidateWebhookURL("http://example.com/webhook"))
		assert.True(t, service.ValidateWebhookURL("https://example.com/webhook"))
		assert.False(t, service.ValidateWebhookURL("invalid-url"))
		assert.False(t, service.ValidateWebhookURL(""))
	})

	t.Run("GetNotificationStatus", func(t *testing.T) {
		status, err := service.GetNotificationStatus("test_notification_001")
		assert.NoError(t, err)
		assert.NotEmpty(t, status)
		assert.Contains(t, []string{"sent", "delivered", "failed", "pending"}, status)
	})

	t.Run("SendBatchNotifications", func(t *testing.T) {
		notifications := []interface{}{
			EmailNotification{
				To:      "<EMAIL>",
				Subject: "测试邮件1",
				Content: "这是第一封测试邮件",
				Type:    "tax_filing",
			},
			SMSNotification{
				Phone:   "13800138001",
				Content: "这是第一条测试短信",
				Type:    "tax_filing",
			},
		}

		err := service.SendBatchNotifications(context.Background(), notifications)
		assert.NoError(t, err)
	})
}
