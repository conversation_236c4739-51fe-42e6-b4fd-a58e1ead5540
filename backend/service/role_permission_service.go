// Package service provides business logic layer for the tax management system.
// This file contains role and permission management service implementations.
package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

// RolePermissionService 角色权限管理服务接口
type RolePermissionService interface {
	// 角色管理
	GetRoles(ctx context.Context, page, pageSize int) (*PaginatedRoles, error)
	GetRolesByContext(ctx context.Context, page, pageSize int, enterpriseID string) (*PaginatedRoles, error)
	GetRoleByID(ctx context.Context, roleID string) (*model.Role, error)
	CreateRole(ctx context.Context, req CreateRoleRequest) (*model.Role, error)
	UpdateRole(ctx context.Context, roleID string, req UpdateRoleRequest) (*model.Role, error)
	DeleteRole(ctx context.Context, roleID string) error

	// 权限管理
	GetPermissions(ctx context.Context) ([]model.Permission, error)
	GetPermissionsByRole(ctx context.Context, roleID string) ([]model.Permission, error)
	AssignPermissionsToRole(ctx context.Context, roleID string, permissionIDs []string) error
	RevokePermissionsFromRole(ctx context.Context, roleID string, permissionIDs []string) error

	// 角色权限查询
	GetRolePermissions(ctx context.Context, roleID string) ([]model.RolePermission, error)
	CheckRolePermission(ctx context.Context, roleID, resource, action string) (bool, error)
}

// rolePermissionService 角色权限管理服务实现
type rolePermissionService struct {
	db *gorm.DB
}

// NewRolePermissionService 创建角色权限管理服务实例
func NewRolePermissionService(db *gorm.DB) RolePermissionService {
	return &rolePermissionService{
		db: db,
	}
}

// PaginatedRoles 分页角色响应
type PaginatedRoles struct {
	Roles []model.Role `json:"roles"`
	Total int64        `json:"total"`
	Page  int          `json:"page"`
	Size  int          `json:"size"`
}

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name        string `json:"name" binding:"required"`
	Code        string `json:"code" binding:"required"`
	Description string `json:"description"`
	IsSystem    bool   `json:"is_system"`
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

// GetRoles 获取角色列表
func (s *rolePermissionService) GetRoles(ctx context.Context, page, pageSize int) (*PaginatedRoles, error) {
	var roles []model.Role
	var total int64

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 查询总数
	if err := s.db.WithContext(ctx).Model(&model.Role{}).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取角色总数失败: %w", err)
	}

	// 查询角色列表
	if err := s.db.WithContext(ctx).
		Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("获取角色列表失败: %w", err)
	}

	return &PaginatedRoles{
		Roles: roles,
		Total: total,
		Page:  page,
		Size:  pageSize,
	}, nil
}

// GetRolesByContext 根据上下文获取角色列表
func (s *rolePermissionService) GetRolesByContext(ctx context.Context, page, pageSize int, enterpriseID string) (*PaginatedRoles, error) {
	offset := (page - 1) * pageSize

	var roles []model.Role
	var total int64

	query := s.db.WithContext(ctx).Model(&model.Role{}).Where("is_active = ?", true)

	if enterpriseID != "" {
		// 企业上下文：只返回业务角色和系统基础角色
		query = query.Where("role_type IN ?", []string{"business", "system"}).
			Where("role_code NOT IN ?", []string{"super_admin"}) // 排除超级管理员角色
	} else {
		// 全局上下文：返回所有角色（仅限系统管理员）
		// 这里不做额外过滤
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取角色总数失败: %w", err)
	}

	// 获取角色列表
	if err := query.Order("role_level ASC, created_at ASC").
		Offset(offset).
		Limit(pageSize).
		Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("获取角色列表失败: %w", err)
	}

	return &PaginatedRoles{
		Roles: roles,
		Total: total,
		Page:  page,
		Size:  pageSize,
	}, nil
}

// GetRoleByID 根据ID获取角色
func (s *rolePermissionService) GetRoleByID(ctx context.Context, roleID string) (*model.Role, error) {
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, "id = ?", roleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("角色不存在")
		}
		return nil, fmt.Errorf("获取角色失败: %w", err)
	}
	return &role, nil
}

// CreateRole 创建角色
func (s *rolePermissionService) CreateRole(ctx context.Context, req CreateRoleRequest) (*model.Role, error) {
	// 检查角色代码是否已存在
	var existing model.Role
	if err := s.db.WithContext(ctx).Where("role_code = ?", req.Code).First(&existing).Error; err == nil {
		return nil, errors.New("角色代码已存在")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("检查角色代码失败: %w", err)
	}

	// 创建角色
	roleType := model.RoleTypeCustom
	if req.IsSystem {
		roleType = model.RoleTypeSystem
	}

	role := &model.Role{
		ID:          util.GenerateID(),
		Name:        req.Name,
		Code:        req.Code,
		Description: req.Description,
		Type:        roleType,
		IsActive:    true,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.db.WithContext(ctx).Create(role).Error; err != nil {
		return nil, fmt.Errorf("创建角色失败: %w", err)
	}

	return role, nil
}

// UpdateRole 更新角色
func (s *rolePermissionService) UpdateRole(ctx context.Context, roleID string, req UpdateRoleRequest) (*model.Role, error) {
	// 检查角色是否存在
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, "id = ?", roleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("角色不存在")
		}
		return nil, fmt.Errorf("获取角色失败: %w", err)
	}

	// 检查是否为系统角色
	if role.Type == model.RoleTypeSystem {
		return nil, errors.New("系统角色不能修改")
	}

	// 更新角色信息
	updates := map[string]interface{}{
		"updated_at": time.Now(),
	}

	if req.Name != "" {
		updates["role_name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}

	if err := s.db.WithContext(ctx).Model(&role).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("更新角色失败: %w", err)
	}

	// 重新查询更新后的角色
	if err := s.db.WithContext(ctx).First(&role, "id = ?", roleID).Error; err != nil {
		return nil, fmt.Errorf("获取更新后的角色失败: %w", err)
	}

	return &role, nil
}

// DeleteRole 删除角色
func (s *rolePermissionService) DeleteRole(ctx context.Context, roleID string) error {
	// 检查角色是否存在
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, "id = ?", roleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return fmt.Errorf("获取角色失败: %w", err)
	}

	// 检查是否为系统角色
	if role.Type == model.RoleTypeSystem {
		return errors.New("系统角色不能删除")
	}

	// 检查是否有用户使用该角色
	var userRoleCount int64
	if err := s.db.WithContext(ctx).Model(&model.UserRole{}).Where("role_id = ?", roleID).Count(&userRoleCount).Error; err != nil {
		return fmt.Errorf("检查角色使用情况失败: %w", err)
	}
	if userRoleCount > 0 {
		return errors.New("该角色正在被用户使用，不能删除")
	}

	// 检查是否有企业用户使用该角色
	var enterpriseUserCount int64
	if err := s.db.WithContext(ctx).Model(&model.EnterpriseUser{}).Where("role_id = ?", roleID).Count(&enterpriseUserCount).Error; err != nil {
		return fmt.Errorf("检查企业用户角色使用情况失败: %w", err)
	}
	if enterpriseUserCount > 0 {
		return errors.New("该角色正在被企业用户使用，不能删除")
	}

	// 在事务中删除角色及其相关数据
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 删除角色权限关联
		if err := tx.Where("role_id = ?", roleID).Delete(&model.RolePermission{}).Error; err != nil {
			return fmt.Errorf("删除角色权限关联失败: %w", err)
		}

		// 删除角色
		if err := tx.Delete(&role).Error; err != nil {
			return fmt.Errorf("删除角色失败: %w", err)
		}

		return nil
	})
}

// GetPermissions 获取所有权限
func (s *rolePermissionService) GetPermissions(ctx context.Context) ([]model.Permission, error) {
	var permissions []model.Permission
	if err := s.db.WithContext(ctx).Order("resource, action").Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("获取权限列表失败: %w", err)
	}
	return permissions, nil
}

// GetPermissionsByRole 获取角色的权限列表
func (s *rolePermissionService) GetPermissionsByRole(ctx context.Context, roleID string) ([]model.Permission, error) {
	var permissions []model.Permission

	if err := s.db.WithContext(ctx).
		Table("permissions p").
		Joins("INNER JOIN role_permissions rp ON p.id = rp.permission_id").
		Where("rp.role_id = ?", roleID).
		Order("p.resource, p.action").
		Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("获取角色权限失败: %w", err)
	}

	return permissions, nil
}

// AssignPermissionsToRole 为角色分配权限
func (s *rolePermissionService) AssignPermissionsToRole(ctx context.Context, roleID string, permissionIDs []string) error {
	// 检查角色是否存在
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, "id = ?", roleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return fmt.Errorf("获取角色失败: %w", err)
	}

	// 检查权限是否存在
	var existingPermissions []model.Permission
	if err := s.db.WithContext(ctx).Where("id IN ?", permissionIDs).Find(&existingPermissions).Error; err != nil {
		return fmt.Errorf("检查权限失败: %w", err)
	}
	if len(existingPermissions) != len(permissionIDs) {
		return errors.New("部分权限不存在")
	}

	// 在事务中处理权限分配
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 删除现有的角色权限关联
		if err := tx.Where("role_id = ?", roleID).Delete(&model.RolePermission{}).Error; err != nil {
			return fmt.Errorf("删除现有角色权限关联失败: %w", err)
		}

		// 创建新的角色权限关联
		for _, permissionID := range permissionIDs {
			rolePermission := &model.RolePermission{
				ID:           util.GenerateID(),
				RoleID:       roleID,
				PermissionID: permissionID,
				CreatedAt:    time.Now(),
			}

			if err := tx.Create(rolePermission).Error; err != nil {
				return fmt.Errorf("创建角色权限关联失败: %w", err)
			}
		}

		return nil
	})
}

// RevokePermissionsFromRole 撤销角色的权限
func (s *rolePermissionService) RevokePermissionsFromRole(ctx context.Context, roleID string, permissionIDs []string) error {
	// 检查角色是否存在
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, "id = ?", roleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return fmt.Errorf("获取角色失败: %w", err)
	}

	// 删除指定的角色权限关联
	if err := s.db.WithContext(ctx).
		Where("role_id = ? AND permission_id IN ?", roleID, permissionIDs).
		Delete(&model.RolePermission{}).Error; err != nil {
		return fmt.Errorf("撤销角色权限失败: %w", err)
	}

	return nil
}

// GetRolePermissions 获取角色权限关联
func (s *rolePermissionService) GetRolePermissions(ctx context.Context, roleID string) ([]model.RolePermission, error) {
	var rolePermissions []model.RolePermission

	if err := s.db.WithContext(ctx).
		Preload("Permission").
		Where("role_id = ?", roleID).
		Find(&rolePermissions).Error; err != nil {
		return nil, fmt.Errorf("获取角色权限关联失败: %w", err)
	}

	return rolePermissions, nil
}

// CheckRolePermission 检查角色是否拥有特定权限
func (s *rolePermissionService) CheckRolePermission(ctx context.Context, roleID, resource, action string) (bool, error) {
	var count int64

	if err := s.db.WithContext(ctx).
		Table("role_permissions rp").
		Joins("INNER JOIN permissions p ON rp.permission_id = p.id").
		Where("rp.role_id = ? AND p.resource = ? AND p.action = ?", roleID, resource, action).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("检查角色权限失败: %w", err)
	}

	return count > 0, nil
}
