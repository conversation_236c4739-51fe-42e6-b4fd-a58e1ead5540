package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"

	"backend/model"
)

// RedisService Redis服务
type RedisService struct {
	client *redis.Client
	logger *zap.Logger
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Password string `json:"password"`
	DB       int    `json:"db"`
	PoolSize int    `json:"pool_size"`
}

// NewRedisService 创建Redis服务
func NewRedisService(config *RedisConfig, logger *zap.Logger) *RedisService {
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", config.Host, config.Port),
		Password: config.Password,
		DB:       config.DB,
		PoolSize: config.PoolSize,
	})

	return &RedisService{
		client: client,
		logger: logger,
	}
}

// Redis键前缀常量
const (
	SubmissionCachePrefix = "tax_filing:submission:"
	BatchCachePrefix      = "tax_filing:batch:"
	ProvinceCachePrefix   = "tax_filing:province:"
	StatusCachePrefix     = "tax_filing:status:"
	LockPrefix            = "tax_filing:lock:"
	StatsPrefix           = "tax_filing:stats:"
	ConfigPrefix          = "tax_filing:config:"
)

// 缓存过期时间常量
const (
	SubmissionCacheTTL = 30 * time.Minute
	BatchCacheTTL      = 15 * time.Minute
	ProvinceCacheTTL   = 60 * time.Minute
	StatusCacheTTL     = 5 * time.Minute
	LockTTL            = 10 * time.Minute
	StatsCacheTTL      = 10 * time.Minute
	ConfigCacheTTL     = 60 * time.Minute
)

// Ping 测试Redis连接
func (r *RedisService) Ping(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}

// Close 关闭Redis连接
func (r *RedisService) Close() error {
	return r.client.Close()
}

// CacheSubmission 缓存申报记录
func (r *RedisService) CacheSubmission(ctx context.Context, submission *model.TaxFilingSubmission) error {
	key := SubmissionCachePrefix + submission.ID

	data, err := json.Marshal(submission)
	if err != nil {
		r.logger.Error("Failed to marshal submission for cache", zap.Error(err))
		return err
	}

	if err := r.client.Set(ctx, key, data, SubmissionCacheTTL).Err(); err != nil {
		r.logger.Error("Failed to cache submission", zap.Error(err), zap.String("submission_id", submission.ID))
		return err
	}

	r.logger.Debug("Submission cached successfully", zap.String("submission_id", submission.ID))
	return nil
}

// GetCachedSubmission 获取缓存的申报记录
func (r *RedisService) GetCachedSubmission(ctx context.Context, submissionID string) (*model.TaxFilingSubmission, error) {
	key := SubmissionCachePrefix + submissionID

	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		r.logger.Error("Failed to get cached submission", zap.Error(err))
		return nil, err
	}

	var submission model.TaxFilingSubmission
	if err := json.Unmarshal([]byte(data), &submission); err != nil {
		r.logger.Error("Failed to unmarshal cached submission", zap.Error(err))
		return nil, err
	}

	r.logger.Debug("Submission cache hit", zap.String("submission_id", submissionID))
	return &submission, nil
}

// InvalidateSubmissionCache 使申报记录缓存失效
func (r *RedisService) InvalidateSubmissionCache(ctx context.Context, submissionID string) error {
	key := SubmissionCachePrefix + submissionID

	if err := r.client.Del(ctx, key).Err(); err != nil {
		r.logger.Error("Failed to invalidate submission cache", zap.Error(err))
		return err
	}

	r.logger.Debug("Submission cache invalidated", zap.String("submission_id", submissionID))
	return nil
}

// CacheBatch 缓存批次记录
func (r *RedisService) CacheBatch(ctx context.Context, batch *model.TaxFilingBatch) error {
	key := BatchCachePrefix + batch.ID

	data, err := json.Marshal(batch)
	if err != nil {
		r.logger.Error("Failed to marshal batch for cache", zap.Error(err))
		return err
	}

	if err := r.client.Set(ctx, key, data, BatchCacheTTL).Err(); err != nil {
		r.logger.Error("Failed to cache batch", zap.Error(err), zap.String("batch_id", batch.ID))
		return err
	}

	r.logger.Debug("Batch cached successfully", zap.String("batch_id", batch.ID))
	return nil
}

// GetCachedBatch 获取缓存的批次记录
func (r *RedisService) GetCachedBatch(ctx context.Context, batchID string) (*model.TaxFilingBatch, error) {
	key := BatchCachePrefix + batchID

	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		r.logger.Error("Failed to get cached batch", zap.Error(err))
		return nil, err
	}

	var batch model.TaxFilingBatch
	if err := json.Unmarshal([]byte(data), &batch); err != nil {
		r.logger.Error("Failed to unmarshal cached batch", zap.Error(err))
		return nil, err
	}

	r.logger.Debug("Batch cache hit", zap.String("batch_id", batchID))
	return &batch, nil
}

// InvalidateBatchCache 使批次记录缓存失效
func (r *RedisService) InvalidateBatchCache(ctx context.Context, batchID string) error {
	key := BatchCachePrefix + batchID

	if err := r.client.Del(ctx, key).Err(); err != nil {
		r.logger.Error("Failed to invalidate batch cache", zap.Error(err))
		return err
	}

	r.logger.Debug("Batch cache invalidated", zap.String("batch_id", batchID))
	return nil
}

// CacheProvince 缓存省份配置
func (r *RedisService) CacheProvince(ctx context.Context, province *model.TaxFilingProvince) error {
	key := ProvinceCachePrefix + province.Code

	data, err := json.Marshal(province)
	if err != nil {
		r.logger.Error("Failed to marshal province for cache", zap.Error(err))
		return err
	}

	if err := r.client.Set(ctx, key, data, ProvinceCacheTTL).Err(); err != nil {
		r.logger.Error("Failed to cache province", zap.Error(err), zap.String("province_code", province.Code))
		return err
	}

	r.logger.Debug("Province cached successfully", zap.String("province_code", province.Code))
	return nil
}

// GetCachedProvince 获取缓存的省份配置
func (r *RedisService) GetCachedProvince(ctx context.Context, provinceCode string) (*model.TaxFilingProvince, error) {
	key := ProvinceCachePrefix + provinceCode

	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		r.logger.Error("Failed to get cached province", zap.Error(err))
		return nil, err
	}

	var province model.TaxFilingProvince
	if err := json.Unmarshal([]byte(data), &province); err != nil {
		r.logger.Error("Failed to unmarshal cached province", zap.Error(err))
		return nil, err
	}

	r.logger.Debug("Province cache hit", zap.String("province_code", provinceCode))
	return &province, nil
}

// InvalidateProvinceCache 使省份配置缓存失效
func (r *RedisService) InvalidateProvinceCache(ctx context.Context, provinceCode string) error {
	key := ProvinceCachePrefix + provinceCode

	if err := r.client.Del(ctx, key).Err(); err != nil {
		r.logger.Error("Failed to invalidate province cache", zap.Error(err))
		return err
	}

	r.logger.Debug("Province cache invalidated", zap.String("province_code", provinceCode))
	return nil
}

// AcquireLock 获取分布式锁
func (r *RedisService) AcquireLock(ctx context.Context, lockKey string, expiration time.Duration) (bool, error) {
	key := LockPrefix + lockKey

	// 使用SET命令的NX选项实现分布式锁
	result, err := r.client.SetNX(ctx, key, "locked", expiration).Result()
	if err != nil {
		r.logger.Error("Failed to acquire lock", zap.Error(err), zap.String("lock_key", lockKey))
		return false, err
	}

	if result {
		r.logger.Debug("Lock acquired successfully", zap.String("lock_key", lockKey))
	} else {
		r.logger.Debug("Lock already exists", zap.String("lock_key", lockKey))
	}

	return result, nil
}

// ReleaseLock 释放分布式锁
func (r *RedisService) ReleaseLock(ctx context.Context, lockKey string) error {
	key := LockPrefix + lockKey

	if err := r.client.Del(ctx, key).Err(); err != nil {
		r.logger.Error("Failed to release lock", zap.Error(err), zap.String("lock_key", lockKey))
		return err
	}

	r.logger.Debug("Lock released successfully", zap.String("lock_key", lockKey))
	return nil
}

// IncrementCounter 增加计数器
func (r *RedisService) IncrementCounter(ctx context.Context, counterKey string, expiration time.Duration) (int64, error) {
	key := StatsPrefix + counterKey

	// 使用管道操作确保原子性
	pipe := r.client.Pipeline()
	incrCmd := pipe.Incr(ctx, key)
	pipe.Expire(ctx, key, expiration)

	if _, err := pipe.Exec(ctx); err != nil {
		r.logger.Error("Failed to increment counter", zap.Error(err), zap.String("counter_key", counterKey))
		return 0, err
	}

	return incrCmd.Val(), nil
}

// GetCounter 获取计数器值
func (r *RedisService) GetCounter(ctx context.Context, counterKey string) (int64, error) {
	key := StatsPrefix + counterKey

	result, err := r.client.Get(ctx, key).Int64()
	if err != nil {
		if err == redis.Nil {
			return 0, nil // 计数器不存在，返回0
		}
		r.logger.Error("Failed to get counter", zap.Error(err), zap.String("counter_key", counterKey))
		return 0, err
	}

	return result, nil
}

// SetConfig 设置配置
func (r *RedisService) SetConfig(ctx context.Context, configKey string, value interface{}) error {
	key := ConfigPrefix + configKey

	data, err := json.Marshal(value)
	if err != nil {
		r.logger.Error("Failed to marshal config for cache", zap.Error(err))
		return err
	}

	if err := r.client.Set(ctx, key, data, ConfigCacheTTL).Err(); err != nil {
		r.logger.Error("Failed to set config", zap.Error(err), zap.String("config_key", configKey))
		return err
	}

	r.logger.Debug("Config set successfully", zap.String("config_key", configKey))
	return nil
}

// GetConfig 获取配置
func (r *RedisService) GetConfig(ctx context.Context, configKey string, result interface{}) error {
	key := ConfigPrefix + configKey

	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return fmt.Errorf("config not found: %s", configKey)
		}
		r.logger.Error("Failed to get config", zap.Error(err), zap.String("config_key", configKey))
		return err
	}

	if err := json.Unmarshal([]byte(data), result); err != nil {
		r.logger.Error("Failed to unmarshal config", zap.Error(err))
		return err
	}

	r.logger.Debug("Config retrieved successfully", zap.String("config_key", configKey))
	return nil
}

// CacheSubmissionStatus 缓存申报状态
func (r *RedisService) CacheSubmissionStatus(ctx context.Context, submissionID string, status model.TaxFilingSubmissionStatus) error {
	key := StatusCachePrefix + submissionID

	if err := r.client.Set(ctx, key, string(status), StatusCacheTTL).Err(); err != nil {
		r.logger.Error("Failed to cache submission status", zap.Error(err))
		return err
	}

	return nil
}

// GetCachedSubmissionStatus 获取缓存的申报状态
func (r *RedisService) GetCachedSubmissionStatus(ctx context.Context, submissionID string) (model.TaxFilingSubmissionStatus, error) {
	key := StatusCachePrefix + submissionID

	status, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return "", fmt.Errorf("status not cached")
		}
		return "", err
	}

	return model.TaxFilingSubmissionStatus(status), nil
}

// BatchInvalidateCache 批量使缓存失效
func (r *RedisService) BatchInvalidateCache(ctx context.Context, keys []string) error {
	if len(keys) == 0 {
		return nil
	}

	if err := r.client.Del(ctx, keys...).Err(); err != nil {
		r.logger.Error("Failed to batch invalidate cache", zap.Error(err), zap.Strings("keys", keys))
		return err
	}

	r.logger.Debug("Batch cache invalidated", zap.Int("count", len(keys)))
	return nil
}

// GetCacheStatistics 获取缓存统计信息
func (r *RedisService) GetCacheStatistics(ctx context.Context) (*CacheStatistics, error) {
	// 获取Redis信息
	info, err := r.client.Info(ctx, "memory", "stats").Result()
	if err != nil {
		return nil, err
	}

	// 获取键数量
	submissionKeys, _ := r.client.Keys(ctx, SubmissionCachePrefix+"*").Result()
	batchKeys, _ := r.client.Keys(ctx, BatchCachePrefix+"*").Result()
	provinceKeys, _ := r.client.Keys(ctx, ProvinceCachePrefix+"*").Result()

	stats := &CacheStatistics{
		SubmissionCacheCount: len(submissionKeys),
		BatchCacheCount:      len(batchKeys),
		ProvinceCacheCount:   len(provinceKeys),
		RedisInfo:            info,
	}

	return stats, nil
}

// CacheStatistics 缓存统计信息
type CacheStatistics struct {
	SubmissionCacheCount int    `json:"submission_cache_count"`
	BatchCacheCount      int    `json:"batch_cache_count"`
	ProvinceCacheCount   int    `json:"province_cache_count"`
	RedisInfo            string `json:"redis_info"`
}

// ClearAllCache 清空所有缓存
func (r *RedisService) ClearAllCache(ctx context.Context) error {
	// 获取所有税务申报相关的键
	patterns := []string{
		SubmissionCachePrefix + "*",
		BatchCachePrefix + "*",
		ProvinceCachePrefix + "*",
		StatusCachePrefix + "*",
		StatsPrefix + "*",
		ConfigPrefix + "*",
	}

	for _, pattern := range patterns {
		keys, err := r.client.Keys(ctx, pattern).Result()
		if err != nil {
			r.logger.Error("Failed to get keys for pattern", zap.Error(err), zap.String("pattern", pattern))
			continue
		}

		if len(keys) > 0 {
			if err := r.client.Del(ctx, keys...).Err(); err != nil {
				r.logger.Error("Failed to delete keys", zap.Error(err), zap.String("pattern", pattern))
				return err
			}
		}
	}

	r.logger.Info("All tax filing cache cleared")
	return nil
}
