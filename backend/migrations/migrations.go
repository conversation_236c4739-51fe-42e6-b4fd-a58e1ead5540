// Package migrations provides database migration functions for the tax management system.
// It includes table creation, schema updates, and data migration utilities.
package migrations

import (
	"log"

	"gorm.io/gorm"

	"backend/model"
)

// RunMigrations executes all database migrations
func RunMigrations(db *gorm.DB) error {
	log.Println("开始执行数据库迁移...")

	// 迁移用户相关表
	if err := migrateUserTables(db); err != nil {
		return err
	}

	// 迁移企业相关表
	if err := migrateEnterpriseTables(db); err != nil {
		return err
	}

	// 迁移税务申报相关表
	if err := migrateTaxFilingTables(db); err != nil {
		return err
	}

	// 迁移配置相关表
	if err := migrateConfigTables(db); err != nil {
		return err
	}

	// 迁移审计日志表
	if err := migrateAuditTables(db); err != nil {
		return err
	}

	log.Println("数据库迁移完成")
	return nil
}

// migrateUserTables migrates user-related tables
func migrateUserTables(db *gorm.DB) error {
	log.Println("迁移用户相关表...")
	
	return db.AutoMigrate(
		&model.User{},
		&model.Role{},
		&model.Permission{},
		&model.UserRole{},
		&model.RolePermission{},
	)
}

// migrateEnterpriseTables migrates enterprise-related tables
func migrateEnterpriseTables(db *gorm.DB) error {
	log.Println("迁移企业相关表...")
	
	return db.AutoMigrate(
		&model.Enterprise{},
		&model.EnterpriseUser{},
	)
}

// migrateTaxFilingTables migrates tax filing-related tables
func migrateTaxFilingTables(db *gorm.DB) error {
	log.Println("迁移税务申报相关表...")
	
	return db.AutoMigrate(
		&model.TaxFilingSubmission{},
		&model.TaxFilingBatch{},
		&model.TaxFilingProvince{},
		&model.TaxFilingCallback{},
		&model.TaxFilingStatusHistory{},
	)
}

// migrateConfigTables migrates configuration-related tables
func migrateConfigTables(db *gorm.DB) error {
	log.Println("迁移配置相关表...")
	
	return db.AutoMigrate(
		&model.TaxRule{},
		&model.TaxType{},
		&model.TaxConfig{},
		&model.SystemConfig{},
	)
}

// migrateAuditTables migrates audit-related tables
func migrateAuditTables(db *gorm.DB) error {
	log.Println("迁移审计日志表...")
	
	return db.AutoMigrate(
		&model.AuditLog{},
		&model.PermissionLog{},
		&model.OperationLog{},
	)
}

// DropAllTables drops all tables (use with caution)
func DropAllTables(db *gorm.DB) error {
	log.Println("警告：正在删除所有表...")
	
	// 删除表的顺序很重要，需要先删除有外键依赖的表
	tables := []interface{}{
		&model.UserRole{},
		&model.RolePermission{},
		&model.EnterpriseUser{},
		&model.TaxFilingStatusHistory{},
		&model.TaxFilingCallback{},
		&model.TaxFilingSubmission{},
		&model.TaxFilingBatch{},
		&model.TaxFilingProvince{},
		&model.PermissionLog{},
		&model.OperationLog{},
		&model.AuditLog{},
		&model.TaxRule{},
		&model.TaxType{},
		&model.TaxConfig{},
		&model.SystemConfig{},
		&model.Permission{},
		&model.Role{},
		&model.User{},
		&model.Enterprise{},
	}

	for _, table := range tables {
		if err := db.Migrator().DropTable(table); err != nil {
			log.Printf("删除表失败: %v", err)
			// 继续删除其他表，不要因为一个表失败就停止
		}
	}

	log.Println("所有表删除完成")
	return nil
}
