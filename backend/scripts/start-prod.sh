#!/bin/bash

# 生产环境启动脚本
# Production Environment Startup Script

set -e

echo "🚀 启动生产环境..."
echo "Starting production environment..."

# 设置环境变量
export APP_ENV=prod
export GO_ENV=prod

# 检查必需的环境变量
check_env_var() {
    if [ -z "${!1}" ]; then
        echo "❌ 环境变量 $1 未设置"
        echo "Environment variable $1 is not set"
        exit 1
    fi
}

echo "🔍 检查必需的环境变量..."
echo "Checking required environment variables..."

# 检查生产环境必需的环境变量
check_env_var "DB_PASSWORD"
check_env_var "JWT_SECRET"

# 验证JWT密钥长度
if [ ${#JWT_SECRET} -lt 32 ]; then
    echo "❌ JWT_SECRET 长度不足32位，不安全"
    echo "JWT_SECRET is less than 32 characters, not secure"
    exit 1
fi

echo "✅ 环境变量检查通过"
echo "Environment variables check passed"

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go"
    echo "Go is not installed, please install Go first"
    exit 1
fi

# 检查数据库连接
echo "🔍 检查数据库连接..."
echo "Checking database connection..."

# 这里可以添加数据库连接检查逻辑
# mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASSWORD} -e "SELECT 1;" > /dev/null 2>&1
# if [ $? -ne 0 ]; then
#     echo "❌ 数据库连接失败，请检查数据库配置"
#     echo "Database connection failed, please check database configuration"
#     exit 1
# fi

echo "✅ 数据库连接正常"
echo "Database connection is OK"

# 构建应用
echo "🔨 构建生产版本..."
echo "Building production version..."
go build -ldflags="-w -s" -o bin/tax-backend main.go

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    echo "Build failed"
    exit 1
fi

echo "✅ 构建成功"
echo "Build successful"

# 创建必要的目录
mkdir -p logs
mkdir -p storage

# 设置文件权限
chmod +x bin/tax-backend

# 启动应用
echo "🎯 启动生产服务器..."
echo "Starting production server..."
echo "Environment: ${APP_ENV}"
echo "Config file: config.prod.yaml"
echo "Database: ${DB_NAME:-smeasy_tax}"
echo "Port: ${APP_PORT:-8081}"
echo ""

# 启动应用（生产环境）
./bin/tax-backend
