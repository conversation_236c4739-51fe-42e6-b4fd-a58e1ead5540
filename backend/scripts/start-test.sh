#!/bin/bash

# 测试环境启动脚本
# Test Environment Startup Script

set -e

echo "🚀 启动测试环境..."
echo "Starting test environment..."

# 设置环境变量
export APP_ENV=test
export GO_ENV=test

# 加载环境变量
if [ -f .env.test ]; then
    echo "📄 加载测试环境变量..."
    echo "Loading test environment variables..."
    export $(cat .env.test | grep -v '^#' | xargs)
fi

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go"
    echo "Go is not installed, please install Go first"
    exit 1
fi

# 安装依赖
echo "📦 安装Go模块依赖..."
echo "Installing Go module dependencies..."
go mod tidy

# 重置测试数据库
echo "🗄️  重置测试数据库..."
echo "Resetting test database..."

# 这里可以添加测试数据库重置逻辑
# mysql -h${DB_HOST:-localhost} -P${DB_PORT:-3306} -u${DB_USER:-root} -p${DB_PASSWORD} -e "DROP DATABASE IF EXISTS ${DB_NAME:-smeasy_tax_test};"
# mysql -h${DB_HOST:-localhost} -P${DB_PORT:-3306} -u${DB_USER:-root} -p${DB_PASSWORD} -e "CREATE DATABASE ${DB_NAME:-smeasy_tax_test};"

# 运行数据库迁移
echo "🗄️  运行数据库迁移..."
echo "Running database migrations..."
# go run cmd/migrate/main.go

# 导入测试数据
echo "📊 导入测试数据..."
echo "Importing test data..."
# go run cmd/seed/main.go

# 启动应用
echo "🎯 启动测试服务器..."
echo "Starting test server..."
echo "Environment: ${APP_ENV}"
echo "Config file: config.test.yaml"
echo "Database: ${DB_NAME:-smeasy_tax_test}"
echo "Port: ${APP_PORT:-8081}"
echo ""

# 启动应用
go run main.go
