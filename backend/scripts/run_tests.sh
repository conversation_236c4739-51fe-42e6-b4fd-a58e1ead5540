#!/bin/bash
# Test execution script for the tax management system
# This script runs all tests with coverage reporting and validation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 税务管理系统测试执行脚本 ===${NC}"

# Set test environment variables
export GO_ENV=test
export GIN_MODE=test

# Create test reports directory
mkdir -p reports

echo -e "${YELLOW}1. 运行代码格式检查...${NC}"
go fmt ./...
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 代码格式检查通过${NC}"
else
    echo -e "${RED}✗ 代码格式检查失败${NC}"
    exit 1
fi

echo -e "${YELLOW}2. 运行导入排序检查...${NC}"
if command -v goimports &> /dev/null; then
    goimports -w .
    echo -e "${GREEN}✓ 导入排序完成${NC}"
else
    echo -e "${YELLOW}⚠ goimports 未安装，跳过导入排序${NC}"
fi

echo -e "${YELLOW}3. 运行静态代码分析...${NC}"
if command -v golangci-lint &> /dev/null; then
    golangci-lint run
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 静态代码分析通过${NC}"
    else
        echo -e "${RED}✗ 静态代码分析发现问题${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠ golangci-lint 未安装，跳过静态分析${NC}"
fi

echo -e "${YELLOW}4. 运行单元测试...${NC}"
go test -v -race -coverprofile=reports/coverage.out ./...
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 单元测试通过${NC}"
else
    echo -e "${RED}✗ 单元测试失败${NC}"
    exit 1
fi

echo -e "${YELLOW}5. 生成测试覆盖率报告...${NC}"
go tool cover -func=reports/coverage.out > reports/coverage.txt
go tool cover -html=reports/coverage.out -o reports/coverage.html

# Display coverage summary
echo -e "${GREEN}测试覆盖率摘要:${NC}"
tail -1 reports/coverage.txt

echo -e "${YELLOW}6. 运行构建测试...${NC}"
go build -o tax-system ./main_tax_system.go
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 构建测试通过${NC}"
    rm -f tax-system
else
    echo -e "${RED}✗ 构建测试失败${NC}"
    exit 1
fi

echo -e "${GREEN}=== 所有测试完成 ===${NC}"
echo -e "${GREEN}测试报告已生成到 reports/ 目录${NC}"
echo -e "${GREEN}- 覆盖率文本报告: reports/coverage.txt${NC}"
echo -e "${GREEN}- 覆盖率HTML报告: reports/coverage.html${NC}"
