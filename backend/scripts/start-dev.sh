#!/bin/bash

# 开发环境启动脚本
# Development Environment Startup Script

set -e

echo "🚀 启动开发环境..."
echo "Starting development environment..."

# 设置环境变量
export APP_ENV=dev
export GO_ENV=dev

# 检查是否存在 .env 文件
if [ ! -f .env ]; then
    echo "⚠️  .env 文件不存在，从示例文件创建..."
    echo ".env file not found, creating from example..."
    cp .env.example .env
    echo "✅ 已创建 .env 文件，请根据需要修改配置"
    echo "Created .env file, please modify the configuration as needed"
fi

# 加载环境变量
if [ -f .env ]; then
    echo "📄 加载环境变量..."
    echo "Loading environment variables..."
    export $(cat .env | grep -v '^#' | xargs)
fi

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go"
    echo "Go is not installed, please install Go first"
    exit 1
fi

# 检查MySQL连接
echo "🔍 检查数据库连接..."
echo "Checking database connection..."

# 这里可以添加数据库连接检查逻辑
# mysql -h${DB_HOST:-localhost} -P${DB_PORT:-3306} -u${DB_USER:-root} -p${DB_PASSWORD} -e "SELECT 1;" > /dev/null 2>&1
# if [ $? -ne 0 ]; then
#     echo "❌ 数据库连接失败，请检查数据库配置"
#     echo "Database connection failed, please check database configuration"
#     exit 1
# fi

echo "✅ 数据库连接正常"
echo "Database connection is OK"

# 安装依赖
echo "📦 安装Go模块依赖..."
echo "Installing Go module dependencies..."
go mod tidy

# 运行数据库迁移（开发环境）
echo "🗄️  运行数据库迁移..."
echo "Running database migrations..."
# 这里可以添加迁移命令
# go run cmd/migrate/main.go

# 启动应用
echo "🎯 启动应用服务器..."
echo "Starting application server..."
echo "Environment: ${APP_ENV}"
echo "Config file: config.dev.yaml"
echo "Database: ${DB_NAME:-smeasy_tax_dev}"
echo "Port: ${APP_PORT:-8081}"
echo ""

# 使用 air 进行热重载（如果安装了）
if command -v air &> /dev/null; then
    echo "🔥 使用 Air 热重载启动..."
    echo "Starting with Air hot reload..."
    air
else
    echo "🏃 直接启动应用..."
    echo "Starting application directly..."
    go run main.go
fi
