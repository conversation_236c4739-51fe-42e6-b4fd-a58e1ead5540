// Package main provides a test script to verify CRUD functionality
// for the newly implemented features in the tax management system.
package main

import (
	"backend/test/common"
	"fmt"
	"io"
	"strings"
	"time"
)

// testTaxRuleCRUD tests tax rule CRUD operations
func testTaxRuleCRUD(client *common.APIClient) []common.TestResult {
	var results []common.TestResult

	// Test 1: Create Tax Rule
	start := time.Now()
	createData := map[string]interface{}{
		"taxTypeId":     "test-tax-type-id",
		"name":          "测试税则",
		"description":   "这是一个测试税则",
		"condition":     "income > 100000",
		"formula":       "income * 0.25",
		"effectiveDate": "2024-01-01",
		"parameters": map[string]interface{}{
			"threshold": 100000,
			"rate":      0.25,
		},
	}

	resp, err := client.MakeRequest("POST", "/api/tax-rules", createData)
	duration := time.Since(start)

	if err != nil {
		results = append(results, common.TestResult{
			TestName: "Create Tax Rule",
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		})
	} else {
		defer resp.Body.Close()
		if resp.StatusCode == 200 || resp.StatusCode == 201 {
			results = append(results, common.TestResult{
				TestName: "Create Tax Rule",
				Success:  true,
				Message:  "Tax rule created successfully",
				Duration: duration,
			})
		} else {
			body, _ := io.ReadAll(resp.Body)
			results = append(results, common.TestResult{
				TestName: "Create Tax Rule",
				Success:  false,
				Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
				Duration: duration,
			})
		}
	}

	// Test 2: Get Tax Rules List
	start = time.Now()
	resp, err = client.MakeRequest("GET", "/api/tax-rules?page=1&pageSize=10", nil)
	duration = time.Since(start)

	if err != nil {
		results = append(results, common.TestResult{
			TestName: "Get Tax Rules List",
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		})
	} else {
		defer resp.Body.Close()
		if resp.StatusCode == 200 {
			results = append(results, common.TestResult{
				TestName: "Get Tax Rules List",
				Success:  true,
				Message:  "Tax rules list retrieved successfully",
				Duration: duration,
			})
		} else {
			body, _ := io.ReadAll(resp.Body)
			results = append(results, common.TestResult{
				TestName: "Get Tax Rules List",
				Success:  false,
				Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
				Duration: duration,
			})
		}
	}

	return results
}

// testNotificationCRUD tests notification CRUD operations
func testNotificationCRUD(client *common.APIClient) []common.TestResult {
	var results []common.TestResult

	// Test 1: Create Notification
	start := time.Now()
	createData := map[string]interface{}{
		"recipientId":       "test-user-id",
		"type":              "system",
		"title":             "测试通知",
		"content":           "这是一个测试通知内容",
		"relatedEntityType": "tax_rule",
		"relatedEntityId":   "test-tax-rule-id",
	}

	resp, err := client.MakeRequest("POST", "/api/notifications", createData)
	duration := time.Since(start)

	if err != nil {
		results = append(results, common.TestResult{
			TestName: "Create Notification",
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		})
	} else {
		defer resp.Body.Close()
		if resp.StatusCode == 200 || resp.StatusCode == 201 {
			results = append(results, common.TestResult{
				TestName: "Create Notification",
				Success:  true,
				Message:  "Notification created successfully",
				Duration: duration,
			})
		} else {
			body, _ := io.ReadAll(resp.Body)
			results = append(results, common.TestResult{
				TestName: "Create Notification",
				Success:  false,
				Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
				Duration: duration,
			})
		}
	}

	// Test 2: Get Notifications List
	start = time.Now()
	resp, err = client.MakeRequest("GET", "/api/notifications?page=1&pageSize=10", nil)
	duration = time.Since(start)

	if err != nil {
		results = append(results, common.TestResult{
			TestName: "Get Notifications List",
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		})
	} else {
		defer resp.Body.Close()
		if resp.StatusCode == 200 {
			results = append(results, common.TestResult{
				TestName: "Get Notifications List",
				Success:  true,
				Message:  "Notifications list retrieved successfully",
				Duration: duration,
			})
		} else {
			body, _ := io.ReadAll(resp.Body)
			results = append(results, common.TestResult{
				TestName: "Get Notifications List",
				Success:  false,
				Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
				Duration: duration,
			})
		}
	}

	return results
}

// testAuditLogCRUD tests audit log CRUD operations
func testAuditLogCRUD(client *common.APIClient) []common.TestResult {
	var results []common.TestResult

	// Test 1: Create Audit Log
	start := time.Now()
	createData := map[string]interface{}{
		"userId":       "test-user-id",
		"userName":     "测试用户",
		"action":       "create",
		"resourceType": "tax_rule",
		"resourceId":   "test-tax-rule-id",
		"description":  "创建税则测试",
		"ipAddress":    "127.0.0.1",
		"userAgent":    "test-agent",
		"status":       "success",
		"module":       "tax_management",
		"riskLevel":    "low",
	}

	resp, err := client.MakeRequest("POST", "/api/audit-logs", createData)
	duration := time.Since(start)

	if err != nil {
		results = append(results, common.TestResult{
			TestName: "Create Audit Log",
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		})
	} else {
		defer resp.Body.Close()
		if resp.StatusCode == 200 || resp.StatusCode == 201 {
			results = append(results, common.TestResult{
				TestName: "Create Audit Log",
				Success:  true,
				Message:  "Audit log created successfully",
				Duration: duration,
			})
		} else {
			body, _ := io.ReadAll(resp.Body)
			results = append(results, common.TestResult{
				TestName: "Create Audit Log",
				Success:  false,
				Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
				Duration: duration,
			})
		}
	}

	// Test 2: Get Audit Logs List
	start = time.Now()
	resp, err = client.MakeRequest("GET", "/api/audit-logs?page=1&pageSize=10", nil)
	duration = time.Since(start)

	if err != nil {
		results = append(results, common.TestResult{
			TestName: "Get Audit Logs List",
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		})
	} else {
		defer resp.Body.Close()
		if resp.StatusCode == 200 {
			results = append(results, common.TestResult{
				TestName: "Get Audit Logs List",
				Success:  true,
				Message:  "Audit logs list retrieved successfully",
				Duration: duration,
			})
		} else {
			body, _ := io.ReadAll(resp.Body)
			results = append(results, common.TestResult{
				TestName: "Get Audit Logs List",
				Success:  false,
				Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
				Duration: duration,
			})
		}
	}

	return results
}

// printResults prints test results in a formatted way
func printResults(results []common.TestResult) {
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("CRUD 功能测试结果")
	fmt.Println(strings.Repeat("=", 80))

	successCount := 0
	totalCount := len(results)

	for _, result := range results {
		status := "❌ FAILED"
		if result.Success {
			status = "✅ PASSED"
			successCount++
		}

		fmt.Printf("%-30s %s (%.2fms)\n", result.TestName, status, float64(result.Duration.Nanoseconds())/1000000)
		if !result.Success {
			fmt.Printf("   Error: %s\n", result.Message)
		}
	}

	fmt.Println(strings.Repeat("-", 80))
	fmt.Printf("总计: %d/%d 测试通过 (%.1f%%)\n", successCount, totalCount, float64(successCount)/float64(totalCount)*100)
	fmt.Println(strings.Repeat("=", 80))
}

func main() {
	fmt.Println("开始测试税务管理系统 CRUD 功能...")

	client := common.NewAPIClient(common.TestBaseURL, common.TestToken)
	var allResults []common.TestResult

	// Test Tax Rule CRUD
	fmt.Println("\n测试税则管理 CRUD 功能...")
	taxRuleResults := testTaxRuleCRUD(client)
	allResults = append(allResults, taxRuleResults...)

	// Test Notification CRUD
	fmt.Println("\n测试通知管理 CRUD 功能...")
	notificationResults := testNotificationCRUD(client)
	allResults = append(allResults, notificationResults...)

	// Test Audit Log CRUD
	fmt.Println("\n测试审计日志 CRUD 功能...")
	auditLogResults := testAuditLogCRUD(client)
	allResults = append(allResults, auditLogResults...)

	// Print final results
	printResults(allResults)
}
