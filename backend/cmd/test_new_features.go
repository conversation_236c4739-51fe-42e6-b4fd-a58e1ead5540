// Package main provides a test script to verify the newly implemented CRUD features
// for tax policy management and report management in the tax management system.
package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

const (
	baseURL   = "http://localhost:8081"
	testToken = "test-token" // 在实际测试中需要获取真实的 JWT token
)

// TestResult represents the result of a test
type TestResult struct {
	TestName string
	Success  bool
	Message  string
	Duration time.Duration
}

// APIClient provides methods to interact with the API
type APIClient struct {
	BaseURL string
	Token   string
	Client  *http.Client
}

// NewAPIClient creates a new API client
func NewAPIClient(baseURL, token string) *APIClient {
	return &APIClient{
		BaseURL: baseURL,
		Token:   token,
		Client:  &http.Client{Timeout: 30 * time.Second},
	}
}

// makeRequest makes an HTTP request to the API
func (c *APIClient) makeRequest(method, endpoint string, body interface{}) (*http.Response, error) {
	var reqBody io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		reqBody = bytes.NewBuffer(jsonBody)
	}

	req, err := http.NewRequest(method, c.BaseURL+endpoint, reqBody)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	if c.Token != "" {
		req.Header.Set("Authorization", "Bearer "+c.Token)
	}

	return c.Client.Do(req)
}

// testTaxPolicyAPI tests tax policy CRUD operations
func (c *APIClient) testTaxPolicyAPI() []TestResult {
	var results []TestResult

	// Test 1: Get tax policies list
	start := time.Now()
	resp, err := c.makeRequest("GET", "/api/tax-policies", nil)
	duration := time.Since(start)

	if err != nil {
		results = append(results, TestResult{
			TestName: "获取税务政策列表",
			Success:  false,
			Message:  err.Error(),
			Duration: duration,
		})
	} else {
		resp.Body.Close()
		results = append(results, TestResult{
			TestName: "获取税务政策列表",
			Success:  resp.StatusCode == 200,
			Message:  fmt.Sprintf("HTTP %d", resp.StatusCode),
			Duration: duration,
		})
	}

	// Test 2: Create tax policy
	start = time.Now()
	policyData := map[string]interface{}{
		"policy_number":     "TEST-001",
		"title":             "测试税务政策",
		"category":          "tax_policy",
		"issuing_authority": "测试机构",
		"issue_date":        "2024-01-01",
		"effective_date":    "2024-01-01",
		"policy_content":    "这是一个测试税务政策的内容",
		"priority_level":    "normal",
	}

	resp, err = c.makeRequest("POST", "/api/tax-policies", policyData)
	duration = time.Since(start)

	if err != nil {
		results = append(results, TestResult{
			TestName: "创建税务政策",
			Success:  false,
			Message:  err.Error(),
			Duration: duration,
		})
	} else {
		resp.Body.Close()
		results = append(results, TestResult{
			TestName: "创建税务政策",
			Success:  resp.StatusCode == 200,
			Message:  fmt.Sprintf("HTTP %d", resp.StatusCode),
			Duration: duration,
		})
	}

	// Test 3: Get tax policy statistics
	start = time.Now()
	resp, err = c.makeRequest("GET", "/api/tax-policies/stats", nil)
	duration = time.Since(start)

	if err != nil {
		results = append(results, TestResult{
			TestName: "获取税务政策统计",
			Success:  false,
			Message:  err.Error(),
			Duration: duration,
		})
	} else {
		resp.Body.Close()
		results = append(results, TestResult{
			TestName: "获取税务政策统计",
			Success:  resp.StatusCode == 200,
			Message:  fmt.Sprintf("HTTP %d", resp.StatusCode),
			Duration: duration,
		})
	}

	return results
}

// testReportAPI tests report management CRUD operations
func (c *APIClient) testReportAPI() []TestResult {
	var results []TestResult

	// Test 1: Get report templates list
	start := time.Now()
	resp, err := c.makeRequest("GET", "/api/report-templates", nil)
	duration := time.Since(start)

	if err != nil {
		results = append(results, TestResult{
			TestName: "获取报表模板列表",
			Success:  false,
			Message:  err.Error(),
			Duration: duration,
		})
	} else {
		resp.Body.Close()
		results = append(results, TestResult{
			TestName: "获取报表模板列表",
			Success:  resp.StatusCode == 200,
			Message:  fmt.Sprintf("HTTP %d", resp.StatusCode),
			Duration: duration,
		})
	}

	// Test 2: Create report template
	start = time.Now()
	templateData := map[string]interface{}{
		"name":        "测试报表模板",
		"type":        "custom",
		"description": "这是一个测试报表模板",
		"content":     `{"title": "测试报表", "columns": ["列1", "列2"]}`,
	}

	resp, err = c.makeRequest("POST", "/api/report-templates", templateData)
	duration = time.Since(start)

	if err != nil {
		results = append(results, TestResult{
			TestName: "创建报表模板",
			Success:  false,
			Message:  err.Error(),
			Duration: duration,
		})
	} else {
		resp.Body.Close()
		results = append(results, TestResult{
			TestName: "创建报表模板",
			Success:  resp.StatusCode == 200,
			Message:  fmt.Sprintf("HTTP %d", resp.StatusCode),
			Duration: duration,
		})
	}

	// Test 3: Get report types
	start = time.Now()
	resp, err = c.makeRequest("GET", "/api/reports/types", nil)
	duration = time.Since(start)

	if err != nil {
		results = append(results, TestResult{
			TestName: "获取报表类型",
			Success:  false,
			Message:  err.Error(),
			Duration: duration,
		})
	} else {
		resp.Body.Close()
		results = append(results, TestResult{
			TestName: "获取报表类型",
			Success:  resp.StatusCode == 200,
			Message:  fmt.Sprintf("HTTP %d", resp.StatusCode),
			Duration: duration,
		})
	}

	// Test 4: Get report formats
	start = time.Now()
	resp, err = c.makeRequest("GET", "/api/reports/formats", nil)
	duration = time.Since(start)

	if err != nil {
		results = append(results, TestResult{
			TestName: "获取报表格式",
			Success:  false,
			Message:  err.Error(),
			Duration: duration,
		})
	} else {
		resp.Body.Close()
		results = append(results, TestResult{
			TestName: "获取报表格式",
			Success:  resp.StatusCode == 200,
			Message:  fmt.Sprintf("HTTP %d", resp.StatusCode),
			Duration: duration,
		})
	}

	return results
}

// printResults prints test results in a formatted way
func printResults(results []TestResult) {
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("新功能 CRUD 测试结果")
	fmt.Println(strings.Repeat("=", 80))

	successCount := 0
	totalCount := len(results)

	for _, result := range results {
		status := "❌ FAILED"
		if result.Success {
			status = "✅ PASSED"
			successCount++
		}

		fmt.Printf("%-30s %s (%.2fms)\n", result.TestName, status, float64(result.Duration.Nanoseconds())/1000000)
		if !result.Success {
			fmt.Printf("   Error: %s\n", result.Message)
		}
	}

	fmt.Println(strings.Repeat("-", 80))
	fmt.Printf("总计: %d/%d 测试通过 (%.1f%%)\n", successCount, totalCount, float64(successCount)/float64(totalCount)*100)
	fmt.Println(strings.Repeat("=", 80))
}

func main() {
	fmt.Println("开始测试税务管理系统新增 CRUD 功能...")

	client := NewAPIClient(baseURL, testToken)

	var allResults []TestResult

	// 测试税务政策管理功能
	fmt.Println("\n🔍 测试税务政策管理功能...")
	taxPolicyResults := client.testTaxPolicyAPI()
	allResults = append(allResults, taxPolicyResults...)

	// 测试报表管理功能
	fmt.Println("\n📊 测试报表管理功能...")
	reportResults := client.testReportAPI()
	allResults = append(allResults, reportResults...)

	// 打印测试结果
	printResults(allResults)

	fmt.Println("\n💡 注意: 这些测试需要后端服务运行在", baseURL)
	fmt.Println("💡 如果测试失败，请确保:")
	fmt.Println("   1. 后端服务已启动")
	fmt.Println("   2. 数据库连接正常")
	fmt.Println("   3. 认证token有效")
}
