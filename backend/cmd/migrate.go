// Package main provides database migration utility for the tax management system.
// It handles database schema migrations and data initialization.
package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"backend/config"
	"backend/migrations"
)

func main() {
	// 命令行参数
	configFile := flag.String("config", "./config/config.yaml", "配置文件路径")
	flag.Parse()

	// 加载配置
	cfg, err := config.LoadConfig(*configFile)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 连接数据库
	db, err := connectDB(cfg.Database)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 创建迁移管理器
	migrator := migrations.NewMigrator(db)

	// 执行迁移
	if err := migrator.RunMigrations(); err != nil {
		log.Fatalf("执行迁移失败: %v", err)
	}

	fmt.Println("数据库迁移成功完成！")
}

// connectDB 连接到数据库
func connectDB(dbConfig config.DatabaseConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbConfig.User,
		dbConfig.Password,
		dbConfig.Host,
		dbConfig.Port,
		dbConfig.Name,
	)

	// 设置日志级别
	logLevel := logger.Silent
	if os.Getenv("DEBUG") == "true" {
		logLevel = logger.Info
	}

	// 配置GORM日志
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	}

	// 连接数据库
	return gorm.Open(mysql.Open(dsn), gormConfig)
}
