// Package main provides database migration for new CRUD features
package main

import (
	"fmt"
	"log"

	"github.com/shopspring/decimal"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"backend/model"
)

func main() {
	// 数据库连接配置
	dsn := "root:Aa123456@@tcp(localhost:3306)/smeasy_tax?charset=utf8mb4&parseTime=True&loc=Local"

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("开始数据库迁移...")

	// 自动迁移新增的模型
	err = db.AutoMigrate(
		&model.TaxRule{},
		&model.Notification{},
		&model.AuditLog{},
	)

	if err != nil {
		log.Fatal("数据库迁移失败:", err)
	}

	fmt.Println("数据库迁移完成!")

	// 检查表是否创建成功
	fmt.Println("\n检查新创建的表...")

	tables := []string{
		"tax_rules",
		"notifications",
		"audit_logs",
	}

	for _, table := range tables {
		if db.Migrator().HasTable(table) {
			fmt.Printf("✅ 表 %s 创建成功\n", table)
		} else {
			fmt.Printf("❌ 表 %s 创建失败\n", table)
		}
	}

	// 插入测试数据
	fmt.Println("\n插入测试数据...")

	// 创建测试税种（如果不存在）
	var taxType model.TaxType
	result := db.Where("code = ?", "TEST_TAX").First(&taxType)
	if result.Error != nil {
		testTaxType := model.TaxType{
			ID:          "test-tax-type-id",
			Code:        "TEST_TAX",
			Name:        "测试税种",
			Description: "用于测试的税种",
			Category:    "test",
			IsActive:    true,
		}
		if err := db.Create(&testTaxType).Error; err != nil {
			fmt.Printf("❌ 创建测试税种失败: %v\n", err)
		} else {
			fmt.Println("✅ 创建测试税种成功")
		}
	} else {
		fmt.Println("✅ 测试税种已存在")
	}

	// 创建测试用户（如果不存在）
	var user model.User
	result = db.Where("user_name = ?", "test_user").First(&user)
	if result.Error != nil {
		testUser := model.User{
			ID:       "test-user-id",
			UserName: "test_user",
			Email:    "<EMAIL>",
			Phone:    "13800138000",
			IsActive: true,
		}
		if err := db.Create(&testUser).Error; err != nil {
			fmt.Printf("❌ 创建测试用户失败: %v\n", err)
		} else {
			fmt.Println("✅ 创建测试用户成功")
		}
	} else {
		fmt.Println("✅ 测试用户已存在")
	}

	// 创建测试企业（如果不存在）
	var enterprise model.Enterprise
	result = db.Where("unified_social_credit_code = ?", "TEST123456789").First(&enterprise)
	if result.Error != nil {
		testEnterprise := model.Enterprise{
			ID:                      "test-enterprise-id",
			Name:                    "测试企业",
			UnifiedSocialCreditCode: "TEST123456789",
			LegalRepresentative:     "张三",
			RegisteredCapital:       decimal.NewFromInt(1000000),
			BusinessScope:           "软件开发",
			RegisteredAddress:       "北京市朝阳区",
			ContactPhone:            "010-12345678",
			ContactEmail:            "<EMAIL>",
			TaxpayerType:            "general",
			Status:                  "active",
		}
		if err := db.Create(&testEnterprise).Error; err != nil {
			fmt.Printf("❌ 创建测试企业失败: %v\n", err)
		} else {
			fmt.Println("✅ 创建测试企业成功")
		}
	} else {
		fmt.Println("✅ 测试企业已存在")
	}

	fmt.Println("\n数据库迁移和测试数据插入完成!")
	fmt.Println("现在可以启动后端服务进行测试了。")
}
