// Package main provides configuration testing utility for the tax management system.
// It validates configuration loading across different environments.
package main

import (
	"fmt"
	"log"
	"os"

	"backend/config"
)

func main() {
	// 测试不同环境的配置加载
	environments := []string{"dev", "test", "prod"}

	for _, env := range environments {
		fmt.Printf("\n=== 测试 %s 环境配置 ===\n", env)

		// 设置环境变量
		os.Setenv("APP_ENV", env)

		// 为生产环境设置必需的环境变量
		if env == "prod" {
			os.Setenv("JWT_SECRET", "production-super-secret-jwt-key-32-chars")
			os.Setenv("DATABASE_PASSWORD", "prod-password")
		}

		// 加载配置
		cfg, err := config.LoadConfig("")
		if err != nil {
			log.Printf("加载 %s 环境配置失败: %v", env, err)
			continue
		}

		// 输出关键配置信息
		fmt.Printf("应用名称: %s\n", cfg.App.Name)
		fmt.Printf("调试模式: %v\n", cfg.App.Debug)
		fmt.Printf("数据库名: %s\n", cfg.Database.Name)
		fmt.Printf("Redis DB: %d\n", cfg.Redis.DB)
		fmt.Printf("最大连接数: %d\n", cfg.Database.MaxOpenConns)
		fmt.Printf("自动迁移: %v\n", cfg.Database.AutoMigrate)
		fmt.Printf("Token有效期: %d分钟\n", cfg.Auth.AccessTokenDuration)

		// 清理生产环境变量
		if env == "prod" {
			os.Unsetenv("JWT_SECRET")
			os.Unsetenv("DATABASE_PASSWORD")
		}
	}

	// 测试环境变量覆盖
	fmt.Printf("\n=== 测试环境变量覆盖 ===\n")
	os.Setenv("APP_ENV", "dev")
	os.Setenv("DATABASE_NAME", "custom_db_name")
	os.Setenv("DATABASE_MAXOPENCONNS", "50")

	cfg, err := config.LoadConfig("")
	if err != nil {
		log.Printf("加载配置失败: %v", err)
		return
	}

	fmt.Printf("数据库名 (应为 custom_db_name): %s\n", cfg.Database.Name)
	fmt.Printf("最大连接数 (应为 50): %d\n", cfg.Database.MaxOpenConns)

	fmt.Printf("\n✅ 配置测试完成\n")
}
