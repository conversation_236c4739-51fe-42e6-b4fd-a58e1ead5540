// Package middleware provides HTTP middleware functions for the tax management system.
// It includes authentication, CORS, logging, request ID generation, and role-based access control.
package middleware

import (
	"fmt"
	"strings"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"backend/service"
	"backend/util"
)

// Auth returns a middleware function that validates JWT tokens for protected endpoints.
// It extracts the token from the Authorization header, validates it, and sets user context.
func Auth(authService *service.AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过公开端点的认证
		if isPublicEndpoint(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			util.Unauthorized(c, "Authorization header is required")
			c.Abort()
			return
		}

		// 检查头部是否包含Bearer前缀
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			util.Unauthorized(c, "Authorization header must be Bearer token")
			c.Abort()
			return
		}

		// 验证令牌
		token := parts[1]
		claims, err := authService.VerifyAccessToken(c.Request.Context(), token)
		if err != nil {
			util.Unauthorized(c, "Invalid or expired token")
			c.Abort()
			return
		}

		// 设置用户信息到上下文
		c.Set("userID", claims["id"])
		c.Set("userEmail", claims["email"])
		c.Set("userRole", claims["role"])

		c.Next()
	}
}

// CORS returns a middleware function that handles Cross-Origin Resource Sharing (CORS).
// It configures allowed origins, methods, headers, and credentials for cross-origin requests.
func CORS() gin.HandlerFunc {
	return cors.New(cors.Config{
		AllowOrigins: []string{
			"http://localhost:3000",
			"http://localhost:8000",
			"http://localhost:8080",
			"http://localhost:8082",
			"http://127.0.0.1:3000",
			"http://127.0.0.1:8000",
			"http://127.0.0.1:8080",
			"http://127.0.0.1:8082",
		},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"},
		ExposeHeaders:    []string{"Content-Length", "X-Request-ID"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	})
}

// Logger returns a middleware function that logs HTTP request and response details.
// It records method, path, status code, response time, and other request metadata.
func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		c.Next()

		end := time.Now()
		latency := end.Sub(start)
		statusCode := c.Writer.Status()

		fmt.Printf("[%s] %s %s %d %s\n",
			end.Format("2006-01-02 15:04:05"),
			method,
			path,
			statusCode,
			latency,
		)
	}
}

// RequestID 为每个请求添加唯一的请求ID中间件
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}
		c.Set("RequestID", requestID)
		c.Writer.Header().Set("X-Request-ID", requestID)
		c.Next()
	}
}

// isPublicEndpoint 检查路径是否为公开端点
func isPublicEndpoint(path string) bool {
	publicPaths := []string{
		"/auth/login",
		"/auth/register",
		"/auth/refresh",
		"/auth/forgot-password",
		"/auth/reset-password",
		"/health",
		"/metrics",
	}

	for _, publicPath := range publicPaths {
		if strings.HasPrefix(path, publicPath) {
			return true
		}
	}

	return false
}
