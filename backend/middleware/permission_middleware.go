// Package middleware provides HTTP middleware components for the tax management system.
// It includes authentication, authorization, logging, and request processing middleware.
package middleware

import (
	"context"
	"errors"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"backend/model"
	"backend/service"
	"backend/util"
)

// PermissionMiddleware provides role-based access control for HTTP endpoints.
// It validates user permissions and enforces authorization policies.
type PermissionMiddleware struct {
	db                *gorm.DB
	permissionService *service.PermissionService
}

// NewPermissionMiddleware creates a new permission middleware instance with the provided dependencies.
// It returns a configured PermissionMiddleware ready to handle authorization checks.
func NewPermissionMiddleware(db *gorm.DB, permissionService *service.PermissionService) *PermissionMiddleware {
	return &PermissionMiddleware{
		db:                db,
		permissionService: permissionService,
	}
}

// RequirePermission 需要特定权限的中间件
func (m *PermissionMiddleware) RequirePermission(resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("userID")
		if !exists {
			util.Unauthorized(c, "用户未认证")
			c.Abort()
			return
		}

		// 获取企业ID（从路径参数或查询参数中）
		enterpriseID := m.getEnterpriseID(c)

		// 检查权限
		hasPermission, err := m.checkUserPermission(c.Request.Context(), userID.(string), enterpriseID, resource, action)
		if err != nil {
			util.InternalServerError(c, "权限检查失败", err)
			c.Abort()
			return
		}

		if !hasPermission {
			util.Forbidden(c, "没有执行此操作的权限")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireOwnership 需要企业所有权的中间件
func (m *PermissionMiddleware) RequireOwnership() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("userID")
		if !exists {
			util.Unauthorized(c, "用户未认证")
			c.Abort()
			return
		}

		enterpriseID := m.getEnterpriseID(c)
		if enterpriseID == "" {
			util.BadRequest(c, "企业ID不能为空")
			c.Abort()
			return
		}

		// 检查是否为企业所有者
		isOwner, err := m.checkEnterpriseOwnership(c.Request.Context(), userID.(string), enterpriseID)
		if err != nil {
			util.InternalServerError(c, "所有权检查失败", err)
			c.Abort()
			return
		}

		if !isOwner {
			util.Forbidden(c, "只有企业所有者可以执行此操作")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireEnterpriseAccess 需要企业访问权限的中间件
func (m *PermissionMiddleware) RequireEnterpriseAccess() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("userID")
		if !exists {
			util.Unauthorized(c, "用户未认证")
			c.Abort()
			return
		}

		enterpriseID := m.getEnterpriseID(c)
		if enterpriseID == "" {
			util.BadRequest(c, "企业ID不能为空")
			c.Abort()
			return
		}

		// 检查用户是否属于该企业
		hasAccess, err := m.checkEnterpriseAccess(c.Request.Context(), userID.(string), enterpriseID)
		if err != nil {
			util.InternalServerError(c, "企业访问权限检查失败", err)
			c.Abort()
			return
		}

		if !hasAccess {
			util.Forbidden(c, "没有访问此企业的权限")
			c.Abort()
			return
		}

		// 将企业ID存储到上下文中，供后续处理器使用
		c.Set("enterpriseID", enterpriseID)
		c.Next()
	}
}

// RequireRole 需要特定角色的中间件
func (m *PermissionMiddleware) RequireRole(roleCode string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("userID")
		if !exists {
			util.Unauthorized(c, "用户未认证")
			c.Abort()
			return
		}

		enterpriseID := m.getEnterpriseID(c)

		// 检查用户角色
		hasRole, err := m.checkUserRole(c.Request.Context(), userID.(string), enterpriseID, roleCode)
		if err != nil {
			util.InternalServerError(c, "角色检查失败", err)
			c.Abort()
			return
		}

		if !hasRole {
			util.Forbidden(c, "没有执行此操作的角色权限")
			c.Abort()
			return
		}

		c.Next()
	}
}

// checkUserPermission 检查用户权限
func (m *PermissionMiddleware) checkUserPermission(ctx context.Context, userID, enterpriseID, resource, action string) (bool, error) {
	// 首先检查是否为企业所有者（所有者拥有所有权限）
	if enterpriseID != "" {
		isOwner, err := m.checkEnterpriseOwnership(ctx, userID, enterpriseID)
		if err != nil {
			return false, err
		}
		if isOwner {
			return true, nil
		}
	}

	// 构建权限代码
	permissionCode := resource + "." + action

	// 检查角色权限
	hasRolePermission, err := m.checkRolePermission(ctx, userID, enterpriseID, permissionCode)
	if err != nil {
		return false, err
	}
	if hasRolePermission {
		return true, nil
	}

	// 检查用户特殊权限
	hasUserPermission, err := m.checkUserSpecialPermission(ctx, userID, enterpriseID, permissionCode)
	if err != nil {
		return false, err
	}

	return hasUserPermission, nil
}

// checkEnterpriseOwnership 检查企业所有权
func (m *PermissionMiddleware) checkEnterpriseOwnership(ctx context.Context, userID, enterpriseID string) (bool, error) {
	var enterprise model.Enterprise
	err := m.db.WithContext(ctx).Where("id = ? AND owner_id = ?", enterpriseID, userID).First(&enterprise).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// checkEnterpriseAccess 检查企业访问权限
func (m *PermissionMiddleware) checkEnterpriseAccess(ctx context.Context, userID, enterpriseID string) (bool, error) {
	// 检查是否为企业所有者
	isOwner, err := m.checkEnterpriseOwnership(ctx, userID, enterpriseID)
	if err != nil {
		return false, err
	}
	if isOwner {
		return true, nil
	}

	// 检查是否为企业成员
	var enterpriseUser model.EnterpriseUser
	err = m.db.WithContext(ctx).Where("enterprise_id = ? AND user_id = ? AND status = ?",
		enterpriseID, userID, model.EnterpriseUserStatusActive).First(&enterpriseUser).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// checkUserRole 检查用户角色
func (m *PermissionMiddleware) checkUserRole(ctx context.Context, userID, enterpriseID, roleCode string) (bool, error) {
	var count int64
	query := m.db.WithContext(ctx).Model(&model.EnterpriseUser{}).
		Joins("JOIN roles ON enterprise_users.role_id = roles.id").
		Where("enterprise_users.user_id = ? AND roles.code = ? AND enterprise_users.status = ?",
			userID, roleCode, model.EnterpriseUserStatusActive)

	if enterpriseID != "" {
		query = query.Where("enterprise_users.enterprise_id = ?", enterpriseID)
	}

	err := query.Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// checkRolePermission 检查角色权限
func (m *PermissionMiddleware) checkRolePermission(ctx context.Context, userID, enterpriseID, permissionCode string) (bool, error) {
	var count int64
	query := m.db.WithContext(ctx).Model(&model.EnterpriseUser{}).
		Joins("JOIN role_permissions ON enterprise_users.role_id = role_permissions.role_id").
		Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
		Where("enterprise_users.user_id = ? AND permissions.permission_code = ? AND enterprise_users.status = ?",
			userID, permissionCode, model.EnterpriseUserStatusActive)

	if enterpriseID != "" {
		query = query.Where("enterprise_users.enterprise_id = ?", enterpriseID)
	}

	err := query.Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// checkUserSpecialPermission 检查用户特殊权限
func (m *PermissionMiddleware) checkUserSpecialPermission(ctx context.Context, userID, enterpriseID, permissionCode string) (bool, error) {
	var count int64
	query := m.db.WithContext(ctx).Model(&model.UserPermission{}).
		Joins("JOIN permissions ON user_permissions.permission_id = permissions.id").
		Where("user_permissions.user_id = ? AND permissions.permission_code = ? AND user_permissions.is_active = ?",
			userID, permissionCode, true).
		Where("(user_permissions.expires_at IS NULL OR user_permissions.expires_at > NOW())")

	if enterpriseID != "" {
		query = query.Where("user_permissions.enterprise_id = ?", enterpriseID)
	}

	err := query.Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// getEnterpriseID 从请求中获取企业ID
func (m *PermissionMiddleware) getEnterpriseID(c *gin.Context) string {
	// 优先从路径参数获取
	if enterpriseID := c.Param("enterpriseId"); enterpriseID != "" {
		return enterpriseID
	}
	if enterpriseID := c.Param("id"); enterpriseID != "" && strings.Contains(c.Request.URL.Path, "enterprises") {
		return enterpriseID
	}

	// 从查询参数获取
	if enterpriseID := c.Query("enterprise_id"); enterpriseID != "" {
		return enterpriseID
	}

	// 从请求头获取
	if enterpriseID := c.GetHeader("X-Enterprise-ID"); enterpriseID != "" {
		return enterpriseID
	}

	return ""
}
