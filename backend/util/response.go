// Package util provides utility functions and common structures for the tax management system.
// It includes response formatting, error handling, validation helpers, and other shared utilities.
package util

import (
	"encoding/json"
	"errors"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// APIResponse represents the unified API response structure
type APIResponse struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Error     string      `json:"error,omitempty"`
	Timestamp int64       `json:"timestamp"`
	RequestID string      `json:"requestId,omitempty"`
}

// Success returns a successful response with optional custom message
func Success(c *gin.Context, data interface{}, message ...string) {
	msg := "Operation successful"
	if len(message) > 0 {
		msg = message[0]
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:      http.StatusOK,
		Message:   msg,
		Data:      data,
		Timestamp: time.Now().Unix(),
		RequestID: getRequestID(c),
	})
}

// ErrorResponse returns an error response with specified status code and message
func ErrorResponse(c *gin.Context, code int, message string, err ...error) {
	response := APIResponse{
		Code:      code,
		Message:   message,
		Timestamp: time.Now().Unix(),
		RequestID: getRequestID(c),
	}

	if len(err) > 0 && err[0] != nil {
		response.Error = err[0].Error()
	}

	c.JSON(code, response)
}

// BadRequest returns a 400 Bad Request error response
func BadRequest(c *gin.Context, message string, err ...error) {
	ErrorResponse(c, http.StatusBadRequest, message, err...)
}

// Unauthorized returns a 401 Unauthorized error response
func Unauthorized(c *gin.Context, message string, err ...error) {
	ErrorResponse(c, http.StatusUnauthorized, message, err...)
}

// Forbidden returns a 403 Forbidden error response
func Forbidden(c *gin.Context, message string, err ...error) {
	ErrorResponse(c, http.StatusForbidden, message, err...)
}

// NotFound returns a 404 Not Found error response
func NotFound(c *gin.Context, message string, err ...error) {
	ErrorResponse(c, http.StatusNotFound, message, err...)
}

// InternalServerError returns a 500 Internal Server Error response
func InternalServerError(c *gin.Context, message string, err ...error) {
	ErrorResponse(c, http.StatusInternalServerError, message, err...)
}

// SuccessResponse returns a success response with data
func SuccessResponse(c *gin.Context, message string, data interface{}) {
	response := APIResponse{
		Code:      http.StatusOK,
		Message:   message,
		Data:      data,
		Timestamp: time.Now().Unix(),
		RequestID: getRequestID(c),
	}
	c.JSON(http.StatusOK, response)
}

// PaginatedResponse represents a paginated response structure
type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// HandleAppError 处理自定义应用错误，根据错误类型返回相应的HTTP状态码
func HandleAppError(c *gin.Context, err error) {
	var appErr *AppError
	if errors.As(err, &appErr) {
		switch appErr.Type {
		case ErrTypeNotFound:
			NotFound(c, appErr.Message, appErr.Err)
		case ErrTypeInvalidInput, ErrTypeValidation:
			BadRequest(c, appErr.Message, appErr.Err)
		case ErrTypeUnauthorized:
			Unauthorized(c, appErr.Message, appErr.Err)
		case ErrTypeForbidden:
			Forbidden(c, appErr.Message, appErr.Err)
		case ErrTypeDuplicate, ErrTypeConflict:
			ErrorResponse(c, http.StatusConflict, appErr.Message, appErr.Err)
		default:
			InternalServerError(c, "Internal server error", appErr.Err)
		}
		return
	}

	// 处理标准错误
	InternalServerError(c, "Internal server error", err)
}

// SuccessWithPagination 分页成功响应
func SuccessWithPagination(c *gin.Context, items interface{}, total int64, page, pageSize int, message ...string) {
	msg := "获取数据成功"
	if len(message) > 0 {
		msg = message[0]
	}

	// 计算总页数
	pages := int((total + int64(pageSize) - 1) / int64(pageSize))

	paginatedData := map[string]interface{}{
		"items":    items,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
		"pages":    pages,
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:      http.StatusOK,
		Message:   msg,
		Data:      paginatedData,
		Timestamp: time.Now().Unix(),
		RequestID: getRequestID(c),
	})
}

// SuccessWithList 列表成功响应（非分页）
func SuccessWithList(c *gin.Context, items interface{}, message ...string) {
	msg := "获取列表成功"
	if len(message) > 0 {
		msg = message[0]
	}

	listData := map[string]interface{}{
		"items": items,
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:      http.StatusOK,
		Message:   msg,
		Data:      listData,
		Timestamp: time.Now().Unix(),
		RequestID: getRequestID(c),
	})
}

// getRequestID 获取请求ID
func getRequestID(c *gin.Context) string {
	if requestID := c.GetHeader("X-Request-ID"); requestID != "" {
		return requestID
	}
	if requestID := c.GetString("requestID"); requestID != "" {
		return requestID
	}
	return GenerateID() // 如果没有请求ID，生成一个新的
}

// ToJSONString converts an interface{} to JSON string
func ToJSONString(data interface{}) (string, error) {
	bytes, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// ParseTime 解析时间字符串
func ParseTime(timeStr string) (time.Time, error) {
	// 支持多种时间格式
	formats := []string{
		"2006-01-02T15:04:05Z07:00", // RFC3339
		"2006-01-02 15:04:05",       // 常用格式
		"2006-01-02T15:04:05",       // ISO格式
		"2006-01-02",                // 日期格式
	}

	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return t, nil
		}
	}

	return time.Time{}, errors.New("unable to parse time: " + timeStr)
}
