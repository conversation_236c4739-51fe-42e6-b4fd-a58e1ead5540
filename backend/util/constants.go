// Package util provides utility functions and common structures for the tax management system.
// It includes constants, response formatting, error handling, validation helpers, and other shared utilities.
package util

// HTTP Status Messages
const (
	StatusOK                  = "success"
	StatusError               = "error"
	StatusUnauthorized        = "unauthorized"
	StatusForbidden           = "forbidden"
	StatusNotFound            = "not_found"
	StatusInternalServerError = "internal_server_error"
	StatusBadRequest          = "bad_request"
)

// Common Response Messages
const (
	MsgSuccess              = "操作成功"
	MsgError                = "操作失败"
	MsgUnauthorized         = "用户未认证"
	MsgForbidden            = "权限不足"
	MsgNotFound             = "资源不存在"
	MsgInternalServerError  = "服务器内部错误"
	MsgBadRequest           = "请求参数错误"
	MsgInvalidInput         = "输入参数无效"
	MsgRequiredFieldMissing = "必填字段缺失"
)

// Authentication Messages
const (
	MsgLoginSuccess         = "登录成功"
	MsgLoginFailed          = "登录失败"
	MsgLogoutSuccess        = "退出成功"
	MsgRegisterSuccess      = "注册成功"
	MsgRegisterFailed       = "注册失败"
	MsgPasswordChanged      = "密码修改成功"
	MsgPasswordChangeFailed = "密码修改失败"
	MsgTokenExpired         = "令牌已过期"
	MsgTokenInvalid         = "令牌无效"
	MsgUserNotFound         = "用户不存在"
	MsgUserExists           = "用户已存在"
	MsgUserInactive         = "用户账户已禁用"
	MsgInvalidCredentials   = "用户名或密码错误"
)

// Enterprise Messages
const (
	MsgEnterpriseCreated      = "企业创建成功"
	MsgEnterpriseCreateFailed = "企业创建失败"
	MsgEnterpriseUpdated      = "企业信息更新成功"
	MsgEnterpriseUpdateFailed = "企业信息更新失败"
	MsgEnterpriseDeleted      = "企业删除成功"
	MsgEnterpriseDeleteFailed = "企业删除失败"
	MsgEnterpriseNotFound     = "企业不存在"
	MsgEnterpriseExists       = "企业已存在"
)

// User Management Messages
const (
	MsgUserInvited       = "用户邀请成功"
	MsgUserInviteFailed  = "用户邀请失败"
	MsgUserRemoved       = "用户移除成功"
	MsgUserRemoveFailed  = "用户移除失败"
	MsgRoleAssigned      = "角色分配成功"
	MsgRoleAssignFailed  = "角色分配失败"
	MsgPermissionGranted = "权限授予成功"
	MsgPermissionRevoked = "权限撤销成功"
	MsgPermissionDenied  = "权限不足"
)

// File Upload Messages
const (
	MsgFileUploaded       = "文件上传成功"
	MsgFileUploadFailed   = "文件上传失败"
	MsgFileDeleted        = "文件删除成功"
	MsgFileDeleteFailed   = "文件删除失败"
	MsgFileNotFound       = "文件不存在"
	MsgFileTooLarge       = "文件过大"
	MsgFileTypeNotAllowed = "文件类型不支持"
)

// Validation Constants
const (
	MinPasswordLength = 8
	MaxPasswordLength = 128
	MinUsernameLength = 2
	MaxUsernameLength = 50
	MaxEmailLength    = 255
	PhoneNumberLength = 11
)

// Pagination Constants
const (
	DefaultPageSize = 10
	MaxPageSize     = 100
	MinPageSize     = 1
	DefaultPage     = 1
)

// File Upload Constants
const (
	MaxFileSize         = 10 * 1024 * 1024 // 10MB
	MaxImageSize        = 5 * 1024 * 1024  // 5MB
	MaxDocumentSize     = 20 * 1024 * 1024 // 20MB
	UploadPathImages    = "uploads/images"
	UploadPathDocuments = "uploads/documents"
	UploadPathInvoices  = "uploads/invoices"
)

// Allowed File Types
var (
	AllowedImageTypes    = []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"}
	AllowedDocumentTypes = []string{".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt"}
	AllowedInvoiceTypes  = []string{".pdf", ".xml", ".ofd"}
)

// Cache Keys
const (
	CacheKeyUserSession    = "user:session:"
	CacheKeyUserProfile    = "user:profile:"
	CacheKeyEnterprise     = "enterprise:"
	CacheKeyPermissions    = "permissions:"
	CacheKeyRoles          = "roles:"
	CacheKeyTaxTypes       = "tax_types"
	CacheKeySystemSettings = "system:settings"
)

// Cache Expiration Times (in seconds)
const (
	CacheExpirationShort  = 300   // 5 minutes
	CacheExpirationMedium = 1800  // 30 minutes
	CacheExpirationLong   = 3600  // 1 hour
	CacheExpirationDay    = 86400 // 24 hours
)

// Database Constants
const (
	DefaultDatabaseTimeout = 30 // seconds
	MaxDatabaseConnections = 100
	MaxIdleConnections     = 10
	ConnectionMaxLifetime  = 3600 // seconds
)

// JWT Constants
const (
	JWTIssuer            = "tax-system"
	DefaultTokenExpiry   = 60 // minutes
	DefaultRefreshExpiry = 30 // days
)

// System Constants
const (
	SystemName      = "智能税务管理系统"
	SystemVersion   = "1.0.0"
	DefaultTimezone = "Asia/Shanghai"
	DefaultLanguage = "zh-CN"
	DefaultCurrency = "CNY"
)

// Enterprise Status Constants
const (
	EnterpriseStatusActive    = "active"
	EnterpriseStatusInactive  = "inactive"
	EnterpriseStatusSuspended = "suspended"
	EnterpriseStatusCancelled = "cancelled"
)

// User Status Constants
const (
	UserStatusActive   = true
	UserStatusInactive = false
)

// Role Types
const (
	RoleTypeSystem     = "system"
	RoleTypeEnterprise = "enterprise"
	RoleTypeCustom     = "custom"
)

// Permission Actions
const (
	PermissionActionCreate = "create"
	PermissionActionRead   = "read"
	PermissionActionUpdate = "update"
	PermissionActionDelete = "delete"
	PermissionActionManage = "manage"
)

// Permission Resources
const (
	PermissionResourceUser        = "user"
	PermissionResourceEnterprise  = "enterprise"
	PermissionResourceInvoice     = "invoice"
	PermissionResourceDeclaration = "declaration"
	PermissionResourceReport      = "report"
	PermissionResourceSystem      = "system"
)

// Taxpayer Types
const (
	TaxpayerTypeGeneral = "general"
	TaxpayerTypeSmall   = "small"
)

// Invoice Types
const (
	InvoiceTypeSpecial    = "special"
	InvoiceTypeOrdinary   = "ordinary"
	InvoiceTypeElectronic = "electronic"
	InvoiceTypeRoll       = "roll"
)

// Invoice Status
const (
	InvoiceStatusDraft      = "draft"
	InvoiceStatusIssued     = "issued"
	InvoiceStatusCancelled  = "cancelled"
	InvoiceStatusInvalid    = "invalid"
	InvoiceStatusRedFlushed = "red_flushed"
)

// Declaration Status
const (
	DeclarationStatusDraft     = "draft"
	DeclarationStatusSubmitted = "submitted"
	DeclarationStatusApproved  = "approved"
	DeclarationStatusRejected  = "rejected"
)

// Tax Filing Frequency
const (
	FilingFrequencyMonthly   = "monthly"
	FilingFrequencyQuarterly = "quarterly"
	FilingFrequencyAnnually  = "annually"
	FilingFrequencyOther     = "other"
)

// Common Tax Codes
const (
	TaxCodeVAT = "VAT" // 增值税
	TaxCodeEIT = "EIT" // 企业所得税
	TaxCodeIIT = "IIT" // 个人所得税
	TaxCodeCIT = "CIT" // 消费税
	TaxCodeBT  = "BT"  // 营业税
	TaxCodeCT  = "CT"  // 城建税
	TaxCodeEET = "EET" // 教育费附加
	TaxCodeLET = "LET" // 地方教育费附加
	TaxCodePIT = "PIT" // 印花税
	TaxCodeVT  = "VT"  // 车辆税
	TaxCodePT  = "PT"  // 房产税
	TaxCodeLUT = "LUT" // 土地使用税
)
