// Package util provides utility functions and common structures for the tax management system.
// It includes decimal conversion and calculation utilities.
package util

import (
	"github.com/shopspring/decimal"
)

// DecimalFromFloat converts a float64 to decimal.Decimal
func DecimalFromFloat(f float64) decimal.Decimal {
	return decimal.NewFromFloat(f)
}

// DecimalFromString converts a string to decimal.Decimal
func DecimalFromString(s string) (decimal.Decimal, error) {
	return decimal.NewFromString(s)
}

// DecimalFromInt converts an int64 to decimal.Decimal
func DecimalFromInt(i int64) decimal.Decimal {
	return decimal.NewFromInt(i)
}

// DecimalZero returns a zero decimal value
func DecimalZero() decimal.Decimal {
	return decimal.Zero
}

// DecimalOne returns a one decimal value
func DecimalOne() decimal.Decimal {
	return decimal.NewFromInt(1)
}

// FormatDecimal formats a decimal to string with specified precision
func FormatDecimal(d decimal.Decimal, precision int32) string {
	return d.StringFixed(precision)
}

// FormatCurrency formats a decimal as currency string
func FormatCurrency(d decimal.Decimal) string {
	return "¥" + d.StringFixed(2)
}

// FormatPercentage formats a decimal as percentage string
func FormatPercentage(d decimal.Decimal) string {
	return d.Mul(decimal.NewFromInt(100)).StringFixed(2) + "%"
}

// CalculateTax calculates tax amount from base amount and tax rate
func CalculateTax(amount, taxRate decimal.Decimal) decimal.Decimal {
	return amount.Mul(taxRate)
}

// CalculateTotal calculates total amount including tax
func CalculateTotal(amount, taxAmount decimal.Decimal) decimal.Decimal {
	return amount.Add(taxAmount)
}

// CalculateAmountFromTotal calculates base amount from total and tax rate
func CalculateAmountFromTotal(total, taxRate decimal.Decimal) decimal.Decimal {
	divisor := decimal.NewFromInt(1).Add(taxRate)
	return total.Div(divisor)
}

// RoundToTwoDecimals rounds a decimal to 2 decimal places
func RoundToTwoDecimals(d decimal.Decimal) decimal.Decimal {
	return d.Round(2)
}

// RoundToFourDecimals rounds a decimal to 4 decimal places
func RoundToFourDecimals(d decimal.Decimal) decimal.Decimal {
	return d.Round(4)
}

// IsPositive checks if decimal is positive
func IsPositive(d decimal.Decimal) bool {
	return d.GreaterThan(decimal.Zero)
}

// IsNegative checks if decimal is negative
func IsNegative(d decimal.Decimal) bool {
	return d.LessThan(decimal.Zero)
}

// IsZero checks if decimal is zero
func IsZero(d decimal.Decimal) bool {
	return d.Equal(decimal.Zero)
}

// Max returns the maximum of two decimals
func MaxDecimal(a, b decimal.Decimal) decimal.Decimal {
	if a.GreaterThan(b) {
		return a
	}
	return b
}

// Min returns the minimum of two decimals
func MinDecimal(a, b decimal.Decimal) decimal.Decimal {
	if a.LessThan(b) {
		return a
	}
	return b
}

// SumDecimals calculates the sum of multiple decimals
func SumDecimals(decimals ...decimal.Decimal) decimal.Decimal {
	sum := decimal.Zero
	for _, d := range decimals {
		sum = sum.Add(d)
	}
	return sum
}

// AverageDecimals calculates the average of multiple decimals
func AverageDecimals(decimals ...decimal.Decimal) decimal.Decimal {
	if len(decimals) == 0 {
		return decimal.Zero
	}
	sum := SumDecimals(decimals...)
	return sum.Div(decimal.NewFromInt(int64(len(decimals))))
}
