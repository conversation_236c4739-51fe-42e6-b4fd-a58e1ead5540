# 工具模块

## 概述
工具模块提供系统所需的通用功能和辅助工具，包括加密解密、时间处理、字符串操作等基础功能。这些工具函数被其他模块广泛使用，提高了代码复用性和开发效率。

## 目录结构
```
util/
├── crypto/     # 加密相关
│   ├── hash.go    # 哈希函数
│   └── aes.go     # AES加解密
├── time/       # 时间处理
│   ├── format.go  # 时间格式化
│   └── calc.go    # 时间计算
├── validator/  # 数据验证
│   └── validator.go # 验证器
└── helper/     # 辅助函数
    ├── string.go   # 字符串处理
    └── convert.go  # 类型转换
```

## 核心功能

### 加密工具
1. 密码哈希
   - bcrypt加密
   - 密码验证
   - 盐值管理

2. AES加解密
   - 配置加密
   - 敏感数据保护
   - 密钥管理

### 时间工具
1. 时间格式化
   - 标准格式转换
   - 自定义格式
   - 时区处理

2. 时间计算
   - 时间间隔
   - 工作日计算
   - 节假日处理

### 数据验证
1. 请求验证
   - 参数格式
   - 必填字段
   - 值范围

2. 业务验证
   - 规则定义
   - 自定义验证
   - 错误信息

## 使用示例
```go
// util/crypto/hash.go
package crypto

import "golang.org/x/crypto/bcrypt"

// HashPassword 使用bcrypt对密码进行加密
func HashPassword(password string) string {
    hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
    if err != nil {
        panic(err)
    }
    return string(hashedBytes)
}

// CheckPassword 验证密码是否正确
func CheckPassword(password, hashedPassword string) bool {
    err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
    return err == nil
}

// util/time/format.go
package time

import "time"

// FormatDateTime 格式化时间为标准格式
func FormatDateTime(t time.Time) string {
    return t.Format("2006-01-02 15:04:05")
}

// ParseDateTime 解析标准格式时间字符串
func ParseDateTime(s string) (time.Time, error) {
    return time.Parse("2006-01-02 15:04:05", s)
}

// util/validator/validator.go
package validator

import "github.com/go-playground/validator/v10"

type Validator struct {
    validate *validator.Validate
}

func NewValidator() *Validator {
    return &Validator{
        validate: validator.New(),
    }
}

func (v *Validator) Validate(i interface{}) error {
    return v.validate.Struct(i)
}
```

## 最佳实践
1. 错误处理
   - 统一错误返回
   - 错误信息明确
   - 避免panic

2. 性能优化
   - 避免重复计算
   - 合理使用缓存
   - 控制内存使用

3. 代码组织
   - 功能模块化
   - 接口设计
   - 注释完善

4. 测试覆盖
   - 单元测试
   - 边界测试
   - 性能测试

## 注意事项
1. 安全性
   - 密钥保护
   - 随机数安全
   - 敏感信息处理

2. 兼容性
   - 跨平台支持
   - 版本兼容
   - 编码处理

3. 可维护性
   - 代码规范
   - 文档完善
   - 版本控制