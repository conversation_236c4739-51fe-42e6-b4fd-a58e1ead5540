// Package util provides utility functions and common structures for the tax management system.
// It includes response formatting, error handling, validation helpers, and other shared utilities.
package util

import (
	"errors"
	"fmt"
)

// 错误类型常量
const (
	ErrTypeNotFound     = "NotFound"
	ErrTypeInvalidInput = "InvalidInput"
	ErrTypeUnauthorized = "Unauthorized"
	ErrTypeInternal     = "InternalError"
	ErrTypeDuplicate    = "DuplicateEntry"
	ErrTypeForbidden    = "Forbidden"
	ErrTypeValidation   = "ValidationError"
	ErrTypeConflict     = "Conflict"
)

// AppError 应用错误结构，符合GOLANG_STANDARDS.md规范
type AppError struct {
	Type    string // 错误类型
	Message string // 错误消息
	Err     error  // 原始错误
}

// Error 实现error接口
func (e *AppError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %s: %v", e.Type, e.Message, e.Err)
	}
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// Unwrap 支持errors.Is和errors.As
func (e *AppError) Unwrap() error {
	return e.Err
}

// NewNotFoundError 创建NotFound错误
func NewNotFoundError(entity string, id interface{}, err error) *AppError {
	return &AppError{
		Type:    ErrTypeNotFound,
		Message: fmt.Sprintf("%s with ID %v not found", entity, id),
		Err:     err,
	}
}

// NewInvalidInputError 创建InvalidInput错误
func NewInvalidInputError(message string, err error) *AppError {
	return &AppError{
		Type:    ErrTypeInvalidInput,
		Message: message,
		Err:     err,
	}
}

// NewUnauthorizedError 创建Unauthorized错误
func NewUnauthorizedError(message string, err error) *AppError {
	return &AppError{
		Type:    ErrTypeUnauthorized,
		Message: message,
		Err:     err,
	}
}

// NewInternalError 创建Internal错误
func NewInternalError(message string, err error) *AppError {
	return &AppError{
		Type:    ErrTypeInternal,
		Message: message,
		Err:     err,
	}
}

// NewDuplicateError 创建Duplicate错误
func NewDuplicateError(entity string, field string, value interface{}, err error) *AppError {
	return &AppError{
		Type:    ErrTypeDuplicate,
		Message: fmt.Sprintf("%s with %s %v already exists", entity, field, value),
		Err:     err,
	}
}

// NewForbiddenError 创建Forbidden错误
func NewForbiddenError(message string, err error) *AppError {
	return &AppError{
		Type:    ErrTypeForbidden,
		Message: message,
		Err:     err,
	}
}

// NewValidationError 创建Validation错误
func NewValidationError(message string, err error) *AppError {
	return &AppError{
		Type:    ErrTypeValidation,
		Message: message,
		Err:     err,
	}
}

// NewConflictError 创建Conflict错误
func NewConflictError(message string, err error) *AppError {
	return &AppError{
		Type:    ErrTypeConflict,
		Message: message,
		Err:     err,
	}
}

// Authentication related errors
var (
	ErrInvalidCredentials = errors.New("invalid email or password")
	ErrUserNotFound       = errors.New("user not found")
	ErrUserExists         = errors.New("user already exists")
	ErrUserInactive       = errors.New("user account is inactive")
	ErrInvalidToken       = errors.New("invalid token")
	ErrTokenExpired       = errors.New("token expired")
	ErrUnauthorized       = errors.New("unauthorized")
)

// Enterprise related errors
var (
	ErrEnterpriseNotFound          = errors.New("enterprise not found")
	ErrEnterpriseExists            = errors.New("enterprise already exists")
	ErrInvalidCreditCode           = errors.New("invalid unified social credit code")
	ErrEnterpriseInactive          = errors.New("enterprise is inactive")
	ErrEnterpriseTaxConfigNotFound = errors.New("enterprise tax config not found")
	ErrInvalidDateRange            = errors.New("invalid date range")
)

// Invoice related errors
var (
	ErrInvoiceNotFound         = errors.New("invoice not found")
	ErrInvoiceExists           = errors.New("invoice already exists")
	ErrInvalidInvoiceNumber    = errors.New("invalid invoice number")
	ErrInvoiceProcessingFailed = errors.New("invoice processing failed")
	ErrInvoiceItemNotFound     = errors.New("invoice item not found")
)

// Declaration related errors
var (
	ErrDeclarationNotFound      = errors.New("declaration not found")
	ErrDeclarationExists        = errors.New("declaration already exists")
	ErrDeclarationSubmitted     = errors.New("declaration already submitted")
	ErrInvalidDeclarationPeriod = errors.New("invalid declaration period")
	ErrDeclarationItemNotFound  = errors.New("declaration item not found")
)

// Tax type related errors
var (
	ErrTaxTypeNotFound = errors.New("tax type not found")
	ErrTaxTypeExists   = errors.New("tax type already exists")
	ErrTaxTypeInactive = errors.New("tax type is inactive")
	ErrInvalidTaxRate  = errors.New("invalid tax rate")
)

// Permission related errors
var (
	ErrPermissionDenied       = errors.New("permission denied")
	ErrRoleNotFound           = errors.New("role not found")
	ErrPermissionNotFound     = errors.New("permission not found")
	ErrInsufficientPrivileges = errors.New("insufficient privileges")
)

// Data validation errors
var (
	ErrInvalidInput         = errors.New("invalid input")
	ErrRequiredFieldMissing = errors.New("required field missing")
	ErrInvalidFormat        = errors.New("invalid format")
	ErrDataTooLong          = errors.New("data too long")
)

// System errors
var (
	ErrDatabaseConnection  = errors.New("database connection failed")
	ErrConfigurationError  = errors.New("configuration error")
	ErrServiceUnavailable  = errors.New("service unavailable")
	ErrInternalServerError = errors.New("internal server error")
)
