// Package util provides utility functions and common structures for the tax management system.
// It includes response formatting, error handling, validation helpers, and other shared utilities.
package util

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"
)

// GenerateID generates a unique ID with timestamp prefix for database records
func GenerateID() string {
	// Use current timestamp as prefix (in seconds)
	timestamp := time.Now().Unix()

	// Generate 6 random bytes
	randomBytes := make([]byte, 6)
	_, err := rand.Read(randomBytes)
	if err != nil {
		// Fallback to timestamp + counter if random fails
		return fmt.Sprintf("%d_%d", timestamp, time.Now().Nanosecond())
	}

	// Combine timestamp with random hex
	return fmt.Sprintf("%d_%s", timestamp, hex.EncodeToString(randomBytes))
}
