// Package main provides the entry point for the tax management system backend API.
// It initializes the database, sets up middleware, configures routes, and starts the HTTP server.
// The system supports enterprise management, invoice processing, tax declarations, and user authentication.
package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"

	"backend/bootstrap"
	"backend/config"
	"backend/controller"
	"backend/middleware"
	"backend/router"
	"backend/service"
	"backend/util"
)

func main() {
	// 加载配置文件
	cfg, err := config.LoadConfig("config")
	if err != nil {
		log.Fatalf("配置加载失败: %v", err)
	}

	// 初始化数据库
	db := bootstrap.SetupDatabase(cfg)

	//// 执行数据库迁移
	//if cfg.Database.AutoMigrate {
	//	if err := migrateDatabase(db); err != nil {
	//		log.Fatalf("数据库迁移失败: %v", err)
	//	}
	//}

	// 创建Gin路由器
	r := gin.New()

	// 添加中间件
	r.Use(middleware.Logger())
	r.Use(gin.Recovery())
	r.Use(middleware.RequestID())
	r.Use(middleware.CORS())

	// 设置404和405错误处理器
	r.NoRoute(func(c *gin.Context) {
		util.ErrorResponse(c, 404, "接口不存在")
	})
	r.NoMethod(func(c *gin.Context) {
		util.ErrorResponse(c, 405, "请求方法不允许")
	})

	// 创建核心服务
	authService := service.NewAuthService(db, &cfg)
	enterpriseService := service.NewEnterpriseService(db)
	userManagementService := service.NewUserManagementService(db, &cfg)
	permissionService := service.NewPermissionService(db, &cfg)

	// 创建税务申报相关控制器（暂时使用空实现）
	var taxFilingController *controller.TaxFilingController
	var taxFilingMonitorController *controller.TaxFilingMonitorController
	var taxFilingProvinceController *controller.TaxFilingProvinceController

	// TODO: 等待税务申报服务完善后，创建实际的控制器实例
	// taxFilingService := service.NewTaxFilingService(db, logger, ...)
	// taxFilingController = controller.NewTaxFilingController(taxFilingService, ...)
	// taxFilingMonitorController = controller.NewTaxFilingMonitorController(...)
	// taxFilingProvinceController = controller.NewTaxFilingProvinceController(...)

	// 使用新的统一路由配置
	router.SetupRoutes(
		r,
		authService,
		enterpriseService,
		userManagementService,
		permissionService,
		taxFilingController,
		taxFilingMonitorController,
		taxFilingProvinceController,
		&cfg,
	)

	// 启动服务器
	port := cfg.App.Port
	if port == "" {
		port = "8081"
	}

	log.Printf("🚀 税易通启动成功")
	log.Printf("📍 服务地址: http://localhost:%s", port)
	log.Printf("📊 健康检查: http://localhost:%s/api/system/health", port)
	log.Printf("🔐 认证端点: http://localhost:%s/api/auth/login", port)
	log.Printf("🏢 企业管理: http://localhost:%s/api/enterprises", port)
	log.Printf("📄 发票管理: http://localhost:%s/api/invoices", port)
	log.Printf("📋 税务申报: http://localhost:%s/api/tax-filing", port)

	// 启动HTTP服务器
	server := &http.Server{
		Addr:    ":" + port,
		Handler: r,
	}

	// 优雅关闭
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("正在关闭服务器...")

	// 5秒超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		log.Fatal("服务器强制关闭:", err)
	}

	log.Println("服务器已退出")
}
