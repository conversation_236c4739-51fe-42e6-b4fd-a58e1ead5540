package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// TaxFilingClient 税务申报服务客户端
type TaxFilingClient struct {
	baseURL    string
	apiKey     string
	httpClient *http.Client
	logger     *zap.Logger

	// 熔断器配置
	circuitBreaker *CircuitBreaker

	// 重试配置
	maxRetries int
	retryDelay time.Duration
}

// TaxFilingClientConfig 客户端配置
type TaxFilingClientConfig struct {
	BaseURL    string        `json:"base_url"`
	APIKey     string        `json:"api_key"`
	Timeout    time.Duration `json:"timeout"`
	MaxRetries int           `json:"max_retries"`
	RetryDelay time.Duration `json:"retry_delay"`

	// 熔断器配置
	CircuitBreakerConfig *CircuitBreakerConfig `json:"circuit_breaker"`
}

// NewTaxFilingClient 创建税务申报服务客户端
func NewTaxFilingClient(config *TaxFilingClientConfig, logger *zap.Logger) *TaxFilingClient {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}
	if config.RetryDelay == 0 {
		config.RetryDelay = time.Second
	}

	httpClient := &http.Client{
		Timeout: config.Timeout,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	// 创建熔断器
	var circuitBreaker *CircuitBreaker
	if config.CircuitBreakerConfig != nil {
		circuitBreaker = NewCircuitBreaker(config.CircuitBreakerConfig)
	} else {
		// 默认熔断器配置
		circuitBreaker = NewCircuitBreaker(&CircuitBreakerConfig{
			MaxFailures:   5,
			ResetTimeout:  60 * time.Second,
			CheckInterval: 10 * time.Second,
		})
	}

	return &TaxFilingClient{
		baseURL:        config.BaseURL,
		apiKey:         config.APIKey,
		httpClient:     httpClient,
		logger:         logger,
		circuitBreaker: circuitBreaker,
		maxRetries:     config.MaxRetries,
		retryDelay:     config.RetryDelay,
	}
}

// TaxSubmissionRequest 税务申报请求
type TaxSubmissionRequest struct {
	ProvinceCode   string                 `json:"province_code"`
	CompanyInfo    CompanyInfo            `json:"company_info"`
	TaxPeriod      TaxPeriod              `json:"tax_period"`
	TaxData        []TaxData              `json:"tax_data"`
	Notes          *string                `json:"notes,omitempty"`
	AdditionalData map[string]interface{} `json:"additional_data,omitempty"`
}

// CompanyInfo 公司信息
type CompanyInfo struct {
	CompanyName         string  `json:"company_name"`
	TaxID               string  `json:"tax_id"`
	RegistrationNumber  string  `json:"registration_number"`
	LegalRepresentative string  `json:"legal_representative"`
	Address             string  `json:"address"`
	Phone               *string `json:"phone,omitempty"`
	Email               *string `json:"email,omitempty"`
}

// TaxPeriod 税务期间
type TaxPeriod struct {
	Year       int    `json:"year"`
	Month      *int   `json:"month,omitempty"`
	Quarter    *int   `json:"quarter,omitempty"`
	PeriodType string `json:"period_type"`
}

// TaxData 税务数据
type TaxData struct {
	TaxType       string  `json:"tax_type"`
	TaxableAmount float64 `json:"taxable_amount"`
	TaxRate       float64 `json:"tax_rate"`
	TaxAmount     float64 `json:"tax_amount"`
	Deductions    float64 `json:"deductions"`
	Credits       float64 `json:"credits"`
	FinalAmount   float64 `json:"final_amount"`
}

// TaxSubmissionResponse 税务申报响应
type TaxSubmissionResponse struct {
	SubmissionID            string  `json:"submission_id"`
	Status                  string  `json:"status"`
	Message                 string  `json:"message"`
	ExternalID              *string `json:"external_id,omitempty"`
	EstimatedProcessingTime *int    `json:"estimated_processing_time,omitempty"`
}

// SubmissionStatusResponse 申报状态响应
type SubmissionStatusResponse struct {
	SubmissionID string                 `json:"submission_id"`
	Status       string                 `json:"status"`
	ExternalID   *string                `json:"external_id,omitempty"`
	SubmittedAt  *time.Time             `json:"submitted_at,omitempty"`
	ProcessedAt  *time.Time             `json:"processed_at,omitempty"`
	ErrorMessage *string                `json:"error_message,omitempty"`
	Progress     map[string]interface{} `json:"progress,omitempty"`
}

// BatchSubmissionRequest 批量申报请求
type BatchSubmissionRequest struct {
	BatchID     *string                `json:"batch_id,omitempty"`
	Submissions []TaxSubmissionRequest `json:"submissions"`
}

// BatchSubmissionResponse 批量申报响应
type BatchSubmissionResponse struct {
	BatchID               string             `json:"batch_id"`
	TotalSubmissions      int                `json:"total_submissions"`
	SuccessfulSubmissions []string           `json:"successful_submissions"`
	FailedSubmissions     []FailedSubmission `json:"failed_submissions"`
}

// FailedSubmission 失败的申报
type FailedSubmission struct {
	Index          int                  `json:"index"`
	Error          string               `json:"error"`
	SubmissionData TaxSubmissionRequest `json:"submission_data"`
}

// ValidationRequest 验证请求
type ValidationRequest struct {
	ProvinceCode string                 `json:"province_code"`
	Data         map[string]interface{} `json:"data"`
}

// ValidationResponse 验证响应
type ValidationResponse struct {
	IsValid  bool              `json:"is_valid"`
	Errors   []ValidationError `json:"errors"`
	Warnings []ValidationError `json:"warnings"`
	Info     []ValidationError `json:"info"`
	Summary  map[string]int    `json:"summary"`
}

// ValidationError 验证错误
type ValidationError struct {
	FieldName     string      `json:"field_name"`
	FieldPath     string      `json:"field_path"`
	Level         string      `json:"level"`
	Message       string      `json:"message"`
	ExpectedValue interface{} `json:"expected_value,omitempty"`
	ActualValue   interface{} `json:"actual_value,omitempty"`
	RuleName      *string     `json:"rule_name,omitempty"`
}

// ProvinceInfo 省份信息
type ProvinceInfo struct {
	Code              string   `json:"code"`
	Name              string   `json:"name"`
	NameEn            *string  `json:"name_en,omitempty"`
	Status            string   `json:"status"`
	SupportedTaxTypes []string `json:"supported_tax_types"`
	Features          []string `json:"features"`
}

// HealthCheckResponse 健康检查响应
type HealthCheckResponse struct {
	Status    string                 `json:"status"`
	Service   string                 `json:"service"`
	Version   string                 `json:"version"`
	Timestamp string                 `json:"timestamp"`
	Details   map[string]interface{} `json:"details,omitempty"`
}

// SubmitTaxReturn 提交税务申报
func (c *TaxFilingClient) SubmitTaxReturn(ctx context.Context, request *TaxSubmissionRequest) (*TaxSubmissionResponse, error) {
	var response TaxSubmissionResponse

	err := c.circuitBreaker.Execute(func() error {
		return c.makeRequestWithRetry(ctx, "POST", "/api/tax/submit", request, &response)
	})

	if err != nil {
		c.logger.Error("Failed to submit tax return",
			zap.Error(err),
			zap.String("province_code", request.ProvinceCode),
			zap.String("company_name", request.CompanyInfo.CompanyName),
		)
		return nil, err
	}

	c.logger.Info("Tax return submitted successfully",
		zap.String("submission_id", response.SubmissionID),
		zap.String("status", response.Status),
	)

	return &response, nil
}

// GetSubmissionStatus 获取申报状态
func (c *TaxFilingClient) GetSubmissionStatus(ctx context.Context, submissionID string) (*SubmissionStatusResponse, error) {
	var response SubmissionStatusResponse

	endpoint := fmt.Sprintf("/api/tax/status/%s", submissionID)
	err := c.circuitBreaker.Execute(func() error {
		return c.makeRequestWithRetry(ctx, "GET", endpoint, nil, &response)
	})

	if err != nil {
		c.logger.Error("Failed to get submission status",
			zap.Error(err),
			zap.String("submission_id", submissionID),
		)
		return nil, err
	}

	return &response, nil
}

// BatchSubmit 批量提交申报
func (c *TaxFilingClient) BatchSubmit(ctx context.Context, request *BatchSubmissionRequest) (*BatchSubmissionResponse, error) {
	var response BatchSubmissionResponse

	err := c.circuitBreaker.Execute(func() error {
		return c.makeRequestWithRetry(ctx, "POST", "/api/tax/batch-submit", request, &response)
	})

	if err != nil {
		c.logger.Error("Failed to batch submit tax returns",
			zap.Error(err),
			zap.Int("total_submissions", len(request.Submissions)),
		)
		return nil, err
	}

	c.logger.Info("Batch submission completed",
		zap.String("batch_id", response.BatchID),
		zap.Int("total", response.TotalSubmissions),
		zap.Int("successful", len(response.SuccessfulSubmissions)),
		zap.Int("failed", len(response.FailedSubmissions)),
	)

	return &response, nil
}

// RetrySubmission 重试申报
func (c *TaxFilingClient) RetrySubmission(ctx context.Context, submissionID string) (*TaxSubmissionResponse, error) {
	var response TaxSubmissionResponse

	endpoint := fmt.Sprintf("/api/tax/retry/%s", submissionID)
	err := c.circuitBreaker.Execute(func() error {
		return c.makeRequestWithRetry(ctx, "POST", endpoint, nil, &response)
	})

	if err != nil {
		c.logger.Error("Failed to retry submission",
			zap.Error(err),
			zap.String("submission_id", submissionID),
		)
		return nil, err
	}

	return &response, nil
}

// CancelSubmission 取消申报
func (c *TaxFilingClient) CancelSubmission(ctx context.Context, submissionID string) error {
	endpoint := fmt.Sprintf("/api/tax/cancel/%s", submissionID)

	err := c.circuitBreaker.Execute(func() error {
		return c.makeRequestWithRetry(ctx, "DELETE", endpoint, nil, nil)
	})

	if err != nil {
		c.logger.Error("Failed to cancel submission",
			zap.Error(err),
			zap.String("submission_id", submissionID),
		)
		return err
	}

	c.logger.Info("Submission cancelled successfully",
		zap.String("submission_id", submissionID),
	)

	return nil
}

// ValidateSubmission 验证申报数据
func (c *TaxFilingClient) ValidateSubmission(ctx context.Context, request *ValidationRequest) (*ValidationResponse, error) {
	var response ValidationResponse

	err := c.circuitBreaker.Execute(func() error {
		return c.makeRequestWithRetry(ctx, "POST", "/api/validation/validate-data", request, &response)
	})

	if err != nil {
		c.logger.Error("Failed to validate submission",
			zap.Error(err),
			zap.String("province_code", request.ProvinceCode),
		)
		return nil, err
	}

	return &response, nil
}

// GetProvinceInfo 获取省份信息
func (c *TaxFilingClient) GetProvinceInfo(ctx context.Context, provinceCode string) (*ProvinceInfo, error) {
	var response ProvinceInfo

	endpoint := fmt.Sprintf("/api/provinces/%s", provinceCode)
	err := c.circuitBreaker.Execute(func() error {
		return c.makeRequestWithRetry(ctx, "GET", endpoint, nil, &response)
	})

	if err != nil {
		c.logger.Error("Failed to get province info",
			zap.Error(err),
			zap.String("province_code", provinceCode),
		)
		return nil, err
	}

	return &response, nil
}

// GetSupportedProvinces 获取支持的省份列表
func (c *TaxFilingClient) GetSupportedProvinces(ctx context.Context) ([]ProvinceInfo, error) {
	var response []ProvinceInfo

	err := c.circuitBreaker.Execute(func() error {
		return c.makeRequestWithRetry(ctx, "GET", "/api/provinces", nil, &response)
	})

	if err != nil {
		c.logger.Error("Failed to get supported provinces", zap.Error(err))
		return nil, err
	}

	return response, nil
}

// HealthCheck 健康检查
func (c *TaxFilingClient) HealthCheck(ctx context.Context) (*HealthCheckResponse, error) {
	var response HealthCheckResponse

	err := c.makeRequestWithRetry(ctx, "GET", "/health", nil, &response)
	if err != nil {
		c.logger.Error("Health check failed", zap.Error(err))
		return nil, err
	}

	return &response, nil
}

// makeRequestWithRetry 带重试的请求
func (c *TaxFilingClient) makeRequestWithRetry(ctx context.Context, method, endpoint string, body interface{}, result interface{}) error {
	var lastErr error

	for attempt := 0; attempt <= c.maxRetries; attempt++ {
		if attempt > 0 {
			// 等待重试延迟
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(c.retryDelay * time.Duration(attempt)):
			}
		}

		err := c.makeRequest(ctx, method, endpoint, body, result)
		if err == nil {
			return nil
		}

		lastErr = err

		// 检查是否应该重试
		if !c.shouldRetry(err) {
			break
		}

		c.logger.Warn("Request failed, retrying",
			zap.Error(err),
			zap.Int("attempt", attempt+1),
			zap.Int("max_retries", c.maxRetries),
		)
	}

	return lastErr
}

// makeRequest 发起HTTP请求
func (c *TaxFilingClient) makeRequest(ctx context.Context, method, endpoint string, body interface{}, result interface{}) error {
	url := c.baseURL + endpoint

	var reqBody io.Reader
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return fmt.Errorf("failed to marshal request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequestWithContext(ctx, method, url, reqBody)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.apiKey)
	req.Header.Set("User-Agent", "TaxManagementSystem/1.0")

	// 添加请求ID用于追踪
	if requestID := ctx.Value("request_id"); requestID != nil {
		req.Header.Set("X-Request-ID", requestID.(string))
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode >= 400 {
		return &HTTPError{
			StatusCode: resp.StatusCode,
			Message:    string(respBody),
		}
	}

	// 解析响应
	if result != nil {
		if err := json.Unmarshal(respBody, result); err != nil {
			return fmt.Errorf("failed to unmarshal response: %w", err)
		}
	}

	return nil
}

// shouldRetry 判断是否应该重试
func (c *TaxFilingClient) shouldRetry(err error) bool {
	if httpErr, ok := err.(*HTTPError); ok {
		// 5xx错误和429错误可以重试
		return httpErr.StatusCode >= 500 || httpErr.StatusCode == 429
	}

	// 网络错误可以重试
	return true
}

// HTTPError HTTP错误
type HTTPError struct {
	StatusCode int
	Message    string
}

func (e *HTTPError) Error() string {
	return fmt.Sprintf("HTTP %d: %s", e.StatusCode, e.Message)
}

// GetCircuitBreakerStatus 获取熔断器状态
func (c *TaxFilingClient) GetCircuitBreakerStatus() string {
	return c.circuitBreaker.GetState().String()
}

// ResetCircuitBreaker 重置熔断器
func (c *TaxFilingClient) ResetCircuitBreaker() {
	c.circuitBreaker.Reset()
}
