package client

import (
	"errors"
	"fmt"
	"sync"
	"time"
)

// CircuitBreakerState 熔断器状态
type CircuitBreakerState int

const (
	// StateClosed 关闭状态 - 正常工作
	StateClosed CircuitBreakerState = iota
	// StateOpen 开启状态 - 熔断中
	StateOpen
	// StateHalfOpen 半开状态 - 尝试恢复
	StateHalfOpen
)

func (s CircuitBreakerState) String() string {
	switch s {
	case StateClosed:
		return "CLOSED"
	case StateOpen:
		return "OPEN"
	case StateHalfOpen:
		return "HALF_OPEN"
	default:
		return "UNKNOWN"
	}
}

// CircuitBreakerConfig 熔断器配置
type CircuitBreakerConfig struct {
	MaxFailures   int           `json:"max_failures"`   // 最大失败次数
	ResetTimeout  time.Duration `json:"reset_timeout"`  // 重置超时时间
	CheckInterval time.Duration `json:"check_interval"` // 检查间隔
}

// CircuitBreaker 熔断器
type CircuitBreaker struct {
	config       *CircuitBreakerConfig
	state        CircuitBreakerState
	failures     int
	lastFailTime time.Time
	mutex        sync.RWMutex

	// 统计信息
	totalRequests    int64
	successRequests  int64
	failedRequests   int64
	rejectedRequests int64
}

// NewCircuitBreaker 创建熔断器
func NewCircuitBreaker(config *CircuitBreakerConfig) *CircuitBreaker {
	return &CircuitBreaker{
		config: config,
		state:  StateClosed,
	}
}

// Execute 执行函数，带熔断保护
func (cb *CircuitBreaker) Execute(fn func() error) error {
	cb.mutex.Lock()
	cb.totalRequests++

	// 检查是否可以执行
	if !cb.canExecute() {
		cb.rejectedRequests++
		cb.mutex.Unlock()
		return errors.New("circuit breaker is open")
	}

	// 如果是半开状态，只允许一个请求通过
	if cb.state == StateHalfOpen {
		cb.mutex.Unlock()
		return cb.executeInHalfOpen(fn)
	}

	cb.mutex.Unlock()

	// 执行函数
	err := fn()

	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	if err != nil {
		cb.onFailure()
		cb.failedRequests++
	} else {
		cb.onSuccess()
		cb.successRequests++
	}

	return err
}

// canExecute 检查是否可以执行
func (cb *CircuitBreaker) canExecute() bool {
	switch cb.state {
	case StateClosed:
		return true
	case StateOpen:
		// 检查是否可以转换到半开状态
		if time.Since(cb.lastFailTime) > cb.config.ResetTimeout {
			cb.state = StateHalfOpen
			return true
		}
		return false
	case StateHalfOpen:
		return true
	default:
		return false
	}
}

// executeInHalfOpen 在半开状态下执行
func (cb *CircuitBreaker) executeInHalfOpen(fn func() error) error {
	err := fn()

	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	if err != nil {
		// 失败，回到开启状态
		cb.state = StateOpen
		cb.failures++
		cb.lastFailTime = time.Now()
		cb.failedRequests++
	} else {
		// 成功，回到关闭状态
		cb.state = StateClosed
		cb.failures = 0
		cb.successRequests++
	}

	return err
}

// onFailure 处理失败
func (cb *CircuitBreaker) onFailure() {
	cb.failures++
	cb.lastFailTime = time.Now()

	if cb.failures >= cb.config.MaxFailures {
		cb.state = StateOpen
	}
}

// onSuccess 处理成功
func (cb *CircuitBreaker) onSuccess() {
	if cb.state == StateClosed {
		cb.failures = 0
	}
}

// GetState 获取当前状态
func (cb *CircuitBreaker) GetState() CircuitBreakerState {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	return cb.state
}

// GetFailures 获取失败次数
func (cb *CircuitBreaker) GetFailures() int {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	return cb.failures
}

// Reset 重置熔断器
func (cb *CircuitBreaker) Reset() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.state = StateClosed
	cb.failures = 0
	cb.lastFailTime = time.Time{}
}

// GetStatistics 获取统计信息
func (cb *CircuitBreaker) GetStatistics() *CircuitBreakerStatistics {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()

	var successRate float64
	if cb.totalRequests > 0 {
		successRate = float64(cb.successRequests) / float64(cb.totalRequests) * 100
	}

	return &CircuitBreakerStatistics{
		State:            cb.state,
		TotalRequests:    cb.totalRequests,
		SuccessRequests:  cb.successRequests,
		FailedRequests:   cb.failedRequests,
		RejectedRequests: cb.rejectedRequests,
		SuccessRate:      successRate,
		CurrentFailures:  cb.failures,
		LastFailTime:     cb.lastFailTime,
	}
}

// CircuitBreakerStatistics 熔断器统计信息
type CircuitBreakerStatistics struct {
	State            CircuitBreakerState `json:"state"`
	TotalRequests    int64               `json:"total_requests"`
	SuccessRequests  int64               `json:"success_requests"`
	FailedRequests   int64               `json:"failed_requests"`
	RejectedRequests int64               `json:"rejected_requests"`
	SuccessRate      float64             `json:"success_rate"`
	CurrentFailures  int                 `json:"current_failures"`
	LastFailTime     time.Time           `json:"last_fail_time"`
}

// CircuitBreakerManager 熔断器管理器
type CircuitBreakerManager struct {
	breakers map[string]*CircuitBreaker
	mutex    sync.RWMutex
}

// NewCircuitBreakerManager 创建熔断器管理器
func NewCircuitBreakerManager() *CircuitBreakerManager {
	return &CircuitBreakerManager{
		breakers: make(map[string]*CircuitBreaker),
	}
}

// GetOrCreateBreaker 获取或创建熔断器
func (cbm *CircuitBreakerManager) GetOrCreateBreaker(name string, config *CircuitBreakerConfig) *CircuitBreaker {
	cbm.mutex.RLock()
	if breaker, exists := cbm.breakers[name]; exists {
		cbm.mutex.RUnlock()
		return breaker
	}
	cbm.mutex.RUnlock()

	cbm.mutex.Lock()
	defer cbm.mutex.Unlock()

	// 双重检查
	if breaker, exists := cbm.breakers[name]; exists {
		return breaker
	}

	breaker := NewCircuitBreaker(config)
	cbm.breakers[name] = breaker
	return breaker
}

// GetBreaker 获取熔断器
func (cbm *CircuitBreakerManager) GetBreaker(name string) *CircuitBreaker {
	cbm.mutex.RLock()
	defer cbm.mutex.RUnlock()
	return cbm.breakers[name]
}

// GetAllBreakers 获取所有熔断器
func (cbm *CircuitBreakerManager) GetAllBreakers() map[string]*CircuitBreaker {
	cbm.mutex.RLock()
	defer cbm.mutex.RUnlock()

	result := make(map[string]*CircuitBreaker)
	for name, breaker := range cbm.breakers {
		result[name] = breaker
	}
	return result
}

// ResetBreaker 重置指定熔断器
func (cbm *CircuitBreakerManager) ResetBreaker(name string) bool {
	cbm.mutex.RLock()
	breaker, exists := cbm.breakers[name]
	cbm.mutex.RUnlock()

	if exists {
		breaker.Reset()
		return true
	}
	return false
}

// ResetAllBreakers 重置所有熔断器
func (cbm *CircuitBreakerManager) ResetAllBreakers() {
	cbm.mutex.RLock()
	defer cbm.mutex.RUnlock()

	for _, breaker := range cbm.breakers {
		breaker.Reset()
	}
}

// GetStatistics 获取所有熔断器统计信息
func (cbm *CircuitBreakerManager) GetStatistics() map[string]*CircuitBreakerStatistics {
	cbm.mutex.RLock()
	defer cbm.mutex.RUnlock()

	result := make(map[string]*CircuitBreakerStatistics)
	for name, breaker := range cbm.breakers {
		result[name] = breaker.GetStatistics()
	}
	return result
}

// MonitorBreakers 监控熔断器状态
func (cbm *CircuitBreakerManager) MonitorBreakers() <-chan *CircuitBreakerEvent {
	eventChan := make(chan *CircuitBreakerEvent, 100)

	go func() {
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()
		defer close(eventChan)

		for {
			select {
			case <-ticker.C:
				cbm.checkBreakers(eventChan)
			}
		}
	}()

	return eventChan
}

// checkBreakers 检查熔断器状态
func (cbm *CircuitBreakerManager) checkBreakers(eventChan chan<- *CircuitBreakerEvent) {
	cbm.mutex.RLock()
	defer cbm.mutex.RUnlock()

	for name, breaker := range cbm.breakers {
		stats := breaker.GetStatistics()

		// 检查是否需要发送告警
		if stats.State == StateOpen {
			event := &CircuitBreakerEvent{
				BreakerName: name,
				EventType:   "circuit_open",
				State:       stats.State,
				Message:     "Circuit breaker is open",
				Timestamp:   time.Now(),
				Statistics:  stats,
			}

			select {
			case eventChan <- event:
			default:
				// 如果通道满了，跳过这个事件
			}
		}

		// 检查成功率是否过低
		if stats.TotalRequests > 10 && stats.SuccessRate < 50 {
			event := &CircuitBreakerEvent{
				BreakerName: name,
				EventType:   "low_success_rate",
				State:       stats.State,
				Message:     fmt.Sprintf("Low success rate: %.2f%%", stats.SuccessRate),
				Timestamp:   time.Now(),
				Statistics:  stats,
			}

			select {
			case eventChan <- event:
			default:
			}
		}
	}
}

// CircuitBreakerEvent 熔断器事件
type CircuitBreakerEvent struct {
	BreakerName string                    `json:"breaker_name"`
	EventType   string                    `json:"event_type"`
	State       CircuitBreakerState       `json:"state"`
	Message     string                    `json:"message"`
	Timestamp   time.Time                 `json:"timestamp"`
	Statistics  *CircuitBreakerStatistics `json:"statistics"`
}
