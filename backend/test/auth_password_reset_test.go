// Package test provides test utilities and test cases for the tax management system.
// It includes unit tests for password reset functionality.
package test

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"backend/api"
	"backend/config"
	"backend/model"
	"backend/service"
	"backend/util"
)

// TestUser is a simplified user model for testing
type TestUser struct {
	ID                string     `gorm:"primaryKey;type:varchar(36)" json:"id"`
	UserName          string     `gorm:"type:varchar(50);not null;uniqueIndex" json:"userName"`
	Email             string     `gorm:"type:varchar(100);uniqueIndex" json:"email"`
	Phone             string     `gorm:"type:varchar(20);uniqueIndex" json:"phone"`
	PasswordHash      string     `gorm:"type:varchar(255);not null" json:"-"`
	IsActive          bool       `gorm:"default:true" json:"isActive"`
	ResetToken        string     `gorm:"type:varchar(255)" json:"-"`
	ResetExpiry       *time.Time `gorm:"type:datetime" json:"-"`
	PasswordChangedAt *time.Time `gorm:"type:datetime" json:"passwordChangedAt"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         time.Time  `json:"updatedAt"`
}

// setupPasswordResetTestDB creates an in-memory SQLite database for password reset testing
func setupPasswordResetTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// Only migrate TestUser model for password reset testing
	err = db.AutoMigrate(&TestUser{})
	if err != nil {
		panic("failed to migrate TestUser model: " + err.Error())
	}

	return db
}

// createTestUser creates a test user for password reset testing
func createTestUser(db *gorm.DB) *TestUser {
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)

	user := &TestUser{
		ID:           util.GenerateID(),
		UserName:     "testuser",
		Email:        "<EMAIL>",
		Phone:        "13800138000",
		PasswordHash: string(hashedPassword),
		IsActive:     true,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	db.Create(user)
	return user
}

// TestAuthService_ForgotPassword tests the ForgotPassword service method
func TestAuthService_ForgotPassword(t *testing.T) {
	db := setupPasswordResetTestDB()
	cfg := &config.Config{
		Auth: config.AuthConfig{
			JWTSecret:            "test-secret",
			AccessTokenDuration:  15,
			RefreshTokenDuration: 7,
		},
	}
	authService := service.NewAuthService(db, cfg)

	t.Run("Success with email", func(t *testing.T) {
		user := createTestUser(db)

		req := model.ForgotPasswordRequest{
			Email: user.Email,
		}

		err := authService.ForgotPassword(context.Background(), req)
		assert.NoError(t, err)

		// Verify that reset token was set
		var updatedUser TestUser
		db.First(&updatedUser, "id = ?", user.ID)
		assert.NotEmpty(t, updatedUser.ResetToken)
		assert.NotNil(t, updatedUser.ResetExpiry)
		assert.True(t, updatedUser.ResetExpiry.After(time.Now()))
	})

	t.Run("Success with phone", func(t *testing.T) {
		user := createTestUser(db)

		req := model.ForgotPasswordRequest{
			Phone: user.Phone,
		}

		err := authService.ForgotPassword(context.Background(), req)
		assert.NoError(t, err)

		// Verify that reset token was set
		var updatedUser model.User
		db.First(&updatedUser, "id = ?", user.ID)
		assert.NotEmpty(t, updatedUser.ResetToken)
		assert.NotNil(t, updatedUser.ResetExpiry)
	})

	t.Run("Error - no email or phone provided", func(t *testing.T) {
		req := model.ForgotPasswordRequest{}

		err := authService.ForgotPassword(context.Background(), req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "邮箱或手机号必须提供其中一个")
	})

	t.Run("Success - user not found (security)", func(t *testing.T) {
		req := model.ForgotPasswordRequest{
			Email: "<EMAIL>",
		}

		// Should not return error for security reasons
		err := authService.ForgotPassword(context.Background(), req)
		assert.NoError(t, err)
	})

	t.Run("Error - inactive user", func(t *testing.T) {
		user := createTestUser(db)
		db.Model(user).Update("is_active", false)

		req := model.ForgotPasswordRequest{
			Email: user.Email,
		}

		err := authService.ForgotPassword(context.Background(), req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "账户已被禁用")
	})
}

// TestAuthService_ResetPassword tests the ResetPassword service method
func TestAuthService_ResetPassword(t *testing.T) {
	db := setupPasswordResetTestDB()
	cfg := &config.Config{
		Auth: config.AuthConfig{
			JWTSecret:            "test-secret",
			AccessTokenDuration:  15,
			RefreshTokenDuration: 7,
		},
	}
	authService := service.NewAuthService(db, cfg)

	t.Run("Success", func(t *testing.T) {
		user := createTestUser(db)

		// Set reset token
		resetToken := "valid-reset-token"
		expiry := time.Now().Add(24 * time.Hour)
		db.Model(user).Updates(map[string]interface{}{
			"reset_token":  resetToken,
			"reset_expiry": expiry,
		})

		req := model.ResetPasswordRequest{
			Token:    resetToken,
			Password: "newpassword123",
		}

		err := authService.ResetPassword(context.Background(), req)
		assert.NoError(t, err)

		// Verify password was changed and token was cleared
		var updatedUser model.User
		db.First(&updatedUser, "id = ?", user.ID)
		assert.Empty(t, updatedUser.ResetToken)
		assert.Nil(t, updatedUser.ResetExpiry)
		assert.NotNil(t, updatedUser.PasswordChangedAt)

		// Verify new password works
		err = bcrypt.CompareHashAndPassword([]byte(updatedUser.PasswordHash), []byte("newpassword123"))
		assert.NoError(t, err)
	})

	t.Run("Error - invalid token", func(t *testing.T) {
		req := model.ResetPasswordRequest{
			Token:    "invalid-token",
			Password: "newpassword123",
		}

		err := authService.ResetPassword(context.Background(), req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效或已过期的重置令牌")
	})

	t.Run("Error - expired token", func(t *testing.T) {
		user := createTestUser(db)

		// Set expired reset token
		resetToken := "expired-reset-token"
		expiry := time.Now().Add(-1 * time.Hour) // Expired 1 hour ago
		db.Model(user).Updates(map[string]interface{}{
			"reset_token":  resetToken,
			"reset_expiry": expiry,
		})

		req := model.ResetPasswordRequest{
			Token:    resetToken,
			Password: "newpassword123",
		}

		err := authService.ResetPassword(context.Background(), req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效或已过期的重置令牌")
	})

	t.Run("Error - inactive user", func(t *testing.T) {
		user := createTestUser(db)

		// Set reset token
		resetToken := "valid-reset-token"
		expiry := time.Now().Add(24 * time.Hour)
		db.Model(user).Updates(map[string]interface{}{
			"reset_token":  resetToken,
			"reset_expiry": expiry,
			"is_active":    false,
		})

		req := model.ResetPasswordRequest{
			Token:    resetToken,
			Password: "newpassword123",
		}

		err := authService.ResetPassword(context.Background(), req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "账户已被禁用")
	})
}

// TestAuthService_GenerateResetToken tests the generateResetToken method
func TestAuthService_GenerateResetToken(t *testing.T) {
	db := setupPasswordResetTestDB()
	cfg := &config.Config{
		Auth: config.AuthConfig{
			JWTSecret:            "test-secret",
			AccessTokenDuration:  15,
			RefreshTokenDuration: 7,
		},
	}
	authService := service.NewAuthService(db, cfg)

	// Use reflection to access private method (for testing purposes)
	// In a real scenario, you might want to make this method public for testing
	// or test it indirectly through the public methods

	// For now, we'll test token generation indirectly through ForgotPassword
	user := createTestUser(db)

	req := model.ForgotPasswordRequest{
		Email: user.Email,
	}

	err := authService.ForgotPassword(context.Background(), req)
	require.NoError(t, err)

	// Verify token was generated
	var updatedUser model.User
	db.First(&updatedUser, "id = ?", user.ID)

	// Token should be 64 characters (32 bytes hex encoded)
	assert.Len(t, updatedUser.ResetToken, 64)

	// Generate another token and verify they're different
	err = authService.ForgotPassword(context.Background(), req)
	require.NoError(t, err)

	var updatedUser2 model.User
	db.First(&updatedUser2, "id = ?", user.ID)
	assert.NotEqual(t, updatedUser.ResetToken, updatedUser2.ResetToken)
}

// TestAuthHandler_ForgotPassword tests the ForgotPassword API endpoint
func TestAuthHandler_ForgotPassword(t *testing.T) {
	db := setupPasswordResetTestDB()
	cfg := &config.Config{
		Auth: config.AuthConfig{
			JWTSecret:            "test-secret",
			AccessTokenDuration:  15,
			RefreshTokenDuration: 7,
		},
	}
	authService := service.NewAuthService(db, cfg)
	authHandler := api.NewAuthHandler(authService, cfg)

	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/auth/forgot-password", authHandler.ForgotPassword)

	t.Run("Success with email", func(t *testing.T) {
		user := createTestUser(db)

		reqBody := map[string]string{
			"email": user.Email,
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/auth/forgot-password", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response util.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, response.Code)
		assert.Contains(t, response.Message, "密码重置邮件已发送")
	})

	t.Run("Success with phone", func(t *testing.T) {
		user := createTestUser(db)

		reqBody := map[string]string{
			"phone": user.Phone,
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/auth/forgot-password", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Error - no email or phone", func(t *testing.T) {
		reqBody := map[string]string{}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/auth/forgot-password", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response util.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response.Message, "邮箱或手机号必须提供其中一个")
	})

	t.Run("Error - invalid JSON", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/auth/forgot-password", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

// TestAuthHandler_ResetPassword tests the ResetPassword API endpoint
func TestAuthHandler_ResetPassword(t *testing.T) {
	db := setupPasswordResetTestDB()
	cfg := &config.Config{
		Auth: config.AuthConfig{
			JWTSecret:            "test-secret",
			AccessTokenDuration:  15,
			RefreshTokenDuration: 7,
		},
	}
	authService := service.NewAuthService(db, cfg)
	authHandler := api.NewAuthHandler(authService, cfg)

	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/auth/reset-password", authHandler.ResetPassword)

	t.Run("Success", func(t *testing.T) {
		user := createTestUser(db)

		// Set reset token
		resetToken := "valid-reset-token"
		expiry := time.Now().Add(24 * time.Hour)
		db.Model(user).Updates(map[string]interface{}{
			"reset_token":  resetToken,
			"reset_expiry": expiry,
		})

		reqBody := map[string]string{
			"token":    resetToken,
			"password": "newpassword123",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/auth/reset-password", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response util.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, response.Code)
		assert.Contains(t, response.Message, "密码重置成功")
	})

	t.Run("Error - invalid token", func(t *testing.T) {
		reqBody := map[string]string{
			"token":    "invalid-token",
			"password": "newpassword123",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/auth/reset-password", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response util.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response.Message, "重置令牌无效或已过期")
	})

	t.Run("Error - missing required fields", func(t *testing.T) {
		reqBody := map[string]string{
			"token": "some-token",
			// missing password
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/auth/reset-password", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Error - invalid JSON", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/auth/reset-password", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
