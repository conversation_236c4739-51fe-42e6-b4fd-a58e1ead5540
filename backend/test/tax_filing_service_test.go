package test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"backend/model"
)

// MockTaxFilingClient 模拟税务申报客户端
type MockTaxFilingClient struct {
	mock.Mock
}

func (m *MockTaxFilingClient) SubmitTaxFiling(ctx context.Context, submission *model.TaxFilingSubmission) error {
	args := m.Called(ctx, submission)
	return args.Error(0)
}

func (m *MockTaxFilingClient) QuerySubmissionStatus(ctx context.Context, submissionID string) (*model.TaxFilingSubmissionStatus, error) {
	args := m.Called(ctx, submissionID)
	return args.Get(0).(*model.TaxFilingSubmissionStatus), args.Error(1)
}

// MockRedisService 模拟Redis服务
type MockRedisService struct {
	mock.Mock
}

func (m *MockRedisService) Set(ctx context.Context, key string, value interface{}) error {
	args := m.Called(ctx, key, value)
	return args.Error(0)
}

func (m *MockRedisService) Get(ctx context.Context, key string) (string, error) {
	args := m.Called(ctx, key)
	return args.String(0), args.Error(1)
}

func (m *MockRedisService) Delete(ctx context.Context, key string) error {
	args := m.Called(ctx, key)
	return args.Error(0)
}

// TestTaxFilingService 税务申报服务测试
func TestTaxFilingService(t *testing.T) {
	// 设置测试数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	// 自动迁移
	err = db.AutoMigrate(
		&model.Enterprise{},
		&model.TaxFilingSubmission{},
		&model.TaxFilingStatusHistory{},
	)
	assert.NoError(t, err)

	// 创建测试数据
	enterprise := &model.Enterprise{
		ID:   "test_enterprise_001",
		Name: "测试企业",
		Code: "TEST001",
	}
	db.Create(enterprise)

	// 创建模拟对象
	logger, _ := zap.NewDevelopment()
	mockClient := new(MockTaxFilingClient)
	mockRedis := new(MockRedisService)
	mockNotification := NewTaxFilingNotificationService(db, logger)

	// 创建服务
	service := NewTaxFilingService(db, logger, mockClient, mockNotification, mockRedis)

	t.Run("CreateSubmission", func(t *testing.T) {
		req := &model.TaxFilingSubmissionCreateRequest{
			EnterpriseID: "test_enterprise_001",
			ProvinceCode: "BJ",
			CompanyName:  "测试企业",
			TaxID:        "*********",
			TaxData:      `{"tax_type":"增值税","amount":1000}`,
		}

		submission, err := service.CreateSubmission(context.Background(), req)
		assert.NoError(t, err)
		assert.NotNil(t, submission)
		assert.Equal(t, "test_enterprise_001", submission.EnterpriseID)
		assert.Equal(t, "BJ", submission.ProvinceCode)
		assert.Equal(t, model.TaxFilingSubmissionStatusPending, submission.Status)
	})

	t.Run("GetSubmission", func(t *testing.T) {
		// 先创建一个申报记录
		req := &model.TaxFilingSubmissionCreateRequest{
			EnterpriseID: "test_enterprise_001",
			ProvinceCode: "SH",
			CompanyName:  "测试企业",
			TaxID:        "*********",
			TaxData:      `{"tax_type":"企业所得税","amount":2000}`,
		}

		created, err := service.CreateSubmission(context.Background(), req)
		assert.NoError(t, err)

		// 获取申报记录
		retrieved, err := service.GetSubmission(context.Background(), created.ID)
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)
		assert.Equal(t, created.ID, retrieved.ID)
		assert.Equal(t, "SH", retrieved.ProvinceCode)
	})

	t.Run("UpdateSubmissionStatus", func(t *testing.T) {
		// 先创建一个申报记录
		req := &model.TaxFilingSubmissionCreateRequest{
			EnterpriseID: "test_enterprise_001",
			ProvinceCode: "GD",
			CompanyName:  "测试企业",
			TaxID:        "*********",
			TaxData:      `{"tax_type":"增值税","amount":3000}`,
		}

		created, err := service.CreateSubmission(context.Background(), req)
		assert.NoError(t, err)

		// 更新状态
		err = service.UpdateSubmissionStatus(
			context.Background(),
			created.ID,
			model.TaxFilingSubmissionStatusSubmitted,
			"申报已提交",
			nil,
		)
		assert.NoError(t, err)

		// 验证状态更新
		updated, err := service.GetSubmission(context.Background(), created.ID)
		assert.NoError(t, err)
		assert.Equal(t, model.TaxFilingSubmissionStatusSubmitted, updated.Status)
	})

	t.Run("ListSubmissions", func(t *testing.T) {
		query := &model.TaxFilingSubmissionQuery{
			Page:         1,
			PageSize:     10,
			EnterpriseID: "test_enterprise_001",
		}

		submissions, total, err := service.ListSubmissions(context.Background(), query)
		assert.NoError(t, err)
		assert.NotNil(t, submissions)
		assert.Greater(t, total, int64(0))
		assert.True(t, len(submissions) > 0)
	})

	// 验证模拟对象的调用
	mockClient.AssertExpectations(t)
	mockRedis.AssertExpectations(t)
}

// TestTaxFilingNotificationService 通知服务测试
func TestTaxFilingNotificationService(t *testing.T) {
	// 设置测试数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	// 自动迁移
	err = db.AutoMigrate(
		&model.Enterprise{},
		&model.TaxFilingSubmission{},
		&model.Notification{},
	)
	assert.NoError(t, err)

	// 创建测试数据
	enterprise := &model.Enterprise{
		ID:   "test_enterprise_001",
		Name: "测试企业",
		Code: "TEST001",
	}
	db.Create(enterprise)

	submission := &model.TaxFilingSubmission{
		ID:           "test_submission_001",
		EnterpriseID: "test_enterprise_001",
		ProvinceCode: "BJ",
		CompanyName:  "测试企业",
		TaxID:        "*********",
		Status:       model.TaxFilingSubmissionStatusPending,
		TaxData:      `{"tax_type":"增值税","amount":1000}`,
	}
	db.Create(submission)

	// 创建服务
	logger, _ := zap.NewDevelopment()
	service := NewTaxFilingNotificationService(db, logger)

	t.Run("SendSubmissionStatusNotification", func(t *testing.T) {
		err := service.SendSubmissionStatusNotification(
			context.Background(),
			"test_submission_001",
			model.TaxFilingSubmissionStatusSubmitted,
		)
		assert.NoError(t, err)

		// 验证通知是否创建
		var notifications []model.Notification
		db.Where("recipient_id = ?", "test_enterprise_001").Find(&notifications)
		assert.True(t, len(notifications) > 0)
	})

	t.Run("SendSystemAlert", func(t *testing.T) {
		err := service.SendSystemAlert(
			context.Background(),
			"high",
			"系统测试告警",
			"这是一个测试告警消息",
		)
		assert.NoError(t, err)
	})
}

// TestNotificationSenderService 通知发送服务测试
func TestNotificationSenderService(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	service := NewNotificationSenderService(logger)

	t.Run("SendEmailNotification", func(t *testing.T) {
		err := service.SendEmailNotification(
			context.Background(),
			"<EMAIL>",
			"测试邮件",
			"这是一封测试邮件",
		)
		assert.NoError(t, err)
	})

	t.Run("SendSMSNotification", func(t *testing.T) {
		err := service.SendSMSNotification(
			context.Background(),
			"13800138000",
			"这是一条测试短信",
		)
		assert.NoError(t, err)
	})

	t.Run("SendWebhookNotification", func(t *testing.T) {
		// 这个测试会失败，因为URL不存在，但我们可以测试参数验证
		data := map[string]interface{}{
			"message": "测试Webhook",
			"type":    "test",
		}

		err := service.SendWebhookNotification(
			context.Background(),
			"http://localhost:9999/webhook",
			data,
		)
		// 预期会失败，因为URL不存在
		assert.Error(t, err)
	})

	t.Run("ValidateEmailAddress", func(t *testing.T) {
		assert.True(t, service.ValidateEmailAddress("<EMAIL>"))
		assert.False(t, service.ValidateEmailAddress("invalid-email"))
		assert.False(t, service.ValidateEmailAddress(""))
	})

	t.Run("ValidatePhoneNumber", func(t *testing.T) {
		assert.True(t, service.ValidatePhoneNumber("13800138000"))
		assert.False(t, service.ValidatePhoneNumber("*********0"))
		assert.False(t, service.ValidatePhoneNumber(""))
	})

	t.Run("ValidateWebhookURL", func(t *testing.T) {
		assert.True(t, service.ValidateWebhookURL("http://example.com/webhook"))
		assert.True(t, service.ValidateWebhookURL("https://example.com/webhook"))
		assert.False(t, service.ValidateWebhookURL("invalid-url"))
		assert.False(t, service.ValidateWebhookURL(""))
	})
}
