// Package test provides test utilities and test cases for the tax management system.
// It includes integration tests for password reset email functionality.
package test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"backend/config"
	"backend/model"
	"backend/service"
	"backend/util"
)

// TestUser is a simplified user model for testing
type TestUserForEmail struct {
	ID                string     `gorm:"primaryKey;type:varchar(36)" json:"id"`
	UserName          string     `gorm:"type:varchar(50);not null;uniqueIndex" json:"userName"`
	Email             string     `gorm:"type:varchar(100);uniqueIndex" json:"email"`
	Phone             string     `gorm:"type:varchar(20);uniqueIndex" json:"phone"`
	PasswordHash      string     `gorm:"type:varchar(255);not null" json:"-"`
	IsActive          bool       `gorm:"default:true" json:"isActive"`
	ResetToken        string     `gorm:"type:varchar(255)" json:"-"`
	ResetExpiry       *time.Time `gorm:"type:datetime" json:"-"`
	PasswordChangedAt *time.Time `gorm:"type:datetime" json:"passwordChangedAt"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         time.Time  `json:"updatedAt"`
}

// setupEmailIntegrationTestDB creates an in-memory SQLite database for testing
func setupEmailIntegrationTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// Migrate TestUser model
	err = db.AutoMigrate(&TestUserForEmail{})
	if err != nil {
		panic("failed to migrate TestUser model: " + err.Error())
	}

	return db
}

// createTestUserForEmail creates a test user for email testing
func createTestUserForEmail(db *gorm.DB) *TestUserForEmail {
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)

	user := &TestUserForEmail{
		ID:           util.GenerateID(),
		UserName:     "testuser",
		Email:        "<EMAIL>",
		Phone:        "13800138000",
		PasswordHash: string(hashedPassword),
		IsActive:     true,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	db.Create(user)
	return user
}

// setupEmailTestConfig creates a test configuration for email integration tests
func setupEmailTestConfig() config.Config {
	return config.Config{
		Email: config.EmailConfig{
			Provider:        "aliyun",
			AccessKeyID:     "test-access-key-id",
			AccessKeySecret: "test-access-key-secret",
			RegionID:        "cn-hangzhou",
			Endpoint:        "dm.aliyuncs.com",
			FromAddress:     "<EMAIL>",
			FromName:        "税易通测试系统",
			ReplyToAddress:  "<EMAIL>",
		},
		App: config.AppConfig{
			Name: "test-app",
		},
		Auth: config.AuthConfig{
			JWTSecret:            "test-secret",
			AccessTokenDuration:  15,
			RefreshTokenDuration: 7,
		},
	}
}

// TestPasswordResetEmailIntegration tests the complete password reset email workflow
func TestPasswordResetEmailIntegration(t *testing.T) {
	db := setupEmailIntegrationTestDB()
	cfg := setupEmailTestConfig()

	// Create auth service with email integration
	authService := service.NewAuthService(db, &cfg)

	t.Run("Forgot password with email - integration test", func(t *testing.T) {
		user := createTestUserForEmail(db)

		req := model.ForgotPasswordRequest{
			Email: user.Email,
		}

		err := authService.ForgotPassword(context.Background(), req)

		// The request should succeed even though email sending will fail
		// (due to invalid credentials in test environment)
		assert.NoError(t, err)

		// Verify that reset token was set in database
		var updatedUser TestUserForEmail
		db.First(&updatedUser, "id = ?", user.ID)
		assert.NotEmpty(t, updatedUser.ResetToken)
		assert.NotNil(t, updatedUser.ResetExpiry)
		assert.True(t, updatedUser.ResetExpiry.After(time.Now()))
	})

	t.Run("Forgot password with phone - no email sent", func(t *testing.T) {
		user := createTestUserForEmail(db)
		// Clear email to test phone-only scenario
		db.Model(user).Update("email", "")

		req := model.ForgotPasswordRequest{
			Phone: user.Phone,
		}

		err := authService.ForgotPassword(context.Background(), req)

		// Should succeed without sending email
		assert.NoError(t, err)

		// Verify that reset token was set
		var updatedUser TestUserForEmail
		db.First(&updatedUser, "id = ?", user.ID)
		assert.NotEmpty(t, updatedUser.ResetToken)
		assert.NotNil(t, updatedUser.ResetExpiry)
	})

	t.Run("Forgot password with inactive user", func(t *testing.T) {
		user := createTestUserForEmail(db)
		db.Model(user).Update("is_active", false)

		req := model.ForgotPasswordRequest{
			Email: user.Email,
		}

		err := authService.ForgotPassword(context.Background(), req)

		// Should fail for inactive user
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "账户已被禁用")
	})

	t.Run("Forgot password with non-existent user", func(t *testing.T) {
		req := model.ForgotPasswordRequest{
			Email: "<EMAIL>",
		}

		err := authService.ForgotPassword(context.Background(), req)

		// Should not return error for security reasons
		assert.NoError(t, err)
	})
}

// TestEmailServiceIntegration tests email service integration scenarios
func TestEmailServiceIntegration(t *testing.T) {
	cfg := setupEmailTestConfig()
	emailService := service.NewEmailService(cfg)

	ctx := context.Background()

	t.Run("Password reset email content validation", func(t *testing.T) {
		// Test that the email service generates proper content
		// This would fail due to invalid credentials, but we can verify the method call

		err := emailService.SendPasswordResetEmail(ctx, "<EMAIL>", "test-token-123", "testuser")

		// Should attempt to send email and fail due to invalid credentials
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "阿里云邮件发送失败")
	})

	t.Run("Welcome email content validation", func(t *testing.T) {
		err := emailService.SendWelcomeEmail(ctx, "<EMAIL>", "testuser")

		// Should attempt to send email and fail due to invalid credentials
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "阿里云邮件发送失败")
	})
}

// TestEmailServiceConfiguration tests different email service configurations
func TestEmailServiceConfiguration(t *testing.T) {
	t.Run("Aliyun provider configuration", func(t *testing.T) {
		cfg := config.Config{
			Email: config.EmailConfig{
				Provider:        "aliyun",
				AccessKeyID:     "test-key",
				AccessKeySecret: "test-secret",
				RegionID:        "cn-hangzhou",
				Endpoint:        "dm.aliyuncs.com",
				FromAddress:     "<EMAIL>",
				FromName:        "测试系统",
			},
			App: config.AppConfig{Name: "test"},
		}

		emailService := service.NewEmailService(cfg)
		assert.NotNil(t, emailService)

		// Test email sending (will fail due to invalid credentials)
		err := emailService.SendEmail(context.Background(), "<EMAIL>", "测试", "内容", false)
		assert.Error(t, err)
	})

	t.Run("SMTP fallback configuration", func(t *testing.T) {
		cfg := config.Config{
			Email: config.EmailConfig{
				Provider:     "smtp",
				SMTPHost:     "smtp.example.com",
				SMTPPort:     587,
				SMTPUsername: "<EMAIL>",
				SMTPPassword: "password",
				FromAddress:  "<EMAIL>",
				FromName:     "测试系统",
			},
			App: config.AppConfig{Name: "test"},
		}

		emailService := service.NewEmailService(cfg)
		assert.NotNil(t, emailService)

		// Test email sending (will fail with SMTP not implemented)
		err := emailService.SendEmail(context.Background(), "<EMAIL>", "测试", "内容", false)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "SMTP邮件发送功能尚未实现")
	})
}

// TestEmailServiceErrorScenarios tests various error scenarios
func TestEmailServiceErrorScenarios(t *testing.T) {
	cfg := setupEmailTestConfig()
	emailService := service.NewEmailService(cfg)

	ctx := context.Background()

	t.Run("Invalid email address", func(t *testing.T) {
		err := emailService.SendEmail(ctx, "invalid-email", "测试", "内容", false)

		// Should attempt to send and fail
		assert.Error(t, err)
	})

	t.Run("Empty subject", func(t *testing.T) {
		err := emailService.SendEmail(ctx, "<EMAIL>", "", "内容", false)

		// Should attempt to send and fail due to credentials, not empty subject
		assert.Error(t, err)
	})

	t.Run("Empty body", func(t *testing.T) {
		err := emailService.SendEmail(ctx, "<EMAIL>", "测试", "", false)

		// Should attempt to send and fail due to credentials, not empty body
		assert.Error(t, err)
	})
}

// TestEmailServicePerformance tests email service performance characteristics
func TestEmailServicePerformance(t *testing.T) {
	cfg := setupEmailTestConfig()
	emailService := service.NewEmailService(cfg)

	ctx := context.Background()

	t.Run("Concurrent email sending", func(t *testing.T) {
		// Test concurrent email sending
		const numGoroutines = 5

		done := make(chan bool, numGoroutines)

		for i := 0; i < numGoroutines; i++ {
			go func(id int) {
				err := emailService.SendEmail(ctx, "<EMAIL>", "测试", "内容", false)
				// All should fail due to invalid credentials
				assert.Error(t, err)
				done <- true
			}(i)
		}

		// Wait for all goroutines to complete
		for i := 0; i < numGoroutines; i++ {
			<-done
		}
	})
}
