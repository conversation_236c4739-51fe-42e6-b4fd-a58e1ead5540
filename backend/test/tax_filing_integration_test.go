package test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"backend/client"
	"backend/controller"
	"backend/model"
	"backend/service"
)

// TaxFilingIntegrationTestSuite 税务申报集成测试套件
type TaxFilingIntegrationTestSuite struct {
	suite.Suite
	db                    *gorm.DB
	logger                *zap.Logger
	router                *gin.Engine
	taxFilingService      *service.TaxFilingService
	taxFilingBatchService *service.TaxFilingBatchService
	taxFilingController   *controller.TaxFilingController
	mockTaxFilingClient   *MockTaxFilingClient
	redisService          *service.RedisService
	notificationService   *service.TaxFilingNotificationService

	// 测试数据
	testEnterprise *model.Enterprise
	testProvince   *model.TaxFilingProvince
}

// MockTaxFilingClient 模拟税务申报客户端
type MockTaxFilingClient struct {
	submissions map[string]*client.TaxSubmissionResponse
	statuses    map[string]*client.SubmissionStatusResponse
}

func NewMockTaxFilingClient() *MockTaxFilingClient {
	return &MockTaxFilingClient{
		submissions: make(map[string]*client.TaxSubmissionResponse),
		statuses:    make(map[string]*client.SubmissionStatusResponse),
	}
}

func (m *MockTaxFilingClient) SubmitTaxReturn(ctx context.Context, request *client.TaxSubmissionRequest) (*client.TaxSubmissionResponse, error) {
	externalID := fmt.Sprintf("ext_%d", time.Now().UnixNano())
	response := &client.TaxSubmissionResponse{
		SubmissionID: externalID,
		Status:       "submitted",
		Message:      "申报已提交",
		ExternalID:   &externalID,
	}

	m.submissions[externalID] = response
	m.statuses[externalID] = &client.SubmissionStatusResponse{
		SubmissionID: externalID,
		Status:       "submitted",
		ExternalID:   &externalID,
		SubmittedAt:  &[]time.Time{time.Now()}[0],
	}

	return response, nil
}

func (m *MockTaxFilingClient) GetSubmissionStatus(ctx context.Context, submissionID string) (*client.SubmissionStatusResponse, error) {
	if status, exists := m.statuses[submissionID]; exists {
		return status, nil
	}
	return nil, fmt.Errorf("submission not found")
}

func (m *MockTaxFilingClient) BatchSubmit(ctx context.Context, request *client.BatchSubmissionRequest) (*client.BatchSubmissionResponse, error) {
	response := &client.BatchSubmissionResponse{
		BatchID:               fmt.Sprintf("batch_%d", time.Now().UnixNano()),
		TotalSubmissions:      len(request.Submissions),
		SuccessfulSubmissions: make([]string, 0),
		FailedSubmissions:     make([]client.FailedSubmission, 0),
	}

	for i, submission := range request.Submissions {
		externalID := fmt.Sprintf("ext_%d_%d", time.Now().UnixNano(), i)
		response.SuccessfulSubmissions = append(response.SuccessfulSubmissions, externalID)

		m.statuses[externalID] = &client.SubmissionStatusResponse{
			SubmissionID: externalID,
			Status:       "submitted",
			ExternalID:   &externalID,
			SubmittedAt:  &[]time.Time{time.Now()}[0],
		}
	}

	return response, nil
}

func (m *MockTaxFilingClient) RetrySubmission(ctx context.Context, submissionID string) (*client.TaxSubmissionResponse, error) {
	return m.SubmitTaxReturn(ctx, &client.TaxSubmissionRequest{})
}

func (m *MockTaxFilingClient) CancelSubmission(ctx context.Context, submissionID string) error {
	if status, exists := m.statuses[submissionID]; exists {
		status.Status = "cancelled"
		return nil
	}
	return fmt.Errorf("submission not found")
}

func (m *MockTaxFilingClient) ValidateSubmission(ctx context.Context, request *client.ValidationRequest) (*client.ValidationResponse, error) {
	return &client.ValidationResponse{
		IsValid: true,
		Errors:  []client.ValidationError{},
	}, nil
}

func (m *MockTaxFilingClient) GetProvinceInfo(ctx context.Context, provinceCode string) (*client.ProvinceInfo, error) {
	return &client.ProvinceInfo{
		Code:   provinceCode,
		Name:   "测试省份",
		Status: "active",
	}, nil
}

func (m *MockTaxFilingClient) GetSupportedProvinces(ctx context.Context) ([]client.ProvinceInfo, error) {
	return []client.ProvinceInfo{
		{Code: "BJ", Name: "北京", Status: "active"},
		{Code: "SH", Name: "上海", Status: "active"},
	}, nil
}

func (m *MockTaxFilingClient) HealthCheck(ctx context.Context) (*client.HealthCheckResponse, error) {
	return &client.HealthCheckResponse{
		Status:    "healthy",
		Service:   "tax-filing-service",
		Version:   "1.0.0",
		Timestamp: time.Now().Format(time.RFC3339),
	}, nil
}

func (m *MockTaxFilingClient) GetCircuitBreakerStatus() string {
	return "CLOSED"
}

func (m *MockTaxFilingClient) ResetCircuitBreaker() {
	// Mock implementation
}

// SetupSuite 设置测试套件
func (suite *TaxFilingIntegrationTestSuite) SetupSuite() {
	// 设置日志
	suite.logger, _ = zap.NewDevelopment()

	// 设置内存数据库
	var err error
	suite.db, err = gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 自动迁移
	err = suite.db.AutoMigrate(
		&model.Enterprise{},
		&model.TaxFilingProvince{},
		&model.TaxFilingSubmission{},
		&model.TaxFilingBatch{},
		&model.TaxFilingStatusHistory{},
		&model.TaxFilingCallback{},
	)
	suite.Require().NoError(err)

	// 创建模拟客户端
	suite.mockTaxFilingClient = NewMockTaxFilingClient()

	// 创建Redis服务（使用模拟实现）
	suite.redisService = &service.RedisService{} // 这里应该使用模拟实现

	// 创建通知服务
	suite.notificationService = service.NewTaxFilingNotificationService(
		suite.db,
		suite.logger,
		suite.redisService,
		nil, // 使用模拟通知服务
	)

	// 创建服务
	suite.taxFilingService = service.NewTaxFilingService(
		suite.db,
		suite.logger,
		suite.mockTaxFilingClient,
		suite.notificationService,
		suite.redisService,
	)

	suite.taxFilingBatchService = service.NewTaxFilingBatchService(
		suite.db,
		suite.logger,
		suite.taxFilingService,
		suite.mockTaxFilingClient,
		suite.notificationService,
		suite.redisService,
	)

	// 创建控制器
	suite.taxFilingController = controller.NewTaxFilingController(
		suite.taxFilingService,
		suite.taxFilingBatchService,
		suite.logger,
	)

	// 设置路由
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()

	// 设置路由
	api := suite.router.Group("/api")
	taxFilingGroup := api.Group("/tax-filing")
	{
		submissions := taxFilingGroup.Group("/submissions")
		{
			submissions.POST("", suite.taxFilingController.CreateSubmission)
			submissions.GET("", suite.taxFilingController.ListSubmissions)
			submissions.GET("/:id", suite.taxFilingController.GetSubmission)
			submissions.POST("/:id/submit", suite.taxFilingController.SubmitToTaxBureau)
			submissions.POST("/:id/sync", suite.taxFilingController.SyncSubmissionStatus)
			submissions.POST("/:id/retry", suite.taxFilingController.RetrySubmission)
			submissions.POST("/:id/cancel", suite.taxFilingController.CancelSubmission)
		}

		batches := taxFilingGroup.Group("/batches")
		{
			batches.POST("", suite.taxFilingController.CreateBatch)
			batches.GET("/:id", suite.taxFilingController.GetBatch)
			batches.POST("/:id/process", suite.taxFilingController.ProcessBatch)
		}
	}
}

// SetupTest 设置每个测试
func (suite *TaxFilingIntegrationTestSuite) SetupTest() {
	// 创建测试企业
	suite.testEnterprise = &model.Enterprise{
		ID:                  "test_enterprise_001",
		Name:                "测试企业有限公司",
		TaxID:               "91110000123456789X",
		RegistrationNumber:  "110000123456789",
		LegalRepresentative: "张三",
		Address:             "北京市朝阳区测试街道123号",
		Phone:               "010-12345678",
		Email:               "<EMAIL>",
		Province:            "BJ",
		City:                "北京市",
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}
	suite.db.Create(suite.testEnterprise)

	// 创建测试省份配置
	suite.testProvince = &model.TaxFilingProvince{
		Code:       "BJ",
		Name:       "北京",
		Status:     model.TaxFilingProvinceStatusActive,
		ServiceURL: "http://localhost:8080/api/bj",
		APIKey:     "test_api_key",
		Timeout:    30,
		MaxRetries: 3,
		Features:   []string{"monthly", "quarterly", "yearly"},
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}
	suite.db.Create(suite.testProvince)
}

// TearDownTest 清理每个测试
func (suite *TaxFilingIntegrationTestSuite) TearDownTest() {
	// 清理测试数据
	suite.db.Where("1 = 1").Delete(&model.TaxFilingSubmission{})
	suite.db.Where("1 = 1").Delete(&model.TaxFilingBatch{})
	suite.db.Where("1 = 1").Delete(&model.TaxFilingStatusHistory{})
	suite.db.Where("1 = 1").Delete(&model.TaxFilingCallback{})
	suite.db.Where("1 = 1").Delete(&model.Enterprise{})
	suite.db.Where("1 = 1").Delete(&model.TaxFilingProvince{})
}

// TestCreateSubmission 测试创建申报
func (suite *TaxFilingIntegrationTestSuite) TestCreateSubmission() {
	// 准备请求数据
	request := model.TaxFilingSubmissionCreateRequest{
		EnterpriseID:        suite.testEnterprise.ID,
		ProvinceCode:        suite.testProvince.Code,
		SubmissionType:      model.TaxFilingTypeManual,
		CompanyName:         "测试企业有限公司",
		TaxID:               "91110000123456789X",
		RegistrationNumber:  "110000123456789",
		LegalRepresentative: "张三",
		CompanyAddress:      "北京市朝阳区测试街道123号",
		TaxYear:             2024,
		TaxMonth:            &[]int{3}[0],
		PeriodType:          model.TaxFilingPeriodMonthly,
		TaxData: []model.TaxData{
			{
				TaxType:       "增值税",
				TaxableAmount: 100000.00,
				TaxRate:       0.13,
				TaxAmount:     13000.00,
				Deductions:    0.00,
				Credits:       0.00,
				FinalAmount:   13000.00,
			},
		},
	}

	// 发送请求
	jsonData, _ := json.Marshal(request)
	req, _ := http.NewRequest("POST", "/api/tax-filing/submissions", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "success", response["status"])

	// 验证数据库中的记录
	var submission model.TaxFilingSubmission
	err = suite.db.First(&submission, "company_name = ?", "测试企业有限公司").Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), model.TaxFilingStatusPending, submission.Status)
	assert.Equal(suite.T(), suite.testEnterprise.ID, submission.EnterpriseID)
}

// TestSubmitToTaxBureau 测试提交到税务局
func (suite *TaxFilingIntegrationTestSuite) TestSubmitToTaxBureau() {
	// 先创建一个申报
	submission := &model.TaxFilingSubmission{
		ID:                  model.GenerateID(),
		EnterpriseID:        suite.testEnterprise.ID,
		ProvinceCode:        suite.testProvince.Code,
		ProvinceName:        suite.testProvince.Name,
		SubmissionType:      model.TaxFilingTypeManual,
		CompanyName:         "测试企业有限公司",
		TaxID:               "91110000123456789X",
		RegistrationNumber:  "110000123456789",
		LegalRepresentative: "张三",
		CompanyAddress:      "北京市朝阳区测试街道123号",
		TaxYear:             2024,
		TaxMonth:            &[]int{3}[0],
		PeriodType:          model.TaxFilingPeriodMonthly,
		TaxData: []model.TaxData{
			{
				TaxType:       "增值税",
				TaxableAmount: 100000.00,
				TaxRate:       0.13,
				TaxAmount:     13000.00,
				FinalAmount:   13000.00,
			},
		},
		Status:    model.TaxFilingStatusPending,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	suite.db.Create(submission)

	// 提交到税务局
	req, _ := http.NewRequest("POST", fmt.Sprintf("/api/tax-filing/submissions/%s/submit", submission.ID), nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// 验证状态更新
	var updatedSubmission model.TaxFilingSubmission
	err := suite.db.First(&updatedSubmission, "id = ?", submission.ID).Error
	assert.NoError(suite.T(), err)
	assert.NotEqual(suite.T(), model.TaxFilingStatusPending, updatedSubmission.Status)
	assert.NotNil(suite.T(), updatedSubmission.ExternalID)
}

// TestBatchProcessing 测试批次处理
func (suite *TaxFilingIntegrationTestSuite) TestBatchProcessing() {
	// 创建批次
	batchRequest := model.TaxFilingBatchCreateRequest{
		Name:         "测试批次",
		Description:  &[]string{"测试批次描述"}[0],
		ProvinceCode: suite.testProvince.Code,
		EnterpriseID: suite.testEnterprise.ID,
	}

	jsonData, _ := json.Marshal(batchRequest)
	req, _ := http.NewRequest("POST", "/api/tax-filing/batches", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	data := response["data"].(map[string]interface{})
	batchID := data["id"].(string)

	// 添加申报到批次
	submissionRequests := []model.TaxFilingSubmissionCreateRequest{
		{
			EnterpriseID:        suite.testEnterprise.ID,
			ProvinceCode:        suite.testProvince.Code,
			SubmissionType:      model.TaxFilingTypeBatch,
			CompanyName:         "测试企业1",
			TaxID:               "91110000123456781",
			RegistrationNumber:  "110000123456781",
			LegalRepresentative: "张三",
			CompanyAddress:      "北京市朝阳区测试街道123号",
			TaxYear:             2024,
			TaxMonth:            &[]int{3}[0],
			PeriodType:          model.TaxFilingPeriodMonthly,
			TaxData: []model.TaxData{
				{
					TaxType:       "增值税",
					TaxableAmount: 100000.00,
					TaxRate:       0.13,
					TaxAmount:     13000.00,
					FinalAmount:   13000.00,
				},
			},
		},
	}

	submissionIDs, errors := suite.taxFilingBatchService.AddSubmissionsToBatch(
		context.Background(),
		batchID,
		submissionRequests,
	)

	assert.Empty(suite.T(), errors)
	assert.Len(suite.T(), submissionIDs, 1)

	// 处理批次
	req, _ = http.NewRequest("POST", fmt.Sprintf("/api/tax-filing/batches/%s/process", batchID), nil)
	w = httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// 等待批次处理完成（在实际测试中可能需要更复杂的同步机制）
	time.Sleep(100 * time.Millisecond)

	// 验证批次状态
	var batch model.TaxFilingBatch
	err = suite.db.First(&batch, "id = ?", batchID).Error
	assert.NoError(suite.T(), err)
	// 注意：由于是异步处理，状态可能还是processing
}

// TestErrorHandling 测试错误处理
func (suite *TaxFilingIntegrationTestSuite) TestErrorHandling() {
	// 测试无效的企业ID
	request := model.TaxFilingSubmissionCreateRequest{
		EnterpriseID: "invalid_enterprise_id",
		ProvinceCode: suite.testProvince.Code,
		CompanyName:  "测试企业",
	}

	jsonData, _ := json.Marshal(request)
	req, _ := http.NewRequest("POST", "/api/tax-filing/submissions", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusInternalServerError, w.Code)

	// 测试无效的省份代码
	request.EnterpriseID = suite.testEnterprise.ID
	request.ProvinceCode = "INVALID"

	jsonData, _ = json.Marshal(request)
	req, _ = http.NewRequest("POST", "/api/tax-filing/submissions", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w = httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusInternalServerError, w.Code)
}

// TestConcurrentSubmissions 测试并发申报
func (suite *TaxFilingIntegrationTestSuite) TestConcurrentSubmissions() {
	const numSubmissions = 10

	// 创建多个申报
	var submissions []*model.TaxFilingSubmission
	for i := 0; i < numSubmissions; i++ {
		submission := &model.TaxFilingSubmission{
			ID:                  model.GenerateID(),
			EnterpriseID:        suite.testEnterprise.ID,
			ProvinceCode:        suite.testProvince.Code,
			ProvinceName:        suite.testProvince.Name,
			SubmissionType:      model.TaxFilingTypeManual,
			CompanyName:         fmt.Sprintf("测试企业%d", i),
			TaxID:               fmt.Sprintf("9111000012345678%02d", i),
			RegistrationNumber:  fmt.Sprintf("11000012345678%d", i),
			LegalRepresentative: "张三",
			CompanyAddress:      "北京市朝阳区测试街道123号",
			TaxYear:             2024,
			TaxMonth:            &[]int{3}[0],
			PeriodType:          model.TaxFilingPeriodMonthly,
			TaxData: []model.TaxData{
				{
					TaxType:       "增值税",
					TaxableAmount: 100000.00,
					TaxRate:       0.13,
					TaxAmount:     13000.00,
					FinalAmount:   13000.00,
				},
			},
			Status:    model.TaxFilingStatusPending,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		suite.db.Create(submission)
		submissions = append(submissions, submission)
	}

	// 并发提交
	ctx := context.Background()
	for _, submission := range submissions {
		go func(sub *model.TaxFilingSubmission) {
			err := suite.taxFilingService.SubmitToTaxBureau(ctx, sub.ID)
			assert.NoError(suite.T(), err)
		}(submission)
	}

	// 等待所有提交完成
	time.Sleep(500 * time.Millisecond)

	// 验证所有申报都已提交
	var count int64
	suite.db.Model(&model.TaxFilingSubmission{}).
		Where("status != ?", model.TaxFilingStatusPending).
		Count(&count)

	assert.Equal(suite.T(), int64(numSubmissions), count)
}

// 运行测试套件
func TestTaxFilingIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(TaxFilingIntegrationTestSuite))
}
