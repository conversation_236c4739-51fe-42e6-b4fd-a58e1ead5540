// Package common provides shared test utilities and models for the tax management system tests.
// It includes common test structures, mock objects, and helper functions used across different test files.
package common

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"time"

	"gorm.io/gorm"
)

// TestUser is a simplified user model for testing
type TestUser struct {
	ID                string         `gorm:"primaryKey;type:varchar(36)" json:"id"`
	UserName          string         `gorm:"type:varchar(50);not null;uniqueIndex" json:"userName"`
	Email             string         `gorm:"type:varchar(100);uniqueIndex" json:"email"`
	Password          string         `gorm:"type:varchar(255);not null" json:"-"`
	IsActive          bool           `gorm:"default:true" json:"isActive"`
	ResetToken        *string        `gorm:"type:varchar(255)" json:"-"`
	ResetTokenExpires *time.Time     `json:"-"`
	CreatedAt         time.Time      `json:"createdAt"`
	UpdatedAt         time.Time      `json:"updatedAt"`
	DeletedAt         gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName returns the table name for TestUser
func (TestUser) TableName() string {
	return "users"
}

// TestResult represents the result of a test
type TestResult struct {
	TestName string
	Success  bool
	Message  string
	Duration time.Duration
}

// APIClient provides methods for making HTTP requests in tests
type APIClient struct {
	BaseURL string
	Token   string
	Client  interface{} // Can be *http.Client or mock client
}

// NewAPIClient creates a new API client for testing
func NewAPIClient(baseURL, token string) *APIClient {
	return &APIClient{
		BaseURL: baseURL,
		Token:   token,
		Client:  &http.Client{Timeout: 30 * time.Second},
	}
}

// MakeRequest makes an HTTP request with authentication
func (c *APIClient) MakeRequest(method, endpoint string, body interface{}) (*http.Response, error) {
	var reqBody io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		reqBody = bytes.NewBuffer(jsonBody)
	}

	req, err := http.NewRequest(method, c.BaseURL+endpoint, reqBody)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	if c.Token != "" {
		req.Header.Set("Authorization", "Bearer "+c.Token)
	}

	if httpClient, ok := c.Client.(*http.Client); ok {
		return httpClient.Do(req)
	}

	// Fallback for mock clients
	return nil, nil
}

// Constants for testing
const (
	TestBaseURL = "http://localhost:8081"
	TestToken   = "test-token" // In actual tests, use real JWT token
)
