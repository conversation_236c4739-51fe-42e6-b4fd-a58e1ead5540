package test

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"backend/config"
	"backend/model"
	"backend/mq"
	"backend/service"
)

// TaxFilingMQTestSuite 税务申报消息队列测试套件
type TaxFilingMQTestSuite struct {
	suite.Suite
	db                  *gorm.DB
	logger              *zap.Logger
	mqClient            *MockRocketMQClient
	mqService           *service.TaxFilingMQService
	taxFilingService    *service.TaxFilingService
	batchService        *service.TaxFilingBatchService
	notificationService *service.TaxFilingNotificationService
}

// MockRocketMQClient 模拟RocketMQ客户端
type MockRocketMQClient struct {
	mock.Mock
	isRunning bool
	messages  []MockMessage
}

type MockMessage struct {
	Topic      string
	Tag        string
	Key        string
	Body       []byte
	Properties map[string]string
}

func (m *MockRocketMQClient) Start() error {
	args := m.Called()
	if args.Error(0) == nil {
		m.isRunning = true
	}
	return args.Error(0)
}

func (m *MockRocketMQClient) Stop() error {
	args := m.Called()
	if args.Error(0) == nil {
		m.isRunning = false
	}
	return args.Error(0)
}

func (m *MockRocketMQClient) SendMessage(ctx context.Context, msg *mq.Message) (*primitive.SendResult, error) {
	args := m.Called(ctx, msg)

	// 记录发送的消息
	m.messages = append(m.messages, MockMessage{
		Topic:      msg.Topic,
		Tag:        msg.Tag,
		Key:        msg.Key,
		Body:       msg.Body,
		Properties: msg.Properties,
	})

	return args.Get(0).(*primitive.SendResult), args.Error(1)
}

func (m *MockRocketMQClient) SendAsyncMessage(ctx context.Context, msg *mq.Message, callback func(*primitive.SendResult, error)) error {
	args := m.Called(ctx, msg, callback)
	return args.Error(0)
}

func (m *MockRocketMQClient) SendBatchMessages(ctx context.Context, messages []*mq.Message) (*primitive.SendResult, error) {
	args := m.Called(ctx, messages)
	return args.Get(0).(*primitive.SendResult), args.Error(1)
}

func (m *MockRocketMQClient) Subscribe(topic, tag string, handler mq.MessageHandler) error {
	args := m.Called(topic, tag, handler)
	return args.Error(0)
}

func (m *MockRocketMQClient) Unsubscribe(topic string) error {
	args := m.Called(topic)
	return args.Error(0)
}

func (m *MockRocketMQClient) IsRunning() bool {
	return m.isRunning
}

func (m *MockRocketMQClient) GetProducerStatus() map[string]interface{} {
	return map[string]interface{}{
		"is_running": m.isRunning,
		"group":      "test_producer_group",
	}
}

func (m *MockRocketMQClient) GetConsumerStatus() map[string]interface{} {
	return map[string]interface{}{
		"is_running": m.isRunning,
		"group":      "test_consumer_group",
	}
}

// SetupSuite 设置测试套件
func (suite *TaxFilingMQTestSuite) SetupSuite() {
	// 设置日志
	suite.logger, _ = zap.NewDevelopment()

	// 设置内存数据库
	var err error
	suite.db, err = gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 自动迁移
	err = suite.db.AutoMigrate(
		&model.Enterprise{},
		&model.TaxFilingProvince{},
		&model.TaxFilingSubmission{},
		&model.TaxFilingBatch{},
		&model.TaxFilingStatusHistory{},
		&model.TaxFilingCallback{},
	)
	suite.Require().NoError(err)

	// 创建模拟客户端
	suite.mqClient = new(MockRocketMQClient)

	// 创建服务实例
	suite.taxFilingService = &service.TaxFilingService{} // 简化的服务实例
	suite.batchService = &service.TaxFilingBatchService{}
	suite.notificationService = &service.TaxFilingNotificationService{}

	// 创建消息队列服务
	suite.mqService = service.NewTaxFilingMQService(
		suite.db,
		suite.logger,
		suite.mqClient,
		suite.taxFilingService,
		suite.batchService,
		suite.notificationService,
	)
}

// SetupTest 设置每个测试
func (suite *TaxFilingMQTestSuite) SetupTest() {
	// 重置模拟对象
	suite.mqClient.ExpectedCalls = nil
	suite.mqClient.messages = nil
	suite.mqClient.isRunning = false
}

// TestMQServiceStart 测试消息队列服务启动
func (suite *TaxFilingMQTestSuite) TestMQServiceStart() {
	// 设置模拟期望
	suite.mqClient.On("Start").Return(nil)
	suite.mqClient.On("Subscribe", mock.AnythingOfType("string"), mock.AnythingOfType("string"), mock.AnythingOfType("mq.MessageHandler")).Return(nil).Times(5)

	// 执行测试
	err := suite.mqService.Start(context.Background())

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), suite.mqService.IsRunning())

	// 验证模拟调用
	suite.mqClient.AssertExpectations(suite.T())
}

// TestMQServiceStop 测试消息队列服务停止
func (suite *TaxFilingMQTestSuite) TestMQServiceStop() {
	// 先启动服务
	suite.mqClient.On("Start").Return(nil)
	suite.mqClient.On("Subscribe", mock.AnythingOfType("string"), mock.AnythingOfType("string"), mock.AnythingOfType("mq.MessageHandler")).Return(nil).Times(5)
	err := suite.mqService.Start(context.Background())
	suite.Require().NoError(err)

	// 设置停止期望
	suite.mqClient.On("Stop").Return(nil)

	// 执行测试
	err = suite.mqService.Stop()

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), suite.mqService.IsRunning())

	// 验证模拟调用
	suite.mqClient.AssertExpectations(suite.T())
}

// TestSendSubmissionMessage 测试发送申报消息
func (suite *TaxFilingMQTestSuite) TestSendSubmissionMessage() {
	// 设置模拟期望
	expectedResult := &primitive.SendResult{
		MsgID: "test_msg_id_001",
	}
	suite.mqClient.On("SendMessage", mock.Anything, mock.AnythingOfType("*mq.Message")).Return(expectedResult, nil)

	// 执行测试
	submissionID := "test_submission_001"
	action := "submit"
	data := map[string]interface{}{
		"priority": "high",
		"province": "BJ",
	}

	err := suite.mqService.SendSubmissionMessage(context.Background(), submissionID, action, data)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), suite.mqClient.messages, 1)

	// 验证消息内容
	sentMessage := suite.mqClient.messages[0]
	assert.Equal(suite.T(), service.TopicTaxFilingSubmission, sentMessage.Topic)
	assert.Equal(suite.T(), service.TagSubmissionSubmit, sentMessage.Tag)
	assert.Equal(suite.T(), submissionID, sentMessage.Key)
	assert.Equal(suite.T(), submissionID, sentMessage.Properties["submission_id"])
	assert.Equal(suite.T(), action, sentMessage.Properties["action"])

	// 验证消息体
	var submissionMsg service.SubmissionMessage
	err = json.Unmarshal(sentMessage.Body, &submissionMsg)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), submissionID, submissionMsg.SubmissionID)
	assert.Equal(suite.T(), action, submissionMsg.Action)
	assert.Equal(suite.T(), data, submissionMsg.Data)

	// 验证模拟调用
	suite.mqClient.AssertExpectations(suite.T())
}

// TestSendBatchMessage 测试发送批次消息
func (suite *TaxFilingMQTestSuite) TestSendBatchMessage() {
	// 设置模拟期望
	expectedResult := &primitive.SendResult{
		MsgID: "test_msg_id_002",
	}
	suite.mqClient.On("SendMessage", mock.Anything, mock.AnythingOfType("*mq.Message")).Return(expectedResult, nil)

	// 执行测试
	batchID := "test_batch_001"
	action := "process"
	data := map[string]interface{}{
		"total_submissions": 100,
		"province":          "SH",
	}

	err := suite.mqService.SendBatchMessage(context.Background(), batchID, action, data)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), suite.mqClient.messages, 1)

	// 验证消息内容
	sentMessage := suite.mqClient.messages[0]
	assert.Equal(suite.T(), service.TopicTaxFilingBatch, sentMessage.Topic)
	assert.Equal(suite.T(), service.TagBatchProcess, sentMessage.Tag)
	assert.Equal(suite.T(), batchID, sentMessage.Key)
	assert.Equal(suite.T(), batchID, sentMessage.Properties["batch_id"])
	assert.Equal(suite.T(), action, sentMessage.Properties["action"])

	// 验证消息体
	var batchMsg service.BatchMessage
	err = json.Unmarshal(sentMessage.Body, &batchMsg)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), batchID, batchMsg.BatchID)
	assert.Equal(suite.T(), action, batchMsg.Action)
	assert.Equal(suite.T(), data, batchMsg.Data)

	// 验证模拟调用
	suite.mqClient.AssertExpectations(suite.T())
}

// TestSendSyncMessage 测试发送同步消息
func (suite *TaxFilingMQTestSuite) TestSendSyncMessage() {
	// 设置模拟期望
	expectedResult := &primitive.SendResult{
		MsgID: "test_msg_id_003",
	}
	suite.mqClient.On("SendMessage", mock.Anything, mock.AnythingOfType("*mq.Message")).Return(expectedResult, nil)

	// 执行测试
	syncType := "submission"
	ids := []string{"sub_001", "sub_002", "sub_003"}
	data := map[string]interface{}{
		"force_sync": true,
	}

	err := suite.mqService.SendSyncMessage(context.Background(), syncType, ids, data)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), suite.mqClient.messages, 1)

	// 验证消息内容
	sentMessage := suite.mqClient.messages[0]
	assert.Equal(suite.T(), service.TopicTaxFilingSync, sentMessage.Topic)
	assert.Equal(suite.T(), service.TagSyncStatus, sentMessage.Tag)
	assert.Equal(suite.T(), syncType, sentMessage.Properties["sync_type"])
	assert.Equal(suite.T(), "3", sentMessage.Properties["count"])

	// 验证消息体
	var syncMsg service.SyncMessage
	err = json.Unmarshal(sentMessage.Body, &syncMsg)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), syncType, syncMsg.Type)
	assert.Equal(suite.T(), ids, syncMsg.IDs)
	assert.Equal(suite.T(), data, syncMsg.Data)

	// 验证模拟调用
	suite.mqClient.AssertExpectations(suite.T())
}

// TestSendNotificationMessage 测试发送通知消息
func (suite *TaxFilingMQTestSuite) TestSendNotificationMessage() {
	// 设置模拟期望
	expectedResult := &primitive.SendResult{
		MsgID: "test_msg_id_004",
	}
	suite.mqClient.On("SendMessage", mock.Anything, mock.AnythingOfType("*mq.Message")).Return(expectedResult, nil)

	// 执行测试
	notificationType := "email"
	target := "<EMAIL>"
	subject := "税务申报状态通知"
	content := "您的税务申报已成功提交"
	data := map[string]interface{}{
		"submission_id": "sub_001",
		"status":        "submitted",
	}

	err := suite.mqService.SendNotificationMessage(context.Background(), notificationType, target, subject, content, data)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), suite.mqClient.messages, 1)

	// 验证消息内容
	sentMessage := suite.mqClient.messages[0]
	assert.Equal(suite.T(), service.TopicTaxFilingNotification, sentMessage.Topic)
	assert.Equal(suite.T(), service.TagNotificationEmail, sentMessage.Tag)
	assert.Equal(suite.T(), notificationType, sentMessage.Properties["notification_type"])
	assert.Equal(suite.T(), target, sentMessage.Properties["target"])

	// 验证消息体
	var notificationMsg service.NotificationMessage
	err = json.Unmarshal(sentMessage.Body, &notificationMsg)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), notificationType, notificationMsg.Type)
	assert.Equal(suite.T(), target, notificationMsg.Target)
	assert.Equal(suite.T(), subject, notificationMsg.Subject)
	assert.Equal(suite.T(), content, notificationMsg.Content)
	assert.Equal(suite.T(), data, notificationMsg.Data)

	// 验证模拟调用
	suite.mqClient.AssertExpectations(suite.T())
}

// TestGetStatus 测试获取服务状态
func (suite *TaxFilingMQTestSuite) TestGetStatus() {
	// 设置模拟状态
	suite.mqClient.isRunning = true

	// 执行测试
	status := suite.mqService.GetStatus()

	// 验证结果
	assert.True(suite.T(), status["is_running"].(bool))
	assert.NotNil(suite.T(), status["producer"])
	assert.NotNil(suite.T(), status["consumer"])

	producerStatus := status["producer"].(map[string]interface{})
	assert.True(suite.T(), producerStatus["is_running"].(bool))
	assert.Equal(suite.T(), "test_producer_group", producerStatus["group"])

	consumerStatus := status["consumer"].(map[string]interface{})
	assert.True(suite.T(), consumerStatus["is_running"].(bool))
	assert.Equal(suite.T(), "test_consumer_group", consumerStatus["group"])
}

// TestRocketMQConfig 测试RocketMQ配置
func (suite *TaxFilingMQTestSuite) TestRocketMQConfig() {
	// 测试默认配置
	config := config.GetRocketMQConfig()
	assert.NotNil(suite.T(), config)
	assert.NotEmpty(suite.T(), config.NameServers)
	assert.NotEmpty(suite.T(), config.ProducerGroup)
	assert.NotEmpty(suite.T(), config.ConsumerGroup)
	assert.Greater(suite.T(), config.SendTimeout, time.Duration(0))
	assert.GreaterOrEqual(suite.T(), config.RetryTimes, 0)
	assert.Greater(suite.T(), config.MaxMessageSize, 0)

	// 测试配置验证
	err := config.ValidateConfig(config)
	assert.NoError(suite.T(), err)

	// 测试环境特定配置
	devConfig := config.GetEnvironmentSpecificConfig("development")
	assert.Contains(suite.T(), devConfig.ProducerGroup, "dev")
	assert.Contains(suite.T(), devConfig.ConsumerGroup, "dev")
	assert.Equal(suite.T(), "dev", devConfig.Namespace)

	prodConfig := config.GetEnvironmentSpecificConfig("production")
	assert.Contains(suite.T(), prodConfig.ProducerGroup, "prod")
	assert.Contains(suite.T(), prodConfig.ConsumerGroup, "prod")
	assert.Equal(suite.T(), "prod", prodConfig.Namespace)
}

// 运行测试套件
func TestTaxFilingMQTestSuite(t *testing.T) {
	suite.Run(t, new(TaxFilingMQTestSuite))
}
