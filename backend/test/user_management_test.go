// Package test provides test utilities and test cases for the tax management system.
// It includes unit tests, integration tests, and test helpers for various components.
package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"backend/api"
	"backend/config"
	"backend/model"
	"backend/service"
	"backend/util"
)

// stringPtr 辅助函数：返回字符串指针
func stringPtr(s string) *string {
	return &s
}

// setupTestDB creates and configures an in-memory SQLite database for testing.
// It automatically migrates all required models and returns a ready-to-use database connection.
func setupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// 自动迁移
	db.AutoMigrate(
		&model.User{},
		&model.Enterprise{},
		&model.Role{},
		&model.Permission{},
		&model.RolePermission{},
		&model.EnterpriseUser{},
		&model.UserPermission{},
		&model.PermissionLog{},
	)

	return db
}

// setupTestData 设置测试数据
func setupTestData(db *gorm.DB) {
	// 创建测试用户
	users := []model.User{
		{
			ID:           "user1",
			UserName:     "testuser1",
			Email:        stringPtr("<EMAIL>"),
			Phone:        "13800138001",
			PasswordHash: "$2a$10$test",
			IsActive:     true,
		},
		{
			ID:           "user2",
			UserName:     "testuser2",
			Email:        stringPtr("<EMAIL>"),
			Phone:        "***********",
			PasswordHash: "$2a$10$test",
			IsActive:     true,
		},
	}
	db.Create(&users)

	// 创建测试企业
	enterprise := model.Enterprise{
		ID:      "enterprise1",
		OwnerID: &users[0].ID,
		Name:    "测试企业",
		Status:  "active",
	}
	db.Create(&enterprise)

	// 创建测试角色
	roles := []model.Role{
		{
			ID:          "role1",
			Code:        "admin",
			Name:        "管理员",
			Description: "管理员角色",
			Type:        "business",
			IsActive:    true,
		},
		{
			ID:          "role2",
			Code:        "member",
			Name:        "普通成员",
			Description: "普通成员角色",
			Type:        "business",
			IsActive:    true,
		},
	}
	db.Create(&roles)

	// 创建测试权限
	permissions := []model.Permission{
		{
			ID:       "perm1",
			Code:     "user.view",
			Name:     "查看用户",
			Resource: "user",
			Action:   "view",
			Type:     "function",
		},
		{
			ID:       "perm2",
			Code:     "user.manage",
			Name:     "管理用户",
			Resource: "user",
			Action:   "manage",
			Type:     "function",
		},
	}
	db.Create(&permissions)

	// 创建角色权限关联
	rolePermissions := []model.RolePermission{
		{
			ID:           "rp1",
			RoleID:       roles[0].ID,
			PermissionID: permissions[0].ID,
		},
		{
			ID:           "rp2",
			RoleID:       roles[0].ID,
			PermissionID: permissions[1].ID,
		},
		{
			ID:           "rp3",
			RoleID:       roles[1].ID,
			PermissionID: permissions[0].ID,
		},
	}
	db.Create(&rolePermissions)

	// 创建企业用户关联
	enterpriseUsers := []model.EnterpriseUser{
		{
			ID:           "eu1",
			EnterpriseID: enterprise.ID,
			UserID:       users[0].ID,
			RoleID:       roles[0].ID,
			Status:       model.EnterpriseUserStatusActive,
			IsOwner:      true,
		},
		{
			ID:           "eu2",
			EnterpriseID: enterprise.ID,
			UserID:       users[1].ID,
			RoleID:       roles[1].ID,
			Status:       model.EnterpriseUserStatusActive,
			IsOwner:      false,
		},
	}
	db.Create(&enterpriseUsers)
}

// setupTestRouter 设置测试路由
func setupTestRouter(db *gorm.DB) *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()

	cfg := &config.Config{}
	permissionService := service.NewPermissionService(db, cfg)
	userManagementService := service.NewUserManagementService(db, cfg)
	userManagementHandler := api.NewUserManagementHandler(userManagementService, permissionService)

	// 模拟认证中间件
	r.Use(func(c *gin.Context) {
		c.Set("userID", "user1")
		c.Next()
	})

	api := r.Group("/api")
	{
		enterprises := api.Group("/enterprises")
		{
			enterprises.GET("/:enterpriseId/users", userManagementHandler.GetEnterpriseUsers)
			enterprises.POST("/:enterpriseId/users", userManagementHandler.InviteUser)
			enterprises.DELETE("/:enterpriseId/users/:userId", userManagementHandler.RemoveUser)
			enterprises.PUT("/:enterpriseId/users/:userId/role", userManagementHandler.UpdateUserRole)
			enterprises.POST("/:enterpriseId/transfer-ownership", userManagementHandler.TransferOwnership)
		}

		users := api.Group("/users")
		{
			users.GET("/search", userManagementHandler.SearchUsers)
			users.GET("/:userId/permissions", userManagementHandler.GetUserPermissions)
			users.GET("/:userId/roles", userManagementHandler.GetUserRoles)
		}
	}

	return r
}

// TestGetEnterpriseUsers 测试获取企业用户列表
func TestGetEnterpriseUsers(t *testing.T) {
	db := setupTestDB()
	setupTestData(db)
	router := setupTestRouter(db)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/enterprises/enterprise1/users", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)

	var response util.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 200, response.Code)
	assert.NotNil(t, response.Data)
}

// TestSearchUsers 测试搜索用户
func TestSearchUsers(t *testing.T) {
	db := setupTestDB()
	setupTestData(db)
	router := setupTestRouter(db)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/users/search?keyword=test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)

	var response util.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 200, response.Code)
	assert.NotNil(t, response.Data)
}

// TestInviteUser 测试邀请用户
func TestInviteUser(t *testing.T) {
	db := setupTestDB()
	setupTestData(db)
	router := setupTestRouter(db)

	// 创建一个新用户用于邀请
	newUser := model.User{
		ID:           "user3",
		UserName:     "testuser3",
		Email:        stringPtr("<EMAIL>"),
		Phone:        "13800138003",
		PasswordHash: "$2a$10$test",
		IsActive:     true,
	}
	db.Create(&newUser)

	inviteData := model.CreateEnterpriseUserRequest{
		UserID: "user3",
		RoleID: "role2",
	}

	jsonData, _ := json.Marshal(inviteData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/enterprises/enterprise1/users", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)

	var response util.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 200, response.Code)
}

// TestUpdateUserRole 测试更新用户角色
func TestUpdateUserRole(t *testing.T) {
	db := setupTestDB()
	setupTestData(db)
	router := setupTestRouter(db)

	updateData := map[string]string{
		"role_id": "role1",
	}

	jsonData, _ := json.Marshal(updateData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("PUT", "/api/enterprises/enterprise1/users/user2/role", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)

	var response util.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 200, response.Code)
}

// TestRemoveUser 测试移除用户
func TestRemoveUser(t *testing.T) {
	db := setupTestDB()
	setupTestData(db)
	router := setupTestRouter(db)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("DELETE", "/api/enterprises/enterprise1/users/user2", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)

	var response util.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 200, response.Code)
}

// TestPermissionCheck 测试权限检查
func TestPermissionCheck(t *testing.T) {
	db := setupTestDB()
	setupTestData(db)

	cfg := &config.Config{}
	permissionService := service.NewPermissionService(db, cfg)

	// 测试企业所有者权限
	hasPermission, err := permissionService.CheckEnterprisePermission(
		nil, "user1", "enterprise1", "user", "manage")
	assert.NoError(t, err)
	assert.True(t, hasPermission, "企业所有者应该拥有所有权限")

	// 测试普通用户权限
	hasPermission, err = permissionService.CheckEnterprisePermission(
		nil, "user2", "enterprise1", "user", "view")
	assert.NoError(t, err)
	assert.True(t, hasPermission, "普通用户应该拥有查看权限")

	// 测试无权限操作
	hasPermission, err = permissionService.CheckEnterprisePermission(
		nil, "user2", "enterprise1", "user", "manage")
	assert.NoError(t, err)
	assert.False(t, hasPermission, "普通用户不应该拥有管理权限")
}

// 运行测试的示例函数
func ExampleRunTests() {
	fmt.Println("运行用户管理模块测试...")
	fmt.Println("测试包括：")
	fmt.Println("1. 获取企业用户列表")
	fmt.Println("2. 搜索用户功能")
	fmt.Println("3. 邀请用户加入企业")
	fmt.Println("4. 更新用户角色")
	fmt.Println("5. 移除企业用户")
	fmt.Println("6. 权限检查机制")
	fmt.Println("所有测试应该通过，确保用户管理功能正常工作")
}
