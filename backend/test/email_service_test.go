// Package test provides test utilities and test cases for the tax management system.
// It includes unit tests for email service functionality.
package test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"backend/config"
	"backend/model"
	"backend/service"
)

// setupEmailServiceTest creates a test configuration for email service
func setupEmailServiceTest() config.Config {
	return config.Config{
		Email: config.EmailConfig{
			Provider:        "aliyun",
			AccessKeyID:     "test-access-key-id",
			AccessKeySecret: "test-access-key-secret",
			RegionID:        "cn-hangzhou",
			Endpoint:        "dm.aliyuncs.com",
			FromAddress:     "<EMAIL>",
			FromName:        "测试系统",
			ReplyToAddress:  "<EMAIL>",
		},
		App: config.AppConfig{
			Name: "test-app",
		},
	}
}

// TestEmailService_NewEmailService tests email service creation
func TestEmailService_NewEmailService(t *testing.T) {
	cfg := setupEmailServiceTest()
	emailService := service.NewEmailService(cfg)

	assert.NotNil(t, emailService)
}

// TestEmailService_SendPasswordResetEmail tests password reset email functionality
func TestEmailService_SendPasswordResetEmail(t *testing.T) {
	cfg := setupEmailServiceTest()
	emailService := service.NewEmailService(cfg)

	ctx := context.Background()

	t.Run("Valid parameters", func(t *testing.T) {
		// Note: This test will fail in CI/CD without valid Aliyun credentials
		// In a real environment, you would mock the HTTP client or use test credentials

		err := emailService.SendPasswordResetEmail(ctx, "<EMAIL>", "test-token-123", "testuser")

		// Since we don't have valid credentials, we expect an error
		// In a real test environment with valid credentials, this should succeed
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "阿里云邮件发送失败")
	})

	t.Run("Empty email", func(t *testing.T) {
		err := emailService.SendPasswordResetEmail(ctx, "", "test-token-123", "testuser")

		// Should still attempt to send and fail due to invalid email
		assert.Error(t, err)
	})

	t.Run("Empty token", func(t *testing.T) {
		err := emailService.SendPasswordResetEmail(ctx, "<EMAIL>", "", "testuser")

		// Should still attempt to send (token is used in email content, not validation)
		assert.Error(t, err)
	})
}

// TestEmailService_SendWelcomeEmail tests welcome email functionality
func TestEmailService_SendWelcomeEmail(t *testing.T) {
	cfg := setupEmailServiceTest()
	emailService := service.NewEmailService(cfg)

	ctx := context.Background()

	t.Run("Valid parameters", func(t *testing.T) {
		err := emailService.SendWelcomeEmail(ctx, "<EMAIL>", "testuser")

		// Since we don't have valid credentials, we expect an error
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "阿里云邮件发送失败")
	})
}

// TestEmailService_SendNotificationEmail tests notification email functionality
func TestEmailService_SendNotificationEmail(t *testing.T) {
	cfg := setupEmailServiceTest()
	emailService := service.NewEmailService(cfg)

	ctx := context.Background()

	notification := model.Notification{
		Title:   "测试通知",
		Content: "这是一个测试通知的内容",
		Type:    "info",
	}

	t.Run("Valid parameters", func(t *testing.T) {
		err := emailService.SendNotificationEmail(ctx, "<EMAIL>", notification)

		// Since we don't have valid credentials, we expect an error
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "阿里云邮件发送失败")
	})
}

// TestEmailService_SendEmail tests generic email sending functionality
func TestEmailService_SendEmail(t *testing.T) {
	cfg := setupEmailServiceTest()
	emailService := service.NewEmailService(cfg)

	ctx := context.Background()

	t.Run("HTML email", func(t *testing.T) {
		htmlBody := "<h1>测试邮件</h1><p>这是一个HTML邮件</p>"
		err := emailService.SendEmail(ctx, "<EMAIL>", "测试主题", htmlBody, true)

		// Since we don't have valid credentials, we expect an error
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "阿里云邮件发送失败")
	})

	t.Run("Text email", func(t *testing.T) {
		textBody := "这是一个纯文本邮件"
		err := emailService.SendEmail(ctx, "<EMAIL>", "测试主题", textBody, false)

		// Since we don't have valid credentials, we expect an error
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "阿里云邮件发送失败")
	})
}

// TestEmailService_Configuration tests different configuration scenarios
func TestEmailService_Configuration(t *testing.T) {
	t.Run("SMTP fallback configuration", func(t *testing.T) {
		cfg := config.Config{
			Email: config.EmailConfig{
				Provider:     "smtp",
				SMTPHost:     "smtp.example.com",
				SMTPPort:     587,
				SMTPUsername: "<EMAIL>",
				SMTPPassword: "password",
				SMTPUseTLS:   true,
				FromAddress:  "<EMAIL>",
				FromName:     "测试系统",
			},
			App: config.AppConfig{
				Name: "test-app",
			},
		}

		emailService := service.NewEmailService(cfg)
		assert.NotNil(t, emailService)

		ctx := context.Background()
		err := emailService.SendEmail(ctx, "<EMAIL>", "测试", "测试内容", false)

		// Should fail with SMTP not implemented error
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "SMTP邮件发送功能尚未实现")
	})

	t.Run("No provider configuration", func(t *testing.T) {
		cfg := config.Config{
			Email: config.EmailConfig{
				Provider: "",
			},
			App: config.AppConfig{
				Name: "test-app",
			},
		}

		emailService := service.NewEmailService(cfg)
		assert.NotNil(t, emailService)

		ctx := context.Background()
		err := emailService.SendEmail(ctx, "<EMAIL>", "测试", "测试内容", false)

		// Should fail with no provider error
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no email provider configured")
	})
}

// TestEmailService_SignatureGeneration tests Aliyun API signature generation
func TestEmailService_SignatureGeneration(t *testing.T) {
	cfg := setupEmailServiceTest()
	emailService := service.NewEmailService(cfg)

	// This is a white-box test that would require exposing internal methods
	// In a real implementation, you might want to test signature generation separately
	// or create a test-specific interface

	t.Run("Signature generation consistency", func(t *testing.T) {
		// Test that the same parameters generate the same signature
		// This would require access to internal methods or a test interface

		// For now, we'll just verify the service was created successfully
		assert.NotNil(t, emailService)
	})
}

// TestEmailService_ErrorHandling tests error handling scenarios
func TestEmailService_ErrorHandling(t *testing.T) {
	cfg := setupEmailServiceTest()
	emailService := service.NewEmailService(cfg)

	ctx := context.Background()

	t.Run("Context cancellation", func(t *testing.T) {
		// Create a cancelled context
		cancelledCtx, cancel := context.WithCancel(ctx)
		cancel()

		err := emailService.SendEmail(cancelledCtx, "<EMAIL>", "测试", "测试内容", false)

		// Should handle context cancellation
		assert.Error(t, err)
	})

	t.Run("Timeout handling", func(t *testing.T) {
		// Create a context with very short timeout
		timeoutCtx, cancel := context.WithTimeout(ctx, 1*time.Millisecond)
		defer cancel()

		err := emailService.SendEmail(timeoutCtx, "<EMAIL>", "测试", "测试内容", false)

		// Should handle timeout
		assert.Error(t, err)
	})
}

// BenchmarkEmailService_SendEmail benchmarks email sending performance
func BenchmarkEmailService_SendEmail(b *testing.B) {
	cfg := setupEmailServiceTest()
	emailService := service.NewEmailService(cfg)

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// This will fail due to invalid credentials, but we're measuring the overhead
		emailService.SendEmail(ctx, "<EMAIL>", "测试", "测试内容", false)
	}
}
