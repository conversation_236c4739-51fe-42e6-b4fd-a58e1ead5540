package integration

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"backend/model"
	"backend/service"
	"backend/util"
)

// TaxFilingIntegrationTestSuite 税务申报集成测试套件
type TaxFilingIntegrationTestSuite struct {
	suite.Suite
	db                  *gorm.DB
	logger              *zap.Logger
	router              *gin.Engine
	notificationService *service.TaxFilingNotificationService
	notificationSender  *service.NotificationSenderService
}

// SetupSuite 设置测试套件
func (suite *TaxFilingIntegrationTestSuite) SetupSuite() {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 设置日志
	suite.logger, _ = zap.NewDevelopment()

	// 设置内存数据库
	var err error
	suite.db, err = gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 自动迁移
	err = suite.db.AutoMigrate(
		&model.Enterprise{},
		&model.User{},
		&model.Notification{},
		&model.TaxFilingSubmission{},
		&model.TaxFilingBatch{},
		&model.TaxFilingProvince{},
	)
	suite.Require().NoError(err)

	// 创建服务
	suite.notificationService = service.NewTaxFilingNotificationService(suite.db, suite.logger)
	suite.notificationSender = service.NewNotificationSenderService(suite.logger)

	// 设置路由
	suite.setupRouter()

	// 插入测试数据
	suite.insertTestData()
}

// setupRouter 设置路由
func (suite *TaxFilingIntegrationTestSuite) setupRouter() {
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())

	// 添加基础路由
	suite.router.GET("/health", func(c *gin.Context) {
		util.Success(c, gin.H{
			"status":    "ok",
			"timestamp": time.Now(),
		}, "服务正常")
	})

	// 税务申报路由组
	taxFiling := suite.router.Group("/api/tax-filing")
	{
		// 申报管理
		submissions := taxFiling.Group("/submissions")
		{
			submissions.GET("", suite.listSubmissions)
			submissions.POST("", suite.createSubmission)
			submissions.GET("/:id", suite.getSubmission)
			submissions.PUT("/:id/status", suite.updateSubmissionStatus)
		}

		// 批次管理
		batches := taxFiling.Group("/batches")
		{
			batches.GET("", suite.listBatches)
			batches.POST("", suite.createBatch)
			batches.GET("/:id", suite.getBatch)
		}

		// 省份管理
		provinces := taxFiling.Group("/provinces")
		{
			provinces.GET("", suite.listProvinces)
			provinces.GET("/:code", suite.getProvince)
		}

		// 通知测试
		notifications := taxFiling.Group("/notifications")
		{
			notifications.POST("/email", suite.sendEmailNotification)
			notifications.POST("/sms", suite.sendSMSNotification)
			notifications.POST("/webhook", suite.sendWebhookNotification)
		}
	}
}

// insertTestData 插入测试数据
func (suite *TaxFilingIntegrationTestSuite) insertTestData() {
	// 插入测试企业
	enterprise := &model.Enterprise{
		ID:   "test_enterprise_001",
		Name: "测试企业有限公司",
		Code: "TEST001",
	}
	suite.db.Create(enterprise)

	// 插入测试省份
	province := &model.TaxFilingProvince{
		Code:       "BJ",
		Name:       "北京",
		Status:     "active",
		ServiceURL: "http://localhost:8000/api/bj",
		APIKey:     "test_api_key",
		Timeout:    30,
		MaxRetries: 3,
	}
	suite.db.Create(province)

	// 插入测试申报记录
	submission := &model.TaxFilingSubmission{
		ID:           "test_submission_001",
		EnterpriseID: "test_enterprise_001",
		ProvinceCode: "BJ",
		ProvinceName: "北京",
		CompanyName:  "测试企业有限公司",
		TaxID:        "*********",
		Status:       "pending",
		TaxData:      `{"tax_type":"增值税","amount":1000}`,
	}
	suite.db.Create(submission)
}

// 路由处理函数
func (suite *TaxFilingIntegrationTestSuite) listSubmissions(c *gin.Context) {
	var submissions []model.TaxFilingSubmission
	result := suite.db.Find(&submissions)
	if result.Error != nil {
		util.Error(c, http.StatusInternalServerError, "查询失败", result.Error.Error())
		return
	}

	util.Success(c, submissions, "查询成功")
}

func (suite *TaxFilingIntegrationTestSuite) createSubmission(c *gin.Context) {
	var req struct {
		EnterpriseID string `json:"enterprise_id" binding:"required"`
		ProvinceCode string `json:"province_code" binding:"required"`
		CompanyName  string `json:"company_name" binding:"required"`
		TaxID        string `json:"tax_id" binding:"required"`
		TaxData      string `json:"tax_data" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.Error(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	submission := &model.TaxFilingSubmission{
		ID:           util.GenerateID(),
		EnterpriseID: req.EnterpriseID,
		ProvinceCode: req.ProvinceCode,
		CompanyName:  req.CompanyName,
		TaxID:        req.TaxID,
		TaxData:      req.TaxData,
		Status:       "pending",
	}

	if err := suite.db.Create(submission).Error; err != nil {
		util.Error(c, http.StatusInternalServerError, "创建失败", err.Error())
		return
	}

	util.Success(c, submission, "创建成功")
}

func (suite *TaxFilingIntegrationTestSuite) getSubmission(c *gin.Context) {
	id := c.Param("id")
	var submission model.TaxFilingSubmission

	if err := suite.db.First(&submission, "id = ?", id).Error; err != nil {
		util.Error(c, http.StatusNotFound, "记录不存在", err.Error())
		return
	}

	util.Success(c, submission, "查询成功")
}

func (suite *TaxFilingIntegrationTestSuite) updateSubmissionStatus(c *gin.Context) {
	id := c.Param("id")
	var req struct {
		Status string `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.Error(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	result := suite.db.Model(&model.TaxFilingSubmission{}).
		Where("id = ?", id).
		Update("status", req.Status)

	if result.Error != nil {
		util.Error(c, http.StatusInternalServerError, "更新失败", result.Error.Error())
		return
	}

	if result.RowsAffected == 0 {
		util.Error(c, http.StatusNotFound, "记录不存在", "")
		return
	}

	util.Success(c, gin.H{"id": id, "status": req.Status}, "更新成功")
}

func (suite *TaxFilingIntegrationTestSuite) listBatches(c *gin.Context) {
	util.Success(c, []interface{}{}, "批次列表")
}

func (suite *TaxFilingIntegrationTestSuite) createBatch(c *gin.Context) {
	util.Success(c, gin.H{"id": util.GenerateID()}, "批次创建成功")
}

func (suite *TaxFilingIntegrationTestSuite) getBatch(c *gin.Context) {
	id := c.Param("id")
	util.Success(c, gin.H{"id": id}, "批次详情")
}

func (suite *TaxFilingIntegrationTestSuite) listProvinces(c *gin.Context) {
	var provinces []model.TaxFilingProvince
	suite.db.Find(&provinces)
	util.Success(c, provinces, "省份列表")
}

func (suite *TaxFilingIntegrationTestSuite) getProvince(c *gin.Context) {
	code := c.Param("code")
	var province model.TaxFilingProvince

	if err := suite.db.First(&province, "code = ?", code).Error; err != nil {
		util.Error(c, http.StatusNotFound, "省份不存在", err.Error())
		return
	}

	util.Success(c, province, "省份详情")
}

func (suite *TaxFilingIntegrationTestSuite) sendEmailNotification(c *gin.Context) {
	var req struct {
		To      string `json:"to" binding:"required"`
		Subject string `json:"subject" binding:"required"`
		Content string `json:"content" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.Error(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	err := suite.notificationSender.SendEmailNotification(c.Request.Context(), req.To, req.Subject, req.Content)
	if err != nil {
		util.Error(c, http.StatusInternalServerError, "发送失败", err.Error())
		return
	}

	util.Success(c, gin.H{"status": "sent"}, "邮件发送成功")
}

func (suite *TaxFilingIntegrationTestSuite) sendSMSNotification(c *gin.Context) {
	var req struct {
		Phone   string `json:"phone" binding:"required"`
		Content string `json:"content" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.Error(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	err := suite.notificationSender.SendSMSNotification(c.Request.Context(), req.Phone, req.Content)
	if err != nil {
		util.Error(c, http.StatusInternalServerError, "发送失败", err.Error())
		return
	}

	util.Success(c, gin.H{"status": "sent"}, "短信发送成功")
}

func (suite *TaxFilingIntegrationTestSuite) sendWebhookNotification(c *gin.Context) {
	var req struct {
		URL  string                 `json:"url" binding:"required"`
		Data map[string]interface{} `json:"data" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.Error(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	err := suite.notificationSender.SendWebhookNotification(c.Request.Context(), req.URL, req.Data)
	if err != nil {
		util.Error(c, http.StatusInternalServerError, "发送失败", err.Error())
		return
	}

	util.Success(c, gin.H{"status": "sent"}, "Webhook发送成功")
}

// 测试用例
func (suite *TaxFilingIntegrationTestSuite) TestHealthCheck() {
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/health", nil)
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(200), response["code"])
}

func (suite *TaxFilingIntegrationTestSuite) TestListSubmissions() {
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/tax-filing/submissions", nil)
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(200), response["code"])
}

func (suite *TaxFilingIntegrationTestSuite) TestCreateSubmission() {
	submissionData := map[string]interface{}{
		"enterprise_id": "test_enterprise_001",
		"province_code": "BJ",
		"company_name":  "新测试企业",
		"tax_id":        "*********",
		"tax_data":      `{"tax_type":"企业所得税","amount":2000}`,
	}

	jsonData, _ := json.Marshal(submissionData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/tax-filing/submissions", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(200), response["code"])
}

func (suite *TaxFilingIntegrationTestSuite) TestGetSubmission() {
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/tax-filing/submissions/test_submission_001", nil)
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(200), response["code"])
}

func (suite *TaxFilingIntegrationTestSuite) TestUpdateSubmissionStatus() {
	statusData := map[string]interface{}{
		"status": "submitted",
	}

	jsonData, _ := json.Marshal(statusData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("PUT", "/api/tax-filing/submissions/test_submission_001/status", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(200), response["code"])
}

func (suite *TaxFilingIntegrationTestSuite) TestListProvinces() {
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/tax-filing/provinces", nil)
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(200), response["code"])
}

func (suite *TaxFilingIntegrationTestSuite) TestSendEmailNotification() {
	emailData := map[string]interface{}{
		"to":      "<EMAIL>",
		"subject": "测试邮件",
		"content": "这是一封测试邮件",
	}

	jsonData, _ := json.Marshal(emailData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/tax-filing/notifications/email", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(200), response["code"])
}

// 运行测试套件
func TestTaxFilingIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(TaxFilingIntegrationTestSuite))
}
