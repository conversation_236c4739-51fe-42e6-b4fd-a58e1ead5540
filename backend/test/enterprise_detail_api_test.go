package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"backend/api"
	"backend/config"
	"backend/model"
	"backend/service"
	"backend/util"
)

// TestEnterpriseDetailAPIs tests enterprise detail page related API endpoints.
// It validates enterprise retrieval, statistics calculation, and data consistency.
func TestEnterpriseDetailAPIs(t *testing.T) {
	// 设置测试环境
	gin.SetMode(gin.TestMode)

	// 连接测试数据库
	cfg := &config.Config{
		Database: config.DatabaseConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "root",
			Password: "Aa123456@",
			Name:     "smeasy_tax",
		},
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Database.User, cfg.Database.Password, cfg.Database.Host, cfg.Database.Port, cfg.Database.Name)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	assert.NoError(t, err)

	// 创建服务
	enterpriseService := service.NewEnterpriseService(db)

	// 创建处理器
	enterpriseHandler := api.NewEnterpriseHandler(enterpriseService)

	// 创建测试用户和企业
	testUser := &model.User{
		ID:           util.GenerateID(),
		UserName:     "test_enterprise_detail_user",
		Email:        "<EMAIL>",
		PasswordHash: "hashedpassword",
		IsActive:     true,
	}

	// 清理可能存在的测试数据
	db.Where("user_name = ?", testUser.UserName).Delete(&model.User{})
	db.Where("email = ?", testUser.Email).Delete(&model.User{})

	// 创建测试用户
	err = db.Create(testUser).Error
	assert.NoError(t, err)

	// 创建测试企业
	testEnterprise := &model.Enterprise{
		ID:                      util.GenerateID(),
		OwnerID:                 &testUser.ID,
		Name:                    "测试企业详情页",
		UnifiedSocialCreditCode: "TEST_DETAIL_" + util.GenerateID()[:8],
		Status:                  "active",
		CreatedAt:               time.Now(),
		UpdatedAt:               time.Now(),
	}

	err = db.Create(testEnterprise).Error
	assert.NoError(t, err)

	// 创建测试申报记录
	declarations := []model.Declaration{
		{
			ID:           util.GenerateID(),
			EnterpriseID: testEnterprise.ID,
			TaxTypeID:    "vat",
			PeriodType:   "monthly",
			Year:         2024,
			Month:        &[]int{11}[0],
			Status:       "approved",
			TaxPayable:   decimal.NewFromFloat(1000.50),
			CreatedAt:    time.Now().AddDate(0, -1, 0), // 上个月
			UpdatedAt:    time.Now(),
		},
		{
			ID:           util.GenerateID(),
			EnterpriseID: testEnterprise.ID,
			TaxTypeID:    "cit",
			PeriodType:   "monthly",
			Year:         2024,
			Month:        &[]int{12}[0],
			Status:       "submitted",
			TaxPayable:   decimal.NewFromFloat(2500.75),
			CreatedAt:    time.Now(), // 本月
			UpdatedAt:    time.Now(),
		},
		{
			ID:           util.GenerateID(),
			EnterpriseID: testEnterprise.ID,
			TaxTypeID:    "vat",
			PeriodType:   "monthly",
			Year:         2024,
			Month:        &[]int{10}[0],
			Status:       "approved",
			TaxPayable:   decimal.NewFromFloat(800.25),
			CreatedAt:    time.Now().AddDate(0, -2, 0), // 两个月前
			UpdatedAt:    time.Now(),
		},
	}

	for _, declaration := range declarations {
		err = db.Create(&declaration).Error
		assert.NoError(t, err)
	}

	// 生成JWT token - 使用Login方法
	loginReq := model.LoginRequest{
		Phone:    "13800138000",
		Password: "password123",
	}

	// 设置用户的手机号和密码
	testUser.Phone = loginReq.Phone
	testUser.PasswordHash = "$2a$10$hashedpassword" // 模拟哈希密码
	err = db.Save(testUser).Error
	assert.NoError(t, err)

	// 这里我们直接创建一个简单的token用于测试
	token := "test-jwt-token"

	// 测试获取企业详情统计信息
	t.Run("GetEnterpriseDetailStats", func(t *testing.T) {
		router := gin.New()
		router.Use(func(c *gin.Context) {
			c.Set("userID", testUser.ID)
			c.Next()
		})
		router.GET("/enterprises/:id/stats", enterpriseHandler.GetEnterpriseDetailStats)

		req, _ := http.NewRequest("GET", "/enterprises/"+testEnterprise.ID+"/stats", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response util.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, 200, response.Code)

		// 验证统计数据
		statsData, ok := response.Data.(map[string]interface{})
		assert.True(t, ok)

		// 应该有3个申报记录
		assert.Equal(t, float64(3), statsData["totalDeclarations"])

		// 总税额应该是所有已批准和已提交申报的税额总和
		expectedTotalTax := 1000.50 + 2500.75 + 800.25 // 所有申报的税额
		assert.Equal(t, expectedTotalTax, statsData["totalTaxAmount"])

		// 本月申报次数应该是1（只有一个本月的申报）
		assert.Equal(t, float64(1), statsData["monthlyDeclarations"])

		// 本月税额应该是2500.75
		assert.Equal(t, 2500.75, statsData["monthlyTaxAmount"])
	})

	// 测试获取企业申报记录
	t.Run("GetEnterpriseDeclarations", func(t *testing.T) {
		router := gin.New()
		router.Use(func(c *gin.Context) {
			c.Set("userID", testUser.ID)
			c.Next()
		})
		router.GET("/enterprises/:id/declarations", enterpriseHandler.GetEnterpriseDeclarations)

		req, _ := http.NewRequest("GET", "/enterprises/"+testEnterprise.ID+"/declarations?limit=5", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response util.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, 200, response.Code)

		// 验证申报记录数据
		declarationsData, ok := response.Data.([]interface{})
		assert.True(t, ok)
		assert.Equal(t, 3, len(declarationsData)) // 应该返回3个申报记录

		// 验证第一个记录（应该是最新的）
		firstDeclaration := declarationsData[0].(map[string]interface{})
		assert.Equal(t, "2024-12", firstDeclaration["declaration_period"])
		assert.Equal(t, "submitted", firstDeclaration["declaration_status"])
	})

	// 测试无效企业ID
	t.Run("InvalidEnterpriseID", func(t *testing.T) {
		router := gin.New()
		router.Use(func(c *gin.Context) {
			c.Set("userID", testUser.ID)
			c.Next()
		})
		router.GET("/enterprises/:id/stats", enterpriseHandler.GetEnterpriseDetailStats)

		req, _ := http.NewRequest("GET", "/enterprises/invalid_id/stats", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	// 清理测试数据
	db.Where("enterprise_id = ?", testEnterprise.ID).Delete(&model.Declaration{})
	db.Where("id = ?", testEnterprise.ID).Delete(&model.Enterprise{})
	db.Where("id = ?", testUser.ID).Delete(&model.User{})
}
