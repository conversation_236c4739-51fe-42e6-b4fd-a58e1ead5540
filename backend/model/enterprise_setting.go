package model

import (
	"time"
)

// EnterpriseSetting 企业配置扩展模型
type EnterpriseSetting struct {
	ID           string    `json:"id" gorm:"primaryKey;size:36;comment:主键ID"`
	EnterpriseID string    `json:"enterpriseId" gorm:"not null;size:36;index:idx_enterprise_settings_enterprise;comment:企业ID"`
	Category     string    `json:"category" gorm:"not null;size:50;index:idx_enterprise_settings_category;comment:配置分类"`
	Key          string    `json:"key" gorm:"not null;size:100;index:idx_enterprise_settings_key;comment:配置键"`
	Value        string    `json:"value" gorm:"type:text;comment:配置值"`
	DataType     string    `json:"dataType" gorm:"not null;size:20;comment:数据类型"`
	Description  string    `json:"description" gorm:"size:500;comment:配置描述"`
	IsEncrypted  bool      `json:"isEncrypted" gorm:"default:false;comment:是否加密"`
	IsSystem     bool      `json:"isSystem" gorm:"default:false;comment:是否系统配置"`
	IsActive     bool      `json:"isActive" gorm:"default:true;comment:是否启用"`
	SortOrder    int       `json:"sortOrder" gorm:"default:0;comment:排序"`
	CreatedBy    string    `json:"createdBy" gorm:"size:36;comment:创建人ID"`
	UpdatedBy    string    `json:"updatedBy" gorm:"size:36;comment:更新人ID"`
	CreatedAt    time.Time `json:"createdAt" gorm:"comment:创建时间"`
	UpdatedAt    time.Time `json:"updatedAt" gorm:"comment:更新时间"`

	// 关联关系
	Enterprise *Enterprise `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID"`
	Creator    *User       `json:"creator,omitempty" gorm:"foreignKey:CreatedBy;references:ID"`
	Updater    *User       `json:"updater,omitempty" gorm:"foreignKey:UpdatedBy;references:ID"`
}

// 配置分类常量
const (
	SettingCategoryTax          = "tax"          // 税务配置
	SettingCategoryInvoice      = "invoice"      // 发票配置
	SettingCategoryDeclaration  = "declaration"  // 申报配置
	SettingCategoryNotification = "notification" // 通知配置
	SettingCategoryReport       = "report"       // 报表配置
	SettingCategorySystem       = "system"       // 系统配置
	SettingCategorySecurity     = "security"     // 安全配置
	SettingCategoryIntegration  = "integration"  // 集成配置
	SettingCategoryCustom       = "custom"       // 自定义配置
)

// 数据类型常量
const (
	DataTypeString  = "string"  // 字符串
	DataTypeNumber  = "number"  // 数字
	DataTypeBoolean = "boolean" // 布尔值
	DataTypeJSON    = "json"    // JSON对象
	DataTypeArray   = "array"   // 数组
	DataTypeDate    = "date"    // 日期
	DataTypeTime    = "time"    // 时间
	DataTypeFile    = "file"    // 文件路径
	DataTypeURL     = "url"     // URL地址
	DataTypeEmail   = "email"   // 邮箱地址
	DataTypePhone   = "phone"   // 电话号码
)

// 常用配置键常量
const (
	// 税务配置
	SettingKeyTaxRate          = "tax.default_rate"       // 默认税率
	SettingKeyTaxPeriod        = "tax.declaration_period" // 申报周期
	SettingKeyTaxReminder      = "tax.reminder_days"      // 提醒天数
	SettingKeyTaxAutoCalculate = "tax.auto_calculate"     // 自动计算

	// 发票配置
	SettingKeyInvoicePrefix     = "invoice.number_prefix"    // 发票号前缀
	SettingKeyInvoiceAutoVerify = "invoice.auto_verify"      // 自动验证
	SettingKeyInvoiceTemplate   = "invoice.default_template" // 默认模板

	// 申报配置
	SettingKeyDeclarationAuto   = "declaration.auto_submit" // 自动提交
	SettingKeyDeclarationBackup = "declaration.auto_backup" // 自动备份

	// 通知配置
	SettingKeyNotificationEmail = "notification.email_enabled" // 邮件通知
	SettingKeyNotificationSMS   = "notification.sms_enabled"   // 短信通知
	SettingKeyNotificationPush  = "notification.push_enabled"  // 推送通知

	// 报表配置
	SettingKeyReportFormat   = "report.default_format" // 默认格式
	SettingKeyReportSchedule = "report.auto_schedule"  // 自动生成

	// 系统配置
	SettingKeySystemLanguage = "system.language" // 系统语言
	SettingKeySystemTimezone = "system.timezone" // 时区
	SettingKeySystemTheme    = "system.theme"    // 主题

	// 安全配置
	SettingKeySecurityMFA     = "security.mfa_enabled"     // 多因素认证
	SettingKeySecuritySession = "security.session_timeout" // 会话超时
	SettingKeySecurityIP      = "security.ip_whitelist"    // IP白名单
)

// EnterpriseSettingCreateRequest 创建企业配置请求
type EnterpriseSettingCreateRequest struct {
	EnterpriseID string `json:"enterpriseId" binding:"required"`
	Category     string `json:"category" binding:"required"`
	Key          string `json:"key" binding:"required"`
	Value        string `json:"value" binding:"required"`
	DataType     string `json:"dataType" binding:"required"`
	Description  string `json:"description"`
	IsEncrypted  bool   `json:"isEncrypted"`
	SortOrder    int    `json:"sortOrder"`
}

// EnterpriseSettingUpdateRequest 更新企业配置请求
type EnterpriseSettingUpdateRequest struct {
	Value       *string `json:"value"`
	Description *string `json:"description"`
	IsEncrypted *bool   `json:"isEncrypted"`
	IsActive    *bool   `json:"isActive"`
	SortOrder   *int    `json:"sortOrder"`
}

// EnterpriseSettingBatchUpdateRequest 批量更新企业配置请求
type EnterpriseSettingBatchUpdateRequest struct {
	Settings []EnterpriseSettingItem `json:"settings" binding:"required,min=1"`
}

// EnterpriseSettingItem 企业配置项
type EnterpriseSettingItem struct {
	Key         string `json:"key" binding:"required"`
	Value       string `json:"value" binding:"required"`
	Description string `json:"description"`
}

// EnterpriseSettingFilter 企业配置过滤条件
type EnterpriseSettingFilter struct {
	EnterpriseID string `json:"enterpriseId"`
	Category     string `json:"category"`
	Key          string `json:"key"`
	DataType     string `json:"dataType"`
	IsEncrypted  *bool  `json:"isEncrypted"`
	IsSystem     *bool  `json:"isSystem"`
	IsActive     *bool  `json:"isActive"`
	CreatedBy    string `json:"createdBy"`
}

// EnterpriseSettingResponse 企业配置响应
type EnterpriseSettingResponse struct {
	ID             string    `json:"id"`
	EnterpriseID   string    `json:"enterpriseId"`
	EnterpriseName string    `json:"enterpriseName"`
	Category       string    `json:"category"`
	CategoryName   string    `json:"categoryName"`
	Key            string    `json:"key"`
	KeyName        string    `json:"keyName"`
	Value          string    `json:"value"`
	DataType       string    `json:"dataType"`
	DataTypeName   string    `json:"dataTypeName"`
	Description    string    `json:"description"`
	IsEncrypted    bool      `json:"isEncrypted"`
	IsSystem       bool      `json:"isSystem"`
	IsActive       bool      `json:"isActive"`
	SortOrder      int       `json:"sortOrder"`
	CreatedBy      string    `json:"createdBy"`
	CreatedByName  string    `json:"createdByName"`
	UpdatedBy      string    `json:"updatedBy"`
	UpdatedByName  string    `json:"updatedByName"`
	CreatedAt      time.Time `json:"createdAt"`
	UpdatedAt      time.Time `json:"updatedAt"`
}

// PaginatedEnterpriseSettings 分页企业配置响应
type PaginatedEnterpriseSettings struct {
	Settings []EnterpriseSettingResponse `json:"settings"`
	Total    int64                       `json:"total"`
	Page     int                         `json:"page"`
	Limit    int                         `json:"limit"`
}

// EnterpriseSettingGroup 企业配置分组
type EnterpriseSettingGroup struct {
	Category     string                      `json:"category"`
	CategoryName string                      `json:"categoryName"`
	Settings     []EnterpriseSettingResponse `json:"settings"`
	Count        int                         `json:"count"`
}

// EnterpriseSettingSummary 企业配置汇总
type EnterpriseSettingSummary struct {
	EnterpriseID   string                   `json:"enterpriseId"`
	EnterpriseName string                   `json:"enterpriseName"`
	Total          int                      `json:"total"`
	Active         int                      `json:"active"`
	Inactive       int                      `json:"inactive"`
	Encrypted      int                      `json:"encrypted"`
	System         int                      `json:"system"`
	Custom         int                      `json:"custom"`
	ByCategory     map[string]int           `json:"byCategory"`
	ByDataType     map[string]int           `json:"byDataType"`
	Groups         []EnterpriseSettingGroup `json:"groups"`
	LastUpdated    *time.Time               `json:"lastUpdated"`
}

// EnterpriseSettingExportRequest 导出企业配置请求
type EnterpriseSettingExportRequest struct {
	EnterpriseID    string   `json:"enterpriseId" binding:"required"`
	Categories      []string `json:"categories"`
	IncludeSystem   bool     `json:"includeSystem"`
	IncludeInactive bool     `json:"includeInactive"`
	Format          string   `json:"format" binding:"required"`
	FileName        string   `json:"fileName"`
}

// EnterpriseSettingExportResult 导出企业配置结果
type EnterpriseSettingExportResult struct {
	FileName    string    `json:"fileName"`
	FileSize    int64     `json:"fileSize"`
	RecordCount int64     `json:"recordCount"`
	Format      string    `json:"format"`
	DownloadURL string    `json:"downloadUrl"`
	ExportedAt  time.Time `json:"exportedAt"`
	ExpiresAt   time.Time `json:"expiresAt"`
}

// EnterpriseSettingImportRequest 导入企业配置请求
type EnterpriseSettingImportRequest struct {
	EnterpriseID string                  `json:"enterpriseId" binding:"required"`
	Settings     []EnterpriseSettingItem `json:"settings" binding:"required,min=1"`
	Overwrite    bool                    `json:"overwrite"`
	ValidateOnly bool                    `json:"validateOnly"`
}

// EnterpriseSettingImportResult 导入企业配置结果
type EnterpriseSettingImportResult struct {
	Total      int                      `json:"total"`
	Success    int                      `json:"success"`
	Failed     int                      `json:"failed"`
	Skipped    int                      `json:"skipped"`
	Errors     []EnterpriseSettingError `json:"errors"`
	ImportedAt time.Time                `json:"importedAt"`
}

// EnterpriseSettingError 企业配置错误
type EnterpriseSettingError struct {
	Key   string `json:"key"`
	Value string `json:"value"`
	Error string `json:"error"`
	Line  int    `json:"line"`
}

// EnterpriseSettingTemplate 企业配置模板
type EnterpriseSettingTemplate struct {
	Name        string                  `json:"name"`
	Description string                  `json:"description"`
	Category    string                  `json:"category"`
	Settings    []EnterpriseSettingItem `json:"settings"`
	CreatedAt   time.Time               `json:"createdAt"`
}

// GetCategoryName 获取分类名称
func GetSettingCategoryName(category string) string {
	switch category {
	case SettingCategoryTax:
		return "税务配置"
	case SettingCategoryInvoice:
		return "发票配置"
	case SettingCategoryDeclaration:
		return "申报配置"
	case SettingCategoryNotification:
		return "通知配置"
	case SettingCategoryReport:
		return "报表配置"
	case SettingCategorySystem:
		return "系统配置"
	case SettingCategorySecurity:
		return "安全配置"
	case SettingCategoryIntegration:
		return "集成配置"
	case SettingCategoryCustom:
		return "自定义配置"
	default:
		return "未知分类"
	}
}

// GetDataTypeName 获取数据类型名称
func GetDataTypeName(dataType string) string {
	switch dataType {
	case DataTypeString:
		return "字符串"
	case DataTypeNumber:
		return "数字"
	case DataTypeBoolean:
		return "布尔值"
	case DataTypeJSON:
		return "JSON对象"
	case DataTypeArray:
		return "数组"
	case DataTypeDate:
		return "日期"
	case DataTypeTime:
		return "时间"
	case DataTypeFile:
		return "文件路径"
	case DataTypeURL:
		return "URL地址"
	case DataTypeEmail:
		return "邮箱地址"
	case DataTypePhone:
		return "电话号码"
	default:
		return "未知类型"
	}
}

// GetKeyName 获取配置键名称
func GetKeyName(key string) string {
	keyNames := map[string]string{
		SettingKeyTaxRate:           "默认税率",
		SettingKeyTaxPeriod:         "申报周期",
		SettingKeyTaxReminder:       "提醒天数",
		SettingKeyTaxAutoCalculate:  "自动计算",
		SettingKeyInvoicePrefix:     "发票号前缀",
		SettingKeyInvoiceAutoVerify: "自动验证",
		SettingKeyInvoiceTemplate:   "默认模板",
		SettingKeyDeclarationAuto:   "自动提交",
		SettingKeyDeclarationBackup: "自动备份",
		SettingKeyNotificationEmail: "邮件通知",
		SettingKeyNotificationSMS:   "短信通知",
		SettingKeyNotificationPush:  "推送通知",
		SettingKeyReportFormat:      "默认格式",
		SettingKeyReportSchedule:    "自动生成",
		SettingKeySystemLanguage:    "系统语言",
		SettingKeySystemTimezone:    "时区",
		SettingKeySystemTheme:       "主题",
		SettingKeySecurityMFA:       "多因素认证",
		SettingKeySecuritySession:   "会话超时",
		SettingKeySecurityIP:        "IP白名单",
	}

	if name, exists := keyNames[key]; exists {
		return name
	}
	return key
}

// IsValidCategory 验证分类是否有效
func IsValidSettingCategory(category string) bool {
	validCategories := []string{
		SettingCategoryTax,
		SettingCategoryInvoice,
		SettingCategoryDeclaration,
		SettingCategoryNotification,
		SettingCategoryReport,
		SettingCategorySystem,
		SettingCategorySecurity,
		SettingCategoryIntegration,
		SettingCategoryCustom,
	}

	for _, validCategory := range validCategories {
		if category == validCategory {
			return true
		}
	}
	return false
}

// IsValidDataType 验证数据类型是否有效
func IsValidDataType(dataType string) bool {
	validDataTypes := []string{
		DataTypeString,
		DataTypeNumber,
		DataTypeBoolean,
		DataTypeJSON,
		DataTypeArray,
		DataTypeDate,
		DataTypeTime,
		DataTypeFile,
		DataTypeURL,
		DataTypeEmail,
		DataTypePhone,
	}

	for _, validDataType := range validDataTypes {
		if dataType == validDataType {
			return true
		}
	}
	return false
}

// TableName 指定表名
func (EnterpriseSetting) TableName() string {
	return "enterprise_settings"
}
