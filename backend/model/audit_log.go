// Package model defines data structures and database models for the tax management system.
// It includes entities for users, enterprises, invoices, declarations, and audit logs.
package model

import (
	"time"
)

// AuditLog represents system audit log entries for tracking user operations and system changes.
// It provides comprehensive logging for security, compliance, and debugging purposes.
type AuditLog struct {
	ID            string     `json:"id" gorm:"primaryKey;type:varchar(50);comment:日志ID"`
	UserID        string     `json:"user_id" gorm:"type:varchar(50);index;comment:操作用户ID"`
	UserName      string     `json:"user_name" gorm:"type:varchar(100);comment:操作用户名称"`
	Action        string     `json:"action" gorm:"type:varchar(50);not null;comment:操作类型"`
	ResourceType  string     `json:"resource_type" gorm:"type:varchar(50);not null;comment:资源类型"`
	ResourceID    string     `json:"resource_id" gorm:"type:varchar(50);not null;index;comment:资源ID"`
	Description   string     `json:"description" gorm:"type:text;comment:操作描述"`
	IPAddress     string     `json:"ip_address" gorm:"type:varchar(45);comment:操作IP地址"`
	UserAgent     string     `json:"user_agent" gorm:"type:varchar(500);comment:用户代理"`
	BeforeState   string     `json:"before_state" gorm:"type:json;comment:操作前状态"`
	AfterState    string     `json:"after_state" gorm:"type:json;comment:操作后状态"`
	Status        string     `json:"status" gorm:"type:varchar(20);default:success;comment:操作状态"`
	ErrorMessage  string     `json:"error_message" gorm:"type:text;comment:错误信息"`
	Module        string     `json:"module" gorm:"type:varchar(50);comment:操作模块"`
	Duration      int64      `json:"duration" gorm:"comment:操作耗时(毫秒)"`
	EnterpriseID  string     `json:"enterprise_id" gorm:"type:varchar(50);index;comment:关联企业ID"`
	RiskLevel     string     `json:"risk_level" gorm:"type:varchar(10);default:low;comment:风险等级"`
	SensitiveData bool       `json:"sensitive_data" gorm:"default:false;comment:是否涉及敏感数据"`
	IsDeleted     bool       `json:"is_deleted" gorm:"default:false;comment:是否删除"`
	CreatedAt     time.Time  `json:"created_at" gorm:"index;comment:创建时间"`
	UpdatedAt     time.Time  `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt     *time.Time `json:"deleted_at" gorm:"comment:删除时间"`
}

// AuditLogFilter 表示审计日志过滤条件
type AuditLogFilter struct {
	UserID       string    `json:"userId"`                           // 用户ID
	Action       string    `json:"action"`                           // 操作类型
	ResourceType string    `json:"resourceType"`                     // 资源类型
	ResourceID   string    `json:"resourceId"`                       // 资源ID
	StartDate    time.Time `json:"startDate"`                        // 开始日期
	EndDate      time.Time `json:"endDate"`                          // 结束日期
	Status       string    `json:"status"`                           // 状态
	Module       string    `json:"module"`                           // 模块
	EnterpriseID string    `json:"enterpriseId"`                     // 企业ID
	Page         int       `json:"page" binding:"min=1"`             // 页码
	PageSize     int       `json:"pageSize" binding:"min=1,max=100"` // 每页大小
}

// AuditLogListResponse 表示审计日志列表响应
type AuditLogListResponse struct {
	Logs       []AuditLog `json:"logs"`       // 日志列表
	Total      int64      `json:"total"`      // 总数
	Page       int        `json:"page"`       // 当前页
	PageSize   int        `json:"pageSize"`   // 每页大小
	TotalPages int        `json:"totalPages"` // 总页数
}
