package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// TaxPayment 税务缴费记录模型
type TaxPayment struct {
	ID              string          `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:缴费记录ID"`
	DeclarationID   string          `json:"declaration_id" gorm:"type:varchar(36);not null;column:declaration_id;index:idx_tax_payments_declaration_id;comment:申报ID"`
	EnterpriseID    string          `json:"enterprise_id" gorm:"type:varchar(36);not null;column:enterprise_id;index:idx_tax_payments_enterprise_id;comment:企业ID"`
	TaxTypeID       string          `json:"tax_type_id" gorm:"type:varchar(36);not null;column:tax_type_id;index:idx_tax_payments_tax_type_id;comment:税种ID"`
	PaymentNumber   string          `json:"payment_number" gorm:"type:varchar(50);not null;uniqueIndex:uk_tax_payments_number;column:payment_number;comment:缴款书号码"`
	PaymentAmount   decimal.Decimal `json:"payment_amount" gorm:"type:decimal(15,2);not null;column:payment_amount;comment:缴款金额"`
	LateFeeAmount   decimal.Decimal `json:"late_fee_amount" gorm:"type:decimal(15,2);default:0;column:late_fee_amount;comment:滞纳金金额"`
	TotalAmount     decimal.Decimal `json:"total_amount" gorm:"type:decimal(15,2);not null;column:total_amount;comment:总金额"`
	PaymentMethod   string          `json:"payment_method" gorm:"type:enum('bank_transfer','online','cash','check','deduction');not null;column:payment_method;comment:缴款方式"`
	PaymentDate     time.Time       `json:"payment_date" gorm:"not null;column:payment_date;index:idx_tax_payments_payment_date;comment:缴款日期"`
	PaymentTime     *time.Time      `json:"payment_time" gorm:"column:payment_time;comment:缴款时间"`
	BankName        string          `json:"bank_name" gorm:"type:varchar(100);column:bank_name;comment:缴款银行"`
	BankAccount     string          `json:"bank_account" gorm:"type:varchar(50);column:bank_account;comment:缴款账户"`
	ReferenceNumber string          `json:"reference_number" gorm:"type:varchar(100);column:reference_number;comment:银行流水号"`
	VoucherNumber   string          `json:"voucher_number" gorm:"type:varchar(50);column:voucher_number;comment:凭证号"`
	Status          string          `json:"status" gorm:"type:enum('pending','completed','failed','cancelled','refunded');default:pending;column:status;index:idx_tax_payments_status;comment:缴款状态"`
	OperatorID      *string         `json:"operator_id" gorm:"type:varchar(36);column:operator_id;comment:操作员ID"`
	OperatorName    string          `json:"operator_name" gorm:"type:varchar(100);column:operator_name;comment:操作员姓名"`
	Remarks         string          `json:"remarks" gorm:"type:text;column:remarks;comment:备注"`
	CreatedAt       time.Time       `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt       time.Time       `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	Declaration *Declaration `json:"declaration,omitempty" gorm:"foreignKey:DeclarationID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	Enterprise  *Enterprise  `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	TaxType     *TaxType     `json:"tax_type,omitempty" gorm:"foreignKey:TaxTypeID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
}

// 缴费状态常量
const (
	TaxPaymentStatusPending   = "pending"
	TaxPaymentStatusCompleted = "completed"
	TaxPaymentStatusFailed    = "failed"
	TaxPaymentStatusCancelled = "cancelled"
	TaxPaymentStatusRefunded  = "refunded"
)

// 缴费方式常量
const (
	TaxPaymentMethodBankTransfer = "bank_transfer"
	TaxPaymentMethodOnline       = "online"
	TaxPaymentMethodCash         = "cash"
	TaxPaymentMethodCheck        = "check"
	TaxPaymentMethodDeduction    = "deduction"
)

// TaxPaymentCreateRequest 创建税务缴费记录请求
type TaxPaymentCreateRequest struct {
	DeclarationID string          `json:"declaration_id" binding:"required"`
	EnterpriseID  string          `json:"enterprise_id" binding:"required"`
	TaxTypeID     string          `json:"tax_type_id" binding:"required"`
	PaymentNumber string          `json:"payment_number" binding:"required"`
	PaymentAmount decimal.Decimal `json:"payment_amount" binding:"required"`
	LateFeeAmount decimal.Decimal `json:"late_fee_amount"`
	PaymentMethod string          `json:"payment_method" binding:"required"`
	PaymentDate   string          `json:"payment_date" binding:"required"`
	BankName      string          `json:"bank_name"`
	BankAccount   string          `json:"bank_account"`
	VoucherNumber string          `json:"voucher_number"`
	Remarks       string          `json:"remarks"`
}

// TaxPaymentUpdateRequest 更新税务缴费记录请求
type TaxPaymentUpdateRequest struct {
	PaymentAmount *float64 `json:"payment_amount" binding:"omitempty,gt=0"`
	PaymentDate   *string  `json:"payment_date"`
	PaymentMethod *string  `json:"payment_method"`
	VoucherNumber *string  `json:"voucher_number"`
	BankAccount   *string  `json:"bank_account"`
	Status        *string  `json:"status"`
	Remark        *string  `json:"remark"`
}

// TaxPaymentResponse 税务缴费记录响应
type TaxPaymentResponse struct {
	ID             string     `json:"id"`
	EnterpriseID   string     `json:"enterprise_id"`
	EnterpriseName string     `json:"enterprise_name"`
	DeclarationID  *string    `json:"declaration_id"`
	TaxTypeID      string     `json:"tax_type_id"`
	TaxTypeName    string     `json:"tax_type_name"`
	PaymentAmount  float64    `json:"payment_amount"`
	PaymentDate    time.Time  `json:"payment_date"`
	PaymentMethod  string     `json:"payment_method"`
	VoucherNumber  string     `json:"voucher_number"`
	BankAccount    string     `json:"bank_account"`
	Status         string     `json:"status"`
	ProcessedBy    string     `json:"processed_by"`
	ProcessedAt    *time.Time `json:"processed_at"`
	Remark         string     `json:"remark"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
}
