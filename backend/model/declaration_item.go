// Package model defines data structures and database models for the tax management system.
// It includes entities for declaration items and related data transfer objects.
package model

import (
	"errors"
	"time"

	"github.com/shopspring/decimal"
)

// DeclarationItem represents a single item in a tax declaration.
// It corresponds to the `declaration_items` table.
type DeclarationItem struct {
	ID            string          `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:申报项ID"`
	DeclarationID string          `json:"declaration_id" gorm:"type:varchar(36);not null;column:declaration_id;index:idx_declaration_items_declaration_id;comment:申报ID"`
	TaxTypeID     *string         `json:"tax_type_id" gorm:"type:varchar(36);column:tax_type_id;index:idx_declaration_items_tax_type_id;comment:税种ID"`
	ItemType      string          `json:"item_type" gorm:"type:varchar(50);not null;column:item_type;comment:项目类型"`
	ItemCode      string          `json:"item_code" gorm:"type:varchar(50);column:item_code;comment:项目代码"`
	ItemName      string          `json:"item_name" gorm:"type:varchar(255);not null;column:item_name;comment:项目名称"`
	Description   string          `json:"description" gorm:"type:varchar(500);column:description;comment:项目描述"`
	Amount        decimal.Decimal `json:"amount" gorm:"type:decimal(20,2);not null;column:amount;comment:金额"`
	TaxableAmount decimal.Decimal `json:"taxable_amount" gorm:"type:decimal(20,2);column:taxable_amount;comment:计税金额"`
	TaxRate       decimal.Decimal `json:"tax_rate" gorm:"type:decimal(5,4);column:tax_rate;comment:税率"`
	TaxAmount     decimal.Decimal `json:"tax_amount" gorm:"type:decimal(20,2);column:tax_amount;comment:税额"`
	Category      string          `json:"category" gorm:"type:varchar(100);column:category;comment:类别"`
	SubCategory   string          `json:"sub_category" gorm:"type:varchar(100);column:sub_category;comment:子类别"`
	LineNumber    int             `json:"line_number" gorm:"type:int;column:line_number;comment:行号"`
	IsRequired    bool            `json:"is_required" gorm:"default:false;column:is_required;comment:是否必填"`
	IsCalculated  bool            `json:"is_calculated" gorm:"default:false;column:is_calculated;comment:是否自动计算"`
	Formula       string          `json:"formula" gorm:"type:varchar(500);column:formula;comment:计算公式"`
	Remarks       string          `json:"remarks" gorm:"type:varchar(500);column:remarks;comment:备注"`
	CreatedAt     time.Time       `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt     time.Time       `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	Declaration *Declaration `json:"declaration,omitempty" gorm:"foreignKey:DeclarationID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	TaxType     *TaxType     `json:"tax_type,omitempty" gorm:"foreignKey:TaxTypeID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

// TableName returns the table name for the DeclarationItem model.
func (DeclarationItem) TableName() string {
	return "declaration_items"
}

// DeclarationItemRequest 申报项请求结构
type DeclarationItemRequest struct {
	DeclarationID string  `json:"declaration_id" binding:"required"`
	TaxTypeID     *string `json:"tax_type_id"`
	ItemType      string  `json:"item_type" binding:"required"`
	ItemCode      string  `json:"item_code"`
	ItemName      string  `json:"item_name" binding:"required"`
	Description   string  `json:"description"`
	Amount        float64 `json:"amount" binding:"required"`
	TaxableAmount float64 `json:"taxable_amount"`
	TaxRate       float64 `json:"tax_rate"`
	Category      string  `json:"category"`
	SubCategory   string  `json:"sub_category"`
	LineNumber    int     `json:"line_number"`
	IsRequired    bool    `json:"is_required"`
	IsCalculated  bool    `json:"is_calculated"`
	Formula       string  `json:"formula"`
	Remarks       string  `json:"remarks"`
}

// DeclarationItemResponse 申报项响应结构
type DeclarationItemResponse struct {
	ID            string          `json:"id"`
	DeclarationID string          `json:"declaration_id"`
	TaxTypeID     *string         `json:"tax_type_id"`
	ItemType      string          `json:"item_type"`
	ItemCode      string          `json:"item_code"`
	ItemName      string          `json:"item_name"`
	Description   string          `json:"description"`
	Amount        decimal.Decimal `json:"amount"`
	TaxableAmount decimal.Decimal `json:"taxable_amount"`
	TaxRate       decimal.Decimal `json:"tax_rate"`
	TaxAmount     decimal.Decimal `json:"tax_amount"`
	Category      string          `json:"category"`
	SubCategory   string          `json:"sub_category"`
	LineNumber    int             `json:"line_number"`
	IsRequired    bool            `json:"is_required"`
	IsCalculated  bool            `json:"is_calculated"`
	Formula       string          `json:"formula"`
	Remarks       string          `json:"remarks"`
	CreatedAt     time.Time       `json:"created_at"`
	UpdatedAt     time.Time       `json:"updated_at"`
}

// CalculateTaxAmount 计算税额
func (item *DeclarationItem) CalculateTaxAmount() {
	if item.IsCalculated && !item.TaxableAmount.IsZero() && !item.TaxRate.IsZero() {
		item.TaxAmount = item.TaxableAmount.Mul(item.TaxRate)
	}
}

// Validate 验证申报项数据
func (item *DeclarationItem) Validate() error {
	if item.ItemName == "" {
		return ErrInvalidDeclarationItemName
	}

	if item.Amount.LessThan(decimal.Zero) {
		return ErrInvalidAmount
	}

	if item.TaxableAmount.LessThan(decimal.Zero) {
		return ErrInvalidTaxableAmount
	}

	if item.TaxRate.LessThan(decimal.Zero) || item.TaxRate.GreaterThan(decimal.NewFromFloat(1)) {
		return ErrInvalidTaxRate
	}

	return nil
}

// 错误定义
var (
	ErrInvalidDeclarationItemName = errors.New("项目名称不能为空")
	ErrInvalidAmount              = errors.New("金额不能为负数")
	ErrInvalidTaxableAmount       = errors.New("计税金额不能为负数")
)

// 申报项类型常量
const (
	DeclarationItemTypeIncome     = "income"     // 收入项
	DeclarationItemTypeDeduction  = "deduction"  // 扣除项
	DeclarationItemTypeTax        = "tax"        // 税额项
	DeclarationItemTypeCredit     = "credit"     // 抵免项
	DeclarationItemTypePayment    = "payment"    // 缴款项
	DeclarationItemTypeRefund     = "refund"     // 退税项
	DeclarationItemTypeAdjustment = "adjustment" // 调整项
	DeclarationItemTypeOther      = "other"      // 其他项
)
