package model

import (
	"time"
)

// TaxDeclarationPeriod 税务申报期间模型
type TaxDeclarationPeriod struct {
	ID          string    `json:"id" gorm:"primaryKey;size:36;comment:主键ID"`
	TaxTypeID   string    `json:"taxTypeId" gorm:"not null;size:36;index:idx_tax_declaration_periods_tax_type_id;comment:税种ID"`
	PeriodType  string    `json:"periodType" gorm:"not null;size:50;comment:期间类型（如月度、季度、年度）"`
	Description *string   `json:"description" gorm:"type:text;comment:描述"`
	CreatedAt   time.Time `json:"createdAt" gorm:"not null;default:CURRENT_TIMESTAMP;comment:创建时间"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间"`

	// 关联关系
	TaxType *TaxType `json:"taxType,omitempty" gorm:"foreignKey:TaxTypeID;references:ID;constraint:OnDelete:CASCADE"`
}

// TaxExemption 税收减免模型
type TaxExemption struct {
	ID            string     `json:"id" gorm:"primaryKey;size:36;comment:主键ID"`
	Name          string     `json:"name" gorm:"not null;size:255;comment:减免名称"`
	Description   *string    `json:"description" gorm:"type:text;comment:描述"`
	TaxTypeID     string     `json:"taxTypeId" gorm:"not null;size:36;index:idx_tax_exemptions_tax_type_id;comment:适用税种ID"`
	Rule          string     `json:"rule" gorm:"type:text;not null;comment:减免规则（JSON格式）"`
	EffectiveDate time.Time  `json:"effectiveDate" gorm:"not null;comment:生效日期"`
	ExpiryDate    *time.Time `json:"expiryDate" gorm:"comment:失效日期"`
	CreatedAt     time.Time  `json:"createdAt" gorm:"not null;default:CURRENT_TIMESTAMP;comment:创建时间"`
	UpdatedAt     time.Time  `json:"updatedAt" gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间"`

	// 关联关系
	TaxType *TaxType `json:"taxType,omitempty" gorm:"foreignKey:TaxTypeID;references:ID;constraint:OnDelete:CASCADE"`
}
