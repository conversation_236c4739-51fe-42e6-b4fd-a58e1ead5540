package model

import (
	"time"
)

// RoleCreateRequest represents a request to create a role
type RoleCreateRequest struct {
	Code          string   `json:"code" binding:"required"`
	Name          string   `json:"name" binding:"required"`
	Description   string   `json:"description"`
	Type          string   `json:"type" binding:"required"`
	Level         int      `json:"level"`
	IsActive      bool     `json:"isActive"`
	PermissionIDs []string `json:"permissionIds"`
}

// RoleUpdateRequest represents a request to update a role
type RoleUpdateRequest struct {
	Name          string   `json:"name"`
	Description   string   `json:"description"`
	Type          string   `json:"type"`
	Level         *int     `json:"level"`
	IsActive      *bool    `json:"isActive"`
	PermissionIDs []string `json:"permissionIds"`
}

// PermissionCreateRequest represents a request to create a permission
type PermissionCreateRequest struct {
	Code        string `json:"code" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Resource    string `json:"resource" binding:"required"`
	Action      string `json:"action" binding:"required"`
	Type        string `json:"type" binding:"required"`
	IsActive    bool   `json:"isActive"`
}

// UserRole represents the relationship between users and roles
type UserRole struct {
	ID         string     `json:"id" gorm:"primaryKey"`
	UserID     string     `json:"userId" gorm:"index"`
	RoleID     string     `json:"roleId" gorm:"index"`
	AssignedBy string     `json:"assignedBy" gorm:"size:36"`
	AssignedAt time.Time  `json:"assignedAt"`
	RevokedBy  string     `json:"revokedBy" gorm:"size:36"`
	RevokedAt  *time.Time `json:"revokedAt"`
	IsActive   bool       `json:"isActive" gorm:"default:true"`
	CreatedAt  time.Time  `json:"createdAt"`
	UpdatedAt  time.Time  `json:"updatedAt"`
}
