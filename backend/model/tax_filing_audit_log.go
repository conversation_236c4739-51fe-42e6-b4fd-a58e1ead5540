package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// TaxFilingAuditAction 审计操作类型
type TaxFilingAuditAction string

const (
	AuditActionCreate       TaxFilingAuditAction = "create"
	AuditActionUpdate       TaxFilingAuditAction = "update"
	AuditActionDelete       TaxFilingAuditAction = "delete"
	AuditActionSubmit       TaxFilingAuditAction = "submit"
	AuditActionCancel       TaxFilingAuditAction = "cancel"
	AuditActionRetry        TaxFilingAuditAction = "retry"
	AuditActionStatusChange TaxFilingAuditAction = "status_change"
	AuditActionBatchProcess TaxFilingAuditAction = "batch_process"
	AuditActionSync         TaxFilingAuditAction = "sync"
	AuditActionCallback     TaxFilingAuditAction = "callback"
)

// AuditData 审计数据结构
type AuditData map[string]interface{}

// Value 实现 driver.Valuer 接口
func (a AuditData) Value() (driver.Value, error) {
	if len(a) == 0 {
		return nil, nil
	}
	return json.Marshal(a)
}

// Scan 实现 sql.Scanner 接口
func (a *AuditData) Scan(value interface{}) error {
	if value == nil {
		*a = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into AuditData", value)
	}

	return json.Unmarshal(bytes, a)
}

// TaxFilingAuditLog 税务申报审计日志模型
type TaxFilingAuditLog struct {
	ID           string               `gorm:"primaryKey;size:50" json:"id"`
	SubmissionID *string              `gorm:"size:50;index" json:"submission_id,omitempty"`
	BatchID      *string              `gorm:"size:50;index" json:"batch_id,omitempty"`
	Action       TaxFilingAuditAction `gorm:"size:50;not null;index" json:"action"`

	// 操作数据
	OldValue AuditData `gorm:"type:json" json:"old_value,omitempty"`
	NewValue AuditData `gorm:"type:json" json:"new_value,omitempty"`
	Changes  AuditData `gorm:"type:json" json:"changes,omitempty"` // 变更字段

	// 操作者信息
	OperatorID   *string `gorm:"size:50;index" json:"operator_id,omitempty"`
	OperatorType string  `gorm:"size:20;not null;default:system" json:"operator_type"` // system, user, api
	OperatorName *string `gorm:"size:100" json:"operator_name,omitempty"`

	// 请求信息
	IPAddress *string `gorm:"size:45" json:"ip_address,omitempty"`
	UserAgent *string `gorm:"size:500" json:"user_agent,omitempty"`
	RequestID *string `gorm:"size:50;index" json:"request_id,omitempty"`
	SessionID *string `gorm:"size:50" json:"session_id,omitempty"`

	// 业务信息
	BusinessType string  `gorm:"size:50;not null;default:tax_filing" json:"business_type"`
	Module       string  `gorm:"size:50;not null" json:"module"`
	Description  *string `gorm:"type:text" json:"description,omitempty"`

	// 结果信息
	Success      bool    `gorm:"not null;default:true" json:"success"`
	ErrorMessage *string `gorm:"type:text" json:"error_message,omitempty"`
	Duration     *int    `json:"duration,omitempty"` // 操作耗时(毫秒)

	// 系统字段
	CreatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;index" json:"created_at"`

	// 关联
	Submission *TaxFilingSubmission `gorm:"foreignKey:SubmissionID" json:"submission,omitempty"`
	Batch      *TaxFilingBatch      `gorm:"foreignKey:BatchID" json:"batch,omitempty"`
}

// TableName 指定表名
func (TaxFilingAuditLog) TableName() string {
	return "tax_filing_audit_logs"
}

// BeforeCreate GORM钩子 - 创建前
func (t *TaxFilingAuditLog) BeforeCreate(tx *gorm.DB) error {
	if t.ID == "" {
		t.ID = GenerateID()
	}
	return nil
}

// CreateAuditLog 创建审计日志的工厂方法
func CreateAuditLog(action TaxFilingAuditAction, module string) *TaxFilingAuditLog {
	return &TaxFilingAuditLog{
		ID:           GenerateID(),
		Action:       action,
		Module:       module,
		BusinessType: "tax_filing",
		OperatorType: "system",
		Success:      true,
		CreatedAt:    time.Now(),
	}
}

// CreateUserAuditLog 创建用户操作审计日志
func CreateUserAuditLog(action TaxFilingAuditAction, module string, operatorID, operatorName string) *TaxFilingAuditLog {
	log := CreateAuditLog(action, module)
	log.OperatorType = "user"
	log.OperatorID = &operatorID
	log.OperatorName = &operatorName
	return log
}

// CreateAPIAuditLog 创建API操作审计日志
func CreateAPIAuditLog(action TaxFilingAuditAction, module string, requestID string) *TaxFilingAuditLog {
	log := CreateAuditLog(action, module)
	log.OperatorType = "api"
	log.RequestID = &requestID
	return log
}

// SetSubmission 设置关联的申报记录
func (t *TaxFilingAuditLog) SetSubmission(submissionID string) *TaxFilingAuditLog {
	t.SubmissionID = &submissionID
	return t
}

// SetBatch 设置关联的批次记录
func (t *TaxFilingAuditLog) SetBatch(batchID string) *TaxFilingAuditLog {
	t.BatchID = &batchID
	return t
}

// SetOldValue 设置操作前的值
func (t *TaxFilingAuditLog) SetOldValue(data interface{}) *TaxFilingAuditLog {
	if data != nil {
		if auditData, err := convertToAuditData(data); err == nil {
			t.OldValue = auditData
		}
	}
	return t
}

// SetNewValue 设置操作后的值
func (t *TaxFilingAuditLog) SetNewValue(data interface{}) *TaxFilingAuditLog {
	if data != nil {
		if auditData, err := convertToAuditData(data); err == nil {
			t.NewValue = auditData
		}
	}
	return t
}

// SetChanges 设置变更字段
func (t *TaxFilingAuditLog) SetChanges(changes map[string]interface{}) *TaxFilingAuditLog {
	if len(changes) > 0 {
		t.Changes = AuditData(changes)
	}
	return t
}

// SetRequestInfo 设置请求信息
func (t *TaxFilingAuditLog) SetRequestInfo(ipAddress, userAgent, requestID, sessionID string) *TaxFilingAuditLog {
	if ipAddress != "" {
		t.IPAddress = &ipAddress
	}
	if userAgent != "" {
		t.UserAgent = &userAgent
	}
	if requestID != "" {
		t.RequestID = &requestID
	}
	if sessionID != "" {
		t.SessionID = &sessionID
	}
	return t
}

// SetDescription 设置描述信息
func (t *TaxFilingAuditLog) SetDescription(description string) *TaxFilingAuditLog {
	if description != "" {
		t.Description = &description
	}
	return t
}

// SetError 设置错误信息
func (t *TaxFilingAuditLog) SetError(err error) *TaxFilingAuditLog {
	if err != nil {
		t.Success = false
		errorMsg := err.Error()
		t.ErrorMessage = &errorMsg
	}
	return t
}

// SetDuration 设置操作耗时
func (t *TaxFilingAuditLog) SetDuration(duration time.Duration) *TaxFilingAuditLog {
	durationMs := int(duration.Milliseconds())
	t.Duration = &durationMs
	return t
}

// convertToAuditData 将任意数据转换为审计数据格式
func convertToAuditData(data interface{}) (AuditData, error) {
	// 先序列化为JSON，再反序列化为map
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	var auditData AuditData
	err = json.Unmarshal(jsonBytes, &auditData)
	if err != nil {
		return nil, err
	}

	return auditData, nil
}

// TaxFilingAuditLogQuery 审计日志查询条件
type TaxFilingAuditLogQuery struct {
	SubmissionID *string               `json:"submission_id,omitempty"`
	BatchID      *string               `json:"batch_id,omitempty"`
	Action       *TaxFilingAuditAction `json:"action,omitempty"`
	OperatorID   *string               `json:"operator_id,omitempty"`
	OperatorType *string               `json:"operator_type,omitempty"`
	Module       *string               `json:"module,omitempty"`
	Success      *bool                 `json:"success,omitempty"`
	StartTime    *time.Time            `json:"start_time,omitempty"`
	EndTime      *time.Time            `json:"end_time,omitempty"`
	IPAddress    *string               `json:"ip_address,omitempty"`
	RequestID    *string               `json:"request_id,omitempty"`

	// 分页参数
	Page     int `json:"page" default:"1"`
	PageSize int `json:"page_size" default:"20"`

	// 排序参数
	OrderBy string `json:"order_by" default:"created_at"`
	Order   string `json:"order" default:"desc"`
}

// TaxFilingAuditLogResponse 审计日志响应
type TaxFilingAuditLogResponse struct {
	TaxFilingAuditLog

	// 扩展字段
	OperatorDisplayName string `json:"operator_display_name"`
	ActionDisplayName   string `json:"action_display_name"`
	DurationDisplay     string `json:"duration_display"`
}

// GetActionDisplayName 获取操作显示名称
func (t *TaxFilingAuditLogResponse) GetActionDisplayName() string {
	actionNames := map[TaxFilingAuditAction]string{
		AuditActionCreate:       "创建申报",
		AuditActionUpdate:       "更新申报",
		AuditActionDelete:       "删除申报",
		AuditActionSubmit:       "提交申报",
		AuditActionCancel:       "取消申报",
		AuditActionRetry:        "重试申报",
		AuditActionStatusChange: "状态变更",
		AuditActionBatchProcess: "批次处理",
		AuditActionSync:         "状态同步",
		AuditActionCallback:     "回调处理",
	}

	if name, exists := actionNames[t.Action]; exists {
		return name
	}
	return string(t.Action)
}

// GetDurationDisplay 获取耗时显示
func (t *TaxFilingAuditLogResponse) GetDurationDisplay() string {
	if t.Duration == nil {
		return ""
	}

	duration := time.Duration(*t.Duration) * time.Millisecond
	if duration < time.Second {
		return fmt.Sprintf("%dms", *t.Duration)
	} else if duration < time.Minute {
		return fmt.Sprintf("%.2fs", duration.Seconds())
	} else {
		return fmt.Sprintf("%.2fm", duration.Minutes())
	}
}
