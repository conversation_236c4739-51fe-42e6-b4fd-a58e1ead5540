package model

import "time"

// DataDictionary 数据字典模型
type DataDictionary struct {
	ID          string    `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:主键ID"`
	Category    string    `json:"category" gorm:"type:varchar(50);not null;column:category;index:idx_data_dict_category;comment:字典分类"`
	DictCode    string    `json:"dict_code" gorm:"type:varchar(50);not null;column:dict_code;comment:字典编码"`
	DictName    string    `json:"dict_name" gorm:"type:varchar(100);not null;column:dict_name;comment:字典名称"`
	DictValue   string    `json:"dict_value" gorm:"type:varchar(200);column:dict_value;comment:字典值"`
	Description string    `json:"description" gorm:"type:varchar(500);column:description;comment:描述"`
	SortOrder   int       `json:"sort_order" gorm:"default:0;column:sort_order;comment:排序"`
	IsActive    bool      `json:"is_active" gorm:"default:true;column:is_active;comment:是否启用"`
	ParentID    *string   `json:"parent_id" gorm:"type:varchar(36);column:parent_id;index:idx_data_dict_parent_id;comment:父级ID"`
	CreatedAt   time.Time `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	Parent   *DataDictionary  `json:"parent,omitempty" gorm:"foreignKey:ParentID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Children []DataDictionary `json:"children,omitempty" gorm:"foreignKey:ParentID;references:ID"`
}

// TableName 指定表名
func (DataDictionary) TableName() string {
	return "data_dictionaries"
}

// 数据字典分类常量
const (
	DataDictCategoryTaxpayerType      = "taxpayer_type"
	DataDictCategoryEnterpriseStatus  = "enterprise_status"
	DataDictCategoryInvoiceType       = "invoice_type"
	DataDictCategoryInvoiceStatus     = "invoice_status"
	DataDictCategoryDeclarationStatus = "declaration_status"
	DataDictCategoryPaymentMethod     = "payment_method"
	DataDictCategoryRiskLevel         = "risk_level"
	DataDictCategoryIndustryCode      = "industry_code"
	DataDictCategoryRegionCode        = "region_code"
	DataDictCategoryTaxType           = "tax_type"
	DataDictCategoryDocumentType      = "document_type"
	DataDictCategoryUserStatus        = "user_status"
	DataDictCategoryMessageType       = "message_type"
)

// 常用字典编码常量
const (
	// 纳税人类型
	DictCodeTaxpayerGeneral = "general"
	DictCodeTaxpayerSmall   = "small"

	// 企业状态
	DictCodeEnterpriseActive    = "active"
	DictCodeEnterpriseInactive  = "inactive"
	DictCodeEnterpriseSuspended = "suspended"
	DictCodeEnterpriseCancelled = "cancelled"

	// 发票类型
	DictCodeInvoiceSpecial    = "special"
	DictCodeInvoiceOrdinary   = "ordinary"
	DictCodeInvoiceElectronic = "electronic"
	DictCodeInvoiceRoll       = "roll"

	// 发票状态
	DictCodeInvoiceDraft      = "draft"
	DictCodeInvoiceIssued     = "issued"
	DictCodeInvoiceCancelled  = "cancelled"
	DictCodeInvoiceInvalid    = "invalid"
	DictCodeInvoiceRedFlushed = "red_flushed"

	// 申报状态
	DictCodeDeclarationDraft     = "draft"
	DictCodeDeclarationSubmitted = "submitted"
	DictCodeDeclarationApproved  = "approved"
	DictCodeDeclarationRejected  = "rejected"
	DictCodeDeclarationPaid      = "paid"
	DictCodeDeclarationOverdue   = "overdue"

	// 缴费方式
	DictCodePaymentBankTransfer = "bank_transfer"
	DictCodePaymentOnline       = "online"
	DictCodePaymentCash         = "cash"
	DictCodePaymentCheck        = "check"
	DictCodePaymentDeduction    = "deduction"

	// 风险等级
	DictCodeRiskLow      = "low"
	DictCodeRiskMedium   = "medium"
	DictCodeRiskHigh     = "high"
	DictCodeRiskCritical = "critical"
)

// DictionaryListRequest 字典列表请求
type DictionaryListRequest struct {
	Category *string `json:"category" form:"category"`                         // 字典分类
	DictCode *string `json:"dict_code" form:"dict_code"`                       // 字典编码
	DictName *string `json:"dict_name" form:"dict_name"`                       // 字典名称
	IsActive *bool   `json:"is_active" form:"is_active"`                       // 是否启用
	Page     int     `json:"page" form:"page" binding:"min=1"`                 // 页码
	PageSize int     `json:"pageSize" form:"pageSize" binding:"min=1,max=100"` // 每页大小
}

// DictionaryListResponse 字典列表响应
type DictionaryListResponse struct {
	Dictionaries []DataDictionary `json:"dictionaries"` // 字典列表
	Total        int64            `json:"total"`        // 总数
	Page         int              `json:"page"`         // 当前页
	PageSize     int              `json:"pageSize"`     // 每页大小
	TotalPages   int              `json:"totalPages"`   // 总页数
}
