package model

import (
	"time"
)

// MessageLog 消息发送日志模型
type MessageLog struct {
	ID               string     `json:"id" gorm:"primaryKey;size:36;comment:主键ID"`
	TemplateID       string     `json:"templateId" gorm:"not null;size:36;index:idx_message_logs_template_id;comment:模板ID"`
	RecipientID      *string    `json:"recipientId" gorm:"size:36;index:idx_message_logs_recipient_id;comment:接收者用户ID"`
	RecipientAddress string     `json:"recipientAddress" gorm:"not null;size:255;comment:接收者地址（邮箱、手机号等）"`
	Channel          string     `json:"channel" gorm:"not null;size:50;comment:发送渠道（email, sms, etc.）"`
	Subject          *string    `json:"subject" gorm:"size:255;comment:消息主题"`
	Content          string     `json:"content" gorm:"type:text;not null;comment:消息内容"`
	Status           string     `json:"status" gorm:"not null;size:50;index:idx_message_logs_status;comment:发送状态"`
	ScheduledAt      *time.Time `json:"scheduledAt" gorm:"comment:计划发送时间"`
	SentAt           *time.Time `json:"sentAt" gorm:"comment:实际发送时间"`
	ErrorMessage     *string    `json:"errorMessage" gorm:"type:text;comment:错误信息"`
	ProviderResponse *string    `json:"providerResponse" gorm:"type:text;comment:服务商返回的原始响应"`
	RetryCount       int        `json:"retryCount" gorm:"default:0;comment:重试次数"`
	MaxRetries       int        `json:"maxRetries" gorm:"default:3;comment:最大重试次数"`
	ScheduleTime     *time.Time `json:"scheduleTime" gorm:"comment:计划发送时间"`
	CreatedAt        time.Time  `json:"createdAt" gorm:"not null;default:CURRENT_TIMESTAMP;comment:创建时间"`
	UpdatedAt        time.Time  `json:"updatedAt" gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间"`

	// 关联关系
	Template *MessageTemplate `json:"template,omitempty" gorm:"foreignKey:TemplateID;references:ID;constraint:OnDelete:CASCADE"`
	User     *User            `json:"user,omitempty" gorm:"foreignKey:RecipientID;references:ID"`
}

// 消息发送状态常量
const (
	MessageStatusPending   = "pending"   // 待发送
	MessageStatusSending   = "sending"   // 发送中
	MessageStatusSent      = "sent"      // 已发送
	MessageStatusDelivered = "delivered" // 已送达
	MessageStatusRead      = "read"      // 已阅读
	MessageStatusFailed    = "failed"    // 发送失败
	MessageStatusCancelled = "cancelled" // 已取消
	MessageStatusExpired   = "expired"   // 已过期
)

// MessageLogCreateRequest 创建消息日志请求
type MessageLogCreateRequest struct {
	TemplateID       string     `json:"templateId" binding:"required"`
	RecipientID      *string    `json:"recipientId"`
	RecipientAddress string     `json:"recipientAddress" binding:"required"`
	Channel          string     `json:"channel" binding:"required"`
	Subject          *string    `json:"subject"`
	Content          string     `json:"content" binding:"required"`
	ScheduledAt      *time.Time `json:"scheduledAt"`
}

// MessageLogUpdateRequest 更新消息日志请求
type MessageLogUpdateRequest struct {
	Status           *string    `json:"status"`
	SentAt           *time.Time `json:"sentAt"`
	ErrorMessage     *string    `json:"errorMessage"`
	ProviderResponse *string    `json:"providerResponse"`
}

// MessageLogFilter 消息日志过滤条件
type MessageLogFilter struct {
	TemplateID       string     `json:"templateId"`
	RecipientID      string     `json:"recipientId"`
	RecipientAddress string     `json:"recipientAddress"`
	Channel          string     `json:"channel"`
	Status           string     `json:"status"`
	CreatedStart     *time.Time `json:"createdStart"`
	CreatedEnd       *time.Time `json:"createdEnd"`
	SentStart        *time.Time `json:"sentStart"`
	SentEnd          *time.Time `json:"sentEnd"`
}

// MessageLogResponse 消息日志响应
type MessageLogResponse struct {
	ID               string     `json:"id"`
	TemplateID       string     `json:"templateId"`
	TemplateName     string     `json:"templateName,omitempty"`
	RecipientID      *string    `json:"recipientId"`
	RecipientName    string     `json:"recipientName,omitempty"`
	RecipientAddress string     `json:"recipientAddress"`
	Channel          string     `json:"channel"`
	Subject          *string    `json:"subject"`
	Content          string     `json:"content"`
	Status           string     `json:"status"`
	ScheduledAt      *time.Time `json:"scheduledAt"`
	SentAt           *time.Time `json:"sentAt"`
	ErrorMessage     *string    `json:"errorMessage"`
	ProviderResponse *string    `json:"providerResponse"`
	CreatedAt        time.Time  `json:"createdAt"`
	UpdatedAt        time.Time  `json:"updatedAt"`
}

// PaginatedMessageLogs 分页消息记录响应
type PaginatedMessageLogs struct {
	Logs  []MessageLogResponse `json:"logs"`
	Total int64                `json:"total"`
	Page  int                  `json:"page"`
	Limit int                  `json:"limit"`
}

// MessageLogStatistics 消息发送统计
type MessageLogStatistics struct {
	Total        int64              `json:"total"`
	Sent         int64              `json:"sent"`
	Delivered    int64              `json:"delivered"`
	Read         int64              `json:"read"`
	Failed       int64              `json:"failed"`
	Pending      int64              `json:"pending"`
	SuccessRate  float64            `json:"successRate"`
	DeliveryRate float64            `json:"deliveryRate"`
	ReadRate     float64            `json:"readRate"`
	TotalCost    float64            `json:"totalCost"`
	AverageCost  float64            `json:"averageCost"`
	ByChannel    map[string]int64   `json:"byChannel"`
	ByStatus     map[string]int64   `json:"byStatus"`
	ByPriority   map[string]int64   `json:"byPriority"`
	ByTemplate   map[string]int64   `json:"byTemplate"`
	ByHour       map[string]int64   `json:"byHour"`
	ByDay        map[string]int64   `json:"byDay"`
	TrendData    []MessageTrendData `json:"trendData"`
}

// MessageTrendData 消息趋势数据
type MessageTrendData struct {
	Date      string  `json:"date"`
	Total     int64   `json:"total"`
	Sent      int64   `json:"sent"`
	Delivered int64   `json:"delivered"`
	Failed    int64   `json:"failed"`
	Cost      float64 `json:"cost"`
}

// MessageLogBatchUpdateRequest 批量更新消息记录请求
type MessageLogBatchUpdateRequest struct {
	IDs    []string                `json:"ids" binding:"required,min=1"`
	Update MessageLogUpdateRequest `json:"update" binding:"required"`
}

// MessageLogRetryRequest 重试发送请求
type MessageLogRetryRequest struct {
	IDs []string `json:"ids" binding:"required,min=1"`
}

// MessageLogRetryResult 重试发送结果
type MessageLogRetryResult struct {
	Total     int                  `json:"total"`
	Success   int                  `json:"success"`
	Failed    int                  `json:"failed"`
	Skipped   int                  `json:"skipped"`
	Results   []MessageRetryResult `json:"results"`
	RetryTime time.Time            `json:"retryTime"`
}

// MessageRetryResult 单个消息重试结果
type MessageRetryResult struct {
	ID      string `json:"id"`
	Status  string `json:"status"`
	Error   string `json:"error"`
	Skipped bool   `json:"skipped"`
	Reason  string `json:"reason"`
}

// MessageLogExportRequest 导出消息记录请求
type MessageLogExportRequest struct {
	Filter    MessageLogFilter `json:"filter"`
	Format    string           `json:"format" binding:"required"`
	Fields    []string         `json:"fields"`
	FileName  string           `json:"fileName"`
	DateRange string           `json:"dateRange"`
}

// MessageLogExportResult 导出消息记录结果
type MessageLogExportResult struct {
	FileName    string    `json:"fileName"`
	FileSize    int64     `json:"fileSize"`
	RecordCount int64     `json:"recordCount"`
	Format      string    `json:"format"`
	DownloadURL string    `json:"downloadUrl"`
	ExportedAt  time.Time `json:"exportedAt"`
	ExpiresAt   time.Time `json:"expiresAt"`
}

// GetStatusName 获取状态名称
func GetStatusName(status string) string {
	switch status {
	case MessageStatusPending:
		return "待发送"
	case MessageStatusSending:
		return "发送中"
	case MessageStatusSent:
		return "已发送"
	case MessageStatusDelivered:
		return "已送达"
	case MessageStatusRead:
		return "已阅读"
	case MessageStatusFailed:
		return "发送失败"
	case MessageStatusCancelled:
		return "已取消"
	case MessageStatusExpired:
		return "已过期"
	default:
		return "未知状态"
	}
}

// IsValidStatus 验证状态是否有效
func IsValidStatus(status string) bool {
	validStatuses := []string{
		MessageStatusPending,
		MessageStatusSending,
		MessageStatusSent,
		MessageStatusDelivered,
		MessageStatusRead,
		MessageStatusFailed,
		MessageStatusCancelled,
		MessageStatusExpired,
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// IsSuccessStatus 判断是否为成功状态
func IsSuccessStatus(status string) bool {
	return status == MessageStatusSent || status == MessageStatusDelivered || status == MessageStatusRead
}

// IsFailureStatus 判断是否为失败状态
func IsFailureStatus(status string) bool {
	return status == MessageStatusFailed || status == MessageStatusCancelled || status == MessageStatusExpired
}

// IsPendingStatus 判断是否为待处理状态
func IsPendingStatus(status string) bool {
	return status == MessageStatusPending || status == MessageStatusSending
}

// CanRetry 判断是否可以重试
func (m *MessageLog) CanRetry() bool {
	return IsFailureStatus(m.Status) && m.RetryCount < m.MaxRetries
}

// IsExpired 判断是否已过期
func (m *MessageLog) IsExpired() bool {
	if m.ScheduledAt == nil {
		return false
	}
	// 如果计划发送时间超过24小时仍未发送，则认为已过期
	return time.Since(*m.ScheduledAt) > 24*time.Hour && IsPendingStatus(m.Status)
}

// GetDuration 获取发送耗时（毫秒）
func (m *MessageLog) GetDuration() *int64 {
	if m.SentAt == nil {
		return nil
	}
	duration := m.SentAt.Sub(m.CreatedAt).Milliseconds()
	return &duration
}

// TableName 指定表名
func (MessageLog) TableName() string {
	return "message_logs"
}
