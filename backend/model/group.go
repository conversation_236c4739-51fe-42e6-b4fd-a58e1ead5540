package model

import (
	"time"
)

// Group represents a company group in the system
type Group struct {
	ID          string        `json:"id" gorm:"primaryKey"`
	Name        string        `json:"name" gorm:"uniqueIndex:idx_group_name;size:191"`
	Description string        `json:"description"`
	ParentID    *string       `json:"parent_id,omitempty" gorm:"index"` // Null for root groups
	Path        string        `json:"path" gorm:"index"`                // Hierarchical path, e.g., "root/child/grandchild"
	Level       int           `json:"level"`                            // Hierarchical level
	IsActive    bool          `json:"is_active" gorm:"default:true"`
	Enterprises []Enterprise  `json:"enterprises" gorm:"foreignKey:GroupID"`
	Members     []GroupMember `json:"members" gorm:"foreignKey:GroupID"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
}

// GroupMember represents a user membership in a group
type GroupMember struct {
	ID        string    `json:"id" gorm:"primaryKey"`
	GroupID   string    `json:"group_id" gorm:"index"`
	UserID    string    `json:"user_id" gorm:"index"`
	Role      string    `json:"role"` // owner, admin, member, guest, etc.
	JoinedAt  time.Time `json:"joined_at"`
	InvitedBy string    `json:"invited_by,omitempty"`
	IsActive  bool      `json:"is_active" gorm:"default:true"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// GroupSettings represents settings for a group
type GroupSettings struct {
	ID                          string    `json:"id" gorm:"primaryKey"`
	GroupID                     string    `json:"group_id" gorm:"uniqueIndex:idx_group_settings_group_id;size:191"`
	DefaultCurrency             string    `json:"default_currency" gorm:"default:CNY"`
	DefaultTaxperiod            string    `json:"default_taxperiod" gorm:"default:monthly"` // monthly, quarterly, yearly
	EnableConsolidation         bool      `json:"enable_consolidation" gorm:"default:false"`
	EnableCrossTaxpayerTransfer bool      `json:"enable_cross_taxpayer_transfer" gorm:"default:false"`
	CreatedAt                   time.Time `json:"created_at"`
	UpdatedAt                   time.Time `json:"updated_at"`
}

// GroupCreateRequest represents the request to create a group
type GroupCreateRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	ParentID    string `json:"parent_id"`
}

// GroupUpdateRequest represents the request to update a group
type GroupUpdateRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	ParentID    string `json:"parent_id"`
	IsActive    *bool  `json:"is_active"`
}

// GroupMemberCreateRequest represents the request to add a member to a group
type GroupMemberCreateRequest struct {
	UserID    string `json:"user_id" binding:"required"`
	Role      string `json:"role" binding:"required"`
	InvitedBy string `json:"invited_by"`
}

// GroupMemberUpdateRequest represents the request to update a group member
type GroupMemberUpdateRequest struct {
	Role     string `json:"role"`
	IsActive *bool  `json:"is_active"`
}

// GroupSettingsUpdateRequest represents the request to update group settings
type GroupSettingsUpdateRequest struct {
	DefaultCurrency             string `json:"default_currency"`
	DefaultTaxperiod            string `json:"default_taxperiod"`
	EnableConsolidation         *bool  `json:"enable_consolidation"`
	EnableCrossTaxpayerTransfer *bool  `json:"enable_cross_taxpayer_transfer"`
}

// GroupConsolidationRequest represents the request for group consolidation
type GroupConsolidationRequest struct {
	GroupID          string    `json:"group_id" binding:"required"`
	TaxTypeID        string    `json:"tax_type_id" binding:"required"`
	TaxPeriodStart   time.Time `json:"tax_period_start" binding:"required"`
	TaxPeriodEnd     time.Time `json:"tax_period_end" binding:"required"`
	IncludeSubgroups bool      `json:"include_subgroups" gorm:"default:true"`
}

// GroupConsolidationResult represents the result of a group consolidation
type GroupConsolidationResult struct {
	GroupID             string                `json:"group_id"`
	TaxTypeID           string                `json:"tax_type_id"`
	TaxPeriodStart      time.Time             `json:"tax_period_start"`
	TaxPeriodEnd        time.Time             `json:"tax_period_end"`
	TotalTaxable        float64               `json:"total_taxable"`
	TotalTax            float64               `json:"total_tax"`
	EnterpriseBreakdown []EnterpriseBreakdown `json:"enterprise_breakdown"`
	CreatedAt           time.Time             `json:"created_at"`
}

// EnterpriseBreakdown represents the tax breakdown for an enterprise
type EnterpriseBreakdown struct {
	EnterpriseID   string  `json:"enterprise_id"`
	EnterpriseName string  `json:"enterprise_name"`
	TaxableAmount  float64 `json:"taxable_amount"`
	TaxAmount      float64 `json:"tax_amount"`
	Percentage     float64 `json:"percentage"` // Percentage of total
}
