package model

import (
	"time"
)

// IntegrationConfig represents an external system integration configuration
type IntegrationConfig struct {
	ID           string     `json:"id" gorm:"primaryKey"`
	EnterpriseID string     `json:"enterpriseId" gorm:"index"`
	Type         string     `json:"type" gorm:"size:50"` // e.g., "tax_bureau", "erp", "bank", etc.
	Name         string     `json:"name" gorm:"size:100"`
	Description  string     `json:"description" gorm:"type:text"`
	Endpoint     string     `json:"endpoint" gorm:"size:255"`
	ApiKey       string     `json:"apiKey" gorm:"size:255"`
	ApiSecret    string     `json:"apiSecret" gorm:"size:255"`
	Username     string     `json:"username" gorm:"size:100"`
	Password     string     `json:"password" gorm:"size:100"`
	Certificate  string     `json:"certificate" gorm:"type:text"`
	Settings     string     `json:"settings" gorm:"type:json"`
	Status       string     `json:"status" gorm:"size:20"` // "active", "inactive", "pending", "error"
	LastSyncAt   time.Time  `json:"lastSyncAt"`
	CreatedAt    time.Time  `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time  `json:"updatedAt" gorm:"autoUpdateTime"`
	DeletedAt    *time.Time `json:"deletedAt,omitempty" gorm:"index"`
}

// IntegrationLog represents a log entry for integration activities
type IntegrationLog struct {
	ID            string    `json:"id" gorm:"primaryKey"`
	IntegrationID string    `json:"integrationId" gorm:"index"`
	Action        string    `json:"action" gorm:"size:50"` // e.g., "sync", "push", "pull", etc.
	Status        string    `json:"status" gorm:"size:20"` // "success", "error", "warning", etc.
	Message       string    `json:"message" gorm:"type:text"`
	Details       string    `json:"details" gorm:"type:json"`
	RequestData   string    `json:"requestData" gorm:"type:text"`
	ResponseData  string    `json:"responseData" gorm:"type:text"`
	Duration      int       `json:"duration"` // in milliseconds
	CreatedAt     time.Time `json:"createdAt" gorm:"autoCreateTime"`
}

// IntegrationStatus represents the status of an integration
type IntegrationStatus struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Name        string    `json:"name"`
	Status      string    `json:"status"`
	LastSyncAt  time.Time `json:"lastSyncAt"`
	IsHealthy   bool      `json:"isHealthy"`
	LastMessage string    `json:"lastMessage"`
}

// IntegrationCreateRequest represents a request to create an integration
type IntegrationCreateRequest struct {
	EnterpriseID string                 `json:"enterpriseId" binding:"required"`
	Type         string                 `json:"type" binding:"required"`
	Name         string                 `json:"name" binding:"required"`
	Description  string                 `json:"description"`
	Endpoint     string                 `json:"endpoint"`
	ApiKey       string                 `json:"apiKey"`
	ApiSecret    string                 `json:"apiSecret"`
	Username     string                 `json:"username"`
	Password     string                 `json:"password"`
	Certificate  string                 `json:"certificate"`
	Settings     map[string]interface{} `json:"settings"`
}

// IntegrationUpdateRequest represents a request to update an integration
type IntegrationUpdateRequest struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Endpoint    string                 `json:"endpoint"`
	ApiKey      string                 `json:"apiKey"`
	ApiSecret   string                 `json:"apiSecret"`
	Username    string                 `json:"username"`
	Password    string                 `json:"password"`
	Certificate string                 `json:"certificate"`
	Settings    map[string]interface{} `json:"settings"`
	Status      string                 `json:"status"`
}

// IntegrationResponse represents an integration response
type IntegrationResponse struct {
	ID           string                 `json:"id"`
	EnterpriseID string                 `json:"enterpriseId"`
	Type         string                 `json:"type"`
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	Endpoint     string                 `json:"endpoint"`
	Settings     map[string]interface{} `json:"settings"`
	Status       string                 `json:"status"`
	LastSyncAt   time.Time              `json:"lastSyncAt"`
	CreatedAt    time.Time              `json:"createdAt"`
	UpdatedAt    time.Time              `json:"updatedAt"`
}

// IntegrationConnectRequest 表示连接外部系统的请求
type IntegrationConnectRequest struct {
	SystemID      string            `json:"systemId" binding:"required"`      // 系统ID
	SystemName    string            `json:"systemName" binding:"required"`    // 系统名称
	SystemType    string            `json:"systemType" binding:"required"`    // 系统类型
	ConnectionURL string            `json:"connectionUrl" binding:"required"` // 连接URL
	Credentials   map[string]string `json:"credentials" binding:"required"`   // 凭证信息
	Settings      map[string]string `json:"settings"`                         // 配置设置
}

// SystemStatus 表示单个系统的连接状态
type SystemStatus struct {
	SystemID      string    `json:"systemId"`               // 系统ID
	SystemName    string    `json:"systemName"`             // 系统名称
	SystemType    string    `json:"systemType"`             // 系统类型
	IsConnected   bool      `json:"isConnected"`            // 是否已连接
	IsAvailable   bool      `json:"isAvailable"`            // 是否可用
	LastSyncTime  time.Time `json:"lastSyncTime,omitempty"` // 最后同步时间
	ConnectionURL string    `json:"connectionUrl"`          // 连接URL（掩码处理）
	StatusMessage string    `json:"statusMessage"`          // 状态信息
}

// GovSubmissionPayload 表示提交到政府系统的数据
type GovSubmissionPayload struct {
	DeclarationID   string    `json:"declarationId"`   // 申报ID
	EnterpriseName  string    `json:"enterpriseName"`  // 企业名称
	EnterpriseTaxID string    `json:"enterpriseTaxId"` // 企业税号
	TaxTypeName     string    `json:"taxTypeName"`     // 税种名称
	TaxTypeCode     string    `json:"taxTypeCode"`     // 税种代码
	Period          string    `json:"period"`          // 期间
	Year            int       `json:"year"`            // 年份
	TaxableAmount   float64   `json:"taxableAmount"`   // 计税基数
	TaxAmount       float64   `json:"taxAmount"`       // 税额
	SubmittedAt     time.Time `json:"submittedAt"`     // 提交时间
}

// GovSubmissionResponse 表示政府系统的响应
type GovSubmissionResponse struct {
	ReferenceNumber string    `json:"referenceNumber"` // 参考号
	ProcessedAt     time.Time `json:"processedAt"`     // 处理时间
	Status          string    `json:"status"`          // 状态
	Message         string    `json:"message"`         // 消息
	ConfirmationURL string    `json:"confirmationUrl"` // 确认链接
}

// DeclarationSubmissionResult 表示申报提交结果
type DeclarationSubmissionResult struct {
	DeclarationID   string    `json:"declarationId"`   // 申报ID
	Success         bool      `json:"success"`         // 是否成功
	ReferenceNumber string    `json:"referenceNumber"` // 参考号
	SubmissionDate  time.Time `json:"submissionDate"`  // 提交日期
	Message         string    `json:"message"`         // 消息
	ConfirmationURL string    `json:"confirmationUrl"` // 确认链接
}

// SyncResult 表示同步结果
type SyncResult struct {
	EnterpriseID string    `json:"enterpriseId"` // 企业ID
	SyncedAt     time.Time `json:"syncedAt"`     // 同步时间
	Added        int       `json:"added"`        // 新增数量
	Updated      int       `json:"updated"`      // 更新数量
	Failed       int       `json:"failed"`       // 失败数量
	Message      string    `json:"message"`      // 消息
}

// GovTaxRate 表示从政府同步的税率
type GovTaxRate struct {
	TaxTypeCode   string    `json:"taxTypeCode"`   // 税种代码
	TaxTypeName   string    `json:"taxTypeName"`   // 税种名称
	Description   string    `json:"description"`   // 描述
	Rate          float64   `json:"rate"`          // 税率
	EffectiveDate time.Time `json:"effectiveDate"` // 生效日期
}
