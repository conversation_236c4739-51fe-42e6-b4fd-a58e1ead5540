package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// TaxFilingMetricsType 指标类型
type TaxFilingMetricsType string

const (
	MetricsTypeSubmission TaxFilingMetricsType = "submission"
	MetricsTypeBatch      TaxFilingMetricsType = "batch"
	MetricsTypeSync       TaxFilingMetricsType = "sync"
	MetricsTypeCallback   TaxFilingMetricsType = "callback"
	MetricsTypeAPI        TaxFilingMetricsType = "api"
	MetricsTypeSystem     TaxFilingMetricsType = "system"
)

// MetricsData 指标数据结构
type MetricsData map[string]interface{}

// Value 实现 driver.Valuer 接口
func (m MetricsData) Value() (driver.Value, error) {
	if len(m) == 0 {
		return nil, nil
	}
	return json.Marshal(m)
}

// Scan 实现 sql.Scanner 接口
func (m *MetricsData) Scan(value interface{}) error {
	if value == nil {
		*m = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into MetricsData", value)
	}

	return json.Unmarshal(bytes, m)
}

// TaxFilingMetrics 税务申报性能指标模型
type TaxFilingMetrics struct {
	ID           string               `gorm:"primaryKey;size:50" json:"id"`
	SubmissionID *string              `gorm:"size:50;index" json:"submission_id,omitempty"`
	BatchID      *string              `gorm:"size:50;index" json:"batch_id,omitempty"`
	MetricsType  TaxFilingMetricsType `gorm:"size:20;not null;index" json:"metrics_type"`

	// 性能指标
	ProcessingTime int `gorm:"not null;default:0" json:"processing_time"` // 总处理时间(毫秒)
	APICallTime    int `gorm:"not null;default:0" json:"api_call_time"`   // API调用时间(毫秒)
	DatabaseTime   int `gorm:"not null;default:0" json:"database_time"`   // 数据库操作时间(毫秒)
	CacheTime      int `gorm:"not null;default:0" json:"cache_time"`      // 缓存操作时间(毫秒)
	ValidationTime int `gorm:"not null;default:0" json:"validation_time"` // 数据验证时间(毫秒)

	// 缓存指标
	CacheHitRate   float64 `gorm:"type:decimal(5,2);not null;default:0.00" json:"cache_hit_rate"` // 缓存命中率(%)
	CacheHitCount  int     `gorm:"not null;default:0" json:"cache_hit_count"`                     // 缓存命中次数
	CacheMissCount int     `gorm:"not null;default:0" json:"cache_miss_count"`                    // 缓存未命中次数

	// 错误指标
	ErrorCount   int `gorm:"not null;default:0" json:"error_count"`   // 错误次数
	RetryCount   int `gorm:"not null;default:0" json:"retry_count"`   // 重试次数
	TimeoutCount int `gorm:"not null;default:0" json:"timeout_count"` // 超时次数

	// 资源使用指标
	MemoryUsage int     `gorm:"not null;default:0" json:"memory_usage"`                   // 内存使用(KB)
	CPUUsage    float64 `gorm:"type:decimal(5,2);not null;default:0.00" json:"cpu_usage"` // CPU使用率(%)

	// 业务指标
	DataSize        int `gorm:"not null;default:0" json:"data_size"`        // 数据大小(字节)
	RecordCount     int `gorm:"not null;default:0" json:"record_count"`     // 记录数量
	ConcurrentCount int `gorm:"not null;default:0" json:"concurrent_count"` // 并发数量

	// 网络指标
	NetworkLatency int `gorm:"not null;default:0" json:"network_latency"` // 网络延迟(毫秒)
	BandwidthUsage int `gorm:"not null;default:0" json:"bandwidth_usage"` // 带宽使用(KB)

	// 扩展指标
	CustomMetrics MetricsData `gorm:"type:json" json:"custom_metrics,omitempty"`

	// 环境信息
	Environment string `gorm:"size:20;not null;default:production" json:"environment"`
	ServerNode  string `gorm:"size:50" json:"server_node,omitempty"`
	Version     string `gorm:"size:20" json:"version,omitempty"`

	// 时间信息
	StartTime time.Time `gorm:"not null" json:"start_time"`
	EndTime   time.Time `gorm:"not null" json:"end_time"`
	CreatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;index" json:"created_at"`

	// 关联
	Submission *TaxFilingSubmission `gorm:"foreignKey:SubmissionID" json:"submission,omitempty"`
	Batch      *TaxFilingBatch      `gorm:"foreignKey:BatchID" json:"batch,omitempty"`
}

// TableName 指定表名
func (TaxFilingMetrics) TableName() string {
	return "tax_filing_metrics"
}

// BeforeCreate GORM钩子 - 创建前
func (t *TaxFilingMetrics) BeforeCreate(tx *gorm.DB) error {
	if t.ID == "" {
		t.ID = GenerateID()
	}
	if t.EndTime.IsZero() {
		t.EndTime = time.Now()
	}
	if t.StartTime.IsZero() {
		t.StartTime = t.EndTime.Add(-time.Duration(t.ProcessingTime) * time.Millisecond)
	}
	return nil
}

// CalculateCacheHitRate 计算缓存命中率
func (t *TaxFilingMetrics) CalculateCacheHitRate() {
	total := t.CacheHitCount + t.CacheMissCount
	if total > 0 {
		t.CacheHitRate = float64(t.CacheHitCount) / float64(total) * 100
	}
}

// SetCustomMetric 设置自定义指标
func (t *TaxFilingMetrics) SetCustomMetric(key string, value interface{}) {
	if t.CustomMetrics == nil {
		t.CustomMetrics = make(MetricsData)
	}
	t.CustomMetrics[key] = value
}

// GetCustomMetric 获取自定义指标
func (t *TaxFilingMetrics) GetCustomMetric(key string) (interface{}, bool) {
	if t.CustomMetrics == nil {
		return nil, false
	}
	value, exists := t.CustomMetrics[key]
	return value, exists
}

// CreateSubmissionMetrics 创建申报指标记录
func CreateSubmissionMetrics(submissionID string) *TaxFilingMetrics {
	return &TaxFilingMetrics{
		ID:           GenerateID(),
		SubmissionID: &submissionID,
		MetricsType:  MetricsTypeSubmission,
		Environment:  "production",
		StartTime:    time.Now(),
		CreatedAt:    time.Now(),
	}
}

// CreateBatchMetrics 创建批次指标记录
func CreateBatchMetrics(batchID string) *TaxFilingMetrics {
	return &TaxFilingMetrics{
		ID:          GenerateID(),
		BatchID:     &batchID,
		MetricsType: MetricsTypeBatch,
		Environment: "production",
		StartTime:   time.Now(),
		CreatedAt:   time.Now(),
	}
}

// CreateAPIMetrics 创建API指标记录
func CreateAPIMetrics() *TaxFilingMetrics {
	return &TaxFilingMetrics{
		ID:          GenerateID(),
		MetricsType: MetricsTypeAPI,
		Environment: "production",
		StartTime:   time.Now(),
		CreatedAt:   time.Now(),
	}
}

// CreateSystemMetrics 创建系统指标记录
func CreateSystemMetrics() *TaxFilingMetrics {
	return &TaxFilingMetrics{
		ID:          GenerateID(),
		MetricsType: MetricsTypeSystem,
		Environment: "production",
		StartTime:   time.Now(),
		CreatedAt:   time.Now(),
	}
}

// TaxFilingMetricsQuery 指标查询条件
type TaxFilingMetricsQuery struct {
	SubmissionID *string               `json:"submission_id,omitempty"`
	BatchID      *string               `json:"batch_id,omitempty"`
	MetricsType  *TaxFilingMetricsType `json:"metrics_type,omitempty"`
	Environment  *string               `json:"environment,omitempty"`
	StartTime    *time.Time            `json:"start_time,omitempty"`
	EndTime      *time.Time            `json:"end_time,omitempty"`

	// 性能过滤条件
	MinProcessingTime *int     `json:"min_processing_time,omitempty"`
	MaxProcessingTime *int     `json:"max_processing_time,omitempty"`
	MinCacheHitRate   *float64 `json:"min_cache_hit_rate,omitempty"`
	MaxErrorCount     *int     `json:"max_error_count,omitempty"`

	// 分页参数
	Page     int `json:"page" default:"1"`
	PageSize int `json:"page_size" default:"20"`

	// 排序参数
	OrderBy string `json:"order_by" default:"created_at"`
	Order   string `json:"order" default:"desc"`
}

// TaxFilingMetricsResponse 指标响应
type TaxFilingMetricsResponse struct {
	TaxFilingMetrics

	// 计算字段
	ProcessingTimeDisplay string `json:"processing_time_display"`
	CacheHitRateDisplay   string `json:"cache_hit_rate_display"`
	ErrorRateDisplay      string `json:"error_rate_display"`
	ThroughputDisplay     string `json:"throughput_display"`
}

// GetProcessingTimeDisplay 获取处理时间显示
func (t *TaxFilingMetricsResponse) GetProcessingTimeDisplay() string {
	duration := time.Duration(t.ProcessingTime) * time.Millisecond
	if duration < time.Second {
		return fmt.Sprintf("%dms", t.ProcessingTime)
	} else if duration < time.Minute {
		return fmt.Sprintf("%.2fs", duration.Seconds())
	} else {
		return fmt.Sprintf("%.2fm", duration.Minutes())
	}
}

// GetCacheHitRateDisplay 获取缓存命中率显示
func (t *TaxFilingMetricsResponse) GetCacheHitRateDisplay() string {
	return fmt.Sprintf("%.2f%%", t.CacheHitRate)
}

// GetErrorRateDisplay 获取错误率显示
func (t *TaxFilingMetricsResponse) GetErrorRateDisplay() string {
	total := t.RecordCount
	if total == 0 {
		return "0.00%"
	}
	errorRate := float64(t.ErrorCount) / float64(total) * 100
	return fmt.Sprintf("%.2f%%", errorRate)
}

// GetThroughputDisplay 获取吞吐量显示
func (t *TaxFilingMetricsResponse) GetThroughputDisplay() string {
	duration := t.EndTime.Sub(t.StartTime)
	if duration.Seconds() == 0 {
		return "0 records/s"
	}
	throughput := float64(t.RecordCount) / duration.Seconds()
	return fmt.Sprintf("%.2f records/s", throughput)
}

// TaxFilingMetricsAggregation 指标聚合结果
type TaxFilingMetricsAggregation struct {
	MetricsType       TaxFilingMetricsType `json:"metrics_type"`
	Count             int64                `json:"count"`
	AvgProcessingTime float64              `json:"avg_processing_time"`
	MaxProcessingTime int                  `json:"max_processing_time"`
	MinProcessingTime int                  `json:"min_processing_time"`
	AvgCacheHitRate   float64              `json:"avg_cache_hit_rate"`
	TotalErrorCount   int64                `json:"total_error_count"`
	TotalRetryCount   int64                `json:"total_retry_count"`
	AvgMemoryUsage    float64              `json:"avg_memory_usage"`
	AvgCPUUsage       float64              `json:"avg_cpu_usage"`
	Date              string               `json:"date"`
}

// TaxFilingMetricsCreateRequest 指标创建请求
type TaxFilingMetricsCreateRequest struct {
	SubmissionID    *string              `json:"submission_id,omitempty"`
	BatchID         *string              `json:"batch_id,omitempty"`
	MetricsType     TaxFilingMetricsType `json:"metrics_type"`
	ProcessingTime  int                  `json:"processing_time"`
	APICallTime     int                  `json:"api_call_time"`
	DatabaseTime    int                  `json:"database_time"`
	CacheTime       int                  `json:"cache_time"`
	ValidationTime  int                  `json:"validation_time"`
	CacheHitCount   int                  `json:"cache_hit_count"`
	CacheMissCount  int                  `json:"cache_miss_count"`
	ErrorCount      int                  `json:"error_count"`
	RetryCount      int                  `json:"retry_count"`
	TimeoutCount    int                  `json:"timeout_count"`
	MemoryUsage     int                  `json:"memory_usage"`
	CPUUsage        float64              `json:"cpu_usage"`
	DataSize        int                  `json:"data_size"`
	RecordCount     int                  `json:"record_count"`
	ConcurrentCount int                  `json:"concurrent_count"`
	NetworkLatency  int                  `json:"network_latency"`
	BandwidthUsage  int                  `json:"bandwidth_usage"`
	CustomMetrics   MetricsData          `json:"custom_metrics,omitempty"`
	Environment     string               `json:"environment"`
	ServerNode      string               `json:"server_node,omitempty"`
	Version         string               `json:"version,omitempty"`
	StartTime       time.Time            `json:"start_time"`
	EndTime         time.Time            `json:"end_time"`
}
