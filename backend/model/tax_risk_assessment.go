package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// TaxRiskAssessment 税务风险评估模型
type TaxRiskAssessment struct {
	ID                   string          `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:风险评估ID"`
	EnterpriseID         string          `json:"enterprise_id" gorm:"type:varchar(36);not null;column:enterprise_id;index:idx_tax_risk_assessments_enterprise_id;comment:企业ID"`
	AssessmentNumber     string          `json:"assessment_number" gorm:"type:varchar(50);not null;uniqueIndex:uk_tax_risk_assessments_number;column:assessment_number;comment:评估编号"`
	AssessmentType       string          `json:"assessment_type" gorm:"type:enum('annual','quarterly','special','real_time');not null;column:assessment_type;index:idx_tax_risk_assessments_type;comment:评估类型"`
	AssessmentPeriod     string          `json:"assessment_period" gorm:"type:varchar(20);not null;column:assessment_period;comment:评估期间"`
	RiskLevel            string          `json:"risk_level" gorm:"type:enum('low','medium','high','critical');not null;column:risk_level;index:idx_tax_risk_assessments_risk_level;comment:风险等级"`
	OverallScore         decimal.Decimal `json:"overall_score" gorm:"type:decimal(5,2);not null;column:overall_score;comment:综合得分"`
	TaxComplianceScore   decimal.Decimal `json:"tax_compliance_score" gorm:"type:decimal(5,2);column:tax_compliance_score;comment:税务合规得分"`
	FinancialHealthScore decimal.Decimal `json:"financial_health_score" gorm:"type:decimal(5,2);column:financial_health_score;comment:财务健康得分"`
	BusinessRiskScore    decimal.Decimal `json:"business_risk_score" gorm:"type:decimal(5,2);column:business_risk_score;comment:经营风险得分"`
	IndustryRiskScore    decimal.Decimal `json:"industry_risk_score" gorm:"type:decimal(5,2);column:industry_risk_score;comment:行业风险得分"`
	RiskIndicators       string          `json:"risk_indicators" gorm:"type:json;column:risk_indicators;comment:风险指标"`
	RiskFactors          string          `json:"risk_factors" gorm:"type:json;column:risk_factors;comment:风险因素"`
	WarningSignals       string          `json:"warning_signals" gorm:"type:json;column:warning_signals;comment:预警信号"`
	Recommendations      string          `json:"recommendations" gorm:"type:text;column:recommendations;comment:建议措施"`
	FollowUpActions      string          `json:"follow_up_actions" gorm:"type:json;column:follow_up_actions;comment:后续行动"`
	AssessmentDate       time.Time       `json:"assessment_date" gorm:"not null;column:assessment_date;index:idx_tax_risk_assessments_assessment_date;comment:评估日期"`
	AssessorID           *string         `json:"assessor_id" gorm:"type:varchar(36);column:assessor_id;comment:评估人ID"`
	AssessorName         string          `json:"assessor_name" gorm:"type:varchar(100);column:assessor_name;comment:评估人姓名"`
	ReviewDate           *time.Time      `json:"review_date" gorm:"type:date;column:review_date;comment:复核日期"`
	ReviewerID           *string         `json:"reviewer_id" gorm:"type:varchar(36);column:reviewer_id;comment:复核人ID"`
	Status               string          `json:"status" gorm:"type:enum('draft','completed','reviewed','archived');default:draft;column:status;comment:状态"`
	CreatedAt            time.Time       `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt            time.Time       `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	Enterprise *Enterprise `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
}

// 风险评估类型常量
const (
	AssessmentTypeAnnual    = "annual"
	AssessmentTypeQuarterly = "quarterly"
	AssessmentTypeSpecial   = "special"
	AssessmentTypeRealTime  = "real_time"
)

// 风险评估状态常量
const (
	AssessmentStatusDraft     = "draft"
	AssessmentStatusCompleted = "completed"
	AssessmentStatusReviewed  = "reviewed"
	AssessmentStatusArchived  = "archived"
)

// TaxRiskAssessmentCreateRequest 创建税务风险评估请求
type TaxRiskAssessmentCreateRequest struct {
	EnterpriseID         string          `json:"enterprise_id" binding:"required"`
	AssessmentDate       string          `json:"assessment_date" binding:"required"`
	AssessmentPeriod     string          `json:"assessment_period" binding:"required"`
	RiskLevel            string          `json:"risk_level" binding:"required"`
	OverallScore         decimal.Decimal `json:"overall_score" binding:"required"`
	TaxComplianceScore   decimal.Decimal `json:"tax_compliance_score"`
	FinancialHealthScore decimal.Decimal `json:"financial_health_score"`
	BusinessRiskScore    decimal.Decimal `json:"business_risk_score"`
	IndustryRiskScore    decimal.Decimal `json:"industry_risk_score"`
	RiskFactors          string          `json:"risk_factors"`
	Recommendations      string          `json:"recommendations"`
	AssessorID           string          `json:"assessor_id" binding:"required"`
	AssessorName         string          `json:"assessor_name" binding:"required"`
	NextAssessmentDate   string          `json:"next_assessment_date"`
}

// TaxRiskAssessmentUpdateRequest 更新税务风险评估请求
type TaxRiskAssessmentUpdateRequest struct {
	AssessmentDate       *string  `json:"assessment_date"`
	AssessmentPeriod     *string  `json:"assessment_period"`
	RiskLevel            *string  `json:"risk_level"`
	RiskScore            *float64 `json:"risk_score" binding:"omitempty,gte=0,lte=100"`
	TaxComplianceScore   *float64 `json:"tax_compliance_score" binding:"omitempty,gte=0,lte=100"`
	FinancialRiskScore   *float64 `json:"financial_risk_score" binding:"omitempty,gte=0,lte=100"`
	OperationalRiskScore *float64 `json:"operational_risk_score" binding:"omitempty,gte=0,lte=100"`
	RiskFactors          *string  `json:"risk_factors"`
	Recommendations      *string  `json:"recommendations"`
	AssessorID           *string  `json:"assessor_id"`
	AssessorName         *string  `json:"assessor_name"`
	Status               *string  `json:"status"`
	NextAssessmentDate   *string  `json:"next_assessment_date"`
}

// TaxRiskAssessmentResponse 税务风险评估响应
type TaxRiskAssessmentResponse struct {
	ID                   string     `json:"id"`
	EnterpriseID         string     `json:"enterprise_id"`
	EnterpriseName       string     `json:"enterprise_name"`
	AssessmentDate       time.Time  `json:"assessment_date"`
	AssessmentPeriod     string     `json:"assessment_period"`
	RiskLevel            string     `json:"risk_level"`
	RiskScore            float64    `json:"risk_score"`
	TaxComplianceScore   float64    `json:"tax_compliance_score"`
	FinancialRiskScore   float64    `json:"financial_risk_score"`
	OperationalRiskScore float64    `json:"operational_risk_score"`
	RiskFactors          string     `json:"risk_factors"`
	Recommendations      string     `json:"recommendations"`
	AssessorID           string     `json:"assessor_id"`
	AssessorName         string     `json:"assessor_name"`
	Status               string     `json:"status"`
	ReviewedBy           string     `json:"reviewed_by"`
	ReviewedAt           *time.Time `json:"reviewed_at"`
	NextAssessmentDate   *time.Time `json:"next_assessment_date"`
	CreatedAt            time.Time  `json:"created_at"`
	UpdatedAt            time.Time  `json:"updated_at"`
}

// 评估期间常量
const (
	AssessmentPeriodMonthly   = "monthly"   // 月度
	AssessmentPeriodQuarterly = "quarterly" // 季度
	AssessmentPeriodAnnual    = "annual"    // 年度
	AssessmentPeriodSpecial   = "special"   // 专项
)
