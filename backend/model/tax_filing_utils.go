package model

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"
)

// GenerateID 生成唯一ID
func GenerateID() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳作为后备方案
		return fmt.Sprintf("tf_%d", time.Now().UnixNano())
	}
	return fmt.Sprintf("tf_%s", hex.EncodeToString(bytes))
}

// GenerateBatchID 生成批次ID
func GenerateBatchID() string {
	bytes := make([]byte, 12)
	if _, err := rand.Read(bytes); err != nil {
		return fmt.Sprintf("batch_%d", time.Now().UnixNano())
	}
	return fmt.Sprintf("batch_%s", hex.EncodeToString(bytes))
}

// GenerateReferenceNumber 生成参考号
func GenerateReferenceNumber(provinceCode string) string {
	timestamp := time.Now().Format("20060102150405")
	bytes := make([]byte, 4)
	if _, err := rand.Read(bytes); err != nil {
		return fmt.Sprintf("%s%s%04d", provinceCode, timestamp, time.Now().Nanosecond()%10000)
	}
	return fmt.Sprintf("%s%s%s", provinceCode, timestamp, hex.EncodeToString(bytes))
}

// TaxFilingConstants 税务申报常量
type TaxFilingConstants struct {
	// 默认配置
	DefaultTimeout    int     `json:"default_timeout"`
	DefaultMaxRetries int     `json:"default_max_retries"`
	DefaultRetryDelay float64 `json:"default_retry_delay"`

	// 批次配置
	MaxBatchSize        int `json:"max_batch_size"`
	BatchTimeoutMinutes int `json:"batch_timeout_minutes"`

	// 回调配置
	CallbackTimeoutSeconds int `json:"callback_timeout_seconds"`
	MaxCallbackRetries     int `json:"max_callback_retries"`

	// 状态同步配置
	StatusSyncIntervalSeconds int `json:"status_sync_interval_seconds"`
	MaxStatusSyncRetries      int `json:"max_status_sync_retries"`
}

// GetTaxFilingConstants 获取税务申报常量
func GetTaxFilingConstants() *TaxFilingConstants {
	return &TaxFilingConstants{
		DefaultTimeout:            30,
		DefaultMaxRetries:         3,
		DefaultRetryDelay:         1.0,
		MaxBatchSize:              100,
		BatchTimeoutMinutes:       60,
		CallbackTimeoutSeconds:    30,
		MaxCallbackRetries:        3,
		StatusSyncIntervalSeconds: 300, // 5分钟
		MaxStatusSyncRetries:      5,
	}
}

// TaxFilingValidationRules 税务申报验证规则
type TaxFilingValidationRules struct {
	// 公司信息验证
	CompanyNameMaxLength        int `json:"company_name_max_length"`
	TaxIDMinLength              int `json:"tax_id_min_length"`
	TaxIDMaxLength              int `json:"tax_id_max_length"`
	RegistrationNumberMaxLength int `json:"registration_number_max_length"`

	// 税务数据验证
	MaxTaxRate       float64 `json:"max_tax_rate"`
	MaxTaxableAmount float64 `json:"max_taxable_amount"`
	MaxTaxAmount     float64 `json:"max_tax_amount"`

	// 申报期间验证
	MinTaxYear int `json:"min_tax_year"`
	MaxTaxYear int `json:"max_tax_year"`

	// 批次验证
	BatchNameMaxLength        int `json:"batch_name_max_length"`
	BatchDescriptionMaxLength int `json:"batch_description_max_length"`
}

// GetTaxFilingValidationRules 获取税务申报验证规则
func GetTaxFilingValidationRules() *TaxFilingValidationRules {
	currentYear := time.Now().Year()
	return &TaxFilingValidationRules{
		CompanyNameMaxLength:        200,
		TaxIDMinLength:              15,
		TaxIDMaxLength:              20,
		RegistrationNumberMaxLength: 50,
		MaxTaxRate:                  1.0,
		MaxTaxableAmount:            *********.99,
		MaxTaxAmount:                *********.99,
		MinTaxYear:                  2000,
		MaxTaxYear:                  currentYear + 1,
		BatchNameMaxLength:          200,
		BatchDescriptionMaxLength:   1000,
	}
}

// TaxFilingErrorCodes 税务申报错误代码
var TaxFilingErrorCodes = map[string]string{
	"INVALID_PROVINCE":             "无效的省份代码",
	"INVALID_ENTERPRISE":           "无效的企业ID",
	"INVALID_TAX_DATA":             "无效的税务数据",
	"INVALID_TAX_PERIOD":           "无效的申报期间",
	"INVALID_COMPANY_INFO":         "无效的公司信息",
	"SUBMISSION_NOT_FOUND":         "申报记录不存在",
	"SUBMISSION_ALREADY_SUBMITTED": "申报已提交，无法修改",
	"SUBMISSION_CANCELLED":         "申报已取消",
	"BATCH_NOT_FOUND":              "批次不存在",
	"BATCH_ALREADY_STARTED":        "批次已开始处理",
	"BATCH_COMPLETED":              "批次已完成",
	"PROVINCE_NOT_ACTIVE":          "省份未激活",
	"PROVINCE_MAINTENANCE":         "省份维护中",
	"EXTERNAL_SERVICE_ERROR":       "外部服务错误",
	"AUTHENTICATION_FAILED":        "认证失败",
	"VALIDATION_FAILED":            "数据验证失败",
	"NETWORK_ERROR":                "网络错误",
	"TIMEOUT_ERROR":                "请求超时",
	"RETRY_EXCEEDED":               "重试次数超限",
	"CALLBACK_FAILED":              "回调失败",
	"INTERNAL_ERROR":               "内部错误",
}

// GetErrorMessage 获取错误消息
func GetErrorMessage(errorCode string) string {
	if message, exists := TaxFilingErrorCodes[errorCode]; exists {
		return message
	}
	return "未知错误"
}

// TaxFilingStatusFlow 税务申报状态流转
var TaxFilingStatusFlow = map[TaxFilingSubmissionStatus][]TaxFilingSubmissionStatus{
	TaxFilingStatusPending: {
		TaxFilingStatusProcessing,
		TaxFilingStatusCancelled,
	},
	TaxFilingStatusProcessing: {
		TaxFilingStatusSubmitted,
		TaxFilingStatusFailed,
		TaxFilingStatusCancelled,
	},
	TaxFilingStatusSubmitted: {
		TaxFilingStatusAccepted,
		TaxFilingStatusRejected,
		TaxFilingStatusFailed,
	},
	TaxFilingStatusAccepted: {
		// 终态，无法转换
	},
	TaxFilingStatusRejected: {
		TaxFilingStatusPending, // 可以重新提交
		TaxFilingStatusCancelled,
	},
	TaxFilingStatusFailed: {
		TaxFilingStatusPending, // 可以重试
		TaxFilingStatusCancelled,
	},
	TaxFilingStatusCancelled: {
		// 终态，无法转换
	},
}

// CanTransitionTo 检查状态是否可以转换
func CanTransitionTo(from, to TaxFilingSubmissionStatus) bool {
	allowedStatuses, exists := TaxFilingStatusFlow[from]
	if !exists {
		return false
	}

	for _, status := range allowedStatuses {
		if status == to {
			return true
		}
	}
	return false
}

// IsTerminalStatus 检查是否为终态
func IsTerminalStatus(status TaxFilingSubmissionStatus) bool {
	return status == TaxFilingStatusAccepted || status == TaxFilingStatusCancelled
}

// TaxFilingMetricsArgs 税务申报指标
type TaxFilingMetricsArgs struct {
	TotalSubmissions      int64   `json:"total_submissions"`
	SuccessfulSubmissions int64   `json:"successful_submissions"`
	FailedSubmissions     int64   `json:"failed_submissions"`
	PendingSubmissions    int64   `json:"pending_submissions"`
	ProcessingSubmissions int64   `json:"processing_submissions"`
	SuccessRate           float64 `json:"success_rate"`
	FailureRate           float64 `json:"failure_rate"`
	AverageProcessingTime float64 `json:"average_processing_time"` // 分钟
	TotalBatches          int64   `json:"total_batches"`
	ActiveBatches         int64   `json:"active_batches"`
	CompletedBatches      int64   `json:"completed_batches"`
}

// CalculateMetrics 计算指标
func (m *TaxFilingMetricsArgs) CalculateMetrics() {
	if m.TotalSubmissions > 0 {
		m.SuccessRate = float64(m.SuccessfulSubmissions) / float64(m.TotalSubmissions) * 100
		m.FailureRate = float64(m.FailedSubmissions) / float64(m.TotalSubmissions) * 100
	}
}

// TaxFilingDashboard 税务申报仪表板数据
type TaxFilingDashboard struct {
	Metrics            *TaxFilingMetricsArgs         `json:"metrics"`
	RecentSubmissions  []TaxFilingSubmissionResponse `json:"recent_submissions"`
	ProvinceStats      []TaxFilingProvinceResponse   `json:"province_stats"`
	StatusDistribution []TaxFilingStatusStatistics   `json:"status_distribution"`
	TrendData          []TaxFilingTrendData          `json:"trend_data"`
}

// TaxFilingTrendData 趋势数据
type TaxFilingTrendData struct {
	Date                  string  `json:"date"`
	TotalSubmissions      int64   `json:"total_submissions"`
	SuccessfulSubmissions int64   `json:"successful_submissions"`
	FailedSubmissions     int64   `json:"failed_submissions"`
	SuccessRate           float64 `json:"success_rate"`
}

// TaxFilingAlert 税务申报告警
type TaxFilingAlert struct {
	ID           string    `json:"id"`
	Type         string    `json:"type"` // error, warning, info
	Title        string    `json:"title"`
	Message      string    `json:"message"`
	ProvinceCode *string   `json:"province_code,omitempty"`
	SubmissionID *string   `json:"submission_id,omitempty"`
	BatchID      *string   `json:"batch_id,omitempty"`
	Severity     int       `json:"severity"` // 1-5, 5最高
	IsRead       bool      `json:"is_read"`
	CreatedAt    time.Time `json:"created_at"`
}

// CreateAlert 创建告警
func CreateAlert(alertType, title, message string, severity int) *TaxFilingAlert {
	return &TaxFilingAlert{
		ID:        GenerateID(),
		Type:      alertType,
		Title:     title,
		Message:   message,
		Severity:  severity,
		IsRead:    false,
		CreatedAt: time.Now(),
	}
}
