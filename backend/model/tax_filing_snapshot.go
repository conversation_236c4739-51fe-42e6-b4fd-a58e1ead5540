package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// TaxFilingSnapshotType 快照类型
type TaxFilingSnapshotType string

const (
	SnapshotTypeBeforeCreate TaxFilingSnapshotType = "before_create"
	SnapshotTypeAfterCreate  TaxFilingSnapshotType = "after_create"
	SnapshotTypeBeforeUpdate TaxFilingSnapshotType = "before_update"
	SnapshotTypeAfterUpdate  TaxFilingSnapshotType = "after_update"
	SnapshotTypeBeforeDelete TaxFilingSnapshotType = "before_delete"
	SnapshotTypeAfterDelete  TaxFilingSnapshotType = "after_delete"
	SnapshotTypeBeforeSubmit TaxFilingSnapshotType = "before_submit"
	SnapshotTypeAfterSubmit  TaxFilingSnapshotType = "after_submit"
	SnapshotTypeScheduled    TaxFilingSnapshotType = "scheduled"
	SnapshotTypeManual       TaxFilingSnapshotType = "manual"
)

// SnapshotData 快照数据结构
type SnapshotData map[string]interface{}

// Value 实现 driver.Valuer 接口
func (s SnapshotData) Value() (driver.Value, error) {
	if len(s) == 0 {
		return nil, nil
	}
	return json.Marshal(s)
}

// Scan 实现 sql.Scanner 接口
func (s *SnapshotData) Scan(value interface{}) error {
	if value == nil {
		*s = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into SnapshotData", value)
	}

	return json.Unmarshal(bytes, s)
}

// TaxFilingSnapshot 税务申报数据快照模型
type TaxFilingSnapshot struct {
	ID           string                `gorm:"primaryKey;size:50" json:"id"`
	SubmissionID *string               `gorm:"size:50;index" json:"submission_id,omitempty"`
	BatchID      *string               `gorm:"size:50;index" json:"batch_id,omitempty"`
	SnapshotType TaxFilingSnapshotType `gorm:"size:20;not null;index" json:"snapshot_type"`

	// 快照数据
	Data     SnapshotData `gorm:"type:longtext;not null" json:"data"`
	DataHash string       `gorm:"size:64;index" json:"data_hash"`      // 数据哈希值，用于去重
	DataSize int          `gorm:"not null;default:0" json:"data_size"` // 数据大小(字节)

	// 版本信息
	Version     int  `gorm:"not null;default:1" json:"version"`
	PrevVersion *int `json:"prev_version,omitempty"`
	NextVersion *int `json:"next_version,omitempty"`

	// 操作信息
	OperationType string  `gorm:"size:20;not null" json:"operation_type"` // create, update, delete, submit
	OperatorID    *string `gorm:"size:50" json:"operator_id,omitempty"`
	OperatorType  string  `gorm:"size:20;not null;default:system" json:"operator_type"` // system, user, api

	// 业务信息
	BusinessType string  `gorm:"size:50;not null;default:tax_filing" json:"business_type"`
	Module       string  `gorm:"size:50;not null" json:"module"`
	Description  *string `gorm:"type:text" json:"description,omitempty"`

	// 环境信息
	Environment string  `gorm:"size:20;not null;default:production" json:"environment"`
	ServerNode  string  `gorm:"size:50" json:"server_node,omitempty"`
	RequestID   *string `gorm:"size:50;index" json:"request_id,omitempty"`

	// 保留策略
	RetentionDays int       `gorm:"not null;default:90" json:"retention_days"` // 保留天数
	ExpiresAt     time.Time `gorm:"index" json:"expires_at"`                   // 过期时间

	// 压缩信息
	Compressed      bool   `gorm:"not null;default:false" json:"compressed"`
	CompressionType string `gorm:"size:20" json:"compression_type,omitempty"` // gzip, lz4
	OriginalSize    int    `json:"original_size,omitempty"`                   // 压缩前大小

	// 系统字段
	CreatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;index" json:"created_at"`

	// 关联
	Submission *TaxFilingSubmission `gorm:"foreignKey:SubmissionID" json:"submission,omitempty"`
	Batch      *TaxFilingBatch      `gorm:"foreignKey:BatchID" json:"batch,omitempty"`
}

// TableName 指定表名
func (TaxFilingSnapshot) TableName() string {
	return "tax_filing_snapshots"
}

// BeforeCreate GORM钩子 - 创建前
func (t *TaxFilingSnapshot) BeforeCreate(tx *gorm.DB) error {
	if t.ID == "" {
		t.ID = GenerateID()
	}

	// 计算数据哈希
	if t.DataHash == "" {
		t.DataHash = t.calculateDataHash()
	}

	// 计算数据大小
	if t.DataSize == 0 {
		t.DataSize = t.calculateDataSize()
	}

	// 设置过期时间
	if t.ExpiresAt.IsZero() {
		t.ExpiresAt = time.Now().AddDate(0, 0, t.RetentionDays)
	}

	return nil
}

// calculateDataHash 计算数据哈希值
func (t *TaxFilingSnapshot) calculateDataHash() string {
	// 这里应该使用实际的哈希算法，比如SHA256
	// 为了简化，这里返回一个模拟的哈希值
	return fmt.Sprintf("hash_%d", time.Now().UnixNano())
}

// calculateDataSize 计算数据大小
func (t *TaxFilingSnapshot) calculateDataSize() int {
	if t.Data == nil {
		return 0
	}

	jsonBytes, err := json.Marshal(t.Data)
	if err != nil {
		return 0
	}

	return len(jsonBytes)
}

// CreateSubmissionSnapshot 创建申报快照
func CreateSubmissionSnapshot(submissionID string, snapshotType TaxFilingSnapshotType, data interface{}) *TaxFilingSnapshot {
	snapshot := &TaxFilingSnapshot{
		ID:            GenerateID(),
		SubmissionID:  &submissionID,
		SnapshotType:  snapshotType,
		BusinessType:  "tax_filing",
		Module:        "submission",
		OperatorType:  "system",
		Environment:   "production",
		RetentionDays: 90,
		CreatedAt:     time.Now(),
	}

	// 设置操作类型
	switch snapshotType {
	case SnapshotTypeBeforeCreate, SnapshotTypeAfterCreate:
		snapshot.OperationType = "create"
	case SnapshotTypeBeforeUpdate, SnapshotTypeAfterUpdate:
		snapshot.OperationType = "update"
	case SnapshotTypeBeforeDelete, SnapshotTypeAfterDelete:
		snapshot.OperationType = "delete"
	case SnapshotTypeBeforeSubmit, SnapshotTypeAfterSubmit:
		snapshot.OperationType = "submit"
	default:
		snapshot.OperationType = "unknown"
	}

	// 转换数据
	if snapshotData, err := convertToSnapshotData(data); err == nil {
		snapshot.Data = snapshotData
	}

	return snapshot
}

// CreateBatchSnapshot 创建批次快照
func CreateBatchSnapshot(batchID string, snapshotType TaxFilingSnapshotType, data interface{}) *TaxFilingSnapshot {
	snapshot := &TaxFilingSnapshot{
		ID:            GenerateID(),
		BatchID:       &batchID,
		SnapshotType:  snapshotType,
		BusinessType:  "tax_filing",
		Module:        "batch",
		OperatorType:  "system",
		Environment:   "production",
		RetentionDays: 90,
		CreatedAt:     time.Now(),
	}

	// 设置操作类型
	switch snapshotType {
	case SnapshotTypeBeforeCreate, SnapshotTypeAfterCreate:
		snapshot.OperationType = "create"
	case SnapshotTypeBeforeUpdate, SnapshotTypeAfterUpdate:
		snapshot.OperationType = "update"
	default:
		snapshot.OperationType = "process"
	}

	// 转换数据
	if snapshotData, err := convertToSnapshotData(data); err == nil {
		snapshot.Data = snapshotData
	}

	return snapshot
}

// SetOperator 设置操作者信息
func (t *TaxFilingSnapshot) SetOperator(operatorID, operatorType string) *TaxFilingSnapshot {
	if operatorID != "" {
		t.OperatorID = &operatorID
	}
	if operatorType != "" {
		t.OperatorType = operatorType
	}
	return t
}

// SetDescription 设置描述信息
func (t *TaxFilingSnapshot) SetDescription(description string) *TaxFilingSnapshot {
	if description != "" {
		t.Description = &description
	}
	return t
}

// SetRequestID 设置请求ID
func (t *TaxFilingSnapshot) SetRequestID(requestID string) *TaxFilingSnapshot {
	if requestID != "" {
		t.RequestID = &requestID
	}
	return t
}

// SetRetentionDays 设置保留天数
func (t *TaxFilingSnapshot) SetRetentionDays(days int) *TaxFilingSnapshot {
	if days > 0 {
		t.RetentionDays = days
		t.ExpiresAt = time.Now().AddDate(0, 0, days)
	}
	return t
}

// IsExpired 检查是否已过期
func (t *TaxFilingSnapshot) IsExpired() bool {
	return time.Now().After(t.ExpiresAt)
}

// convertToSnapshotData 将任意数据转换为快照数据格式
func convertToSnapshotData(data interface{}) (SnapshotData, error) {
	// 先序列化为JSON，再反序列化为map
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	var snapshotData SnapshotData
	err = json.Unmarshal(jsonBytes, &snapshotData)
	if err != nil {
		return nil, err
	}

	return snapshotData, nil
}

// TaxFilingSnapshotQuery 快照查询条件
type TaxFilingSnapshotQuery struct {
	SubmissionID  *string                `json:"submission_id,omitempty"`
	BatchID       *string                `json:"batch_id,omitempty"`
	SnapshotType  *TaxFilingSnapshotType `json:"snapshot_type,omitempty"`
	OperationType *string                `json:"operation_type,omitempty"`
	OperatorID    *string                `json:"operator_id,omitempty"`
	OperatorType  *string                `json:"operator_type,omitempty"`
	Module        *string                `json:"module,omitempty"`
	Environment   *string                `json:"environment,omitempty"`
	StartTime     *time.Time             `json:"start_time,omitempty"`
	EndTime       *time.Time             `json:"end_time,omitempty"`
	Version       *int                   `json:"version,omitempty"`

	// 数据过滤
	MinDataSize *int  `json:"min_data_size,omitempty"`
	MaxDataSize *int  `json:"max_data_size,omitempty"`
	Compressed  *bool `json:"compressed,omitempty"`

	// 分页参数
	Page     int `json:"page" default:"1"`
	PageSize int `json:"page_size" default:"20"`

	// 排序参数
	OrderBy string `json:"order_by" default:"created_at"`
	Order   string `json:"order" default:"desc"`
}

// TaxFilingSnapshotResponse 快照响应
type TaxFilingSnapshotResponse struct {
	TaxFilingSnapshot

	// 扩展字段
	OperationTypeDisplay string `json:"operation_type_display"`
	DataSizeDisplay      string `json:"data_size_display"`
	RetentionDisplay     string `json:"retention_display"`
	ExpiresInDays        int    `json:"expires_in_days"`
}

// GetOperationTypeDisplay 获取操作类型显示名称
func (t *TaxFilingSnapshotResponse) GetOperationTypeDisplay() string {
	operationNames := map[string]string{
		"create":  "创建",
		"update":  "更新",
		"delete":  "删除",
		"submit":  "提交",
		"process": "处理",
	}

	if name, exists := operationNames[t.OperationType]; exists {
		return name
	}
	return t.OperationType
}

// GetDataSizeDisplay 获取数据大小显示
func (t *TaxFilingSnapshotResponse) GetDataSizeDisplay() string {
	size := t.DataSize
	if t.Compressed && t.OriginalSize > 0 {
		compressionRatio := float64(size) / float64(t.OriginalSize) * 100
		return fmt.Sprintf("%s (压缩率: %.1f%%)", formatBytes(size), compressionRatio)
	}
	return formatBytes(size)
}

// GetRetentionDisplay 获取保留期显示
func (t *TaxFilingSnapshotResponse) GetRetentionDisplay() string {
	return fmt.Sprintf("%d天", t.RetentionDays)
}

// GetExpiresInDays 获取剩余天数
func (t *TaxFilingSnapshotResponse) GetExpiresInDays() int {
	if t.ExpiresAt.IsZero() {
		return 0
	}

	duration := t.ExpiresAt.Sub(time.Now())
	days := int(duration.Hours() / 24)
	if days < 0 {
		return 0
	}
	return days
}

// formatBytes 格式化字节数显示
func formatBytes(bytes int) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// TaxFilingSnapshotCreateRequest 快照创建请求
type TaxFilingSnapshotCreateRequest struct {
	SubmissionID  *string               `json:"submission_id,omitempty"`
	BatchID       *string               `json:"batch_id,omitempty"`
	SnapshotType  TaxFilingSnapshotType `json:"snapshot_type"`
	Data          SnapshotData          `json:"data"`
	OperationType string                `json:"operation_type"`
	OperatorID    *string               `json:"operator_id,omitempty"`
	OperatorType  string                `json:"operator_type"`
	Module        string                `json:"module"`
	Description   *string               `json:"description,omitempty"`
	Environment   string                `json:"environment"`
	RequestID     *string               `json:"request_id,omitempty"`
	RetentionDays int                   `json:"retention_days"`
}
