package model

import (
	"fmt"
	"time"
)

// ExportTask 数据导出任务模型
type ExportTask struct {
	ID               string     `json:"id" gorm:"primaryKey;size:36;comment:主键ID"`
	Name             string     `json:"name" gorm:"not null;size:200;comment:任务名称"`
	Type             string     `json:"type" gorm:"not null;size:50;index:idx_export_tasks_type;comment:导出类型"`
	Format           string     `json:"format" gorm:"not null;size:20;comment:导出格式"`
	Status           string     `json:"status" gorm:"not null;size:20;index:idx_export_tasks_status;comment:任务状态"`
	Progress         int        `json:"progress" gorm:"default:0;comment:进度百分比"`
	TotalRecords     int64      `json:"totalRecords" gorm:"default:0;comment:总记录数"`
	ProcessedRecords int64      `json:"processedRecords" gorm:"default:0;comment:已处理记录数"`
	Parameters       string     `json:"parameters" gorm:"type:text;comment:导出参数JSON"`
	Filters          string     `json:"filters" gorm:"type:text;comment:过滤条件JSON"`
	Fields           string     `json:"fields" gorm:"type:text;comment:导出字段JSON"`
	FileName         string     `json:"fileName" gorm:"size:255;comment:文件名"`
	FilePath         string     `json:"filePath" gorm:"size:500;comment:文件路径"`
	FileSize         int64      `json:"fileSize" gorm:"default:0;comment:文件大小"`
	DownloadURL      string     `json:"downloadUrl" gorm:"size:500;comment:下载链接"`
	StartTime        *time.Time `json:"startTime" gorm:"comment:开始时间"`
	EndTime          *time.Time `json:"endTime" gorm:"comment:结束时间"`
	ExpiresAt        *time.Time `json:"expiresAt" gorm:"index:idx_export_tasks_expires;comment:过期时间"`
	ErrorMessage     string     `json:"errorMessage" gorm:"size:1000;comment:错误信息"`
	EnterpriseID     string     `json:"enterpriseId" gorm:"not null;size:36;index:idx_export_tasks_enterprise;comment:企业ID"`
	CreatedBy        string     `json:"createdBy" gorm:"not null;size:36;index:idx_export_tasks_creator;comment:创建人ID"`
	CreatedAt        time.Time  `json:"createdAt" gorm:"index:idx_export_tasks_created;comment:创建时间"`
	UpdatedAt        time.Time  `json:"updatedAt" gorm:"comment:更新时间"`

	// 关联关系
	Enterprise *Enterprise `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID"`
	Creator    *User       `json:"creator,omitempty" gorm:"foreignKey:CreatedBy;references:ID"`
}

// 导出类型常量
const (
	ExportTypeInvoice     = "invoice"     // 发票导出
	ExportTypeDeclaration = "declaration" // 申报导出
	ExportTypeEnterprise  = "enterprise"  // 企业导出
	ExportTypeUser        = "user"        // 用户导出
	ExportTypeAuditLog    = "audit_log"   // 审计日志导出
	ExportTypeReport      = "report"      // 报表导出
	ExportTypeCustom      = "custom"      // 自定义导出
)

// 导出格式常量
const (
	ExportFormatExcel = "excel" // Excel格式
	ExportFormatCSV   = "csv"   // CSV格式
	ExportFormatPDF   = "pdf"   // PDF格式
	ExportFormatJSON  = "json"  // JSON格式
	ExportFormatXML   = "xml"   // XML格式
)

// 任务状态常量
const (
	ExportStatusPending    = "pending"    // 待处理
	ExportStatusProcessing = "processing" // 处理中
	ExportStatusCompleted  = "completed"  // 已完成
	ExportStatusFailed     = "failed"     // 失败
	ExportStatusCancelled  = "cancelled"  // 已取消
	ExportStatusExpired    = "expired"    // 已过期
)

// ExportTaskCreateRequest 创建导出任务请求
type ExportTaskCreateRequest struct {
	Name         string                 `json:"name" binding:"required,max=200"`
	Type         string                 `json:"type" binding:"required"`
	Format       string                 `json:"format" binding:"required"`
	Parameters   map[string]interface{} `json:"parameters"`
	Filters      map[string]interface{} `json:"filters"`
	Fields       []string               `json:"fields"`
	FileName     string                 `json:"fileName"`
	EnterpriseID string                 `json:"enterpriseId" binding:"required"`
	ExpiresIn    int                    `json:"expiresIn"` // 过期时间（小时）
}

// ExportTaskUpdateRequest 更新导出任务请求
type ExportTaskUpdateRequest struct {
	Status           *string    `json:"status"`
	Progress         *int       `json:"progress"`
	TotalRecords     *int64     `json:"totalRecords"`
	ProcessedRecords *int64     `json:"processedRecords"`
	FileName         *string    `json:"fileName"`
	FilePath         *string    `json:"filePath"`
	FileSize         *int64     `json:"fileSize"`
	DownloadURL      *string    `json:"downloadUrl"`
	StartTime        *time.Time `json:"startTime"`
	EndTime          *time.Time `json:"endTime"`
	ErrorMessage     *string    `json:"errorMessage"`
}

// ExportTaskFilter 导出任务过滤条件
type ExportTaskFilter struct {
	Name         string     `json:"name"`
	Type         string     `json:"type"`
	Format       string     `json:"format"`
	Status       string     `json:"status"`
	EnterpriseID string     `json:"enterpriseId"`
	CreatedBy    string     `json:"createdBy"`
	StartTime    *time.Time `json:"startTime"`
	EndTime      *time.Time `json:"endTime"`
	CreatedStart *time.Time `json:"createdStart"`
	CreatedEnd   *time.Time `json:"createdEnd"`
}

// ExportTaskResponse 导出任务响应
type ExportTaskResponse struct {
	ID                string     `json:"id"`
	Name              string     `json:"name"`
	Type              string     `json:"type"`
	TypeName          string     `json:"typeName"`
	Format            string     `json:"format"`
	FormatName        string     `json:"formatName"`
	Status            string     `json:"status"`
	StatusName        string     `json:"statusName"`
	Progress          int        `json:"progress"`
	TotalRecords      int64      `json:"totalRecords"`
	ProcessedRecords  int64      `json:"processedRecords"`
	Parameters        string     `json:"parameters"`
	Filters           string     `json:"filters"`
	Fields            string     `json:"fields"`
	FileName          string     `json:"fileName"`
	FilePath          string     `json:"filePath"`
	FileSize          int64      `json:"fileSize"`
	FileSizeFormatted string     `json:"fileSizeFormatted"`
	DownloadURL       string     `json:"downloadUrl"`
	StartTime         *time.Time `json:"startTime"`
	EndTime           *time.Time `json:"endTime"`
	Duration          *int64     `json:"duration"` // 执行时长（秒）
	ExpiresAt         *time.Time `json:"expiresAt"`
	ErrorMessage      string     `json:"errorMessage"`
	EnterpriseID      string     `json:"enterpriseId"`
	EnterpriseName    string     `json:"enterpriseName"`
	CreatedBy         string     `json:"createdBy"`
	CreatedByName     string     `json:"createdByName"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         time.Time  `json:"updatedAt"`
	CanDownload       bool       `json:"canDownload"`
	CanCancel         bool       `json:"canCancel"`
	CanRetry          bool       `json:"canRetry"`
}

// PaginatedExportTasks 分页导出任务响应
type PaginatedExportTasks struct {
	Tasks []ExportTaskResponse `json:"tasks"`
	Total int64                `json:"total"`
	Page  int                  `json:"page"`
	Limit int                  `json:"limit"`
}

// ExportTaskStatistics 导出任务统计
type ExportTaskStatistics struct {
	Total       int64             `json:"total"`
	Completed   int64             `json:"completed"`
	Failed      int64             `json:"failed"`
	Processing  int64             `json:"processing"`
	Pending     int64             `json:"pending"`
	SuccessRate float64           `json:"successRate"`
	TotalSize   int64             `json:"totalSize"`
	AverageSize int64             `json:"averageSize"`
	ByType      map[string]int64  `json:"byType"`
	ByFormat    map[string]int64  `json:"byFormat"`
	ByStatus    map[string]int64  `json:"byStatus"`
	ByDay       map[string]int64  `json:"byDay"`
	TrendData   []ExportTrendData `json:"trendData"`
}

// ExportTrendData 导出趋势数据
type ExportTrendData struct {
	Date      string `json:"date"`
	Total     int64  `json:"total"`
	Completed int64  `json:"completed"`
	Failed    int64  `json:"failed"`
	TotalSize int64  `json:"totalSize"`
}

// ExportTaskBatchRequest 批量导出任务请求
type ExportTaskBatchRequest struct {
	Tasks []ExportTaskCreateRequest `json:"tasks" binding:"required,min=1"`
}

// ExportTaskBatchResult 批量导出任务结果
type ExportTaskBatchResult struct {
	Total     int                  `json:"total"`
	Success   int                  `json:"success"`
	Failed    int                  `json:"failed"`
	Tasks     []ExportTaskResponse `json:"tasks"`
	Errors    []ExportTaskError    `json:"errors"`
	CreatedAt time.Time            `json:"createdAt"`
}

// ExportTaskError 导出任务错误
type ExportTaskError struct {
	Index int    `json:"index"`
	Name  string `json:"name"`
	Error string `json:"error"`
}

// ExportTaskCancelRequest 取消导出任务请求
type ExportTaskCancelRequest struct {
	IDs    []string `json:"ids" binding:"required,min=1"`
	Reason string   `json:"reason"`
}

// ExportTaskCancelResult 取消导出任务结果
type ExportTaskCancelResult struct {
	Total       int                    `json:"total"`
	Success     int                    `json:"success"`
	Failed      int                    `json:"failed"`
	Results     []ExportTaskCancelItem `json:"results"`
	CancelledAt time.Time              `json:"cancelledAt"`
}

// ExportTaskCancelItem 取消导出任务项
type ExportTaskCancelItem struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Success bool   `json:"success"`
	Error   string `json:"error"`
}

// ExportTaskRetryRequest 重试导出任务请求
type ExportTaskRetryRequest struct {
	IDs []string `json:"ids" binding:"required,min=1"`
}

// ExportTaskCleanupRequest 清理导出任务请求
type ExportTaskCleanupRequest struct {
	KeepDays     int    `json:"keepDays" binding:"required,min=1"`
	Status       string `json:"status"`
	EnterpriseID string `json:"enterpriseId"`
	DryRun       bool   `json:"dryRun"`
}

// ExportTaskCleanupResult 清理导出任务结果
type ExportTaskCleanupResult struct {
	TotalScanned int64     `json:"totalScanned"`
	TotalDeleted int64     `json:"totalDeleted"`
	FilesDeleted int64     `json:"filesDeleted"`
	SpaceFreed   int64     `json:"spaceFreed"`
	CleanedAt    time.Time `json:"cleanedAt"`
	DryRun       bool      `json:"dryRun"`
}

// GetTypeName 获取导出类型名称
func GetExportTypeName(exportType string) string {
	switch exportType {
	case ExportTypeInvoice:
		return "发票导出"
	case ExportTypeDeclaration:
		return "申报导出"
	case ExportTypeEnterprise:
		return "企业导出"
	case ExportTypeUser:
		return "用户导出"
	case ExportTypeAuditLog:
		return "审计日志导出"
	case ExportTypeReport:
		return "报表导出"
	case ExportTypeCustom:
		return "自定义导出"
	default:
		return "未知类型"
	}
}

// GetFormatName 获取导出格式名称
func GetExportFormatName(format string) string {
	switch format {
	case ExportFormatExcel:
		return "Excel"
	case ExportFormatCSV:
		return "CSV"
	case ExportFormatPDF:
		return "PDF"
	case ExportFormatJSON:
		return "JSON"
	case ExportFormatXML:
		return "XML"
	default:
		return "未知格式"
	}
}

// GetStatusName 获取任务状态名称
func GetExportStatusName(status string) string {
	switch status {
	case ExportStatusPending:
		return "待处理"
	case ExportStatusProcessing:
		return "处理中"
	case ExportStatusCompleted:
		return "已完成"
	case ExportStatusFailed:
		return "失败"
	case ExportStatusCancelled:
		return "已取消"
	case ExportStatusExpired:
		return "已过期"
	default:
		return "未知状态"
	}
}

// IsValidExportType 验证导出类型是否有效
func IsValidExportType(exportType string) bool {
	validTypes := []string{
		ExportTypeInvoice,
		ExportTypeDeclaration,
		ExportTypeEnterprise,
		ExportTypeUser,
		ExportTypeAuditLog,
		ExportTypeReport,
		ExportTypeCustom,
	}

	for _, validType := range validTypes {
		if exportType == validType {
			return true
		}
	}
	return false
}

// IsValidExportFormat 验证导出格式是否有效
func IsValidExportFormat(format string) bool {
	validFormats := []string{
		ExportFormatExcel,
		ExportFormatCSV,
		ExportFormatPDF,
		ExportFormatJSON,
		ExportFormatXML,
	}

	for _, validFormat := range validFormats {
		if format == validFormat {
			return true
		}
	}
	return false
}

// IsValidExportStatus 验证任务状态是否有效
func IsValidExportStatus(status string) bool {
	validStatuses := []string{
		ExportStatusPending,
		ExportStatusProcessing,
		ExportStatusCompleted,
		ExportStatusFailed,
		ExportStatusCancelled,
		ExportStatusExpired,
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// CanDownload 判断是否可以下载
func (e *ExportTask) CanDownload() bool {
	return e.Status == ExportStatusCompleted && e.DownloadURL != "" &&
		(e.ExpiresAt == nil || time.Now().Before(*e.ExpiresAt))
}

// CanCancel 判断是否可以取消
func (e *ExportTask) CanCancel() bool {
	return e.Status == ExportStatusPending || e.Status == ExportStatusProcessing
}

// CanRetry 判断是否可以重试
func (e *ExportTask) CanRetry() bool {
	return e.Status == ExportStatusFailed
}

// IsExpired 判断是否已过期
func (e *ExportTask) IsExpired() bool {
	return e.ExpiresAt != nil && time.Now().After(*e.ExpiresAt)
}

// GetDuration 获取执行时长（秒）
func (e *ExportTask) GetDuration() *int64 {
	if e.StartTime == nil || e.EndTime == nil {
		return nil
	}
	duration := int64(e.EndTime.Sub(*e.StartTime).Seconds())
	return &duration
}

// FormatFileSize 格式化文件大小
func FormatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// TableName 指定表名
func (ExportTask) TableName() string {
	return "export_tasks"
}
