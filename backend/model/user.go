// Package model defines the data models and request/response structures for the tax management system.
// It includes user authentication, enterprise management, and related data transfer objects.
package model

import (
	"time"
)

// User represents a user entity in the tax management system.
// It contains user authentication information, profile data, and enterprise associations.
type User struct {
	ID                string     `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:用户ID"`
	UserName          string     `json:"user_name" gorm:"type:varchar(100);not null;column:user_name;index:idx_users_name;comment:用户名"`
	Email             *string    `json:"email" gorm:"type:varchar(255);uniqueIndex:uk_users_email;column:email;comment:邮箱"`
	PasswordHash      string     `json:"-" gorm:"type:varchar(255);not null;column:password_hash;comment:密码哈希"`
	Phone             string     `json:"phone" gorm:"type:varchar(20);column:phone;uniqueIndex:uk_users_phone;not null;comment:手机号"`
	EnterpriseID      *string    `json:"enterprise_id" gorm:"type:varchar(36);column:enterprise_id;index:idx_users_enterprise_id;comment:所属企业ID"`
	Department        string     `json:"department" gorm:"type:varchar(100);column:department;comment:部门"`
	Position          string     `json:"position" gorm:"type:varchar(100);column:position;comment:职位"`
	Avatar            string     `json:"avatar" gorm:"type:varchar(500);column:avatar;comment:头像URL"`
	IsActive          bool       `json:"is_active" gorm:"default:true;column:is_active;comment:是否启用"`
	EmailVerified     bool       `json:"email_verified" gorm:"default:false;column:email_verified;comment:邮箱是否验证"`
	PhoneVerified     bool       `json:"phone_verified" gorm:"default:false;column:phone_verified;comment:手机是否验证"`
	ResetToken        string     `json:"-" gorm:"type:varchar(255);column:reset_token;comment:重置密码令牌"`
	ResetExpiry       *time.Time `json:"-" gorm:"column:reset_expiry;comment:重置令牌过期时间"`
	PreferredLang     string     `json:"preferred_lang" gorm:"type:varchar(10);default:'zh-CN';column:preferred_lang;comment:首选语言"`
	TwoFactorEnabled  bool       `json:"two_factor_enabled" gorm:"default:false;column:two_factor_enabled;comment:是否启用双因子认证"`
	TwoFactorSecret   string     `json:"-" gorm:"type:varchar(255);column:two_factor_secret;comment:双因子认证密钥"`
	LastLoginAt       *time.Time `json:"last_login_at" gorm:"column:last_login_at;index:idx_users_last_login;comment:最后登录时间"`
	LastLoginIP       string     `json:"last_login_ip" gorm:"type:varchar(45);column:last_login_ip;comment:最后登录IP"`
	PasswordChangedAt *time.Time `json:"password_changed_at" gorm:"column:password_changed_at;comment:密码修改时间"`
	LoginAttempts     int        `json:"login_attempts" gorm:"default:0;column:login_attempts;comment:登录尝试次数"`
	LockedUntil       *time.Time `json:"locked_until" gorm:"column:locked_until;comment:锁定到期时间"`
	CreatedAt         time.Time  `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt         time.Time  `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	Enterprise *Enterprise `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

// LoginRequest represents a user login request with phone number and password.
type LoginRequest struct {
	Phone    string `json:"phone" binding:"required,len=11"` // 用户手机号
	Password string `json:"password" binding:"required"`     // 用户密码
}

// RegisterRequest represents a user registration request with required and optional fields.
type RegisterRequest struct {
	Phone      string `json:"phone" binding:"required,len=11"`   // 手机号码
	Password   string `json:"password" binding:"required,min=8"` // 用户密码
	Name       string `json:"name" binding:"required"`           // 用户姓名
	Email      string `json:"email" binding:"omitempty,email"`   // 用户邮箱（可选）
	Enterprise string `json:"enterprise"`                        // 企业名称
	Position   string `json:"position"`                          // 职位
	Department string `json:"department"`                        // 部门
}

// ChangePasswordRequest represents a password change request
type ChangePasswordRequest struct {
	OldPassword string `json:"oldPassword" binding:"required,min=6"`
	NewPassword string `json:"newPassword" binding:"required,min=6"`
}

// UpdateProfileRequest represents a profile update request
type UpdateProfileRequest struct {
	Name             string `json:"name"`      // 保持向后兼容
	UserName         string `json:"user_name"` // 新的用户名字段
	Phone            string `json:"phone"`
	Department       string `json:"department"`
	Position         string `json:"position"`
	Company          string `json:"company"`
	Bio              string `json:"bio"`
	Location         string `json:"location"`
	PreferredLang    string `json:"preferredLang"`
	Avatar           string `json:"avatar"`
	TwoFactorEnabled *bool  `json:"twoFactorEnabled"` // 使用指针类型以区分未设置和false
}

// TokenRefreshRequest 刷新令牌请求结构
type TokenRefreshRequest struct {
	RefreshToken string `json:"refreshToken" binding:"required"` // 刷新令牌
}

// ForgotPasswordRequest 忘记密码请求结构
type ForgotPasswordRequest struct {
	Email string `json:"email" binding:"omitempty,email"`  // 用户邮箱（可选）
	Phone string `json:"phone" binding:"omitempty,len=11"` // 用户手机号（可选）
}

// ResetPasswordRequest 重置密码请求结构
type ResetPasswordRequest struct {
	Token    string `json:"token" binding:"required"`          // 重置令牌
	Password string `json:"password" binding:"required,min=8"` // 新密码
}

// UserResponse 用户响应结构
type UserResponse struct {
	ID                string     `json:"id"`
	UserName          string     `json:"user_name"`
	Email             string     `json:"email"`
	PhoneNumber       *string    `json:"phoneNumber"`
	FullName          string     `json:"fullName"`
	AvatarURL         *string    `json:"avatarUrl"`
	RoleID            string     `json:"roleId"`
	RoleName          string     `json:"roleName,omitempty"`
	Status            string     `json:"status"`
	LastLoginAt       *time.Time `json:"lastLoginAt"`
	LastLoginIP       *string    `json:"lastLoginIp"`
	PreferredLanguage *string    `json:"preferredLanguage"`
	Timezone          *string    `json:"timezone"`
	CreatedAt         time.Time  `json:"createdAt"`
}

// AuthResponse 认证响应结构
type AuthResponse struct {
	User                  interface{} `json:"user"`
	AccessToken           string      `json:"accessToken"`
	RefreshToken          string      `json:"refreshToken"`
	AccessTokenExpiresAt  time.Time   `json:"accessTokenExpiresAt"`
	RefreshTokenExpiresAt time.Time   `json:"refreshTokenExpiresAt"`
}

// PaginatedUsers 分页用户响应结构
type PaginatedUsers struct {
	Users []UserResponse `json:"users"`
	Total int64          `json:"total"`
	Page  int            `json:"page"`
	Limit int            `json:"limit"`
}

// Token types
const (
	TokenTypeAccess  = "access"
	TokenTypeRefresh = "refresh"
	TokenTypeReset   = "reset"
)
