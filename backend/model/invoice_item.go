package model

import (
	"errors"
	"time"

	"github.com/shopspring/decimal"
)

// InvoiceItem 发票明细模型
type InvoiceItem struct {
	ID            string          `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:主键ID"`
	InvoiceID     string          `json:"invoice_id" gorm:"type:varchar(36);not null;column:invoice_id;index:idx_invoice_items_invoice_id;comment:发票ID"`
	TaxTypeID     *string         `json:"tax_type_id" gorm:"type:varchar(36);column:tax_type_id;index:idx_invoice_items_tax_type_id;comment:税种ID"`
	ItemName      string          `json:"item_name" gorm:"type:varchar(255);not null;column:item_name;comment:商品名称"`
	Specification string          `json:"specification" gorm:"type:varchar(100);column:specification;comment:规格型号"`
	Unit          string          `json:"unit" gorm:"type:varchar(20);column:unit;comment:单位"`
	Quantity      decimal.Decimal `json:"quantity" gorm:"type:decimal(12,4);not null;column:quantity;comment:数量"`
	UnitPrice     decimal.Decimal `json:"unit_price" gorm:"type:decimal(15,6);not null;column:unit_price;comment:单价"`
	Amount        decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null;column:amount;comment:金额"`
	TaxRate       decimal.Decimal `json:"tax_rate" gorm:"type:decimal(5,4);not null;column:tax_rate;comment:税率"`
	TaxAmount     decimal.Decimal `json:"tax_amount" gorm:"type:decimal(15,2);not null;column:tax_amount;comment:税额"`
	TotalAmount   decimal.Decimal `json:"total_amount" gorm:"type:decimal(15,2);not null;column:total_amount;comment:合计"`
	TaxCategory   string          `json:"tax_category" gorm:"type:varchar(50);column:tax_category;comment:税收分类编码"`
	Remarks       string          `json:"remarks" gorm:"type:varchar(500);column:remarks;comment:备注"`
	CreatedAt     time.Time       `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt     time.Time       `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	Invoice *Invoice `json:"invoice,omitempty" gorm:"foreignKey:InvoiceID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	TaxType *TaxType `json:"tax_type,omitempty" gorm:"foreignKey:TaxTypeID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

// TableName 指定表名
func (InvoiceItem) TableName() string {
	return "invoice_items"
}

// InvoiceItemRequest 发票明细请求结构
type InvoiceItemRequest struct {
	ItemName      string          `json:"item_name" binding:"required"`
	Specification string          `json:"specification"`
	Unit          string          `json:"unit"`
	Quantity      decimal.Decimal `json:"quantity" binding:"required"`
	UnitPrice     decimal.Decimal `json:"unit_price" binding:"required"`
	TaxRate       decimal.Decimal `json:"tax_rate" binding:"required"`
	Remarks       string          `json:"remarks"`
}

// InvoiceItemResponse 发票明细响应结构
type InvoiceItemResponse struct {
	ID            string          `json:"id"`
	InvoiceID     string          `json:"invoice_id"`
	ItemName      string          `json:"item_name"`
	Specification string          `json:"specification"`
	Unit          string          `json:"unit"`
	Quantity      decimal.Decimal `json:"quantity"`
	UnitPrice     decimal.Decimal `json:"unit_price"`
	Amount        decimal.Decimal `json:"amount"`
	TaxRate       decimal.Decimal `json:"tax_rate"`
	TaxAmount     decimal.Decimal `json:"tax_amount"`
	TotalAmount   decimal.Decimal `json:"total_amount"`
	Remarks       string          `json:"remarks"`
	CreatedAt     time.Time       `json:"created_at"`
	UpdatedAt     time.Time       `json:"updated_at"`
}

// CalculateAmounts 计算金额、税额和合计
func (item *InvoiceItem) CalculateAmounts() {
	// 计算金额 = 数量 × 单价
	item.Amount = item.Quantity.Mul(item.UnitPrice)

	// 计算税额 = 金额 × 税率
	item.TaxAmount = item.Amount.Mul(item.TaxRate)

	// 计算合计 = 金额 + 税额
	item.TotalAmount = item.Amount.Add(item.TaxAmount)
}

// Validate 验证发票明细数据
func (item *InvoiceItem) Validate() error {
	if item.ItemName == "" {
		return ErrInvalidItemName
	}

	if item.Quantity.LessThanOrEqual(decimal.Zero) {
		return ErrInvalidQuantity
	}

	if item.UnitPrice.LessThan(decimal.Zero) {
		return ErrInvalidUnitPrice
	}

	if item.TaxRate.LessThan(decimal.Zero) || item.TaxRate.GreaterThan(decimal.NewFromFloat(1)) {
		return ErrInvalidTaxRate
	}

	return nil
}

// 错误定义
var (
	ErrInvalidItemName  = errors.New("商品名称不能为空")
	ErrInvalidQuantity  = errors.New("数量必须大于0")
	ErrInvalidUnitPrice = errors.New("单价不能为负数")
	ErrInvalidTaxRate   = errors.New("税率必须在0-1之间")
)

// 常用单位常量
const (
	UnitPiece       = "件"
	UnitSet         = "套"
	UnitBox         = "箱"
	UnitKilogram    = "千克"
	UnitGram        = "克"
	UnitMeter       = "米"
	UnitSquareMeter = "平方米"
	UnitCubicMeter  = "立方米"
	UnitLiter       = "升"
	UnitHour        = "小时"
	UnitDay         = "天"
	UnitMonth       = "月"
	UnitYear        = "年"
	UnitService     = "项"
)

// 常用税率常量
var (
	TaxRate0  = decimal.NewFromFloat(0.00) // 0%
	TaxRate3  = decimal.NewFromFloat(0.03) // 3%
	TaxRate5  = decimal.NewFromFloat(0.05) // 5%
	TaxRate6  = decimal.NewFromFloat(0.06) // 6%
	TaxRate9  = decimal.NewFromFloat(0.09) // 9%
	TaxRate10 = decimal.NewFromFloat(0.10) // 10%
	TaxRate11 = decimal.NewFromFloat(0.11) // 11%
	TaxRate13 = decimal.NewFromFloat(0.13) // 13%
	TaxRate16 = decimal.NewFromFloat(0.16) // 16%
	TaxRate17 = decimal.NewFromFloat(0.17) // 17%
)
