package model

import (
	"time"
)

// WorkflowDefinition 表示工作流定义
type WorkflowDefinition struct {
	ID          string    `json:"id" gorm:"primaryKey;size:36;comment:主键ID"`
	Name        string    `json:"name" gorm:"not null;size:255;comment:工作流名称"`
	Description *string   `json:"description" gorm:"type:text;comment:描述"`
	Type        string    `json:"type" gorm:"not null;size:100;comment:工作流类型（如申报、审批）"`
	Definition  string    `json:"definition" gorm:"not null;type:json;comment:工作流定义（JSON格式）"`
	Version     int       `json:"version" gorm:"not null;default:1;comment:版本号"`
	IsActive    bool      `json:"isActive" gorm:"not null;default:true;comment:是否激活"`
	CreatedAt   time.Time `json:"createdAt" gorm:"not null;default:CURRENT_TIMESTAMP;comment:创建时间"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间"`
}

// WorkflowInstance 表示工作流实例
type WorkflowInstance struct {
	ID                   string     `json:"id" gorm:"primaryKey;size:36;comment:主键ID"`
	WorkflowDefinitionID string     `json:"workflowDefinitionId" gorm:"not null;size:36;index;comment:工作流定义ID"`
	RelatedEntityType    string     `json:"relatedEntityType" gorm:"not null;size:100;comment:关联实体类型"`
	RelatedEntityID      string     `json:"relatedEntityId" gorm:"not null;size:36;comment:关联实体ID"`
	Status               string     `json:"status" gorm:"not null;size:50;comment:实例状态"`
	CurrentState         string     `json:"currentState" gorm:"not null;size:100;comment:当前状态节点"`
	Context              *string    `json:"context" gorm:"type:json;comment:上下文数据"`
	StartedAt            time.Time  `json:"startedAt" gorm:"not null;default:CURRENT_TIMESTAMP;comment:开始时间"`
	EndedAt              *time.Time `json:"endedAt" gorm:"comment:结束时间"`
	CreatedAt            time.Time  `json:"createdAt" gorm:"not null;default:CURRENT_TIMESTAMP;comment:创建时间"`
	UpdatedAt            time.Time  `json:"updatedAt" gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间"`

	// 关联关系
	WorkflowDefinition *WorkflowDefinition `json:"workflowDefinition,omitempty" gorm:"foreignKey:WorkflowDefinitionID;references:ID"`
}
