package model

import (
	"time"
)

// Notification 系统通知模型
type Notification struct {
	ID                string     `json:"id" gorm:"primaryKey;size:36;comment:主键ID"`
	RecipientID       string     `json:"recipientId" gorm:"not null;size:36;index:idx_notifications_recipient_id;comment:接收者用户ID"`
	Type              string     `json:"type" gorm:"not null;size:50;comment:通知类型"`
	Title             string     `json:"title" gorm:"not null;size:255;comment:通知标题"`
	Content           string     `json:"content" gorm:"type:text;not null;comment:通知内容"`
	IsRead            bool       `json:"isRead" gorm:"not null;default:false;comment:是否已读"`
	ReadAt            *time.Time `json:"readAt" gorm:"comment:阅读时间"`
	RelatedEntityType *string    `json:"relatedEntityType" gorm:"size:100;comment:关联实体类型"`
	RelatedEntityID   *string    `json:"relatedEntityId" gorm:"size:36;comment:关联实体ID"`
	CreatedAt         time.Time  `json:"createdAt" gorm:"not null;comment:创建时间"`
	UpdatedAt         time.Time  `json:"updatedAt" gorm:"not null;comment:更新时间"`

	// 关联关系
	Recipient *User `json:"recipient,omitempty" gorm:"foreignKey:RecipientID;references:ID;constraint:OnDelete:CASCADE"`
}

// NotificationCreateRequest 创建通知请求
type NotificationCreateRequest struct {
	RecipientID       string  `json:"recipientId" binding:"required"`
	Type              string  `json:"type" binding:"required"`
	Title             string  `json:"title" binding:"required"`
	Content           string  `json:"content" binding:"required"`
	RelatedEntityType *string `json:"relatedEntityType"`
	RelatedEntityID   *string `json:"relatedEntityId"`
}

// NotificationBatchCreateRequest 批量创建通知请求
type NotificationBatchCreateRequest struct {
	RecipientIDs      []string `json:"recipientIds" binding:"required"`
	Type              string   `json:"type" binding:"required"`
	Title             string   `json:"title" binding:"required"`
	Content           string   `json:"content" binding:"required"`
	RelatedEntityType *string  `json:"relatedEntityType"`
	RelatedEntityID   *string  `json:"relatedEntityId"`
}

// NotificationFilter 通知过滤条件
type NotificationFilter struct {
	RecipientID  string     `json:"recipientId"`
	Type         string     `json:"type"`
	IsRead       *bool      `json:"isRead"`
	CreatedStart *time.Time `json:"createdStart"`
	CreatedEnd   *time.Time `json:"createdEnd"`
}

// NotificationResponse 通知响应
type NotificationResponse struct {
	ID                string     `json:"id"`
	RecipientID       string     `json:"recipientId"`
	Type              string     `json:"type"`
	Title             string     `json:"title"`
	Content           string     `json:"content"`
	IsRead            bool       `json:"isRead"`
	ReadAt            *time.Time `json:"readAt"`
	RelatedEntityType *string    `json:"relatedEntityType"`
	RelatedEntityID   *string    `json:"relatedEntityId"`
	CreatedAt         time.Time  `json:"createdAt"`
}

// MarkAsReadRequest 标记为已读请求
type MarkAsReadRequest struct {
	IDs []string `json:"ids" binding:"required"`
}

// NotificationCountResponse 表示通知计数响应
type NotificationCountResponse struct {
	Total  int64 `json:"total"`  // 总数
	Unread int64 `json:"unread"` // 未读数量
}
