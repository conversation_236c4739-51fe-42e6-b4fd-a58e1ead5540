package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// Enterprise 企业模型
type Enterprise struct {
	ID                      string          `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:企业ID"`
	NumericID               *int            `json:"numeric_id" gorm:"type:int;column:numeric_id;uniqueIndex:uk_enterprises_numeric_id;comment:数字ID"`
	OwnerID                 *string         `json:"owner_id" gorm:"type:varchar(36);column:owner_id;index:idx_enterprises_owner_id;comment:企业所有者ID"`
	Name                    string          `json:"name" gorm:"type:varchar(255);not null;column:name;index:idx_enterprises_name;comment:企业名称"`
	UnifiedSocialCreditCode string          `json:"unified_social_credit_code" gorm:"type:varchar(18);uniqueIndex:uk_enterprises_credit_code;not null;column:unified_social_credit_code;comment:统一社会信用代码"`
	TaxpayerType            string          `json:"taxpayer_type" gorm:"type:enum('general','small');not null;column:taxpayer_type;index:idx_enterprises_taxpayer_type;comment:纳税人类型"`
	BusinessScope           string          `json:"business_scope" gorm:"type:text;column:business_scope;comment:经营范围"`
	RegisteredAddress       string          `json:"registered_address" gorm:"type:varchar(500);column:registered_address;comment:注册地址"`
	BusinessAddress         string          `json:"business_address" gorm:"type:varchar(500);column:business_address;comment:经营地址"`
	LegalRepresentative     string          `json:"legal_representative" gorm:"type:varchar(100);column:legal_representative;comment:法定代表人"`
	ContactPerson           string          `json:"contact_person" gorm:"type:varchar(100);column:contact_person;comment:联系人"`
	ContactPhone            string          `json:"contact_phone" gorm:"type:varchar(20);column:contact_phone;comment:联系电话"`
	ContactEmail            string          `json:"contact_email" gorm:"type:varchar(100);column:contact_email;comment:联系邮箱"`
	RegisteredDate          *time.Time      `json:"registered_date" gorm:"type:date;column:registered_date;comment:注册日期"`
	IndustryCode            string          `json:"industry_code" gorm:"type:varchar(10);column:industry_code;comment:行业代码"`
	RegionCode              string          `json:"region_code" gorm:"type:varchar(10);column:region_code;comment:地区代码"`
	ParentID                *string         `json:"parent_id" gorm:"type:varchar(36);column:parent_id;index:idx_enterprises_parent_id;comment:上级企业ID"`
	GroupID                 *string         `json:"group_id" gorm:"type:varchar(36);column:group_id;index:idx_enterprises_group_id;comment:分组ID"`
	IsHeadquarters          bool            `json:"is_headquarters" gorm:"default:false;column:is_headquarters;comment:是否总部"`
	Status                  string          `json:"status" gorm:"type:enum('active','inactive','suspended','cancelled');default:active;column:status;index:idx_enterprises_status;comment:状态"`
	TaxAuthority            string          `json:"tax_authority" gorm:"type:varchar(100);column:tax_authority;comment:主管税务机关"`
	BankName                string          `json:"bank_name" gorm:"type:varchar(100);column:bank_name;comment:开户银行"`
	BankAccount             string          `json:"bank_account" gorm:"type:varchar(50);column:bank_account;comment:银行账号"`
	VerificationStatus      string          `json:"verification_status" gorm:"type:enum('pending','verified','rejected');default:pending;column:verification_status;comment:验证状态"`
	RegisteredCapital       decimal.Decimal `json:"registered_capital" gorm:"type:decimal(15,2);column:registered_capital;comment:注册资本"`
	PaidCapital             decimal.Decimal `json:"paid_capital" gorm:"type:decimal(15,2);column:paid_capital;comment:实缴资本"`
	BusinessLicenseNumber   string          `json:"business_license_number" gorm:"type:varchar(50);uniqueIndex:uk_enterprises_license_number;column:business_license_number;comment:营业执照号码"`
	BusinessLicenseExpiry   *time.Time      `json:"business_license_expiry" gorm:"type:date;column:business_license_expiry;comment:营业执照有效期"`
	TaxRegistrationNumber   string          `json:"tax_registration_number" gorm:"type:varchar(50);column:tax_registration_number;comment:税务登记号"`
	OrganizationCode        string          `json:"organization_code" gorm:"type:varchar(20);column:organization_code;comment:组织机构代码"`
	EstablishmentDate       *time.Time      `json:"establishment_date" gorm:"type:date;column:establishment_date;comment:成立日期"`
	BusinessTermStart       *time.Time      `json:"business_term_start" gorm:"type:date;column:business_term_start;comment:营业期限开始"`
	BusinessTermEnd         *time.Time      `json:"business_term_end" gorm:"type:date;column:business_term_end;comment:营业期限结束"`
	CompanyType             string          `json:"company_type" gorm:"type:varchar(50);column:company_type;comment:公司类型"`
	ApprovalDate            *time.Time      `json:"approval_date" gorm:"type:date;column:approval_date;comment:核准日期"`
	RegistrationAuthority   string          `json:"registration_authority" gorm:"type:varchar(100);column:registration_authority;comment:登记机关"`
	AnnualReportStatus      string          `json:"annual_report_status" gorm:"type:enum('normal','abnormal','not_submitted');default:normal;column:annual_report_status;comment:年报状态"`
	CreditRating            string          `json:"credit_rating" gorm:"type:varchar(10);column:credit_rating;comment:信用等级"`
	RiskLevel               string          `json:"risk_level" gorm:"type:enum('low','medium','high','critical');default:low;column:risk_level;index:idx_enterprises_risk_level;comment:风险等级"`
	CreatedAt               time.Time       `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt               time.Time       `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`
	DeletedAt               *time.Time      `json:"deleted_at" gorm:"column:deleted_at;index:idx_enterprises_deleted_at;comment:删除时间"`

	// 关联关系
	Owner  *User            `json:"owner,omitempty" gorm:"foreignKey:OwnerID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Parent *Enterprise      `json:"parent,omitempty" gorm:"foreignKey:ParentID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Users  []EnterpriseUser `json:"users,omitempty" gorm:"foreignKey:EnterpriseID;references:ID"`
}

// Document represents a document uploaded for an enterprise
type Document struct {
	ID           string     `json:"id" gorm:"primaryKey"`
	EnterpriseID string     `json:"enterpriseId" gorm:"index"`
	Type         string     `json:"type" gorm:"size:50"` // "business_license", "tax_registration", etc.
	Name         string     `json:"name" gorm:"size:255"`
	FilePath     string     `json:"filePath" gorm:"size:255"`
	FileSize     int64      `json:"fileSize"`
	ContentType  string     `json:"contentType" gorm:"size:100"`
	ExpiryDate   *time.Time `json:"expiryDate"`
	IsVerified   bool       `json:"isVerified" gorm:"default:false"`
	VerifiedAt   *time.Time `json:"verifiedAt"`
	VerifiedBy   string     `json:"verifiedBy" gorm:"size:36"`
	UploadedBy   string     `json:"uploadedBy" gorm:"size:36"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	DeletedAt    *time.Time `json:"deletedAt,omitempty" gorm:"index"`
}

// EnterpriseCreateRequest represents a request to create an enterprise
type EnterpriseCreateRequest struct {
	Name                    string    `json:"name" binding:"required"`
	UnifiedSocialCreditCode string    `json:"unifiedSocialCreditCode" binding:"required"`
	TaxpayerType            string    `json:"taxpayerType" binding:"required"`
	BusinessScope           string    `json:"businessScope"`
	RegisteredAddress       string    `json:"registeredAddress" binding:"required"`
	ActualAddress           string    `json:"actualAddress"`
	LegalRepresentative     string    `json:"legalRepresentative" binding:"required"`
	ContactPerson           string    `json:"contactPerson"`
	ContactPhone            string    `json:"contactPhone"`
	ContactEmail            string    `json:"contactEmail"`
	RegistrationDate        time.Time `json:"registrationDate"`
	BusinessLicenseExpiry   time.Time `json:"businessLicenseExpiry"`
	Industry                string    `json:"industry"`
	RegionCode              string    `json:"regionCode"`
	ParentID                string    `json:"parentId"`
	GroupID                 string    `json:"groupId"`
	IsHeadquarters          bool      `json:"isHeadquarters"`
	TaxBureau               string    `json:"taxBureau"`
	BankName                string    `json:"bankName"`
	BankAccount             string    `json:"bankAccount"`
	AccountingFirmID        string    `json:"accountingFirmId"`
}

// EnterpriseUpdateRequest represents a request to update an enterprise
type EnterpriseUpdateRequest struct {
	Name                  string    `json:"name"`
	TaxpayerType          string    `json:"taxpayerType"`
	BusinessScope         string    `json:"businessScope"`
	RegisteredAddress     string    `json:"registeredAddress"`
	ActualAddress         string    `json:"actualAddress"`
	LegalRepresentative   string    `json:"legalRepresentative"`
	ContactPerson         string    `json:"contactPerson"`
	ContactPhone          string    `json:"contactPhone"`
	ContactEmail          string    `json:"contactEmail"`
	BusinessLicenseExpiry time.Time `json:"businessLicenseExpiry"`
	Industry              string    `json:"industry"`
	RegionCode            string    `json:"regionCode"`
	ParentID              string    `json:"parentId"`
	GroupID               string    `json:"groupId"`
	IsHeadquarters        bool      `json:"isHeadquarters"`
	TaxBureau             string    `json:"taxBureau"`
	Status                string    `json:"status"`
	BankName              string    `json:"bankName"`
	BankAccount           string    `json:"bankAccount"`
	AccountingFirmID      string    `json:"accountingFirmId"`
}

// DocumentUploadRequest represents a request to upload a document
type DocumentUploadRequest struct {
	EnterpriseID string    `json:"enterpriseId" binding:"required"`
	Type         string    `json:"type" binding:"required"`
	Name         string    `json:"name" binding:"required"`
	ExpiryDate   time.Time `json:"expiryDate"`
}

// DocumentVerifyRequest represents a request to verify a document
type DocumentVerifyRequest struct {
	IsVerified bool   `json:"isVerified" binding:"required"`
	VerifiedBy string `json:"verifiedBy" binding:"required"`
}

// 企业状态常量
const (
	EnterpriseStatusActive    = "active"
	EnterpriseStatusInactive  = "inactive"
	EnterpriseStatusSuspended = "suspended"
	EnterpriseStatusCancelled = "cancelled"
)

// 纳税人类型常量
const (
	TaxpayerTypeGeneral = "general"
	TaxpayerTypeSmall   = "small"
)

// 验证状态常量
const (
	VerificationStatusPending  = "pending"
	VerificationStatusVerified = "verified"
	VerificationStatusRejected = "rejected"
)

// 年报状态常量
const (
	AnnualReportStatusNormal       = "normal"
	AnnualReportStatusAbnormal     = "abnormal"
	AnnualReportStatusNotSubmitted = "not_submitted"
)

// 风险等级常量
const (
	RiskLevelLow      = "low"
	RiskLevelMedium   = "medium"
	RiskLevelHigh     = "high"
	RiskLevelCritical = "critical"
)

// DocumentType constants
const (
	DocumentTypeBusinessLicense       = "business_license"        // 营业执照
	DocumentTypeTaxRegistration       = "tax_registration"        // 税务登记证
	DocumentTypeBankAccount           = "bank_account"            // 开户许可证
	DocumentTypeOrganizationCode      = "organization_code"       // 组织机构代码证
	DocumentTypeGeneralTaxpayerCert   = "general_taxpayer_cert"   // 一般纳税人资格证书
	DocumentTypeLegalRepresentativeID = "legal_representative_id" // 法人身份证
	DocumentTypeAuthorizedPersonID    = "authorized_person_id"    // 经办人身份证
	DocumentTypeOther                 = "other"                   // 其他
)

// EnterpriseResponse represents the response for an enterprise
type EnterpriseResponse struct {
	ID                      string    `json:"id"`
	Name                    string    `json:"name"`
	UnifiedSocialCreditCode string    `json:"unifiedSocialCreditCode"`
	TaxpayerType            string    `json:"taxpayerType"`
	LegalRepresentative     string    `json:"legalRepresentative"`
	RegisteredAddress       string    `json:"registeredAddress"`
	ContactPerson           string    `json:"contactPerson"`
	ContactPhone            string    `json:"contactPhone"`
	ContactEmail            string    `json:"contactEmail"`
	BankName                string    `json:"bankName"`
	BankAccount             string    `json:"bankAccount"`
	Industry                string    `json:"industry"`
	AnnualRevenue           float64   `json:"annualRevenue"`
	EmployeeCount           int       `json:"employeeCount"`
	RegisteredCapital       float64   `json:"registeredCapital"`
	CreatedAt               time.Time `json:"createdAt"`
	UpdatedAt               time.Time `json:"updatedAt"`
}
