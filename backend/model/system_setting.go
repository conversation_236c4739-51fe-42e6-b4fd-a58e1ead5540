package model

import "time"

// SystemSetting 系统设置模型
type SystemSetting struct {
	ID          string    `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:主键ID"`
	Category    string    `json:"category" gorm:"type:varchar(50);not null;column:category;index:idx_system_settings_category;comment:配置分类"`
	ConfigKey   string    `json:"config_key" gorm:"type:varchar(100);not null;column:config_key;comment:配置键"`
	ConfigValue string    `json:"config_value" gorm:"type:text;column:config_value;comment:配置值"`
	Description string    `json:"description" gorm:"type:varchar(500);column:description;comment:配置描述"`
	IsSystem    bool      `json:"is_system" gorm:"default:false;column:is_system;comment:是否系统配置"`
	IsEncrypted bool      `json:"is_encrypted" gorm:"default:false;column:is_encrypted;comment:是否加密"`
	CreatedAt   time.Time `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`
}

// TableName 指定表名
func (SystemSetting) TableName() string {
	return "system_settings"
}

// 系统设置分类常量
const (
	SystemSettingCategorySystem   = "system"
	SystemSettingCategorySecurity = "security"
	SystemSettingCategoryEmail    = "email"
	SystemSettingCategorySMS      = "sms"
	SystemSettingCategoryTax      = "tax"
	SystemSettingCategoryReport   = "report"
)

// 常用系统设置键常量
const (
	SystemSettingKeySystemName       = "system_name"
	SystemSettingKeyVersion          = "version"
	SystemSettingKeyPasswordExpiry   = "password_expiry_days"
	SystemSettingKeyMaxLoginAttempts = "max_login_attempts"
	SystemSettingKeyDefaultCurrency  = "default_currency"
	SystemSettingKeyDefaultTimezone  = "default_timezone"
	SystemSettingKeyEmailHost        = "email_host"
	SystemSettingKeyEmailPort        = "email_port"
	SystemSettingKeyEmailUsername    = "email_username"
	SystemSettingKeyEmailPassword    = "email_password"
	SystemSettingKeySMSProvider      = "sms_provider"
	SystemSettingKeySMSAPIKey        = "sms_api_key"
)
