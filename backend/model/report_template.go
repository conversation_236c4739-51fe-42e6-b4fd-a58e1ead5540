package model

import (
	"time"
)

// ReportTemplate 报表模板模型
type ReportTemplate struct {
	ID          string    `json:"id" gorm:"primaryKey;size:36;comment:主键ID"`
	Name        string    `json:"name" gorm:"not null;size:255;comment:模板名称"`
	Description *string   `json:"description" gorm:"type:text;comment:模板描述"`
	Type        string    `json:"type" gorm:"not null;size:100;comment:模板类型"`
	Content     string    `json:"content" gorm:"type:longtext;not null;comment:模板内容（HTML, JSON, etc.）"`
	Parameters  *string   `json:"parameters" gorm:"type:text;comment:模板参数定义（JSON）"`
	CreatedBy   *string   `json:"createdBy" gorm:"size:36;comment:创建者ID"`
	UpdatedBy   *string   `json:"updatedBy" gorm:"size:36;comment:更新者ID"`
	CreatedAt   time.Time `json:"createdAt" gorm:"not null;default:CURRENT_TIMESTAMP;comment:创建时间"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间"`

	// 关联关系
	Creator *User `json:"creator,omitempty" gorm:"foreignKey:CreatedBy;references:ID"`
	Updater *User `json:"updater,omitempty" gorm:"foreignKey:UpdatedBy;references:ID"`
}

// 报表模板分类常量
const (
	ReportCategoryDeclaration = "declaration" // 申报表
	ReportCategoryInvoice     = "invoice"     // 发票报表
	ReportCategoryTax         = "tax"         // 税务报表
	ReportCategoryFinancial   = "financial"   // 财务报表
	ReportCategoryStatistics  = "statistics"  // 统计报表
	ReportCategoryCustom      = "custom"      // 自定义报表
)

// 报表格式类型常量
const (
	FormatTypeExcel = "excel" // Excel格式
	FormatTypePDF   = "pdf"   // PDF格式
	FormatTypeHTML  = "html"  // HTML格式
	FormatTypeCSV   = "csv"   // CSV格式
	FormatTypeXML   = "xml"   // XML格式
	FormatTypeJSON  = "json"  // JSON格式
)

// ReportTemplateCreateRequest 创建报表模板请求
type ReportTemplateCreateRequest struct {
	Name        string  `json:"name" binding:"required"`
	Description *string `json:"description"`
	Type        string  `json:"type" binding:"required"`
	Content     string  `json:"content" binding:"required"`
	Parameters  *string `json:"parameters"`
}

// ReportTemplateUpdateRequest 更新报表模板请求
type ReportTemplateUpdateRequest struct {
	Name        *string `json:"name"`
	Description *string `json:"description"`
	Type        *string `json:"type"`
	Content     *string `json:"content"`
	Parameters  *string `json:"parameters"`
}

// ReportTemplateFilter 报表模板过滤条件
type ReportTemplateFilter struct {
	Name string `json:"name"`
	Type string `json:"type"`
}

// ReportTemplateResponse 报表模板响应
type ReportTemplateResponse struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description *string   `json:"description"`
	Type        string    `json:"type"`
	Content     string    `json:"content"`
	Parameters  *string   `json:"parameters"`
	CreatedBy   *string   `json:"createdBy"`
	UpdatedBy   *string   `json:"updatedBy"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// PaginatedReportTemplates 分页报表模板响应
type PaginatedReportTemplates struct {
	Templates []ReportTemplateResponse `json:"templates"`
	Total     int64                    `json:"total"`
	Page      int                      `json:"page"`
	Limit     int                      `json:"limit"`
}

// ReportTemplateField 报表模板字段
type ReportTemplateField struct {
	Name         string                `json:"name"`
	Label        string                `json:"label"`
	Type         string                `json:"type"`
	Required     bool                  `json:"required"`
	DefaultValue interface{}           `json:"defaultValue"`
	Options      []ReportFieldOption   `json:"options"`
	Validation   ReportFieldValidation `json:"validation"`
	Format       string                `json:"format"`
	Width        int                   `json:"width"`
	Align        string                `json:"align"`
	Sortable     bool                  `json:"sortable"`
	Filterable   bool                  `json:"filterable"`
}

// ReportFieldOption 报表字段选项
type ReportFieldOption struct {
	Label string      `json:"label"`
	Value interface{} `json:"value"`
}

// ReportFieldValidation 报表字段验证
type ReportFieldValidation struct {
	Min       *float64 `json:"min"`
	Max       *float64 `json:"max"`
	MinLength *int     `json:"minLength"`
	MaxLength *int     `json:"maxLength"`
	Pattern   string   `json:"pattern"`
	Message   string   `json:"message"`
}

// ReportTemplateGenerateRequest 生成报表请求
type ReportTemplateGenerateRequest struct {
	TemplateID   string                 `json:"templateId" binding:"required"`
	Data         map[string]interface{} `json:"data" binding:"required"`
	Parameters   map[string]interface{} `json:"parameters"`
	FormatType   string                 `json:"formatType"`
	FileName     string                 `json:"fileName"`
	EnterpriseID string                 `json:"enterpriseId"`
}

// ReportTemplateGenerateResult 生成报表结果
type ReportTemplateGenerateResult struct {
	FileName    string    `json:"fileName"`
	FileSize    int64     `json:"fileSize"`
	FormatType  string    `json:"formatType"`
	DownloadURL string    `json:"downloadUrl"`
	GeneratedAt time.Time `json:"generatedAt"`
	ExpiresAt   time.Time `json:"expiresAt"`
}

// ReportTemplateCopyRequest 复制报表模板请求
type ReportTemplateCopyRequest struct {
	SourceID string `json:"sourceId" binding:"required"`
	Name     string `json:"name" binding:"required,max=100"`
	Code     string `json:"code" binding:"required,max=50"`
}

// ReportTemplateSummary 报表模板汇总
type ReportTemplateSummary struct {
	Total        int            `json:"total"`
	Active       int            `json:"active"`
	Inactive     int            `json:"inactive"`
	System       int            `json:"system"`
	Custom       int            `json:"custom"`
	ByCategory   map[string]int `json:"byCategory"`
	ByFormatType map[string]int `json:"byFormatType"`
	ByTaxType    map[string]int `json:"byTaxType"`
}

// GetCategoryName 获取分类名称
func GetCategoryName(category string) string {
	switch category {
	case ReportCategoryDeclaration:
		return "申报表"
	case ReportCategoryInvoice:
		return "发票报表"
	case ReportCategoryTax:
		return "税务报表"
	case ReportCategoryFinancial:
		return "财务报表"
	case ReportCategoryStatistics:
		return "统计报表"
	case ReportCategoryCustom:
		return "自定义报表"
	default:
		return "未知分类"
	}
}

// GetFormatTypeName 获取格式类型名称
func GetFormatTypeName(formatType string) string {
	switch formatType {
	case FormatTypeExcel:
		return "Excel"
	case FormatTypePDF:
		return "PDF"
	case FormatTypeHTML:
		return "HTML"
	case FormatTypeCSV:
		return "CSV"
	case FormatTypeXML:
		return "XML"
	case FormatTypeJSON:
		return "JSON"
	default:
		return "未知格式"
	}
}

// IsValidCategory 验证分类是否有效
func IsValidCategory(category string) bool {
	validCategories := []string{
		ReportCategoryDeclaration,
		ReportCategoryInvoice,
		ReportCategoryTax,
		ReportCategoryFinancial,
		ReportCategoryStatistics,
		ReportCategoryCustom,
	}

	for _, validCategory := range validCategories {
		if category == validCategory {
			return true
		}
	}
	return false
}

// IsValidFormatType 验证格式类型是否有效
func IsValidFormatType(formatType string) bool {
	validFormats := []string{
		FormatTypeExcel,
		FormatTypePDF,
		FormatTypeHTML,
		FormatTypeCSV,
		FormatTypeXML,
		FormatTypeJSON,
	}

	for _, validFormat := range validFormats {
		if formatType == validFormat {
			return true
		}
	}
	return false
}

// TableName 指定表名
func (ReportTemplate) TableName() string {
	return "report_templates"
}
