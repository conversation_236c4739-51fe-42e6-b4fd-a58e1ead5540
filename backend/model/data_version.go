package model

import (
	"time"
)

// DataVersion 数据版本控制模型
type DataVersion struct {
	ID           string    `json:"id" gorm:"primaryKey;size:36;comment:主键ID"`
	Table        string    `json:"tableName" gorm:"column:table_name;not null;size:100;index:idx_data_versions_table;comment:表名"`
	RecordID     string    `json:"recordId" gorm:"not null;size:36;index:idx_data_versions_record;comment:记录ID"`
	Version      int       `json:"version" gorm:"not null;index:idx_data_versions_version;comment:版本号"`
	Operation    string    `json:"operation" gorm:"not null;size:20;index:idx_data_versions_operation;comment:操作类型"`
	OldData      string    `json:"oldData" gorm:"type:longtext;comment:变更前数据JSON"`
	NewData      string    `json:"newData" gorm:"type:longtext;comment:变更后数据JSON"`
	Changes      string    `json:"changes" gorm:"type:text;comment:变更字段JSON"`
	ChangeReason string    `json:"changeReason" gorm:"size:500;comment:变更原因"`
	UserID       string    `json:"userId" gorm:"not null;size:36;index:idx_data_versions_user;comment:操作用户ID"`
	UserIP       string    `json:"userIp" gorm:"size:45;comment:用户IP地址"`
	UserAgent    string    `json:"userAgent" gorm:"size:500;comment:用户代理"`
	EnterpriseID *string   `json:"enterpriseId" gorm:"size:36;index:idx_data_versions_enterprise;comment:企业ID"`
	CreatedAt    time.Time `json:"createdAt" gorm:"index:idx_data_versions_created;comment:创建时间"`

	// 关联关系
	User       *User       `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
	Enterprise *Enterprise `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID"`
}

// 操作类型常量
const (
	OperationCreate  = "create"  // 创建
	OperationUpdate  = "update"  // 更新
	OperationDelete  = "delete"  // 删除
	OperationRestore = "restore" // 恢复
)

// DataVersionCreateRequest 创建数据版本请求
type DataVersionCreateRequest struct {
	TableName    string                 `json:"tableName" binding:"required"`
	RecordID     string                 `json:"recordId" binding:"required"`
	Operation    string                 `json:"operation" binding:"required"`
	OldData      map[string]interface{} `json:"oldData"`
	NewData      map[string]interface{} `json:"newData"`
	Changes      []string               `json:"changes"`
	ChangeReason string                 `json:"changeReason"`
	UserIP       string                 `json:"userIp"`
	UserAgent    string                 `json:"userAgent"`
	EnterpriseID *string                `json:"enterpriseId"`
}

// DataVersionFilter 数据版本过滤条件
type DataVersionFilter struct {
	TableName    string     `json:"tableName"`
	RecordID     string     `json:"recordId"`
	Operation    string     `json:"operation"`
	UserID       string     `json:"userId"`
	EnterpriseID string     `json:"enterpriseId"`
	StartTime    *time.Time `json:"startTime"`
	EndTime      *time.Time `json:"endTime"`
	VersionFrom  *int       `json:"versionFrom"`
	VersionTo    *int       `json:"versionTo"`
}

// DataVersionResponse 数据版本响应
type DataVersionResponse struct {
	ID             string    `json:"id"`
	TableName      string    `json:"tableName"`
	TableNameCN    string    `json:"tableNameCn"`
	RecordID       string    `json:"recordId"`
	Version        int       `json:"version"`
	Operation      string    `json:"operation"`
	OperationName  string    `json:"operationName"`
	OldData        string    `json:"oldData"`
	NewData        string    `json:"newData"`
	Changes        string    `json:"changes"`
	ChangeFields   []string  `json:"changeFields"`
	ChangeReason   string    `json:"changeReason"`
	UserID         string    `json:"userId"`
	UserName       string    `json:"userName"`
	UserIP         string    `json:"userIp"`
	UserAgent      string    `json:"userAgent"`
	EnterpriseID   *string   `json:"enterpriseId"`
	EnterpriseName string    `json:"enterpriseName"`
	CreatedAt      time.Time `json:"createdAt"`
}

// PaginatedDataVersions 分页数据版本响应
type PaginatedDataVersions struct {
	Versions []DataVersionResponse `json:"versions"`
	Total    int64                 `json:"total"`
	Page     int                   `json:"page"`
	Limit    int                   `json:"limit"`
}

// DataVersionComparison 数据版本比较
type DataVersionComparison struct {
	RecordID     string                   `json:"recordId"`
	TableName    string                   `json:"tableName"`
	FromVersion  int                      `json:"fromVersion"`
	ToVersion    int                      `json:"toVersion"`
	FieldChanges []DataVersionFieldChange `json:"fieldChanges"`
	FromData     map[string]interface{}   `json:"fromData"`
	ToData       map[string]interface{}   `json:"toData"`
	ComparedAt   time.Time                `json:"comparedAt"`
}

// DataVersionFieldChange 字段变更详情
type DataVersionFieldChange struct {
	FieldName  string      `json:"fieldName"`
	FieldLabel string      `json:"fieldLabel"`
	OldValue   interface{} `json:"oldValue"`
	NewValue   interface{} `json:"newValue"`
	ChangeType string      `json:"changeType"`
}

// DataVersionStatistics 数据版本统计
type DataVersionStatistics struct {
	Total         int64                 `json:"total"`
	ByOperation   map[string]int64      `json:"byOperation"`
	ByTable       map[string]int64      `json:"byTable"`
	ByUser        map[string]int64      `json:"byUser"`
	ByEnterprise  map[string]int64      `json:"byEnterprise"`
	ByHour        map[string]int64      `json:"byHour"`
	ByDay         map[string]int64      `json:"byDay"`
	RecentChanges []DataVersionResponse `json:"recentChanges"`
	TopUsers      []UserChangeStats     `json:"topUsers"`
	TopTables     []TableChangeStats    `json:"topTables"`
}

// UserChangeStats 用户变更统计
type UserChangeStats struct {
	UserID     string    `json:"userId"`
	UserName   string    `json:"userName"`
	Count      int64     `json:"count"`
	LastChange time.Time `json:"lastChange"`
}

// TableChangeStats 表变更统计
type TableChangeStats struct {
	TableName   string    `json:"tableName"`
	TableNameCN string    `json:"tableNameCn"`
	Count       int64     `json:"count"`
	LastChange  time.Time `json:"lastChange"`
}

// DataVersionRestoreRequest 数据恢复请求
type DataVersionRestoreRequest struct {
	VersionID     string `json:"versionId" binding:"required"`
	RestoreReason string `json:"restoreReason" binding:"required"`
}

// DataVersionRestoreResult 数据恢复结果
type DataVersionRestoreResult struct {
	Success      bool      `json:"success"`
	Message      string    `json:"message"`
	RestoredData string    `json:"restoredData"`
	NewVersionID string    `json:"newVersionId"`
	RestoredAt   time.Time `json:"restoredAt"`
}

// DataVersionExportRequest 导出数据版本请求
type DataVersionExportRequest struct {
	Filter   DataVersionFilter `json:"filter"`
	Format   string            `json:"format" binding:"required"`
	Fields   []string          `json:"fields"`
	FileName string            `json:"fileName"`
}

// DataVersionExportResult 导出数据版本结果
type DataVersionExportResult struct {
	FileName    string    `json:"fileName"`
	FileSize    int64     `json:"fileSize"`
	RecordCount int64     `json:"recordCount"`
	Format      string    `json:"format"`
	DownloadURL string    `json:"downloadUrl"`
	ExportedAt  time.Time `json:"exportedAt"`
	ExpiresAt   time.Time `json:"expiresAt"`
}

// DataVersionCleanupRequest 清理数据版本请求
type DataVersionCleanupRequest struct {
	TableName    string     `json:"tableName"`
	KeepDays     int        `json:"keepDays" binding:"required,min=1"`
	KeepVersions int        `json:"keepVersions" binding:"required,min=1"`
	DryRun       bool       `json:"dryRun"`
	BeforeDate   *time.Time `json:"beforeDate"`
}

// DataVersionCleanupResult 清理数据版本结果
type DataVersionCleanupResult struct {
	TotalScanned int64            `json:"totalScanned"`
	TotalDeleted int64            `json:"totalDeleted"`
	ByTable      map[string]int64 `json:"byTable"`
	CleanedAt    time.Time        `json:"cleanedAt"`
	DryRun       bool             `json:"dryRun"`
}

// GetOperationName 获取操作名称
func GetOperationName(operation string) string {
	switch operation {
	case OperationCreate:
		return "创建"
	case OperationUpdate:
		return "更新"
	case OperationDelete:
		return "删除"
	case OperationRestore:
		return "恢复"
	default:
		return "未知操作"
	}
}

// GetTableNameCN 获取表中文名称
func GetTableNameCN(tableName string) string {
	tableNames := map[string]string{
		"enterprises":              "企业信息",
		"users":                    "用户信息",
		"invoices":                 "发票信息",
		"declarations":             "申报信息",
		"tax_types":                "税种信息",
		"tax_calendars":            "税务日历",
		"enterprise_relationships": "企业关系",
		"report_templates":         "报表模板",
		"message_templates":        "消息模板",
		"message_logs":             "消息记录",
		"enterprise_settings":      "企业配置",
		"export_tasks":             "导出任务",
		"import_tasks":             "导入任务",
		"audit_logs":               "审计日志",
		"notifications":            "通知信息",
		"roles":                    "角色信息",
		"user_roles":               "用户角色",
		"permissions":              "权限信息",
	}

	if cnName, exists := tableNames[tableName]; exists {
		return cnName
	}
	return tableName
}

// IsValidOperation 验证操作类型是否有效
func IsValidOperation(operation string) bool {
	validOperations := []string{
		OperationCreate,
		OperationUpdate,
		OperationDelete,
		OperationRestore,
	}

	for _, validOperation := range validOperations {
		if operation == validOperation {
			return true
		}
	}
	return false
}

// GetChangeType 获取变更类型
func GetChangeType(oldValue, newValue interface{}) string {
	if oldValue == nil && newValue != nil {
		return "added"
	}
	if oldValue != nil && newValue == nil {
		return "removed"
	}
	if oldValue != newValue {
		return "modified"
	}
	return "unchanged"
}

// TableName 指定表名
func (DataVersion) TableName() string {
	return "data_versions"
}
