package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// 申报状态常量
const (
	DeclarationStatusDraft     = "draft"
	DeclarationStatusSubmitted = "submitted"
	DeclarationStatusApproved  = "approved"
	DeclarationStatusRejected  = "rejected"
	DeclarationStatusPaid      = "paid"
	DeclarationStatusOverdue   = "overdue"
)

// 申报期间类型常量
const (
	DeclarationPeriodMonthly   = "monthly"
	DeclarationPeriodQuarterly = "quarterly"
	DeclarationPeriodAnnually  = "annually"
	DeclarationPeriodOther     = "other"
)

// 缴款方式常量
const (
	PaymentMethodBankTransfer = "bank_transfer"
	PaymentMethodOnline       = "online"
	PaymentMethodCash         = "cash"
	PaymentMethodCheck        = "check"
)

// Declaration 税务申报模型
type Declaration struct {
	ID                    string          `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:申报ID"`
	EnterpriseID          string          `json:"enterprise_id" gorm:"type:varchar(36);not null;column:enterprise_id;index:idx_declarations_enterprise_id;comment:企业ID"`
	TaxTypeID             string          `json:"tax_type_id" gorm:"type:varchar(36);not null;column:tax_type_id;index:idx_declarations_tax_type_id;comment:税种ID"`
	DeclarationNumber     string          `json:"declaration_number" gorm:"type:varchar(50);not null;uniqueIndex:uk_declarations_number;column:declaration_number;comment:申报编号"`
	DeclarationDate       time.Time       `json:"declaration_date" gorm:"not null;column:declaration_date;index:idx_declarations_date;comment:申报日期"`
	PeriodType            string          `json:"period_type" gorm:"type:enum('monthly','quarterly','annually','other');not null;column:period_type;comment:期间类型"`
	Year                  int             `json:"year" gorm:"not null;column:year;comment:年度"`
	Month                 *int            `json:"month" gorm:"column:month;comment:月份"`
	Quarter               *int            `json:"quarter" gorm:"column:quarter;comment:季度"`
	Status                string          `json:"status" gorm:"type:enum('draft','submitted','approved','rejected','paid','overdue');default:draft;column:status;index:idx_declarations_status;comment:申报状态"`
	Amount                decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);default:0;column:amount;comment:申报金额"`
	TotalIncome           decimal.Decimal `json:"total_income" gorm:"type:decimal(15,2);default:0;column:total_income;comment:总收入"`
	TaxableIncome         decimal.Decimal `json:"taxable_income" gorm:"type:decimal(15,2);default:0;column:taxable_income;comment:应税收入"`
	TaxPayable            decimal.Decimal `json:"tax_payable" gorm:"type:decimal(15,2);default:0;column:tax_payable;comment:应纳税额"`
	TaxPaid               decimal.Decimal `json:"tax_paid" gorm:"type:decimal(15,2);default:0;column:tax_paid;comment:已缴税额"`
	TaxRefund             decimal.Decimal `json:"tax_refund" gorm:"type:decimal(15,2);default:0;column:tax_refund;comment:退税金额"`
	LateFee               decimal.Decimal `json:"late_fee" gorm:"type:decimal(15,2);default:0;column:late_fee;comment:滞纳金"`
	DueDate               time.Time       `json:"due_date" gorm:"not null;column:due_date;index:idx_declarations_due_date;comment:申报截止日期"`
	TaxPeriodStart        time.Time       `json:"tax_period_start" gorm:"not null;column:tax_period_start;comment:计税期间开始"`
	TaxPeriodEnd          time.Time       `json:"tax_period_end" gorm:"not null;column:tax_period_end;comment:计税期间结束"`
	PaymentDueDate        *time.Time      `json:"payment_due_date" gorm:"column:payment_due_date;comment:缴款截止日期"`
	SubmittedAt           *time.Time      `json:"submitted_at" gorm:"column:submitted_at;comment:提交时间"`
	SubmittedBy           *string         `json:"submitted_by" gorm:"type:varchar(36);column:submitted_by;comment:提交人ID"`
	ApprovedAt            *time.Time      `json:"approved_at" gorm:"column:approved_at;comment:审批时间"`
	ApprovedBy            *string         `json:"approved_by" gorm:"type:varchar(36);column:approved_by;comment:审批人ID"`
	PaidAt                *time.Time      `json:"paid_at" gorm:"column:paid_at;comment:缴款时间"`
	PaymentMethod         string          `json:"payment_method" gorm:"type:enum('bank_transfer','online','cash','check');column:payment_method;comment:缴款方式"`
	PaymentReference      string          `json:"payment_reference" gorm:"type:varchar(100);column:payment_reference;comment:缴款凭证号"`
	CorrectionCount       int             `json:"correction_count" gorm:"default:0;column:correction_count;comment:更正次数"`
	IsCorrection          bool            `json:"is_correction" gorm:"default:false;column:is_correction;comment:是否更正申报"`
	OriginalDeclarationID *string         `json:"original_declaration_id" gorm:"type:varchar(36);column:original_declaration_id;index:idx_declarations_original_id;comment:原申报ID"`
	CorrectionReason      string          `json:"correction_reason" gorm:"type:varchar(500);column:correction_reason;comment:更正原因"`
	AutoCalculated        bool            `json:"auto_calculated" gorm:"default:false;column:auto_calculated;comment:是否自动计算"`
	CalculationFormula    string          `json:"calculation_formula" gorm:"type:json;column:calculation_formula;comment:计算公式"`
	AttachmentCount       int             `json:"attachment_count" gorm:"default:0;column:attachment_count;comment:附件数量"`
	Remarks               string          `json:"remarks" gorm:"type:text;column:remarks;comment:备注"`
	CreatedAt             time.Time       `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt             time.Time       `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`
	DeletedAt             *time.Time      `json:"deleted_at" gorm:"column:deleted_at;index:idx_declarations_deleted_at;comment:删除时间"`

	// 关联关系
	Enterprise          *Enterprise  `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	TaxType             *TaxType     `json:"tax_type,omitempty" gorm:"foreignKey:TaxTypeID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	OriginalDeclaration *Declaration `json:"original_declaration,omitempty" gorm:"foreignKey:OriginalDeclarationID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

// DeclarationAttachment represents an attachment to a tax declaration
type DeclarationAttachment struct {
	ID            string    `json:"id" gorm:"primaryKey"`
	DeclarationID string    `json:"declarationId" gorm:"index"`
	Type          string    `json:"type" gorm:"size:50"` // "form", "receipt", "supporting_doc", etc.
	Name          string    `json:"name" gorm:"size:255"`
	FileName      string    `json:"fileName" gorm:"size:255"`
	Description   string    `json:"description" gorm:"type:text"`
	FilePath      string    `json:"filePath" gorm:"size:255"`
	FileSize      int64     `json:"fileSize"`
	ContentType   string    `json:"contentType" gorm:"size:100"`
	UploadedBy    string    `json:"uploadedBy" gorm:"size:36"`
	UploadedAt    time.Time `json:"uploadedAt"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
}

// DeclarationHistory represents the history of changes to a declaration
type DeclarationHistory struct {
	ID            string    `json:"id" gorm:"primaryKey"`
	DeclarationID string    `json:"declarationId" gorm:"index"`
	Action        string    `json:"action" gorm:"size:50"` // "created", "updated", "submitted", "approved", etc.
	Status        string    `json:"status" gorm:"size:20"` // Status after the action
	PerformedBy   string    `json:"performedBy" gorm:"size:36"`
	Details       string    `json:"details" gorm:"type:text"`
	Notes         string    `json:"notes" gorm:"type:text"`
	OldValues     string    `json:"oldValues,omitempty" gorm:"type:json"`
	NewValues     string    `json:"newValues,omitempty" gorm:"type:json"`
	Timestamp     time.Time `json:"timestamp"`
	CreatedAt     time.Time `json:"createdAt"`
}

// DeclarationTemplate represents a tax declaration template
type DeclarationTemplate struct {
	ID          string     `json:"id" gorm:"primaryKey"`
	TaxTypeID   string     `json:"taxTypeId" gorm:"index"`
	Name        string     `json:"name" gorm:"size:100"`
	Description string     `json:"description" gorm:"type:text"`
	Version     string     `json:"version" gorm:"size:20"`
	Structure   string     `json:"structure" gorm:"type:json"` // JSON schema of the template
	ValidFrom   time.Time  `json:"validFrom"`
	ValidUntil  *time.Time `json:"validUntil"`
	IsActive    bool       `json:"isActive" gorm:"default:true"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   time.Time  `json:"updatedAt"`
}

// TaxCalculation 表示税款计算结果
type TaxCalculation struct {
	EnterpriseID   string                 `json:"enterpriseId"`   // 企业ID
	TaxTypeID      string                 `json:"taxTypeId"`      // 税种ID
	TaxPeriodStart time.Time              `json:"taxPeriodStart"` // 计税期间开始
	TaxPeriodEnd   time.Time              `json:"taxPeriodEnd"`   // 计税期间结束
	TotalTaxable   float64                `json:"totalTaxable"`   // 计税基数合计
	TotalTax       float64                `json:"totalTax"`       // 税额合计
	Breakdown      []TaxCalculationDetail `json:"breakdown"`      // 计算明细
	AppliedRules   []string               `json:"appliedRules"`   // 应用的规则ID列表
	CalculatedAt   time.Time              `json:"calculatedAt"`   // 计算时间
}

// TaxCalculationDetail 表示税款计算的明细
type TaxCalculationDetail struct {
	TaxRuleID     string  `json:"taxRuleId"`     // 税则ID
	TaxRuleName   string  `json:"taxRuleName"`   // 税则名称
	TaxableAmount float64 `json:"taxableAmount"` // 计税基数
	Rate          float64 `json:"rate"`          // 税率
	TaxAmount     float64 `json:"taxAmount"`     // 税额
	Description   string  `json:"description"`   // 描述
}

// DeclarationSummary 表示企业的申报汇总
type DeclarationSummary struct {
	EnterpriseID         string                `json:"enterpriseId"`         // 企业ID
	Year                 int                   `json:"year"`                 // 年份
	TotalAmount          float64               `json:"totalAmount"`          // 应纳税总额
	ByStatus             map[string]int        `json:"byStatus"`             // 各状态数量
	ByPeriod             map[string]float64    `json:"byPeriod"`             // 各期间金额
	ByTaxType            map[string]float64    `json:"byTaxType"`            // 各税种金额
	UpcomingDeclarations []UpcomingDeclaration `json:"upcomingDeclarations"` // 即将到期的申报
}

// UpcomingDeclaration 表示即将到期的申报
type UpcomingDeclaration struct {
	ID            string    `json:"id"`            // 申报ID
	TaxTypeName   string    `json:"taxTypeName"`   // 税种名称
	Period        string    `json:"period"`        // 期间
	DueDate       time.Time `json:"dueDate"`       // 截止日期
	Amount        float64   `json:"amount"`        // 金额
	DaysRemaining int       `json:"daysRemaining"` // 剩余天数
}

// DeclarationCreateRequest 表示创建申报的请求
type DeclarationCreateRequest struct {
	EnterpriseID    string                         `json:"enterprise_id" binding:"required"`    // 企业ID
	TaxTypeID       string                         `json:"tax_type_id" binding:"required"`      // 税种ID
	DeclarationDate time.Time                      `json:"declaration_date" binding:"required"` // 申报日期
	DueDate         time.Time                      `json:"due_date" binding:"required"`         // 截止日期
	PeriodType      string                         `json:"period_type" binding:"required"`      // 期间类型
	Year            int                            `json:"year" binding:"required"`             // 年份
	Month           *int                           `json:"month"`                               // 月份
	Quarter         *int                           `json:"quarter"`                             // 季度
	Amount          decimal.Decimal                `json:"amount"`                              // 申报金额
	TaxPeriodStart  time.Time                      `json:"tax_period_start" binding:"required"` // 计税期间开始
	TaxPeriodEnd    time.Time                      `json:"tax_period_end" binding:"required"`   // 计税期间结束
	Items           []DeclarationItemCreateRequest `json:"items"`                               // 申报项
	Notes           string                         `json:"notes"`                               // 备注
}

// DeclarationItemCreateRequest 表示创建申报项的请求
type DeclarationItemCreateRequest struct {
	ItemType      string   `json:"itemType" binding:"required"` // 项目类型
	Description   string   `json:"description"`                 // 描述
	Amount        float64  `json:"amount" binding:"required"`   // 金额
	TaxableAmount float64  `json:"taxableAmount"`               // 计税金额
	TaxRate       float64  `json:"taxRate"`                     // 税率
	TaxAmount     float64  `json:"taxAmount"`                   // 税额
	Category      string   `json:"category"`                    // 类别
	SubCategory   string   `json:"subCategory"`                 // 子类别
	InvoiceIDs    []string `json:"invoiceIds"`                  // 关联发票ID列表
}

// DeclarationUpdateRequest 表示更新申报的请求
type DeclarationUpdateRequest struct {
	Status        *string                        `json:"status,omitempty"`        // 状态
	Notes         *string                        `json:"notes,omitempty"`         // 备注
	TaxableAmount *float64                       `json:"taxableAmount,omitempty"` // 计税基数
	TaxAmount     *float64                       `json:"taxAmount,omitempty"`     // 应纳税额
	Items         []DeclarationItemUpdateRequest `json:"items,omitempty"`         // 申报项
}

// DeclarationSubmitRequest 表示提交申报的请求
type DeclarationSubmitRequest struct {
	Notes string `json:"notes"` // 提交备注
}

// DeclarationItemUpdateRequest 表示更新申报项的请求
type DeclarationItemUpdateRequest struct {
	ID            string   `json:"id,omitempty"`            // 明细ID（为空表示新增）
	ItemType      string   `json:"itemType,omitempty"`      // 项目类型
	Description   string   `json:"description,omitempty"`   // 描述
	Amount        float64  `json:"amount,omitempty"`        // 金额
	TaxableAmount float64  `json:"taxableAmount,omitempty"` // 计税金额
	TaxRate       float64  `json:"taxRate,omitempty"`       // 税率
	TaxAmount     float64  `json:"taxAmount,omitempty"`     // 税额
	Category      string   `json:"category,omitempty"`      // 类别
	SubCategory   string   `json:"subCategory,omitempty"`   // 子类别
	InvoiceIDs    []string `json:"invoiceIds,omitempty"`    // 关联发票ID列表
}

// DeclarationGenerateRequest 表示批量生成申报的请求
type DeclarationGenerateRequest struct {
	EnterpriseID string `json:"enterpriseId,omitempty"`    // 企业ID（可选，为空表示所有企业）
	TaxTypeID    string `json:"taxTypeId,omitempty"`       // 税种ID（可选，为空表示所有税种）
	Period       string `json:"period" binding:"required"` // 期间
	Year         int    `json:"year" binding:"required"`   // 年份
}

// DeclarationGenerateResult 表示批量生成申报的结果
type DeclarationGenerateResult struct {
	GeneratedCount int      `json:"generatedCount"`   // 已生成数量
	ExistingCount  int      `json:"existingCount"`    // 已存在数量
	Errors         []string `json:"errors,omitempty"` // 错误信息列表
}

// DeclarationCalculation 表示申报计算结果
type DeclarationCalculation struct {
	DeclarationID string    `json:"declarationId"` // 申报ID
	TaxTypeID     string    `json:"taxTypeId"`     // 税种ID
	TaxTypeName   string    `json:"taxTypeName"`   // 税种名称
	Period        string    `json:"period"`        // 申报期间
	Year          int       `json:"year"`          // 申报年份
	StartDate     time.Time `json:"startDate"`     // 开始日期
	EndDate       time.Time `json:"endDate"`       // 结束日期
	TaxableAmount float64   `json:"taxableAmount"` // 计税基数
	TaxAmount     float64   `json:"taxAmount"`     // 应纳税额
	TaxRule       TaxRule   `json:"taxRule"`       // 应用的税则
	CalculatedAt  time.Time `json:"calculatedAt"`  // 计算时间
}

// TaxCalculationRequest 表示税款计算请求
type TaxCalculationRequest struct {
	EnterpriseID   string        `json:"enterpriseId" binding:"required"`   // 企业ID
	TaxTypeID      string        `json:"taxTypeId" binding:"required"`      // 税种ID
	TaxPeriodStart time.Time     `json:"taxPeriodStart" binding:"required"` // 计税期间开始
	TaxPeriodEnd   time.Time     `json:"taxPeriodEnd" binding:"required"`   // 计税期间结束
	TaxableItems   []TaxableItem `json:"taxableItems"`                      // 计税项目
	ApplyRules     []string      `json:"applyRules,omitempty"`              // 指定应用的规则ID列表（为空表示全部）
}

// TaxableItem 表示计税项目
type TaxableItem struct {
	ItemType    string  `json:"itemType" binding:"required"` // 项目类型
	Amount      float64 `json:"amount" binding:"required"`   // 金额
	Description string  `json:"description"`                 // 描述
	Category    string  `json:"category"`                    // 类别
	SubCategory string  `json:"subCategory"`                 // 子类别
}

// DeclarationFilter 定义申报查询过滤条件
type DeclarationFilter struct {
	EnterpriseID string     `json:"enterpriseId"` // 企业ID
	TaxTypeID    string     `json:"taxTypeId"`    // 税种ID
	StartDate    *time.Time `json:"startDate"`    // 开始日期
	EndDate      *time.Time `json:"endDate"`      // 结束日期
	Status       string     `json:"status"`       // 状态
	Year         int        `json:"year"`         // 年份
	Quarter      int        `json:"quarter"`      // 季度
	Month        int        `json:"month"`        // 月份
	Page         int        `json:"page"`         // 页码
	PageSize     int        `json:"pageSize"`     // 每页数量
	SortBy       string     `json:"sortBy"`       // 排序字段
	SortDesc     bool       `json:"sortDesc"`     // 是否降序
}

// PaginatedDeclarations 分页申报响应结构
type PaginatedDeclarations struct {
	Declarations []Declaration `json:"declarations"`
	Total        int64         `json:"total"`
	Page         int           `json:"page"`
	PageSize     int           `json:"pageSize"`
	Limit        int           `json:"limit"`
	TotalPages   int64         `json:"totalPages"`
}
