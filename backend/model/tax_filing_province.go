package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// TaxFilingProvinceStatus 省份状态枚举
type TaxFilingProvinceStatus string

const (
	TaxFilingProvinceStatusActive      TaxFilingProvinceStatus = "active"
	TaxFilingProvinceStatusInactive    TaxFilingProvinceStatus = "inactive"
	TaxFilingProvinceStatusMaintenance TaxFilingProvinceStatus = "maintenance"
)

// TaxFilingAuthMethod 认证方式枚举
type TaxFilingAuthMethod string

const (
	TaxFilingAuthMethodUsernamePassword TaxFilingAuthMethod = "username_password"
	TaxFilingAuthMethodAPIKey           TaxFilingAuthMethod = "api_key"
	TaxFilingAuthMethodCertificate      TaxFilingAuthMethod = "certificate"
	TaxFilingAuthMethodOAuth2           TaxFilingAuthMethod = "oauth2"
)

// AuthConfig 认证配置
type AuthConfig map[string]interface{}

// Value 实现 driver.Valuer 接口
func (a AuthConfig) Value() (driver.Value, error) {
	if len(a) == 0 {
		return nil, nil
	}
	return json.Marshal(a)
}

// Scan 实现 sql.Scanner 接口
func (a *AuthConfig) Scan(value interface{}) error {
	if value == nil {
		*a = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into AuthConfig", value)
	}

	return json.Unmarshal(bytes, a)
}

// APIEndpoints API端点配置
type APIEndpoints map[string]string

// Value 实现 driver.Valuer 接口
func (a APIEndpoints) Value() (driver.Value, error) {
	if len(a) == 0 {
		return nil, nil
	}
	return json.Marshal(a)
}

// Scan 实现 sql.Scanner 接口
func (a *APIEndpoints) Scan(value interface{}) error {
	if value == nil {
		*a = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into APIEndpoints", value)
	}

	return json.Unmarshal(bytes, a)
}

// DataMappings 数据映射配置
type DataMappings map[string]interface{}

// Value 实现 driver.Valuer 接口
func (d DataMappings) Value() (driver.Value, error) {
	if len(d) == 0 {
		return nil, nil
	}
	return json.Marshal(d)
}

// Scan 实现 sql.Scanner 接口
func (d *DataMappings) Scan(value interface{}) error {
	if value == nil {
		*d = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into DataMappings", value)
	}

	return json.Unmarshal(bytes, d)
}

// ValidationRules 验证规则配置
type ValidationRules map[string]interface{}

// Value 实现 driver.Valuer 接口
func (v ValidationRules) Value() (driver.Value, error) {
	if len(v) == 0 {
		return nil, nil
	}
	return json.Marshal(v)
}

// Scan 实现 sql.Scanner 接口
func (v *ValidationRules) Scan(value interface{}) error {
	if value == nil {
		*v = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into ValidationRules", value)
	}

	return json.Unmarshal(bytes, v)
}

// TaxFilingProvince 税务申报省份配置模型
type TaxFilingProvince struct {
	ID     string  `gorm:"primaryKey;size:50" json:"id"`
	Code   string  `gorm:"uniqueIndex;size:10;not null" json:"code"`
	Name   string  `gorm:"size:50;not null" json:"name"`
	NameEn *string `gorm:"size:100" json:"name_en,omitempty"`

	// 基础配置
	BaseURL    string                  `gorm:"size:500;not null" json:"base_url"`
	Status     TaxFilingProvinceStatus `gorm:"size:20;not null;default:active;index" json:"status"`
	AuthMethod TaxFilingAuthMethod     `gorm:"size:50;not null" json:"auth_method"`

	// 超时和重试配置
	Timeout    int     `gorm:"not null;default:30" json:"timeout"`                         // 超时时间(秒)
	MaxRetries int     `gorm:"not null;default:3" json:"max_retries"`                      // 最大重试次数
	RetryDelay float64 `gorm:"type:decimal(5,2);not null;default:1.00" json:"retry_delay"` // 重试延迟(秒)

	// 配置信息
	AuthConfig      AuthConfig      `gorm:"type:json;not null" json:"auth_config"`
	APIEndpoints    APIEndpoints    `gorm:"type:json;not null" json:"api_endpoints"`
	DataMappings    DataMappings    `gorm:"type:json" json:"data_mappings,omitempty"`
	ValidationRules ValidationRules `gorm:"type:json" json:"validation_rules,omitempty"`

	// 健康检查
	LastHealthCheck *time.Time `gorm:"index:idx_health_check" json:"last_health_check,omitempty"`
	HealthStatus    *string    `gorm:"size:20;index:idx_health_check" json:"health_status,omitempty"`
	HealthMessage   *string    `gorm:"type:text" json:"health_message,omitempty"`

	// 统计信息
	TotalSubmissions      int        `gorm:"not null;default:0" json:"total_submissions"`
	SuccessfulSubmissions int        `gorm:"not null;default:0" json:"successful_submissions"`
	FailedSubmissions     int        `gorm:"not null;default:0" json:"failed_submissions"`
	AverageProcessingTime *float64   `gorm:"type:decimal(8,2)" json:"average_processing_time,omitempty"` // 分钟
	LastSubmissionAt      *time.Time `json:"last_submission_at,omitempty"`
	UptimePercentage      *float64   `gorm:"type:decimal(5,2)" json:"uptime_percentage,omitempty"`

	// 系统字段
	CreatedBy *string    `gorm:"size:50" json:"created_by,omitempty"`
	UpdatedBy *string    `gorm:"size:50" json:"updated_by,omitempty"`
	IsDeleted bool       `gorm:"not null;default:false" json:"is_deleted"`
	CreatedAt time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP;index" json:"created_at"`
	UpdatedAt time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty"`

	// 关联
	Submissions []TaxFilingSubmission `gorm:"foreignKey:ProvinceCode;references:Code" json:"submissions,omitempty"`
}

// TableName 指定表名
func (TaxFilingProvince) TableName() string {
	return "tax_filing_provinces"
}

// BeforeCreate GORM钩子 - 创建前
func (p *TaxFilingProvince) BeforeCreate(tx *gorm.DB) error {
	if p.ID == "" {
		p.ID = GenerateID()
	}
	return nil
}

// BeforeUpdate GORM钩子 - 更新前
func (p *TaxFilingProvince) BeforeUpdate(tx *gorm.DB) error {
	p.UpdatedAt = time.Now()
	return nil
}

// IsActive 检查省份是否激活
func (p *TaxFilingProvince) IsActive() bool {
	return p.Status == TaxFilingProvinceStatusActive
}

// GetSuccessRate 获取成功率
func (p *TaxFilingProvince) GetSuccessRate() float64 {
	if p.TotalSubmissions == 0 {
		return 0.0
	}
	return float64(p.SuccessfulSubmissions) / float64(p.TotalSubmissions) * 100
}

// GetFailureRate 获取失败率
func (p *TaxFilingProvince) GetFailureRate() float64 {
	if p.TotalSubmissions == 0 {
		return 0.0
	}
	return float64(p.FailedSubmissions) / float64(p.TotalSubmissions) * 100
}

// UpdateStatistics 更新统计信息
func (p *TaxFilingProvince) UpdateStatistics(successful bool, processingTime *float64) {
	p.TotalSubmissions++
	if successful {
		p.SuccessfulSubmissions++
	} else {
		p.FailedSubmissions++
	}

	if processingTime != nil {
		if p.AverageProcessingTime == nil {
			p.AverageProcessingTime = processingTime
		} else {
			// 计算移动平均值
			*p.AverageProcessingTime = (*p.AverageProcessingTime + *processingTime) / 2
		}
	}

	now := time.Now()
	p.LastSubmissionAt = &now
}

// TaxFilingProvinceCreateRequest 创建省份配置请求
type TaxFilingProvinceCreateRequest struct {
	Code            string                  `json:"code" binding:"required"`
	Name            string                  `json:"name" binding:"required"`
	NameEn          *string                 `json:"name_en,omitempty"`
	BaseURL         string                  `json:"base_url" binding:"required"`
	Status          TaxFilingProvinceStatus `json:"status"`
	AuthMethod      TaxFilingAuthMethod     `json:"auth_method" binding:"required"`
	Timeout         int                     `json:"timeout"`
	MaxRetries      int                     `json:"max_retries"`
	RetryDelay      float64                 `json:"retry_delay"`
	AuthConfig      AuthConfig              `json:"auth_config" binding:"required"`
	APIEndpoints    APIEndpoints            `json:"api_endpoints" binding:"required"`
	DataMappings    DataMappings            `json:"data_mappings,omitempty"`
	ValidationRules ValidationRules         `json:"validation_rules,omitempty"`
}

// TaxFilingProvinceUpdateRequest 更新省份配置请求
type TaxFilingProvinceUpdateRequest struct {
	Name            *string                  `json:"name,omitempty"`
	NameEn          *string                  `json:"name_en,omitempty"`
	BaseURL         *string                  `json:"base_url,omitempty"`
	Status          *TaxFilingProvinceStatus `json:"status,omitempty"`
	AuthMethod      *TaxFilingAuthMethod     `json:"auth_method,omitempty"`
	Timeout         *int                     `json:"timeout,omitempty"`
	MaxRetries      *int                     `json:"max_retries,omitempty"`
	RetryDelay      *float64                 `json:"retry_delay,omitempty"`
	AuthConfig      AuthConfig               `json:"auth_config,omitempty"`
	APIEndpoints    APIEndpoints             `json:"api_endpoints,omitempty"`
	DataMappings    DataMappings             `json:"data_mappings,omitempty"`
	ValidationRules ValidationRules          `json:"validation_rules,omitempty"`
}

// TaxFilingProvinceResponse 省份配置响应
type TaxFilingProvinceResponse struct {
	TaxFilingProvince
	StatusText        string   `json:"status_text"`
	SuccessRate       float64  `json:"success_rate"`
	FailureRate       float64  `json:"failure_rate"`
	IsHealthy         bool     `json:"is_healthy"`
	SupportedTaxTypes []string `json:"supported_tax_types,omitempty"`
}

// GetStatusText 获取状态文本
func (p *TaxFilingProvinceResponse) GetStatusText() string {
	switch p.Status {
	case TaxFilingProvinceStatusActive:
		return "激活"
	case TaxFilingProvinceStatusInactive:
		return "未激活"
	case TaxFilingProvinceStatusMaintenance:
		return "维护中"
	default:
		return "未知状态"
	}
}

// GetAuthMethodText 获取认证方式文本
func (p *TaxFilingProvinceResponse) GetAuthMethodText() string {
	switch p.AuthMethod {
	case TaxFilingAuthMethodUsernamePassword:
		return "用户名密码"
	case TaxFilingAuthMethodAPIKey:
		return "API密钥"
	case TaxFilingAuthMethodCertificate:
		return "数字证书"
	case TaxFilingAuthMethodOAuth2:
		return "OAuth2"
	default:
		return "未知方式"
	}
}

// TaxFilingProvinceListRequest 省份配置列表请求
type TaxFilingProvinceListRequest struct {
	Status   *TaxFilingProvinceStatus `form:"status"`
	Keyword  string                   `form:"keyword"`
	Page     int                      `form:"page,default=1"`
	PageSize int                      `form:"page_size,default=20"`
	OrderBy  string                   `form:"order_by,default=created_at"`
	Order    string                   `form:"order,default=desc"`
}

// TaxFilingProvinceListResponse 省份配置列表响应
type TaxFilingProvinceListResponse struct {
	List       []TaxFilingProvinceResponse `json:"list"`
	Total      int64                       `json:"total"`
	Page       int                         `json:"page"`
	PageSize   int                         `json:"page_size"`
	TotalPages int                         `json:"total_pages"`
}

// TaxFilingProvinceHealthCheck 省份健康检查结果
type TaxFilingProvinceHealthCheck struct {
	ProvinceCode string    `json:"province_code"`
	ProvinceName string    `json:"province_name"`
	Status       string    `json:"status"`
	ResponseTime *float64  `json:"response_time,omitempty"` // 响应时间(秒)
	LastCheck    time.Time `json:"last_check"`
	ErrorMessage *string   `json:"error_message,omitempty"`
	IsHealthy    bool      `json:"is_healthy"`
}

// TaxFilingProvinceCapabilities 省份功能特性
type TaxFilingProvinceCapabilities struct {
	ProvinceCode      string   `json:"province_code"`
	ProvinceName      string   `json:"province_name"`
	SupportedTaxTypes []string `json:"supported_tax_types"`
	BatchSubmission   bool     `json:"batch_submission"`
	RealTimeStatus    bool     `json:"real_time_status"`
	FileUpload        bool     `json:"file_upload"`
	DigitalSignature  bool     `json:"digital_signature"`
	MaxSubmissionSize int      `json:"max_submission_size,omitempty"`
	SupportedFormats  []string `json:"supported_formats,omitempty"`
}

// TaxFilingProvinceQuery 省份查询参数
type TaxFilingProvinceQuery struct {
	Page     int                      `form:"page,default=1"`
	PageSize int                      `form:"page_size,default=20"`
	Status   *TaxFilingProvinceStatus `form:"status"`
	Keyword  string                   `form:"keyword"`
	OrderBy  string                   `form:"order_by,default=created_at"`
	Order    string                   `form:"order,default=desc"`
}

// ProvinceStatusUpdateRequest 省份状态更新请求
type ProvinceStatusUpdateRequest struct {
	Status TaxFilingProvinceStatus `json:"status" binding:"required"`
	Reason string                  `json:"reason,omitempty"`
}
