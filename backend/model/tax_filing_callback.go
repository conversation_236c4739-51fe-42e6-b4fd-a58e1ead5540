package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// TaxFilingCallbackType 回调类型枚举
type TaxFilingCallbackType string

const (
	TaxFilingCallbackTypeStatusUpdate TaxFilingCallbackType = "status_update"
	TaxFilingCallbackTypeCompletion   TaxFilingCallbackType = "completion"
	TaxFilingCallbackTypeError        TaxFilingCallbackType = "error"
)

// TaxFilingCallbackStatus 回调状态枚举
type TaxFilingCallbackStatus string

const (
	TaxFilingCallbackStatusPending   TaxFilingCallbackStatus = "pending"
	TaxFilingCallbackStatusSent      TaxFilingCallbackStatus = "sent"
	TaxFilingCallbackStatusFailed    TaxFilingCallbackStatus = "failed"
	TaxFilingCallbackStatusCancelled TaxFilingCallbackStatus = "cancelled"
)

// CallbackHeaders 回调头部
type CallbackHeaders map[string]string

// Value 实现 driver.Valuer 接口
func (c CallbackHeaders) Value() (driver.Value, error) {
	if len(c) == 0 {
		return nil, nil
	}
	return json.Marshal(c)
}

// Scan 实现 sql.Scanner 接口
func (c *CallbackHeaders) Scan(value interface{}) error {
	if value == nil {
		*c = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into CallbackHeaders", value)
	}

	return json.Unmarshal(bytes, c)
}

// CallbackPayload 回调载荷
type CallbackPayload map[string]interface{}

// Value 实现 driver.Valuer 接口
func (c CallbackPayload) Value() (driver.Value, error) {
	if len(c) == 0 {
		return nil, nil
	}
	return json.Marshal(c)
}

// Scan 实现 sql.Scanner 接口
func (c *CallbackPayload) Scan(value interface{}) error {
	if value == nil {
		*c = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into CallbackPayload", value)
	}

	return json.Unmarshal(bytes, c)
}

// TaxFilingCallback 税务申报回调模型
type TaxFilingCallback struct {
	ID           string                `gorm:"primaryKey;size:50" json:"id"`
	SubmissionID string                `gorm:"size:50;not null;index" json:"submission_id"`
	CallbackType TaxFilingCallbackType `gorm:"size:20;not null" json:"callback_type"`

	// 回调配置
	CallbackURL     *string         `gorm:"size:500" json:"callback_url,omitempty"`
	CallbackMethod  string          `gorm:"size:10;not null;default:POST" json:"callback_method"`
	CallbackHeaders CallbackHeaders `gorm:"type:json" json:"callback_headers,omitempty"`
	CallbackPayload CallbackPayload `gorm:"type:json" json:"callback_payload,omitempty"`

	// 回调状态
	Status TaxFilingCallbackStatus `gorm:"size:20;not null;default:pending;index" json:"status"`

	// 重试配置
	RetryCount    int        `gorm:"not null;default:0" json:"retry_count"`
	MaxRetries    int        `gorm:"not null;default:3" json:"max_retries"`
	NextRetryAt   *time.Time `gorm:"index:idx_next_retry" json:"next_retry_at,omitempty"`
	LastAttemptAt *time.Time `json:"last_attempt_at,omitempty"`

	// 响应信息
	ResponseStatus *int    `json:"response_status,omitempty"`
	ResponseBody   *string `gorm:"type:text" json:"response_body,omitempty"`
	ErrorMessage   *string `gorm:"type:text" json:"error_message,omitempty"`

	// 时间戳
	CreatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;index" json:"created_at"`
	UpdatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 关联
	Submission *TaxFilingSubmission `gorm:"foreignKey:SubmissionID" json:"submission,omitempty"`
}

// TableName 指定表名
func (TaxFilingCallback) TableName() string {
	return "tax_filing_callbacks"
}

// BeforeCreate GORM钩子 - 创建前
func (c *TaxFilingCallback) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = GenerateID()
	}
	return nil
}

// BeforeUpdate GORM钩子 - 更新前
func (c *TaxFilingCallback) BeforeUpdate(tx *gorm.DB) error {
	c.UpdatedAt = time.Now()
	return nil
}

// CanRetry 检查是否可以重试
func (c *TaxFilingCallback) CanRetry() bool {
	return c.RetryCount < c.MaxRetries &&
		c.Status == TaxFilingCallbackStatusFailed
}

// IsCompleted 检查是否已完成
func (c *TaxFilingCallback) IsCompleted() bool {
	return c.Status == TaxFilingCallbackStatusSent ||
		c.Status == TaxFilingCallbackStatusCancelled ||
		(c.Status == TaxFilingCallbackStatusFailed && !c.CanRetry())
}

// MarkAsSent 标记为已发送
func (c *TaxFilingCallback) MarkAsSent(responseStatus int, responseBody string) {
	c.Status = TaxFilingCallbackStatusSent
	c.ResponseStatus = &responseStatus
	c.ResponseBody = &responseBody
	now := time.Now()
	c.LastAttemptAt = &now
	c.UpdatedAt = now
}

// MarkAsFailed 标记为失败
func (c *TaxFilingCallback) MarkAsFailed(errorMessage string, responseStatus *int, responseBody *string) {
	c.Status = TaxFilingCallbackStatusFailed
	c.ErrorMessage = &errorMessage
	c.ResponseStatus = responseStatus
	c.ResponseBody = responseBody
	c.RetryCount++

	now := time.Now()
	c.LastAttemptAt = &now
	c.UpdatedAt = now

	// 设置下次重试时间
	if c.CanRetry() {
		// 指数退避算法
		retryDelay := time.Duration(c.RetryCount*c.RetryCount) * time.Minute
		nextRetry := now.Add(retryDelay)
		c.NextRetryAt = &nextRetry
	}
}

// TaxFilingCallbackCreateRequest 创建回调请求
type TaxFilingCallbackCreateRequest struct {
	SubmissionID    string                `json:"submission_id" binding:"required"`
	CallbackType    TaxFilingCallbackType `json:"callback_type" binding:"required"`
	CallbackURL     *string               `json:"callback_url,omitempty"`
	CallbackMethod  string                `json:"callback_method"`
	CallbackHeaders CallbackHeaders       `json:"callback_headers,omitempty"`
	CallbackPayload CallbackPayload       `json:"callback_payload,omitempty"`
	MaxRetries      int                   `json:"max_retries"`
}

// TaxFilingCallbackUpdateRequest 更新回调请求
type TaxFilingCallbackUpdateRequest struct {
	Status          *TaxFilingCallbackStatus `json:"status,omitempty"`
	CallbackURL     *string                  `json:"callback_url,omitempty"`
	CallbackHeaders CallbackHeaders          `json:"callback_headers,omitempty"`
	CallbackPayload CallbackPayload          `json:"callback_payload,omitempty"`
	ErrorMessage    *string                  `json:"error_message,omitempty"`
}

// TaxFilingCallbackResponse 回调响应
type TaxFilingCallbackResponse struct {
	TaxFilingCallback
	StatusText      string `json:"status_text"`
	TypeText        string `json:"type_text"`
	CanRetryFlag    bool   `json:"can_retry"`
	IsCompletedFlag bool   `json:"is_completed"`
}

// GetStatusText 获取状态文本
func (c *TaxFilingCallbackResponse) GetStatusText() string {
	switch c.Status {
	case TaxFilingCallbackStatusPending:
		return "待发送"
	case TaxFilingCallbackStatusSent:
		return "已发送"
	case TaxFilingCallbackStatusFailed:
		return "失败"
	case TaxFilingCallbackStatusCancelled:
		return "已取消"
	default:
		return "未知状态"
	}
}

// GetTypeText 获取类型文本
func (c *TaxFilingCallbackResponse) GetTypeText() string {
	switch c.CallbackType {
	case TaxFilingCallbackTypeStatusUpdate:
		return "状态更新"
	case TaxFilingCallbackTypeCompletion:
		return "完成通知"
	case TaxFilingCallbackTypeError:
		return "错误通知"
	default:
		return "未知类型"
	}
}

// TaxFilingCallbackListRequest 回调列表请求
type TaxFilingCallbackListRequest struct {
	SubmissionID string                   `form:"submission_id"`
	CallbackType *TaxFilingCallbackType   `form:"callback_type"`
	Status       *TaxFilingCallbackStatus `form:"status"`
	StartTime    *time.Time               `form:"start_time"`
	EndTime      *time.Time               `form:"end_time"`
	Page         int                      `form:"page,default=1"`
	PageSize     int                      `form:"page_size,default=20"`
	OrderBy      string                   `form:"order_by,default=created_at"`
	Order        string                   `form:"order,default=desc"`
}

// TaxFilingCallbackListResponse 回调列表响应
type TaxFilingCallbackListResponse struct {
	List       []TaxFilingCallbackResponse `json:"list"`
	Total      int64                       `json:"total"`
	Page       int                         `json:"page"`
	PageSize   int                         `json:"page_size"`
	TotalPages int                         `json:"total_pages"`
}

// TaxFilingCallbackStatistics 回调统计
type TaxFilingCallbackStatistics struct {
	TotalCallbacks   int64   `json:"total_callbacks"`
	SentCallbacks    int64   `json:"sent_callbacks"`
	FailedCallbacks  int64   `json:"failed_callbacks"`
	PendingCallbacks int64   `json:"pending_callbacks"`
	SuccessRate      float64 `json:"success_rate"`
	AverageRetries   float64 `json:"average_retries"`
}

// CreateStatusUpdateCallback 创建状态更新回调
func CreateStatusUpdateCallback(
	submissionID string,
	callbackURL string,
	payload CallbackPayload,
) *TaxFilingCallback {
	return &TaxFilingCallback{
		ID:             GenerateID(),
		SubmissionID:   submissionID,
		CallbackType:   TaxFilingCallbackTypeStatusUpdate,
		CallbackURL:    &callbackURL,
		CallbackMethod: "POST",
		CallbackHeaders: CallbackHeaders{
			"Content-Type": "application/json",
		},
		CallbackPayload: payload,
		Status:          TaxFilingCallbackStatusPending,
		MaxRetries:      3,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
}

// CreateCompletionCallback 创建完成通知回调
func CreateCompletionCallback(
	submissionID string,
	callbackURL string,
	payload CallbackPayload,
) *TaxFilingCallback {
	return &TaxFilingCallback{
		ID:             GenerateID(),
		SubmissionID:   submissionID,
		CallbackType:   TaxFilingCallbackTypeCompletion,
		CallbackURL:    &callbackURL,
		CallbackMethod: "POST",
		CallbackHeaders: CallbackHeaders{
			"Content-Type": "application/json",
		},
		CallbackPayload: payload,
		Status:          TaxFilingCallbackStatusPending,
		MaxRetries:      3,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
}

// CreateErrorCallback 创建错误通知回调
func CreateErrorCallback(
	submissionID string,
	callbackURL string,
	payload CallbackPayload,
) *TaxFilingCallback {
	return &TaxFilingCallback{
		ID:             GenerateID(),
		SubmissionID:   submissionID,
		CallbackType:   TaxFilingCallbackTypeError,
		CallbackURL:    &callbackURL,
		CallbackMethod: "POST",
		CallbackHeaders: CallbackHeaders{
			"Content-Type": "application/json",
		},
		CallbackPayload: payload,
		Status:          TaxFilingCallbackStatusPending,
		MaxRetries:      3,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
}
