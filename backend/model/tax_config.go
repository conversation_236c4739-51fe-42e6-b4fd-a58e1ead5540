// Package model provides data models for tax configuration management.
// It includes tax rules, tax types, and system configuration structures.
package model

import (
	"time"

	"gorm.io/gorm"
)

// TaxConfig 税务配置模型
type TaxConfig struct {
	ID          string `gorm:"primaryKey;size:50" json:"id"`
	Name        string `gorm:"size:100;not null" json:"name"`
	Description string `gorm:"type:text" json:"description"`
	ConfigType  string `gorm:"size:50;not null;index" json:"config_type"`
	ConfigKey   string `gorm:"size:100;not null;uniqueIndex" json:"config_key"`
	ConfigValue string `gorm:"type:text;not null" json:"config_value"`
	IsActive    bool   `gorm:"not null;default:true" json:"is_active"`
	
	// 系统字段
	CreatedBy *string    `gorm:"size:50" json:"created_by,omitempty"`
	UpdatedBy *string    `gorm:"size:50" json:"updated_by,omitempty"`
	CreatedAt time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (TaxConfig) TableName() string {
	return "tax_configs"
}

// SystemConfig 系统配置模型
type SystemConfig struct {
	ID          string `gorm:"primaryKey;size:50" json:"id"`
	Module      string `gorm:"size:50;not null;index" json:"module"`
	ConfigKey   string `gorm:"size:100;not null" json:"config_key"`
	ConfigValue string `gorm:"type:text;not null" json:"config_value"`
	DataType    string `gorm:"size:20;not null;default:string" json:"data_type"`
	Description string `gorm:"type:text" json:"description"`
	IsSystem    bool   `gorm:"not null;default:false" json:"is_system"`
	IsActive    bool   `gorm:"not null;default:true" json:"is_active"`
	
	// 系统字段
	CreatedBy *string    `gorm:"size:50" json:"created_by,omitempty"`
	UpdatedBy *string    `gorm:"size:50" json:"updated_by,omitempty"`
	CreatedAt time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (SystemConfig) TableName() string {
	return "system_configs"
}

// 添加唯一索引
func (SystemConfig) BeforeCreate(tx *gorm.DB) error {
	// 确保 module + config_key 的组合是唯一的
	return nil
}
