package model

import "time"

// Role 角色模型
type Role struct {
	ID          string    `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:角色ID"`
	Code        string    `json:"code" gorm:"type:varchar(50);not null;uniqueIndex:uk_roles_code;column:role_code;comment:角色编码"`
	Name        string    `json:"name" gorm:"type:varchar(100);not null;column:role_name;comment:角色名称"`
	Description string    `json:"description" gorm:"type:varchar(500);column:description;comment:角色描述"`
	Type        string    `json:"type" gorm:"type:enum('system','business','custom');default:custom;column:role_type;index:idx_roles_type;comment:角色类型"`
	Level       int       `json:"level" gorm:"default:0;column:role_level;index:idx_roles_level;comment:角色级别"`
	IsActive    bool      `json:"is_active" gorm:"default:true;column:is_active;comment:是否启用"`
	CreatedAt   time.Time `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:role_permissions;foreignKey:ID;joinForeignKey:RoleID;References:ID;joinReferences:PermissionID"`
}

// TableName 指定表名
func (Role) TableName() string {
	return "roles"
}

// Permission 权限模型
type Permission struct {
	ID          string    `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:权限ID"`
	Code        string    `json:"code" gorm:"type:varchar(100);not null;uniqueIndex:uk_permissions_code;column:permission_code;comment:权限编码"`
	Name        string    `json:"name" gorm:"type:varchar(100);not null;column:permission_name;comment:权限名称"`
	Description string    `json:"description" gorm:"type:varchar(500);column:description;comment:权限描述"`
	Module      string    `json:"module" gorm:"type:varchar(50);not null;column:module;index:idx_permissions_module;comment:模块"`
	Resource    string    `json:"resource" gorm:"type:varchar(50);not null;column:resource;comment:资源"`
	Action      string    `json:"action" gorm:"type:varchar(50);not null;column:action;comment:操作"`
	Type        string    `json:"type" gorm:"type:enum('menu','function','data');default:function;column:permission_type;comment:权限类型"`
	ParentID    *string   `json:"parent_id" gorm:"type:varchar(36);column:parent_id;index:idx_permissions_parent_id;comment:父权限ID"`
	SortOrder   int       `json:"sort_order" gorm:"default:0;column:sort_order;comment:排序"`
	IsSystem    bool      `json:"is_system" gorm:"default:false;column:is_system;comment:是否系统权限"`
	IsActive    bool      `json:"is_active" gorm:"default:true;column:is_active;comment:是否启用"`
	CreatedAt   time.Time `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	Parent   *Permission  `json:"parent,omitempty" gorm:"foreignKey:ParentID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Children []Permission `json:"children,omitempty" gorm:"foreignKey:ParentID;references:ID"`
	Roles    []Role       `json:"roles,omitempty" gorm:"many2many:role_permissions;foreignKey:ID;joinForeignKey:PermissionID;References:ID;joinReferences:RoleID"`
}

// TableName 指定表名
func (Permission) TableName() string {
	return "permissions"
}

// RolePermission 角色权限关联模型
type RolePermission struct {
	ID           string    `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:关联ID"`
	RoleID       string    `json:"role_id" gorm:"type:varchar(36);not null;column:role_id;index:idx_role_permissions_role_id;comment:角色ID"`
	PermissionID string    `json:"permission_id" gorm:"type:varchar(36);not null;column:permission_id;index:idx_role_permissions_permission_id;comment:权限ID"`
	CreatedAt    time.Time `json:"created_at" gorm:"column:created_at;comment:创建时间"`

	// 关联关系
	Role       *Role       `json:"role,omitempty" gorm:"foreignKey:RoleID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	Permission *Permission `json:"permission,omitempty" gorm:"foreignKey:PermissionID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
}

// TableName 指定表名
func (RolePermission) TableName() string {
	return "role_permissions"
}

// 角色类型常量
const (
	RoleTypeSystem   = "system"
	RoleTypeBusiness = "business"
	RoleTypeCustom   = "custom"
)

// 权限类型常量
const (
	PermissionTypeMenu     = "menu"
	PermissionTypeFunction = "function"
	PermissionTypeData     = "data"
)

// 常用角色编码常量
const (
	RoleCodeSuperAdmin      = "super_admin"
	RoleCodeAdmin           = "admin"
	RoleCodeEnterpriseAdmin = "enterprise_admin"
	RoleCodeAccountant      = "accountant"
	RoleCodeOperator        = "operator"
	RoleCodeAuditor         = "auditor"
	RoleCodeViewer          = "viewer"
)

// 常用权限编码常量
const (
	PermissionCodeSystemManage      = "system.manage"
	PermissionCodeUserManage        = "user.manage"
	PermissionCodeEnterpriseManage  = "enterprise.manage"
	PermissionCodeInvoiceManage     = "invoice.manage"
	PermissionCodeDeclarationManage = "declaration.manage"
	PermissionCodePaymentManage     = "payment.manage"
	PermissionCodeReportView        = "report.view"
	PermissionCodeAuditView         = "audit.view"
	PermissionCodeTaxPolicyView     = "tax_policy.view"
	PermissionCodeRiskAssessment    = "risk_assessment.manage"
)

// 权限模块常量
const (
	PermissionModuleSystem      = "system"
	PermissionModuleUser        = "user"
	PermissionModuleEnterprise  = "enterprise"
	PermissionModuleInvoice     = "invoice"
	PermissionModuleDeclaration = "declaration"
	PermissionModulePayment     = "payment"
	PermissionModuleReport      = "report"
	PermissionModuleAudit       = "audit"
	PermissionModuleTaxPolicy   = "tax_policy"
	PermissionModuleRisk        = "risk"
)

// 权限操作常量
const (
	PermissionActionCreate  = "create"
	PermissionActionRead    = "read"
	PermissionActionUpdate  = "update"
	PermissionActionDelete  = "delete"
	PermissionActionManage  = "manage"
	PermissionActionView    = "view"
	PermissionActionExport  = "export"
	PermissionActionImport  = "import"
	PermissionActionApprove = "approve"
	PermissionActionReject  = "reject"
)
