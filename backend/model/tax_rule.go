package model

import (
	"encoding/json"
	"time"
)

// TaxRule 表示特定税种的计税规则
type TaxRule struct {
	ID            string     `json:"id" gorm:"primaryKey;size:36;comment:主键ID"`
	TaxTypeID     string     `json:"taxTypeId" gorm:"not null;size:36;index:idx_tax_rules_tax_type;comment:税种ID"`
	Name          string     `json:"name" gorm:"not null;size:255;comment:规则名称"`
	Description   *string    `json:"description" gorm:"type:text;comment:规则描述"`
	Condition     *string    `json:"condition" gorm:"type:text;comment:规则适用条件"`
	Formula       *string    `json:"formula" gorm:"type:text;comment:计算公式"`
	EffectiveDate time.Time  `json:"effectiveDate" gorm:"not null;comment:生效日期"`
	ExpiryDate    *time.Time `json:"expiryDate" gorm:"comment:失效日期"`
	Parameters    *string    `json:"parameters" gorm:"type:json;comment:额外配置参数（JSON格式）"`
	CreatedAt     time.Time  `json:"createdAt" gorm:"not null;comment:创建时间"`
	UpdatedAt     time.Time  `json:"updatedAt" gorm:"not null;comment:更新时间"`

	// 关联关系
	TaxType *TaxType `json:"taxType,omitempty" gorm:"foreignKey:TaxTypeID;references:ID"`
}

// GetParameters 返回规则参数作为映射
func (r *TaxRule) GetParameters() (map[string]interface{}, error) {
	if r.Parameters == nil || *r.Parameters == "" {
		return map[string]interface{}{}, nil
	}

	var params map[string]interface{}
	if err := json.Unmarshal([]byte(*r.Parameters), &params); err != nil {
		return nil, err
	}

	return params, nil
}

// SetParameters 从映射设置规则参数
func (r *TaxRule) SetParameters(params map[string]interface{}) error {
	data, err := json.Marshal(params)
	if err != nil {
		return err
	}
	strData := string(data)
	r.Parameters = &strData
	return nil
}
