package model

import (
	"time"
)

// FileStorage 表示文件存储信息
type FileStorage struct {
	ID            string     `json:"id" gorm:"primaryKey"`                     // 文件ID，主键
	OriginalName  string     `json:"originalName" gorm:"size:255;not null"`    // 原始文件名
	StoredName    string     `json:"storedName" gorm:"size:255;not null"`      // 存储文件名
	FilePath      string     `json:"filePath" gorm:"size:500;not null"`        // 文件路径
	FileSize      int64      `json:"fileSize" gorm:"not null"`                 // 文件大小（字节）
	MimeType      string     `json:"mimeType" gorm:"size:100;not null"`        // MIME类型
	MD5Hash       string     `json:"md5Hash" gorm:"size:32;not null;index"`    // MD5哈希值
	UploadedBy    string     `json:"uploadedBy" gorm:"size:36;not null;index"` // 上传用户ID
	EnterpriseID  string     `json:"enterpriseId" gorm:"size:36;index"`        // 关联企业ID
	BusinessType  string     `json:"businessType" gorm:"size:50"`              // 业务类型
	BusinessID    string     `json:"businessId" gorm:"size:36"`                // 业务ID
	IsPublic      bool       `json:"isPublic" gorm:"default:false"`            // 是否公开
	DownloadCount int        `json:"downloadCount" gorm:"default:0"`           // 下载次数
	CreatedAt     time.Time  `json:"createdAt" gorm:"index"`                   // 创建时间
	UpdatedAt     time.Time  `json:"updatedAt"`                                // 更新时间
	DeletedAt     *time.Time `json:"deletedAt,omitempty" gorm:"index"`         // 删除时间
}

// FileUploadRequest 表示文件上传请求
type FileUploadRequest struct {
	BusinessType string `json:"businessType" binding:"required"` // 业务类型
	BusinessID   string `json:"businessId"`                      // 业务ID
	EnterpriseID string `json:"enterpriseId"`                    // 企业ID
	IsPublic     bool   `json:"isPublic"`                        // 是否公开
}

// FileUploadResponse 表示文件上传响应
type FileUploadResponse struct {
	ID           string `json:"id"`           // 文件ID
	OriginalName string `json:"originalName"` // 原始文件名
	FileSize     int64  `json:"fileSize"`     // 文件大小
	MimeType     string `json:"mimeType"`     // MIME类型
	DownloadURL  string `json:"downloadUrl"`  // 下载链接
}

// FileListRequest 表示文件列表请求
type FileListRequest struct {
	BusinessType string `json:"businessType" form:"businessType"`                 // 业务类型
	BusinessID   string `json:"businessId" form:"businessId"`                     // 业务ID
	EnterpriseID string `json:"enterpriseId" form:"enterpriseId"`                 // 企业ID
	MimeType     string `json:"mimeType" form:"mimeType"`                         // MIME类型
	Keyword      string `json:"keyword" form:"keyword"`                           // 关键词搜索
	Page         int    `json:"page" form:"page" binding:"min=1"`                 // 页码
	PageSize     int    `json:"pageSize" form:"pageSize" binding:"min=1,max=100"` // 每页大小
}

// FileListResponse 表示文件列表响应
type FileListResponse struct {
	Files      []FileInfo `json:"files"`      // 文件列表
	Total      int64      `json:"total"`      // 总数
	Page       int        `json:"page"`       // 当前页
	PageSize   int        `json:"pageSize"`   // 每页大小
	TotalPages int        `json:"totalPages"` // 总页数
}

// FileInfo 表示文件信息
type FileInfo struct {
	ID            string    `json:"id"`            // 文件ID
	OriginalName  string    `json:"originalName"`  // 原始文件名
	FileSize      int64     `json:"fileSize"`      // 文件大小
	MimeType      string    `json:"mimeType"`      // MIME类型
	UploadedBy    string    `json:"uploadedBy"`    // 上传用户ID
	UploaderName  string    `json:"uploaderName"`  // 上传用户名称
	EnterpriseID  string    `json:"enterpriseId"`  // 企业ID
	BusinessType  string    `json:"businessType"`  // 业务类型
	BusinessID    string    `json:"businessId"`    // 业务ID
	IsPublic      bool      `json:"isPublic"`      // 是否公开
	DownloadCount int       `json:"downloadCount"` // 下载次数
	DownloadURL   string    `json:"downloadUrl"`   // 下载链接
	CreatedAt     time.Time `json:"createdAt"`     // 创建时间
}

// DictionaryCreateRequest 表示创建字典请求
type DictionaryCreateRequest struct {
	Category    string                 `json:"category" binding:"required"` // 字典分类
	Code        string                 `json:"code" binding:"required"`     // 字典代码
	Name        string                 `json:"name" binding:"required"`     // 字典名称
	Value       string                 `json:"value"`                       // 字典值
	ParentCode  string                 `json:"parentCode"`                  // 父级代码
	SortOrder   int                    `json:"sortOrder"`                   // 排序序号
	Description string                 `json:"description"`                 // 描述
	ExtraData   map[string]interface{} `json:"extraData"`                   // 扩展数据
}

// DictionaryUpdateRequest 表示更新字典请求
type DictionaryUpdateRequest struct {
	Name        string                 `json:"name"`        // 字典名称
	Value       string                 `json:"value"`       // 字典值
	SortOrder   int                    `json:"sortOrder"`   // 排序序号
	IsActive    bool                   `json:"isActive"`    // 是否启用
	Description string                 `json:"description"` // 描述
	ExtraData   map[string]interface{} `json:"extraData"`   // 扩展数据
}

// DictionaryTreeNode 表示字典树节点
type DictionaryTreeNode struct {
	ID          string                `json:"id"`          // 字典ID
	Category    string                `json:"category"`    // 字典分类
	Code        string                `json:"code"`        // 字典代码
	Name        string                `json:"name"`        // 字典名称
	Value       string                `json:"value"`       // 字典值
	ParentCode  string                `json:"parentCode"`  // 父级代码
	SortOrder   int                   `json:"sortOrder"`   // 排序序号
	IsActive    bool                  `json:"isActive"`    // 是否启用
	Description string                `json:"description"` // 描述
	Children    []*DictionaryTreeNode `json:"children"`    // 子节点
}

// OperationLog 表示详细操作日志
type OperationLog struct {
	ID             string                 `json:"id" gorm:"primaryKey"`                 // 日志ID，主键
	UserID         string                 `json:"userId" gorm:"size:36;not null;index"` // 操作用户ID
	UserName       string                 `json:"userName" gorm:"size:100"`             // 用户名称
	EnterpriseID   string                 `json:"enterpriseId" gorm:"size:36;index"`    // 企业ID
	Module         string                 `json:"module" gorm:"size:50;not null;index"` // 操作模块
	Operation      string                 `json:"operation" gorm:"size:50;not null"`    // 操作类型
	ResourceType   string                 `json:"resourceType" gorm:"size:50;not null"` // 资源类型
	ResourceID     string                 `json:"resourceId" gorm:"size:36;not null"`   // 资源ID
	RequestMethod  string                 `json:"requestMethod" gorm:"size:10"`         // 请求方法
	RequestURL     string                 `json:"requestUrl" gorm:"size:500"`           // 请求URL
	RequestParams  map[string]interface{} `json:"requestParams" gorm:"type:json"`       // 请求参数
	ResponseStatus int                    `json:"responseStatus"`                       // 响应状态码
	ResponseMsg    string                 `json:"responseMessage" gorm:"type:text"`     // 响应消息
	IPAddress      string                 `json:"ipAddress" gorm:"size:45"`             // IP地址
	UserAgent      string                 `json:"userAgent" gorm:"size:500"`            // 用户代理
	ExecutionTime  int                    `json:"executionTime"`                        // 执行时间（毫秒）
	ErrorMessage   string                 `json:"errorMessage" gorm:"type:text"`        // 错误信息
	CreatedAt      time.Time              `json:"createdAt" gorm:"index"`               // 创建时间
}

// OperationLogFilter 表示操作日志过滤条件
type OperationLogFilter struct {
	UserID       string    `json:"userId"`                           // 用户ID
	EnterpriseID string    `json:"enterpriseId"`                     // 企业ID
	Module       string    `json:"module"`                           // 操作模块
	Operation    string    `json:"operation"`                        // 操作类型
	ResourceType string    `json:"resourceType"`                     // 资源类型
	ResourceID   string    `json:"resourceId"`                       // 资源ID
	StartDate    time.Time `json:"startDate"`                        // 开始日期
	EndDate      time.Time `json:"endDate"`                          // 结束日期
	IPAddress    string    `json:"ipAddress"`                        // IP地址
	Page         int       `json:"page" binding:"min=1"`             // 页码
	PageSize     int       `json:"pageSize" binding:"min=1,max=100"` // 每页大小
}

// OperationLogListResponse 表示操作日志列表响应
type OperationLogListResponse struct {
	Logs       []OperationLog `json:"logs"`       // 日志列表
	Total      int64          `json:"total"`      // 总数
	Page       int            `json:"page"`       // 当前页
	PageSize   int            `json:"pageSize"`   // 每页大小
	TotalPages int            `json:"totalPages"` // 总页数
}
