package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// Invoice 发票模型
type Invoice struct {
	ID                   string          `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:发票ID"`
	EnterpriseID         string          `json:"enterpriseId" gorm:"type:varchar(36);not null;column:enterprise_id;index:idx_invoices_enterprise_id;comment:企业ID"`
	InvoiceNumber        string          `json:"invoiceNumber" gorm:"type:varchar(50);not null;column:invoice_number;comment:发票号码"`
	InvoiceCode          string          `json:"invoiceCode" gorm:"type:varchar(20);not null;column:invoice_code;comment:发票代码"`
	Type                 string          `json:"type" gorm:"type:enum('special','ordinary','electronic','roll');not null;column:invoice_type;index:idx_invoices_type;comment:发票类型"`
	Status               string          `json:"status" gorm:"type:enum('draft','issued','cancelled','invalid','red_flushed');default:draft;column:invoice_status;index:idx_invoices_status;comment:发票状态"`
	AuthenticationStatus string          `json:"authenticationStatus" gorm:"type:enum('pending','authenticated','failed','expired');default:pending;column:authentication_status;index:idx_invoices_auth_status;comment:认证状态"`
	DeclarationStatus    string          `json:"declarationStatus" gorm:"type:enum('undeclared','declared','confirmed');default:undeclared;column:declaration_status;index:idx_invoices_decl_status;comment:申报状态"`
	IssueDate            time.Time       `json:"issueDate" gorm:"not null;column:issue_date;index:idx_invoices_issue_date;comment:开票日期"`
	AuthenticationDate   *time.Time      `json:"authenticationDate" gorm:"column:authentication_date;comment:认证日期"`
	DeclarationPeriod    string          `json:"declarationPeriod" gorm:"type:varchar(10);column:declaration_period;index:idx_invoices_decl_period;comment:申报期间(YYYY-MM)"`
	BuyerName            string          `json:"buyerName" gorm:"type:varchar(255);not null;column:buyer_name;comment:购买方名称"`
	BuyerTaxNumber       string          `json:"buyerTaxNumber" gorm:"type:varchar(20);column:buyer_tax_number;index:idx_invoices_buyer_tax_number;comment:购买方纳税人识别号"`
	BuyerAddress         string          `json:"buyerAddress" gorm:"type:varchar(500);column:buyer_address;comment:购买方地址电话"`
	BuyerBankAccount     string          `json:"buyerBankAccount" gorm:"type:varchar(200);column:buyer_bank_account;comment:购买方开户行及账号"`
	SellerName           string          `json:"sellerName" gorm:"type:varchar(255);not null;column:seller_name;comment:销售方名称"`
	SellerTaxNumber      string          `json:"sellerTaxNumber" gorm:"type:varchar(20);not null;column:seller_tax_number;comment:销售方纳税人识别号"`
	SellerAddress        string          `json:"sellerAddress" gorm:"type:varchar(500);column:seller_address;comment:销售方地址电话"`
	SellerBankAccount    string          `json:"sellerBankAccount" gorm:"type:varchar(200);column:seller_bank_account;comment:销售方开户行及账号"`
	TotalAmount          decimal.Decimal `json:"totalAmount" gorm:"type:decimal(15,2);not null;default:0;column:total_amount;comment:合计金额"`
	TotalTax             decimal.Decimal `json:"taxAmount" gorm:"type:decimal(15,2);not null;default:0;column:total_tax;comment:合计税额"`
	TotalAmountWithTax   decimal.Decimal `json:"totalAmountWithTax" gorm:"type:decimal(15,2);not null;default:0;column:total_amount_with_tax;comment:价税合计"`
	Remarks              string          `json:"remarks" gorm:"type:text;column:remarks;comment:备注"`
	Drawer               string          `json:"drawer" gorm:"type:varchar(100);column:drawer;comment:开票人"`
	Reviewer             string          `json:"reviewer" gorm:"type:varchar(100);column:reviewer;comment:复核人"`
	Payee                string          `json:"payee" gorm:"type:varchar(100);column:payee;comment:收款人"`
	MachineNumber        string          `json:"machine_number" gorm:"type:varchar(50);column:machine_number;comment:机器编号"`
	OriginalInvoiceID    *string         `json:"original_invoice_id" gorm:"type:varchar(36);column:original_invoice_id;index:idx_invoices_original_invoice_id;comment:原发票ID(红冲时使用)"`
	RedFlushReason       string          `json:"red_flush_reason" gorm:"type:varchar(500);column:red_flush_reason;comment:红冲原因"`
	VerificationCode     string          `json:"verification_code" gorm:"type:varchar(50);column:verification_code;comment:校验码"`
	QRCode               string          `json:"qr_code" gorm:"type:text;column:qr_code;comment:二维码内容"`
	PrintTimes           int             `json:"print_times" gorm:"default:0;column:print_times;comment:打印次数"`
	LastPrintTime        *time.Time      `json:"last_print_time" gorm:"column:last_print_time;comment:最后打印时间"`
	SubmitTime           *time.Time      `json:"submit_time" gorm:"column:submit_time;comment:提交时间"`
	ApprovalTime         *time.Time      `json:"approval_time" gorm:"column:approval_time;comment:审批时间"`
	CancelTime           *time.Time      `json:"cancel_time" gorm:"column:cancel_time;comment:作废时间"`
	CancelReason         string          `json:"cancel_reason" gorm:"type:varchar(500);column:cancel_reason;comment:作废原因"`
	InvoiceSource        string          `json:"invoice_source" gorm:"type:enum('manual','import','api','scan');default:manual;column:invoice_source;comment:发票来源"`
	CurrencyCode         string          `json:"currency_code" gorm:"type:varchar(10);default:'CNY';column:currency_code;comment:币种代码"`
	ExchangeRate         decimal.Decimal `json:"exchange_rate" gorm:"type:decimal(10,6);default:1.000000;column:exchange_rate;comment:汇率"`
	CreatedAt            time.Time       `json:"createdAt" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt            time.Time       `json:"updatedAt" gorm:"column:updated_at;comment:更新时间"`
	DeletedAt            *time.Time      `json:"deletedAt" gorm:"column:deleted_at;index:idx_invoices_deleted_at;comment:删除时间"`

	// 关联关系
	Enterprise      *Enterprise   `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	OriginalInvoice *Invoice      `json:"original_invoice,omitempty" gorm:"foreignKey:OriginalInvoiceID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Items           []InvoiceItem `json:"items,omitempty" gorm:"foreignKey:InvoiceID;references:ID"`
}

// 发票类型常量
const (
	InvoiceTypeSpecial    = "special"
	InvoiceTypeOrdinary   = "ordinary"
	InvoiceTypeElectronic = "electronic"
	InvoiceTypeRoll       = "roll"
)

// 发票状态常量
const (
	InvoiceStatusDraft      = "draft"
	InvoiceStatusIssued     = "issued"
	InvoiceStatusCancelled  = "cancelled"
	InvoiceStatusInvalid    = "invalid"
	InvoiceStatusRedFlushed = "red_flushed"
)

// 发票认证状态常量
const (
	AuthStatusPending       = "pending"
	AuthStatusAuthenticated = "authenticated"
	AuthStatusFailed        = "failed"
	AuthStatusExpired       = "expired"
)

// 发票申报状态常量
const (
	DeclStatusUndeclared = "undeclared"
	DeclStatusDeclared   = "declared"
	DeclStatusConfirmed  = "confirmed"
)

// 发票来源常量
const (
	InvoiceSourceManual = "manual"
	InvoiceSourceImport = "import"
	InvoiceSourceAPI    = "api"
	InvoiceSourceScan   = "scan"
)
