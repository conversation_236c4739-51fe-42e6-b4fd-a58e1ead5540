package model

import (
	"time"
)

// TaxPolicy 税务政策管理模型
type TaxPolicy struct {
	ID                     string     `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:政策ID"`
	PolicyNumber           string     `json:"policy_number" gorm:"type:varchar(50);not null;uniqueIndex:uk_tax_policies_number;column:policy_number;comment:政策编号"`
	Title                  string     `json:"title" gorm:"type:varchar(255);not null;column:title;comment:政策标题"`
	Category               string     `json:"category" gorm:"type:enum('tax_rate','exemption','preferential','procedure','penalty','other');not null;column:category;index:idx_tax_policies_category;comment:政策分类"`
	TaxTypes               string     `json:"tax_types" gorm:"type:json;column:tax_types;comment:适用税种"`
	ApplicableRegions      string     `json:"applicable_regions" gorm:"type:json;column:applicable_regions;comment:适用地区"`
	ApplicableIndustries   string     `json:"applicable_industries" gorm:"type:json;column:applicable_industries;comment:适用行业"`
	ApplicableEnterprises  string     `json:"applicable_enterprises" gorm:"type:json;column:applicable_enterprises;comment:适用企业类型"`
	PolicyContent          string     `json:"policy_content" gorm:"type:text;not null;column:policy_content;comment:政策内容"`
	PolicySummary          string     `json:"policy_summary" gorm:"type:varchar(1000);column:policy_summary;comment:政策摘要"`
	LegalBasis             string     `json:"legal_basis" gorm:"type:varchar(500);column:legal_basis;comment:法律依据"`
	IssuingAuthority       string     `json:"issuing_authority" gorm:"type:varchar(100);not null;column:issuing_authority;index:idx_tax_policies_issuing_authority;comment:发布机关"`
	IssueDate              time.Time  `json:"issue_date" gorm:"not null;column:issue_date;comment:发布日期"`
	EffectiveDate          time.Time  `json:"effective_date" gorm:"not null;column:effective_date;index:idx_tax_policies_effective_date;comment:生效日期"`
	ExpiryDate             *time.Time `json:"expiry_date" gorm:"column:expiry_date;comment:失效日期"`
	Status                 string     `json:"status" gorm:"type:enum('draft','published','effective','expired','revoked');default:draft;column:status;index:idx_tax_policies_status;comment:状态"`
	PriorityLevel          string     `json:"priority_level" gorm:"type:enum('low','normal','high','urgent');default:normal;column:priority_level;comment:优先级"`
	ImpactAssessment       string     `json:"impact_assessment" gorm:"type:text;column:impact_assessment;comment:影响评估"`
	ImplementationGuidance string     `json:"implementation_guidance" gorm:"type:text;column:implementation_guidance;comment:执行指导"`
	RelatedPolicies        string     `json:"related_policies" gorm:"type:json;column:related_policies;comment:相关政策"`
	Attachments            string     `json:"attachments" gorm:"type:json;column:attachments;comment:附件信息"`
	Keywords               string     `json:"keywords" gorm:"type:varchar(500);column:keywords;comment:关键词"`
	ViewCount              int        `json:"view_count" gorm:"default:0;column:view_count;comment:查看次数"`
	CreatedBy              string     `json:"created_by" gorm:"type:varchar(36);not null;column:created_by;index:idx_tax_policies_created_by;comment:创建人ID"`
	CreatedAt              time.Time  `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt              time.Time  `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	Creator *User `json:"creator,omitempty" gorm:"foreignKey:CreatedBy;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
}

// 政策分类常量
const (
	PolicyCategoryTaxRate      = "tax_rate"
	PolicyCategoryExemption    = "exemption"
	PolicyCategoryPreferential = "preferential"
	PolicyCategoryProcedure    = "procedure"
	PolicyCategoryPenalty      = "penalty"
	PolicyCategoryOther        = "other"
)

// 政策状态常量
const (
	PolicyStatusDraft     = "draft"
	PolicyStatusPublished = "published"
	PolicyStatusEffective = "effective"
	PolicyStatusExpired   = "expired"
	PolicyStatusRevoked   = "revoked"
)

// 优先级常量
const (
	PolicyPriorityLow    = "low"
	PolicyPriorityNormal = "normal"
	PolicyPriorityHigh   = "high"
	PolicyPriorityUrgent = "urgent"
)

// TaxPolicyCreateRequest 创建税务政策请求
type TaxPolicyCreateRequest struct {
	PolicyNumber     string `json:"policy_number" binding:"required"`
	Title            string `json:"title" binding:"required"`
	Category         string `json:"category" binding:"required"`
	TaxTypeID        string `json:"tax_type_id"`
	Content          string `json:"content" binding:"required"`
	Summary          string `json:"summary"`
	EffectiveDate    string `json:"effective_date" binding:"required"`
	ExpiryDate       string `json:"expiry_date"`
	Priority         string `json:"priority"`
	IssuingAuthority string `json:"issuing_authority" binding:"required"`
	DocumentURL      string `json:"document_url"`
	Tags             string `json:"tags"`
}

// TaxPolicyUpdateRequest 更新税务政策请求
type TaxPolicyUpdateRequest struct {
	PolicyNumber     *string `json:"policy_number"`
	Title            *string `json:"title"`
	Category         *string `json:"category"`
	TaxTypeID        *string `json:"tax_type_id"`
	Content          *string `json:"content"`
	Summary          *string `json:"summary"`
	EffectiveDate    *string `json:"effective_date"`
	ExpiryDate       *string `json:"expiry_date"`
	Status           *string `json:"status"`
	Priority         *string `json:"priority"`
	IssuingAuthority *string `json:"issuing_authority"`
	DocumentURL      *string `json:"document_url"`
	Tags             *string `json:"tags"`
}

// TaxPolicyResponse 税务政策响应
type TaxPolicyResponse struct {
	ID               string     `json:"id"`
	PolicyNumber     string     `json:"policy_number"`
	Title            string     `json:"title"`
	Category         string     `json:"category"`
	TaxTypeID        *string    `json:"tax_type_id"`
	TaxTypeName      string     `json:"tax_type_name"`
	Content          string     `json:"content"`
	Summary          string     `json:"summary"`
	EffectiveDate    time.Time  `json:"effective_date"`
	ExpiryDate       *time.Time `json:"expiry_date"`
	Status           string     `json:"status"`
	Priority         string     `json:"priority"`
	IssuingAuthority string     `json:"issuing_authority"`
	DocumentURL      string     `json:"document_url"`
	Tags             string     `json:"tags"`
	ViewCount        int        `json:"view_count"`
	PublishedBy      string     `json:"published_by"`
	PublishedAt      *time.Time `json:"published_at"`
	LastModifiedBy   string     `json:"last_modified_by"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
}

// TaxPolicyListResponse 税务政策列表响应
type TaxPolicyListResponse struct {
	ID               string     `json:"id"`
	PolicyNumber     string     `json:"policy_number"`
	Title            string     `json:"title"`
	Category         string     `json:"category"`
	TaxTypeName      string     `json:"tax_type_name"`
	Summary          string     `json:"summary"`
	EffectiveDate    time.Time  `json:"effective_date"`
	ExpiryDate       *time.Time `json:"expiry_date"`
	Status           string     `json:"status"`
	Priority         string     `json:"priority"`
	IssuingAuthority string     `json:"issuing_authority"`
	ViewCount        int        `json:"view_count"`
	PublishedAt      *time.Time `json:"published_at"`
	CreatedAt        time.Time  `json:"created_at"`
}
