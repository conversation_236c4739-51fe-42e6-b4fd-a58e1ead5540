package model

import (
	"time"
)

// ImportTask 数据导入任务模型
type ImportTask struct {
	ID               string     `json:"id" gorm:"primaryKey;size:36;comment:主键ID"`
	Name             string     `json:"name" gorm:"not null;size:200;comment:任务名称"`
	Type             string     `json:"type" gorm:"not null;size:50;index:idx_import_tasks_type;comment:导入类型"`
	Format           string     `json:"format" gorm:"not null;size:20;comment:文件格式"`
	Status           string     `json:"status" gorm:"not null;size:20;index:idx_import_tasks_status;comment:任务状态"`
	Progress         int        `json:"progress" gorm:"default:0;comment:进度百分比"`
	TotalRecords     int64      `json:"totalRecords" gorm:"default:0;comment:总记录数"`
	ProcessedRecords int64      `json:"processedRecords" gorm:"default:0;comment:已处理记录数"`
	SuccessRecords   int64      `json:"successRecords" gorm:"default:0;comment:成功记录数"`
	FailedRecords    int64      `json:"failedRecords" gorm:"default:0;comment:失败记录数"`
	SkippedRecords   int64      `json:"skippedRecords" gorm:"default:0;comment:跳过记录数"`
	Parameters       string     `json:"parameters" gorm:"type:text;comment:导入参数JSON"`
	Mapping          string     `json:"mapping" gorm:"type:text;comment:字段映射JSON"`
	ValidationRules  string     `json:"validationRules" gorm:"type:text;comment:验证规则JSON"`
	SourceFileName   string     `json:"sourceFileName" gorm:"size:255;comment:源文件名"`
	SourceFilePath   string     `json:"sourceFilePath" gorm:"size:500;comment:源文件路径"`
	SourceFileSize   int64      `json:"sourceFileSize" gorm:"default:0;comment:源文件大小"`
	ResultFileName   string     `json:"resultFileName" gorm:"size:255;comment:结果文件名"`
	ResultFilePath   string     `json:"resultFilePath" gorm:"size:500;comment:结果文件路径"`
	ResultFileSize   int64      `json:"resultFileSize" gorm:"default:0;comment:结果文件大小"`
	DownloadURL      string     `json:"downloadUrl" gorm:"size:500;comment:结果下载链接"`
	StartTime        *time.Time `json:"startTime" gorm:"comment:开始时间"`
	EndTime          *time.Time `json:"endTime" gorm:"comment:结束时间"`
	ErrorMessage     string     `json:"errorMessage" gorm:"size:1000;comment:错误信息"`
	ErrorDetails     string     `json:"errorDetails" gorm:"type:longtext;comment:错误详情JSON"`
	EnterpriseID     string     `json:"enterpriseId" gorm:"not null;size:36;index:idx_import_tasks_enterprise;comment:企业ID"`
	CreatedBy        string     `json:"createdBy" gorm:"not null;size:36;index:idx_import_tasks_creator;comment:创建人ID"`
	CreatedAt        time.Time  `json:"createdAt" gorm:"index:idx_import_tasks_created;comment:创建时间"`
	UpdatedAt        time.Time  `json:"updatedAt" gorm:"comment:更新时间"`

	// 关联关系
	Enterprise *Enterprise `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID"`
	Creator    *User       `json:"creator,omitempty" gorm:"foreignKey:CreatedBy;references:ID"`
}

// 导入类型常量
const (
	ImportTypeInvoice     = "invoice"     // 发票导入
	ImportTypeDeclaration = "declaration" // 申报导入
	ImportTypeEnterprise  = "enterprise"  // 企业导入
	ImportTypeUser        = "user"        // 用户导入
	ImportTypeTaxType     = "tax_type"    // 税种导入
	ImportTypeCustom      = "custom"      // 自定义导入
)

// 导入格式常量
const (
	ImportFormatExcel = "excel" // Excel格式
	ImportFormatCSV   = "csv"   // CSV格式
	ImportFormatJSON  = "json"  // JSON格式
	ImportFormatXML   = "xml"   // XML格式
	ImportFormatTXT   = "txt"   // 文本格式
)

// 任务状态常量
const (
	ImportStatusPending    = "pending"    // 待处理
	ImportStatusValidating = "validating" // 验证中
	ImportStatusProcessing = "processing" // 处理中
	ImportStatusCompleted  = "completed"  // 已完成
	ImportStatusFailed     = "failed"     // 失败
	ImportStatusCancelled  = "cancelled"  // 已取消
	ImportStatusPartial    = "partial"    // 部分成功
)

// ImportTaskCreateRequest 创建导入任务请求
type ImportTaskCreateRequest struct {
	Name            string                 `json:"name" binding:"required,max=200"`
	Type            string                 `json:"type" binding:"required"`
	Format          string                 `json:"format" binding:"required"`
	Parameters      map[string]interface{} `json:"parameters"`
	Mapping         map[string]string      `json:"mapping"`
	ValidationRules map[string]interface{} `json:"validationRules"`
	SourceFileName  string                 `json:"sourceFileName" binding:"required"`
	SourceFilePath  string                 `json:"sourceFilePath" binding:"required"`
	SourceFileSize  int64                  `json:"sourceFileSize"`
	EnterpriseID    string                 `json:"enterpriseId" binding:"required"`
}

// ImportTaskUpdateRequest 更新导入任务请求
type ImportTaskUpdateRequest struct {
	Status           *string    `json:"status"`
	Progress         *int       `json:"progress"`
	TotalRecords     *int64     `json:"totalRecords"`
	ProcessedRecords *int64     `json:"processedRecords"`
	SuccessRecords   *int64     `json:"successRecords"`
	FailedRecords    *int64     `json:"failedRecords"`
	SkippedRecords   *int64     `json:"skippedRecords"`
	ResultFileName   *string    `json:"resultFileName"`
	ResultFilePath   *string    `json:"resultFilePath"`
	ResultFileSize   *int64     `json:"resultFileSize"`
	DownloadURL      *string    `json:"downloadUrl"`
	StartTime        *time.Time `json:"startTime"`
	EndTime          *time.Time `json:"endTime"`
	ErrorMessage     *string    `json:"errorMessage"`
	ErrorDetails     *string    `json:"errorDetails"`
}

// ImportTaskFilter 导入任务过滤条件
type ImportTaskFilter struct {
	Name         string     `json:"name"`
	Type         string     `json:"type"`
	Format       string     `json:"format"`
	Status       string     `json:"status"`
	EnterpriseID string     `json:"enterpriseId"`
	CreatedBy    string     `json:"createdBy"`
	StartTime    *time.Time `json:"startTime"`
	EndTime      *time.Time `json:"endTime"`
	CreatedStart *time.Time `json:"createdStart"`
	CreatedEnd   *time.Time `json:"createdEnd"`
}

// ImportTaskResponse 导入任务响应
type ImportTaskResponse struct {
	ID                      string     `json:"id"`
	Name                    string     `json:"name"`
	Type                    string     `json:"type"`
	TypeName                string     `json:"typeName"`
	Format                  string     `json:"format"`
	FormatName              string     `json:"formatName"`
	Status                  string     `json:"status"`
	StatusName              string     `json:"statusName"`
	Progress                int        `json:"progress"`
	TotalRecords            int64      `json:"totalRecords"`
	ProcessedRecords        int64      `json:"processedRecords"`
	SuccessRecords          int64      `json:"successRecords"`
	FailedRecords           int64      `json:"failedRecords"`
	SkippedRecords          int64      `json:"skippedRecords"`
	SuccessRate             float64    `json:"successRate"`
	Parameters              string     `json:"parameters"`
	Mapping                 string     `json:"mapping"`
	ValidationRules         string     `json:"validationRules"`
	SourceFileName          string     `json:"sourceFileName"`
	SourceFilePath          string     `json:"sourceFilePath"`
	SourceFileSize          int64      `json:"sourceFileSize"`
	SourceFileSizeFormatted string     `json:"sourceFileSizeFormatted"`
	ResultFileName          string     `json:"resultFileName"`
	ResultFilePath          string     `json:"resultFilePath"`
	ResultFileSize          int64      `json:"resultFileSize"`
	ResultFileSizeFormatted string     `json:"resultFileSizeFormatted"`
	DownloadURL             string     `json:"downloadUrl"`
	StartTime               *time.Time `json:"startTime"`
	EndTime                 *time.Time `json:"endTime"`
	Duration                *int64     `json:"duration"` // 执行时长（秒）
	ErrorMessage            string     `json:"errorMessage"`
	ErrorDetails            string     `json:"errorDetails"`
	EnterpriseID            string     `json:"enterpriseId"`
	EnterpriseName          string     `json:"enterpriseName"`
	CreatedBy               string     `json:"createdBy"`
	CreatedByName           string     `json:"createdByName"`
	CreatedAt               time.Time  `json:"createdAt"`
	UpdatedAt               time.Time  `json:"updatedAt"`
	CanDownload             bool       `json:"canDownload"`
	CanCancel               bool       `json:"canCancel"`
	CanRetry                bool       `json:"canRetry"`
	HasErrors               bool       `json:"hasErrors"`
}

// PaginatedImportTasks 分页导入任务响应
type PaginatedImportTasks struct {
	Tasks []ImportTaskResponse `json:"tasks"`
	Total int64                `json:"total"`
	Page  int                  `json:"page"`
	Limit int                  `json:"limit"`
}

// ImportTaskStatistics 导入任务统计
type ImportTaskStatistics struct {
	Total          int64             `json:"total"`
	Completed      int64             `json:"completed"`
	Failed         int64             `json:"failed"`
	Processing     int64             `json:"processing"`
	Pending        int64             `json:"pending"`
	Partial        int64             `json:"partial"`
	SuccessRate    float64           `json:"successRate"`
	TotalRecords   int64             `json:"totalRecords"`
	SuccessRecords int64             `json:"successRecords"`
	FailedRecords  int64             `json:"failedRecords"`
	ByType         map[string]int64  `json:"byType"`
	ByFormat       map[string]int64  `json:"byFormat"`
	ByStatus       map[string]int64  `json:"byStatus"`
	ByDay          map[string]int64  `json:"byDay"`
	TrendData      []ImportTrendData `json:"trendData"`
}

// ImportTrendData 导入趋势数据
type ImportTrendData struct {
	Date           string  `json:"date"`
	Total          int64   `json:"total"`
	Completed      int64   `json:"completed"`
	Failed         int64   `json:"failed"`
	TotalRecords   int64   `json:"totalRecords"`
	SuccessRecords int64   `json:"successRecords"`
	SuccessRate    float64 `json:"successRate"`
}

// ImportTaskError 导入错误详情
type ImportTaskError struct {
	Row      int    `json:"row"`
	Column   string `json:"column"`
	Value    string `json:"value"`
	Error    string `json:"error"`
	Type     string `json:"type"`
	Severity string `json:"severity"`
}

// ImportTaskValidationResult 导入验证结果
type ImportTaskValidationResult struct {
	IsValid     bool              `json:"isValid"`
	TotalRows   int64             `json:"totalRows"`
	ValidRows   int64             `json:"validRows"`
	InvalidRows int64             `json:"invalidRows"`
	Errors      []ImportTaskError `json:"errors"`
	Warnings    []ImportTaskError `json:"warnings"`
	Summary     map[string]int64  `json:"summary"`
	ValidatedAt time.Time         `json:"validatedAt"`
}

// ImportTaskPreviewRequest 预览导入数据请求
type ImportTaskPreviewRequest struct {
	FilePath string            `json:"filePath" binding:"required"`
	Format   string            `json:"format" binding:"required"`
	Mapping  map[string]string `json:"mapping"`
	Limit    int               `json:"limit"`
}

// ImportTaskPreviewResult 预览导入数据结果
type ImportTaskPreviewResult struct {
	Headers     []string                 `json:"headers"`
	Rows        []map[string]interface{} `json:"rows"`
	TotalRows   int64                    `json:"totalRows"`
	PreviewRows int                      `json:"previewRows"`
	Mapping     map[string]string        `json:"mapping"`
	Suggestions map[string][]string      `json:"suggestions"`
}

// ImportTaskTemplateRequest 获取导入模板请求
type ImportTaskTemplateRequest struct {
	Type   string `json:"type" binding:"required"`
	Format string `json:"format" binding:"required"`
}

// ImportTaskTemplateResult 获取导入模板结果
type ImportTaskTemplateResult struct {
	FileName    string                   `json:"fileName"`
	DownloadURL string                   `json:"downloadUrl"`
	Headers     []string                 `json:"headers"`
	SampleData  []map[string]interface{} `json:"sampleData"`
	Rules       map[string]interface{}   `json:"rules"`
	GeneratedAt time.Time                `json:"generatedAt"`
}

// ImportTaskBatchRequest 批量导入任务请求
type ImportTaskBatchRequest struct {
	Tasks []ImportTaskCreateRequest `json:"tasks" binding:"required,min=1"`
}

// ImportTaskBatchResult 批量导入任务结果
type ImportTaskBatchResult struct {
	Total     int                  `json:"total"`
	Success   int                  `json:"success"`
	Failed    int                  `json:"failed"`
	Tasks     []ImportTaskResponse `json:"tasks"`
	Errors    []ImportTaskError    `json:"errors"`
	CreatedAt time.Time            `json:"createdAt"`
}

// ImportTaskCancelRequest 取消导入任务请求
type ImportTaskCancelRequest struct {
	IDs    []string `json:"ids" binding:"required,min=1"`
	Reason string   `json:"reason"`
}

// ImportTaskRetryRequest 重试导入任务请求
type ImportTaskRetryRequest struct {
	IDs []string `json:"ids" binding:"required,min=1"`
}

// GetTypeName 获取导入类型名称
func GetImportTypeName(importType string) string {
	switch importType {
	case ImportTypeInvoice:
		return "发票导入"
	case ImportTypeDeclaration:
		return "申报导入"
	case ImportTypeEnterprise:
		return "企业导入"
	case ImportTypeUser:
		return "用户导入"
	case ImportTypeTaxType:
		return "税种导入"
	case ImportTypeCustom:
		return "自定义导入"
	default:
		return "未知类型"
	}
}

// GetFormatName 获取导入格式名称
func GetImportFormatName(format string) string {
	switch format {
	case ImportFormatExcel:
		return "Excel"
	case ImportFormatCSV:
		return "CSV"
	case ImportFormatJSON:
		return "JSON"
	case ImportFormatXML:
		return "XML"
	case ImportFormatTXT:
		return "文本"
	default:
		return "未知格式"
	}
}

// GetStatusName 获取任务状态名称
func GetImportStatusName(status string) string {
	switch status {
	case ImportStatusPending:
		return "待处理"
	case ImportStatusValidating:
		return "验证中"
	case ImportStatusProcessing:
		return "处理中"
	case ImportStatusCompleted:
		return "已完成"
	case ImportStatusFailed:
		return "失败"
	case ImportStatusCancelled:
		return "已取消"
	case ImportStatusPartial:
		return "部分成功"
	default:
		return "未知状态"
	}
}

// IsValidImportType 验证导入类型是否有效
func IsValidImportType(importType string) bool {
	validTypes := []string{
		ImportTypeInvoice,
		ImportTypeDeclaration,
		ImportTypeEnterprise,
		ImportTypeUser,
		ImportTypeTaxType,
		ImportTypeCustom,
	}

	for _, validType := range validTypes {
		if importType == validType {
			return true
		}
	}
	return false
}

// IsValidImportFormat 验证导入格式是否有效
func IsValidImportFormat(format string) bool {
	validFormats := []string{
		ImportFormatExcel,
		ImportFormatCSV,
		ImportFormatJSON,
		ImportFormatXML,
		ImportFormatTXT,
	}

	for _, validFormat := range validFormats {
		if format == validFormat {
			return true
		}
	}
	return false
}

// IsValidImportStatus 验证任务状态是否有效
func IsValidImportStatus(status string) bool {
	validStatuses := []string{
		ImportStatusPending,
		ImportStatusValidating,
		ImportStatusProcessing,
		ImportStatusCompleted,
		ImportStatusFailed,
		ImportStatusCancelled,
		ImportStatusPartial,
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// CanDownload 判断是否可以下载结果
func (i *ImportTask) CanDownload() bool {
	return (i.Status == ImportStatusCompleted || i.Status == ImportStatusPartial) && i.DownloadURL != ""
}

// CanCancel 判断是否可以取消
func (i *ImportTask) CanCancel() bool {
	return i.Status == ImportStatusPending || i.Status == ImportStatusValidating || i.Status == ImportStatusProcessing
}

// CanRetry 判断是否可以重试
func (i *ImportTask) CanRetry() bool {
	return i.Status == ImportStatusFailed
}

// HasErrors 判断是否有错误
func (i *ImportTask) HasErrors() bool {
	return i.FailedRecords > 0 || i.ErrorMessage != ""
}

// GetSuccessRate 获取成功率
func (i *ImportTask) GetSuccessRate() float64 {
	if i.TotalRecords == 0 {
		return 0
	}
	return float64(i.SuccessRecords) / float64(i.TotalRecords) * 100
}

// GetDuration 获取执行时长（秒）
func (i *ImportTask) GetDuration() *int64 {
	if i.StartTime == nil || i.EndTime == nil {
		return nil
	}
	duration := int64(i.EndTime.Sub(*i.StartTime).Seconds())
	return &duration
}

// TableName 指定表名
func (ImportTask) TableName() string {
	return "import_tasks"
}
