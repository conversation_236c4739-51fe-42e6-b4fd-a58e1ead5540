package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// TaxFilingOperatorType 操作类型枚举
type TaxFilingOperatorType string

const (
	TaxFilingOperatorSystem TaxFilingOperatorType = "system"
	TaxFilingOperatorUser   TaxFilingOperatorType = "user"
	TaxFilingOperatorAuto   TaxFilingOperatorType = "auto"
)

// ExternalResponse 外部系统响应数据
type ExternalResponse map[string]interface{}

// Value 实现 driver.Valuer 接口
func (e ExternalResponse) Value() (driver.Value, error) {
	if len(e) == 0 {
		return nil, nil
	}
	return json.Marshal(e)
}

// Scan 实现 sql.Scanner 接口
func (e *ExternalResponse) Scan(value interface{}) error {
	if value == nil {
		*e = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into ExternalResponse", value)
	}

	return json.Unmarshal(bytes, e)
}

// TaxFilingStatusHistory 税务申报状态历史模型
type TaxFilingStatusHistory struct {
	ID           string                     `gorm:"primaryKey;size:50" json:"id"`
	SubmissionID string                     `gorm:"size:50;not null;index" json:"submission_id"`
	FromStatus   *TaxFilingSubmissionStatus `gorm:"size:20" json:"from_status,omitempty"`
	ToStatus     TaxFilingSubmissionStatus  `gorm:"size:20;not null;index:idx_status_change" json:"to_status"`
	Reason       *string                    `gorm:"size:100" json:"reason,omitempty"`
	Description  *string                    `gorm:"type:text" json:"description,omitempty"`
	ErrorCode    *string                    `gorm:"size:50" json:"error_code,omitempty"`
	ErrorMessage *string                    `gorm:"type:text" json:"error_message,omitempty"`

	// 外部系统响应
	ExternalResponse ExternalResponse `gorm:"type:json" json:"external_response,omitempty"`

	// 处理信息
	ProcessingTime *int `json:"processing_time,omitempty"` // 处理耗时(秒)

	// 操作信息
	Operator     *string               `gorm:"size:50" json:"operator,omitempty"`
	OperatorType TaxFilingOperatorType `gorm:"size:20;not null;default:system" json:"operator_type"`
	IPAddress    *string               `gorm:"size:45" json:"ip_address,omitempty"`
	UserAgent    *string               `gorm:"size:500" json:"user_agent,omitempty"`

	// 时间戳
	CreatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;index" json:"created_at"`

	// 关联
	Submission *TaxFilingSubmission `gorm:"foreignKey:SubmissionID" json:"submission,omitempty"`
}

// TableName 指定表名
func (TaxFilingStatusHistory) TableName() string {
	return "tax_filing_status_history"
}

// BeforeCreate GORM钩子 - 创建前
func (h *TaxFilingStatusHistory) BeforeCreate(tx *gorm.DB) error {
	if h.ID == "" {
		h.ID = GenerateID()
	}
	return nil
}

// TaxFilingStatusHistoryCreateRequest 创建状态历史请求
type TaxFilingStatusHistoryCreateRequest struct {
	SubmissionID     string                     `json:"submission_id" binding:"required"`
	FromStatus       *TaxFilingSubmissionStatus `json:"from_status,omitempty"`
	ToStatus         TaxFilingSubmissionStatus  `json:"to_status" binding:"required"`
	Reason           *string                    `json:"reason,omitempty"`
	Description      *string                    `json:"description,omitempty"`
	ErrorCode        *string                    `json:"error_code,omitempty"`
	ErrorMessage     *string                    `json:"error_message,omitempty"`
	ExternalResponse ExternalResponse           `json:"external_response,omitempty"`
	ProcessingTime   *int                       `json:"processing_time,omitempty"`
	Operator         *string                    `json:"operator,omitempty"`
	OperatorType     TaxFilingOperatorType      `json:"operator_type"`
	IPAddress        *string                    `json:"ip_address,omitempty"`
	UserAgent        *string                    `json:"user_agent,omitempty"`
}

// TaxFilingStatusHistoryResponse 状态历史响应
type TaxFilingStatusHistoryResponse struct {
	TaxFilingStatusHistory
	FromStatusText string `json:"from_status_text"`
	ToStatusText   string `json:"to_status_text"`
	OperatorName   string `json:"operator_name,omitempty"`
}

// GetFromStatusText 获取原状态文本
func (h *TaxFilingStatusHistoryResponse) GetFromStatusText() string {
	if h.FromStatus == nil {
		return "无"
	}

	switch *h.FromStatus {
	case TaxFilingStatusPending:
		return "待处理"
	case TaxFilingStatusProcessing:
		return "处理中"
	case TaxFilingStatusSubmitted:
		return "已提交"
	case TaxFilingStatusAccepted:
		return "已接受"
	case TaxFilingStatusRejected:
		return "已拒绝"
	case TaxFilingStatusFailed:
		return "失败"
	case TaxFilingStatusCancelled:
		return "已取消"
	default:
		return "未知状态"
	}
}

// GetToStatusText 获取新状态文本
func (h *TaxFilingStatusHistoryResponse) GetToStatusText() string {
	switch h.ToStatus {
	case TaxFilingStatusPending:
		return "待处理"
	case TaxFilingStatusProcessing:
		return "处理中"
	case TaxFilingStatusSubmitted:
		return "已提交"
	case TaxFilingStatusAccepted:
		return "已接受"
	case TaxFilingStatusRejected:
		return "已拒绝"
	case TaxFilingStatusFailed:
		return "失败"
	case TaxFilingStatusCancelled:
		return "已取消"
	default:
		return "未知状态"
	}
}

// GetOperatorTypeText 获取操作类型文本
func (h *TaxFilingStatusHistoryResponse) GetOperatorTypeText() string {
	switch h.OperatorType {
	case TaxFilingOperatorSystem:
		return "系统"
	case TaxFilingOperatorUser:
		return "用户"
	case TaxFilingOperatorAuto:
		return "自动"
	default:
		return "未知"
	}
}

// TaxFilingStatusHistoryListRequest 状态历史列表请求
type TaxFilingStatusHistoryListRequest struct {
	SubmissionID string                     `form:"submission_id"`
	FromStatus   *TaxFilingSubmissionStatus `form:"from_status"`
	ToStatus     *TaxFilingSubmissionStatus `form:"to_status"`
	OperatorType *TaxFilingOperatorType     `form:"operator_type"`
	StartTime    *time.Time                 `form:"start_time"`
	EndTime      *time.Time                 `form:"end_time"`
	Page         int                        `form:"page,default=1"`
	PageSize     int                        `form:"page_size,default=20"`
	OrderBy      string                     `form:"order_by,default=created_at"`
	Order        string                     `form:"order,default=desc"`
}

// TaxFilingStatusHistoryListResponse 状态历史列表响应
type TaxFilingStatusHistoryListResponse struct {
	List       []TaxFilingStatusHistoryResponse `json:"list"`
	Total      int64                            `json:"total"`
	Page       int                              `json:"page"`
	PageSize   int                              `json:"page_size"`
	TotalPages int                              `json:"total_pages"`
}

// TaxFilingStatusStatistics 状态统计
type TaxFilingStatusStatistics struct {
	Status TaxFilingSubmissionStatus `json:"status"`
	Count  int64                     `json:"count"`
	Text   string                    `json:"text"`
}

// TaxFilingStatusTransition 状态转换统计
type TaxFilingStatusTransition struct {
	FromStatus *TaxFilingSubmissionStatus `json:"from_status"`
	ToStatus   TaxFilingSubmissionStatus  `json:"to_status"`
	Count      int64                      `json:"count"`
	FromText   string                     `json:"from_text"`
	ToText     string                     `json:"to_text"`
}

// TaxFilingStatusReport 状态报告
type TaxFilingStatusReport struct {
	TotalSubmissions int64                       `json:"total_submissions"`
	StatusStats      []TaxFilingStatusStatistics `json:"status_stats"`
	Transitions      []TaxFilingStatusTransition `json:"transitions"`
	SuccessRate      float64                     `json:"success_rate"`
	FailureRate      float64                     `json:"failure_rate"`
	AverageTime      float64                     `json:"average_time"` // 平均处理时间(分钟)
}

// CreateStatusHistory 创建状态历史记录的辅助函数
func CreateStatusHistory(
	submissionID string,
	fromStatus *TaxFilingSubmissionStatus,
	toStatus TaxFilingSubmissionStatus,
	reason string,
	operator *string,
	operatorType TaxFilingOperatorType,
) *TaxFilingStatusHistory {
	history := &TaxFilingStatusHistory{
		ID:           GenerateID(),
		SubmissionID: submissionID,
		FromStatus:   fromStatus,
		ToStatus:     toStatus,
		Reason:       &reason,
		Operator:     operator,
		OperatorType: operatorType,
		CreatedAt:    time.Now(),
	}

	return history
}

// CreateSystemStatusHistory 创建系统状态历史记录
func CreateSystemStatusHistory(
	submissionID string,
	fromStatus *TaxFilingSubmissionStatus,
	toStatus TaxFilingSubmissionStatus,
	reason string,
) *TaxFilingStatusHistory {
	return CreateStatusHistory(
		submissionID,
		fromStatus,
		toStatus,
		reason,
		nil,
		TaxFilingOperatorSystem,
	)
}

// CreateUserStatusHistory 创建用户状态历史记录
func CreateUserStatusHistory(
	submissionID string,
	fromStatus *TaxFilingSubmissionStatus,
	toStatus TaxFilingSubmissionStatus,
	reason string,
	operator string,
) *TaxFilingStatusHistory {
	return CreateStatusHistory(
		submissionID,
		fromStatus,
		toStatus,
		reason,
		&operator,
		TaxFilingOperatorUser,
	)
}
