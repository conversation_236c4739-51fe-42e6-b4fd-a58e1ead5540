package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// TaxType 税务类型模型
type TaxType struct {
	ID              string          `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:税种ID"`
	Code            string          `json:"code" gorm:"type:varchar(20);not null;uniqueIndex:uk_tax_types_code;column:tax_code;comment:税种编码"`
	Name            string          `json:"name" gorm:"type:varchar(100);not null;column:tax_name;comment:税种名称"`
	Description     string          `json:"description" gorm:"type:text;column:description;comment:税种描述"`
	Category        string          `json:"category" gorm:"type:enum('shared','central','local');not null;column:category;index:idx_tax_types_category;comment:税种分类"`
	RateType        string          `json:"rate_type" gorm:"type:enum('percentage','fixed','progressive');default:percentage;column:rate_type;comment:税率类型"`
	TaxRate         decimal.Decimal `json:"tax_rate" gorm:"type:decimal(8,4);column:default_rate;comment:默认税率"`
	TaxRateOptions  string          `json:"tax_rate_options" gorm:"type:json;column:rate_options;comment:可选税率"`
	FilingFrequency string          `json:"filing_frequency" gorm:"type:enum('monthly','quarterly','annually','other');not null;column:filing_frequency;index:idx_tax_types_filing_frequency;comment:申报频率"`
	FilingDay       *int            `json:"filing_day" gorm:"column:filing_day;comment:申报日"`
	PaymentDeadline *int            `json:"payment_deadline" gorm:"column:payment_deadline;comment:缴款期限(天)"`
	LegalBasis      string          `json:"legal_basis" gorm:"type:varchar(500);column:legal_basis;comment:法律依据"`
	TaxAuthority    string          `json:"tax_authority" gorm:"type:varchar(100);column:tax_authority;comment:征收机关"`
	IsActive        bool            `json:"is_active" gorm:"default:true;column:is_active;comment:是否启用"`
	CreatedAt       time.Time       `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt       time.Time       `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`
}

// 税种分类常量
const (
	TaxCategoryShared  = "shared"
	TaxCategoryCentral = "central"
	TaxCategoryLocal   = "local"
)

// 税率类型常量
const (
	TaxRateTypePercentage  = "percentage"
	TaxRateTypeFixed       = "fixed"
	TaxRateTypeProgressive = "progressive"
)

// 申报频率常量
const (
	FilingFrequencyMonthly   = "monthly"
	FilingFrequencyQuarterly = "quarterly"
	FilingFrequencyAnnually  = "annually"
	FilingFrequencyOther     = "other"
)

// 常用税种代码常量
const (
	TaxCodeVAT = "VAT" // 增值税
	TaxCodeEIT = "EIT" // 企业所得税
	TaxCodeIIT = "IIT" // 个人所得税
	TaxCodeCIT = "CIT" // 消费税
	TaxCodeBT  = "BT"  // 营业税
	TaxCodeCT  = "CT"  // 城建税
	TaxCodeEET = "EET" // 教育费附加
	TaxCodeLET = "LET" // 地方教育费附加
	TaxCodePIT = "PIT" // 印花税
	TaxCodeVT  = "VT"  // 车辆税
	TaxCodePT  = "PT"  // 房产税
	TaxCodeLUT = "LUT" // 土地使用税
)

// TaxDeclarationPeriodRequest 表示设置申报周期的请求
type TaxDeclarationPeriodRequest struct {
	PeriodType    string   `json:"periodType" binding:"required"` // 周期类型
	DueDay        int      `json:"dueDay" binding:"required"`     // 截止日
	GracePeriod   int      `json:"gracePeriod"`                   // 宽限期
	Notifications []string `json:"notifications"`                 // 提醒时间点
	Description   string   `json:"description"`                   // 描述
}

// TaxTypeListResponse 表示税种列表响应
type TaxTypeListResponse struct {
	TaxTypes   []TaxType `json:"taxTypes"`   // 税种列表
	Total      int64     `json:"total"`      // 总数
	Page       int       `json:"page"`       // 当前页
	PageSize   int       `json:"pageSize"`   // 每页大小
	TotalPages int       `json:"totalPages"` // 总页数
}

// TaxTypeCreateRequest 创建税种请求
type TaxTypeCreateRequest struct {
	Code        string  `json:"code" binding:"required,max=20"`
	Name        string  `json:"name" binding:"required,max=100"`
	Category    string  `json:"category" binding:"required,oneof=shared central local"`
	Description string  `json:"description"`
	Rate        float64 `json:"rate" binding:"min=0,max=1"`
}

// TaxTypeUpdateRequest 更新税种请求
type TaxTypeUpdateRequest struct {
	Name        string  `json:"name" binding:"max=100"`
	Category    string  `json:"category" binding:"oneof=shared central local"`
	Description string  `json:"description"`
	Rate        float64 `json:"rate" binding:"min=0,max=1"`
	IsActive    *bool   `json:"isActive"`
}

// TaxTypeQuery 税种查询请求
type TaxTypeQuery struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"pageSize" form:"pageSize"`
	Keyword  string `json:"keyword" form:"keyword"`
	Category string `json:"category" form:"category"`
	IsActive *bool  `json:"isActive" form:"isActive"`
}

// TaxTypeStats 税种统计
type TaxTypeStats struct {
	Total    int64 `json:"total"`
	Active   int64 `json:"active"`
	Inactive int64 `json:"inactive"`
}

// TaxTypeOption 税种选项（用于下拉选择）
type TaxTypeOption struct {
	Value string  `json:"value"`
	Label string  `json:"label"`
	Rate  float64 `json:"rate"`
}
