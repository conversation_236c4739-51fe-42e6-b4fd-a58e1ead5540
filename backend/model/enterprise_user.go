package model

import (
	"time"
)

// EnterpriseUser 企业用户关联模型
type EnterpriseUser struct {
	ID           string    `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:主键ID"`
	EnterpriseID string    `json:"enterprise_id" gorm:"type:varchar(36);not null;column:enterprise_id;index:idx_enterprise_users_enterprise_id;uniqueIndex:uk_enterprise_users,priority:1;comment:企业ID"`
	UserID       string    `json:"user_id" gorm:"type:varchar(36);not null;column:user_id;index:idx_enterprise_users_user_id;uniqueIndex:uk_enterprise_users,priority:2;comment:用户ID"`
	RoleID       string    `json:"role_id" gorm:"type:varchar(36);not null;column:role_id;index:idx_enterprise_users_role_id;comment:角色ID"`
	Status       string    `json:"status" gorm:"type:enum('active','inactive','pending');default:active;column:status;index:idx_enterprise_users_status;comment:状态"`
	JoinedAt     time.Time `json:"joined_at" gorm:"column:joined_at;comment:加入时间"`
	InvitedBy    *string   `json:"invited_by" gorm:"type:varchar(36);column:invited_by;comment:邀请人ID"`
	IsOwner      bool      `json:"is_owner" gorm:"default:false;column:is_owner;index:idx_enterprise_users_is_owner;uniqueIndex:uk_enterprise_owner,priority:2;comment:是否为企业所有者"`
	CreatedAt    time.Time `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	Enterprise    *Enterprise `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	User          *User       `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	Role          *Role       `json:"role,omitempty" gorm:"foreignKey:RoleID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	InvitedByUser *User       `json:"invited_by_user,omitempty" gorm:"foreignKey:InvitedBy;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

// TableName 指定表名
func (EnterpriseUser) TableName() string {
	return "enterprise_users"
}

// UserPermission 用户特殊权限模型
type UserPermission struct {
	ID           string     `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:主键ID"`
	UserID       string     `json:"user_id" gorm:"type:varchar(36);not null;column:user_id;index:idx_user_permissions_user_id;comment:用户ID"`
	PermissionID string     `json:"permission_id" gorm:"type:varchar(36);not null;column:permission_id;index:idx_user_permissions_permission_id;comment:权限ID"`
	EnterpriseID *string    `json:"enterprise_id" gorm:"type:varchar(36);column:enterprise_id;index:idx_user_permissions_enterprise_id;comment:企业ID（企业级权限）"`
	GrantedBy    string     `json:"granted_by" gorm:"type:varchar(36);not null;column:granted_by;index:idx_user_permissions_granted_by;comment:授权人ID"`
	GrantedAt    time.Time  `json:"granted_at" gorm:"column:granted_at;comment:授权时间"`
	ExpiresAt    *time.Time `json:"expires_at" gorm:"column:expires_at;comment:过期时间"`
	IsActive     bool       `json:"is_active" gorm:"default:true;column:is_active;comment:是否启用"`
	CreatedAt    time.Time  `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt    time.Time  `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	User          *User       `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	Permission    *Permission `json:"permission,omitempty" gorm:"foreignKey:PermissionID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	Enterprise    *Enterprise `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	GrantedByUser *User       `json:"granted_by_user,omitempty" gorm:"foreignKey:GrantedBy;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
}

// TableName 指定表名
func (UserPermission) TableName() string {
	return "user_permissions"
}

// PermissionLog 权限操作日志模型
type PermissionLog struct {
	ID            string      `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:主键ID"`
	UserID        string      `json:"user_id" gorm:"type:varchar(36);not null;column:user_id;index:idx_permission_logs_user_id;comment:操作用户ID"`
	TargetUserID  *string     `json:"target_user_id" gorm:"type:varchar(36);column:target_user_id;index:idx_permission_logs_target_user_id;comment:目标用户ID"`
	EnterpriseID  *string     `json:"enterprise_id" gorm:"type:varchar(36);column:enterprise_id;index:idx_permission_logs_enterprise_id;comment:企业ID"`
	OperationType string      `json:"operation_type" gorm:"type:enum('role_assign','role_revoke','permission_grant','permission_revoke','owner_transfer','user_invite','user_remove');not null;column:operation_type;index:idx_permission_logs_operation_type;comment:操作类型"`
	ResourceType  string      `json:"resource_type" gorm:"type:varchar(50);not null;column:resource_type;comment:资源类型"`
	ResourceID    string      `json:"resource_id" gorm:"type:varchar(36);not null;column:resource_id;comment:资源ID"`
	OldValue      interface{} `json:"old_value" gorm:"type:json;column:old_value;comment:操作前值"`
	NewValue      interface{} `json:"new_value" gorm:"type:json;column:new_value;comment:操作后值"`
	Reason        string      `json:"reason" gorm:"type:varchar(500);column:reason;comment:操作原因"`
	IPAddress     string      `json:"ip_address" gorm:"type:varchar(45);column:ip_address;comment:操作IP"`
	UserAgent     string      `json:"user_agent" gorm:"type:varchar(500);column:user_agent;comment:用户代理"`
	CreatedAt     time.Time   `json:"created_at" gorm:"column:created_at;index:idx_permission_logs_created_at;comment:创建时间"`

	// 关联关系
	User       *User       `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	TargetUser *User       `json:"target_user,omitempty" gorm:"foreignKey:TargetUserID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	Enterprise *Enterprise `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
}

// TableName 指定表名
func (PermissionLog) TableName() string {
	return "permission_logs"
}

// 状态常量
const (
	EnterpriseUserStatusActive   = "active"
	EnterpriseUserStatusInactive = "inactive"
	EnterpriseUserStatusPending  = "pending"
)

// 操作类型常量
const (
	OperationTypeRoleAssign       = "role_assign"
	OperationTypeRoleRevoke       = "role_revoke"
	OperationTypePermissionGrant  = "permission_grant"
	OperationTypePermissionRevoke = "permission_revoke"
	OperationTypeOwnerTransfer    = "owner_transfer"
	OperationTypeUserInvite       = "user_invite"
	OperationTypeUserRemove       = "user_remove"
)

// CreateEnterpriseUserRequest 创建企业用户关联请求
type CreateEnterpriseUserRequest struct {
	EnterpriseID string `json:"enterprise_id" binding:"required"`
	UserID       string `json:"user_id" binding:"required"`
	RoleID       string `json:"role_id" binding:"required"`
	InvitedBy    string `json:"invited_by"`
}

// UpdateEnterpriseUserRequest 更新企业用户关联请求
type UpdateEnterpriseUserRequest struct {
	RoleID string `json:"role_id"`
	Status string `json:"status"`
}

// EnterpriseUserResponse 企业用户关联响应
type EnterpriseUserResponse struct {
	ID           string    `json:"id"`
	EnterpriseID string    `json:"enterprise_id"`
	UserID       string    `json:"user_id"`
	UserName     string    `json:"user_name"`
	Email        string    `json:"email"`
	Phone        string    `json:"phone"`
	RoleID       string    `json:"role_id"`
	RoleName     string    `json:"role_name"`
	Status       string    `json:"status"`
	JoinedAt     time.Time `json:"joined_at"`
	IsOwner      bool      `json:"is_owner"`
	CreatedAt    time.Time `json:"created_at"`
}

// GrantUserPermissionRequest 授予用户权限请求
type GrantUserPermissionRequest struct {
	UserID       string     `json:"user_id" binding:"required"`
	PermissionID string     `json:"permission_id" binding:"required"`
	EnterpriseID *string    `json:"enterprise_id"`
	ExpiresAt    *time.Time `json:"expires_at"`
	Reason       string     `json:"reason"`
}

// TransferOwnershipRequest 转让企业所有权请求
type TransferOwnershipRequest struct {
	NewOwnerID string `json:"new_owner_id" binding:"required"`
	Reason     string `json:"reason"`
	Password   string `json:"password" binding:"required"` // 需要当前所有者密码确认
}

// UserSearchResponse 用户搜索响应
type UserSearchResponse struct {
	ID         string    `json:"id"`
	UserName   string    `json:"user_name"`
	Email      string    `json:"email"`
	Phone      string    `json:"phone"`
	Avatar     string    `json:"avatar"`
	Department string    `json:"department"`
	Position   string    `json:"position"`
	IsActive   bool      `json:"is_active"`
	CreatedAt  time.Time `json:"created_at"`
}

// SearchUserResponse 搜索用户响应
type SearchUserResponse struct {
	ID         string    `json:"id"`
	UserName   string    `json:"user_name"`
	Email      string    `json:"email"`
	Phone      string    `json:"phone"`
	Avatar     string    `json:"avatar"`
	Department string    `json:"department"`
	Position   string    `json:"position"`
	Status     string    `json:"status"`
	CreatedAt  time.Time `json:"created_at"`
}

// 用户管理增强请求和响应结构体

// InviteUserRequest 邀请用户请求
type InviteUserRequest struct {
	UserID      string `json:"user_id" binding:"required" comment:"用户ID"`
	RoleID      string `json:"role_id" binding:"required" comment:"角色ID"`
	Description string `json:"description" comment:"邀请说明"`
}

// AssignRoleRequest 分配角色请求
type AssignRoleRequest struct {
	UserID string `json:"user_id" binding:"required" comment:"用户ID"`
	RoleID string `json:"role_id" binding:"required" comment:"角色ID"`
	Reason string `json:"reason" comment:"变更原因"`
}

// RemoveUserRequest 移除用户请求
type RemoveUserRequest struct {
	UserID string `json:"user_id" binding:"required" comment:"用户ID"`
	Reason string `json:"reason" comment:"移除原因"`
}

// GrantPermissionRequest 授予特殊权限请求
type GrantPermissionRequest struct {
	UserID       string     `json:"user_id" binding:"required" comment:"用户ID"`
	PermissionID string     `json:"permission_id" binding:"required" comment:"权限ID"`
	ExpiresAt    *time.Time `json:"expires_at" comment:"过期时间"`
	Reason       string     `json:"reason" comment:"授权原因"`
}

// EnterpriseUserDetailResponse 企业用户详细响应
type EnterpriseUserDetailResponse struct {
	ID            string    `json:"id"`
	EnterpriseID  string    `json:"enterprise_id"`
	UserID        string    `json:"user_id"`
	UserName      string    `json:"user_name"`
	Email         string    `json:"email"`
	Phone         string    `json:"phone"`
	Department    string    `json:"department"`
	Position      string    `json:"position"`
	RoleID        string    `json:"role_id"`
	RoleName      string    `json:"role_name"`
	Status        string    `json:"status"`
	IsOwner       bool      `json:"is_owner"`
	JoinedAt      time.Time `json:"joined_at"`
	InvitedBy     *string   `json:"invited_by"`
	InvitedByName *string   `json:"invited_by_name"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// UserPermissionSummaryResponse 用户权限汇总响应
type UserPermissionSummaryResponse struct {
	UserID               string                 `json:"user_id"`
	UserName             string                 `json:"user_name"`
	RoleID               string                 `json:"role_id"`
	RoleName             string                 `json:"role_name"`
	RolePermissions      []PermissionSummary    `json:"role_permissions"`
	SpecialPermissions   []UserPermissionDetail `json:"special_permissions"`
	EffectivePermissions []string               `json:"effective_permissions"`
}

// PermissionSummary 权限汇总
type PermissionSummary struct {
	ID          string `json:"id"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Module      string `json:"module"`
	Resource    string `json:"resource"`
	Action      string `json:"action"`
	Type        string `json:"type"`
}

// UserPermissionDetail 用户特殊权限详情
type UserPermissionDetail struct {
	ID            string            `json:"id"`
	PermissionID  string            `json:"permission_id"`
	Permission    PermissionSummary `json:"permission"`
	GrantedBy     string            `json:"granted_by"`
	GrantedByName string            `json:"granted_by_name"`
	GrantedAt     time.Time         `json:"granted_at"`
	ExpiresAt     *time.Time        `json:"expires_at"`
	IsActive      bool              `json:"is_active"`
}

// EnterpriseUserListRequest 企业用户列表请求
type EnterpriseUserListRequest struct {
	Page         int    `json:"page" form:"page" binding:"min=1"`
	PageSize     int    `json:"page_size" form:"page_size" binding:"min=1,max=100"`
	Status       string `json:"status" form:"status"`
	RoleID       string `json:"role_id" form:"role_id"`
	Keyword      string `json:"keyword" form:"keyword"`
	IncludeOwner bool   `json:"include_owner" form:"include_owner"`
}

// EnterpriseUserListResponseV2 企业用户列表响应V2
type EnterpriseUserListResponseV2 struct {
	Users    []EnterpriseUserDetailResponse `json:"users"`
	Total    int64                          `json:"total"`
	Page     int                            `json:"page"`
	PageSize int                            `json:"page_size"`
}
