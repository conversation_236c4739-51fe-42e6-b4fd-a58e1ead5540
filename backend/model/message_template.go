package model

import (
	"time"
)

// MessageTemplate 消息模板模型
type MessageTemplate struct {
	ID        string    `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:主键ID"`
	Code      string    `json:"code" gorm:"type:varchar(50);not null;uniqueIndex:uk_message_templates_code;column:code;comment:模板编码"`
	Name      string    `json:"name" gorm:"type:varchar(100);not null;column:name;comment:模板名称"`
	Type      string    `json:"type" gorm:"type:enum('email','sms','system','push');not null;column:type;index:idx_message_templates_type;comment:消息类型"`
	Category  string    `json:"category" gorm:"type:varchar(50);not null;column:category;index:idx_message_templates_category;comment:消息分类"`
	Subject   string    `json:"subject" gorm:"type:varchar(200);column:subject;comment:消息主题"`
	Content   string    `json:"content" gorm:"type:text;not null;column:content;comment:消息内容"`
	Variables string    `json:"variables" gorm:"type:json;column:variables;comment:变量定义"`
	IsActive  bool      `json:"is_active" gorm:"default:true;column:is_active;comment:是否启用"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`
}

// TableName 指定表名
func (MessageTemplate) TableName() string {
	return "message_templates"
}

// 消息类型常量
const (
	MessageTypeEmail  = "email"  // 邮件
	MessageTypeSMS    = "sms"    // 短信
	MessageTypePush   = "push"   // 推送
	MessageTypeSystem = "system" // 系统消息
	MessageTypeApp    = "app"    // 应用内消息
)

// 消息分类常量
const (
	MessageCategoryReminder     = "reminder"     // 提醒类
	MessageCategoryNotification = "notification" // 通知类
	MessageCategoryAlert        = "alert"        // 警告类
	MessageCategoryReport       = "report"       // 报告类
	MessageCategorySystem       = "system"       // 系统类
	MessageCategoryMarketing    = "marketing"    // 营销类
	MessageCategoryCustom       = "custom"       // 自定义类
)

// 消息优先级常量
const (
	MessagePriorityLow    = "low"    // 低优先级
	MessagePriorityNormal = "normal" // 普通优先级
	MessagePriorityHigh   = "high"   // 高优先级
	MessagePriorityUrgent = "urgent" // 紧急优先级
)

// 发送渠道常量
const (
	ChannelEmail = "email" // 邮件渠道
	ChannelSMS   = "sms"   // 短信渠道
	ChannelPush  = "push"  // 推送渠道
	ChannelApp   = "app"   // 应用内渠道
)

// MessageTemplateCreateRequest 创建消息模板请求
type MessageTemplateCreateRequest struct {
	Name      string  `json:"name" binding:"required,max=255"`
	Code      string  `json:"code" binding:"required,max=100"`
	Type      string  `json:"type" binding:"required"`
	Category  string  `json:"category" binding:"required"`
	Subject   *string `json:"subject" binding:"omitempty,max=500"`
	Content   string  `json:"content" binding:"required"`
	Variables *string `json:"variables"`
}

// MessageTemplateUpdateRequest 更新消息模板请求
type MessageTemplateUpdateRequest struct {
	Name      *string `json:"name" binding:"omitempty,max=255"`
	Type      *string `json:"type"`
	Category  *string `json:"category"`
	Subject   *string `json:"subject" binding:"omitempty,max=500"`
	Content   *string `json:"content"`
	Variables *string `json:"variables"`
	IsActive  *bool   `json:"isActive"`
}

// MessageTemplateFilter 消息模板过滤条件
type MessageTemplateFilter struct {
	Name      string `json:"name"`
	Code      string `json:"code"`
	Type      string `json:"type"`
	Category  string `json:"category"`
	IsActive  *bool  `json:"isActive"`
	CreatedBy string `json:"createdBy"`
}

// MessageTemplateResponse 消息模板响应
type MessageTemplateResponse struct {
	ID            string    `json:"id"`
	Name          string    `json:"name"`
	Code          string    `json:"code"`
	Type          string    `json:"type"`
	Category      string    `json:"category"`
	Subject       *string   `json:"subject"`
	Content       string    `json:"content"`
	Variables     *string   `json:"variables"`
	IsActive      bool      `json:"isActive"`
	CreatedBy     string    `json:"createdBy"`
	CreatedByName string    `json:"createdByName,omitempty"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
}

// PaginatedMessageTemplates 分页消息模板响应
type PaginatedMessageTemplates struct {
	Templates []MessageTemplateResponse `json:"templates"`
	Total     int64                     `json:"total"`
	Page      int                       `json:"page"`
	Limit     int                       `json:"limit"`
}

// MessageTemplateVariable 消息模板变量
type MessageTemplateVariable struct {
	Name         string      `json:"name"`
	Label        string      `json:"label"`
	Type         string      `json:"type"`
	Required     bool        `json:"required"`
	DefaultValue interface{} `json:"defaultValue"`
	Description  string      `json:"description"`
	Format       string      `json:"format"`
	Example      string      `json:"example"`
}

// MessageTemplateSendRequest 发送消息请求
type MessageTemplateSendRequest struct {
	TemplateID   string                 `json:"templateId" binding:"required"`
	Recipients   []MessageRecipient     `json:"recipients" binding:"required,min=1"`
	Variables    map[string]interface{} `json:"variables"`
	Channels     []string               `json:"channels"`
	ScheduleTime *time.Time             `json:"scheduleTime"`
	Priority     string                 `json:"priority"`
}

// MessageRecipient 消息接收者
type MessageRecipient struct {
	UserID    string                 `json:"userId"`
	Email     string                 `json:"email"`
	Phone     string                 `json:"phone"`
	Name      string                 `json:"name"`
	Variables map[string]interface{} `json:"variables"`
}

// MessageTemplateSendResult 发送消息结果
type MessageTemplateSendResult struct {
	MessageID    string              `json:"messageId"`
	TemplateID   string              `json:"templateId"`
	TotalCount   int                 `json:"totalCount"`
	SuccessCount int                 `json:"successCount"`
	FailureCount int                 `json:"failureCount"`
	SentAt       time.Time           `json:"sentAt"`
	Results      []MessageSendResult `json:"results"`
}

// MessageSendResult 单个消息发送结果
type MessageSendResult struct {
	Recipient string    `json:"recipient"`
	Channel   string    `json:"channel"`
	Status    string    `json:"status"`
	Error     string    `json:"error"`
	SentAt    time.Time `json:"sentAt"`
}

// MessageTemplatePreviewRequest 预览消息模板请求
type MessageTemplatePreviewRequest struct {
	TemplateID string                 `json:"templateId" binding:"required"`
	Variables  map[string]interface{} `json:"variables"`
	Channel    string                 `json:"channel"`
}

// MessageTemplatePreviewResult 预览消息模板结果
type MessageTemplatePreviewResult struct {
	Title   string `json:"title"`
	Content string `json:"content"`
	Channel string `json:"channel"`
}

// MessageTemplateCopyRequest 复制消息模板请求
type MessageTemplateCopyRequest struct {
	SourceID string `json:"sourceId" binding:"required"`
	Name     string `json:"name" binding:"required,max=100"`
	Code     string `json:"code" binding:"required,max=50"`
}

// MessageTemplateSummary 消息模板汇总
type MessageTemplateSummary struct {
	Total      int            `json:"total"`
	Active     int            `json:"active"`
	Inactive   int            `json:"inactive"`
	System     int            `json:"system"`
	Custom     int            `json:"custom"`
	ByType     map[string]int `json:"byType"`
	ByCategory map[string]int `json:"byCategory"`
	ByPriority map[string]int `json:"byPriority"`
	ByChannel  map[string]int `json:"byChannel"`
}

// GetTypeName 获取消息类型名称
func GetTypeName(messageType string) string {
	switch messageType {
	case MessageTypeEmail:
		return "邮件"
	case MessageTypeSMS:
		return "短信"
	case MessageTypePush:
		return "推送"
	case MessageTypeApp:
		return "应用内消息"
	default:
		return "未知类型"
	}
}

// GetCategoryName 获取消息分类名称
func GetMessageCategoryName(category string) string {
	switch category {
	case MessageCategoryReminder:
		return "提醒类"
	case MessageCategoryNotification:
		return "通知类"
	case MessageCategoryAlert:
		return "警告类"
	case MessageCategorySystem:
		return "系统类"
	case MessageCategoryMarketing:
		return "营销类"
	case MessageCategoryCustom:
		return "自定义类"
	default:
		return "未知分类"
	}
}

// GetPriorityName 获取优先级名称
func GetPriorityName(priority string) string {
	switch priority {
	case MessagePriorityLow:
		return "低优先级"
	case MessagePriorityNormal:
		return "普通优先级"
	case MessagePriorityHigh:
		return "高优先级"
	case MessagePriorityUrgent:
		return "紧急优先级"
	default:
		return "未知优先级"
	}
}

// GetChannelName 获取渠道名称
func GetChannelName(channel string) string {
	switch channel {
	case ChannelEmail:
		return "邮件"
	case ChannelSMS:
		return "短信"
	case ChannelPush:
		return "推送"
	case ChannelApp:
		return "应用内"
	default:
		return "未知渠道"
	}
}

// IsValidMessageType 验证消息类型是否有效
func IsValidMessageType(messageType string) bool {
	validTypes := []string{
		MessageTypeEmail,
		MessageTypeSMS,
		MessageTypePush,
		MessageTypeApp,
	}

	for _, validType := range validTypes {
		if messageType == validType {
			return true
		}
	}
	return false
}

// IsValidMessageCategory 验证消息分类是否有效
func IsValidMessageCategory(category string) bool {
	validCategories := []string{
		MessageCategoryReminder,
		MessageCategoryNotification,
		MessageCategoryAlert,
		MessageCategorySystem,
		MessageCategoryMarketing,
		MessageCategoryCustom,
	}

	for _, validCategory := range validCategories {
		if category == validCategory {
			return true
		}
	}
	return false
}

// IsValidPriority 验证优先级是否有效
func IsValidPriority(priority string) bool {
	validPriorities := []string{
		MessagePriorityLow,
		MessagePriorityNormal,
		MessagePriorityHigh,
		MessagePriorityUrgent,
	}

	for _, validPriority := range validPriorities {
		if priority == validPriority {
			return true
		}
	}
	return false
}

// IsValidChannel 验证渠道是否有效
func IsValidChannel(channel string) bool {
	validChannels := []string{
		ChannelEmail,
		ChannelSMS,
		ChannelPush,
		ChannelApp,
	}

	for _, validChannel := range validChannels {
		if channel == validChannel {
			return true
		}
	}
	return false
}
