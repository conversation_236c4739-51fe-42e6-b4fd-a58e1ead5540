package model

import "time"

// StandardResponse 标准API响应结构
type StandardResponse struct {
	Code      int         `json:"code"`      // 响应状态码
	Message   string      `json:"message"`   // 响应消息
	Data      interface{} `json:"data"`      // 响应数据
	Timestamp int64       `json:"timestamp"` // 响应时间戳
	RequestID string      `json:"requestId"` // 请求ID（用于追踪）
}

// PaginatedData 分页数据结构
type PaginatedData struct {
	Items    interface{} `json:"items"`    // 数据列表，统一使用items字段
	Total    int64       `json:"total"`    // 总记录数
	Page     int         `json:"page"`     // 当前页码
	PageSize int         `json:"pageSize"` // 每页大小
	Pages    int         `json:"pages"`    // 总页数
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Code      int                    `json:"code"`      // 错误状态码
	Message   string                 `json:"message"`   // 错误消息
	Error     string                 `json:"error"`     // 详细错误信息
	Details   map[string]interface{} `json:"details"`   // 错误详情
	Timestamp int64                  `json:"timestamp"` // 错误时间戳
	RequestID string                 `json:"requestId"` // 请求ID
}

// ListResponse 列表响应结构（非分页）
type ListResponse struct {
	Items interface{} `json:"items"` // 数据列表
	Count int         `json:"count"` // 数据数量
}

// CreateResponse 创建操作响应结构
type CreateResponse struct {
	ID      string      `json:"id"`      // 创建的资源ID
	Data    interface{} `json:"data"`    // 创建的资源数据
	Message string      `json:"message"` // 创建成功消息
}

// UpdateResponse 更新操作响应结构
type UpdateResponse struct {
	ID      string      `json:"id"`      // 更新的资源ID
	Data    interface{} `json:"data"`    // 更新后的资源数据
	Message string      `json:"message"` // 更新成功消息
}

// DeleteResponse 删除操作响应结构
type DeleteResponse struct {
	ID      string `json:"id"`      // 删除的资源ID
	Message string `json:"message"` // 删除成功消息
}

// BatchResponse 批量操作响应结构
type BatchResponse struct {
	Success []string `json:"success"` // 成功处理的ID列表
	Failed  []string `json:"failed"`  // 失败处理的ID列表
	Total   int      `json:"total"`   // 总处理数量
	Message string   `json:"message"` // 批量操作消息
}

// StatsResponse 统计数据响应结构
type StatsResponse struct {
	Data    interface{} `json:"data"`    // 统计数据
	Period  string      `json:"period"`  // 统计周期
	Updated time.Time   `json:"updated"` // 更新时间
}

// HealthResponse 健康检查响应结构
type HealthResponse struct {
	Status    string                 `json:"status"`    // 服务状态：healthy, unhealthy, degraded
	Version   string                 `json:"version"`   // 服务版本
	Timestamp time.Time              `json:"timestamp"` // 检查时间
	Services  map[string]interface{} `json:"services"`  // 依赖服务状态
	Uptime    string                 `json:"uptime"`    // 运行时间
}

// 注意：FileUploadResponse 已在 file_storage.go 中定义

// ValidationError 验证错误结构
type ValidationError struct {
	Field   string `json:"field"`   // 字段名
	Message string `json:"message"` // 错误消息
	Value   string `json:"value"`   // 错误值
}

// ValidationErrorResponse 验证错误响应结构
type ValidationErrorResponse struct {
	Code      int               `json:"code"`      // 错误状态码（通常是400）
	Message   string            `json:"message"`   // 总体错误消息
	Errors    []ValidationError `json:"errors"`    // 具体验证错误列表
	Timestamp int64             `json:"timestamp"` // 错误时间戳
	RequestID string            `json:"requestId"` // 请求ID
}

// NewStandardResponse 创建标准响应
func NewStandardResponse(code int, message string, data interface{}) *StandardResponse {
	return &StandardResponse{
		Code:      code,
		Message:   message,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
}

// NewPaginatedData 创建分页数据
func NewPaginatedData(items interface{}, total int64, page, pageSize int) *PaginatedData {
	pages := int((total + int64(pageSize) - 1) / int64(pageSize))
	return &PaginatedData{
		Items:    items,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		Pages:    pages,
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(code int, message, error string, details map[string]interface{}) *ErrorResponse {
	return &ErrorResponse{
		Code:      code,
		Message:   message,
		Error:     error,
		Details:   details,
		Timestamp: time.Now().Unix(),
	}
}

// NewListResponse 创建列表响应
func NewListResponse(items interface{}, count int) *ListResponse {
	return &ListResponse{
		Items: items,
		Count: count,
	}
}

// NewValidationErrorResponse 创建验证错误响应
func NewValidationErrorResponse(message string, errors []ValidationError) *ValidationErrorResponse {
	return &ValidationErrorResponse{
		Code:      400,
		Message:   message,
		Errors:    errors,
		Timestamp: time.Now().Unix(),
	}
}

// ResponseCodes 响应状态码常量
const (
	// 成功响应码
	CodeSuccess = 200
	CodeCreated = 201

	// 客户端错误响应码
	CodeBadRequest       = 400
	CodeUnauthorized     = 401
	CodeForbidden        = 403
	CodeNotFound         = 404
	CodeMethodNotAllowed = 405
	CodeConflict         = 409
	CodeValidationFailed = 422
	CodeTooManyRequests  = 429

	// 服务器错误响应码
	CodeInternalServerError = 500
	CodeBadGateway          = 502
	CodeServiceUnavailable  = 503
	CodeGatewayTimeout      = 504
)

// ResponseMessages 响应消息常量
const (
	// 成功消息
	MsgSuccess = "操作成功"
	MsgCreated = "创建成功"
	MsgUpdated = "更新成功"
	MsgDeleted = "删除成功"

	// 错误消息
	MsgBadRequest       = "请求参数错误"
	MsgUnauthorized     = "未授权访问"
	MsgForbidden        = "禁止访问"
	MsgNotFound         = "资源不存在"
	MsgValidationFailed = "数据验证失败"
	MsgInternalError    = "服务器内部错误"
	MsgServiceBusy      = "服务繁忙，请稍后重试"
)

// HealthStatus 健康状态常量
const (
	HealthStatusHealthy   = "healthy"
	HealthStatusUnhealthy = "unhealthy"
	HealthStatusDegraded  = "degraded"
)
