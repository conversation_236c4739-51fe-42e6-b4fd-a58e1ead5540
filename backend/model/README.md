# 数据模型模块

## 概述
数据模型模块定义了系统的核心数据结构，包括实体模型（Entity）和数据传输对象（DTO）。使用GORM作为ORM框架，实现数据持久化和对象关系映射。

## 目录结构
```
model/
├── entity/     # 数据库实体模型
│   ├── user.go        # 用户实体
│   ├── company.go     # 公司实体
│   └── tax.go         # 税务实体
└── dto/        # 数据传输对象
    ├── request/       # 请求DTO
    └── response/      # 响应DTO
```

## 实体模型
1. **用户模型**
   - 基本信息（ID、用户名、密码等）
   - 角色权限
   - 关联关系

2. **公司模型**
   - 基本信息（名称、地址、联系方式等）
   - 税务信息
   - 业务数据

3. **税务模型**
   - 申报记录
   - 发票信息
   - 税收政策

## 数据传输对象
1. **请求DTO**
   - 登录请求
   - 注册请求
   - 业务操作请求

2. **响应DTO**
   - 统一响应格式
   - 数据列表响应
   - 错误响应

## 最佳实践
1. 使用结构体标签定义模型属性
2. 实现数据验证和转换方法
3. 保持DTO和实体模型的清晰分离
4. 使用合适的字段类型和索引

## 示例代码
```go
// entity/user.go
type User struct {
    ID        uint      `gorm:"primaryKey"`
    Username  string    `gorm:"size:50;not null;unique"`
    Password  string    `gorm:"size:100;not null"`
    Role      string    `gorm:"size:20;not null;default:'user'"`
    CreatedAt time.Time `gorm:"not null"`
    UpdatedAt time.Time `gorm:"not null"`
}

// dto/request/login.go
type LoginRequest struct {
    Username string `json:"username" binding:"required,min=3,max=50"`
    Password string `json:"password" binding:"required,min=6"`
}
```