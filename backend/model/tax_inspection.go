package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// TaxInspection 税务稽查管理模型
type TaxInspection struct {
	ID                        string          `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:稽查ID"`
	EnterpriseID              string          `json:"enterprise_id" gorm:"type:varchar(36);not null;column:enterprise_id;index:idx_tax_inspections_enterprise_id;comment:企业ID"`
	InspectionNumber          string          `json:"inspection_number" gorm:"type:varchar(50);not null;uniqueIndex:uk_tax_inspections_number;column:inspection_number;comment:稽查编号"`
	InspectionType            string          `json:"inspection_type" gorm:"type:enum('routine','special','complaint','random');not null;column:inspection_type;index:idx_tax_inspections_type;comment:稽查类型"`
	InspectionScope           string          `json:"inspection_scope" gorm:"type:enum('comprehensive','specific_tax','specific_period','specific_business');not null;column:inspection_scope;comment:稽查范围"`
	TaxTypes                  string          `json:"tax_types" gorm:"type:json;column:tax_types;comment:涉及税种"`
	PeriodStart               time.Time       `json:"period_start" gorm:"not null;column:period_start;comment:稽查期间开始"`
	PeriodEnd                 time.Time       `json:"period_end" gorm:"not null;column:period_end;comment:稽查期间结束"`
	Status                    string          `json:"status" gorm:"type:enum('planned','ongoing','suspended','completed','closed');default:planned;column:status;index:idx_tax_inspections_status;comment:稽查状态"`
	InspectorTeam             string          `json:"inspector_team" gorm:"type:varchar(200);column:inspector_team;comment:稽查组"`
	TeamLeader                string          `json:"team_leader" gorm:"type:varchar(100);column:team_leader;comment:组长"`
	TeamMembers               string          `json:"team_members" gorm:"type:json;column:team_members;comment:组员"`
	StartDate                 *time.Time      `json:"start_date" gorm:"type:date;column:start_date;comment:开始日期"`
	EndDate                   *time.Time      `json:"end_date" gorm:"type:date;column:end_date;comment:结束日期"`
	InspectionBasis           string          `json:"inspection_basis" gorm:"type:varchar(500);column:inspection_basis;comment:稽查依据"`
	InspectionContent         string          `json:"inspection_content" gorm:"type:text;column:inspection_content;comment:稽查内容"`
	Findings                  string          `json:"findings" gorm:"type:text;column:findings;comment:稽查发现"`
	Violations                string          `json:"violations" gorm:"type:json;column:violations;comment:违法行为"`
	PenaltyAmount             decimal.Decimal `json:"penalty_amount" gorm:"type:decimal(15,2);default:0;column:penalty_amount;comment:处罚金额"`
	AdditionalTax             decimal.Decimal `json:"additional_tax" gorm:"type:decimal(15,2);default:0;column:additional_tax;comment:补缴税款"`
	LateFee                   decimal.Decimal `json:"late_fee" gorm:"type:decimal(15,2);default:0;column:late_fee;comment:滞纳金"`
	TotalAmount               decimal.Decimal `json:"total_amount" gorm:"type:decimal(15,2);default:0;column:total_amount;comment:总金额"`
	Conclusion                string          `json:"conclusion" gorm:"type:text;column:conclusion;comment:稽查结论"`
	RectificationRequirements string          `json:"rectification_requirements" gorm:"type:text;column:rectification_requirements;comment:整改要求"`
	RectificationDeadline     *time.Time      `json:"rectification_deadline" gorm:"type:date;column:rectification_deadline;comment:整改期限"`
	RectificationStatus       string          `json:"rectification_status" gorm:"type:enum('pending','in_progress','completed','overdue');column:rectification_status;comment:整改状态"`
	AppealStatus              string          `json:"appeal_status" gorm:"type:enum('none','appealing','accepted','rejected');default:none;column:appeal_status;comment:申诉状态"`
	CreatedAt                 time.Time       `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt                 time.Time       `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	Enterprise *Enterprise `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
}

// 稽查类型常量
const (
	InspectionTypeRoutine   = "routine"
	InspectionTypeSpecial   = "special"
	InspectionTypeComplaint = "complaint"
	InspectionTypeRandom    = "random"
)

// 稽查范围常量
const (
	InspectionScopeComprehensive    = "comprehensive"
	InspectionScopeSpecificTax      = "specific_tax"
	InspectionScopeSpecificPeriod   = "specific_period"
	InspectionScopeSpecificBusiness = "specific_business"
)

// 稽查状态常量
const (
	InspectionStatusPlanned   = "planned"
	InspectionStatusOngoing   = "ongoing"
	InspectionStatusSuspended = "suspended"
	InspectionStatusCompleted = "completed"
	InspectionStatusClosed    = "closed"
)

// 整改状态常量
const (
	RectificationStatusPending    = "pending"
	RectificationStatusInProgress = "in_progress"
	RectificationStatusCompleted  = "completed"
	RectificationStatusOverdue    = "overdue"
)

// 申诉状态常量
const (
	AppealStatusNone      = "none"
	AppealStatusAppealing = "appealing"
	AppealStatusAccepted  = "accepted"
	AppealStatusRejected  = "rejected"
)

// TaxInspectionCreateRequest 创建税务稽查请求
type TaxInspectionCreateRequest struct {
	EnterpriseID     string `json:"enterprise_id" binding:"required"`
	InspectionNumber string `json:"inspection_number" binding:"required"`
	InspectionType   string `json:"inspection_type" binding:"required"`
	StartDate        string `json:"start_date" binding:"required"`
	EndDate          string `json:"end_date"`
	InspectorID      string `json:"inspector_id" binding:"required"`
	InspectorName    string `json:"inspector_name" binding:"required"`
	Scope            string `json:"scope"`
}

// TaxInspectionUpdateRequest 更新税务稽查请求
type TaxInspectionUpdateRequest struct {
	InspectionType   *string  `json:"inspection_type"`
	Status           *string  `json:"status"`
	StartDate        *string  `json:"start_date"`
	EndDate          *string  `json:"end_date"`
	InspectorID      *string  `json:"inspector_id"`
	InspectorName    *string  `json:"inspector_name"`
	Scope            *string  `json:"scope"`
	Findings         *string  `json:"findings"`
	Recommendations  *string  `json:"recommendations"`
	PenaltyAmount    *float64 `json:"penalty_amount" binding:"omitempty,gte=0"`
	AdditionalTax    *float64 `json:"additional_tax" binding:"omitempty,gte=0"`
	CompletionDate   *string  `json:"completion_date"`
	FollowUpRequired *bool    `json:"follow_up_required"`
	FollowUpDate     *string  `json:"follow_up_date"`
}

// TaxInspectionResponse 税务稽查响应
type TaxInspectionResponse struct {
	ID               string     `json:"id"`
	EnterpriseID     string     `json:"enterprise_id"`
	EnterpriseName   string     `json:"enterprise_name"`
	InspectionNumber string     `json:"inspection_number"`
	InspectionType   string     `json:"inspection_type"`
	Status           string     `json:"status"`
	StartDate        time.Time  `json:"start_date"`
	EndDate          *time.Time `json:"end_date"`
	InspectorID      string     `json:"inspector_id"`
	InspectorName    string     `json:"inspector_name"`
	Scope            string     `json:"scope"`
	Findings         string     `json:"findings"`
	Recommendations  string     `json:"recommendations"`
	PenaltyAmount    float64    `json:"penalty_amount"`
	AdditionalTax    float64    `json:"additional_tax"`
	CompletionDate   *time.Time `json:"completion_date"`
	FollowUpRequired bool       `json:"follow_up_required"`
	FollowUpDate     *time.Time `json:"follow_up_date"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
}
