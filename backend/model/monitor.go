// Package model provides data models for the tax management system monitoring functionality.
// It includes alert management, system health monitoring, and performance metrics structures.
package model

import (
	"time"
)

// AlertLevel 告警级别枚举
type AlertLevel string

const (
	AlertLevelInfo     AlertLevel = "info"
	AlertLevelWarning  AlertLevel = "warning"
	AlertLevelError    AlertLevel = "error"
	AlertLevelCritical AlertLevel = "critical"
)

// AlertStatus 告警状态枚举
type AlertStatus string

const (
	AlertStatusActive       AlertStatus = "active"
	AlertStatusAcknowledged AlertStatus = "acknowledged"
	AlertStatusResolved     AlertStatus = "resolved"
	AlertStatusClosed       AlertStatus = "closed"
)

// Alert 告警模型
type Alert struct {
	ID          string      `gorm:"primaryKey;size:50" json:"id"`
	Title       string      `gorm:"size:200;not null" json:"title"`
	Description string      `gorm:"type:text" json:"description"`
	Level       AlertLevel  `gorm:"size:20;not null;index" json:"level"`
	Status      AlertStatus `gorm:"size:20;not null;index" json:"status"`
	Source      string      `gorm:"size:100;not null" json:"source"`
	Category    string      `gorm:"size:50;not null;index" json:"category"`

	// 时间字段
	TriggeredAt    time.Time  `gorm:"not null;index" json:"triggered_at"`
	AcknowledgedAt *time.Time `json:"acknowledged_at,omitempty"`
	ResolvedAt     *time.Time `json:"resolved_at,omitempty"`
	ClosedAt       *time.Time `json:"closed_at,omitempty"`

	// 处理信息
	AcknowledgedBy string `gorm:"size:50" json:"acknowledged_by,omitempty"`
	ResolvedBy     string `gorm:"size:50" json:"resolved_by,omitempty"`
	ClosedBy       string `gorm:"size:50" json:"closed_by,omitempty"`

	// 附加信息
	Metadata    string `gorm:"type:json" json:"metadata,omitempty"`
	ActionTaken string `gorm:"type:text" json:"action_taken,omitempty"`

	CreatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName 指定表名
func (Alert) TableName() string {
	return "alerts"
}

// AlertQuery 告警查询参数
type AlertQuery struct {
	Page     int          `form:"page,default=1"`
	PageSize int          `form:"page_size,default=20"`
	Level    *AlertLevel  `form:"level"`
	Status   *AlertStatus `form:"status"`
	Source   string       `form:"source"`
	Category string       `form:"category"`
	Keyword  string       `form:"keyword"`
	OrderBy  string       `form:"order_by,default=triggered_at"`
	Order    string       `form:"order,default=desc"`
}

// SyncServiceStatus 同步服务状态
type SyncServiceStatus struct {
	IsRunning       bool       `json:"is_running"`
	LastSyncTime    *time.Time `json:"last_sync_time,omitempty"`
	NextSyncTime    *time.Time `json:"next_sync_time,omitempty"`
	SyncInterval    string     `json:"sync_interval"`
	Status          string     `json:"status"`
	Message         string     `json:"message,omitempty"`
	PendingCount    int        `json:"pending_count"`
	ProcessingCount int        `json:"processing_count"`
	SubmittedCount  int        `json:"submitted_count"`
}

// CallbackServiceStatus 回调服务状态
type CallbackServiceStatus struct {
	IsRunning        bool       `json:"is_running"`
	PendingCallbacks int        `json:"pending_callbacks"`
	FailedCallbacks  int        `json:"failed_callbacks"`
	LastProcessedAt  *time.Time `json:"last_processed_at,omitempty"`
	Status           string     `json:"status"`
	Message          string     `json:"message,omitempty"`
	PendingCount     int        `json:"pending_count"`
	SentCount        int        `json:"sent_count"`
	FailedCount      int        `json:"failed_count"`
}

// SystemHealth 系统健康状态
type SystemHealth struct {
	Status       string                 `json:"status"`
	Version      string                 `json:"version"`
	Timestamp    time.Time              `json:"timestamp"`
	Uptime       string                 `json:"uptime"`
	Services     map[string]interface{} `json:"services"`
	Database     ServiceHealth          `json:"database"`
	Redis        ServiceHealth          `json:"redis"`
	MessageQueue ServiceHealth          `json:"message_queue"`
}

// ServiceHealth 服务健康状态
type ServiceHealth struct {
	Status       string    `json:"status"`
	ResponseTime float64   `json:"response_time"`
	LastCheck    time.Time `json:"last_check"`
	Message      string    `json:"message,omitempty"`
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	Timestamp time.Time `json:"timestamp"`

	// 系统资源
	CPU    CPUMetrics    `json:"cpu"`
	Memory MemoryMetrics `json:"memory"`
	Disk   DiskMetrics   `json:"disk"`

	// 应用指标
	Requests       RequestMetrics       `json:"requests"`
	Database       DatabaseMetrics      `json:"database"`
	TaxSubmissions TaxSubmissionMetrics `json:"tax_submissions"`
}

// CPUMetrics CPU指标
type CPUMetrics struct {
	Usage     float64 `json:"usage"`
	LoadAvg1  float64 `json:"load_avg_1"`
	LoadAvg5  float64 `json:"load_avg_5"`
	LoadAvg15 float64 `json:"load_avg_15"`
}

// MemoryMetrics 内存指标
type MemoryMetrics struct {
	Total     uint64  `json:"total"`
	Used      uint64  `json:"used"`
	Available uint64  `json:"available"`
	Usage     float64 `json:"usage"`
}

// DiskMetrics 磁盘指标
type DiskMetrics struct {
	Total uint64  `json:"total"`
	Used  uint64  `json:"used"`
	Free  uint64  `json:"free"`
	Usage float64 `json:"usage"`
}

// RequestMetrics 请求指标
type RequestMetrics struct {
	Total       int64   `json:"total"`
	Success     int64   `json:"success"`
	Failed      int64   `json:"failed"`
	AvgResponse float64 `json:"avg_response"`
}

// DatabaseMetrics 数据库指标
type DatabaseMetrics struct {
	Connections int64   `json:"connections"`
	Queries     int64   `json:"queries"`
	SlowQueries int64   `json:"slow_queries"`
	AvgResponse float64 `json:"avg_response"`
}

// TaxSubmissionMetrics 税务申报指标
type TaxSubmissionMetrics struct {
	Total      int64 `json:"total"`
	Pending    int64 `json:"pending"`
	Success    int64 `json:"success"`
	Failed     int64 `json:"failed"`
	Processing int64 `json:"processing"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	Timestamp time.Time `json:"timestamp"`

	// API性能
	APIResponseTime float64 `json:"api_response_time"`
	APIThroughput   float64 `json:"api_throughput"`
	APIErrorRate    float64 `json:"api_error_rate"`

	// 数据库性能
	DBResponseTime float64 `json:"db_response_time"`
	DBConnections  int     `json:"db_connections"`
	DBSlowQueries  int     `json:"db_slow_queries"`

	// 税务申报性能
	SubmissionRate    float64 `json:"submission_rate"`
	SuccessRate       float64 `json:"success_rate"`
	AvgProcessingTime float64 `json:"avg_processing_time"`
}

// PerformanceQuery 性能指标查询参数
type PerformanceQuery struct {
	StartTime time.Time `form:"start_time"`
	EndTime   time.Time `form:"end_time"`
	Interval  string    `form:"interval,default=1h"`
	Metrics   []string  `form:"metrics"`
}
