package model

import (
	"time"
)

// EnterpriseRelationship 企业关系模型
type EnterpriseRelationship struct {
	ID                 string     `json:"id" gorm:"primaryKey;size:36;comment:主键ID"`
	ParentEnterpriseID string     `json:"parentEnterpriseId" gorm:"not null;size:36;index:idx_enterprise_relationships_parent;comment:父企业ID"`
	ChildEnterpriseID  string     `json:"childEnterpriseId" gorm:"not null;size:36;index:idx_enterprise_relationships_child;comment:子企业ID"`
	RelationshipType   string     `json:"relationshipType" gorm:"not null;size:50;index:idx_enterprise_relationships_type;comment:关系类型"`
	OwnershipRatio     *float64   `json:"ownershipRatio" gorm:"type:decimal(5,4);comment:持股比例"`
	StartDate          time.Time  `json:"startDate" gorm:"not null;type:date;comment:关系开始日期"`
	EndDate            *time.Time `json:"endDate" gorm:"type:date;comment:关系结束日期"`
	IsActive           bool       `json:"isActive" gorm:"default:true;index:idx_enterprise_relationships_active;comment:是否有效"`
	Description        string     `json:"description" gorm:"size:500;comment:关系描述"`
	CreatedAt          time.Time  `json:"createdAt" gorm:"comment:创建时间"`
	UpdatedAt          time.Time  `json:"updatedAt" gorm:"comment:更新时间"`

	// 关联关系
	ParentEnterprise *Enterprise `json:"parentEnterprise,omitempty" gorm:"foreignKey:ParentEnterpriseID;references:ID"`
	ChildEnterprise  *Enterprise `json:"childEnterprise,omitempty" gorm:"foreignKey:ChildEnterpriseID;references:ID"`
}

// 关系类型常量
const (
	RelationshipTypeSubsidiary   = "subsidiary"    // 子公司
	RelationshipTypeBranch       = "branch"        // 分公司
	RelationshipTypePartnership  = "partnership"   // 合伙关系
	RelationshipTypeJointVenture = "joint_venture" // 合资企业
	RelationshipTypeSupplier     = "supplier"      // 供应商
	RelationshipTypeCustomer     = "customer"      // 客户
	RelationshipTypeInvestor     = "investor"      // 投资方
	RelationshipTypeInvestee     = "investee"      // 被投资方
	RelationshipTypeOther        = "other"         // 其他
)

// EnterpriseRelationshipCreateRequest 创建企业关系请求
type EnterpriseRelationshipCreateRequest struct {
	ParentEnterpriseID string     `json:"parentEnterpriseId" binding:"required"`
	ChildEnterpriseID  string     `json:"childEnterpriseId" binding:"required"`
	RelationshipType   string     `json:"relationshipType" binding:"required"`
	OwnershipRatio     *float64   `json:"ownershipRatio" binding:"omitempty,min=0,max=1"`
	StartDate          time.Time  `json:"startDate" binding:"required"`
	EndDate            *time.Time `json:"endDate"`
	Description        string     `json:"description"`
}

// EnterpriseRelationshipUpdateRequest 更新企业关系请求
type EnterpriseRelationshipUpdateRequest struct {
	RelationshipType *string    `json:"relationshipType"`
	OwnershipRatio   *float64   `json:"ownershipRatio" binding:"omitempty,min=0,max=1"`
	StartDate        *time.Time `json:"startDate"`
	EndDate          *time.Time `json:"endDate"`
	IsActive         *bool      `json:"isActive"`
	Description      *string    `json:"description"`
}

// EnterpriseRelationshipFilter 企业关系过滤条件
type EnterpriseRelationshipFilter struct {
	ParentEnterpriseID string     `json:"parentEnterpriseId"`
	ChildEnterpriseID  string     `json:"childEnterpriseId"`
	RelationshipType   string     `json:"relationshipType"`
	IsActive           *bool      `json:"isActive"`
	StartDate          *time.Time `json:"startDate"`
	EndDate            *time.Time `json:"endDate"`
}

// EnterpriseRelationshipResponse 企业关系响应
type EnterpriseRelationshipResponse struct {
	ID                   string     `json:"id"`
	ParentEnterpriseID   string     `json:"parentEnterpriseId"`
	ParentEnterpriseName string     `json:"parentEnterpriseName"`
	ChildEnterpriseID    string     `json:"childEnterpriseId"`
	ChildEnterpriseName  string     `json:"childEnterpriseName"`
	RelationshipType     string     `json:"relationshipType"`
	RelationshipTypeName string     `json:"relationshipTypeName"`
	OwnershipRatio       *float64   `json:"ownershipRatio"`
	StartDate            time.Time  `json:"startDate"`
	EndDate              *time.Time `json:"endDate"`
	IsActive             bool       `json:"isActive"`
	Description          string     `json:"description"`
	CreatedAt            time.Time  `json:"createdAt"`
	UpdatedAt            time.Time  `json:"updatedAt"`
}

// PaginatedEnterpriseRelationships 分页企业关系响应
type PaginatedEnterpriseRelationships struct {
	Relationships []EnterpriseRelationshipResponse `json:"relationships"`
	Total         int64                            `json:"total"`
	Page          int                              `json:"page"`
	Limit         int                              `json:"limit"`
}

// EnterpriseRelationshipTree 企业关系树
type EnterpriseRelationshipTree struct {
	Enterprise   EnterpriseBasicInfo             `json:"enterprise"`
	Children     []EnterpriseRelationshipTree    `json:"children"`
	Relationship *EnterpriseRelationshipResponse `json:"relationship,omitempty"`
	Level        int                             `json:"level"`
}

// EnterpriseBasicInfo 企业基本信息
type EnterpriseBasicInfo struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	CreditCode string `json:"creditCode"`
	Status     string `json:"status"`
}

// EnterpriseRelationshipSummary 企业关系汇总
type EnterpriseRelationshipSummary struct {
	EnterpriseID   string                           `json:"enterpriseId"`
	EnterpriseName string                           `json:"enterpriseName"`
	TotalChildren  int                              `json:"totalChildren"`
	ActiveChildren int                              `json:"activeChildren"`
	TotalParents   int                              `json:"totalParents"`
	ActiveParents  int                              `json:"activeParents"`
	ByType         map[string]int                   `json:"byType"`
	RecentChanges  []EnterpriseRelationshipResponse `json:"recentChanges"`
}

// EnterpriseRelationshipBatchCreateRequest 批量创建企业关系请求
type EnterpriseRelationshipBatchCreateRequest struct {
	Relationships []EnterpriseRelationshipCreateRequest `json:"relationships" binding:"required,min=1"`
}

// EnterpriseRelationshipValidationResult 企业关系验证结果
type EnterpriseRelationshipValidationResult struct {
	IsValid      bool     `json:"isValid"`
	Errors       []string `json:"errors"`
	Warnings     []string `json:"warnings"`
	CircularRefs []string `json:"circularRefs"`
}

// GetRelationshipTypeName 获取关系类型名称
func GetRelationshipTypeName(relationshipType string) string {
	switch relationshipType {
	case RelationshipTypeSubsidiary:
		return "子公司"
	case RelationshipTypeBranch:
		return "分公司"
	case RelationshipTypePartnership:
		return "合伙关系"
	case RelationshipTypeJointVenture:
		return "合资企业"
	case RelationshipTypeSupplier:
		return "供应商"
	case RelationshipTypeCustomer:
		return "客户"
	case RelationshipTypeInvestor:
		return "投资方"
	case RelationshipTypeInvestee:
		return "被投资方"
	case RelationshipTypeOther:
		return "其他"
	default:
		return "未知"
	}
}

// IsValidRelationshipType 验证关系类型是否有效
func IsValidRelationshipType(relationshipType string) bool {
	validTypes := []string{
		RelationshipTypeSubsidiary,
		RelationshipTypeBranch,
		RelationshipTypePartnership,
		RelationshipTypeJointVenture,
		RelationshipTypeSupplier,
		RelationshipTypeCustomer,
		RelationshipTypeInvestor,
		RelationshipTypeInvestee,
		RelationshipTypeOther,
	}

	for _, validType := range validTypes {
		if relationshipType == validType {
			return true
		}
	}
	return false
}

// TableName 指定表名
func (EnterpriseRelationship) TableName() string {
	return "enterprise_relationships"
}
