package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// TaxFilingSubmissionStatus 申报状态枚举
type TaxFilingSubmissionStatus string

const (
	TaxFilingStatusPending    TaxFilingSubmissionStatus = "pending"
	TaxFilingStatusProcessing TaxFilingSubmissionStatus = "processing"
	TaxFilingStatusSubmitted  TaxFilingSubmissionStatus = "submitted"
	TaxFilingStatusAccepted   TaxFilingSubmissionStatus = "accepted"
	TaxFilingStatusRejected   TaxFilingSubmissionStatus = "rejected"
	TaxFilingStatusFailed     TaxFilingSubmissionStatus = "failed"
	TaxFilingStatusCancelled  TaxFilingSubmissionStatus = "cancelled"
)

// TaxFilingSubmissionType 申报类型枚举
type TaxFilingSubmissionType string

const (
	TaxFilingTypeSingle TaxFilingSubmissionType = "single"
	TaxFilingTypeBatch  TaxFilingSubmissionType = "batch"
)

// TaxFilingPeriodType 申报期间类型枚举
type TaxFilingPeriodType string

const (
	TaxFilingPeriodMonthly   TaxFilingPeriodType = "monthly"
	TaxFilingPeriodQuarterly TaxFilingPeriodType = "quarterly"
	TaxFilingPeriodYearly    TaxFilingPeriodType = "yearly"
)

// TaxFilingPriority 申报优先级枚举
type TaxFilingPriority int

const (
	TaxFilingPriorityNormal TaxFilingPriority = 0
	TaxFilingPriorityHigh   TaxFilingPriority = 1
	TaxFilingPriorityUrgent TaxFilingPriority = 2
)

// TaxData 税务数据结构
type TaxData struct {
	TaxType       string  `json:"tax_type"`       // 税种类型
	TaxableAmount float64 `json:"taxable_amount"` // 应税金额
	TaxRate       float64 `json:"tax_rate"`       // 税率
	TaxAmount     float64 `json:"tax_amount"`     // 税额
	Deductions    float64 `json:"deductions"`     // 扣除额
	Credits       float64 `json:"credits"`        // 抵免额
	FinalAmount   float64 `json:"final_amount"`   // 最终应缴税额
}

// TaxDataArray 税务数据数组类型
type TaxDataArray []TaxData

// Value 实现 driver.Valuer 接口
func (t TaxDataArray) Value() (driver.Value, error) {
	if len(t) == 0 {
		return nil, nil
	}
	return json.Marshal(t)
}

// Scan 实现 sql.Scanner 接口
func (t *TaxDataArray) Scan(value interface{}) error {
	if value == nil {
		*t = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into TaxDataArray", value)
	}

	return json.Unmarshal(bytes, t)
}

// AdditionalData 附加数据类型
type AdditionalData map[string]interface{}

// Value 实现 driver.Valuer 接口
func (a AdditionalData) Value() (driver.Value, error) {
	if len(a) == 0 {
		return nil, nil
	}
	return json.Marshal(a)
}

// Scan 实现 sql.Scanner 接口
func (a *AdditionalData) Scan(value interface{}) error {
	if value == nil {
		*a = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into AdditionalData", value)
	}

	return json.Unmarshal(bytes, a)
}

// TaxFilingSubmission 税务申报记录模型
type TaxFilingSubmission struct {
	ID         string  `gorm:"primaryKey;size:50" json:"id"`
	ExternalID *string `gorm:"uniqueIndex;size:100" json:"external_id,omitempty"`

	// 关联信息
	EnterpriseID string `gorm:"size:50;not null;index" json:"enterprise_id"`
	ProvinceCode string `gorm:"size:10;not null;index" json:"province_code"`
	ProvinceName string `gorm:"size:50;not null" json:"province_name"`

	// 申报类型和批次
	SubmissionType TaxFilingSubmissionType   `gorm:"size:20;not null;default:single" json:"submission_type"`
	BatchID        *string                   `gorm:"size:50;index" json:"batch_id,omitempty"`
	Status         TaxFilingSubmissionStatus `gorm:"size:20;not null;default:pending;index" json:"status"`
	Priority       TaxFilingPriority         `gorm:"not null;default:0" json:"priority"`

	// 公司信息
	CompanyName         string  `gorm:"size:200;not null" json:"company_name"`
	TaxID               string  `gorm:"size:50;not null" json:"tax_id"`
	RegistrationNumber  string  `gorm:"size:50;not null" json:"registration_number"`
	LegalRepresentative string  `gorm:"size:100;not null" json:"legal_representative"`
	CompanyAddress      string  `gorm:"type:text;not null" json:"company_address"`
	ContactPhone        *string `gorm:"size:20" json:"contact_phone,omitempty"`
	ContactEmail        *string `gorm:"size:100" json:"contact_email,omitempty"`

	// 申报期间
	TaxYear    int                 `gorm:"not null;index:idx_tax_period" json:"tax_year"`
	TaxMonth   *int                `gorm:"index:idx_tax_period" json:"tax_month,omitempty"`
	TaxQuarter *int                `gorm:"index:idx_tax_period" json:"tax_quarter,omitempty"`
	PeriodType TaxFilingPeriodType `gorm:"size:20;not null" json:"period_type"`

	// 申报数据
	TaxData          TaxDataArray `gorm:"type:json;not null" json:"tax_data"`
	TotalTaxAmount   float64      `gorm:"type:decimal(15,2);not null;default:0.00" json:"total_tax_amount"`
	TotalFinalAmount float64      `gorm:"type:decimal(15,2);not null;default:0.00" json:"total_final_amount"`

	// 申报状态信息
	SubmittedAt  *time.Time `gorm:"index" json:"submitted_at,omitempty"`
	ProcessedAt  *time.Time `json:"processed_at,omitempty"`
	ErrorMessage *string    `gorm:"type:text" json:"error_message,omitempty"`
	RetryCount   int        `gorm:"not null;default:0" json:"retry_count"`
	MaxRetries   int        `gorm:"not null;default:3" json:"max_retries"`
	NextRetryAt  *time.Time `gorm:"index:idx_next_retry" json:"next_retry_at,omitempty"`

	// 附加信息
	Notes                   *string        `gorm:"type:text" json:"notes,omitempty"`
	AdditionalData          AdditionalData `gorm:"type:json" json:"additional_data,omitempty"`
	EstimatedProcessingTime *int           `json:"estimated_processing_time,omitempty"` // 分钟
	ReferenceNumber         *string        `gorm:"size:100" json:"reference_number,omitempty"`

	// 系统字段
	CreatedBy *string    `gorm:"size:50" json:"created_by,omitempty"`
	UpdatedBy *string    `gorm:"size:50" json:"updated_by,omitempty"`
	IsDeleted bool       `gorm:"not null;default:false" json:"is_deleted"`
	CreatedAt time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP;index" json:"created_at"`
	UpdatedAt time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty"`

	// 关联
	Enterprise *Enterprise              `gorm:"foreignKey:EnterpriseID" json:"enterprise,omitempty"`
	Province   *TaxFilingProvince       `gorm:"foreignKey:ProvinceCode;references:Code" json:"province,omitempty"`
	Batch      *TaxFilingBatch          `gorm:"foreignKey:BatchID" json:"batch,omitempty"`
	History    []TaxFilingStatusHistory `gorm:"foreignKey:SubmissionID" json:"history,omitempty"`
	Callbacks  []TaxFilingCallback      `gorm:"foreignKey:SubmissionID" json:"callbacks,omitempty"`
}

// TableName 指定表名
func (TaxFilingSubmission) TableName() string {
	return "tax_filing_submissions"
}

// BeforeCreate GORM钩子 - 创建前
func (t *TaxFilingSubmission) BeforeCreate(tx *gorm.DB) error {
	if t.ID == "" {
		t.ID = GenerateID()
	}
	return nil
}

// BeforeUpdate GORM钩子 - 更新前
func (t *TaxFilingSubmission) BeforeUpdate(tx *gorm.DB) error {
	t.UpdatedAt = time.Now()
	return nil
}

// CanRetry 检查是否可以重试
func (t *TaxFilingSubmission) CanRetry() bool {
	return t.RetryCount < t.MaxRetries &&
		(t.Status == TaxFilingStatusFailed || t.Status == TaxFilingStatusRejected)
}

// IsCompleted 检查是否已完成
func (t *TaxFilingSubmission) IsCompleted() bool {
	return t.Status == TaxFilingStatusAccepted ||
		t.Status == TaxFilingStatusCancelled ||
		(t.Status == TaxFilingStatusFailed && !t.CanRetry())
}

// GetTotalTaxAmount 计算总税额
func (t *TaxFilingSubmission) GetTotalTaxAmount() float64 {
	total := 0.0
	for _, tax := range t.TaxData {
		total += tax.TaxAmount
	}
	return total
}

// GetTotalFinalAmount 计算最终应缴税额
func (t *TaxFilingSubmission) GetTotalFinalAmount() float64 {
	total := 0.0
	for _, tax := range t.TaxData {
		total += tax.FinalAmount
	}
	return total
}

// UpdateTotals 更新总金额
func (t *TaxFilingSubmission) UpdateTotals() {
	t.TotalTaxAmount = t.GetTotalTaxAmount()
	t.TotalFinalAmount = t.GetTotalFinalAmount()
}

// TaxFilingSubmissionCreateRequest 创建申报请求
type TaxFilingSubmissionCreateRequest struct {
	EnterpriseID        string                  `json:"enterprise_id" binding:"required"`
	ProvinceCode        string                  `json:"province_code" binding:"required"`
	SubmissionType      TaxFilingSubmissionType `json:"submission_type"`
	BatchID             *string                 `json:"batch_id,omitempty"`
	Priority            TaxFilingPriority       `json:"priority"`
	CompanyName         string                  `json:"company_name" binding:"required"`
	TaxID               string                  `json:"tax_id" binding:"required"`
	RegistrationNumber  string                  `json:"registration_number" binding:"required"`
	LegalRepresentative string                  `json:"legal_representative" binding:"required"`
	CompanyAddress      string                  `json:"company_address" binding:"required"`
	ContactPhone        *string                 `json:"contact_phone,omitempty"`
	ContactEmail        *string                 `json:"contact_email,omitempty"`
	TaxYear             int                     `json:"tax_year" binding:"required"`
	TaxMonth            *int                    `json:"tax_month,omitempty"`
	TaxQuarter          *int                    `json:"tax_quarter,omitempty"`
	PeriodType          TaxFilingPeriodType     `json:"period_type" binding:"required"`
	TaxData             TaxDataArray            `json:"tax_data" binding:"required"`
	Notes               *string                 `json:"notes,omitempty"`
	AdditionalData      AdditionalData          `json:"additional_data,omitempty"`
}

// TaxFilingSubmissionUpdateRequest 更新申报请求
type TaxFilingSubmissionUpdateRequest struct {
	Status         *TaxFilingSubmissionStatus `json:"status,omitempty"`
	ExternalID     *string                    `json:"external_id,omitempty"`
	ErrorMessage   *string                    `json:"error_message,omitempty"`
	Notes          *string                    `json:"notes,omitempty"`
	AdditionalData AdditionalData             `json:"additional_data,omitempty"`
}

// TaxFilingSubmissionResponse 申报响应
type TaxFilingSubmissionResponse struct {
	TaxFilingSubmission
	StatusText string `json:"status_text"`
}

// GetStatusText 获取状态文本
func (t *TaxFilingSubmissionResponse) GetStatusText() string {
	switch t.Status {
	case TaxFilingStatusPending:
		return "待处理"
	case TaxFilingStatusProcessing:
		return "处理中"
	case TaxFilingStatusSubmitted:
		return "已提交"
	case TaxFilingStatusAccepted:
		return "已接受"
	case TaxFilingStatusRejected:
		return "已拒绝"
	case TaxFilingStatusFailed:
		return "失败"
	case TaxFilingStatusCancelled:
		return "已取消"
	default:
		return "未知状态"
	}
}
