// Package model defines data structures and database models for the tax management system.
// It includes entities for enterprise tax configuration and related data transfer objects.
package model

import (
	"backend/util"
	"errors"
	"time"

	"github.com/shopspring/decimal"
)

// EnterpriseTaxConfig 企业税务配置
type EnterpriseTaxConfig struct {
	ID                 string          `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:企业税务配置ID"`
	EnterpriseID       string          `json:"enterprise_id" gorm:"type:varchar(36);not null;column:enterprise_id;index:idx_enterprise_tax_configs_enterprise_id;comment:企业ID"`
	TaxTypeID          string          `json:"tax_type_id" gorm:"type:varchar(36);not null;column:tax_type_id;index:idx_enterprise_tax_configs_tax_type_id;comment:税种ID"`
	TaxpayerType       string          `json:"taxpayer_type" gorm:"type:enum('general','small');not null;column:taxpayer_type;comment:纳税人类型"`
	TaxRate            decimal.Decimal `json:"tax_rate" gorm:"type:decimal(5,4);not null;column:tax_rate;comment:税率"`
	CalculationMethod  string          `json:"calculation_method" gorm:"type:varchar(100);not null;column:calculation_method;comment:计税方法"`
	FilingFrequency    string          `json:"filing_frequency" gorm:"type:enum('monthly','quarterly','annually');not null;column:filing_frequency;comment:申报频率"`
	StartDate          time.Time       `json:"start_date" gorm:"not null;column:start_date;comment:开始日期"`
	EndDate            *time.Time      `json:"end_date" gorm:"column:end_date;comment:结束日期"`
	IsActive           bool            `json:"is_active" gorm:"default:true;column:is_active;comment:是否激活"`
	PreferentialPolicy string          `json:"preferential_policy" gorm:"type:varchar(500);column:preferential_policy;comment:优惠政策"`
	DeductionItems     string          `json:"deduction_items" gorm:"type:json;column:deduction_items;comment:扣除项目"`
	ExemptionAmount    decimal.Decimal `json:"exemption_amount" gorm:"type:decimal(15,2);default:0;column:exemption_amount;comment:免征额"`
	ThresholdAmount    decimal.Decimal `json:"threshold_amount" gorm:"type:decimal(15,2);default:0;column:threshold_amount;comment:起征点"`
	SpecialProvisions  string          `json:"special_provisions" gorm:"type:text;column:special_provisions;comment:特殊规定"`
	ApprovalStatus     string          `json:"approval_status" gorm:"type:enum('pending','approved','rejected');default:pending;column:approval_status;comment:审批状态"`
	ApprovedBy         *string         `json:"approved_by" gorm:"type:varchar(36);column:approved_by;comment:审批人ID"`
	ApprovedAt         *time.Time      `json:"approved_at" gorm:"column:approved_at;comment:审批时间"`
	Remarks            string          `json:"remarks" gorm:"type:varchar(500);column:remarks;comment:备注"`
	CreatedAt          time.Time       `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt          time.Time       `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	Enterprise *Enterprise `json:"enterprise,omitempty" gorm:"foreignKey:EnterpriseID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	TaxType    *TaxType    `json:"tax_type,omitempty" gorm:"foreignKey:TaxTypeID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	Approver   *User       `json:"approver,omitempty" gorm:"foreignKey:ApprovedBy;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

// TableName 指定表名
func (EnterpriseTaxConfig) TableName() string {
	return "enterprise_tax_configs"
}

// EnterpriseTaxConfigRequest 企业税务配置请求结构
type EnterpriseTaxConfigRequest struct {
	EnterpriseID       string  `json:"enterprise_id" binding:"required"`
	TaxTypeID          string  `json:"tax_type_id" binding:"required"`
	TaxpayerType       string  `json:"taxpayer_type" binding:"required,oneof=general small"`
	TaxRate            float64 `json:"tax_rate" binding:"required,gte=0,lte=1"`
	CalculationMethod  string  `json:"calculation_method" binding:"required"`
	FilingFrequency    string  `json:"filing_frequency" binding:"required,oneof=monthly quarterly annually"`
	StartDate          string  `json:"start_date" binding:"required"`
	EndDate            *string `json:"end_date"`
	PreferentialPolicy string  `json:"preferential_policy"`
	DeductionItems     string  `json:"deduction_items"`
	ExemptionAmount    float64 `json:"exemption_amount"`
	ThresholdAmount    float64 `json:"threshold_amount"`
	SpecialProvisions  string  `json:"special_provisions"`
	Remarks            string  `json:"remarks"`
}

// EnterpriseTaxConfigResponse 企业税务配置响应结构
type EnterpriseTaxConfigResponse struct {
	ID                 string          `json:"id"`
	EnterpriseID       string          `json:"enterprise_id"`
	TaxTypeID          string          `json:"tax_type_id"`
	TaxpayerType       string          `json:"taxpayer_type"`
	TaxRate            decimal.Decimal `json:"tax_rate"`
	CalculationMethod  string          `json:"calculation_method"`
	FilingFrequency    string          `json:"filing_frequency"`
	StartDate          time.Time       `json:"start_date"`
	EndDate            *time.Time      `json:"end_date"`
	IsActive           bool            `json:"is_active"`
	PreferentialPolicy string          `json:"preferential_policy"`
	DeductionItems     string          `json:"deduction_items"`
	ExemptionAmount    decimal.Decimal `json:"exemption_amount"`
	ThresholdAmount    decimal.Decimal `json:"threshold_amount"`
	SpecialProvisions  string          `json:"special_provisions"`
	ApprovalStatus     string          `json:"approval_status"`
	ApprovedBy         *string         `json:"approved_by"`
	ApprovedAt         *time.Time      `json:"approved_at"`
	Remarks            string          `json:"remarks"`
	CreatedAt          time.Time       `json:"created_at"`
	UpdatedAt          time.Time       `json:"updated_at"`
	Enterprise         *Enterprise     `json:"enterprise,omitempty"`
	TaxType            *TaxType        `json:"tax_type,omitempty"`
}

// Validate 验证企业税务配置数据
func (config *EnterpriseTaxConfig) Validate() error {
	if config.EnterpriseID == "" {
		return ErrInvalidEnterpriseID
	}

	if config.TaxTypeID == "" {
		return ErrInvalidTaxTypeID
	}

	if config.TaxpayerType != TaxpayerTypeGeneral && config.TaxpayerType != TaxpayerTypeSmall {
		return ErrInvalidTaxpayerType
	}

	if config.TaxRate.LessThan(decimal.Zero) || config.TaxRate.GreaterThan(decimal.NewFromFloat(1)) {
		return ErrInvalidTaxRate
	}

	if config.EndDate != nil && config.EndDate.Before(config.StartDate) {
		return util.ErrInvalidDateRange
	}

	return nil
}

// IsExpired 检查配置是否已过期
func (config *EnterpriseTaxConfig) IsExpired() bool {
	if config.EndDate == nil {
		return false
	}
	return time.Now().After(*config.EndDate)
}

// 错误定义
var (
	ErrInvalidEnterpriseID = errors.New("企业ID无效")
	ErrInvalidTaxTypeID    = errors.New("税种ID无效")
	ErrInvalidTaxpayerType = errors.New("纳税人类型无效")
)

// 注意：纳税人类型和申报频率常量已在 enterprise.go 和 tax_type.go 中定义，此处不重复定义

// 审批状态常量
const (
	ApprovalStatusPending  = "pending"  // 待审批
	ApprovalStatusApproved = "approved" // 已审批
	ApprovalStatusRejected = "rejected" // 已拒绝
)
