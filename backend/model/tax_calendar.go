package model

import (
	"fmt"
	"time"
)

// TaxCalendar 税务日历模型
type TaxCalendar struct {
	ID                string     `json:"id" gorm:"primaryKey;type:varchar(36);column:id;comment:主键ID"`
	TaxTypeID         string     `json:"tax_type_id" gorm:"type:varchar(36);not null;column:tax_type_id;index:idx_tax_calendars_tax_type_id;comment:税种ID"`
	PeriodType        string     `json:"period_type" gorm:"type:enum('monthly','quarterly','annually');not null;column:period_type;index:idx_tax_calendars_period_type;comment:申报周期类型"`
	PeriodYear        int        `json:"period_year" gorm:"not null;column:period_year;comment:申报年度"`
	PeriodMonth       *int       `json:"period_month" gorm:"column:period_month;comment:申报月份"`
	PeriodQuarter     *int       `json:"period_quarter" gorm:"column:period_quarter;comment:申报季度"`
	StartDate         time.Time  `json:"start_date" gorm:"not null;column:start_date;comment:申报期开始日期"`
	EndDate           time.Time  `json:"end_date" gorm:"not null;column:end_date;comment:申报期结束日期"`
	DueDate           time.Time  `json:"due_date" gorm:"not null;column:due_date;index:idx_tax_calendars_due_date;comment:申报截止日期"`
	PaymentDueDate    *time.Time `json:"payment_due_date" gorm:"column:payment_due_date;comment:缴款截止日期"`
	IsHolidayAdjusted bool       `json:"is_holiday_adjusted" gorm:"default:false;column:is_holiday_adjusted;comment:是否节假日调整"`
	Status            string     `json:"status" gorm:"type:enum('upcoming','current','overdue','completed');default:upcoming;column:status;index:idx_tax_calendars_status;comment:状态"`
	CreatedAt         time.Time  `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt         time.Time  `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`

	// 关联关系
	TaxType *TaxType `json:"tax_type,omitempty" gorm:"foreignKey:TaxTypeID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
}

// TableName 指定表名
func (TaxCalendar) TableName() string {
	return "tax_calendars"
}

// GetPeriodDescription 获取期间描述
func (tc *TaxCalendar) GetPeriodDescription() string {
	switch tc.PeriodType {
	case PeriodTypeMonthly:
		if tc.PeriodMonth != nil {
			return fmt.Sprintf("%d年%d月", tc.PeriodYear, *tc.PeriodMonth)
		}
	case PeriodTypeQuarterly:
		if tc.PeriodQuarter != nil {
			return fmt.Sprintf("%d年第%d季度", tc.PeriodYear, *tc.PeriodQuarter)
		}
	case PeriodTypeAnnually:
		return fmt.Sprintf("%d年度", tc.PeriodYear)
	}
	return ""
}

// GetDaysRemaining 获取剩余天数
func (tc *TaxCalendar) GetDaysRemaining() int {
	now := time.Now()
	if tc.DueDate.Before(now) {
		return 0
	}
	return int(tc.DueDate.Sub(now).Hours() / 24)
}

// IsOverdue 是否逾期
func (tc *TaxCalendar) IsOverdue() bool {
	return time.Now().After(tc.DueDate)
}

// UpdateStatus 更新状态
func (tc *TaxCalendar) UpdateStatus() {
	now := time.Now()

	if tc.IsOverdue() {
		tc.Status = TaxCalendarStatusOverdue
	} else if now.After(tc.StartDate) && now.Before(tc.EndDate) {
		tc.Status = TaxCalendarStatusCurrent
	} else if now.Before(tc.StartDate) {
		tc.Status = TaxCalendarStatusUpcoming
	}
}

// 税务日历状态常量
const (
	TaxCalendarStatusUpcoming  = "upcoming"
	TaxCalendarStatusCurrent   = "current"
	TaxCalendarStatusOverdue   = "overdue"
	TaxCalendarStatusCompleted = "completed"
)

// 申报周期类型常量
const (
	PeriodTypeMonthly   = "monthly"
	PeriodTypeQuarterly = "quarterly"
	PeriodTypeAnnually  = "annually"
)

// TaxCalendarCreateRequest 创建税务日历事件请求
type TaxCalendarCreateRequest struct {
	EventName   string    `json:"eventName" binding:"required"`
	EventType   string    `json:"eventType" binding:"required"`
	EventDate   time.Time `json:"eventDate" binding:"required"`
	Description *string   `json:"description"`
	UserID      *string   `json:"userId"`
}

// TaxCalendarUpdateRequest 更新税务日历事件请求
type TaxCalendarUpdateRequest struct {
	EventName   *string    `json:"eventName"`
	EventType   *string    `json:"eventType"`
	EventDate   *time.Time `json:"eventDate"`
	Description *string    `json:"description"`
	UserID      *string    `json:"userId"`
}

// TaxCalendarFilter 税务日历事件过滤条件
type TaxCalendarFilter struct {
	EventType string     `json:"eventType"`
	StartDate *time.Time `json:"startDate"`
	EndDate   *time.Time `json:"endDate"`
	UserID    string     `json:"userId"`
}

// TaxCalendarResponse 税务日历事件响应
type TaxCalendarResponse struct {
	ID          string    `json:"id"`
	EventName   string    `json:"eventName"`
	EventType   string    `json:"eventType"`
	EventDate   time.Time `json:"eventDate"`
	Description *string   `json:"description"`
	UserID      *string   `json:"userId"`
	UserName    string    `json:"userName,omitempty"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// PaginatedTaxCalendars 分页税务日历响应
type PaginatedTaxCalendars struct {
	Calendars []TaxCalendarResponse `json:"calendars"`
	Total     int64                 `json:"total"`
	Page      int                   `json:"page"`
	Limit     int                   `json:"limit"`
}

// TaxCalendarBatchCreateRequest 批量创建税务日历请求
type TaxCalendarBatchCreateRequest struct {
	TaxTypeID string                     `json:"taxTypeId" binding:"required"`
	Year      int                        `json:"year" binding:"required,min=2020,max=2050"`
	Calendars []TaxCalendarCreateRequest `json:"calendars" binding:"required,min=1"`
}

// TaxCalendarSummary 税务日历汇总
type TaxCalendarSummary struct {
	Year              int                `json:"year"`
	TotalPeriods      int                `json:"totalPeriods"`
	ActivePeriods     int                `json:"activePeriods"`
	UpcomingDeadlines []UpcomingDeadline `json:"upcomingDeadlines"`
	ByTaxType         map[string]int     `json:"byTaxType"`
	ByMonth           map[string]int     `json:"byMonth"`
}

// UpcomingDeadline 即将到期的申报期限
type UpcomingDeadline struct {
	ID              string     `json:"id"`
	TaxTypeID       string     `json:"taxTypeId"`
	TaxTypeName     string     `json:"taxTypeName"`
	Period          string     `json:"period"`
	DueDate         time.Time  `json:"dueDate"`
	ExtendedDueDate *time.Time `json:"extendedDueDate"`
	DaysRemaining   int        `json:"daysRemaining"`
	IsUrgent        bool       `json:"isUrgent"`
}
