package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// TaxFilingBatchStatus 批次状态枚举
type TaxFilingBatchStatus string

const (
	TaxFilingBatchStatusPending    TaxFilingBatchStatus = "pending"
	TaxFilingBatchStatusProcessing TaxFilingBatchStatus = "processing"
	TaxFilingBatchStatusCompleted  TaxFilingBatchStatus = "completed"
	TaxFilingBatchStatusFailed     TaxFilingBatchStatus = "failed"
	TaxFilingBatchStatusCancelled  TaxFilingBatchStatus = "cancelled"
)

// ErrorSummary 错误汇总
type ErrorSummary map[string]interface{}

// Value 实现 driver.Valuer 接口
func (e ErrorSummary) Value() (driver.Value, error) {
	if len(e) == 0 {
		return nil, nil
	}
	return json.Marshal(e)
}

// Scan 实现 sql.Scanner 接口
func (e *ErrorSummary) Scan(value interface{}) error {
	if value == nil {
		*e = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into ErrorSummary", value)
	}

	return json.Unmarshal(bytes, e)
}

// TaxFilingBatch 税务申报批次模型
type TaxFilingBatch struct {
	ID          string  `gorm:"primaryKey;size:50" json:"id"`
	Name        string  `gorm:"size:200;not null" json:"name"`
	Description *string `gorm:"type:text" json:"description,omitempty"`

	// 关联信息
	ProvinceCode string `gorm:"size:10;not null;index" json:"province_code"`
	EnterpriseID string `gorm:"size:50;not null;index" json:"enterprise_id"`

	// 批次状态
	Status TaxFilingBatchStatus `gorm:"size:20;not null;default:pending;index" json:"status"`

	// 统计信息
	TotalSubmissions      int `gorm:"not null;default:0" json:"total_submissions"`
	SuccessfulSubmissions int `gorm:"not null;default:0" json:"successful_submissions"`
	FailedSubmissions     int `gorm:"not null;default:0" json:"failed_submissions"`
	ProcessingSubmissions int `gorm:"not null;default:0" json:"processing_submissions"`

	// 时间信息
	StartedAt           *time.Time `json:"started_at,omitempty"`
	CompletedAt         *time.Time `json:"completed_at,omitempty"`
	EstimatedCompletion *time.Time `json:"estimated_completion,omitempty"`

	// 进度信息
	ProgressPercentage float64 `gorm:"type:decimal(5,2);not null;default:0.00" json:"progress_percentage"`

	// 错误汇总
	ErrorSummary ErrorSummary `gorm:"type:json" json:"error_summary,omitempty"`

	// 系统字段
	CreatedBy *string    `gorm:"size:50" json:"created_by,omitempty"`
	UpdatedBy *string    `gorm:"size:50" json:"updated_by,omitempty"`
	IsDeleted bool       `gorm:"not null;default:false" json:"is_deleted"`
	CreatedAt time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP;index" json:"created_at"`
	UpdatedAt time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty"`

	// 关联
	Enterprise  *Enterprise           `gorm:"foreignKey:EnterpriseID" json:"enterprise,omitempty"`
	Province    *TaxFilingProvince    `gorm:"foreignKey:ProvinceCode;references:Code" json:"province,omitempty"`
	Submissions []TaxFilingSubmission `gorm:"foreignKey:BatchID" json:"submissions,omitempty"`
}

// TableName 指定表名
func (TaxFilingBatch) TableName() string {
	return "tax_filing_batches"
}

// BeforeCreate GORM钩子 - 创建前
func (b *TaxFilingBatch) BeforeCreate(tx *gorm.DB) error {
	if b.ID == "" {
		b.ID = GenerateID()
	}
	return nil
}

// BeforeUpdate GORM钩子 - 更新前
func (b *TaxFilingBatch) BeforeUpdate(tx *gorm.DB) error {
	b.UpdatedAt = time.Now()
	return nil
}

// IsCompleted 检查批次是否已完成
func (b *TaxFilingBatch) IsCompleted() bool {
	return b.Status == TaxFilingBatchStatusCompleted ||
		b.Status == TaxFilingBatchStatusFailed ||
		b.Status == TaxFilingBatchStatusCancelled
}

// GetSuccessRate 获取成功率
func (b *TaxFilingBatch) GetSuccessRate() float64 {
	if b.TotalSubmissions == 0 {
		return 0.0
	}
	return float64(b.SuccessfulSubmissions) / float64(b.TotalSubmissions) * 100
}

// GetFailureRate 获取失败率
func (b *TaxFilingBatch) GetFailureRate() float64 {
	if b.TotalSubmissions == 0 {
		return 0.0
	}
	return float64(b.FailedSubmissions) / float64(b.TotalSubmissions) * 100
}

// UpdateProgress 更新进度
func (b *TaxFilingBatch) UpdateProgress() {
	if b.TotalSubmissions == 0 {
		b.ProgressPercentage = 0.0
		return
	}

	completed := b.SuccessfulSubmissions + b.FailedSubmissions
	b.ProgressPercentage = float64(completed) / float64(b.TotalSubmissions) * 100

	// 如果全部完成，更新状态
	if completed == b.TotalSubmissions {
		if b.FailedSubmissions == 0 {
			b.Status = TaxFilingBatchStatusCompleted
		} else if b.SuccessfulSubmissions == 0 {
			b.Status = TaxFilingBatchStatusFailed
		} else {
			b.Status = TaxFilingBatchStatusCompleted // 部分成功也算完成
		}

		now := time.Now()
		b.CompletedAt = &now
	}
}

// TaxFilingBatchCreateRequest 创建批次请求
type TaxFilingBatchCreateRequest struct {
	Name         string  `json:"name" binding:"required"`
	Description  *string `json:"description,omitempty"`
	ProvinceCode string  `json:"province_code" binding:"required"`
	EnterpriseID string  `json:"enterprise_id" binding:"required"`
}

// TaxFilingBatchUpdateRequest 更新批次请求
type TaxFilingBatchUpdateRequest struct {
	Name        *string               `json:"name,omitempty"`
	Description *string               `json:"description,omitempty"`
	Status      *TaxFilingBatchStatus `json:"status,omitempty"`
}

// TaxFilingBatchResponse 批次响应
type TaxFilingBatchResponse struct {
	TaxFilingBatch
	StatusText     string  `json:"status_text"`
	SuccessRate    float64 `json:"success_rate"`
	FailureRate    float64 `json:"failure_rate"`
	EstimatedTime  *int    `json:"estimated_time,omitempty"`  // 预计剩余时间(分钟)
	ProcessingTime *int    `json:"processing_time,omitempty"` // 已处理时间(分钟)
}

// GetStatusText 获取状态文本
func (b *TaxFilingBatchResponse) GetStatusText() string {
	switch b.Status {
	case TaxFilingBatchStatusPending:
		return "待处理"
	case TaxFilingBatchStatusProcessing:
		return "处理中"
	case TaxFilingBatchStatusCompleted:
		return "已完成"
	case TaxFilingBatchStatusFailed:
		return "失败"
	case TaxFilingBatchStatusCancelled:
		return "已取消"
	default:
		return "未知状态"
	}
}

// GetProcessingTime 获取处理时间(分钟)
func (b *TaxFilingBatchResponse) GetProcessingTime() *int {
	if b.StartedAt == nil {
		return nil
	}

	endTime := time.Now()
	if b.CompletedAt != nil {
		endTime = *b.CompletedAt
	}

	duration := endTime.Sub(*b.StartedAt)
	minutes := int(duration.Minutes())
	return &minutes
}

// TaxFilingBatchListRequest 批次列表请求
type TaxFilingBatchListRequest struct {
	EnterpriseID string                `form:"enterprise_id"`
	ProvinceCode string                `form:"province_code"`
	Status       *TaxFilingBatchStatus `form:"status"`
	Keyword      string                `form:"keyword"`
	StartTime    *time.Time            `form:"start_time"`
	EndTime      *time.Time            `form:"end_time"`
	Page         int                   `form:"page,default=1"`
	PageSize     int                   `form:"page_size,default=20"`
	OrderBy      string                `form:"order_by,default=created_at"`
	Order        string                `form:"order,default=desc"`
}

// TaxFilingBatchListResponse 批次列表响应
type TaxFilingBatchListResponse struct {
	List       []TaxFilingBatchResponse `json:"list"`
	Total      int64                    `json:"total"`
	Page       int                      `json:"page"`
	PageSize   int                      `json:"page_size"`
	TotalPages int                      `json:"total_pages"`
}

// TaxFilingBatchStatistics 批次统计
type TaxFilingBatchStatistics struct {
	TotalBatches          int64   `json:"total_batches"`
	CompletedBatches      int64   `json:"completed_batches"`
	ProcessingBatches     int64   `json:"processing_batches"`
	FailedBatches         int64   `json:"failed_batches"`
	AverageSuccessRate    float64 `json:"average_success_rate"`
	AverageProcessingTime float64 `json:"average_processing_time"` // 分钟
}

// TaxFilingBatchProgress 批次进度信息
type TaxFilingBatchProgress struct {
	BatchID               string     `json:"batch_id"`
	BatchName             string     `json:"batch_name"`
	Status                string     `json:"status"`
	TotalSubmissions      int        `json:"total_submissions"`
	SuccessfulSubmissions int        `json:"successful_submissions"`
	FailedSubmissions     int        `json:"failed_submissions"`
	ProcessingSubmissions int        `json:"processing_submissions"`
	ProgressPercentage    float64    `json:"progress_percentage"`
	StartedAt             *time.Time `json:"started_at,omitempty"`
	EstimatedCompletion   *time.Time `json:"estimated_completion,omitempty"`
	LastUpdated           time.Time  `json:"last_updated"`
}

// CreateBatchProgress 创建批次进度信息
func (b *TaxFilingBatch) CreateBatchProgress() *TaxFilingBatchProgress {
	return &TaxFilingBatchProgress{
		BatchID:               b.ID,
		BatchName:             b.Name,
		Status:                string(b.Status),
		TotalSubmissions:      b.TotalSubmissions,
		SuccessfulSubmissions: b.SuccessfulSubmissions,
		FailedSubmissions:     b.FailedSubmissions,
		ProcessingSubmissions: b.ProcessingSubmissions,
		ProgressPercentage:    b.ProgressPercentage,
		StartedAt:             b.StartedAt,
		EstimatedCompletion:   b.EstimatedCompletion,
		LastUpdated:           b.UpdatedAt,
	}
}
