# 测试环境配置
# Test Environment Configuration

# Application settings
app:
  name: SMEasyTax-Test
  port: 8081
  debug: true
  readTimeout: 10  # seconds
  writeTimeout: 30 # seconds
  idleTimeout: 120 # seconds
  timezone: Asia/Shanghai

# Database settings - 测试环境数据库
database:
  driver: mysql
  host: localhost
  port: 3306
  user: root
  password: Aa123456@
  name: smeasy_tax_test  # 测试环境数据库
  maxOpenConns: 10       # 测试环境连接数最少
  maxIdleConns: 2
  connMaxLifetime: 300   # seconds
  autoMigrate: true      # 测试环境自动迁移

# Redis settings - 测试环境Redis
redis:
  host: localhost
  port: 6379
  password: ""
  db: 2  # 使用db2，避免与其他环境冲突

# JWT settings - 测试环境JWT配置
auth:
  jwtSecret: test-jwt-secret-key-2024
  accessTokenDuration: 240   # 测试环境token有效期更长
  refreshTokenDuration: 1    # days

# Storage settings - 测试环境存储
storage:
  type: local
  path: ./storage/test
  accessKey: ""
  secretKey: ""
  bucket: ""
  region: ""

# CORS settings - 测试环境CORS配置
cors:
  allowOrigins:
    - http://localhost:3000
    - http://localhost:8000
    - http://localhost:8080
    - http://test.local
  allowMethods:
    - GET
    - POST
    - PUT
    - PATCH
    - DELETE
    - OPTIONS
  allowHeaders:
    - Origin
    - Content-Type
    - Accept
    - Authorization
    - X-Test-Header
  exposeHeaders:
    - Content-Length
  allowCredentials: true
  maxAge: 86400

# Email service - 测试环境邮件服务（使用阿里云邮件推送）
email:
  provider: aliyun
  accessKeyID: ${ALIYUN_ACCESS_KEY_ID}  # 从环境变量获取
  accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET}  # 从环境变量获取
  regionID: cn-hangzhou
  endpoint: dm.aliyuncs.com
  fromAddress: <EMAIL>  # 需要在阿里云配置的发信地址
  fromName: 税易通系统（测试环境）
  replyToAddress: <EMAIL>
  # SMTP fallback configuration (for testing)
  smtpHost: smtp.mailtrap.io
  smtpPort: 587
  smtpUsername: <EMAIL>
  smtpPassword: test-email-password
  smtpUseTLS: true

# Third party services - 测试环境第三方服务（使用Mock服务）
thirdParty:
  # Tax bureau API - Mock服务
  taxBureau:
    baseURL: http://localhost:9001/mock/tax-bureau
    apiKey: test-tax-api-key
    apiSecret: test-tax-api-secret
    authMethod: bearer

  # Electronic invoice API - Mock服务
  eInvoiceAPI:
    baseURL: http://localhost:9002/mock/einvoice
    apiKey: test-einvoice-api-key
    apiSecret: test-einvoice-api-secret
    authMethod: basic

  # OCR service - Mock服务
  ocrService:
    baseURL: http://localhost:9003/mock/ocr
    apiKey: test-ocr-api-key

  # Bank API - Mock服务
  bankAPI:
    baseURL: http://localhost:9004/mock/bank
    appID: test-bank-app-id
    appSecret: test-bank-app-secret
    authMethod: oauth2

# 日志配置 - 测试环境
logging:
  level: debug
  format: text
  output: stdout
  file: ./logs/test.log

# 监控配置 - 测试环境
monitoring:
  enabled: false  # 测试环境关闭监控
  endpoint: ""
  interval: 30

# 缓存配置 - 测试环境
cache:
  enabled: false  # 测试环境关闭缓存，确保数据一致性
  ttl: 60
  maxSize: 100
