// Package config provides configuration management for the tax management system.
// It supports loading configuration from YAML files and environment variables with default values.
package config

import (
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	App      AppConfig
	Database DatabaseConfig
	Redis    RedisConfig
	Storage  StorageConfig
	Auth     AuthConfig
	Email    EmailConfig
	Aliyun   AliyunConfig
}

// AppConfig represents the application configuration
type AppConfig struct {
	Name         string
	Version      string
	Port         string
	Debug        bool
	TimeZone     string
	ReadTimeout  int
	WriteTimeout int
	IdleTimeout  int
}

// DatabaseConfig represents the database configuration
type DatabaseConfig struct {
	Driver          string
	Host            string
	Port            int
	User            string
	Password        string
	Name            string
	MaxOpenConns    int
	MaxIdleConns    int
	ConnMaxLifetime int
	AutoMigrate     bool
}

// RedisConfig represents the Redis configuration
type RedisConfig struct {
	Host     string
	Port     string
	Password string
	DB       int
}

// StorageConfig represents the file storage configuration
type StorageConfig struct {
	Type      string // "local" or "s3"
	Path      string // Local storage path
	AccessKey string // S3 access key
	SecretKey string // S3 secret key
	Bucket    string // S3 bucket name
	Region    string // S3 region
}

// AuthConfig represents the authentication configuration
type AuthConfig struct {
	JWTSecret            string
	AccessTokenDuration  int // in minutes
	RefreshTokenDuration int // in days
}

// EmailConfig represents the email service configuration
type EmailConfig struct {
	Provider        string // "aliyun" or "smtp"
	AccessKeyID     string // Aliyun Access Key ID
	AccessKeySecret string // Aliyun Access Key Secret
	RegionID        string // Aliyun Region ID (e.g., "cn-hangzhou")
	Endpoint        string // Aliyun Direct Mail endpoint
	FromAddress     string // Sender email address
	FromName        string // Sender name
	ReplyToAddress  string // Reply-to email address
	// SMTP fallback configuration
	SMTPHost     string
	SMTPPort     int
	SMTPUsername string
	SMTPPassword string
	SMTPUseTLS   bool
}

// AliyunConfig represents the Alibaba Cloud services configuration
type AliyunConfig struct {
	AccessKeyID     string    // Aliyun Access Key ID
	AccessKeySecret string    // Aliyun Access Key Secret
	RegionID        string    // Aliyun Region ID (e.g., "cn-hangzhou")
	OCR             OCRConfig // OCR service configuration
}

// OCRConfig represents the OCR service configuration
type OCRConfig struct {
	Endpoint string // OCR service endpoint
	Timeout  int    // Request timeout in seconds
}

// LoadConfig loads the configuration from file based on environment
func LoadConfig(path string) (Config, error) {
	var config Config

	// 获取环境变量，默认为开发环境
	env := getEnvironment()
	log.Printf("Loading configuration for environment: %s", env)

	// 根据环境选择配置文件
	configName := getConfigFileName(env)
	viper.SetConfigName(configName)
	viper.SetConfigType("yaml")

	if path != "" {
		viper.AddConfigPath(path)
	} else {
		viper.AddConfigPath(".")
		viper.AddConfigPath("./config")
		viper.AddConfigPath("../config")
	}

	// Set default values
	setDefaults()

	// Environment variables override
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	// 手动绑定重要的环境变量
	viper.BindEnv("auth.jwtSecret", "JWT_SECRET")
	viper.BindEnv("database.password", "DATABASE_PASSWORD", "DB_PASSWORD")
	viper.BindEnv("database.host", "DATABASE_HOST", "DB_HOST")
	viper.BindEnv("database.port", "DATABASE_PORT", "DB_PORT")
	viper.BindEnv("database.user", "DATABASE_USER", "DB_USER")
	viper.BindEnv("database.name", "DATABASE_NAME", "DB_NAME")
	viper.BindEnv("redis.host", "REDIS_HOST")
	viper.BindEnv("redis.port", "REDIS_PORT")
	viper.BindEnv("redis.password", "REDIS_PASSWORD")
	viper.BindEnv("redis.db", "REDIS_DB")
	viper.BindEnv("aliyun.accessKeyID", "ALIYUN_ACCESS_KEY_ID")
	viper.BindEnv("aliyun.accessKeySecret", "ALIYUN_ACCESS_KEY_SECRET")
	viper.BindEnv("aliyun.regionID", "ALIYUN_REGION_ID")
	viper.BindEnv("aliyun.ocr.endpoint", "ALIYUN_OCR_ENDPOINT")

	if err := viper.ReadInConfig(); err != nil {
		// Config file not found; use defaults and environment variables
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return config, fmt.Errorf("error reading config file: %w", err)
		}
		log.Printf("Config file %s not found, using defaults and environment variables", configName)
	} else {
		log.Printf("Using config file: %s", viper.ConfigFileUsed())
	}

	if err := viper.Unmarshal(&config); err != nil {
		return config, fmt.Errorf("unable to decode config: %w", err)
	}

	// 验证生产环境必需的配置
	if err := validateConfig(env, &config); err != nil {
		return config, fmt.Errorf("config validation failed: %w", err)
	}

	return config, nil
}

// getEnvironment 获取当前环境
func getEnvironment() string {
	env := os.Getenv("APP_ENV")
	if env == "" {
		env = os.Getenv("GO_ENV")
	}
	if env == "" {
		env = "dev" // 默认开发环境
	}
	return env
}

// getConfigFileName 根据环境获取配置文件名
func getConfigFileName(env string) string {
	switch env {
	case "prod", "production":
		return "config.prod"
	case "test", "testing":
		return "config.test"
	case "dev", "development":
		return "config.dev"
	default:
		return "config.dev" // 默认使用开发环境配置
	}
}

// validateConfig 验证配置的有效性
func validateConfig(env string, config *Config) error {
	// 生产环境必须配置的项目
	if env == "prod" || env == "production" {
		if config.Auth.JWTSecret == "" {
			return fmt.Errorf("JWT secret is required in production environment")
		}
		if config.Database.Password == "" {
			return fmt.Errorf("database password is required in production environment")
		}
		if config.Aliyun.AccessKeyID == "" {
			return fmt.Errorf("Aliyun Access Key ID is required in production environment")
		}
		if config.Aliyun.AccessKeySecret == "" {
			return fmt.Errorf("Aliyun Access Key Secret is required in production environment")
		}
		if config.App.Debug {
			log.Println("Warning: Debug mode is enabled in production environment")
		}
	}
	return nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// 获取当前环境
	env := getEnvironment()

	// App defaults
	viper.SetDefault("app.name", "Tax Backend API")
	viper.SetDefault("app.version", "1.0.0")
	viper.SetDefault("app.port", "8081")
	viper.SetDefault("app.timezone", "Asia/Shanghai")

	// 根据环境设置不同的默认值
	if env == "prod" || env == "production" {
		// 生产环境默认值
		viper.SetDefault("app.debug", false)
		viper.SetDefault("app.readTimeout", 30)
		viper.SetDefault("app.writeTimeout", 60)
		viper.SetDefault("app.idleTimeout", 300)
		viper.SetDefault("database.name", "smeasy_tax")
		viper.SetDefault("database.maxOpenConns", 100)
		viper.SetDefault("database.maxIdleConns", 20)
		viper.SetDefault("database.autoMigrate", false)
		viper.SetDefault("auth.accessTokenDuration", 60)
		viper.SetDefault("redis.db", 0)
	} else {
		// 开发/测试环境默认值
		viper.SetDefault("app.debug", true)
		viper.SetDefault("app.readTimeout", 10)
		viper.SetDefault("app.writeTimeout", 30)
		viper.SetDefault("app.idleTimeout", 120)
		viper.SetDefault("database.name", "smeasy_tax_dev")
		viper.SetDefault("database.maxOpenConns", 20)
		viper.SetDefault("database.maxIdleConns", 5)
		viper.SetDefault("database.autoMigrate", true)
		viper.SetDefault("auth.accessTokenDuration", 120)
		viper.SetDefault("redis.db", 1)
	}

	// 通用默认值
	viper.SetDefault("database.driver", "mysql")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 3306)
	viper.SetDefault("database.user", "root")
	viper.SetDefault("database.password", "Aa123456@")
	viper.SetDefault("database.connMaxLifetime", 3600)

	// Redis defaults
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", "6379")
	viper.SetDefault("redis.password", "")

	// Storage defaults
	viper.SetDefault("storage.type", "local")
	viper.SetDefault("storage.path", "./storage")
	viper.SetDefault("storage.accessKey", "")
	viper.SetDefault("storage.secretKey", "")
	viper.SetDefault("storage.bucket", "")
	viper.SetDefault("storage.region", "")

	// Email service defaults
	viper.SetDefault("email.provider", "aliyun")
	viper.SetDefault("email.regionID", "cn-hangzhou")
	viper.SetDefault("email.endpoint", "dm.aliyuncs.com")
	viper.SetDefault("email.fromName", "税易通系统")
	viper.SetDefault("email.replyToAddress", true)
	viper.SetDefault("email.smtpPort", 587)
	viper.SetDefault("email.smtpUseTLS", true)

	// Auth defaults
	viper.SetDefault("auth.jwtSecret", "taxappsecret")
	viper.SetDefault("auth.refreshTokenDuration", 30) // 30 days

	// Aliyun OCR defaults
	viper.SetDefault("aliyun.regionID", "cn-hangzhou")
	viper.SetDefault("aliyun.ocr.endpoint", "ocr.cn-hangzhou.aliyuncs.com")
	viper.SetDefault("aliyun.ocr.timeout", 30) // 30 seconds
}
