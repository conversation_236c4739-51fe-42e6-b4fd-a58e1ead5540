package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"backend/mq"
)

// GetRocketMQConfig 获取RocketMQ配置
func GetRocketMQConfig() *mq.RocketMQConfig {
	return &mq.RocketMQConfig{
		NameServers:    getNameServers(),
		ProducerGroup:  getEnvOrDefault("ROCKETMQ_PRODUCER_GROUP", "tax_filing_producer_group"),
		ConsumerGroup:  getEnvOrDefault("ROCKETMQ_CONSUMER_GROUP", "tax_filing_consumer_group"),
		Namespace:      getEnvOrDefault("ROCKETMQ_NAMESPACE", ""),
		AccessKey:      getEnvOrDefault("ROCKETMQ_ACCESS_KEY", ""),
		SecretKey:      getEnvOrDefault("ROCKETMQ_SECRET_KEY", ""),
		SecurityToken:  getEnvOrDefault("ROCKETMQ_SECURITY_TOKEN", ""),
		Region:         getEnvOrDefault("ROCKETMQ_REGION", ""),
		SendTimeout:    getSendTimeout(),
		RetryTimes:     getRetryTimes(),
		MaxMessageSize: getMaxMessageSize(),
	}
}

// getNameServers 获取NameServer地址列表
func getNameServers() []string {
	nameServers := getEnvOrDefault("ROCKETMQ_NAME_SERVERS", "localhost:9876")
	return strings.Split(nameServers, ",")
}

// getSendTimeout 获取发送超时时间
func getSendTimeout() time.Duration {
	timeoutStr := getEnvOrDefault("ROCKETMQ_SEND_TIMEOUT", "30")
	timeout, err := strconv.Atoi(timeoutStr)
	if err != nil {
		timeout = 30
	}
	return time.Duration(timeout) * time.Second
}

// getRetryTimes 获取重试次数
func getRetryTimes() int {
	retryStr := getEnvOrDefault("ROCKETMQ_RETRY_TIMES", "3")
	retry, err := strconv.Atoi(retryStr)
	if err != nil {
		retry = 3
	}
	return retry
}

// getMaxMessageSize 获取最大消息大小
func getMaxMessageSize() int {
	sizeStr := getEnvOrDefault("ROCKETMQ_MAX_MESSAGE_SIZE", "4194304") // 4MB
	size, err := strconv.Atoi(sizeStr)
	if err != nil {
		size = 4194304
	}
	return size
}

// getEnvOrDefault 获取环境变量或默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// RocketMQTopicConfig 主题配置
type RocketMQTopicConfig struct {
	Name         string `json:"name"`
	QueueNum     int    `json:"queue_num"`
	Permission   int    `json:"permission"`
	TopicSysFlag int    `json:"topic_sys_flag"`
}

// GetTopicConfigs 获取主题配置列表
func GetTopicConfigs() []RocketMQTopicConfig {
	return []RocketMQTopicConfig{
		{
			Name:         "tax_filing_submission",
			QueueNum:     8,
			Permission:   6, // 读写权限
			TopicSysFlag: 0,
		},
		{
			Name:         "tax_filing_batch",
			QueueNum:     4,
			Permission:   6,
			TopicSysFlag: 0,
		},
		{
			Name:         "tax_filing_sync",
			QueueNum:     4,
			Permission:   6,
			TopicSysFlag: 0,
		},
		{
			Name:         "tax_filing_callback",
			QueueNum:     4,
			Permission:   6,
			TopicSysFlag: 0,
		},
		{
			Name:         "tax_filing_notification",
			QueueNum:     2,
			Permission:   6,
			TopicSysFlag: 0,
		},
		{
			Name:         "tax_filing_dead_letter",
			QueueNum:     2,
			Permission:   6,
			TopicSysFlag: 0,
		},
	}
}

// RocketMQConsumerConfig 消费者配置
type RocketMQConsumerConfig struct {
	Topic                      string `json:"topic"`
	Tag                        string `json:"tag"`
	ConsumeThreadMin           int    `json:"consume_thread_min"`
	ConsumeThreadMax           int    `json:"consume_thread_max"`
	ConsumeMessageBatchMaxSize int    `json:"consume_message_batch_max_size"`
	PullBatchSize              int    `json:"pull_batch_size"`
}

// GetConsumerConfigs 获取消费者配置列表
func GetConsumerConfigs() []RocketMQConsumerConfig {
	return []RocketMQConsumerConfig{
		{
			Topic:                      "tax_filing_submission",
			Tag:                        "*",
			ConsumeThreadMin:           5,
			ConsumeThreadMax:           20,
			ConsumeMessageBatchMaxSize: 1,
			PullBatchSize:              32,
		},
		{
			Topic:                      "tax_filing_batch",
			Tag:                        "*",
			ConsumeThreadMin:           2,
			ConsumeThreadMax:           10,
			ConsumeMessageBatchMaxSize: 1,
			PullBatchSize:              32,
		},
		{
			Topic:                      "tax_filing_sync",
			Tag:                        "*",
			ConsumeThreadMin:           3,
			ConsumeThreadMax:           15,
			ConsumeMessageBatchMaxSize: 10,
			PullBatchSize:              32,
		},
		{
			Topic:                      "tax_filing_callback",
			Tag:                        "*",
			ConsumeThreadMin:           2,
			ConsumeThreadMax:           8,
			ConsumeMessageBatchMaxSize: 1,
			PullBatchSize:              32,
		},
		{
			Topic:                      "tax_filing_notification",
			Tag:                        "*",
			ConsumeThreadMin:           1,
			ConsumeThreadMax:           5,
			ConsumeMessageBatchMaxSize: 1,
			PullBatchSize:              32,
		},
	}
}

// RocketMQProducerConfig 生产者配置
type RocketMQProducerConfig struct {
	SendMsgTimeout                   time.Duration `json:"send_msg_timeout"`
	CompressMsgBodyOverHowmuch       int           `json:"compress_msg_body_over_howmuch"`
	RetryTimesWhenSendFailed         int           `json:"retry_times_when_send_failed"`
	RetryTimesWhenSendAsyncFailed    int           `json:"retry_times_when_send_async_failed"`
	RetryAnotherBrokerWhenNotStoreOK bool          `json:"retry_another_broker_when_not_store_ok"`
	MaxMessageSize                   int           `json:"max_message_size"`
}

// GetProducerConfig 获取生产者配置
func GetProducerConfig() RocketMQProducerConfig {
	return RocketMQProducerConfig{
		SendMsgTimeout:                   getSendTimeout(),
		CompressMsgBodyOverHowmuch:       4096, // 4KB
		RetryTimesWhenSendFailed:         getRetryTimes(),
		RetryTimesWhenSendAsyncFailed:    2,
		RetryAnotherBrokerWhenNotStoreOK: false,
		MaxMessageSize:                   getMaxMessageSize(),
	}
}

// RocketMQMonitorConfig 监控配置
type RocketMQMonitorConfig struct {
	EnableMetrics       bool          `json:"enable_metrics"`
	MetricsPort         int           `json:"metrics_port"`
	MetricsPath         string        `json:"metrics_path"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	LogLevel            string        `json:"log_level"`
}

// GetMonitorConfig 获取监控配置
func GetMonitorConfig() RocketMQMonitorConfig {
	enableMetrics, _ := strconv.ParseBool(getEnvOrDefault("ROCKETMQ_ENABLE_METRICS", "true"))
	metricsPort, _ := strconv.Atoi(getEnvOrDefault("ROCKETMQ_METRICS_PORT", "9091"))
	healthCheckInterval, _ := time.ParseDuration(getEnvOrDefault("ROCKETMQ_HEALTH_CHECK_INTERVAL", "30s"))

	return RocketMQMonitorConfig{
		EnableMetrics:       enableMetrics,
		MetricsPort:         metricsPort,
		MetricsPath:         getEnvOrDefault("ROCKETMQ_METRICS_PATH", "/metrics"),
		HealthCheckInterval: healthCheckInterval,
		LogLevel:            getEnvOrDefault("ROCKETMQ_LOG_LEVEL", "info"),
	}
}

// RocketMQDeadLetterConfig 死信队列配置
type RocketMQDeadLetterConfig struct {
	MaxReconsumeTimes int           `json:"max_reconsume_times"`
	DeadLetterTopic   string        `json:"dead_letter_topic"`
	RetentionTime     time.Duration `json:"retention_time"`
}

// GetDeadLetterConfig 获取死信队列配置
func GetDeadLetterConfig() RocketMQDeadLetterConfig {
	maxReconsumeTimes, _ := strconv.Atoi(getEnvOrDefault("ROCKETMQ_MAX_RECONSUME_TIMES", "16"))
	retentionTime, _ := time.ParseDuration(getEnvOrDefault("ROCKETMQ_DEAD_LETTER_RETENTION", "72h"))

	return RocketMQDeadLetterConfig{
		MaxReconsumeTimes: maxReconsumeTimes,
		DeadLetterTopic:   getEnvOrDefault("ROCKETMQ_DEAD_LETTER_TOPIC", "tax_filing_dead_letter"),
		RetentionTime:     retentionTime,
	}
}

// ValidateConfig 验证配置
func ValidateConfig(config *mq.RocketMQConfig) error {
	if len(config.NameServers) == 0 {
		return fmt.Errorf("name servers cannot be empty")
	}

	if config.ProducerGroup == "" {
		return fmt.Errorf("producer group cannot be empty")
	}

	if config.ConsumerGroup == "" {
		return fmt.Errorf("consumer group cannot be empty")
	}

	if config.SendTimeout <= 0 {
		return fmt.Errorf("send timeout must be positive")
	}

	if config.RetryTimes < 0 {
		return fmt.Errorf("retry times cannot be negative")
	}

	if config.MaxMessageSize <= 0 {
		return fmt.Errorf("max message size must be positive")
	}

	return nil
}

// GetEnvironmentSpecificConfig 获取环境特定配置
func GetEnvironmentSpecificConfig(env string) *mq.RocketMQConfig {
	config := GetRocketMQConfig()

	switch env {
	case "development":
		config.ProducerGroup = "tax_filing_producer_group_dev"
		config.ConsumerGroup = "tax_filing_consumer_group_dev"
		config.Namespace = "dev"
	case "testing":
		config.ProducerGroup = "tax_filing_producer_group_test"
		config.ConsumerGroup = "tax_filing_consumer_group_test"
		config.Namespace = "test"
	case "staging":
		config.ProducerGroup = "tax_filing_producer_group_staging"
		config.ConsumerGroup = "tax_filing_consumer_group_staging"
		config.Namespace = "staging"
	case "production":
		config.ProducerGroup = "tax_filing_producer_group_prod"
		config.ConsumerGroup = "tax_filing_consumer_group_prod"
		config.Namespace = "prod"
	}

	return config
}
