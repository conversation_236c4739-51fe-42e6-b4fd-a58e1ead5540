# SMEasyTax Backend Configuration

# Application settings
app:
  name: SMEasyTax
  port: 8081
  debug: true
  readTimeout: 10  # seconds
  writeTimeout: 30 # seconds
  idleTimeout: 120 # seconds
  secret: your-secret-key-here
  timezone: Asia/Shanghai

# Database settings
database:
  driver: mysql
  host: localhost
  port: 3306
  user: root
  password: Aa123456@
  name: smeasy_tax
  maxOpenConns: 50
  maxIdleConns: 10
  connMaxLifetime: 300 # seconds
  autoMigrate: true    # 是否自动执行数据库迁移

# Redis settings
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0

# JWT settings
jwt:
  secret: your-jwt-secret-key-here
  accessExpiry: 60    # minutes
  refreshExpiry: 7    # days
  issuer: sme_easy_tax
  refreshEnabled: true

# Storage settings
storage:
  type: local  # local, s3, oss
  path: ./uploads
  accessKey: ""
  secretKey: ""
  bucket: ""
  region: ""
  endpoint: ""

# CORS settings
cors:
  allowOrigins:
    - http://localhost:3000
    - http://localhost:8000
  allowMethods:
    - GET
    - POST
    - PUT
    - PATCH
    - DELETE
    - OPTIONS
  allowHeaders:
    - Origin
    - Content-Type
    - Accept
    - Authorization
  exposeHeaders:
    - Content-Length
  allowCredentials: true
  maxAge: 86400  # 24 hours in seconds

# Email service - 生产环境邮件服务（阿里云邮件推送）
email:
  provider: aliyun
  accessKeyID: ${ALIYUN_ACCESS_KEY_ID}  # 从环境变量获取
  accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET}  # 从环境变量获取
  regionID: cn-hangzhou
  endpoint: dm.aliyuncs.com
  fromAddress: <EMAIL>  # 需要在阿里云配置的发信地址
  fromName: 税易通系统
  replyToAddress: <EMAIL>
  # SMTP fallback configuration (optional)
  smtpHost: smtp.example.com
  smtpPort: 587
  smtpUsername: <EMAIL>
  smtpPassword: email-password
  smtpUseTLS: true

# Third party services
thirdParty:
  # Tax bureau API
  taxBureau:
    baseURL: https://api.tax.gov.example
    apiKey: tax-api-key
    apiSecret: tax-api-secret
    authMethod: bearer

  # Electronic invoice API
  eInvoiceAPI:
    baseURL: https://api.einvoice.example
    apiKey: einvoice-api-key
    apiSecret: einvoice-api-secret
    authMethod: basic

  # OCR service
  ocrService:
    baseURL: https://api.ocr.example
    apiKey: ocr-api-key

  # Bank API
  bankAPI:
    baseURL: https://api.bank.example
    appID: bank-app-id
    appSecret: bank-app-secret
    authMethod: oauth2