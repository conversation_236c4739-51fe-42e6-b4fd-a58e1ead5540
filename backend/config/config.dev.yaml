# 开发环境配置
# Development Environment Configuration

# Application settings
app:
  name: SMEasyTax-Dev
  port: 8081
  debug: true
  readTimeout: 10  # seconds
  writeTimeout: 30 # seconds
  idleTimeout: 120 # seconds
  timezone: Asia/Shanghai

# Database settings - 开发环境数据库
database:
  driver: mysql
  host: localhost
  port: 3306
  user: root
  password: Aa123456@
  name: smeasy_tax_dev  # 开发环境数据库
  maxOpenConns: 20      # 开发环境连接数较少
  maxIdleConns: 5
  connMaxLifetime: 300  # seconds
  autoMigrate: true     # 开发环境自动迁移

# Redis settings - 开发环境Redis
redis:
  host: localhost
  port: 6379
  password: ""
  db: 1  # 使用db1，避免与其他环境冲突

# JWT settings - 开发环境JWT配置
auth:
  jwtSecret: dev-jwt-secret-key-2024
  accessTokenDuration: 120   # 开发环境token有效期更长，方便调试
  refreshTokenDuration: 7    # days

# Storage settings - 开发环境存储
storage:
  type: local
  path: ./storage/dev
  accessKey: ""
  secretKey: ""
  bucket: ""
  region: ""

# CORS settings - 开发环境CORS配置
cors:
  allowOrigins:
    - http://localhost:3000
    - http://localhost:8000
    - http://localhost:8080
    - http://localhost:8082
    - http://127.0.0.1:3000
    - http://127.0.0.1:8000
    - http://127.0.0.1:8080
    - http://127.0.0.1:8082
  allowMethods:
    - GET
    - POST
    - PUT
    - PATCH
    - DELETE
    - OPTIONS
  allowHeaders:
    - Origin
    - Content-Type
    - Accept
    - Authorization
    - X-Requested-With
  exposeHeaders:
    - Content-Length
  allowCredentials: true
  maxAge: 86400

# Email service - 开发环境邮件服务（使用阿里云邮件推送）
email:
  provider: aliyun
  accessKeyID: ${ALIYUN_ACCESS_KEY_ID}  # 从环境变量获取
  accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET}  # 从环境变量获取
  regionID: cn-hangzhou
  endpoint: dm.aliyuncs.com
  fromAddress: <EMAIL>  # 需要在阿里云配置的发信地址
  fromName: 税易通系统（开发环境）
  replyToAddress: <EMAIL>
  # SMTP fallback configuration (for testing)
  smtpHost: smtp.mailtrap.io
  smtpPort: 587
  smtpUsername: <EMAIL>
  smtpPassword: dev-email-password
  smtpUseTLS: true

# Aliyun services - 阿里云服务配置
aliyun:
  accessKeyID: ${ALIYUN_ACCESS_KEY_ID}      # 从环境变量获取
  accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET}  # 从环境变量获取
  regionID: cn-hangzhou
  ocr:
    endpoint: ocr.cn-hangzhou.aliyuncs.com
    timeout: 30  # seconds

# Third party services - 开发环境第三方服务（使用测试API）
thirdParty:
  # Tax bureau API - 测试环境
  taxBureau:
    baseURL: https://test-api.tax.gov.example
    apiKey: dev-tax-api-key
    apiSecret: dev-tax-api-secret
    authMethod: bearer

  # Electronic invoice API - 测试环境
  eInvoiceAPI:
    baseURL: https://test-api.einvoice.example
    apiKey: dev-einvoice-api-key
    apiSecret: dev-einvoice-api-secret
    authMethod: basic

  # OCR service - 测试环境
  ocrService:
    baseURL: https://test-api.ocr.example
    apiKey: dev-ocr-api-key

  # Bank API - 测试环境
  bankAPI:
    baseURL: https://test-api.bank.example
    appID: dev-bank-app-id
    appSecret: dev-bank-app-secret
    authMethod: oauth2

# 日志配置 - 开发环境
logging:
  level: debug
  format: text
  output: stdout
  file: ./logs/dev.log

# 监控配置 - 开发环境
monitoring:
  enabled: false  # 开发环境关闭监控
  endpoint: ""
  interval: 30

# 缓存配置 - 开发环境
cache:
  enabled: true
  ttl: 300  # 5分钟，开发环境缓存时间较短
  maxSize: 1000
