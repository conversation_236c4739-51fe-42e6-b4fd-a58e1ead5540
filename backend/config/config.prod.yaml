# 生产环境配置
# Production Environment Configuration

# Application settings
app:
  name: SMEasyTax
  port: 8081
  debug: false  # 生产环境关闭调试
  readTimeout: 30   # seconds
  writeTimeout: 60  # seconds
  idleTimeout: 300  # seconds
  timezone: Asia/Shanghai

# Database settings - 生产环境数据库
database:
  driver: mysql
  host: localhost
  port: 3306
  user: root
  password: ""                         # 生产环境密码通过环境变量设置
  name: smeasy_tax                     # 生产环境数据库
  maxOpenConns: 100                    # 生产环境连接数更多
  maxIdleConns: 20
  connMaxLifetime: 3600                # seconds
  autoMigrate: false                   # 生产环境不自动迁移

# Redis settings - 生产环境Redis
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0

# JWT settings - 生产环境JWT配置
auth:
  jwtSecret: ""                        # 生产环境必须通过环境变量设置
  accessTokenDuration: 60              # 生产环境token有效期较短
  refreshTokenDuration: 30             # days

# Storage settings - 生产环境存储
storage:
  type: s3                             # 生产环境推荐使用云存储
  path: ./storage
  accessKey: ""
  secretKey: ""
  bucket: ""
  region: ""

# CORS settings - 生产环境CORS配置（更严格）
cors:
  allowOrigins:
    - https://tax.yourdomain.com
    - https://admin.yourdomain.com
  allowMethods:
    - GET
    - POST
    - PUT
    - PATCH
    - DELETE
    - OPTIONS
  allowHeaders:
    - Origin
    - Content-Type
    - Accept
    - Authorization
  exposeHeaders:
    - Content-Length
  allowCredentials: true
  maxAge: 86400

# Email service - 生产环境邮件服务（阿里云邮件推送）
email:
  provider: aliyun
  accessKeyID: ${ALIYUN_ACCESS_KEY_ID}  # 从环境变量获取
  accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET}  # 从环境变量获取
  regionID: cn-hangzhou
  endpoint: dm.aliyuncs.com
  fromAddress: ${EMAIL_FROM_ADDRESS:<EMAIL>}  # 需要在阿里云配置的发信地址
  fromName: ${EMAIL_FROM_NAME:税易通系统}
  replyToAddress: ${EMAIL_REPLY_TO:<EMAIL>}
  # SMTP fallback configuration (optional)
  smtpHost: ${SMTP_HOST:}
  smtpPort: ${SMTP_PORT:587}
  smtpUsername: ${SMTP_USERNAME:}
  smtpPassword: ${SMTP_PASSWORD:}
  smtpUseTLS: ${SMTP_USE_TLS:true}

# Aliyun services - 阿里云服务配置（生产环境）
aliyun:
  accessKeyID: ${ALIYUN_ACCESS_KEY_ID}      # 从环境变量获取
  accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET}  # 从环境变量获取
  regionID: ${ALIYUN_REGION_ID:cn-hangzhou}
  ocr:
    endpoint: ${ALIYUN_OCR_ENDPOINT:ocr.cn-hangzhou.aliyuncs.com}
    timeout: ${ALIYUN_OCR_TIMEOUT:30}  # seconds

# Third party services - 生产环境第三方服务
thirdParty:
  # Tax bureau API - 正式环境
  taxBureau:
    baseURL: https://api.tax.gov.cn
    apiKey: ""
    apiSecret: ""
    authMethod: bearer

  # Electronic invoice API - 正式环境
  eInvoiceAPI:
    baseURL: https://api.einvoice.gov.cn
    apiKey: ""
    apiSecret: ""
    authMethod: basic

  # OCR service - 正式环境
  ocrService:
    baseURL: https://api.ocr.service.com
    apiKey: ""

  # Bank API - 正式环境
  bankAPI:
    baseURL: https://api.bank.com
    appID: ""
    appSecret: ""
    authMethod: oauth2

# 日志配置 - 生产环境
logging:
  level: info
  format: json                         # 生产环境使用JSON格式便于日志分析
  output: file
  file: ./logs/prod.log

# 监控配置 - 生产环境
monitoring:
  enabled: true
  endpoint: ""
  interval: 60  # seconds

# 缓存配置 - 生产环境
cache:
  enabled: true
  ttl: 1800               # 30分钟
  maxSize: 10000
