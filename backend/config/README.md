# 配置文件说明
# Configuration Documentation

本目录包含税务管理系统后端的配置文件，支持多环境配置。

## 📁 文件结构

```
config/
├── config.go           # 配置加载逻辑
├── config.yaml         # 基础配置文件（已废弃，请使用环境特定配置）
├── config.dev.yaml     # 开发环境配置
├── config.test.yaml    # 测试环境配置
├── config.prod.yaml    # 生产环境配置
└── README.md           # 本文档
```

## 🌍 环境配置

系统根据环境变量 `APP_ENV` 或 `GO_ENV` 自动选择对应的配置文件：

| 环境变量值 | 配置文件 | 说明 |
|-----------|---------|------|
| `dev` / `development` | `config.dev.yaml` | 开发环境 |
| `test` / `testing` | `config.test.yaml` | 测试环境 |
| `prod` / `production` | `config.prod.yaml` | 生产环境 |
| 未设置或其他 | `config.dev.yaml` | 默认开发环境 |

## 🚀 快速开始

### 1. 设置环境变量

```bash
# 开发环境
export APP_ENV=dev

# 测试环境
export APP_ENV=test

# 生产环境
export APP_ENV=prod
```

### 2. 使用启动脚本

```bash
# 开发环境
./scripts/start-dev.sh

# 测试环境
./scripts/start-test.sh

# 生产环境
./scripts/start-prod.sh
```

### 3. 手动启动

```bash
# 设置环境变量后直接运行
APP_ENV=prod go run main.go
```

## 目录结构
```
config/
├── config.go      # 配置结构定义和加载逻辑
├── config.yaml    # 默认配置文件
└── config.dev.yaml # 开发环境配置
```

## 配置项
```yaml
# 应用配置
app:
  name: smeasy-tax
  mode: development
  port: 8080

# 数据库配置
database:
  driver: mysql
  host: localhost
  port: 3306
  name: smeasy_tax
  username: root
  password: ${DB_PASSWORD} # 环境变量
  max_idle_conns: 10
  max_open_conns: 100

# Redis配置
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0

# JWT配置
jwt:
  secret: ${JWT_SECRET}
  expire: 24h
  refresh: 72h

# 日志配置
log:
  level: debug
  filename: logs/app.log
  max_size: 100
  max_age: 30
  max_backups: 10
```

## 核心功能

### 配置加载
1. 支持YAML、JSON等多种格式
2. 多环境配置文件
3. 环境变量覆盖
4. 配置热重载

### 敏感信息
1. 环境变量注入
2. 密钥加密存储
3. 配置值脱敏

### 配置验证
1. 必填项检查
2. 格式验证
3. 值范围验证

## 使用示例
```go
// config/config.go
type Config struct {
    App      AppConfig      `mapstructure:"app"`
    Database DatabaseConfig `mapstructure:"database"`
    Redis    RedisConfig    `mapstructure:"redis"`
    JWT      JWTConfig      `mapstructure:"jwt"`
    Log      LogConfig      `mapstructure:"log"`
}

type AppConfig struct {
    Name string `mapstructure:"name"`
    Mode string `mapstructure:"mode"`
    Port int    `mapstructure:"port"`
}

func LoadConfig() (*Config, error) {
    viper.SetConfigName("config")
    viper.SetConfigType("yaml")
    viper.AddConfigPath("./config")

    // 加载环境变量
    viper.AutomaticEnv()
    viper.SetEnvPrefix("SMEASY")

    if err := viper.ReadInConfig(); err != nil {
        return nil, fmt.Errorf("读取配置文件失败: %w", err)
    }

    var config Config
    if err := viper.Unmarshal(&config); err != nil {
        return nil, fmt.Errorf("解析配置失败: %w", err)
    }

    return &config, nil
}
```

## 最佳实践
1. 使用环境变量管理敏感信息
2. 不同环境使用不同配置文件
3. 定期备份配置文件
4. 实现配置变更通知
5. 记录配置修改日志

## 配置管理
1. 版本控制
   - 配置文件纳入版本管理
   - 敏感信息使用占位符

2. 部署策略
   - 使用环境变量注入敏感信息
   - 配置文件与代码分离部署

3. 安全建议
   - 限制配置文件访问权限
   - 加密存储敏感信息
   - 定期轮换密钥和凭证