# 税务申报模块部署指南

## 概述

本文档描述了如何部署和配置税务申报模块，包括Go后端服务、Python税务申报服务、数据库、Redis等组件。

## 系统要求

### 硬件要求

- **CPU**: 最少2核，推荐4核以上
- **内存**: 最少4GB，推荐8GB以上
- **存储**: 最少50GB可用空间
- **网络**: 稳定的互联网连接

### 软件要求

- **操作系统**: Linux (Ubuntu 20.04+, CentOS 7+) 或 macOS
- **Go**: 1.19+
- **Python**: 3.8+
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Docker**: 20.10+ (可选)
- **Docker Compose**: 1.29+ (可选)

## 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   Go后端服务    │    │  Python申报服务 │
│   (Vue 3)       │◄──►│   (Gin)         │◄──►│   (FastAPI)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │     MySQL       │    │     Redis       │
                       │   (主数据库)    │    │   (缓存/队列)   │
                       └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   省级税务局    │
                       │   API接口       │
                       └─────────────────┘
```

## 环境配置

### 1. 数据库配置

#### MySQL配置

```sql
-- 创建数据库
CREATE DATABASE smeasy_tax CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'tax_user'@'%' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON smeasy_tax.* TO 'tax_user'@'%';
FLUSH PRIVILEGES;

-- 配置参数
SET GLOBAL max_connections = 1000;
SET GLOBAL innodb_buffer_pool_size = 2G;
SET GLOBAL query_cache_size = 256M;
```

#### Redis配置

```bash
# redis.conf
bind 0.0.0.0
port 6379
requirepass your_redis_password
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 2. 环境变量配置

创建 `.env` 文件：

```bash
# 应用配置
APP_ENV=production
APP_PORT=8081
APP_DEBUG=false

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=smeasy_tax
DB_USER=tax_user
DB_PASSWORD=your_secure_password
DB_MAX_OPEN_CONNS=100
DB_MAX_IDLE_CONNS=10

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0
REDIS_POOL_SIZE=10

# 税务申报服务配置
TAX_FILING_SERVICE_URL=http://localhost:8000
TAX_FILING_SERVICE_API_KEY=your_api_key
TAX_FILING_SERVICE_TIMEOUT=30

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRE_HOURS=24

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/tax-management/app.log

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 通知配置
NOTIFICATION_EMAIL_ENABLED=true
NOTIFICATION_SMS_ENABLED=false
NOTIFICATION_WEBHOOK_ENABLED=true
NOTIFICATION_WEBHOOK_URL=http://localhost:8081/api/notifications/webhook

# 邮件配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_smtp_password

# 文件存储配置
FILE_STORAGE_TYPE=local
FILE_STORAGE_PATH=/var/data/tax-management/files
```

## 部署方式

### 方式一：Docker部署（推荐）

#### 1. 创建Docker Compose文件

```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: tax-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: smeasy_tax
      MYSQL_USER: tax_user
      MYSQL_PASSWORD: your_secure_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped

  redis:
    image: redis:6.2-alpine
    container_name: tax-redis
    command: redis-server --requirepass your_redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  tax-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tax-backend
    environment:
      - APP_ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
    ports:
      - "8081:8081"
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/var/log/tax-management
      - ./data:/var/data/tax-management
    restart: unless-stopped

  tax-filing-service:
    image: tax-filing-python:latest
    container_name: tax-filing-service
    environment:
      - DATABASE_URL=mysql://tax_user:your_secure_password@mysql:3306/smeasy_tax
      - REDIS_URL=redis://:your_redis_password@redis:6379/1
    ports:
      - "8000:8000"
    depends_on:
      - mysql
      - redis
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

#### 2. 创建Dockerfile

```dockerfile
# Dockerfile
FROM golang:1.19-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/main .
COPY --from=builder /app/config ./config

EXPOSE 8081
CMD ["./main"]
```

#### 3. 部署命令

```bash
# 构建并启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f tax-backend

# 停止服务
docker-compose down
```

### 方式二：传统部署

#### 1. 安装依赖

```bash
# 安装Go
wget https://golang.org/dl/go1.19.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.19.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc

# 安装MySQL
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation

# 安装Redis
sudo apt install redis-server
sudo systemctl enable redis-server
sudo systemctl start redis-server
```

#### 2. 编译应用

```bash
# 克隆代码
git clone https://github.com/your-org/tax-management-system.git
cd tax-management-system/backend

# 安装依赖
go mod download

# 编译应用
go build -o tax-management-backend .
```

#### 3. 配置系统服务

创建systemd服务文件：

```ini
# /etc/systemd/system/tax-management.service
[Unit]
Description=Tax Management Backend Service
After=network.target mysql.service redis.service

[Service]
Type=simple
User=tax-user
WorkingDirectory=/opt/tax-management
ExecStart=/opt/tax-management/tax-management-backend
Restart=always
RestartSec=5
Environment=APP_ENV=production

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
# 重新加载systemd
sudo systemctl daemon-reload

# 启动服务
sudo systemctl enable tax-management
sudo systemctl start tax-management

# 查看状态
sudo systemctl status tax-management
```

## 数据库迁移

### 1. 自动迁移

应用启动时会自动执行数据库迁移：

```go
// 在main.go中
db.AutoMigrate(
    &model.Enterprise{},
    &model.TaxFilingProvince{},
    &model.TaxFilingSubmission{},
    &model.TaxFilingBatch{},
    &model.TaxFilingStatusHistory{},
    &model.TaxFilingCallback{},
)
```

### 2. 手动迁移

```bash
# 使用migrate工具
migrate -path ./migrations -database "mysql://user:password@tcp(localhost:3306)/smeasy_tax" up

# 或使用SQL脚本
mysql -u tax_user -p smeasy_tax < migrations/001_initial_schema.sql
```

## 配置管理

### 1. 配置文件结构

```
config/
├── config.yaml          # 主配置文件
├── database.yaml         # 数据库配置
├── redis.yaml           # Redis配置
├── tax-filing.yaml      # 税务申报配置
└── logging.yaml         # 日志配置
```

### 2. 配置示例

```yaml
# config/config.yaml
app:
  name: "Tax Management System"
  version: "1.0.0"
  port: 8081
  debug: false

database:
  host: "localhost"
  port: 3306
  name: "smeasy_tax"
  user: "tax_user"
  password: "your_secure_password"
  max_open_conns: 100
  max_idle_conns: 10

redis:
  host: "localhost"
  port: 6379
  password: "your_redis_password"
  db: 0
  pool_size: 10

tax_filing:
  service_url: "http://localhost:8000"
  api_key: "your_api_key"
  timeout: 30
  max_retries: 3
  retry_delay: "1s"

logging:
  level: "info"
  file_path: "/var/log/tax-management/app.log"
  max_size: 100
  max_backups: 10
  max_age: 30
```

## 监控和日志

### 1. 日志配置

```bash
# 创建日志目录
sudo mkdir -p /var/log/tax-management
sudo chown tax-user:tax-user /var/log/tax-management

# 配置logrotate
sudo tee /etc/logrotate.d/tax-management << EOF
/var/log/tax-management/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 tax-user tax-user
    postrotate
        systemctl reload tax-management
    endscript
}
EOF
```

### 2. 监控配置

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'tax-management'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/metrics'
```

## 安全配置

### 1. 防火墙配置

```bash
# 开放必要端口
sudo ufw allow 8081/tcp  # 应用端口
sudo ufw allow 3306/tcp  # MySQL端口（仅内网）
sudo ufw allow 6379/tcp  # Redis端口（仅内网）

# 启用防火墙
sudo ufw enable
```

### 2. SSL/TLS配置

```nginx
# nginx配置
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location / {
        proxy_pass http://localhost:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 性能优化

### 1. 数据库优化

```sql
-- 创建索引
CREATE INDEX idx_submission_status ON tax_filing_submissions(status);
CREATE INDEX idx_submission_created_at ON tax_filing_submissions(created_at);
CREATE INDEX idx_submission_enterprise ON tax_filing_submissions(enterprise_id);
CREATE INDEX idx_batch_status ON tax_filing_batches(status);

-- 分区表（可选）
ALTER TABLE tax_filing_submissions 
PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 2. Redis优化

```bash
# redis.conf
maxmemory-policy allkeys-lru
tcp-keepalive 300
timeout 0
tcp-backlog 511
```

### 3. 应用优化

```go
// 连接池配置
db.SetMaxOpenConns(100)
db.SetMaxIdleConns(10)
db.SetConnMaxLifetime(time.Hour)

// Redis连接池
redis.NewClient(&redis.Options{
    PoolSize:     10,
    MinIdleConns: 5,
    PoolTimeout:  30 * time.Second,
})
```

## 故障排除

### 1. 常见问题

**问题**: 数据库连接失败
```bash
# 检查MySQL状态
sudo systemctl status mysql

# 检查连接
mysql -u tax_user -p -h localhost

# 查看错误日志
sudo tail -f /var/log/mysql/error.log
```

**问题**: Redis连接失败
```bash
# 检查Redis状态
sudo systemctl status redis

# 测试连接
redis-cli -h localhost -p 6379 -a your_password ping

# 查看日志
sudo tail -f /var/log/redis/redis-server.log
```

**问题**: 应用启动失败
```bash
# 查看应用日志
sudo journalctl -u tax-management -f

# 检查配置文件
./tax-management-backend --config-check

# 检查端口占用
sudo netstat -tlnp | grep 8081
```

### 2. 性能问题

```bash
# 查看系统资源
top
htop
iostat -x 1

# 查看数据库性能
SHOW PROCESSLIST;
SHOW ENGINE INNODB STATUS;

# 查看Redis性能
redis-cli info stats
redis-cli --latency
```

## 备份和恢复

### 1. 数据库备份

```bash
# 创建备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"
mkdir -p $BACKUP_DIR

mysqldump -u tax_user -p smeasy_tax > $BACKUP_DIR/smeasy_tax_$DATE.sql
gzip $BACKUP_DIR/smeasy_tax_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
```

### 2. Redis备份

```bash
# 创建Redis备份
redis-cli -h localhost -p 6379 -a your_password BGSAVE

# 复制RDB文件
cp /var/lib/redis/dump.rdb /backup/redis/dump_$(date +%Y%m%d_%H%M%S).rdb
```

### 3. 应用数据备份

```bash
# 备份配置文件和日志
tar -czf /backup/app/config_$(date +%Y%m%d_%H%M%S).tar.gz /opt/tax-management/config
tar -czf /backup/app/logs_$(date +%Y%m%d_%H%M%S).tar.gz /var/log/tax-management
```

## 更新和维护

### 1. 应用更新

```bash
# 停止服务
sudo systemctl stop tax-management

# 备份当前版本
cp /opt/tax-management/tax-management-backend /opt/tax-management/tax-management-backend.backup

# 部署新版本
cp ./tax-management-backend /opt/tax-management/

# 运行数据库迁移
./tax-management-backend migrate

# 启动服务
sudo systemctl start tax-management

# 验证服务
curl http://localhost:8081/health
```

### 2. 定期维护

```bash
# 清理日志
find /var/log/tax-management -name "*.log" -mtime +30 -delete

# 优化数据库
mysql -u tax_user -p -e "OPTIMIZE TABLE smeasy_tax.tax_filing_submissions;"

# 清理Redis
redis-cli -h localhost -p 6379 -a your_password FLUSHDB
```

这个部署指南提供了完整的部署流程和配置说明，可以根据实际环境进行调整。
