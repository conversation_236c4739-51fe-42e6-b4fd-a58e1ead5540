# 税务申报API文档

## 概述

税务申报模块提供了完整的税务申报管理功能，包括单个申报、批量申报、状态同步、回调处理等。

## 基础信息

- **基础URL**: `/api/tax-filing`
- **认证方式**: <PERSON><PERSON>
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## API端点

### 1. 申报管理

#### 1.1 创建申报

**POST** `/submissions`

创建新的税务申报记录。

**请求体**:
```json
{
  "enterprise_id": "string",
  "province_code": "string",
  "submission_type": "manual|batch",
  "company_name": "string",
  "tax_id": "string",
  "registration_number": "string",
  "legal_representative": "string",
  "company_address": "string",
  "contact_phone": "string",
  "contact_email": "string",
  "tax_year": 2024,
  "tax_month": 3,
  "tax_quarter": null,
  "period_type": "monthly|quarterly|yearly",
  "tax_data": [
    {
      "tax_type": "string",
      "taxable_amount": 100000.00,
      "tax_rate": 0.13,
      "tax_amount": 13000.00,
      "deductions": 0.00,
      "credits": 0.00,
      "final_amount": 13000.00
    }
  ],
  "notes": "string",
  "additional_data": {}
}
```

**响应**:
```json
{
  "status": "success",
  "message": "Submission created successfully",
  "data": {
    "id": "tf_abc123",
    "enterprise_id": "string",
    "province_code": "string",
    "status": "pending",
    "reference_number": "BJ20240315abc123",
    "created_at": "2024-03-15T10:30:00Z",
    "status_text": "待处理"
  }
}
```

#### 1.2 获取申报列表

**GET** `/submissions`

分页获取申报记录列表。

**查询参数**:
- `enterprise_id` (可选): 企业ID
- `province_code` (可选): 省份代码
- `status` (可选): 申报状态
- `page` (可选): 页码，默认1
- `page_size` (可选): 每页大小，默认20

**响应**:
```json
{
  "status": "success",
  "message": "Submissions retrieved successfully",
  "data": {
    "list": [
      {
        "id": "tf_abc123",
        "enterprise_id": "string",
        "province_code": "string",
        "company_name": "string",
        "status": "pending",
        "status_text": "待处理",
        "created_at": "2024-03-15T10:30:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "total_pages": 5
  }
}
```

#### 1.3 获取申报详情

**GET** `/submissions/{id}`

根据ID获取申报记录详情。

**路径参数**:
- `id`: 申报ID

**响应**:
```json
{
  "status": "success",
  "message": "Submission retrieved successfully",
  "data": {
    "id": "tf_abc123",
    "enterprise_id": "string",
    "province_code": "string",
    "province_name": "北京",
    "submission_type": "manual",
    "company_name": "测试企业有限公司",
    "tax_id": "91110000123456789X",
    "status": "pending",
    "status_text": "待处理",
    "tax_data": [
      {
        "tax_type": "增值税",
        "taxable_amount": 100000.00,
        "tax_rate": 0.13,
        "tax_amount": 13000.00,
        "final_amount": 13000.00
      }
    ],
    "total_taxable_amount": 100000.00,
    "total_tax_amount": 13000.00,
    "created_at": "2024-03-15T10:30:00Z",
    "updated_at": "2024-03-15T10:30:00Z"
  }
}
```

#### 1.4 提交申报到税务局

**POST** `/submissions/{id}/submit`

将申报记录提交到对应的省级税务局。

**路径参数**:
- `id`: 申报ID

**响应**:
```json
{
  "status": "success",
  "message": "Submission submitted to tax bureau successfully"
}
```

#### 1.5 同步申报状态

**POST** `/submissions/{id}/sync`

从税务局同步申报状态。

**路径参数**:
- `id`: 申报ID

**响应**:
```json
{
  "status": "success",
  "message": "Submission status synced successfully"
}
```

#### 1.6 重试申报

**POST** `/submissions/{id}/retry`

重试失败的申报。

**路径参数**:
- `id`: 申报ID

**响应**:
```json
{
  "status": "success",
  "message": "Submission retry initiated successfully"
}
```

#### 1.7 取消申报

**POST** `/submissions/{id}/cancel`

取消待处理或处理中的申报。

**路径参数**:
- `id`: 申报ID

**响应**:
```json
{
  "status": "success",
  "message": "Submission cancelled successfully"
}
```

#### 1.8 更新申报状态

**PUT** `/submissions/{id}/status`

手动更新申报状态。

**路径参数**:
- `id`: 申报ID

**请求体**:
```json
{
  "status": "pending|processing|submitted|accepted|rejected|failed|cancelled",
  "error_message": "string"
}
```

**响应**:
```json
{
  "status": "success",
  "message": "Submission status updated successfully"
}
```

### 2. 批次管理

#### 2.1 创建批次

**POST** `/batches`

创建新的申报批次。

**请求体**:
```json
{
  "name": "string",
  "description": "string",
  "province_code": "string",
  "enterprise_id": "string"
}
```

**响应**:
```json
{
  "status": "success",
  "message": "Batch created successfully",
  "data": {
    "id": "batch_abc123",
    "name": "string",
    "province_code": "string",
    "status": "pending",
    "status_text": "待处理",
    "created_at": "2024-03-15T10:30:00Z"
  }
}
```

#### 2.2 获取批次详情

**GET** `/batches/{id}`

根据ID获取批次详情。

**路径参数**:
- `id`: 批次ID

**响应**:
```json
{
  "status": "success",
  "message": "Batch retrieved successfully",
  "data": {
    "id": "batch_abc123",
    "name": "string",
    "description": "string",
    "province_code": "string",
    "status": "pending",
    "status_text": "待处理",
    "total_submissions": 10,
    "successful_submissions": 0,
    "failed_submissions": 0,
    "success_rate": 0.0,
    "failure_rate": 0.0,
    "created_at": "2024-03-15T10:30:00Z",
    "updated_at": "2024-03-15T10:30:00Z"
  }
}
```

#### 2.3 处理批次

**POST** `/batches/{id}/process`

开始处理申报批次中的所有申报。

**路径参数**:
- `id`: 批次ID

**响应**:
```json
{
  "status": "success",
  "message": "Batch processing started successfully"
}
```

### 3. Webhook端点

#### 3.1 通用Webhook

**POST** `/webhook`

接收来自Python税务申报服务的Webhook通知。

**请求体**:
```json
{
  "type": "submission_created|submission_submitted|submission_accepted|submission_rejected|submission_failed|validation_failed|system_error|batch_notification",
  "data": {},
  "service": "string",
  "timestamp": "string"
}
```

**响应**:
```json
{
  "status": "success",
  "message": "Webhook processed successfully"
}
```

#### 3.2 状态更新Webhook

**POST** `/webhook/status`

接收申报状态更新通知。

**请求体**:
```json
{
  "submission_id": "string",
  "external_id": "string",
  "status": "pending|processing|submitted|accepted|rejected|failed|cancelled",
  "message": "string",
  "error_message": "string",
  "processed_at": "2024-03-15T10:30:00Z",
  "progress": {}
}
```

**响应**:
```json
{
  "status": "success",
  "message": "Status updated successfully"
}
```

#### 3.3 批次更新Webhook

**POST** `/webhook/batch`

接收批次状态更新通知。

**请求体**:
```json
{
  "batch_id": "string",
  "status": "string",
  "total_submissions": 10,
  "successful_submissions": 8,
  "failed_submissions": 2,
  "processing_submissions": 0,
  "progress_percentage": 100.0,
  "message": "string"
}
```

**响应**:
```json
{
  "status": "success",
  "message": "Batch update processed successfully"
}
```

## 状态说明

### 申报状态

- `pending`: 待处理
- `processing`: 处理中
- `submitted`: 已提交
- `accepted`: 已接受
- `rejected`: 已拒绝
- `failed`: 失败
- `cancelled`: 已取消

### 批次状态

- `pending`: 待处理
- `processing`: 处理中
- `completed`: 已完成
- `failed`: 失败
- `cancelled`: 已取消

## 错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |
| 502 | 外部服务错误 |
| 503 | 服务不可用 |

## 使用示例

### 创建并提交申报

```bash
# 1. 创建申报
curl -X POST "http://localhost:8081/api/tax-filing/submissions" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "enterprise_id": "enterprise_001",
    "province_code": "BJ",
    "submission_type": "manual",
    "company_name": "测试企业有限公司",
    "tax_id": "91110000123456789X",
    "registration_number": "110000123456789",
    "legal_representative": "张三",
    "company_address": "北京市朝阳区测试街道123号",
    "tax_year": 2024,
    "tax_month": 3,
    "period_type": "monthly",
    "tax_data": [
      {
        "tax_type": "增值税",
        "taxable_amount": 100000.00,
        "tax_rate": 0.13,
        "tax_amount": 13000.00,
        "deductions": 0.00,
        "credits": 0.00,
        "final_amount": 13000.00
      }
    ]
  }'

# 2. 提交申报
curl -X POST "http://localhost:8081/api/tax-filing/submissions/tf_abc123/submit" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 3. 查询申报状态
curl -X GET "http://localhost:8081/api/tax-filing/submissions/tf_abc123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 批量申报

```bash
# 1. 创建批次
curl -X POST "http://localhost:8081/api/tax-filing/batches" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "2024年3月批量申报",
    "description": "2024年3月增值税批量申报",
    "province_code": "BJ",
    "enterprise_id": "enterprise_001"
  }'

# 2. 处理批次
curl -X POST "http://localhost:8081/api/tax-filing/batches/batch_abc123/process" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 3. 查询批次状态
curl -X GET "http://localhost:8081/api/tax-filing/batches/batch_abc123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 注意事项

1. 所有API调用都需要有效的认证令牌
2. 申报数据必须符合对应省份的验证规则
3. 批次处理是异步的，需要通过查询接口或Webhook获取处理结果
4. 建议在生产环境中配置适当的重试和超时机制
5. 敏感数据传输建议使用HTTPS协议
