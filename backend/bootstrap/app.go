package bootstrap

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"backend/config"
	"backend/middleware"
)

// App represents the application instance
type App struct {
	Router     *gin.Engine
	DB         *gorm.DB
	Config     *config.Config
	HTTPServer *http.Server
}

// NewApp creates and configures a new App instance
func NewApp(cfg *config.Config) *App {
	// Initialize components
	db := SetupDatabase(*cfg)
	router := setupRouter(*cfg)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%s", cfg.App.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.App.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.App.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.App.IdleTimeout) * time.Second,
	}

	return &App{
		Router:     router,
		DB:         db,
		Config:     cfg,
		HTTPServer: server,
	}
}

// Run starts the application server
func (a *App) Run() error {
	// Create a context for graceful shutdown
	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	// Start the server in a goroutine
	go func() {
		log.Printf("Server starting on port %s\n", a.Config.App.Port)
		if err := a.HTTPServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("HTTP server error: %v", err)
			// Signal the main goroutine to stop
			stop()
		}
	}()

	log.Printf("Server is running at http://localhost:%s", a.Config.App.Port)

	// Wait for interrupt signal
	<-ctx.Done()
	log.Println("Shutting down server...")

	// Create a deadline for graceful shutdown
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Shutdown the server
	if err := a.HTTPServer.Shutdown(shutdownCtx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
		return err
	}

	// Close database connection
	CloseDatabase(a.DB)

	log.Println("Server exited gracefully")
	return nil
}

// setupRouter initializes the Gin router with middleware
func setupRouter(cfg config.Config) *gin.Engine {
	// Set gin mode based on configuration
	if cfg.App.Debug {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.New()

	// Recovery middleware
	r.Use(gin.Recovery())

	// CORS middleware
	r.Use(middleware.CORS())

	// Logger middleware
	r.Use(middleware.Logger())

	// Request ID middleware
	r.Use(middleware.RequestID())

	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"version":   "1.0.0",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})

	return r
}
