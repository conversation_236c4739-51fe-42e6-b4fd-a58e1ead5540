// Package bootstrap provides application initialization functions for the tax management system.
// It includes database setup, connection pool configuration, and other startup operations.
package bootstrap

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"backend/config"
)

// SetupDatabase initializes the database connection with the provided configuration.
// It configures the connection pool, logger, and returns a ready-to-use GORM database instance.
func SetupDatabase(cfg config.Config) *gorm.DB {
	// Configure logger
	gormLogger := logger.New(
		log.New(log.Writer(), "\r\n", log.LstdFlags), // io writer
		logger.Config{
			SlowThreshold:             time.Second,   // Slow SQL threshold
			LogLevel:                  logger.Silent, // Log level
			IgnoreRecordNotFoundError: true,          // Ignore ErrRecordNotFound error
			Colorful:                  false,         // Disable color
		},
	)

	if cfg.App.Debug {
		gormLogger.LogMode(logger.Info)
	}

	// Database connection string
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Name,
	)

	// Connect to database
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})

	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("Failed to get database instance: %v", err)
	}

	// Set connection pool parameters from config with optimized defaults
	// 根据GOLANG_STANDARDS.md性能优化指南设置连接池参数
	maxOpenConns := cfg.Database.MaxOpenConns
	if maxOpenConns == 0 {
		maxOpenConns = 100 // 生产环境推荐值
	}
	sqlDB.SetMaxOpenConns(maxOpenConns)

	maxIdleConns := cfg.Database.MaxIdleConns
	if maxIdleConns == 0 {
		maxIdleConns = 20 // 建议为最大连接数的10-20%
	}
	sqlDB.SetMaxIdleConns(maxIdleConns)

	connMaxLifetime := time.Duration(cfg.Database.ConnMaxLifetime) * time.Second
	if connMaxLifetime == 0 {
		connMaxLifetime = time.Hour // 默认1小时，避免长时间连接导致的问题
	}
	sqlDB.SetConnMaxLifetime(connMaxLifetime)

	log.Printf("Database connection pool configured: MaxOpen=%d, MaxIdle=%d, MaxLifetime=%v",
		maxOpenConns, maxIdleConns, connMaxLifetime)

	log.Println("Connected to database successfully")

	return db
}

// CloseDatabase gracefully closes the database connection and releases resources.
// It should be called during application shutdown to ensure proper cleanup.
func CloseDatabase(db *gorm.DB) {
	sqlDB, err := db.DB()
	if err != nil {
		log.Printf("Error getting database instance: %v", err)
		return
	}

	if err := sqlDB.Close(); err != nil {
		log.Printf("Error closing database connection: %v", err)
		return
	}

	log.Println("Database connection closed successfully")
}
