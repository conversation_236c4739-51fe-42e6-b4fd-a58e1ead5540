# 税易通后端 API

## 项目概述

这是一个完整的企业级智能税务管理系统后端，包含以下核心功能模块：

### 🏢 一、基础数据管理模块

#### 1. 企业档案管理
- ✅ 多维度企业信息登记（工商/税务/银行）
- ✅ 纳税人资格智能识别（小规模/一般纳税人）
- ✅ 资质文件电子化归档

#### 2. 税种配置中心
- ✅ 多税种启用管理（增值税/所得税/附加税）
- ✅ 计税规则可视化配置（税率/优惠政策）
- ✅ 申报周期设定（月报/季报/年报）

### 📄 二、智能票据处理模块

#### 3. 全渠道票据采集
- ✅ 纸质发票智能扫描（支持增值税专票/普票/电子票）
- ✅ 电子发票API自动同步（税控盘/第三方平台）
- ✅ 银行回单OCR识别

#### 4. 票据智能处理
- ✅ 自动分类（进项/销项/费用票据）
- ✅ 关键信息结构化提取（金额/税号/商品明细）
- ✅ 发票真伪在线核验（对接税务总局查验平台）

### 🧮 三、智能申报核心模块

#### 5. 税款自动计算引擎
- ✅ 多税种并行计算（增值税+附加税联动）
- ✅ 减免政策自动匹配（小规模纳税人免税额度）
- ✅ 历史数据对比分析（税负率波动预警）

#### 6. 申报表智能生成
- ✅ 标准模板自动填充（增值税申报表/所得税预缴表）
- ✅ 跨表数据自动勾稽（主表与附表逻辑校验）
- ✅ 申报表合规性预检（必填项/逻辑关系/数据范围）

### 👥 四、企业级功能模块

#### 9. 多维度权限体系
- ✅ 角色权限矩阵（法人/财务/办税员）
- ✅ 操作权限粒度控制（字段级读写权限）
- ✅ 登录安全策略（设备绑定/IP白名单）

#### 10. 集团化管理
- ✅ 多账套独立管理（母子公司数据隔离）
- ✅ 跨公司数据汇总（集团合并申报视图）
- ✅ 组织架构权限继承

## 技术架构

### 后端技术栈
- **语言**: Go 1.21+
- **框架**: Gin Web Framework
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **ORM**: GORM
- **认证**: JWT
- **配置**: Viper
- **日志**: Gin Logger

### 项目结构
```
backend/
├── api/                    # API处理程序
│   └── tax_system_handler.go
├── config/                 # 配置管理
│   ├── config.go
│   └── simple.go
├── model/                  # 数据模型
│   ├── user.go
│   ├── enterprise.go
│   ├── tax_type.go
│   ├── invoice.go
│   ├── declaration.go
│   └── ...
├── service/               # 业务逻辑层
│   ├── enterprise_service.go
│   ├── tax_config_service.go
│   ├── invoice_processing_service.go
│   ├── tax_calculation_service.go
│   └── permission_service.go
├── main_tax_system.go     # 主程序入口
├── go.mod                 # 依赖管理
└── README.md              # 项目说明
```

## 快速开始

### 1. 环境要求
- Go 1.21 或更高版本
- Git

### 2. 安装依赖
```bash
# 进入后端目录
cd backend

# 下载依赖
go mod download
```

### 3. 启动服务
```bash
# 运行完整的税务系统
go run main_tax_system.go
```

### 4. 验证服务
访问 http://localhost:8081 查看API端点信息

## API 端点

### 🔐 认证模块
```
POST   /api/auth/login              # 用户登录
POST   /api/auth/register           # 用户注册
POST   /api/auth/logout             # 用户登出
GET    /api/auth/profile            # 获取用户资料
PUT    /api/auth/profile            # 更新用户资料
```

### 🏢 企业管理
```
POST   /api/enterprises             # 创建企业
GET    /api/enterprises             # 企业列表
GET    /api/enterprises/:id         # 获取企业详情
PUT    /api/enterprises/:id         # 更新企业信息
DELETE /api/enterprises/:id         # 删除企业

POST   /api/enterprises/:id/documents                    # 上传企业文档
GET    /api/enterprises/:id/documents                    # 获取企业文档
PUT    /api/enterprises/:id/documents/:docId/verify      # 验证企业文档
POST   /api/enterprises/:id/detect-qualification        # 检测纳税人资格
```

### ⚙️ 税种配置
```
POST   /api/tax-config/tax-types                        # 创建税种
GET    /api/tax-config/tax-types                        # 税种列表
GET    /api/tax-config/tax-types/:id                    # 获取税种详情
PUT    /api/tax-config/tax-types/:id                    # 更新税种

POST   /api/tax-config/tax-types/:id/rules              # 创建税则
GET    /api/tax-config/tax-types/:id/rules              # 获取税则列表
PUT    /api/tax-config/tax-types/:id/rules/:ruleId      # 更新税则

PUT    /api/tax-config/tax-types/:id/declaration-period # 设置申报周期

POST   /api/tax-config/enterprise-tax/:enterpriseId/enable/:taxTypeId   # 启用税种
POST   /api/tax-config/enterprise-tax/:enterpriseId/disable/:taxTypeId  # 禁用税种
GET    /api/tax-config/enterprise-tax/:enterpriseId                     # 获取企业税种配置

POST   /api/tax-config/calculate-preview                # 税款计算预览
```

### 📄 发票管理
```
POST   /api/invoices/import         # 批量导入发票
POST   /api/invoices/upload         # 上传发票文件
GET    /api/invoices               # 发票列表
GET    /api/invoices/:id           # 获取发票详情
PUT    /api/invoices/:id           # 更新发票信息
POST   /api/invoices/:id/process/:type  # 处理发票（OCR/验证）
POST   /api/invoices/verify        # 在线验证发票
```

### 📊 申报管理
```
POST   /api/declarations/calculate                      # 多税种计算
POST   /api/declarations/generate                       # 生成申报表
POST   /api/declarations/preview                        # 预览申报表
GET    /api/declarations                                # 申报列表
GET    /api/declarations/:id                            # 获取申报详情
PUT    /api/declarations/:id                            # 更新申报表
POST   /api/declarations/:id/submit                     # 提交申报
POST   /api/declarations/:id/validate                   # 验证申报表
GET    /api/declarations/enterprise/:enterpriseId/summary/:year  # 申报汇总
```

### 👥 权限管理
```
POST   /api/permissions/roles                           # 创建角色
GET    /api/permissions/roles                           # 角色列表
GET    /api/permissions/roles/:id                       # 获取角色详情
PUT    /api/permissions/roles/:id                       # 更新角色
GET    /api/permissions/roles/:id/permissions           # 获取角色权限

POST   /api/permissions/user-roles/:userId/assign/:roleId   # 分配角色
POST   /api/permissions/user-roles/:userId/revoke/:roleId   # 撤销角色
GET    /api/permissions/user-roles/:userId/roles            # 获取用户角色
GET    /api/permissions/user-roles/:userId/permissions      # 获取用户权限

POST   /api/permissions                                 # 创建权限
GET    /api/permissions                                 # 权限列表
POST   /api/permissions/check                           # 检查权限
```

### 🔧 系统管理
```
GET    /api/system/health           # 健康检查
POST   /api/system/init             # 系统初始化
```

## 默认账户

系统默认创建超级管理员账户：
- **邮箱**: <EMAIL>
- **密码**: password123

## 权限角色体系

### 系统角色
- **超级管理员**: 拥有所有权限
- **管理员**: 拥有大部分管理权限

### 企业角色
- **法人代表**: 企业最高权限
- **财务经理**: 财务相关权限
- **办税员**: 税务申报权限
- **会计**: 基础财务权限
- **查看者**: 只读权限

## 数据库设计

系统使用 SQLite 作为开发数据库，生产环境建议使用 PostgreSQL 或 MySQL。

### 核心数据表
- `users` - 用户表
- `enterprises` - 企业表
- `tax_types` - 税种表
- `tax_rules` - 税则表
- `invoices` - 发票表
- `declarations` - 申报表
- `roles` - 角色表
- `permissions` - 权限表
- `audit_logs` - 审计日志表

## 开发指南

### 添加新功能模块
1. 在 `model/` 目录下创建数据模型
2. 在 `service/` 目录下实现业务逻辑
3. 在 `api/` 目录下添加API处理程序
4. 在主程序中注册路由

### 测试
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./service
```

### 构建
```bash
# 构建可执行文件
go build -o tax_system main_tax_system.go
```

## 部署指南

### Docker 部署
```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN go build -o tax_system main_tax_system.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/tax_system .
EXPOSE 8081
CMD ["./tax_system"]
```

### 生产环境配置
1. 使用 PostgreSQL 或 MySQL 替代 SQLite
2. 配置 Redis 用于缓存和会话存储
3. 启用 HTTPS
4. 配置负载均衡
5. 设置监控和日志收集

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 📧 Email: <EMAIL>
- 🐛 Issues: GitHub Issues
- 📖 文档: API 文档

## 更新日志

### v1.0.0 (2024-12-20)
- ✅ 完整的企业税务管理系统
- ✅ 多模块功能实现
- ✅ 权限管理体系
- ✅ 智能税款计算
- ✅ 发票处理功能
- ✅ 申报表生成