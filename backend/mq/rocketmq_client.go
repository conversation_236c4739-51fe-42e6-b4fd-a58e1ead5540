package mq

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/consumer"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/apache/rocketmq-client-go/v2/producer"
	"go.uber.org/zap"
)

// RocketMQClient RocketMQ客户端
type RocketMQClient struct {
	producer  rocketmq.Producer
	consumers map[string]rocketmq.PushConsumer
	config    *RocketMQConfig
	logger    *zap.Logger
	isRunning bool
	mutex     sync.RWMutex
}

// RocketMQConfig RocketMQ配置
type RocketMQConfig struct {
	NameServers    []string      `json:"name_servers"`
	ProducerGroup  string        `json:"producer_group"`
	ConsumerGroup  string        `json:"consumer_group"`
	Namespace      string        `json:"namespace"`
	AccessKey      string        `json:"access_key"`
	SecretKey      string        `json:"secret_key"`
	SecurityToken  string        `json:"security_token"`
	Region         string        `json:"region"`
	SendTimeout    time.Duration `json:"send_timeout"`
	RetryTimes     int           `json:"retry_times"`
	MaxMessageSize int           `json:"max_message_size"`
}

// Message 消息结构
type Message struct {
	Topic      string            `json:"topic"`
	Tag        string            `json:"tag"`
	Key        string            `json:"key"`
	Body       []byte            `json:"body"`
	Properties map[string]string `json:"properties"`
	DelayLevel int               `json:"delay_level"`
}

// MessageHandler 消息处理器
type MessageHandler func(ctx context.Context, msg *primitive.MessageExt) error

// NewRocketMQClient 创建RocketMQ客户端
func NewRocketMQClient(config *RocketMQConfig, logger *zap.Logger) *RocketMQClient {
	return &RocketMQClient{
		config:    config,
		logger:    logger,
		consumers: make(map[string]rocketmq.PushConsumer),
	}
}

// Start 启动RocketMQ客户端
func (c *RocketMQClient) Start() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.isRunning {
		return fmt.Errorf("RocketMQ client is already running")
	}

	// 创建生产者
	if err := c.createProducer(); err != nil {
		return fmt.Errorf("failed to create producer: %w", err)
	}

	// 启动生产者
	if err := c.producer.Start(); err != nil {
		return fmt.Errorf("failed to start producer: %w", err)
	}

	c.isRunning = true
	c.logger.Info("RocketMQ client started successfully")

	return nil
}

// Stop 停止RocketMQ客户端
func (c *RocketMQClient) Stop() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.isRunning {
		return fmt.Errorf("RocketMQ client is not running")
	}

	// 停止所有消费者
	for topic, consumer := range c.consumers {
		if err := consumer.Shutdown(); err != nil {
			c.logger.Error("Failed to shutdown consumer",
				zap.String("topic", topic),
				zap.Error(err))
		}
	}

	// 停止生产者
	if c.producer != nil {
		if err := c.producer.Shutdown(); err != nil {
			c.logger.Error("Failed to shutdown producer", zap.Error(err))
		}
	}

	c.isRunning = false
	c.logger.Info("RocketMQ client stopped successfully")

	return nil
}

// createProducer 创建生产者
func (c *RocketMQClient) createProducer() error {
	opts := []producer.Option{
		producer.WithNameServer(c.config.NameServers),
		producer.WithGroupName(c.config.ProducerGroup),
		producer.WithSendMsgTimeout(c.config.SendTimeout),
		producer.WithRetry(c.config.RetryTimes),
	}

	if c.config.Namespace != "" {
		opts = append(opts, producer.WithNamespace(c.config.Namespace))
	}

	if c.config.AccessKey != "" && c.config.SecretKey != "" {
		opts = append(opts, producer.WithCredentials(primitive.Credentials{
			AccessKey:     c.config.AccessKey,
			SecretKey:     c.config.SecretKey,
			SecurityToken: c.config.SecurityToken,
		}))
	}

	p, err := rocketmq.NewProducer(opts...)
	if err != nil {
		return err
	}

	c.producer = p
	return nil
}

// SendMessage 发送消息
func (c *RocketMQClient) SendMessage(ctx context.Context, msg *Message) (*primitive.SendResult, error) {
	if !c.isRunning {
		return nil, fmt.Errorf("RocketMQ client is not running")
	}

	// 构建RocketMQ消息
	rocketMsg := &primitive.Message{
		Topic: msg.Topic,
		Body:  msg.Body,
	}

	if msg.Tag != "" {
		rocketMsg.WithTag(msg.Tag)
	}

	if msg.Key != "" {
		rocketMsg.WithKeys([]string{msg.Key})
	}

	if msg.DelayLevel > 0 {
		rocketMsg.WithDelayTimeLevel(msg.DelayLevel)
	}

	// 设置属性
	for key, value := range msg.Properties {
		rocketMsg.WithProperty(key, value)
	}

	// 发送消息
	result, err := c.producer.SendSync(ctx, rocketMsg)
	if err != nil {
		c.logger.Error("Failed to send message",
			zap.String("topic", msg.Topic),
			zap.String("tag", msg.Tag),
			zap.String("key", msg.Key),
			zap.Error(err))
		return nil, err
	}

	c.logger.Debug("Message sent successfully",
		zap.String("topic", msg.Topic),
		zap.String("tag", msg.Tag),
		zap.String("key", msg.Key),
		zap.String("msg_id", result.MsgID))

	return result, nil
}

// SendAsyncMessage 异步发送消息
func (c *RocketMQClient) SendAsyncMessage(ctx context.Context, msg *Message, callback func(*primitive.SendResult, error)) error {
	if !c.isRunning {
		return fmt.Errorf("RocketMQ client is not running")
	}

	// 构建RocketMQ消息
	rocketMsg := &primitive.Message{
		Topic: msg.Topic,
		Body:  msg.Body,
	}

	if msg.Tag != "" {
		rocketMsg.WithTag(msg.Tag)
	}

	if msg.Key != "" {
		rocketMsg.WithKeys([]string{msg.Key})
	}

	if msg.DelayLevel > 0 {
		rocketMsg.WithDelayTimeLevel(msg.DelayLevel)
	}

	// 设置属性
	for key, value := range msg.Properties {
		rocketMsg.WithProperty(key, value)
	}

	// 异步发送消息
	err := c.producer.SendAsync(ctx, func(ctx context.Context, result *primitive.SendResult, err error) {
		if err != nil {
			c.logger.Error("Failed to send async message",
				zap.String("topic", msg.Topic),
				zap.String("tag", msg.Tag),
				zap.String("key", msg.Key),
				zap.Error(err))
		} else {
			c.logger.Debug("Async message sent successfully",
				zap.String("topic", msg.Topic),
				zap.String("tag", msg.Tag),
				zap.String("key", msg.Key),
				zap.String("msg_id", result.MsgID))
		}

		if callback != nil {
			callback(result, err)
		}
	}, rocketMsg)

	return err
}

// SendBatchMessages 批量发送消息
func (c *RocketMQClient) SendBatchMessages(ctx context.Context, messages []*Message) (*primitive.SendResult, error) {
	if !c.isRunning {
		return nil, fmt.Errorf("RocketMQ client is not running")
	}

	if len(messages) == 0 {
		return nil, fmt.Errorf("no messages to send")
	}

	// 构建RocketMQ消息列表
	var rocketMsgs []*primitive.Message
	for _, msg := range messages {
		rocketMsg := &primitive.Message{
			Topic: msg.Topic,
			Body:  msg.Body,
		}

		if msg.Tag != "" {
			rocketMsg.WithTag(msg.Tag)
		}

		if msg.Key != "" {
			rocketMsg.WithKeys([]string{msg.Key})
		}

		// 设置属性
		for key, value := range msg.Properties {
			rocketMsg.WithProperty(key, value)
		}

		rocketMsgs = append(rocketMsgs, rocketMsg)
	}

	// 批量发送消息
	result, err := c.producer.SendSync(ctx, rocketMsgs...)
	if err != nil {
		c.logger.Error("Failed to send batch messages",
			zap.Int("count", len(messages)),
			zap.Error(err))
		return nil, err
	}

	c.logger.Debug("Batch messages sent successfully",
		zap.Int("count", len(messages)),
		zap.String("msg_id", result.MsgID))

	return result, nil
}

// Subscribe 订阅主题
func (c *RocketMQClient) Subscribe(topic, tag string, handler MessageHandler) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 检查是否已经订阅
	if _, exists := c.consumers[topic]; exists {
		return fmt.Errorf("topic %s already subscribed", topic)
	}

	// 创建消费者选项
	opts := []consumer.Option{
		consumer.WithNameServer(c.config.NameServers),
		consumer.WithGroupName(c.config.ConsumerGroup),
		consumer.WithConsumeFromWhere(consumer.ConsumeFromLastOffset),
		consumer.WithConsumerModel(consumer.Clustering),
	}

	if c.config.Namespace != "" {
		opts = append(opts, consumer.WithNamespace(c.config.Namespace))
	}

	if c.config.AccessKey != "" && c.config.SecretKey != "" {
		opts = append(opts, consumer.WithCredentials(primitive.Credentials{
			AccessKey:     c.config.AccessKey,
			SecretKey:     c.config.SecretKey,
			SecurityToken: c.config.SecurityToken,
		}))
	}

	// 创建消费者
	pushConsumer, err := rocketmq.NewPushConsumer(opts...)
	if err != nil {
		return fmt.Errorf("failed to create consumer for topic %s: %w", topic, err)
	}

	// 订阅主题
	selector := consumer.MessageSelector{
		Type:       consumer.TAG,
		Expression: tag,
	}

	err = pushConsumer.Subscribe(topic, selector, func(ctx context.Context, msgs ...*primitive.MessageExt) (consumer.ConsumeResult, error) {
		for _, msg := range msgs {
			if err := handler(ctx, msg); err != nil {
				c.logger.Error("Failed to handle message",
					zap.String("topic", topic),
					zap.String("tag", tag),
					zap.String("msg_id", msg.MsgId),
					zap.Error(err))
				return consumer.ConsumeRetryLater, err
			}
		}
		return consumer.ConsumeSuccess, nil
	})

	if err != nil {
		return fmt.Errorf("failed to subscribe topic %s: %w", topic, err)
	}

	// 启动消费者
	if c.isRunning {
		if err := pushConsumer.Start(); err != nil {
			return fmt.Errorf("failed to start consumer for topic %s: %w", topic, err)
		}
	}

	c.consumers[topic] = pushConsumer

	c.logger.Info("Subscribed to topic successfully",
		zap.String("topic", topic),
		zap.String("tag", tag))

	return nil
}

// Unsubscribe 取消订阅主题
func (c *RocketMQClient) Unsubscribe(topic string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	consumer, exists := c.consumers[topic]
	if !exists {
		return fmt.Errorf("topic %s not subscribed", topic)
	}

	// 停止消费者
	if err := consumer.Shutdown(); err != nil {
		c.logger.Error("Failed to shutdown consumer",
			zap.String("topic", topic),
			zap.Error(err))
	}

	delete(c.consumers, topic)

	c.logger.Info("Unsubscribed from topic successfully", zap.String("topic", topic))

	return nil
}

// IsRunning 检查客户端是否运行中
func (c *RocketMQClient) IsRunning() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.isRunning
}

// GetProducerStatus 获取生产者状态
func (c *RocketMQClient) GetProducerStatus() map[string]interface{} {
	status := map[string]interface{}{
		"is_running": c.isRunning,
		"group":      c.config.ProducerGroup,
	}

	if c.producer != nil {
		// 这里可以添加更多生产者状态信息
		status["name_servers"] = c.config.NameServers
	}

	return status
}

// GetConsumerStatus 获取消费者状态
func (c *RocketMQClient) GetConsumerStatus() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	status := map[string]interface{}{
		"is_running":        c.isRunning,
		"group":             c.config.ConsumerGroup,
		"subscribed_topics": make([]string, 0, len(c.consumers)),
	}

	for topic := range c.consumers {
		status["subscribed_topics"] = append(status["subscribed_topics"].([]string), topic)
	}

	return status
}

// CreateMessage 创建消息的辅助方法
func CreateMessage(topic, tag, key string, body interface{}) (*Message, error) {
	var bodyBytes []byte
	var err error

	switch v := body.(type) {
	case []byte:
		bodyBytes = v
	case string:
		bodyBytes = []byte(v)
	default:
		bodyBytes, err = json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal message body: %w", err)
		}
	}

	return &Message{
		Topic: topic,
		Tag:   tag,
		Key:   key,
		Body:  bodyBytes,
		Properties: map[string]string{
			"timestamp": fmt.Sprintf("%d", time.Now().Unix()),
		},
	}, nil
}
