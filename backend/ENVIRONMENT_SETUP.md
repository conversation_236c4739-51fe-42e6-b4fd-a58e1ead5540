# 环境配置使用指南
# Environment Configuration Guide

本文档介绍如何在不同环境中配置和运行税务管理系统后端。

## 🌍 支持的环境

| 环境 | 配置文件 | 数据库 | 特点 |
|------|---------|--------|------|
| 开发环境 (dev) | `config.dev.yaml` | `smeasy_tax_dev` | 调试模式、自动迁移、长Token |
| 测试环境 (test) | `config.test.yaml` | `smeasy_tax_test` | 调试模式、Mock服务、无缓存 |
| 生产环境 (prod) | `config.prod.yaml` | `smeasy_tax` | 安全配置、性能优化、监控 |

## 🚀 快速开始

### 1. 开发环境

```bash
# 方法1: 使用启动脚本（推荐）
./scripts/start-dev.sh

# 方法2: 手动设置环境变量
export APP_ENV=dev
go run main.go

# 方法3: 临时设置
APP_ENV=dev go run main.go
```

### 2. 测试环境

```bash
# 方法1: 使用启动脚本
./scripts/start-test.sh

# 方法2: 手动设置
export APP_ENV=test
go run main.go
```

### 3. 生产环境

```bash
# 设置必需的环境变量
export APP_ENV=prod
export JWT_SECRET="your-super-secret-jwt-key-32-chars"
export DB_PASSWORD="your-strong-database-password"

# 方法1: 使用启动脚本（推荐）
./scripts/start-prod.sh

# 方法2: 手动启动
go build -o tax-backend main.go
./tax-backend
```

## ⚙️ 环境变量配置

### 必需的环境变量

#### 开发环境
```bash
APP_ENV=dev
# 其他配置可选，有默认值
```

#### 测试环境
```bash
APP_ENV=test
# 其他配置可选，有默认值
```

#### 生产环境
```bash
APP_ENV=prod
JWT_SECRET=your-super-secret-jwt-key-32-chars
DB_PASSWORD=your-strong-database-password
```

### 可选的环境变量

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_NAME=smeasy_tax

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 存储配置
STORAGE_TYPE=local
STORAGE_PATH=./storage

# 第三方服务
TAX_BUREAU_API_KEY=your-api-key
EINVOICE_API_KEY=your-api-key
```

## 📁 配置文件详解

### 开发环境配置特点
- 数据库: `smeasy_tax_dev`
- Redis DB: 1
- 调试模式: 开启
- 自动迁移: 开启
- Token有效期: 120分钟
- 连接数: 20

### 测试环境配置特点
- 数据库: `smeasy_tax_test`
- Redis DB: 2
- 调试模式: 开启
- 自动迁移: 开启
- Token有效期: 240分钟
- 连接数: 10
- 缓存: 关闭

### 生产环境配置特点
- 数据库: `smeasy_tax`
- Redis DB: 0
- 调试模式: 关闭
- 自动迁移: 关闭
- Token有效期: 60分钟
- 连接数: 100
- 安全验证: 严格

## 🔒 安全最佳实践

### 1. JWT密钥
```bash
# 生成强密钥
openssl rand -base64 32

# 设置环境变量
export JWT_SECRET="生成的32位密钥"
```

### 2. 数据库密码
```bash
# 使用强密码
export DB_PASSWORD="复杂的数据库密码"
```

### 3. 环境变量文件
```bash
# 创建 .env 文件（不要提交到Git）
cp .env.example .env
# 编辑 .env 文件设置实际值
```

## 🐳 Docker部署

### 开发环境
```dockerfile
# Dockerfile.dev
FROM golang:1.21-alpine
WORKDIR /app
COPY . .
ENV APP_ENV=dev
RUN go build -o main .
CMD ["./main"]
```

### 生产环境
```dockerfile
# Dockerfile.prod
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -ldflags="-w -s" -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/main .
ENV APP_ENV=prod
CMD ["./main"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    environment:
      - APP_ENV=prod
      - JWT_SECRET=${JWT_SECRET}
      - DB_PASSWORD=${DB_PASSWORD}
    ports:
      - "8081:8081"
```

## 🔍 故障排除

### 1. 配置文件未找到
```
Config file config.prod not found, using defaults and environment variables
```
**解决方案**: 确保配置文件存在于 `config/` 目录

### 2. 生产环境验证失败
```
JWT secret is required in production environment
```
**解决方案**: 设置 `JWT_SECRET` 环境变量

### 3. 数据库连接失败
```
database password is required in production environment
```
**解决方案**: 设置 `DB_PASSWORD` 环境变量

### 4. 环境变量不生效
**检查步骤**:
1. 确认环境变量名称正确
2. 确认环境变量已导出 (`export`)
3. 重启应用程序

## 📊 配置验证

运行配置测试程序：
```bash
go run cmd/test-config/main.go
```

输出示例：
```
=== 测试 dev 环境配置 ===
应用名称: SMEasyTax-Dev
调试模式: true
数据库名: smeasy_tax_dev
Redis DB: 1
最大连接数: 20
自动迁移: true
Token有效期: 120分钟
```

## 📝 配置检查清单

### 开发环境
- [ ] 设置 `APP_ENV=dev`
- [ ] 确认数据库 `smeasy_tax_dev` 存在
- [ ] Redis 可访问

### 测试环境
- [ ] 设置 `APP_ENV=test`
- [ ] 确认数据库 `smeasy_tax_test` 存在
- [ ] Mock服务配置正确

### 生产环境
- [ ] 设置 `APP_ENV=prod`
- [ ] 设置强 `JWT_SECRET`
- [ ] 设置 `DB_PASSWORD`
- [ ] 数据库 `smeasy_tax` 存在
- [ ] 关闭调试模式
- [ ] 配置监控和日志
- [ ] 设置CORS白名单
- [ ] 配置第三方API密钥

## 🔄 环境切换

### 本地开发切换
```bash
# 切换到开发环境
export APP_ENV=dev

# 切换到测试环境
export APP_ENV=test

# 重启应用生效
```

### CI/CD环境切换
```yaml
# GitHub Actions 示例
env:
  APP_ENV: ${{ github.ref == 'refs/heads/main' && 'prod' || 'dev' }}
  JWT_SECRET: ${{ secrets.JWT_SECRET }}
  DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
```

## 📚 相关文档

- [配置文件详细说明](config/README.md)
- [API文档](api/README.md)
- [部署指南](DEPLOYMENT.md)
- [开发指南](DEVELOPMENT.md)
