package router

import (
	"github.com/gin-gonic/gin"

	"backend/controller"
	"backend/middleware"
	"backend/service"
)

// SetupTaxFilingRoutes 设置税务申报路由
func SetupTaxFilingRoutes(r *gin.RouterGroup, taxFilingController *controller.TaxFilingController, authService *service.AuthService) {
	// 税务申报路由组
	taxFilingGroup := r.Group("/tax-filing")
	{
		// 应用认证中间件
		taxFilingGroup.Use(middleware.Auth(authService))

		// 申报管理路由
		submissions := taxFilingGroup.Group("/submissions")
		{
			submissions.POST("", taxFilingController.CreateSubmission)                 // 创建申报
			submissions.GET("", taxFilingController.ListSubmissions)                   // 获取申报列表
			submissions.GET("/:id", taxFilingController.GetSubmission)                 // 获取申报详情
			submissions.PUT("/:id/status", taxFilingController.UpdateSubmissionStatus) // 更新申报状态
			submissions.POST("/:id/submit", taxFilingController.SubmitToTaxBureau)     // 提交到税务局
			submissions.POST("/:id/sync", taxFilingController.SyncSubmissionStatus)    // 同步状态
			submissions.POST("/:id/retry", taxFilingController.RetrySubmission)        // 重试申报
			submissions.POST("/:id/cancel", taxFilingController.CancelSubmission)      // 取消申报
		}

		// 批次管理路由
		batches := taxFilingGroup.Group("/batches")
		{
			batches.POST("", taxFilingController.CreateBatch)              // 创建批次
			batches.GET("/:id", taxFilingController.GetBatch)              // 获取批次详情
			batches.POST("/:id/process", taxFilingController.ProcessBatch) // 处理批次
		}
	}
}

// SetupTaxFilingWebhookRoutes 设置税务申报Webhook路由
func SetupTaxFilingWebhookRoutes(r *gin.RouterGroup, webhookController *controller.TaxFilingWebhookController) {
	// Webhook路由组（不需要认证）
	webhookGroup := r.Group("/tax-filing")
	{
		webhookGroup.POST("/webhook", webhookController.HandleWebhook)             // 处理Webhook
		webhookGroup.POST("/webhook/status", webhookController.HandleStatusUpdate) // 处理状态更新
		webhookGroup.POST("/webhook/batch", webhookController.HandleBatchUpdate)   // 处理批次更新
	}
}
