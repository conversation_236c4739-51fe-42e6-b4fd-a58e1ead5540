// Package router provides HTTP route configuration and registration for the tax management system.
// It defines API endpoints, middleware setup, and route grouping for different functional modules.
package router

import (
	"time"

	"github.com/gin-gonic/gin"

	"backend/config"
	"backend/controller"
	"backend/middleware"
	"backend/service"
	"backend/util"
)

// RegisterAPIRoutes 注册所有 API 路由
func RegisterAPIRoutes(
	r *gin.Engine,
	authService *service.AuthService,
	enterpriseService service.EnterpriseService,
	userManagementService *service.UserManagementService,
	permissionService *service.PermissionService,
	cfg *config.Config,
) {
	// 创建控制器
	authController := controller.NewAuthController(authService, cfg)
	enterpriseController := controller.NewEnterpriseController(enterpriseService)
	userController := controller.NewUserManagementController(userManagementService, permissionService)

	// 注册基本路由
	apiGroup := r.Group("/api")

	// 系统健康检查
	apiGroup.GET("/system/health", func(c *gin.Context) {
		util.Success(c, gin.H{
			"status":    "ok",
			"timestamp": time.Now(),
			"version":   cfg.App.Version,
			"database":  "connected",
		}, "税易通运行正常")
	})

	// Auth routes - 认证相关路由
	auth := apiGroup.Group("/auth")
	{
		auth.POST("/login", authController.Login)
		auth.POST("/register", authController.Register)
		auth.POST("/logout", authController.Logout)
		auth.POST("/refresh-token", authController.RefreshToken)
		auth.POST("/forgot-password", authController.ForgotPassword)
		auth.POST("/reset-password", authController.ResetPassword)

		// 需要认证的路由
		authProtected := auth.Group("")
		authProtected.Use(middleware.Auth(authService))
		{
			authProtected.GET("/profile", authController.GetProfile)
			authProtected.PUT("/profile", authController.UpdateProfile)
			authProtected.POST("/change-password", authController.ChangePassword)
		}
	}

	// Enterprise routes - 企业管理路由
	enterprises := apiGroup.Group("/enterprises")
	enterprises.Use(middleware.Auth(authService))
	{
		enterprises.POST("", enterpriseController.CreateEnterprise)
		enterprises.GET("", enterpriseController.GetEnterprises)
		enterprises.GET("/stats", enterpriseController.GetEnterpriseStats) // 全局企业统计
		enterprises.GET("/:id", enterpriseController.GetEnterpriseByID)
		enterprises.PUT("/:id", enterpriseController.UpdateEnterprise)
		enterprises.DELETE("/:id", enterpriseController.DeleteEnterprise)
		enterprises.GET("/:id/stats", enterpriseController.GetEnterpriseDetailStats) // 单个企业统计
		enterprises.GET("/:id/declarations", enterpriseController.GetEnterpriseDeclarations)

		// 企业用户管理
		enterprises.GET("/:id/users", userController.GetEnterpriseUsers)
	}

	// User management routes - 用户管理路由
	users := apiGroup.Group("/users")
	users.Use(middleware.Auth(authService))
	{
		users.PUT("/enterprises/:enterpriseId/users/:userId/role", userController.UpdateUserRole)
		users.GET("/enterprises/:enterpriseId/users/:userId/permissions", userController.GetUserPermissions)
	}

	// TODO: 其他功能路由将在后续添加
	// 如：发票管理、申报管理、文件上传等
}

// RegisterTaxFilingRoutes 注册税务申报相关路由（暂时禁用，等待controller完善）
func RegisterTaxFilingRoutes(
	r *gin.RouterGroup,
	taxFilingController *controller.TaxFilingController,
	taxFilingMonitorController *controller.TaxFilingMonitorController,
	taxFilingProvinceController *controller.TaxFilingProvinceController,
	authService *service.AuthService,
) {
	// TODO: 等待税务申报controller完善后再启用这些路由
	_ = taxFilingController
	_ = taxFilingMonitorController
	_ = taxFilingProvinceController
}

// SetupRoutes 设置所有路由
func SetupRoutes(
	r *gin.Engine,
	authService *service.AuthService,
	enterpriseService service.EnterpriseService,
	userManagementService *service.UserManagementService,
	permissionService *service.PermissionService,
	taxFilingController *controller.TaxFilingController,
	taxFilingMonitorController *controller.TaxFilingMonitorController,
	taxFilingProvinceController *controller.TaxFilingProvinceController,
	cfg *config.Config,
) {
	// 注册基础API路由
	RegisterAPIRoutes(r, authService, enterpriseService, userManagementService, permissionService, cfg)

	// 暂时不注册税务申报路由，等待controller完善
	// apiGroup := r.Group("/api")
	// RegisterTaxFilingRoutes(apiGroup, taxFilingController, taxFilingMonitorController, taxFilingProvinceController, authService)
}
