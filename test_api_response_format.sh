#!/bin/bash

# 税易通系统 - API响应格式验证脚本

BASE_URL="http://localhost:8081"
echo "🧪 开始验证API响应格式标准..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 验证函数
validate_response_format() {
    local response="$1"
    local test_name="$2"
    
    echo -e "\n${BLUE}📋 测试: $test_name${NC}"
    
    # 检查必需字段
    local has_code=$(echo "$response" | jq 'has("code")')
    local has_message=$(echo "$response" | jq 'has("message")')
    local has_timestamp=$(echo "$response" | jq 'has("timestamp")')
    local has_requestId=$(echo "$response" | jq 'has("requestId")')
    
    if [[ "$has_code" == "true" && "$has_message" == "true" && "$has_timestamp" == "true" && "$has_requestId" == "true" ]]; then
        echo -e "  ${GREEN}✅ 基础响应格式正确${NC}"
        echo "  📊 响应字段:"
        echo "    - code: $(echo "$response" | jq '.code')"
        echo "    - message: $(echo "$response" | jq -r '.message')"
        echo "    - timestamp: $(echo "$response" | jq '.timestamp')"
        echo "    - requestId: $(echo "$response" | jq -r '.requestId')"
        return 0
    else
        echo -e "  ${RED}❌ 基础响应格式不完整${NC}"
        echo "    - code: $has_code"
        echo "    - message: $has_message"
        echo "    - timestamp: $has_timestamp"
        echo "    - requestId: $has_requestId"
        return 1
    fi
}

validate_pagination_format() {
    local response="$1"
    local test_name="$2"
    
    echo -e "\n${BLUE}📋 测试: $test_name (分页格式)${NC}"
    
    # 检查分页字段
    local has_items=$(echo "$response" | jq '.data | has("items")')
    local has_total=$(echo "$response" | jq '.data | has("total")')
    local has_page=$(echo "$response" | jq '.data | has("page")')
    local has_pageSize=$(echo "$response" | jq '.data | has("pageSize")')
    local has_pages=$(echo "$response" | jq '.data | has("pages")')
    
    if [[ "$has_items" == "true" && "$has_total" == "true" && "$has_page" == "true" && "$has_pageSize" == "true" && "$has_pages" == "true" ]]; then
        echo -e "  ${GREEN}✅ 分页响应格式正确${NC}"
        echo "  📊 分页字段:"
        echo "    - items: $(echo "$response" | jq '.data.items | length') 条记录"
        echo "    - total: $(echo "$response" | jq '.data.total')"
        echo "    - page: $(echo "$response" | jq '.data.page')"
        echo "    - pageSize: $(echo "$response" | jq '.data.pageSize')"
        echo "    - pages: $(echo "$response" | jq '.data.pages')"
        return 0
    else
        echo -e "  ${RED}❌ 分页响应格式不完整${NC}"
        echo "    - items: $has_items"
        echo "    - total: $has_total"
        echo "    - page: $has_page"
        echo "    - pageSize: $has_pageSize"
        echo "    - pages: $has_pages"
        return 1
    fi
}

# 测试计数器
total_tests=0
passed_tests=0

echo -e "\n${YELLOW}🔍 第一部分: 基础API响应格式验证${NC}"

# 1. 测试根路径
echo -e "\n${BLUE}1. 测试根路径响应格式${NC}"
response=$(curl -s "$BASE_URL/")
if validate_response_format "$response" "根路径"; then
    ((passed_tests++))
fi
((total_tests++))

# 2. 测试健康检查
echo -e "\n${BLUE}2. 测试健康检查响应格式${NC}"
response=$(curl -s "$BASE_URL/api/system/health")
if validate_response_format "$response" "健康检查"; then
    ((passed_tests++))
fi
((total_tests++))

# 3. 测试错误响应格式
echo -e "\n${BLUE}3. 测试错误响应格式${NC}"
response=$(curl -s "$BASE_URL/api/enterprises")
if validate_response_format "$response" "401错误响应"; then
    ((passed_tests++))
fi
((total_tests++))

# 4. 测试404错误
echo -e "\n${BLUE}4. 测试404错误响应格式${NC}"
response=$(curl -s "$BASE_URL/api/nonexistent")
if validate_response_format "$response" "404错误响应"; then
    ((passed_tests++))
fi
((total_tests++))

echo -e "\n${YELLOW}🔍 第二部分: 数据字段命名验证${NC}"

# 5. 检查是否还有使用旧格式的API
echo -e "\n${BLUE}5. 检查数据字段命名规范${NC}"
echo "  📝 验证要点:"
echo "    - 分页数据必须使用 'items' 字段"
echo "    - 禁止使用 'data'、'list'、'records' 等字段名"
echo "    - 必须包含完整的分页信息"

echo -e "\n${YELLOW}🔍 第三部分: 响应时间和性能验证${NC}"

# 6. 测试响应时间
echo -e "\n${BLUE}6. 测试API响应时间${NC}"
start_time=$(date +%s%N)
response=$(curl -s "$BASE_URL/api/system/health")
end_time=$(date +%s%N)
response_time=$(( (end_time - start_time) / 1000000 ))

echo "  ⏱️  响应时间: ${response_time}ms"
if [ $response_time -lt 1000 ]; then
    echo -e "  ${GREEN}✅ 响应时间良好 (<1000ms)${NC}"
    ((passed_tests++))
else
    echo -e "  ${YELLOW}⚠️  响应时间较慢 (>1000ms)${NC}"
fi
((total_tests++))

echo -e "\n${YELLOW}🔍 第四部分: 请求ID唯一性验证${NC}"

# 7. 测试请求ID唯一性
echo -e "\n${BLUE}7. 测试请求ID唯一性${NC}"
response1=$(curl -s "$BASE_URL/api/system/health")
response2=$(curl -s "$BASE_URL/api/system/health")

requestId1=$(echo "$response1" | jq -r '.requestId')
requestId2=$(echo "$response2" | jq -r '.requestId')

echo "  🔑 请求ID1: $requestId1"
echo "  🔑 请求ID2: $requestId2"

if [ "$requestId1" != "$requestId2" ]; then
    echo -e "  ${GREEN}✅ 请求ID唯一性正确${NC}"
    ((passed_tests++))
else
    echo -e "  ${RED}❌ 请求ID重复${NC}"
fi
((total_tests++))

echo -e "\n${YELLOW}🔍 第五部分: 时间戳格式验证${NC}"

# 8. 测试时间戳格式
echo -e "\n${BLUE}8. 测试时间戳格式${NC}"
response=$(curl -s "$BASE_URL/api/system/health")
timestamp=$(echo "$response" | jq '.timestamp')
current_time=$(date +%s)
time_diff=$((current_time - timestamp))

echo "  🕐 API时间戳: $timestamp"
echo "  🕐 当前时间戳: $current_time"
echo "  ⏰ 时间差: ${time_diff}秒"

if [ $time_diff -lt 60 ] && [ $time_diff -gt -60 ]; then
    echo -e "  ${GREEN}✅ 时间戳格式正确且时间同步${NC}"
    ((passed_tests++))
else
    echo -e "  ${YELLOW}⚠️  时间戳可能不准确${NC}"
fi
((total_tests++))

# 总结报告
echo -e "\n${YELLOW}📊 测试结果总结${NC}"
echo "=================================="
echo -e "总测试数: ${BLUE}$total_tests${NC}"
echo -e "通过测试: ${GREEN}$passed_tests${NC}"
echo -e "失败测试: ${RED}$((total_tests - passed_tests))${NC}"

if [ $passed_tests -eq $total_tests ]; then
    echo -e "\n${GREEN}🎉 所有测试通过！API响应格式完全符合标准${NC}"
    echo -e "${GREEN}✅ 统一响应格式实施成功${NC}"
    exit 0
else
    echo -e "\n${RED}❌ 部分测试失败，需要修复API响应格式${NC}"
    echo -e "${YELLOW}📋 修复建议:${NC}"
    echo "1. 确保所有API返回统一的响应格式"
    echo "2. 检查timestamp和requestId字段是否正确添加"
    echo "3. 验证分页数据使用items字段"
    echo "4. 确保错误响应格式一致"
    exit 1
fi
