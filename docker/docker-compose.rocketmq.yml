version: '3.8'

services:
  # RocketMQ NameServer
  rocketmq-nameserver:
    image: apache/rocketmq:4.9.4
    container_name: rocketmq-nameserver
    ports:
      - "9876:9876"
    environment:
      - JAVA_OPT_EXT=-server -Xms512m -Xmx512m
    command: ["sh", "mqnamesrv"]
    networks:
      - rocketmq-network
    volumes:
      - rocketmq-nameserver-data:/home/<USER>/logs
    restart: unless-stopped

  # RocketMQ Broker
  rocketmq-broker:
    image: apache/rocketmq:4.9.4
    container_name: rocketmq-broker
    ports:
      - "10909:10909"
      - "10911:10911"
    environment:
      - JAVA_OPT_EXT=-server -Xms1g -Xmx1g
      - NAMESRV_ADDR=rocketmq-nameserver:9876
    command: ["sh", "mqbroker", "-n", "rocketmq-nameserver:9876", "-c", "/opt/rocketmq-4.9.4/conf/broker.conf"]
    depends_on:
      - rocketmq-nameserver
    networks:
      - rocketmq-network
    volumes:
      - rocketmq-broker-data:/home/<USER>/logs
      - rocketmq-broker-store:/home/<USER>/store
      - ./rocketmq/broker.conf:/opt/rocketmq-4.9.4/conf/broker.conf
    restart: unless-stopped

  # RocketMQ Console (管理界面)
  rocketmq-console:
    image: styletang/rocketmq-console-ng:latest
    container_name: rocketmq-console
    ports:
      - "8080:8080"
    environment:
      - JAVA_OPTS=-Drocketmq.namesrv.addr=rocketmq-nameserver:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false
    depends_on:
      - rocketmq-nameserver
      - rocketmq-broker
    networks:
      - rocketmq-network
    restart: unless-stopped

  # Redis (用于缓存)
  redis:
    image: redis:7-alpine
    container_name: tax-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass "redis123"
    networks:
      - rocketmq-network
    volumes:
      - redis-data:/data
    restart: unless-stopped

  # MySQL (数据库)
  mysql:
    image: mysql:8.0
    container_name: tax-mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=Aa123456@
      - MYSQL_DATABASE=smeasy_tax
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    networks:
      - rocketmq-network
    volumes:
      - mysql-data:/var/lib/mysql
      - ../backend/sql:/docker-entrypoint-initdb.d
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

networks:
  rocketmq-network:
    driver: bridge

volumes:
  rocketmq-nameserver-data:
  rocketmq-broker-data:
  rocketmq-broker-store:
  redis-data:
  mysql-data:
