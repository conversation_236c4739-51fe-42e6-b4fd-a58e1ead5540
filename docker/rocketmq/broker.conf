# RocketMQ Broker配置文件

# Broker集群名称
brokerClusterName = DefaultCluster

# Broker名称
brokerName = broker-a

# Broker ID (0表示Master，>0表示Slave)
brokerId = 0

# NameServer地址
namesrvAddr = rocketmq-nameserver:9876

# 删除文件时间点，默认凌晨4点
deleteWhen = 04

# 文件保留时间，默认72小时
fileReservedTime = 72

# Broker角色
# - ASYNC_MASTER 异步复制Master
# - SYNC_MASTER 同步双写Master
# - SLAVE
brokerRole = ASYNC_MASTER

# 刷盘方式
# - ASYNC_FLUSH 异步刷盘
# - SYNC_FLUSH 同步刷盘
flushDiskType = ASYNC_FLUSH

# Broker监听端口
listenPort = 10911

# 存储路径
storePathRootDir = /home/<USER>/store
storePathCommitLog = /home/<USER>/store/commitlog

# 是否允许Broker自动创建Topic
autoCreateTopicEnable = true

# 是否允许Broker自动创建订阅组
autoCreateSubscriptionGroup = true

# 发送消息线程池数量
sendMessageThreadPoolNums = 128

# 拉取消息线程池数量
pullMessageThreadPoolNums = 128

# 查询消息线程池数量
queryMessageThreadPoolNums = 8

# 管理Broker线程池数量
adminBrokerThreadPoolNums = 16

# 客户端管理线程池数量
clientManageThreadPoolNums = 32

# 消费者管理线程池数量
consumerManageThreadPoolNums = 32

# 心跳线程池数量
heartbeatThreadPoolNums = 8

# 结束事务线程池数量
endTransactionThreadPoolNums = 8

# 磁盘使用率超过多少后开始拒绝写入
diskMaxUsedSpaceRatio = 88

# 是否开启字节缓冲区重用
transientStorePoolEnable = false

# 字节缓冲区大小
transientStorePoolSize = 5

# 是否快速失败
fastFailIfNoBufferInStorePool = false

# 是否开启DLeger
enableDLegerCommitLog = false

# 是否开启Controller模式
enableControllerMode = false

# 网络相关配置
# 客户端连接超时时间
clientChannelMaxIdleTimeSeconds = 120

# 服务端发送缓冲区大小
serverSocketSndBufSize = 131072

# 服务端接收缓冲区大小
serverSocketRcvBufSize = 131072

# 客户端发送缓冲区大小
clientSocketSndBufSize = 131072

# 客户端接收缓冲区大小
clientSocketRcvBufSize = 131072

# 是否开启Epoll
useEpollNativeSelector = false

# 消息相关配置
# 最大消息大小 (4MB)
maxMessageSize = 4194304

# 是否检查CRC32
checkCRC32 = false

# 是否开启消息轨迹
traceOn = true

# 消息轨迹Topic
msgTraceTopicName = RMQ_SYS_TRACE_TOPIC

# 是否开启ACL
aclEnable = false

# 是否开启Pop消费
enablePopLog = false

# 是否开启定时消息
enableScheduleMessageStats = true

# 是否开启批量消息
enableBatchPush = false

# 是否开启压缩
compressedRegister = false

# 是否开启广播消费
enableBroadcastOffsetStore = true

# 高可用相关配置
# 是否开启主从同步
enableSlaveActingMaster = false

# 主从同步超时时间
syncFlushTimeout = 5000

# 主从同步间隔
slaveReadEnable = false

# 性能调优配置
# 是否开启内存映射文件预热
warmMapedFileEnable = false

# 内存映射文件大小
mapedFileSizeCommitLog = 1073741824

# 索引文件大小
mapedFileSizeConsumeQueue = 6000000

# 是否开启内存锁定
lockInStrictMode = false

# 刷盘间隔
flushIntervalCommitLog = 500

# 提交间隔
commitIntervalCommitLog = 200

# 是否开启异步刷盘
useReentrantLockWhenPutMessage = true

# 等待存储完成的最大时间
waitTimeMillsInSendQueue = 200

# 事务相关配置
# 事务消息检查间隔
transactionCheckInterval = 60000

# 事务消息检查最大次数
transactionCheckMax = 15

# 事务超时时间
transactionTimeOut = 6000

# 日志配置
# 是否开启操作系统页缓存
enableCalcFilterBitMap = false

# 是否期望从主节点消费
expectConsumerNumUseFilter = 32

# 最大错误重试次数
maxErrorRateOfBloomFilter = 20

# 过滤服务器数量
filterServerNums = 0
