# ========== 操作系统相关 ==========

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ========== IDE和编辑器 ==========

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# JetBrains IDEs
.idea/
*.iws
*.iml
*.ipr
out/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ========== Go后端相关 ==========

# Go编译输出
*.exe
*.exe~
*.dll
*.so
*.dylib
backend/main
backend/tax-backend
backend/tax-system
backend/*.exe

# Go测试输出
*.test
*.out
*.prof

# Go模块缓存
go.sum.backup

# Go工作区
go.work
go.work.sum

# 依赖目录
vendor/

# 二进制文件
bin/
dist/

# 调试文件
debug
*.pdb

# Go覆盖率文件
coverage.txt
coverage.html
*.cover

# Air热重载工具
tmp/
.air.toml

# ========== Node.js前端相关 ==========

# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov
.nyc_output

# 构建输出
frontend/dist/
frontend/build/
frontend/.nuxt/
frontend/.next/
frontend/.vuepress/dist/

# 缓存目录
.npm
.eslintcache
.stylelintcache
.parcel-cache/
.cache/

# 可选的npm缓存目录
.npm

# 可选的REPL历史
.node_repl_history

# 输出的npm包
*.tgz

# Yarn完整性文件
.yarn-integrity

# dotenv环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Storybook构建输出
.out
.storybook-out

# ========== 数据库相关 ==========

# SQLite数据库文件
*.sqlite
*.sqlite3
*.db

# MySQL dump文件
*.sql

# 数据库备份文件
*.backup
*.bak

# ========== 日志文件 ==========

# 应用日志
logs/
*.log
log/

# 系统日志
syslog
auth.log

# ========== 配置文件 ==========

# 本地配置文件
config/local.yaml
config/local.yml
config/local.json
config/development.yaml
config/development.yml
config/production.yaml
config/production.yml

# 环境配置
.env.local
.env.*.local

# ========== 安全相关 ==========

# 密钥文件
*.key
*.pem
*.crt
*.p12
*.pfx
private/
secrets/

# JWT密钥
jwt.key
jwt.pub

# SSL证书
ssl/
certs/

# ========== 临时文件 ==========

# 临时目录
temp/
tmp/
.tmp/

# 备份文件
*.bak
*.backup
*.old
*.orig

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# ========== 文档和媒体 ==========

# 生成的文档
docs/build/
docs/dist/

# 上传的文件
uploads/
upload/
static/uploads/

# 媒体文件缓存
.media/

# ========== 测试相关 ==========

# 测试覆盖率
coverage/
.coverage
htmlcov/

# 测试输出
test-results/
test-reports/

# ========== 部署相关 ==========

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
*.kubeconfig

# 部署脚本生成的文件
deploy/generated/

# ========== 项目特定 ==========

# 税务系统特定的忽略文件
tax-data/
invoice-files/
declaration-files/
export-files/

# 开发时的测试数据
test-data/
mock-data/

# 性能分析文件
*.prof
*.trace

# 监控和指标
metrics/
monitoring/

# ========== 其他 ==========

# 编辑器配置（保留）
# .editorconfig

# Git配置（保留）
# .gitattributes

# 项目文档（保留）
# README.md
# CHANGELOG.md

# 许可证（保留）
# LICENSE

# 包管理器锁文件（保留前端的，忽略不必要的）
# package-lock.json
# yarn.lock

# 忽略所有以点开头的隐藏文件，除了特定的
.*
!.gitignore
!.gitattributes
!.editorconfig
!.eslintrc*
!.prettierrc*
!.babelrc*
!.browserslistrc