{"name": "tax-frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "ant-design-vue": "^3.2.20", "axios": "^1.4.0", "core-js": "^3.31.0", "moment": "^2.29.4", "pinia": "^2.1.4", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/eslint-parser": "^7.27.1", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-standard": "^8.0.1", "eslint": "^8.43.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-vue": "^9.15.1", "npm-check-updates": "^18.0.1", "sass": "^1.89.0", "sass-loader": "^13.3.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "@vue/standard"], "parserOptions": {"ecmaVersion": 2020}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}