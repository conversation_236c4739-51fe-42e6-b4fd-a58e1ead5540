import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/antd.css'
import '@/assets/styles/common.scss'
import '@/assets/styles/macos-theme.scss'
import '@/assets/styles/antd-theme-override.scss'

// 开发环境初始化调试工具
if (process.env.NODE_ENV === 'development') {
  import('@/utils/api-debug').then(({ debugApi }) => {
    window.debugApi = debugApi
    console.log('🛠️ API调试工具已加载: window.debugApi')
  })

  import('@/utils/api-fix').then(({ apiFix }) => {
    window.apiFix = apiFix
    window.fixApi = apiFix.quickFix
    console.log('🔧 API修复工具已加载: window.fixApi()')
  })
}

const app = createApp(App)
const pinia = createPinia()

app.use(router)
app.use(pinia)
app.use(Antd)

app.mount('#app')
