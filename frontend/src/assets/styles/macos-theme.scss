/* macOS Aqua UI 主题样式 */

/* 全局字体设置 */
* {
  font-family: var(--font-family-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 全局过渡动画 */
* {
  transition: all var(--transition-normal);
}

/* 毛玻璃效果基础类 */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
  -webkit-backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
}

.glass-effect-dark {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
  -webkit-backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
}

/* macOS 风格按钮 */
.macos-button {
  border-radius: var(--radius-base);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  border: none;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: var(--shadow-light);
  }
  
  &.primary {
    background: var(--color-primary);
    color: white;
    
    &:hover {
      background: var(--color-primary-hover);
    }
    
    &:active {
      background: var(--color-primary-active);
    }
  }
  
  &.secondary {
    background: var(--color-bg-secondary);
    color: var(--color-text-primary);
    
    &:hover {
      background: var(--color-bg-tertiary);
    }
  }
  
  &.danger {
    background: var(--color-danger);
    color: white;
    
    &:hover {
      background: var(--color-danger-hover);
    }
  }
}

/* macOS 风格输入框 */
.macos-input {
  border: 1px solid var(--color-bg-quaternary);
  border-radius: var(--radius-base);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  background: var(--color-bg-primary);
  
  &:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
  }
  
  &:disabled {
    background: var(--color-bg-secondary);
    color: var(--color-text-tertiary);
    cursor: not-allowed;
  }
}

/* macOS 风格卡片 */
.macos-card {
  background: var(--color-bg-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-card);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
  
  &:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
  }
}

/* macOS 风格模态框 */
.macos-modal {
  .ant-modal-content {
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-heavy);
    border: none;
  }
  
  .ant-modal-header {
    border-radius: var(--radius-large) var(--radius-large) 0 0;
    border-bottom: 1px solid var(--color-bg-quaternary);
    padding: var(--spacing-lg);
  }
  
  .ant-modal-body {
    padding: var(--spacing-lg);
  }
  
  .ant-modal-footer {
    border-top: 1px solid var(--color-bg-quaternary);
    padding: var(--spacing-lg);
    border-radius: 0 0 var(--radius-large) var(--radius-large);
  }
}

/* macOS 风格表格 */
.macos-table {
  .ant-table {
    border-radius: var(--radius-large);
    overflow: hidden;
  }
  
  .ant-table-thead > tr > th {
    background: var(--color-bg-secondary);
    border-bottom: 1px solid var(--color-bg-quaternary);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
  }
  
  .ant-table-tbody > tr > td {
    border-bottom: 1px solid var(--color-bg-quaternary);
  }
  
  .ant-table-tbody > tr:hover > td {
    background: var(--color-bg-secondary);
  }
}

/* macOS 风格导航菜单 */
.macos-menu {
  background: transparent;
  border: none;
  
  .ant-menu-item {
    border-radius: var(--radius-base);
    margin: var(--spacing-xs) 0;
    transition: all var(--transition-fast);
    
    &:hover {
      background: rgba(0, 122, 255, 0.1);
      color: var(--color-primary);
    }
    
    &.ant-menu-item-selected {
      background: var(--color-primary);
      color: white;
      
      &::after {
        display: none;
      }
    }
  }
  
  .ant-menu-submenu-title {
    border-radius: var(--radius-base);
    margin: var(--spacing-xs) 0;
    
    &:hover {
      background: rgba(0, 122, 255, 0.1);
      color: var(--color-primary);
    }
  }
}

/* macOS 风格表单 */
.macos-form {
  .ant-form-item-label > label {
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
  }
  
  .ant-input,
  .ant-select-selector,
  .ant-picker {
    border-radius: var(--radius-base);
    border: 1px solid var(--color-bg-quaternary);
    
    &:focus,
    &.ant-select-focused .ant-select-selector,
    &.ant-picker-focused {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    }
  }
}

/* macOS 风格标签页 */
.macos-tabs {
  .ant-tabs-nav {
    background: var(--color-bg-secondary);
    border-radius: var(--radius-base);
    padding: var(--spacing-xs);
    margin-bottom: var(--spacing-lg);
  }
  
  .ant-tabs-tab {
    border-radius: var(--radius-base);
    border: none;
    margin: 0 var(--spacing-xs);
    
    &.ant-tabs-tab-active {
      background: var(--color-bg-primary);
      box-shadow: var(--shadow-light);
    }
  }
}

/* macOS 风格通知 */
.macos-notification {
  .ant-notification-notice {
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-heavy);
    backdrop-filter: var(--backdrop-blur);
    background: rgba(255, 255, 255, 0.9);
  }
}

/* macOS 风格抽屉 */
.macos-drawer {
  .ant-drawer-content {
    background: var(--color-bg-primary);
  }
  
  .ant-drawer-header {
    border-bottom: 1px solid var(--color-bg-quaternary);
    padding: var(--spacing-lg);
  }
  
  .ant-drawer-body {
    padding: var(--spacing-lg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .macos-card {
    padding: var(--spacing-md);
    border-radius: var(--radius-base);
  }
  
  .macos-modal .ant-modal-content {
    border-radius: var(--radius-base);
  }
}
