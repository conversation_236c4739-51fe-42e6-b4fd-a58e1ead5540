/* Ant Design Vue macOS 主题覆盖 */

/* 全局组件样式覆盖 */
.ant-btn {
  border-radius: var(--radius-base) !important;
  font-weight: var(--font-weight-medium) !important;
  transition: all var(--transition-fast) !important;
  border: none !important;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium) !important;
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &.ant-btn-primary {
    background: var(--color-primary) !important;
    
    &:hover {
      background: var(--color-primary-hover) !important;
    }
    
    &:active {
      background: var(--color-primary-active) !important;
    }
  }
  
  &.ant-btn-default {
    background: var(--color-bg-secondary) !important;
    color: var(--color-text-primary) !important;
    
    &:hover {
      background: var(--color-bg-tertiary) !important;
      color: var(--color-primary) !important;
    }
  }
  
  &.ant-btn-danger {
    background: var(--color-danger) !important;
    
    &:hover {
      background: var(--color-danger-hover) !important;
    }
  }
  
  &.ant-btn-text {
    &:hover {
      background: rgba(0, 122, 255, 0.08) !important;
      color: var(--color-primary) !important;
    }
  }
}

/* 输入框样式 */
.ant-input,
.ant-input-affix-wrapper {
  border-radius: var(--radius-base) !important;
  border: 1px solid var(--color-bg-quaternary) !important;
  transition: all var(--transition-fast) !important;
  
  &:focus,
  &.ant-input-affix-wrapper-focused {
    border-color: var(--color-primary) !important;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1) !important;
  }
  
  &:hover {
    border-color: var(--color-primary) !important;
  }
}

/* 选择器样式 */
.ant-select {
  .ant-select-selector {
    border-radius: var(--radius-base) !important;
    border: 1px solid var(--color-bg-quaternary) !important;
    transition: all var(--transition-fast) !important;
  }
  
  &.ant-select-focused .ant-select-selector {
    border-color: var(--color-primary) !important;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1) !important;
  }
  
  &:hover .ant-select-selector {
    border-color: var(--color-primary) !important;
  }
}

/* 下拉菜单样式 */
.ant-dropdown {
  .ant-dropdown-menu {
    border-radius: var(--radius-large) !important;
    box-shadow: var(--shadow-heavy) !important;
    border: none !important;
    overflow: hidden;
    
    .ant-dropdown-menu-item {
      transition: all var(--transition-fast) !important;
      
      &:hover {
        background: var(--color-bg-secondary) !important;
      }
    }
  }
}

/* 模态框样式 */
.ant-modal {
  .ant-modal-content {
    border-radius: var(--radius-large) !important;
    box-shadow: var(--shadow-heavy) !important;
    border: none !important;
  }
  
  .ant-modal-header {
    border-radius: var(--radius-large) var(--radius-large) 0 0 !important;
    border-bottom: 1px solid var(--color-bg-quaternary) !important;
    padding: var(--spacing-lg) !important;
  }
  
  .ant-modal-body {
    padding: var(--spacing-lg) !important;
  }
  
  .ant-modal-footer {
    border-top: 1px solid var(--color-bg-quaternary) !important;
    padding: var(--spacing-lg) !important;
    border-radius: 0 0 var(--radius-large) var(--radius-large) !important;
  }
}

/* 表格样式 */
.ant-table {
  border-radius: var(--radius-large) !important;
  overflow: hidden;
  
  .ant-table-thead > tr > th {
    background: var(--color-bg-secondary) !important;
    border-bottom: 1px solid var(--color-bg-quaternary) !important;
    font-weight: var(--font-weight-semibold) !important;
    color: var(--color-text-primary) !important;
  }
  
  .ant-table-tbody > tr > td {
    border-bottom: 1px solid var(--color-bg-quaternary) !important;
    transition: all var(--transition-fast) !important;
  }
  
  .ant-table-tbody > tr:hover > td {
    background: var(--color-bg-secondary) !important;
  }
}

/* 卡片样式 */
.ant-card {
  border-radius: var(--radius-large) !important;
  box-shadow: var(--shadow-card) !important;
  border: none !important;
  transition: all var(--transition-normal) !important;
  
  &:hover {
    box-shadow: var(--shadow-medium) !important;
    transform: translateY(-2px);
  }
  
  .ant-card-head {
    border-bottom: 1px solid var(--color-bg-quaternary) !important;
    border-radius: var(--radius-large) var(--radius-large) 0 0 !important;
  }
}

/* 标签页样式 */
.ant-tabs {
  .ant-tabs-nav {
    background: var(--color-bg-secondary) !important;
    border-radius: var(--radius-base) !important;
    padding: var(--spacing-xs) !important;
    margin-bottom: var(--spacing-lg) !important;
  }
  
  .ant-tabs-tab {
    border-radius: var(--radius-base) !important;
    border: none !important;
    margin: 0 var(--spacing-xs) !important;
    transition: all var(--transition-fast) !important;
    
    &.ant-tabs-tab-active {
      background: var(--color-bg-primary) !important;
      box-shadow: var(--shadow-light) !important;
    }
  }
}

/* 表单样式 */
.ant-form {
  .ant-form-item-label > label {
    font-weight: var(--font-weight-medium) !important;
    color: var(--color-text-primary) !important;
  }
}

/* 通知样式 */
.ant-notification {
  .ant-notification-notice {
    border-radius: var(--radius-large) !important;
    box-shadow: var(--shadow-heavy) !important;
    backdrop-filter: var(--backdrop-blur) !important;
    background: rgba(255, 255, 255, 0.9) !important;
  }
}

/* 抽屉样式 */
.ant-drawer {
  .ant-drawer-content {
    background: var(--color-bg-primary) !important;
  }
  
  .ant-drawer-header {
    border-bottom: 1px solid var(--color-bg-quaternary) !important;
    padding: var(--spacing-lg) !important;
  }
  
  .ant-drawer-body {
    padding: var(--spacing-lg) !important;
  }
}

/* 分页器样式 */
.ant-pagination {
  .ant-pagination-item {
    border-radius: var(--radius-base) !important;
    border: 1px solid var(--color-bg-quaternary) !important;
    transition: all var(--transition-fast) !important;
    
    &:hover {
      border-color: var(--color-primary) !important;
    }
    
    &.ant-pagination-item-active {
      background: var(--color-primary) !important;
      border-color: var(--color-primary) !important;
    }
  }
  
  .ant-pagination-prev,
  .ant-pagination-next {
    border-radius: var(--radius-base) !important;
    border: 1px solid var(--color-bg-quaternary) !important;
    transition: all var(--transition-fast) !important;
    
    &:hover {
      border-color: var(--color-primary) !important;
      color: var(--color-primary) !important;
    }
  }
}

/* 开关样式 */
.ant-switch {
  border-radius: var(--radius-large) !important;
  
  &.ant-switch-checked {
    background: var(--color-primary) !important;
  }
}

/* 滑块样式 */
.ant-slider {
  .ant-slider-rail {
    background: var(--color-bg-quaternary) !important;
    border-radius: var(--radius-base) !important;
  }
  
  .ant-slider-track {
    background: var(--color-primary) !important;
    border-radius: var(--radius-base) !important;
  }
  
  .ant-slider-handle {
    border: 2px solid var(--color-primary) !important;
    box-shadow: var(--shadow-light) !important;
  }
}

/* 进度条样式 */
.ant-progress {
  .ant-progress-bg {
    border-radius: var(--radius-base) !important;
  }
}

/* 标签样式 */
.ant-tag {
  border-radius: var(--radius-base) !important;
  border: none !important;
  font-weight: var(--font-weight-medium) !important;
}

/* 徽章样式 */
.ant-badge {
  .ant-badge-count {
    border-radius: var(--radius-large) !important;
    box-shadow: var(--shadow-light) !important;
  }
}
