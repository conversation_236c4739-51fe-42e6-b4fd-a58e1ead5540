/**
 * 表格处理的Composable函数
 * 基于VUE_STANDARDS.md规范，提供统一的表格处理逻辑
 */

import { ref, reactive, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { formatErrorMessage } from '@/utils/api-helper'

/**
 * 标准化的表格处理Hook
 * @param {Object} options - 配置选项
 * @param {Array} options.columns - 表格列配置
 * @param {Function} options.fetchData - 数据获取函数
 * @param {Object} options.defaultParams - 默认查询参数
 * @param {number} options.pageSize - 每页大小
 * @param {boolean} options.showSelection - 是否显示选择列
 * @param {string} options.rowKey - 行键字段名
 * @returns {Object} - 返回表格相关状态和方法
 */
export function useTable (options = {}) {
  const {
    columns = [],
    fetchData = null,
    defaultParams = {},
    pageSize = 10,
    showSelection = false,
    rowKey = 'id'
  } = options

  // 表格状态
  const loading = ref(false)
  const dataSource = ref([])
  const selectedRowKeys = ref([])
  const selectedRows = ref([])

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
    pageSizeOptions: ['10', '20', '50', '100']
  })

  // 查询参数
  const params = reactive({
    page: 1,
    pageSize,
    ...defaultParams
  })

  // 排序和筛选状态
  const sorter = reactive({})
  const filters = reactive({})

  // 计算属性
  const hasData = computed(() => dataSource.value.length > 0)
  const isEmpty = computed(() => !loading.value && !hasData.value)
  const hasSelection = computed(() => selectedRowKeys.value.length > 0)
  const isAllSelected = computed(() => {
    return hasData.value && selectedRowKeys.value.length === dataSource.value.length
  })
  const isIndeterminate = computed(() => {
    return hasSelection.value && !isAllSelected.value
  })

  // 表格列配置（包含选择列）
  const tableColumns = computed(() => {
    const cols = [...columns]
    if (showSelection) {
      cols.unshift({
        title: '选择',
        width: 60,
        customRender: ({ record }) => (
          <a-checkbox
            checked={selectedRowKeys.value.includes(record[rowKey])}
            onChange={(e) => handleRowSelect(record, e.target.checked)}
          />
        )
      })
    }
    return cols
  })

  /**
   * 获取表格数据
   * @param {boolean} resetPage - 是否重置页码
   */
  const loadData = async (resetPage = false) => {
    if (!fetchData || typeof fetchData !== 'function') {
      console.warn('fetchData function is required')
      return
    }

    try {
      loading.value = true

      if (resetPage) {
        params.page = 1
        pagination.current = 1
      }

      const requestParams = {
        ...params,
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...sorter,
        ...filters
      }

      const response = await fetchData(requestParams)
      
      // 假设响应已经通过标准化处理
      if (response && response.items) {
        dataSource.value = response.items
        pagination.total = response.total || 0
        pagination.current = response.page || pagination.current
      } else if (Array.isArray(response)) {
        // 兼容直接返回数组的情况
        dataSource.value = response
        pagination.total = response.length
      }

      // 清空选择状态
      clearSelection()
    } catch (error) {
      console.error('获取表格数据失败:', error)
      const errorMessage = formatErrorMessage(error)
      message.error(errorMessage)
    } finally {
      loading.value = false
    }
  }

  /**
   * 处理表格变化（分页、排序、筛选）
   * @param {Object} pag - 分页信息
   * @param {Object} fil - 筛选信息
   * @param {Object} sort - 排序信息
   */
  const handleTableChange = (pag, fil, sort) => {
    // 更新分页
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize

    // 更新排序
    if (sort && sort.field) {
      sorter.sortField = sort.field
      sorter.sortOrder = sort.order
    } else {
      delete sorter.sortField
      delete sorter.sortOrder
    }

    // 更新筛选
    Object.keys(filters).forEach(key => delete filters[key])
    Object.keys(fil).forEach(key => {
      if (fil[key] && fil[key].length > 0) {
        filters[key] = fil[key]
      }
    })

    // 重新加载数据
    loadData()
  }

  /**
   * 搜索
   * @param {Object} searchParams - 搜索参数
   */
  const search = (searchParams = {}) => {
    Object.assign(params, searchParams)
    loadData(true)
  }

  /**
   * 重置搜索
   */
  const reset = () => {
    // 重置参数
    Object.keys(params).forEach(key => {
      if (key !== 'page' && key !== 'pageSize') {
        delete params[key]
      }
    })
    Object.assign(params, defaultParams)

    // 重置排序和筛选
    Object.keys(sorter).forEach(key => delete sorter[key])
    Object.keys(filters).forEach(key => delete filters[key])

    // 重新加载数据
    loadData(true)
  }

  /**
   * 刷新数据
   */
  const refresh = () => {
    loadData()
  }

  /**
   * 处理行选择
   * @param {Object} record - 行数据
   * @param {boolean} selected - 是否选中
   */
  const handleRowSelect = (record, selected) => {
    const key = record[rowKey]
    if (selected) {
      if (!selectedRowKeys.value.includes(key)) {
        selectedRowKeys.value.push(key)
        selectedRows.value.push(record)
      }
    } else {
      const index = selectedRowKeys.value.indexOf(key)
      if (index > -1) {
        selectedRowKeys.value.splice(index, 1)
        selectedRows.value.splice(index, 1)
      }
    }
  }

  /**
   * 处理全选
   * @param {boolean} selected - 是否全选
   */
  const handleSelectAll = (selected) => {
    if (selected) {
      selectedRowKeys.value = dataSource.value.map(record => record[rowKey])
      selectedRows.value = [...dataSource.value]
    } else {
      clearSelection()
    }
  }

  /**
   * 清空选择
   */
  const clearSelection = () => {
    selectedRowKeys.value = []
    selectedRows.value = []
  }

  /**
   * 批量操作确认
   * @param {string} title - 确认标题
   * @param {string} content - 确认内容
   * @param {Function} onOk - 确认回调
   * @param {Object} options - 其他选项
   */
  const batchConfirm = (title, content, onOk, options = {}) => {
    if (!hasSelection.value) {
      message.warning('请先选择要操作的数据')
      return
    }

    Modal.confirm({
      title,
      content: content || `确定要对选中的 ${selectedRowKeys.value.length} 条数据执行此操作吗？`,
      okType: 'danger',
      ...options,
      onOk: async () => {
        try {
          if (onOk && typeof onOk === 'function') {
            await onOk(selectedRowKeys.value, selectedRows.value)
            clearSelection()
            refresh()
          }
        } catch (error) {
          console.error('批量操作失败:', error)
          const errorMessage = formatErrorMessage(error)
          message.error(errorMessage)
        }
      }
    })
  }

  /**
   * 单行操作确认
   * @param {Object} record - 行数据
   * @param {string} title - 确认标题
   * @param {string} content - 确认内容
   * @param {Function} onOk - 确认回调
   * @param {Object} options - 其他选项
   */
  const rowConfirm = (record, title, content, onOk, options = {}) => {
    Modal.confirm({
      title,
      content,
      okType: 'danger',
      ...options,
      onOk: async () => {
        try {
          if (onOk && typeof onOk === 'function') {
            await onOk(record)
            refresh()
          }
        } catch (error) {
          console.error('操作失败:', error)
          const errorMessage = formatErrorMessage(error)
          message.error(errorMessage)
        }
      }
    })
  }

  /**
   * 设置表格数据
   * @param {Array} data - 数据数组
   * @param {number} total - 总数
   */
  const setData = (data, total) => {
    dataSource.value = data || []
    if (total !== undefined) {
      pagination.total = total
    }
  }

  /**
   * 获取选中的数据
   * @returns {Object} - 选中的键和行数据
   */
  const getSelection = () => {
    return {
      keys: [...selectedRowKeys.value],
      rows: [...selectedRows.value]
    }
  }

  return {
    // 状态
    loading,
    dataSource,
    pagination,
    params,
    selectedRowKeys,
    selectedRows,
    sorter,
    filters,

    // 计算属性
    hasData,
    isEmpty,
    hasSelection,
    isAllSelected,
    isIndeterminate,
    tableColumns,

    // 方法
    loadData,
    handleTableChange,
    search,
    reset,
    refresh,
    handleRowSelect,
    handleSelectAll,
    clearSelection,
    batchConfirm,
    rowConfirm,
    setData,
    getSelection
  }
}

/**
 * 简化的表格Hook，用于只读表格
 * @param {Object} options - 配置选项
 * @returns {Object} - 返回简化的表格状态和方法
 */
export function useSimpleTable (options = {}) {
  const {
    columns = [],
    data = [],
    loading: initialLoading = false
  } = options

  const loading = ref(initialLoading)
  const dataSource = ref([...data])

  const hasData = computed(() => dataSource.value.length > 0)
  const isEmpty = computed(() => !loading.value && !hasData.value)

  const setData = (newData) => {
    dataSource.value = newData || []
  }

  const setLoading = (isLoading) => {
    loading.value = isLoading
  }

  return {
    loading,
    dataSource,
    hasData,
    isEmpty,
    columns,
    setData,
    setLoading
  }
}
