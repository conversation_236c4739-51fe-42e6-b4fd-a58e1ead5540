/**
 * 表单处理的Composable函数
 * 基于VUE_STANDARDS.md规范，提供统一的表单处理逻辑
 */

import { ref, reactive, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { formatErrorMessage } from '@/utils/api-helper'

/**
 * 标准化的表单处理Hook
 * @param {Object} options - 配置选项
 * @param {Object} options.initialValues - 初始值
 * @param {Object} options.rules - 验证规则
 * @param {Function} options.onSubmit - 提交处理函数
 * @param {Function} options.onReset - 重置处理函数
 * @param {boolean} options.resetAfterSubmit - 提交后是否重置表单
 * @returns {Object} - 返回表单相关状态和方法
 */
export function useForm (options = {}) {
  const {
    initialValues = {},
    rules = {},
    onSubmit = null,
    onReset = null,
    resetAfterSubmit = false
  } = options

  // 表单状态
  const loading = ref(false)
  const submitting = ref(false)
  const error = ref(null)
  const formRef = ref(null)

  // 表单数据
  const formData = reactive({ ...initialValues })

  // 验证状态
  const errors = reactive({})
  const touched = reactive({})

  // 计算属性
  const isValid = computed(() => {
    return Object.keys(errors).length === 0
  })

  const isDirty = computed(() => {
    return Object.keys(touched).length > 0
  })

  const hasErrors = computed(() => {
    return Object.keys(errors).length > 0
  })

  /**
   * 验证单个字段
   * @param {string} field - 字段名
   * @param {*} value - 字段值
   * @returns {Promise<boolean>} - 验证是否通过
   */
  const validateField = async (field, value) => {
    const fieldRules = rules[field]
    if (!fieldRules || !Array.isArray(fieldRules)) {
      delete errors[field]
      return true
    }

    for (const rule of fieldRules) {
      try {
        // 必填验证
        if (rule.required && (value === undefined || value === null || value === '')) {
          errors[field] = rule.message || `${field}是必填项`
          return false
        }

        // 类型验证
        if (rule.type && value !== undefined && value !== null && value !== '') {
          if (rule.type === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            errors[field] = rule.message || '请输入有效的邮箱地址'
            return false
          }
          if (rule.type === 'number' && isNaN(Number(value))) {
            errors[field] = rule.message || '请输入有效的数字'
            return false
          }
        }

        // 长度验证
        if (rule.min !== undefined && value && value.length < rule.min) {
          errors[field] = rule.message || `最少需要${rule.min}个字符`
          return false
        }
        if (rule.max !== undefined && value && value.length > rule.max) {
          errors[field] = rule.message || `最多允许${rule.max}个字符`
          return false
        }

        // 正则验证
        if (rule.pattern && value && !rule.pattern.test(value)) {
          errors[field] = rule.message || '格式不正确'
          return false
        }

        // 自定义验证器
        if (rule.validator && typeof rule.validator === 'function') {
          await rule.validator(rule, value)
        }
      } catch (err) {
        errors[field] = err.message || rule.message || '验证失败'
        return false
      }
    }

    delete errors[field]
    return true
  }

  /**
   * 验证整个表单
   * @returns {Promise<boolean>} - 验证是否通过
   */
  const validateForm = async () => {
    const validationPromises = Object.keys(rules).map(field => 
      validateField(field, formData[field])
    )

    const results = await Promise.all(validationPromises)
    return results.every(result => result === true)
  }

  /**
   * 处理字段变化
   * @param {string} field - 字段名
   * @param {*} value - 新值
   */
  const handleFieldChange = async (field, value) => {
    formData[field] = value
    touched[field] = true

    // 如果字段之前有错误，立即重新验证
    if (errors[field]) {
      await validateField(field, value)
    }
  }

  /**
   * 处理字段失焦
   * @param {string} field - 字段名
   */
  const handleFieldBlur = async (field) => {
    touched[field] = true
    await validateField(field, formData[field])
  }

  /**
   * 提交表单
   * @param {Event} event - 表单提交事件
   */
  const handleSubmit = async (event) => {
    if (event) {
      event.preventDefault()
    }

    try {
      submitting.value = true
      error.value = null

      // 验证表单
      const isFormValid = await validateForm()
      if (!isFormValid) {
        message.error('请检查表单输入')
        return false
      }

      // 调用提交处理函数
      if (onSubmit && typeof onSubmit === 'function') {
        const result = await onSubmit(formData)
        
        if (resetAfterSubmit) {
          resetForm()
        }
        
        return result
      }

      return true
    } catch (err) {
      const errorMessage = formatErrorMessage(err)
      error.value = errorMessage
      message.error(errorMessage)
      return false
    } finally {
      submitting.value = false
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    // 重置数据
    Object.keys(formData).forEach(key => {
      if (initialValues.hasOwnProperty(key)) {
        formData[key] = initialValues[key]
      } else {
        delete formData[key]
      }
    })

    // 重置状态
    Object.keys(errors).forEach(key => delete errors[key])
    Object.keys(touched).forEach(key => delete touched[key])
    error.value = null

    // 调用重置处理函数
    if (onReset && typeof onReset === 'function') {
      onReset()
    }

    // 重置表单验证状态
    nextTick(() => {
      if (formRef.value && formRef.value.resetFields) {
        formRef.value.resetFields()
      }
    })
  }

  /**
   * 设置表单数据
   * @param {Object} data - 新的表单数据
   */
  const setFormData = (data) => {
    Object.assign(formData, data)
  }

  /**
   * 设置字段错误
   * @param {string} field - 字段名
   * @param {string} errorMessage - 错误消息
   */
  const setFieldError = (field, errorMessage) => {
    errors[field] = errorMessage
  }

  /**
   * 清除字段错误
   * @param {string} field - 字段名
   */
  const clearFieldError = (field) => {
    delete errors[field]
  }

  /**
   * 清除所有错误
   */
  const clearErrors = () => {
    Object.keys(errors).forEach(key => delete errors[key])
    error.value = null
  }

  /**
   * 获取字段错误信息
   * @param {string} field - 字段名
   * @returns {string|undefined} - 错误信息
   */
  const getFieldError = (field) => {
    return errors[field]
  }

  /**
   * 检查字段是否有错误
   * @param {string} field - 字段名
   * @returns {boolean} - 是否有错误
   */
  const hasFieldError = (field) => {
    return !!errors[field]
  }

  /**
   * 检查字段是否被触摸过
   * @param {string} field - 字段名
   * @returns {boolean} - 是否被触摸过
   */
  const isFieldTouched = (field) => {
    return !!touched[field]
  }

  return {
    // 状态
    loading,
    submitting,
    error,
    formRef,
    formData,
    errors,
    touched,
    
    // 计算属性
    isValid,
    isDirty,
    hasErrors,
    
    // 方法
    validateField,
    validateForm,
    handleFieldChange,
    handleFieldBlur,
    handleSubmit,
    resetForm,
    setFormData,
    setFieldError,
    clearFieldError,
    clearErrors,
    getFieldError,
    hasFieldError,
    isFieldTouched
  }
}

/**
 * 搜索表单的Composable函数
 * @param {Object} options - 配置选项
 * @param {Object} options.initialValues - 初始搜索值
 * @param {Function} options.onSearch - 搜索处理函数
 * @param {Function} options.onReset - 重置处理函数
 * @returns {Object} - 返回搜索表单相关状态和方法
 */
export function useSearchForm (options = {}) {
  const {
    initialValues = {},
    onSearch = null,
    onReset = null
  } = options

  const searchForm = reactive({ ...initialValues })
  const searching = ref(false)

  /**
   * 执行搜索
   */
  const handleSearch = async () => {
    if (!onSearch || typeof onSearch !== 'function') {
      return
    }

    try {
      searching.value = true
      await onSearch(searchForm)
    } catch (error) {
      console.error('搜索失败:', error)
      message.error('搜索失败，请稍后重试')
    } finally {
      searching.value = false
    }
  }

  /**
   * 重置搜索表单
   */
  const handleReset = async () => {
    // 重置表单数据
    Object.keys(searchForm).forEach(key => {
      if (initialValues.hasOwnProperty(key)) {
        searchForm[key] = initialValues[key]
      } else {
        searchForm[key] = ''
      }
    })

    // 调用重置处理函数
    if (onReset && typeof onReset === 'function') {
      await onReset()
    }
  }

  /**
   * 设置搜索参数
   * @param {Object} params - 搜索参数
   */
  const setSearchParams = (params) => {
    Object.assign(searchForm, params)
  }

  return {
    searchForm,
    searching,
    handleSearch,
    handleReset,
    setSearchParams
  }
}
