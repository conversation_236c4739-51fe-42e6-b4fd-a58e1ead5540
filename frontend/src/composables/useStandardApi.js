/**
 * 标准化API调用的Composable函数
 * 基于API_DATA_STANDARDS.md和VUE_STANDARDS.md规范
 */

import { ref, reactive, computed, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  handleApiResponse,
  handlePaginatedResponse,
  handleListResponse,
  handleEntityResponse,
  handleOperationResponse,
  formatErrorMessage
} from '@/utils/api-helper'

/**
 * 标准化的API调用Hook
 * @param {Function} apiFunction - API函数
 * @param {Object} options - 配置选项
 * @returns {Object} - 返回状态和方法
 */
export function useStandardApi (apiFunction, options = {}) {
  const {
    immediate = false,
    showError = true,
    showSuccess = false,
    successMessage = '操作成功',
    transform = null,
    responseHandler = handleApiResponse
  } = options

  const loading = ref(false)
  const error = ref(null)
  const data = ref(null)

  // 组件卸载标志
  let isUnmounted = false

  onUnmounted(() => {
    isUnmounted = true
  })

  const execute = async (...args) => {
    try {
      if (isUnmounted) return null

      loading.value = true
      error.value = null

      const response = await apiFunction(...args)

      // 检查组件是否已卸载
      if (isUnmounted) return null

      const responseData = responseHandler(response)
      data.value = transform ? transform(responseData) : responseData

      if (showSuccess && !isUnmounted) {
        message.success(successMessage)
      }

      return data.value
    } catch (err) {
      // 检查组件是否已卸载
      if (isUnmounted) return null

      const errorMessage = formatErrorMessage(err)
      error.value = errorMessage

      if (showError) {
        message.error(errorMessage)
      }

      throw err
    } finally {
      if (!isUnmounted) {
        loading.value = false
      }
    }
  }

  // 如果设置了immediate，立即执行
  if (immediate) {
    execute().catch(() => {
      // 静默处理immediate执行的错误
    })
  }

  return {
    loading,
    error,
    data,
    execute
  }
}

/**
 * 标准化的分页数据Hook
 * @param {Function} apiFunction - 分页API函数
 * @param {Object} options - 配置选项
 * @returns {Object} - 返回分页相关状态和方法
 */
export function useStandardPagination (apiFunction, options = {}) {
  const {
    defaultParams = {},
    pageSize = 10,
    showError = true
  } = options

  const loading = ref(false)
  const error = ref(null)
  const dataSource = ref([])

  // 组件卸载标志
  let isUnmounted = false

  onUnmounted(() => {
    isUnmounted = true
  })

  const pagination = reactive({
    current: 1,
    pageSize,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
  })

  const params = reactive({
    page: 1,
    pageSize,
    ...defaultParams
  })

  // 计算属性
  const hasData = computed(() => dataSource.value.length > 0)
  const isEmpty = computed(() => !loading.value && !hasData.value)

  const fetchData = async (resetPage = false) => {
    try {
      if (isUnmounted) return null

      loading.value = true
      error.value = null

      if (resetPage) {
        params.page = 1
        pagination.current = 1
      }

      const requestParams = {
        ...params,
        page: pagination.current,
        pageSize: pagination.pageSize
      }

      const response = await apiFunction(requestParams)

      // 检查组件是否已卸载
      if (isUnmounted) return null

      const paginatedData = handlePaginatedResponse(response)

      dataSource.value = paginatedData.items
      pagination.total = paginatedData.total
      pagination.current = paginatedData.page

      return paginatedData
    } catch (err) {
      // 检查组件是否已卸载
      if (isUnmounted) return null

      const errorMessage = formatErrorMessage(err)
      error.value = errorMessage

      if (showError) {
        message.error(errorMessage)
      }

      throw err
    } finally {
      if (!isUnmounted) {
        loading.value = false
      }
    }
  }

  const handleTableChange = (pag, filters, sorter) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize

    // 处理排序
    if (sorter && sorter.field) {
      params.sortField = sorter.field
      params.sortOrder = sorter.order
    } else {
      delete params.sortField
      delete params.sortOrder
    }

    // 处理筛选
    Object.keys(filters).forEach(key => {
      if (filters[key] && filters[key].length > 0) {
        params[key] = filters[key]
      } else {
        delete params[key]
      }
    })

    fetchData()
  }

  const search = (searchParams = {}) => {
    Object.assign(params, searchParams)
    fetchData(true)
  }

  const reset = () => {
    Object.keys(params).forEach(key => {
      if (key !== 'page' && key !== 'pageSize') {
        delete params[key]
      }
    })
    Object.assign(params, defaultParams)
    fetchData(true)
  }

  const refresh = () => {
    fetchData()
  }

  return {
    loading,
    error,
    dataSource,
    pagination,
    params,
    hasData,
    isEmpty,
    fetchData,
    handleTableChange,
    search,
    reset,
    refresh
  }
}

/**
 * 标准化的列表数据Hook
 * @param {Function} apiFunction - 列表API函数
 * @param {Object} options - 配置选项
 * @returns {Object} - 返回列表相关状态和方法
 */
export function useStandardList (apiFunction, options = {}) {
  const {
    immediate = false,
    showError = true,
    transform = null
  } = options

  const loading = ref(false)
  const error = ref(null)
  const list = ref([])

  const fetchList = async (...args) => {
    try {
      loading.value = true
      error.value = null

      const response = await apiFunction(...args)
      const listData = handleListResponse(response)
      list.value = transform ? transform(listData) : listData

      return list.value
    } catch (err) {
      const errorMessage = formatErrorMessage(err)
      error.value = errorMessage

      if (showError) {
        message.error(errorMessage)
      }

      throw err
    } finally {
      loading.value = false
    }
  }

  const refresh = () => fetchList()

  // 如果设置了immediate，立即执行
  if (immediate) {
    fetchList()
  }

  return {
    loading,
    error,
    list,
    fetchList,
    refresh
  }
}

/**
 * 标准化的实体数据Hook
 * @param {Function} apiFunction - 实体API函数
 * @param {Object} options - 配置选项
 * @returns {Object} - 返回实体相关状态和方法
 */
export function useStandardEntity (apiFunction, options = {}) {
  const {
    immediate = false,
    showError = true,
    transform = null
  } = options

  const loading = ref(false)
  const error = ref(null)
  const entity = ref(null)

  const fetchEntity = async (...args) => {
    try {
      loading.value = true
      error.value = null

      const response = await apiFunction(...args)
      const entityData = handleEntityResponse(response)
      entity.value = transform ? transform(entityData) : entityData

      return entity.value
    } catch (err) {
      const errorMessage = formatErrorMessage(err)
      error.value = errorMessage

      if (showError) {
        message.error(errorMessage)
      }

      throw err
    } finally {
      loading.value = false
    }
  }

  const refresh = () => fetchEntity()

  // 如果设置了immediate，立即执行
  if (immediate) {
    fetchEntity()
  }

  return {
    loading,
    error,
    entity,
    fetchEntity,
    refresh
  }
}

/**
 * 标准化的操作Hook
 * @param {Function} apiFunction - 操作API函数
 * @param {Object} options - 配置选项
 * @returns {Object} - 返回操作相关状态和方法
 */
export function useStandardOperation (apiFunction, options = {}) {
  const {
    showError = true,
    showSuccess = true,
    successMessage = '操作成功',
    onSuccess = null,
    onError = null
  } = options

  const loading = ref(false)
  const error = ref(null)

  const execute = async (...args) => {
    try {
      loading.value = true
      error.value = null

      const response = await apiFunction(...args)
      const success = handleOperationResponse(response, successMessage, showSuccess)

      if (success && onSuccess) {
        await onSuccess(response)
      }

      return success
    } catch (err) {
      const errorMessage = formatErrorMessage(err)
      error.value = errorMessage

      if (showError) {
        message.error(errorMessage)
      }

      if (onError) {
        await onError(err)
      }

      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    execute
  }
}
