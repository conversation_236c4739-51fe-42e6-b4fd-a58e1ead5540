/**
 * API相关的JSDoc类型定义
 * 基于API_DATA_STANDARDS.md规范
 * 使用JSDoc注释提供类型提示
 */

/**
 * @typedef {Object} ApiResponse
 * @property {number} code - HTTP状态码
 * @property {string} message - 响应消息
 * @property {*} data - 响应数据
 * @property {number} timestamp - Unix时间戳
 * @property {string} requestId - 请求追踪ID
 */

/**
 * @typedef {Object} PaginatedData
 * @property {Array} items - 数据列表
 * @property {number} total - 总记录数
 * @property {number} page - 当前页码
 * @property {number} pageSize - 每页大小
 * @property {number} pages - 总页数
 */

/**
 * @typedef {ApiResponse} PaginatedResponse
 * @property {PaginatedData} data - 分页数据
 */

/**
 * @typedef {Object} Enterprise
 * @property {string} id - 企业ID
 * @property {string} name - 企业名称
 * @property {string} unified_social_credit_code - 统一社会信用代码
 * @property {string} legal_representative - 法定代表人
 * @property {number} registered_capital - 注册资本
 * @property {string} registered_date - 注册日期
 * @property {string} industry_code - 行业代码
 * @property {'active'|'inactive'|'suspended'|'cancelled'|'pending'} status - 状态
 * @property {number} [employees] - 员工数量
 * @property {number} [tax_amount] - 税额
 * @property {string} [address] - 地址
 * @property {string} [phone] - 电话
 * @property {string} [email] - 邮箱
 * @property {string} created_at - 创建时间
 * @property {string} updated_at - 更新时间
 */

/**
 * @typedef {Object} EnterpriseStats
 * @property {number} total - 总数
 * @property {number} active - 活跃数
 * @property {number} inactive - 非活跃数
 * @property {number} pending - 待审核数
 * @property {number} thisMonth - 本月新增数
 */

/**
 * @typedef {Object} User
 * @property {string} id - 用户ID
 * @property {string} username - 用户名
 * @property {string} email - 邮箱
 * @property {string} name - 姓名
 * @property {string} role - 角色
 * @property {string} [avatar] - 头像
 * @property {string} [phone] - 电话
 * @property {'active'|'inactive'} status - 状态
 * @property {string} [last_login_at] - 最后登录时间
 * @property {string} created_at - 创建时间
 * @property {string} updated_at - 更新时间
 */

/**
 * @typedef {Object} TaxType
 * @property {string} id - 税种ID
 * @property {string} name - 税种名称
 * @property {string} code - 税种代码
 * @property {number} rate - 税率
 * @property {string} [description] - 描述
 * @property {'active'|'inactive'} status - 状态
 * @property {string} created_at - 创建时间
 * @property {string} updated_at - 更新时间
 */

/**
 * @typedef {Object} Declaration
 * @property {string} id - 申报ID
 * @property {string} enterprise_id - 企业ID
 * @property {string} tax_type_id - 税种ID
 * @property {string} period - 申报期间
 * @property {number} amount - 金额
 * @property {number} tax_amount - 税额
 * @property {'draft'|'submitted'|'approved'|'rejected'} status - 状态
 * @property {string} [submitted_at] - 提交时间
 * @property {string} [approved_at] - 审批时间
 * @property {string} created_at - 创建时间
 * @property {string} updated_at - 更新时间
 */

/**
 * @typedef {Object} Invoice
 * @property {string} id - 发票ID
 * @property {string} enterprise_id - 企业ID
 * @property {string} invoice_number - 发票号码
 * @property {string} invoice_date - 发票日期
 * @property {number} amount - 金额
 * @property {number} tax_amount - 税额
 * @property {'input'|'output'} type - 类型
 * @property {'normal'|'cancelled'|'invalid'} status - 状态
 * @property {string} created_at - 创建时间
 * @property {string} updated_at - 更新时间
 */

/**
 * @typedef {Object} PaginationParams
 * @property {number} [page] - 页码
 * @property {number} [pageSize] - 每页大小
 * @property {string} [sortField] - 排序字段
 * @property {'ascend'|'descend'} [sortOrder] - 排序方向
 */

/**
 * @typedef {PaginationParams} SearchParams
 * @property {string} [keyword] - 关键词
 * @property {string} [status] - 状态
 * @property {Array<string>} [dateRange] - 日期范围
 */

/**
 * @typedef {SearchParams} EnterpriseSearchParams
 * @property {string} [industry] - 行业
 */

/**
 * @typedef {SearchParams} UserSearchParams
 * @property {string} [role] - 角色
 */

/**
 * @typedef {Object} FormRule
 * @property {boolean} [required] - 是否必填
 * @property {string} [type] - 类型
 * @property {number} [min] - 最小值
 * @property {number} [max] - 最大值
 * @property {RegExp} [pattern] - 正则表达式
 * @property {string} message - 错误消息
 * @property {Function} [validator] - 自定义验证器
 */

/**
 * @typedef {Object.<string, FormRule[]>} FormRules
 */

/**
 * @typedef {Object} TableColumn
 * @property {string} title - 列标题
 * @property {string} [dataIndex] - 数据索引
 * @property {string} [key] - 列键
 * @property {number} [width] - 列宽
 * @property {'left'|'right'} [fixed] - 固定位置
 * @property {boolean} [sorter] - 是否可排序
 * @property {Array<{text: string, value: *}>} [filters] - 筛选选项
 * @property {Function} [customRender] - 自定义渲染函数
 */

/**
 * @typedef {Object} UseApiReturn
 * @property {import('vue').Ref<boolean>} loading - 加载状态
 * @property {import('vue').Ref<string|null>} error - 错误信息
 * @property {import('vue').Ref<*>} data - 数据
 * @property {Function} execute - 执行函数
 */

/**
 * @typedef {Object} UsePaginationReturn
 * @property {import('vue').Ref<boolean>} loading - 加载状态
 * @property {import('vue').Ref<string|null>} error - 错误信息
 * @property {import('vue').Ref<Array>} dataSource - 数据源
 * @property {Object} pagination - 分页配置
 * @property {Object} params - 请求参数
 * @property {import('vue').ComputedRef<boolean>} hasData - 是否有数据
 * @property {import('vue').ComputedRef<boolean>} isEmpty - 是否为空
 * @property {Function} fetchData - 获取数据
 * @property {Function} handleTableChange - 表格变化处理
 * @property {Function} search - 搜索
 * @property {Function} reset - 重置
 * @property {Function} refresh - 刷新
 */

/**
 * @typedef {Object} UseListReturn
 * @property {import('vue').Ref<boolean>} loading - 加载状态
 * @property {import('vue').Ref<string|null>} error - 错误信息
 * @property {import('vue').Ref<Array>} list - 列表数据
 * @property {Function} fetchList - 获取列表
 * @property {Function} refresh - 刷新
 */

/**
 * @typedef {Object} UseEntityReturn
 * @property {import('vue').Ref<boolean>} loading - 加载状态
 * @property {import('vue').Ref<string|null>} error - 错误信息
 * @property {import('vue').Ref<*>} entity - 实体数据
 * @property {Function} fetchEntity - 获取实体
 * @property {Function} refresh - 刷新
 */

/**
 * @typedef {Object} UseOperationReturn
 * @property {import('vue').Ref<boolean>} loading - 加载状态
 * @property {import('vue').Ref<string|null>} error - 错误信息
 * @property {Function} execute - 执行操作
 */

// 导出类型定义（用于IDE提示）
export {}
