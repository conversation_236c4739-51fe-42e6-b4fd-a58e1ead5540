import { createRouter, createWebHistory } from 'vue-router'

// 布局组件使用懒加载
const Layout = () => import('@/components/layout/Layout.vue')
const RouteView = () => import('@/components/layout/RouteView.vue')

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { title: '登录', hideInMenu: true }
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('@/views/auth/Register.vue'),
    meta: { title: '注册', hideInMenu: true }
  },
  {
    path: '/forgot-password',
    name: 'forgot-password',
    component: () => import('@/views/auth/ForgotPassword.vue'),
    meta: { title: '忘记密码', hideInMenu: true }
  },
  {
    path: '/reset-password',
    name: 'reset-password',
    component: () => import('@/views/auth/ResetPassword.vue'),
    meta: { title: '重置密码', hideInMenu: true }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      // 仪表盘
      {
        path: 'dashboard',
        name: 'dashboard',
        component: () => import('@/views/dashboard/Dashboard.vue'),
        meta: { title: '仪表盘', icon: 'dashboard' }
      },

      // 个人信息
      {
        path: 'profile',
        name: 'user-profile',
        component: () => import('@/views/user/Profile.vue'),
        meta: { title: '个人信息', icon: 'user', hideInMenu: true }
      },

      // 用户相关路由重定向
      {
        path: 'user/profile',
        redirect: '/profile',
        meta: { hideInMenu: true }
      },
      {
        path: 'user/settings',
        redirect: '/settings',
        meta: { hideInMenu: true }
      },

      // 基础数据管理模块
      {
        path: '/enterprise',
        name: 'enterprise',
        component: RouteView,
        redirect: '/enterprise/list',
        meta: { title: '企业档案管理', icon: 'bank' },
        children: [
          {
            path: 'list',
            name: 'enterprise-list',
            component: () => import('@/views/enterprise/List.vue'),
            meta: { title: '企业列表' }
          },
          {
            path: 'detail/:id',
            name: 'enterprise-detail',
            component: () => import('@/views/enterprise/Detail.vue'),
            meta: { title: '企业详情', hideInMenu: true }
          },
          {
            path: 'create',
            name: 'enterprise-create',
            component: () => import('@/views/enterprise/Edit.vue'),
            meta: { title: '新增企业', hideInMenu: true }
          },
          {
            path: 'edit/:id',
            name: 'enterprise-edit',
            component: () => import('@/views/enterprise/Edit.vue'),
            meta: { title: '编辑企业', hideInMenu: true }
          },
          {
            path: 'documents/:id',
            name: 'enterprise-documents',
            component: () => import('@/views/enterprise/Documents.vue'),
            meta: { title: '企业资质文件', hideInMenu: true }
          }
        ]
      },
      {
        path: '/enterprises',
        redirect: '/enterprise/list',
        meta: { hideInMenu: true }
      },
      {
        path: '/enterprises/add',
        redirect: '/enterprise/create',
        meta: { hideInMenu: true }
      },
      {
        path: '/tax-type',
        name: 'tax-type',
        component: RouteView,
        meta: { title: '税种配置中心', icon: 'control' },
        children: [
          {
            path: 'list',
            name: 'tax-type-list',
            component: () => import('@/views/taxtype/List.vue'),
            meta: { title: '税种列表' }
          },
          {
            path: 'create',
            name: 'tax-type-create',
            component: () => import('@/views/taxtype/Create.vue'),
            meta: { title: '新增税种', hideInMenu: true }
          },
          {
            path: 'detail/:id',
            name: 'tax-type-detail',
            component: () => import('@/views/taxtype/Detail.vue'),
            meta: { title: '税种详情', hideInMenu: true }
          },
          {
            path: 'edit/:id',
            name: 'tax-type-edit',
            component: () => import('@/views/taxtype/Edit.vue'),
            meta: { title: '编辑税种', hideInMenu: true }
          },
          {
            path: 'rules/:id',
            name: 'tax-rules',
            component: () => import('@/views/taxtype/Rules.vue'),
            meta: { title: '计税规则配置', hideInMenu: true }
          }
        ]
      },

      // 税种管理 - 直接路由访问
      {
        path: '/tax-types',
        name: 'tax-types-direct',
        component: () => import('@/views/taxtype/List.vue'),
        meta: { title: '税种列表' }
      },

      // 税则管理模块
      {
        path: '/taxrule',
        name: 'TaxRule',
        component: RouteView,
        meta: { title: '税则管理', icon: 'file-text' },
        children: [
          {
            path: 'list',
            name: 'TaxRuleList',
            component: () => import('@/views/taxrule/List.vue'),
            meta: { title: '税则列表' }
          },
          {
            path: 'detail/:id',
            name: 'TaxRuleDetail',
            component: () => import('@/views/taxrule/Detail.vue'),
            meta: { title: '税则详情', hideInMenu: true }
          },
          {
            path: 'create',
            name: 'TaxRuleCreate',
            component: () => import('@/views/taxrule/Create.vue'),
            meta: { title: '新增税则', hideInMenu: true }
          },
          {
            path: 'edit/:id',
            name: 'TaxRuleEdit',
            component: () => import('@/views/taxrule/Edit.vue'),
            meta: { title: '编辑税则', hideInMenu: true }
          }
        ]
      },

      // 通知管理模块
      {
        path: '/notification',
        name: 'Notification',
        component: RouteView,
        meta: { title: '通知管理', icon: 'bell' },
        children: [
          {
            path: 'list',
            name: 'NotificationList',
            component: () => import('@/views/notification/List.vue'),
            meta: { title: '通知列表' }
          },
          {
            path: 'detail/:id',
            name: 'NotificationDetail',
            component: () => import('@/views/notification/Detail.vue'),
            meta: { title: '通知详情', hideInMenu: true }
          }
        ]
      },

      // 审计日志模块
      {
        path: '/auditlog',
        name: 'AuditLog',
        component: RouteView,
        meta: { title: '审计日志', icon: 'file-search' },
        children: [
          {
            path: 'list',
            name: 'AuditLogList',
            component: () => import('@/views/auditlog/List.vue'),
            meta: { title: '日志列表' }
          },
          {
            path: 'detail/:id',
            name: 'AuditLogDetail',
            component: () => import('@/views/auditlog/Detail.vue'),
            meta: { title: '日志详情', hideInMenu: true }
          },
          {
            path: 'stats',
            name: 'AuditLogStats',
            component: () => import('@/views/auditlog/Stats.vue'),
            meta: { title: '统计分析', hideInMenu: true }
          }
        ]
      },

      // 用户管理模块
      {
        path: '/user-management',
        name: 'user-management',
        component: RouteView,
        meta: {
          title: '用户管理',
          icon: 'team',
          permission: 'user.view'
        },
        children: [
          {
            path: 'list',
            name: 'user-management-list',
            component: () => import('@/views/user/UserList.vue'),
            meta: {
              title: '用户列表',
              permission: 'user.view'
            }
          },
          {
            path: 'roles',
            name: 'role-management',
            component: () => import('@/views/user/RoleManagement.vue'),
            meta: {
              title: '角色管理',
              permission: 'role.view'
            }
          },
          {
            path: 'permissions',
            name: 'permission-management',
            component: () => import('@/views/user/PermissionManagement.vue'),
            meta: {
              title: '权限管理',
              permission: 'permission.view'
            }
          }
        ]
      },

      // 用户管理 - 直接路由访问（保持向后兼容）
      {
        path: '/users',
        name: 'users-direct',
        component: () => import('@/views/user/UserList.vue'),
        meta: {
          title: '用户管理',
          permission: 'user.view'
        }
      },

      // 智能票据处理模块
      {
        path: '/invoice',
        name: 'invoice',
        component: RouteView,
        meta: { title: '票据管理', icon: 'file-text' },
        children: [
          {
            path: 'list',
            name: 'invoice-list',
            component: () => import('@/views/invoice/List.vue'),
            meta: { title: '票据列表' }
          },
          {
            path: 'detail/:id',
            name: 'invoice-detail',
            component: () => import('@/views/invoice/Detail.vue'),
            meta: { title: '票据详情', hideInMenu: true }
          },
          {
            path: 'upload',
            name: 'invoice-upload',
            component: () => import('@/views/invoice/Upload.vue'),
            meta: { title: '票据上传' }
          },
          {
            path: 'scan',
            name: 'invoice-scan',
            component: () => import('@/views/invoice/Scan.vue'),
            meta: { title: '票据扫描识别' }
          },
          {
            path: 'import',
            name: 'invoice-import',
            component: () => import('@/views/invoice/Import.vue'),
            meta: { title: '批量导入' }
          },
          {
            path: 'verify/:id',
            name: 'invoice-verify',
            component: () => import('@/views/invoice/Verify.vue'),
            meta: { title: '发票核验', hideInMenu: true }
          }
        ]
      },
      {
        path: '/invoices',
        redirect: '/invoice/list',
        meta: { hideInMenu: true }
      },
      {
        path: '/invoices/upload',
        redirect: '/invoice/upload',
        meta: { hideInMenu: true }
      },
      {
        path: '/invoices/scan',
        redirect: '/invoice/scan',
        meta: { hideInMenu: true }
      },

      // 智能申报核心模块
      {
        path: '/declaration',
        name: 'Declaration',
        component: RouteView,
        meta: { title: '智能申报', icon: 'audit' },
        children: [
          {
            path: 'list',
            name: 'DeclarationList',
            component: () => import('@/views/declaration/List.vue'),
            meta: { title: '申报列表' }
          },
          {
            path: 'detail/:id',
            name: 'DeclarationDetail',
            component: () => import('@/views/declaration/Detail.vue'),
            meta: { title: '申报详情', hideInMenu: true }
          },
          {
            path: 'create',
            name: 'DeclarationCreate',
            component: () => import('@/views/declaration/Edit.vue'),
            meta: { title: '新建申报' }
          },
          {
            path: 'edit/:id',
            name: 'DeclarationEdit',
            component: () => import('@/views/declaration/Edit.vue'),
            meta: { title: '编辑申报', hideInMenu: true }
          },
          {
            path: 'preview/:id',
            name: 'DeclarationPreview',
            component: () => import('@/views/declaration/Preview.vue'),
            meta: { title: '申报预览', hideInMenu: true }
          },
          {
            path: 'submit/:id',
            name: 'DeclarationSubmit',
            component: () => import('@/views/declaration/Submit.vue'),
            meta: { title: '提交申报', hideInMenu: true }
          },
          {
            path: 'history/:id',
            name: 'DeclarationHistory',
            component: () => import('@/views/declaration/History.vue'),
            meta: { title: '申报历史', hideInMenu: true }
          },
          {
            path: 'calendar',
            name: 'DeclarationCalendar',
            component: () => import('@/views/declaration/CalendarSimple.vue'),
            meta: { title: '申报日历' }
          }
        ]
      },
      {
        path: '/declarations',
        redirect: '/declaration/list',
        meta: { hideInMenu: true }
      },
      {
        path: '/declarations/create',
        redirect: '/declaration/create',
        meta: { hideInMenu: true }
      },
      {
        path: '/declarations/calendar',
        redirect: '/declaration/calendar',
        meta: { hideInMenu: true }
      },

      // 企业级功能模块
      {
        path: '/permissions',
        name: 'Permissions',
        component: RouteView,
        meta: { title: '权限管理', icon: 'safety' },
        children: [
          {
            path: 'users',
            name: 'UserList',
            component: () => import('@/views/user/UserList.vue'),
            meta: { title: '用户管理' }
          },
          {
            path: 'roles',
            name: 'RoleList',
            component: () => import('@/views/user/RoleList.vue'),
            meta: { title: '角色管理' }
          },
          {
            path: 'role/edit/:id',
            name: 'RoleEdit',
            component: () => import('@/views/user/RoleEdit.vue'),
            meta: { title: '编辑角色', hideInMenu: true }
          }
        ]
      },
      {
        path: '/groups',
        name: 'Groups',
        component: RouteView,
        meta: { title: '集团管理', icon: 'cluster' },
        children: [
          {
            path: 'list',
            name: 'GroupList',
            component: () => import('@/views/groups/List.vue'),
            meta: { title: '集团列表' }
          },
          {
            path: 'detail/:id',
            name: 'GroupDetail',
            component: () => import('@/views/groups/Detail.vue'),
            meta: { title: '集团详情', hideInMenu: true }
          },
          {
            path: 'members/:id',
            name: 'GroupMembers',
            component: () => import('@/views/groups/Members.vue'),
            meta: { title: '成员管理', hideInMenu: true }
          }
        ]
      },

      // 系统对接模块
      {
        path: '/integration',
        name: 'Integration',
        component: RouteView,
        redirect: '/integration/list',
        meta: { title: '系统集成', icon: 'link-outlined' },
        children: [
          {
            path: 'list',
            name: 'IntegrationList',
            component: () => import('@/views/integration/List.vue'),
            meta: { title: '集成总览' }
          },
          {
            path: 'gov',
            name: 'GovIntegration',
            component: () => import('@/views/integration/Gov.vue'),
            meta: { title: '政务平台集成' }
          },
          {
            path: 'financial',
            name: 'FinancialIntegration',
            component: () => import('@/views/integration/Financial.vue'),
            meta: { title: '财务系统集成' }
          },
          {
            path: 'bank',
            name: 'BankIntegration',
            component: () => import('@/views/integration/Bank.vue'),
            meta: { title: '银行系统集成' }
          },
          {
            path: 'invoice',
            name: 'InvoiceIntegration',
            component: () => import('@/views/integration/Invoice.vue'),
            meta: { title: '发票平台集成' }
          },
          {
            path: 'ecommerce',
            name: 'EcommerceIntegration',
            component: () => import('@/views/integration/Ecommerce.vue'),
            meta: { title: '电商平台集成' }
          },
          {
            path: 'logistics',
            name: 'LogisticsIntegration',
            component: () => import('@/views/integration/Logistics.vue'),
            meta: { title: '物流系统集成' }
          },
          {
            path: 'status/:type',
            name: 'IntegrationStatus',
            component: () => import('@/views/integration/Status.vue'),
            meta: { title: '集成状态详情', hideInMenu: true }
          }
        ]
      },
      {
        path: '/integrations',
        redirect: '/integration/list',
        meta: { hideInMenu: true }
      },

      // 税务政策管理
      {
        path: '/tax-policy',
        name: 'TaxPolicy',
        component: RouteView,
        redirect: '/tax-policy/list',
        meta: { title: '税务政策管理', icon: 'file-text' },
        children: [
          {
            path: 'list',
            name: 'TaxPolicyList',
            component: () => import('@/views/tax-policy/index.vue'),
            meta: { title: '政策列表' }
          }
        ]
      },

      // 报表管理
      {
        path: '/report',
        name: 'Report',
        component: RouteView,
        redirect: '/report/template',
        meta: { title: '报表管理', icon: 'bar-chart' },
        children: [
          {
            path: 'template',
            name: 'ReportTemplate',
            component: () => import('@/views/report/index.vue'),
            meta: { title: '报表模板' }
          }
        ]
      },

      // 系统设置
      {
        path: '/settings',
        name: 'Settings',
        component: () => import('@/views/user/Settings.vue'),
        meta: { title: '系统设置', icon: 'setting' }
      }
    ]
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/common/404.vue'),
    meta: { hideInMenu: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 税易通` : '税易通'

  // 检查是否需要认证
  const requiresAuth = to.meta.requiresAuth !== false
  const token = localStorage.getItem('token')

  // 如果是认证相关页面，直接放行
  if (to.path === '/login' || to.path === '/register' || to.path === '/forgot-password' || to.path === '/reset-password') {
    next()
    return
  }

  // 检查登录状态
  if (requiresAuth && !token) {
    next('/login')
    return
  }

  next()
})

export default router
