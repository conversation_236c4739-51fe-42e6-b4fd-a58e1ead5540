/**
 * 报表管理 API 接口
 * 遵循 VUE_STANDARDS.md 规范
 */
import request from '@/utils/request'

/**
 * 报表管理 API 接口集合
 */
export const reportApi = {
  /**
   * 获取报表模板列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string} params.keyword - 搜索关键词
   * @param {string} params.type - 模板类型
   * @returns {Promise<Object>} API响应
   */
  getReportTemplates(params = {}) {
    return request({
      url: '/report-templates',
      method: 'GET',
      params
    })
  },

  /**
   * 根据ID获取报表模板详情
   * @param {string} id - 模板ID
   * @returns {Promise<Object>} API响应
   */
  getReportTemplateById(id) {
    return request({
      url: `/report-templates/${id}`,
      method: 'GET'
    })
  },

  /**
   * 创建报表模板
   * @param {Object} data - 模板数据
   * @param {string} data.name - 模板名称
   * @param {string} data.description - 模板描述
   * @param {string} data.type - 模板类型
   * @param {string} data.content - 模板内容
   * @param {string} data.parameters - 模板参数
   * @returns {Promise<Object>} API响应
   */
  createReportTemplate(data) {
    return request({
      url: '/report-templates',
      method: 'POST',
      data
    })
  },

  /**
   * 更新报表模板
   * @param {string} id - 模板ID
   * @param {Object} data - 更新的模板数据
   * @returns {Promise<Object>} API响应
   */
  updateReportTemplate(id, data) {
    return request({
      url: `/report-templates/${id}`,
      method: 'PUT',
      data
    })
  },

  /**
   * 删除报表模板
   * @param {string} id - 模板ID
   * @returns {Promise<Object>} API响应
   */
  deleteReportTemplate(id) {
    return request({
      url: `/report-templates/${id}`,
      method: 'DELETE'
    })
  },

  /**
   * 批量删除报表模板
   * @param {Array<string>} ids - 模板ID数组
   * @returns {Promise<Object>} API响应
   */
  batchDeleteReportTemplates(ids) {
    return request({
      url: '/report-templates/batch/delete',
      method: 'POST',
      data: { ids }
    })
  },

  /**
   * 根据类型获取报表模板
   * @param {string} type - 模板类型，可选
   * @returns {Promise<Object>} API响应
   */
  getReportTemplatesByType(type = '') {
    return request({
      url: '/report-templates/by-type',
      method: 'GET',
      params: type ? { type } : {}
    })
  },

  /**
   * 生成报表
   * @param {Object} data - 生成参数
   * @param {string} data.template_id - 模板ID
   * @param {Object} data.parameters - 生成参数
   * @param {string} data.format - 输出格式
   * @returns {Promise<Object>} API响应
   */
  generateReport(data) {
    return request({
      url: '/reports/generate',
      method: 'POST',
      data
    })
  },

  /**
   * 获取报表类型列表
   * @returns {Promise<Object>} API响应
   */
  getReportTypes() {
    return request({
      url: '/reports/types',
      method: 'GET'
    })
  },

  /**
   * 获取报表格式列表
   * @returns {Promise<Object>} API响应
   */
  getReportFormats() {
    return request({
      url: '/reports/formats',
      method: 'GET'
    })
  }
}

/**
 * 报表类型选项
 */
export const REPORT_TYPES = [
  { value: 'declaration', label: '申报表', color: 'blue', description: '税务申报相关报表' },
  { value: 'invoice', label: '发票报表', color: 'green', description: '发票管理相关报表' },
  { value: 'tax', label: '税务报表', color: 'orange', description: '税务计算和分析报表' },
  { value: 'financial', label: '财务报表', color: 'purple', description: '财务数据相关报表' },
  { value: 'statistics', label: '统计报表', color: 'cyan', description: '数据统计和分析报表' },
  { value: 'custom', label: '自定义报表', color: 'red', description: '用户自定义报表模板' }
]

/**
 * 报表格式选项
 */
export const REPORT_FORMATS = [
  { 
    value: 'excel', 
    label: 'Excel格式', 
    extension: '.xlsx',
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    icon: 'file-excel'
  },
  { 
    value: 'pdf', 
    label: 'PDF格式', 
    extension: '.pdf',
    mimeType: 'application/pdf',
    icon: 'file-pdf'
  },
  { 
    value: 'html', 
    label: 'HTML格式', 
    extension: '.html',
    mimeType: 'text/html',
    icon: 'file-text'
  },
  { 
    value: 'csv', 
    label: 'CSV格式', 
    extension: '.csv',
    mimeType: 'text/csv',
    icon: 'file'
  },
  { 
    value: 'json', 
    label: 'JSON格式', 
    extension: '.json',
    mimeType: 'application/json',
    icon: 'file-text'
  }
]

/**
 * 获取报表类型信息
 * @param {string} type - 类型值
 * @returns {Object} 类型信息
 */
export const getReportTypeInfo = (type) => {
  return REPORT_TYPES.find(item => item.value === type) || 
         { value: type, label: type, color: 'default', description: '' }
}

/**
 * 获取报表格式信息
 * @param {string} format - 格式值
 * @returns {Object} 格式信息
 */
export const getReportFormatInfo = (format) => {
  return REPORT_FORMATS.find(item => item.value === format) || 
         { value: format, label: format, extension: '', mimeType: '', icon: 'file' }
}

/**
 * 报表模板参数类型
 */
export const PARAMETER_TYPES = [
  { value: 'string', label: '文本', description: '字符串类型参数' },
  { value: 'number', label: '数字', description: '数值类型参数' },
  { value: 'date', label: '日期', description: '日期类型参数' },
  { value: 'daterange', label: '日期范围', description: '日期范围类型参数' },
  { value: 'select', label: '选择', description: '下拉选择类型参数' },
  { value: 'multiselect', label: '多选', description: '多选类型参数' },
  { value: 'boolean', label: '布尔', description: '是/否类型参数' }
]

/**
 * 获取参数类型信息
 * @param {string} type - 参数类型
 * @returns {Object} 参数类型信息
 */
export const getParameterTypeInfo = (type) => {
  return PARAMETER_TYPES.find(item => item.value === type) || 
         { value: type, label: type, description: '' }
}

/**
 * 验证报表模板数据
 * @param {Object} templateData - 模板数据
 * @returns {Object} 验证结果
 */
export const validateTemplateData = (templateData) => {
  const errors = []
  
  if (!templateData.name || templateData.name.trim().length === 0) {
    errors.push('模板名称不能为空')
  }
  
  if (!templateData.type) {
    errors.push('模板类型不能为空')
  }
  
  if (!templateData.content || templateData.content.trim().length === 0) {
    errors.push('模板内容不能为空')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 验证报表生成参数
 * @param {Object} generateData - 生成参数
 * @returns {Object} 验证结果
 */
export const validateGenerateData = (generateData) => {
  const errors = []
  
  if (!generateData.template_id) {
    errors.push('模板ID不能为空')
  }
  
  if (!generateData.format) {
    errors.push('输出格式不能为空')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

export default reportApi
