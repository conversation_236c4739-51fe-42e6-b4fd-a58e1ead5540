import request from '@/utils/request'
import {
  createPaginatedApiCall,
  createListApiCall,
  createEntityApiCall,
  createOperationApiCall
} from '@/utils/api-helper'

// 原始API函数
function _getEnterprises (params) {
  return request({
    url: '/enterprises',
    method: 'get',
    params
  })
}

// 标准化的企业列表API（分页）
export const getEnterprises = createPaginatedApiCall(_getEnterprises)

// 原始API函数
function _getEnterpriseById (id) {
  return request({
    url: `/enterprises/${id}`,
    method: 'get'
  })
}

function _createEnterprise (data) {
  return request({
    url: '/enterprises',
    method: 'post',
    data
  })
}

function _updateEnterprise (id, data) {
  return request({
    url: `/enterprises/${id}`,
    method: 'put',
    data
  })
}

function _deleteEnterprise (id) {
  return request({
    url: `/enterprises/${id}`,
    method: 'delete'
  })
}

// 标准化的API函数
export const getEnterpriseById = createEntityApiCall(_getEnterpriseById)
export const createEnterprise = createOperationApiCall(_createEnterprise, {
  successMessage: '创建企业成功'
})
export const updateEnterprise = createOperationApiCall(_updateEnterprise, {
  successMessage: '更新企业成功'
})
export const deleteEnterprise = createOperationApiCall(_deleteEnterprise, {
  successMessage: '删除企业成功'
})

// 原始API函数
function _getEnterpriseStats () {
  return request({
    url: '/enterprises/stats',
    method: 'get'
  })
}

function _getEnterpriseTaxTypes (id) {
  return request({
    url: `/enterprises/${id}/tax-types`,
    method: 'get'
  })
}

// 标准化的API函数
export const getEnterpriseStats = createEntityApiCall(_getEnterpriseStats)
export const getEnterpriseTaxTypes = createListApiCall(_getEnterpriseTaxTypes)

// 分配税种给企业
export function assignTaxType (enterpriseId, data) {
  return request({
    url: `/enterprises/${enterpriseId}/tax-types`,
    method: 'post',
    data
  })
}

// 移除企业税种
export function removeTaxType (enterpriseId, taxTypeId) {
  return request({
    url: `/enterprises/${enterpriseId}/tax-types/${taxTypeId}`,
    method: 'delete'
  })
}

// 获取企业申报统计
export function getDeclarationSummary (enterpriseId) {
  return request({
    url: `/enterprises/${enterpriseId}/declarations/summary`,
    method: 'get'
  })
}

// 获取企业集成状态
export function getIntegrationStatus (enterpriseId) {
  return request({
    url: `/enterprises/${enterpriseId}/integrations`,
    method: 'get'
  })
}

// 连接外部系统
export function connectToSystem (enterpriseId, data) {
  return request({
    url: `/enterprises/${enterpriseId}/integrations`,
    method: 'post',
    data
  })
}

// 断开外部系统连接
export function disconnectFromSystem (enterpriseId, systemId) {
  return request({
    url: `/enterprises/${enterpriseId}/integrations/${systemId}`,
    method: 'delete'
  })
}

// 同步税率
export function syncTaxRates (enterpriseId) {
  return request({
    url: `/enterprises/${enterpriseId}/integrations/sync-tax-rates`,
    method: 'post'
  })
}

// 上传企业文档
export function uploadDocument (enterpriseId, formData) {
  return request({
    url: `/enterprises/${enterpriseId}/documents`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

// 获取企业文档列表
export function getDocumentList (enterpriseId) {
  return request({
    url: `/enterprises/${enterpriseId}/documents`,
    method: 'get'
  })
}
