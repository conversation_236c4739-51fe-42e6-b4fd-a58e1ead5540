import request from '@/utils/request'

// 获取税种列表
export function getTaxTypeList (params) {
  return request({
    url: '/tax-types',
    method: 'get',
    params
  })
}

// 获取税种列表 (简化版本，用于下拉选择等场景)
export function getTaxTypes (params = {}) {
  return request({
    url: '/tax-types',
    method: 'get',
    params: {
      ...params,
      pageSize: 1000 // 获取所有税种用于选择
    }
  })
}

// 获取税种详情
export function getTaxTypeDetail (id) {
  return request({
    url: `/tax-types/${id}`,
    method: 'get'
  })
}

// 创建税种
export function createTaxType (data) {
  return request({
    url: '/tax-types',
    method: 'post',
    data
  })
}

// 更新税种
export function updateTaxType (id, data) {
  return request({
    url: `/tax-types/${id}`,
    method: 'put',
    data
  })
}

// 删除税种
export function deleteTaxType (id) {
  return request({
    url: `/tax-types/${id}`,
    method: 'delete'
  })
}

// 获取税种规则
export function getTaxRules (taxTypeId) {
  return request({
    url: `/tax-types/${taxTypeId}/rules`,
    method: 'get'
  })
}

// 创建税种规则
export function createTaxRule (taxTypeId, data) {
  return request({
    url: `/tax-types/${taxTypeId}/rules`,
    method: 'post',
    data
  })
}

// 更新税种规则
export function updateTaxRule (taxTypeId, ruleId, data) {
  return request({
    url: `/tax-types/${taxTypeId}/rules/${ruleId}`,
    method: 'put',
    data
  })
}

// 删除税种规则
export function deleteTaxRule (taxTypeId, ruleId) {
  return request({
    url: `/tax-types/${taxTypeId}/rules/${ruleId}`,
    method: 'delete'
  })
}

// 获取申报周期
export function getDeclarationPeriod (taxTypeId) {
  return request({
    url: `/tax-types/${taxTypeId}/declaration-period`,
    method: 'get'
  })
}

// 设置申报周期
export function setDeclarationPeriod (taxTypeId, data) {
  return request({
    url: `/tax-types/${taxTypeId}/declaration-period`,
    method: 'post',
    data
  })
}

// 批量更新税种状态
export function batchUpdateTaxTypeStatus (ids, isActive) {
  return request({
    url: '/tax-types/batch/status',
    method: 'post',
    data: { ids, isActive }
  })
}

// 批量删除税种
export function batchDeleteTaxTypes (ids) {
  return request({
    url: '/tax-types/batch/delete',
    method: 'post',
    data: { ids }
  })
}

// 导出税种数据
export function exportTaxTypes (params) {
  return request({
    url: '/tax-types/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导入税种数据
export function importTaxTypes (formData) {
  return request({
    url: '/tax-types/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
