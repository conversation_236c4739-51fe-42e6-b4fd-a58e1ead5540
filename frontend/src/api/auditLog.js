import request from '@/utils/request'

/**
 * 获取审计日志列表
 * @param {Object} params - 查询参数
 * @param {string} params.userId - 用户ID
 * @param {string} params.action - 操作类型
 * @param {string} params.resourceType - 资源类型
 * @param {string} params.resourceId - 资源ID
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {string} params.status - 状态
 * @param {string} params.module - 模块
 * @param {string} params.enterpriseId - 企业ID
 * @param {string} params.riskLevel - 风险等级
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.sortBy - 排序字段
 * @param {string} params.sortOrder - 排序方向
 */
export function getAuditLogs (params) {
  return request({
    url: '/audit-logs',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取审计日志详情
 * @param {string} id - 审计日志ID
 */
export function getAuditLogById (id) {
  return request({
    url: `/audit-logs/${id}`,
    method: 'get'
  })
}

/**
 * 创建审计日志
 * @param {Object} data - 审计日志数据
 * @param {string} data.userId - 用户ID
 * @param {string} data.userName - 用户名
 * @param {string} data.action - 操作类型
 * @param {string} data.resourceType - 资源类型
 * @param {string} data.resourceId - 资源ID
 * @param {string} data.description - 操作描述
 * @param {string} data.ipAddress - IP地址
 * @param {string} data.userAgent - 用户代理
 * @param {Object} data.beforeState - 操作前状态
 * @param {Object} data.afterState - 操作后状态
 * @param {string} data.status - 状态
 * @param {string} data.errorMessage - 错误信息
 * @param {string} data.module - 模块
 * @param {number} data.duration - 操作耗时
 * @param {string} data.enterpriseId - 企业ID
 * @param {string} data.riskLevel - 风险等级
 * @param {boolean} data.sensitiveData - 是否涉及敏感数据
 */
export function createAuditLog (data) {
  return request({
    url: '/audit-logs',
    method: 'post',
    data
  })
}

/**
 * 删除审计日志
 * @param {string} id - 审计日志ID
 */
export function deleteAuditLog (id) {
  return request({
    url: `/audit-logs/${id}`,
    method: 'delete'
  })
}

/**
 * 获取我的审计日志
 * @param {Object} params - 查询参数
 */
export function getMyAuditLogs (params) {
  return request({
    url: '/audit-logs/my',
    method: 'get',
    params
  })
}

/**
 * 根据用户ID获取审计日志
 * @param {string} userId - 用户ID
 * @param {Object} params - 查询参数
 */
export function getAuditLogsByUser (userId, params) {
  return request({
    url: `/audit-logs/user/${userId}`,
    method: 'get',
    params
  })
}

/**
 * 根据资源获取审计日志
 * @param {string} resourceType - 资源类型
 * @param {string} resourceId - 资源ID
 * @param {Object} params - 查询参数
 */
export function getAuditLogsByResource (resourceType, resourceId, params) {
  return request({
    url: `/audit-logs/resource/${resourceType}/${resourceId}`,
    method: 'get',
    params
  })
}

/**
 * 获取审计日志统计信息
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {string} params.enterpriseId - 企业ID
 * @param {string} params.userId - 用户ID
 */
export function getAuditLogStats (params) {
  return request({
    url: '/audit-logs/stats',
    method: 'get',
    params
  })
}

/**
 * 清理旧的审计日志
 * @param {number} retentionDays - 保留天数
 */
export function cleanupOldAuditLogs (retentionDays) {
  return request({
    url: '/audit-logs/cleanup',
    method: 'post',
    data: { retentionDays }
  })
}

/**
 * 导出审计日志
 * @param {string} format - 导出格式 (csv, excel, json)
 * @param {Object} params - 查询参数
 */
export function exportAuditLogs (format = 'excel', params = {}) {
  return request({
    url: '/audit-logs/export',
    method: 'get',
    params: { ...params, format },
    responseType: 'blob'
  })
}

// ========== 便捷方法 ==========

/**
 * 记录用户操作日志
 * @param {string} action - 操作类型
 * @param {string} resourceType - 资源类型
 * @param {string} resourceId - 资源ID
 * @param {string} description - 操作描述
 * @param {Object} options - 可选参数
 */
export function logUserAction (action, resourceType, resourceId, description, options = {}) {
  const data = {
    action,
    resourceType,
    resourceId,
    description,
    status: 'success',
    riskLevel: 'low',
    ...options
  }

  // 自动获取用户信息和IP等
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  if (userInfo.id) {
    data.userId = userInfo.id
    data.userName = userInfo.userName || userInfo.name
  }

  return createAuditLog(data)
}

/**
 * 记录错误操作日志
 * @param {string} action - 操作类型
 * @param {string} resourceType - 资源类型
 * @param {string} resourceId - 资源ID
 * @param {string} errorMessage - 错误信息
 * @param {Object} options - 可选参数
 */
export function logErrorAction (action, resourceType, resourceId, errorMessage, options = {}) {
  return logUserAction(action, resourceType, resourceId, `操作失败: ${errorMessage}`, {
    status: 'error',
    errorMessage,
    riskLevel: 'medium',
    ...options
  })
}

/**
 * 获取操作类型选项
 */
export function getActionTypes () {
  return [
    { value: 'create', label: '创建' },
    { value: 'read', label: '查看' },
    { value: 'update', label: '更新' },
    { value: 'delete', label: '删除' },
    { value: 'login', label: '登录' },
    { value: 'logout', label: '登出' },
    { value: 'export', label: '导出' },
    { value: 'import', label: '导入' },
    { value: 'approve', label: '审批' },
    { value: 'reject', label: '拒绝' },
    { value: 'submit', label: '提交' },
    { value: 'cancel', label: '取消' },
    { value: 'upload', label: '上传' },
    { value: 'download', label: '下载' }
  ]
}

/**
 * 获取资源类型选项
 */
export function getResourceTypes () {
  return [
    { value: 'user', label: '用户' },
    { value: 'enterprise', label: '企业' },
    { value: 'invoice', label: '发票' },
    { value: 'declaration', label: '申报' },
    { value: 'tax_type', label: '税种' },
    { value: 'tax_rule', label: '税则' },
    { value: 'role', label: '角色' },
    { value: 'permission', label: '权限' },
    { value: 'notification', label: '通知' },
    { value: 'system', label: '系统' }
  ]
}

/**
 * 获取风险等级选项
 */
export function getRiskLevels () {
  return [
    { value: 'low', label: '低风险', color: 'green' },
    { value: 'medium', label: '中风险', color: 'orange' },
    { value: 'high', label: '高风险', color: 'red' },
    { value: 'critical', label: '严重风险', color: 'red' }
  ]
}

// ========== 兼容性API ==========

/**
 * 获取审计日志列表（兼容store中的命名）
 * @param {Object} params - 查询参数
 */
export function getAuditLogList (params) {
  return getAuditLogs(params)
}

/**
 * 获取审计日志详情（兼容store中的命名）
 * @param {string} id - 审计日志ID
 */
export function getAuditLogDetail (id) {
  return getAuditLogById(id)
}
