import request from '@/utils/request'
import {
  createEntityApiCall,
  createOperationApiCall,
  createSafeApiCall
} from '@/utils/api-helper'

/**
 * 认证相关API
 * 提供用户认证、授权相关的API接口
 * 基于API_DATA_STANDARDS.md规范实现
 */

// 原始API函数
/**
 * @typedef {Object} LoginData
 * @property {string} phone - 手机号
 * @property {string} password - 密码
 * @property {boolean} [remember] - 是否记住登录状态
 */

/**
 * @typedef {Object} LoginResponse
 * @property {string} accessToken - 访问令牌
 * @property {string} refreshToken - 刷新令牌
 * @property {import('@/types/api').User} user - 用户信息
 */

/**
 * 用户登录（原始函数）
 * @param {LoginData} data - 登录数据
 * @returns {Promise<import('@/types/api').EntityResponse<LoginResponse>>}
 */
function _login (data) {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  })
}

/**
 * 用户注册（原始函数）
 * @param {Object} data - 注册数据
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _register (data) {
  return request({
    url: '/auth/register',
    method: 'post',
    data
  })
}

/**
 * 用户登出（原始函数）
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _logout () {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

// 标准化的API函数
/**
 * 用户登录
 * 自动处理登录响应和错误，不显示成功消息（由调用方处理）
 */
export const login = createEntityApiCall(_login, {
  showSuccess: false // 登录成功由页面组件处理跳转和提示
})

/**
 * 用户注册
 * 返回注册响应数据，不自动显示成功消息（由页面组件处理）
 */
export const register = createEntityApiCall(_register, {
  showSuccess: false // 注册成功由页面组件处理跳转和提示
})

/**
 * 用户登出
 * 自动处理登出结果和成功提示
 */
export const logout = createOperationApiCall(_logout, {
  successMessage: '退出登录成功'
})

// 原始API函数
/**
 * 刷新令牌（原始函数）
 * @param {string} refreshToken - 刷新令牌
 * @returns {Promise<import('@/types/api').EntityResponse<LoginResponse>>}
 */
function _refreshToken (refreshToken) {
  return request({
    url: '/auth/refresh-token',
    method: 'post',
    data: {
      refreshToken
    }
  })
}

/**
 * 忘记密码（原始函数）
 * @param {Object} data - 忘记密码数据
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _forgotPassword (data) {
  return request({
    url: '/auth/forgot-password',
    method: 'post',
    data
  })
}

/**
 * 重置密码（原始函数）
 * @param {Object} data - 重置密码数据
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _resetPassword (data) {
  return request({
    url: '/auth/reset-password',
    method: 'post',
    data
  })
}

// 标准化的API函数
/**
 * 刷新令牌
 * 用于token刷新，不显示成功消息
 */
export const refreshToken = createEntityApiCall(_refreshToken, {
  showSuccess: false,
  showError: false // token刷新失败由request拦截器处理
})

/**
 * 忘记密码
 * 发送密码重置邮件/短信
 */
export const forgotPassword = createOperationApiCall(_forgotPassword, {
  successMessage: '密码重置链接已发送，请查收'
})

/**
 * 重置密码
 * 使用重置令牌重置密码
 */
export const resetPassword = createOperationApiCall(_resetPassword, {
  successMessage: '密码重置成功，请使用新密码登录'
})

// 原始API函数
/**
 * 获取用户信息（原始函数）
 * @returns {Promise<import('@/types/api').EntityResponse<import('@/types/api').User>>}
 */
function _getUserInfo () {
  return request({
    url: '/auth/profile',
    method: 'get'
  })
}

/**
 * 更新用户信息（原始函数）
 * @param {Partial<import('@/types/api').User>} data - 用户信息数据
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _updateProfile (data) {
  return request({
    url: '/auth/profile',
    method: 'put',
    data
  })
}

/**
 * 修改密码（原始函数）
 * @param {Object} data - 密码数据
 * @param {string} data.oldPassword - 旧密码
 * @param {string} data.newPassword - 新密码
 * @param {string} data.confirmPassword - 确认密码
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _changePassword (data) {
  return request({
    url: '/auth/change-password',
    method: 'post',
    data
  })
}

// 标准化的API函数
/**
 * 获取用户信息
 * 自动处理用户信息获取和错误
 */
export const getUserInfo = createEntityApiCall(_getUserInfo, {
  showError: true
})

/**
 * 更新用户信息
 * 自动处理更新结果和成功提示
 */
export const updateProfile = createOperationApiCall(_updateProfile, {
  successMessage: '个人信息更新成功'
})

/**
 * 修改密码
 * 自动处理密码修改结果和成功提示
 */
export const changePassword = createOperationApiCall(_changePassword, {
  successMessage: '密码修改成功，请重新登录'
})
