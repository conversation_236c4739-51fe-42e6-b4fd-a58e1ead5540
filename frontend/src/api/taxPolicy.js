/**
 * 税务政策管理 API 接口
 * 遵循 VUE_STANDARDS.md 规范
 */
import request from '@/utils/request'

/**
 * 税务政策 API 接口集合
 */
export const taxPolicyApi = {
  /**
   * 获取税务政策列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string} params.keyword - 搜索关键词
   * @param {string} params.category - 政策分类
   * @param {string} params.status - 政策状态
   * @param {string} params.priority_level - 优先级
   * @param {string} params.issuing_authority - 发布机构
   * @param {string} params.effective_from - 生效开始日期
   * @param {string} params.effective_to - 生效结束日期
   * @returns {Promise<Object>} API响应
   */
  getTaxPolicies(params = {}) {
    return request({
      url: '/tax-policies',
      method: 'GET',
      params
    })
  },

  /**
   * 根据ID获取税务政策详情
   * @param {string} id - 政策ID
   * @returns {Promise<Object>} API响应
   */
  getTaxPolicyById(id) {
    return request({
      url: `/tax-policies/${id}`,
      method: 'GET'
    })
  },

  /**
   * 创建税务政策
   * @param {Object} data - 政策数据
   * @param {string} data.policy_number - 政策编号
   * @param {string} data.title - 政策标题
   * @param {string} data.category - 政策分类
   * @param {string} data.tax_types - 适用税种
   * @param {string} data.applicable_regions - 适用地区
   * @param {string} data.applicable_industries - 适用行业
   * @param {string} data.applicable_enterprises - 适用企业
   * @param {string} data.policy_content - 政策内容
   * @param {string} data.policy_summary - 政策摘要
   * @param {string} data.legal_basis - 法律依据
   * @param {string} data.issuing_authority - 发布机构
   * @param {string} data.issue_date - 发布日期
   * @param {string} data.effective_date - 生效日期
   * @param {string} data.expiry_date - 到期日期
   * @param {string} data.priority_level - 优先级
   * @param {string} data.impact_assessment - 影响评估
   * @param {string} data.implementation_guidance - 实施指导
   * @param {string} data.related_policies - 相关政策
   * @param {string} data.attachments - 附件
   * @param {string} data.keywords - 关键词
   * @returns {Promise<Object>} API响应
   */
  createTaxPolicy(data) {
    return request({
      url: '/tax-policies',
      method: 'POST',
      data
    })
  },

  /**
   * 更新税务政策
   * @param {string} id - 政策ID
   * @param {Object} data - 更新的政策数据
   * @returns {Promise<Object>} API响应
   */
  updateTaxPolicy(id, data) {
    return request({
      url: `/tax-policies/${id}`,
      method: 'PUT',
      data
    })
  },

  /**
   * 删除税务政策
   * @param {string} id - 政策ID
   * @returns {Promise<Object>} API响应
   */
  deleteTaxPolicy(id) {
    return request({
      url: `/tax-policies/${id}`,
      method: 'DELETE'
    })
  },

  /**
   * 批量删除税务政策
   * @param {Array<string>} ids - 政策ID数组
   * @returns {Promise<Object>} API响应
   */
  batchDeleteTaxPolicies(ids) {
    return request({
      url: '/tax-policies/batch/delete',
      method: 'POST',
      data: { ids }
    })
  },

  /**
   * 更新税务政策状态
   * @param {string} id - 政策ID
   * @param {string} status - 新状态 (draft|effective|expired|revoked)
   * @returns {Promise<Object>} API响应
   */
  updateTaxPolicyStatus(id, status) {
    return request({
      url: `/tax-policies/${id}/status`,
      method: 'PUT',
      data: { status }
    })
  },

  /**
   * 批量更新税务政策状态
   * @param {Array<string>} ids - 政策ID数组
   * @param {string} status - 新状态
   * @returns {Promise<Object>} API响应
   */
  batchUpdateTaxPolicyStatus(ids, status) {
    return request({
      url: '/tax-policies/batch/status',
      method: 'POST',
      data: { ids, status }
    })
  },

  /**
   * 获取税务政策统计信息
   * @returns {Promise<Object>} API响应
   */
  getTaxPolicyStats() {
    return request({
      url: '/tax-policies/stats',
      method: 'GET'
    })
  },

  /**
   * 搜索税务政策
   * @param {string} keyword - 搜索关键词
   * @param {number} limit - 限制数量，默认10
   * @returns {Promise<Object>} API响应
   */
  searchTaxPolicies(keyword, limit = 10) {
    return request({
      url: '/tax-policies/search',
      method: 'GET',
      params: { keyword, limit }
    })
  },

  /**
   * 获取有效的税务政策
   * @param {string} category - 政策分类，可选
   * @returns {Promise<Object>} API响应
   */
  getEffectiveTaxPolicies(category = '') {
    return request({
      url: '/tax-policies/effective',
      method: 'GET',
      params: category ? { category } : {}
    })
  }
}

/**
 * 政策分类选项
 */
export const POLICY_CATEGORIES = [
  { value: 'tax_law', label: '税法法规', color: 'red' },
  { value: 'tax_policy', label: '税收政策', color: 'blue' },
  { value: 'tax_notice', label: '税务通知', color: 'orange' },
  { value: 'tax_guidance', label: '税务指导', color: 'green' }
]

/**
 * 政策状态选项
 */
export const POLICY_STATUSES = [
  { value: 'draft', label: '草稿', color: 'default' },
  { value: 'effective', label: '生效', color: 'success' },
  { value: 'expired', label: '过期', color: 'warning' },
  { value: 'revoked', label: '撤销', color: 'error' }
]

/**
 * 优先级选项
 */
export const PRIORITY_LEVELS = [
  { value: 'high', label: '高', color: 'red' },
  { value: 'normal', label: '普通', color: 'blue' },
  { value: 'low', label: '低', color: 'default' }
]

/**
 * 获取分类显示信息
 * @param {string} category - 分类值
 * @returns {Object} 分类信息
 */
export const getCategoryInfo = (category) => {
  return POLICY_CATEGORIES.find(item => item.value === category) || 
         { value: category, label: category, color: 'default' }
}

/**
 * 获取状态显示信息
 * @param {string} status - 状态值
 * @returns {Object} 状态信息
 */
export const getStatusInfo = (status) => {
  return POLICY_STATUSES.find(item => item.value === status) || 
         { value: status, label: status, color: 'default' }
}

/**
 * 获取优先级显示信息
 * @param {string} priority - 优先级值
 * @returns {Object} 优先级信息
 */
export const getPriorityInfo = (priority) => {
  return PRIORITY_LEVELS.find(item => item.value === priority) || 
         { value: priority, label: priority, color: 'default' }
}

export default taxPolicyApi
