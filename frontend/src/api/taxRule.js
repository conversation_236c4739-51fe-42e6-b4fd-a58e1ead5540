import request from '@/utils/request'

/**
 * 获取税则列表
 * @param {Object} params - 查询参数
 * @param {string} params.taxTypeId - 税种ID
 * @param {string} params.name - 税则名称
 * @param {boolean} params.isActive - 是否有效
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.sortBy - 排序字段
 * @param {string} params.sortOrder - 排序方向
 */
export function getTaxRules (params) {
  return request({
    url: '/tax-rules',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取税则详情
 * @param {string} id - 税则ID
 */
export function getTaxRuleById (id) {
  return request({
    url: `/tax-rules/${id}`,
    method: 'get'
  })
}

/**
 * 创建税则
 * @param {Object} data - 税则数据
 * @param {string} data.taxTypeId - 税种ID
 * @param {string} data.name - 税则名称
 * @param {string} data.description - 税则描述
 * @param {string} data.condition - 适用条件
 * @param {string} data.formula - 计算公式
 * @param {string} data.effectiveDate - 生效日期
 * @param {string} data.expiryDate - 失效日期
 * @param {Object} data.parameters - 额外参数
 */
export function createTaxRule (data) {
  return request({
    url: '/tax-rules',
    method: 'post',
    data
  })
}

/**
 * 更新税则
 * @param {string} id - 税则ID
 * @param {Object} data - 更新数据
 */
export function updateTaxRule (id, data) {
  return request({
    url: `/tax-rules/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除税则
 * @param {string} id - 税则ID
 */
export function deleteTaxRule (id) {
  return request({
    url: `/tax-rules/${id}`,
    method: 'delete'
  })
}

/**
 * 根据税种ID获取税则列表
 * @param {string} taxTypeId - 税种ID
 */
export function getTaxRulesByTaxType (taxTypeId) {
  return request({
    url: `/tax-types/${taxTypeId}/rules`,
    method: 'get'
  })
}

/**
 * 验证税则
 * @param {Object} data - 税则数据
 */
export function validateTaxRule (data) {
  return request({
    url: '/tax-rules/validate',
    method: 'post',
    data
  })
}

/**
 * 获取税则统计信息
 */
export function getTaxRuleStats () {
  return request({
    url: '/tax-rules/stats',
    method: 'get'
  })
}

/**
 * 批量删除税则
 * @param {Array} ids - 税则ID数组
 */
export function batchDeleteTaxRules (ids) {
  return request({
    url: '/tax-rules/batch',
    method: 'delete',
    data: { ids }
  })
}

/**
 * 导出税则
 * @param {string} format - 导出格式 (csv, excel, json)
 */
export function exportTaxRules (format = 'excel') {
  return request({
    url: '/tax-rules/export',
    method: 'get',
    params: { format },
    responseType: 'blob'
  })
}

/**
 * 导入税则
 * @param {FormData} formData - 包含文件的表单数据
 */
export function importTaxRules (formData) {
  return request({
    url: '/tax-rules/import',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

// ========== 兼容性API ==========

/**
 * 获取税则列表（兼容store中的命名）
 * @param {Object} params - 查询参数
 */
export function getTaxRuleList (params) {
  return getTaxRules(params)
}

/**
 * 获取税则详情（兼容store中的命名）
 * @param {string} id - 税则ID
 */
export function getTaxRuleDetail (id) {
  return getTaxRuleById(id)
}
