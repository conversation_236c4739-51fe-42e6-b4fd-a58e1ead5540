import request from '@/utils/request'

// 获取集成列表
export function getIntegrationList () {
  return request({
    url: '/integrations',
    method: 'get'
  })
}

// 连接服务
export function connectService (type, data) {
  return request({
    url: `/integrations/${type}/connect`,
    method: 'post',
    data
  })
}

// 断开服务
export function disconnectService (type) {
  return request({
    url: `/integrations/${type}/disconnect`,
    method: 'delete'
  })
}

// 检查集成状态
export function checkIntegrationStatus (type) {
  return request({
    url: `/integrations/${type}/status`,
    method: 'get'
  })
}

// 政府平台对接 - 检查连接状态
export function checkGovConnectionStatus () {
  return request({
    url: '/gov/status',
    method: 'get'
  })
}

// 政府平台对接 - 建立连接
export function establishGovConnection (data) {
  return request({
    url: '/gov/connect',
    method: 'post',
    data
  })
}

// 政府平台对接 - 提交到政府平台
export function submitToGov (data) {
  return request({
    url: '/gov/submit',
    method: 'post',
    data
  })
}

// 政府平台对接 - 获取已提交的报告列表
export function getSubmittedReports () {
  return request({
    url: '/gov/reports',
    method: 'get'
  })
}
