import request from '@/utils/request'

// 上传文件
export function uploadFile (formData) {
  return request({
    url: '/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

// 上传头像
export function uploadAvatar (formData) {
  return request({
    url: '/upload/avatar',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

// 删除文件
export function deleteFile (fileId) {
  return request({
    url: `/upload/${fileId}`,
    method: 'delete'
  })
}

// 获取文件信息
export function getFileInfo (fileId) {
  return request({
    url: `/upload/${fileId}`,
    method: 'get'
  })
}
