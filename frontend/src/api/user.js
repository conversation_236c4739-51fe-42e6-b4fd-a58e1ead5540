import request from '@/utils/request'
import {
  createPaginatedApiCall,
  createListApiCall,
  createEntityApiCall,
  createOperationApiCall
} from '@/utils/api-helper'

/**
 * 用户管理API
 * 提供用户相关的API接口，包括用户信息管理、个人资料更新、企业用户管理等功能
 * 基于API_DATA_STANDARDS.md规范实现
 */

// 原始API函数
/**
 * @typedef {import('@/types/api').UserSearchParams} UserSearchParams
 * @typedef {import('@/types/api').User} User
 * @typedef {import('@/types/api').PaginatedResponse} PaginatedResponse
 */

/**
 * 获取用户列表（原始函数）
 * @param {UserSearchParams} params - 查询参数
 * @returns {Promise<PaginatedResponse<User>>}
 */
function _getUserList (params) {
  return request({
    url: '/users',
    method: 'get',
    params
  })
}

/**
 * 获取用户列表简化版本（原始函数）
 * @param {Object} params - 查询参数
 * @returns {Promise<import('@/types/api').ListResponse<User>>}
 */
function _getUsers (params = {}) {
  return request({
    url: '/users/search',
    method: 'get',
    params
  })
}

// 标准化的API函数
/**
 * 获取用户列表（分页）
 * 自动处理分页数据格式，确保使用items字段
 */
export const getUserList = createPaginatedApiCall(_getUserList)

/**
 * 获取用户列表（简化版本，用于下拉选择等场景）
 * 自动处理列表数据格式
 */
export const getUsers = createListApiCall(_getUsers)

// 原始API函数
/**
 * 获取用户详情（原始函数）
 * @param {string} userId - 用户ID
 * @returns {Promise<import('@/types/api').EntityResponse<User>>}
 */
function _getUserDetail (userId) {
  return request({
    url: `/users/${userId}`,
    method: 'get'
  })
}

/**
 * 创建用户（原始函数）
 * @param {Partial<User>} data - 用户数据
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _createUser (data) {
  return request({
    url: '/users',
    method: 'post',
    data
  })
}

/**
 * 更新用户信息（原始函数）
 * @param {string} userId - 用户ID
 * @param {Partial<User>} data - 更新数据
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _updateUser (userId, data) {
  return request({
    url: `/users/${userId}`,
    method: 'put',
    data
  })
}

/**
 * 删除用户（原始函数）
 * @param {string} userId - 用户ID
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _deleteUser (userId) {
  return request({
    url: `/users/${userId}`,
    method: 'delete'
  })
}

// 标准化的API函数
/**
 * 获取用户详情
 * 自动处理实体数据格式和错误
 */
export const getUserDetail = createEntityApiCall(_getUserDetail)

/**
 * 创建用户
 * 自动处理操作结果和成功提示
 */
export const createUser = createOperationApiCall(_createUser, {
  successMessage: '创建用户成功'
})

/**
 * 更新用户信息
 * 自动处理操作结果和成功提示
 */
export const updateUser = createOperationApiCall(_updateUser, {
  successMessage: '更新用户成功'
})

/**
 * 删除用户
 * 自动处理操作结果和成功提示
 */
export const deleteUser = createOperationApiCall(_deleteUser, {
  successMessage: '删除用户成功'
})

/**
 * 重置用户密码
 * @param {string} userId - 用户ID
 */
export function resetUserPassword (userId) {
  return request({
    url: `/users/${userId}/reset-password`,
    method: 'post'
  })
}

/**
 * 批量删除用户
 * @param {Array<string>} userIds - 用户ID数组
 */
export function batchDeleteUsers (userIds) {
  return request({
    url: '/users/batch-delete',
    method: 'post',
    data: { userIds }
  })
}

/**
 * 切换用户状态
 * @param {string} userId - 用户ID
 * @param {boolean} isActive - 是否激活
 */
export function toggleUserStatus (userId, isActive) {
  return request({
    url: `/users/${userId}/status`,
    method: 'patch',
    data: { is_active: isActive }
  })
}

/**
 * 获取用户权限
 * @param {string} userId - 用户ID
 */
export function getUserPermissions (userId) {
  return request({
    url: `/users/${userId}/permissions`,
    method: 'get'
  })
}

/**
 * 更新用户权限
 * @param {string} userId - 用户ID
 * @param {Array<string>} permissions - 权限ID数组
 */
export function updateUserPermissions (userId, permissions) {
  return request({
    url: `/users/${userId}/permissions`,
    method: 'put',
    data: { permissions }
  })
}

/**
 * 获取用户角色
 * @param {string} userId - 用户ID
 */
export function getUserRoles (userId) {
  return request({
    url: `/users/${userId}/roles`,
    method: 'get'
  })
}

/**
 * 更新用户角色
 * @param {string} userId - 用户ID
 * @param {Array<string>} roles - 角色ID数组
 */
export function updateUserRoles (userId, roles) {
  return request({
    url: `/users/${userId}/roles`,
    method: 'put',
    data: { roles }
  })
}

/**
 * 导出用户数据
 * @param {Object} params - 导出参数
 * @param {string} params.format - 导出格式 (excel, csv)
 * @param {Array<string>} params.fields - 导出字段
 * @param {Object} params.filters - 过滤条件
 */
export function exportUsers (params) {
  return request({
    url: '/users/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

/**
 * 导入用户数据
 * @param {FormData} formData - 包含文件的表单数据
 */
export function importUsers (formData) {
  return request({
    url: '/users/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取用户统计信息
 * @param {Object} params - 统计参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {string} params.groupBy - 分组方式 (day, week, month)
 */
export function getUserStatistics (params) {
  return request({
    url: '/users/statistics',
    method: 'get',
    params
  })
}

/**
 * 验证用户名是否可用
 * @param {string} userName - 用户名
 * @param {string} excludeUserId - 排除的用户ID（用于编辑时验证）
 */
export function validateUserName (userName, excludeUserId = null) {
  return request({
    url: '/users/validate-username',
    method: 'post',
    data: {
      user_name: userName,
      exclude_user_id: excludeUserId
    }
  })
}

/**
 * 验证邮箱是否可用
 * @param {string} email - 邮箱
 * @param {string} excludeUserId - 排除的用户ID（用于编辑时验证）
 */
export function validateEmail (email, excludeUserId = null) {
  return request({
    url: '/users/validate-email',
    method: 'post',
    data: {
      email,
      exclude_user_id: excludeUserId
    }
  })
}

/**
 * 验证手机号是否可用
 * @param {string} phone - 手机号
 * @param {string} excludeUserId - 排除的用户ID（用于编辑时验证）
 */
export function validatePhone (phone, excludeUserId = null) {
  return request({
    url: '/users/validate-phone',
    method: 'post',
    data: {
      phone,
      exclude_user_id: excludeUserId
    }
  })
}

// ==================== 企业用户管理API ====================

/**
 * 获取企业用户列表
 * @param {string} enterpriseId - 企业ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 */
export function getEnterpriseUsers (enterpriseId, params) {
  return request({
    url: `/enterprises/${enterpriseId}/users`,
    method: 'get',
    params
  })
}

/**
 * 邀请用户加入企业
 * @param {string} enterpriseId - 企业ID
 * @param {Object} data - 邀请数据
 * @param {string} data.user_id - 用户ID
 * @param {string} data.role_id - 角色ID
 */
export function inviteUserToEnterprise (enterpriseId, data) {
  return request({
    url: `/enterprises/${enterpriseId}/users`,
    method: 'post',
    data
  })
}

/**
 * 移除企业用户
 * @param {string} enterpriseId - 企业ID
 * @param {string} userId - 用户ID
 */
export function removeUserFromEnterprise (enterpriseId, userId) {
  return request({
    url: `/enterprises/${enterpriseId}/users/${userId}`,
    method: 'delete'
  })
}

/**
 * 更新企业用户角色
 * @param {string} enterpriseId - 企业ID
 * @param {string} userId - 用户ID
 * @param {Object} data - 更新数据
 * @param {string} data.role_id - 新角色ID
 */
export function updateEnterpriseUserRole (enterpriseId, userId, data) {
  return request({
    url: `/enterprises/${enterpriseId}/users/${userId}`,
    method: 'put',
    data
  })
}

// ==================== 角色管理API ====================

/**
 * 获取角色列表
 * @param {Object} params - 查询参数
 */
export function getRoles (params = {}) {
  return request({
    url: '/roles',
    method: 'get',
    params
  })
}

/**
 * 创建角色
 * @param {Object} data - 角色数据
 */
export function createRole (data) {
  return request({
    url: '/roles',
    method: 'post',
    data
  })
}

/**
 * 更新角色
 * @param {string} roleId - 角色ID
 * @param {Object} data - 更新数据
 */
export function updateRole (roleId, data) {
  return request({
    url: `/roles/${roleId}`,
    method: 'put',
    data
  })
}

/**
 * 删除角色
 * @param {string} roleId - 角色ID
 */
export function deleteRole (roleId) {
  return request({
    url: `/roles/${roleId}`,
    method: 'delete'
  })
}

/**
 * 获取权限列表
 * @param {Object} params - 查询参数
 */
export function getPermissions (params = {}) {
  return request({
    url: '/permissions',
    method: 'get',
    params
  })
}

/**
 * 更新用户角色
 * @param {string} userId - 用户ID
 * @param {Object} data - 角色数据
 */
export function updateUserRole (userId, data) {
  return request({
    url: `/users/${userId}/role`,
    method: 'put',
    data
  })
}

/**
 * 转让所有权
 * @param {string} enterpriseId - 企业ID
 * @param {Object} data - 转让数据
 */
export function transferOwnership (enterpriseId, data) {
  return request({
    url: `/enterprises/${enterpriseId}/transfer-ownership`,
    method: 'post',
    data
  })
}

/**
 * 搜索用户（用于邀请）
 * @param {string} keyword - 搜索关键词
 * @param {Object} params - 查询参数
 */
export function searchUsersForInvite (keyword, params = {}) {
  return request({
    url: '/users/search',
    method: 'get',
    params: {
      keyword,
      ...params
    }
  })
}

/**
 * 检查用户所有权
 * @param {string} enterpriseId - 企业ID
 * @param {string} userId - 用户ID
 */
export function checkUserOwnership (enterpriseId, userId) {
  return request({
    url: `/enterprises/${enterpriseId}/users/${userId}/ownership`,
    method: 'get'
  })
}
