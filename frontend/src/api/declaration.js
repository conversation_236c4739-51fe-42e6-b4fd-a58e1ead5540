import request from '@/utils/request'

// 获取申报列表
export function getDeclarationList (params) {
  return request({
    url: '/declarations',
    method: 'get',
    params
  })
}

// 获取申报详情
export function getDeclarationDetail (id) {
  return request({
    url: `/declarations/${id}`,
    method: 'get'
  })
}

// 创建申报
export function createDeclaration (data) {
  return request({
    url: '/declarations',
    method: 'post',
    data
  })
}

// 更新申报
export function updateDeclaration (id, data) {
  return request({
    url: `/declarations/${id}`,
    method: 'put',
    data
  })
}

// 删除申报
export function deleteDeclaration (id) {
  return request({
    url: `/declarations/${id}`,
    method: 'delete'
  })
}

// 提交申报
export function submitDeclaration (id, data) {
  return request({
    url: `/declarations/${id}/submit`,
    method: 'post',
    data
  })
}

// 计算申报税款
export function calculateDeclaration (id) {
  return request({
    url: `/declarations/${id}/calculate`,
    method: 'get'
  })
}

// 生成申报单
export function generateDeclarations (data) {
  return request({
    url: '/declarations/generate',
    method: 'post',
    data
  })
}

// 获取申报历史
export function getDeclarationHistory (id) {
  return request({
    url: `/declarations/${id}/history`,
    method: 'get'
  })
}

// 获取申报附件
export function getDeclarationAttachments (id) {
  return request({
    url: `/declarations/${id}/attachments`,
    method: 'get'
  })
}

// 添加申报附件
export function addDeclarationAttachment (id, formData) {
  return request({
    url: `/declarations/${id}/attachments`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

// 删除申报附件
export function deleteDeclarationAttachment (declarationId, attachmentId) {
  return request({
    url: `/declarations/${declarationId}/attachments/${attachmentId}`,
    method: 'delete'
  })
}

// 提交申报到政府平台
export function submitDeclarationToGov (id, data) {
  return request({
    url: `/declarations/${id}/submit-to-gov`,
    method: 'post',
    data
  })
}

// 获取申报表模板
export function getDeclarationTemplates () {
  return request({
    url: '/declarations/templates',
    method: 'get'
  })
}

// 预览申报表
export function previewDeclaration (data) {
  return request({
    url: '/declarations/preview',
    method: 'post',
    data
  })
}

// 验证申报表
export function validateDeclaration (data) {
  return request({
    url: '/declarations/validate',
    method: 'post',
    data
  })
}

// 计算税款
export function calculateTax (params) {
  return request({
    url: '/declarations/calculate',
    method: 'get',
    params
  })
}

// 获取申报日历
export function getDeclarationCalendar (params) {
  return request({
    url: '/declarations/calendar',
    method: 'get',
    params
  })
}

// 获取申报统计信息
export function getDeclarationStats () {
  return request({
    url: '/declarations/stats',
    method: 'get'
  })
}
