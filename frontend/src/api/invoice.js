import request from '@/utils/request'
import {
  createPaginatedApiCall,
  createListApiCall,
  createEntityApiCall,
  createOperationApiCall
} from '@/utils/api-helper'

/**
 * 发票管理API
 * 提供发票相关的API接口，包括发票CRUD、核验、统计等功能
 * 基于API_DATA_STANDARDS.md规范实现
 */

// 原始API函数
/**
 * @typedef {import('@/types/api').Invoice} Invoice
 * @typedef {import('@/types/api').SearchParams} InvoiceSearchParams
 */

/**
 * 获取发票列表（原始函数）
 * @param {InvoiceSearchParams} params - 搜索参数
 * @returns {Promise<import('@/types/api').PaginatedResponse<Invoice>>}
 */
function _getInvoices (params) {
  return request({
    url: '/invoices',
    method: 'get',
    params
  })
}

/**
 * 获取发票详情（原始函数）
 * @param {string} id - 发票ID
 * @returns {Promise<import('@/types/api').EntityResponse<Invoice>>}
 */
function _getInvoiceById (id) {
  return request({
    url: `/invoices/${id}`,
    method: 'get'
  })
}

/**
 * 创建发票（原始函数）
 * @param {Partial<Invoice>} data - 发票数据
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _createInvoice (data) {
  return request({
    url: '/invoices',
    method: 'post',
    data
  })
}

/**
 * 更新发票信息（原始函数）
 * @param {string} id - 发票ID
 * @param {Partial<Invoice>} data - 更新数据
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _updateInvoice (id, data) {
  return request({
    url: `/invoices/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除发票（原始函数）
 * @param {string} id - 发票ID
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _deleteInvoice (id) {
  return request({
    url: `/invoices/${id}`,
    method: 'delete'
  })
}

// 标准化的API函数
/**
 * 获取发票列表（分页）
 * 自动处理分页数据格式，确保使用items字段
 */
export const getInvoices = createPaginatedApiCall(_getInvoices)

/**
 * 获取发票详情
 * 自动处理实体数据格式和错误
 */
export const getInvoiceById = createEntityApiCall(_getInvoiceById)

/**
 * 创建发票
 * 自动处理操作结果和成功提示
 */
export const createInvoice = createOperationApiCall(_createInvoice, {
  successMessage: '创建发票成功'
})

/**
 * 更新发票信息
 * 自动处理操作结果和成功提示
 */
export const updateInvoice = createOperationApiCall(_updateInvoice, {
  successMessage: '更新发票成功'
})

/**
 * 删除发票
 * 自动处理操作结果和成功提示
 */
export const deleteInvoice = createOperationApiCall(_deleteInvoice, {
  successMessage: '删除发票成功'
})

// 原始API函数
/**
 * 核验发票（原始函数）
 * @param {string} id - 发票ID
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _verifyInvoice (id) {
  return request({
    url: `/invoices/${id}/verify`,
    method: 'post'
  })
}

/**
 * 批量核验发票（原始函数）
 * @param {string[]} invoiceIds - 发票ID数组
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _batchVerifyInvoices (invoiceIds) {
  return request({
    url: '/invoices/batch/verify',
    method: 'post',
    data: { invoiceIds }
  })
}

/**
 * 批量删除发票（原始函数）
 * @param {string[]} invoiceIds - 发票ID数组
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _batchDeleteInvoices (invoiceIds) {
  return request({
    url: '/invoices/batch/delete',
    method: 'post',
    data: { invoiceIds }
  })
}

/**
 * 上传发票（原始函数）
 * @param {FormData} formData - 包含发票文件的表单数据
 * @returns {Promise<import('@/types/api').OperationResponse>}
 */
function _uploadInvoice (formData) {
  return request({
    url: '/invoices/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取发票统计信息（原始函数）
 * @returns {Promise<import('@/types/api').EntityResponse<Object>>}
 */
function _getInvoiceStats () {
  return request({
    url: '/invoices/stats',
    method: 'get'
  })
}

// 标准化的API函数
/**
 * 核验发票
 * 自动处理核验结果和成功提示
 */
export const verifyInvoice = createOperationApiCall(_verifyInvoice, {
  successMessage: '发票核验成功'
})

/**
 * 批量核验发票
 * 自动处理批量核验结果和成功提示
 */
export const batchVerifyInvoices = createOperationApiCall(_batchVerifyInvoices, {
  successMessage: '批量核验完成'
})

/**
 * 批量删除发票
 * 自动处理批量删除结果和成功提示
 */
export const batchDeleteInvoices = createOperationApiCall(_batchDeleteInvoices, {
  successMessage: '批量删除完成'
})

/**
 * 上传发票
 * 自动处理上传结果和成功提示
 */
export const uploadInvoice = createOperationApiCall(_uploadInvoice, {
  successMessage: '发票上传成功'
})

/**
 * 获取发票统计信息
 * 自动处理统计数据格式和错误
 */
export const getInvoiceStats = createEntityApiCall(_getInvoiceStats)



// 获取发票附件
export function getInvoiceAttachments (id) {
  return request({
    url: `/invoices/${id}/attachments`,
    method: 'get'
  })
}

// 添加发票附件
export function addInvoiceAttachment (id, formData) {
  return request({
    url: `/invoices/${id}/attachments`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

// 删除发票附件
export function deleteInvoiceAttachment (invoiceId, attachmentId) {
  return request({
    url: `/invoices/${invoiceId}/attachments/${attachmentId}`,
    method: 'delete'
  })
}

// 发票OCR扫描识别
export function scanInvoice (formData) {
  return request({
    url: '/invoices/scan',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

// 批量导入发票
export function importInvoices (formData) {
  return request({
    url: '/invoices/import',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

// ========== 发票数据集成相关API ==========

// 获取发票列表（兼容store中的命名）
export function getInvoiceList (params) {
  return getInvoices(params)
}

// 获取发票详情（兼容store中的命名）
export function getInvoiceDetail (id) {
  return getInvoiceById(id)
}

// 获取指定申报期间的发票
export function getInvoicesByDeclarationPeriod (enterpriseId, period) {
  return request({
    url: `/invoices/enterprise/${enterpriseId}/period/${period}`,
    method: 'get'
  })
}

// 更新发票认证状态
export function updateInvoiceAuthenticationStatus (id, data) {
  return request({
    url: `/invoices/${id}/authentication`,
    method: 'put',
    data
  })
}

// 更新发票申报状态
export function updateInvoiceDeclarationStatus (id, data) {
  return request({
    url: `/invoices/${id}/declaration`,
    method: 'put',
    data
  })
}

// 获取发票税务汇总
export function getInvoiceTaxSummary (enterpriseId, period = '') {
  return request({
    url: `/invoices/enterprise/${enterpriseId}/tax-summary`,
    method: 'get',
    params: { period }
  })
}

// ==================== 发票明细管理API ====================

/**
 * 创建发票明细
 * @param {Object} data - 发票明细数据
 * @param {string} data.invoice_id - 发票ID
 * @param {string} data.tax_type_id - 税种ID
 * @param {string} data.item_name - 商品名称
 * @param {string} data.specification - 规格型号
 * @param {string} data.unit - 单位
 * @param {number} data.quantity - 数量
 * @param {number} data.unit_price - 单价
 * @param {number} data.tax_rate - 税率
 * @param {string} data.tax_category - 税收分类编码
 * @param {string} data.remarks - 备注
 */
export function createInvoiceItem (data) {
  return request({
    url: '/invoice-items',
    method: 'post',
    data
  })
}

/**
 * 获取发票明细列表
 * @param {string} invoiceId - 发票ID
 * @param {Object} params - 查询参数
 */
export function getInvoiceItems (invoiceId, params = {}) {
  return request({
    url: `/invoices/${invoiceId}/items`,
    method: 'get',
    params
  })
}

/**
 * 获取发票明细详情
 * @param {string} itemId - 明细ID
 */
export function getInvoiceItemById (itemId) {
  return request({
    url: `/invoice-items/${itemId}`,
    method: 'get'
  })
}

/**
 * 更新发票明细
 * @param {string} itemId - 明细ID
 * @param {Object} data - 更新数据
 */
export function updateInvoiceItem (itemId, data) {
  return request({
    url: `/invoice-items/${itemId}`,
    method: 'put',
    data
  })
}

/**
 * 删除发票明细
 * @param {string} itemId - 明细ID
 */
export function deleteInvoiceItem (itemId) {
  return request({
    url: `/invoice-items/${itemId}`,
    method: 'delete'
  })
}

/**
 * 批量创建发票明细
 * @param {Array} items - 明细数据数组
 */
export function batchCreateInvoiceItems (items) {
  return request({
    url: '/invoice-items/batch',
    method: 'post',
    data: { items }
  })
}

/**
 * 批量更新发票明细
 * @param {Array} items - 明细数据数组
 */
export function batchUpdateInvoiceItems (items) {
  return request({
    url: '/invoice-items/batch',
    method: 'put',
    data: { items }
  })
}

/**
 * 批量删除发票明细
 * @param {Array} itemIds - 明细ID数组
 */
export function batchDeleteInvoiceItems (itemIds) {
  return request({
    url: '/invoice-items/batch',
    method: 'delete',
    data: { item_ids: itemIds }
  })
}

// ==================== 发票明细常量和工具函数 ====================

/**
 * 常用单位列表
 */
export const COMMON_UNITS = [
  '个', '台', '套', '件', '张', '本', '支', '只', '盒', '包',
  '箱', '袋', '瓶', '罐', '桶', '吨', '千克', '克', '米', '厘米',
  '平方米', '立方米', '升', '毫升', '小时', '天', '月', '年', '次', '项'
]

/**
 * 常用税率列表
 */
export const COMMON_TAX_RATES = [
  { label: '0%', value: 0 },
  { label: '3%', value: 0.03 },
  { label: '5%', value: 0.05 },
  { label: '6%', value: 0.06 },
  { label: '9%', value: 0.09 },
  { label: '13%', value: 0.13 },
  { label: '16%', value: 0.16 },
  { label: '17%', value: 0.17 }
]

/**
 * 计算发票明细金额
 * @param {Object} item - 发票明细项
 * @returns {Object} - 计算后的金额信息
 */
export function calculateInvoiceItemAmounts (item) {
  const quantity = parseFloat(item.quantity) || 0
  const unitPrice = parseFloat(item.unit_price) || 0
  const taxRate = parseFloat(item.tax_rate) || 0

  // 计算不含税金额
  const amountExcludingTax = quantity * unitPrice

  // 计算税额
  const taxAmount = amountExcludingTax * taxRate

  // 计算含税金额
  const amountIncludingTax = amountExcludingTax + taxAmount

  return {
    amount_excluding_tax: amountExcludingTax,
    tax_amount: taxAmount,
    amount_including_tax: amountIncludingTax
  }
}

/**
 * 验证发票明细数据
 * @param {Object} item - 发票明细项
 * @returns {Object} - 验证结果
 */
export function validateInvoiceItem (item) {
  const errors = []

  // 必填字段验证
  if (!item.item_name || item.item_name.trim() === '') {
    errors.push('商品名称不能为空')
  }

  if (!item.unit || item.unit.trim() === '') {
    errors.push('单位不能为空')
  }

  // 数值字段验证
  const quantity = parseFloat(item.quantity)
  if (isNaN(quantity) || quantity <= 0) {
    errors.push('数量必须大于0')
  }

  const unitPrice = parseFloat(item.unit_price)
  if (isNaN(unitPrice) || unitPrice < 0) {
    errors.push('单价不能为负数')
  }

  const taxRate = parseFloat(item.tax_rate)
  if (isNaN(taxRate) || taxRate < 0 || taxRate > 1) {
    errors.push('税率必须在0-100%之间')
  }

  // 字符串长度验证
  if (item.item_name && item.item_name.length > 100) {
    errors.push('商品名称不能超过100个字符')
  }

  if (item.specification && item.specification.length > 50) {
    errors.push('规格型号不能超过50个字符')
  }

  if (item.remarks && item.remarks.length > 200) {
    errors.push('备注不能超过200个字符')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
