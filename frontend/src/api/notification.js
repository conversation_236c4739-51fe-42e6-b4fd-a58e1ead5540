import request from '@/utils/request'

/**
 * 获取通知列表
 * @param {Object} params - 查询参数
 * @param {string} params.recipientId - 接收者ID
 * @param {string} params.type - 通知类型
 * @param {boolean} params.isRead - 是否已读
 * @param {string} params.createdStart - 创建开始时间
 * @param {string} params.createdEnd - 创建结束时间
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.sortBy - 排序字段
 * @param {string} params.sortOrder - 排序方向
 */
export function getNotifications (params) {
  return request({
    url: '/notifications',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取通知详情
 * @param {string} id - 通知ID
 */
export function getNotificationById (id) {
  return request({
    url: `/notifications/${id}`,
    method: 'get'
  })
}

/**
 * 创建通知
 * @param {Object} data - 通知数据
 * @param {string} data.recipientId - 接收者ID
 * @param {string} data.type - 通知类型
 * @param {string} data.title - 通知标题
 * @param {string} data.content - 通知内容
 * @param {string} data.relatedEntityType - 关联实体类型
 * @param {string} data.relatedEntityId - 关联实体ID
 */
export function createNotification (data) {
  return request({
    url: '/notifications',
    method: 'post',
    data
  })
}

/**
 * 批量创建通知
 * @param {Object} data - 批量通知数据
 * @param {Array} data.recipientIds - 接收者ID数组
 * @param {string} data.type - 通知类型
 * @param {string} data.title - 通知标题
 * @param {string} data.content - 通知内容
 * @param {string} data.relatedEntityType - 关联实体类型
 * @param {string} data.relatedEntityId - 关联实体ID
 */
export function batchCreateNotifications (data) {
  return request({
    url: '/notifications/batch',
    method: 'post',
    data
  })
}

/**
 * 更新通知
 * @param {string} id - 通知ID
 * @param {Object} data - 更新数据
 */
export function updateNotification (id, data) {
  return request({
    url: `/notifications/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除通知
 * @param {string} id - 通知ID
 */
export function deleteNotification (id) {
  return request({
    url: `/notifications/${id}`,
    method: 'delete'
  })
}

/**
 * 获取我的通知列表
 * @param {Object} params - 查询参数
 */
export function getMyNotifications (params) {
  return request({
    url: '/notifications/my',
    method: 'get',
    params
  })
}

/**
 * 获取通知计数
 * @param {string} recipientId - 接收者ID（可选，默认为当前用户）
 */
export function getNotificationCount (recipientId) {
  const params = recipientId ? { recipientId } : {}
  return request({
    url: '/notifications/count',
    method: 'get',
    params
  })
}

/**
 * 获取未读通知
 * @param {number} limit - 限制数量
 */
export function getUnreadNotifications (limit = 10) {
  return request({
    url: '/notifications/unread',
    method: 'get',
    params: { limit }
  })
}

/**
 * 标记通知为已读
 * @param {Array} ids - 通知ID数组
 */
export function markNotificationsAsRead (ids) {
  return request({
    url: '/notifications/mark-read',
    method: 'put',
    data: { ids }
  })
}

/**
 * 删除已读通知
 */
export function deleteReadNotifications () {
  return request({
    url: '/notifications/read',
    method: 'delete'
  })
}

/**
 * 批量删除通知
 * @param {Array} ids - 通知ID数组
 */
export function batchDeleteNotifications (ids) {
  return request({
    url: '/notifications/batch',
    method: 'delete',
    data: { ids }
  })
}

// ========== 便捷方法 ==========

/**
 * 标记单个通知为已读
 * @param {string} id - 通知ID
 */
export function markNotificationAsRead (id) {
  return markNotificationsAsRead([id])
}

/**
 * 标记所有未读通知为已读
 */
export function markAllNotificationsAsRead () {
  return getUnreadNotifications(1000).then(response => {
    if (response.code === 200 && response.data.length > 0) {
      const ids = response.data.map(notification => notification.id)
      return markNotificationsAsRead(ids)
    }
    return Promise.resolve({ code: 200, message: '没有未读通知' })
  })
}

/**
 * 获取通知类型选项
 */
export function getNotificationTypes () {
  return [
    { value: 'system', label: '系统通知' },
    { value: 'tax_reminder', label: '税务提醒' },
    { value: 'declaration_deadline', label: '申报截止提醒' },
    { value: 'invoice_verification', label: '发票验证通知' },
    { value: 'enterprise_update', label: '企业信息更新' },
    { value: 'user_action', label: '用户操作通知' },
    { value: 'audit_alert', label: '审计警告' },
    { value: 'policy_update', label: '政策更新' },
    { value: 'maintenance', label: '系统维护' },
    { value: 'security', label: '安全通知' }
  ]
}

// ========== 兼容性API ==========

/**
 * 获取通知列表（兼容store中的命名）
 * @param {Object} params - 查询参数
 */
export function getNotificationList (params) {
  return getNotifications(params)
}

/**
 * 获取通知详情（兼容store中的命名）
 * @param {string} id - 通知ID
 */
export function getNotificationDetail (id) {
  return getNotificationById(id)
}
