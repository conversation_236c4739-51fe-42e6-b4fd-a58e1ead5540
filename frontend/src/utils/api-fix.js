/**
 * API问题快速修复工具
 * 提供一键修复常见API连接问题的功能
 */

import { message } from 'ant-design-vue'
import { debugApi } from './api-debug'

/**
 * 检查并修复API配置问题
 * @returns {Promise<Object>} - 修复结果
 */
export async function fixApiConfiguration() {
  console.log('🔧 开始修复API配置问题...')
  
  const fixes = {
    applied: [],
    failed: [],
    warnings: []
  }

  try {
    // 1. 检查环境变量
    const apiBaseUrl = process.env.VUE_APP_API_BASE_URL
    if (!apiBaseUrl) {
      fixes.warnings.push('未设置VUE_APP_API_BASE_URL环境变量')
      console.warn('建议在.env.development文件中设置: VUE_APP_API_BASE_URL=http://localhost:8081/api')
    }

    // 2. 检查当前端口配置
    const currentPort = window.location.port
    if (currentPort === '8080') {
      // 前端在8080，检查后端是否在8081
      try {
        const response = await fetch('http://localhost:8081/api/health', {
          method: 'GET',
          timeout: 5000
        })
        if (response.ok) {
          fixes.applied.push('确认后端服务在8081端口正常运行')
        }
      } catch (error) {
        fixes.failed.push('无法连接到后端服务(8081端口): ' + error.message)
      }
    }

    // 3. 检查认证令牌
    const token = localStorage.getItem('token') || localStorage.getItem('access_token')
    if (!token) {
      fixes.warnings.push('未找到认证令牌，可能需要重新登录')
    } else {
      // 检查令牌是否过期
      try {
        const payload = JSON.parse(atob(token.split('.')[1]))
        const now = Math.floor(Date.now() / 1000)
        if (payload.exp && payload.exp < now) {
          fixes.warnings.push('认证令牌已过期，需要重新登录')
        } else {
          fixes.applied.push('认证令牌有效')
        }
      } catch (error) {
        fixes.warnings.push('认证令牌格式无效')
      }
    }

    console.log('✅ API配置检查完成')
    return fixes
  } catch (error) {
    fixes.failed.push('配置检查失败: ' + error.message)
    return fixes
  }
}

/**
 * 修复企业统计API问题
 * @returns {Promise<Object>} - 修复结果
 */
export async function fixEnterpriseStatsApi() {
  console.log('🔧 开始修复企业统计API问题...')
  
  const fixes = {
    applied: [],
    failed: [],
    warnings: []
  }

  try {
    // 1. 测试不同的API端点
    const endpoints = [
      '/enterprises/stats',
      '/api/enterprises/stats',
      '/enterprise/stats'
    ]

    for (const endpoint of endpoints) {
      try {
        const result = await debugApi.test(endpoint, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          timeout: 5000
        })

        if (result.success) {
          fixes.applied.push(`找到可用的API端点: ${endpoint}`)
          break
        }
      } catch (error) {
        fixes.failed.push(`端点 ${endpoint} 测试失败: ${error.message}`)
      }
    }

    // 2. 检查后端服务状态
    try {
      const healthCheck = await fetch('http://localhost:8081/health', {
        method: 'GET',
        timeout: 3000
      })
      
      if (healthCheck.ok) {
        fixes.applied.push('后端服务健康检查通过')
      } else {
        fixes.failed.push(`后端服务健康检查失败: ${healthCheck.status}`)
      }
    } catch (error) {
      fixes.failed.push('无法连接到后端服务: ' + error.message)
    }

    // 3. 检查数据库连接（通过API）
    try {
      const dbCheck = await fetch('http://localhost:8081/api/health/db', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        timeout: 5000
      })
      
      if (dbCheck.ok) {
        fixes.applied.push('数据库连接正常')
      } else {
        fixes.failed.push('数据库连接可能有问题')
      }
    } catch (error) {
      fixes.warnings.push('无法检查数据库连接状态')
    }

    console.log('✅ 企业统计API检查完成')
    return fixes
  } catch (error) {
    fixes.failed.push('API检查失败: ' + error.message)
    return fixes
  }
}

/**
 * 一键修复所有常见问题
 * @returns {Promise<Object>} - 完整的修复结果
 */
export async function quickFixAll() {
  console.log('🚀 开始一键修复所有问题...')
  
  const results = {
    configFix: null,
    apiFix: null,
    summary: {
      totalApplied: 0,
      totalFailed: 0,
      totalWarnings: 0
    }
  }

  try {
    // 1. 修复配置问题
    message.loading('正在检查API配置...', 0)
    results.configFix = await fixApiConfiguration()
    message.destroy()

    // 2. 修复API问题
    message.loading('正在检查API连接...', 0)
    results.apiFix = await fixEnterpriseStatsApi()
    message.destroy()

    // 3. 汇总结果
    results.summary.totalApplied = 
      results.configFix.applied.length + results.apiFix.applied.length
    results.summary.totalFailed = 
      results.configFix.failed.length + results.apiFix.failed.length
    results.summary.totalWarnings = 
      results.configFix.warnings.length + results.apiFix.warnings.length

    // 4. 显示结果
    if (results.summary.totalFailed === 0) {
      message.success(`修复完成！应用了 ${results.summary.totalApplied} 个修复`)
    } else {
      message.warning(`部分修复失败，${results.summary.totalFailed} 个问题需要手动处理`)
    }

    console.log('📊 修复结果汇总:', results.summary)
    return results
  } catch (error) {
    message.error('修复过程中出现错误: ' + error.message)
    console.error('修复失败:', error)
    return results
  }
}

/**
 * 重置API配置到默认状态
 * @returns {Object} - 重置结果
 */
export function resetApiConfiguration() {
  console.log('🔄 重置API配置到默认状态...')
  
  const reset = {
    applied: [],
    failed: []
  }

  try {
    // 1. 清除可能有问题的缓存
    if (window.localStorage) {
      const keysToRemove = ['api_cache', 'request_cache', 'response_cache']
      keysToRemove.forEach(key => {
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key)
          reset.applied.push(`清除缓存: ${key}`)
        }
      })
    }

    // 2. 重置axios默认配置
    if (window.axios) {
      delete window.axios.defaults.baseURL
      reset.applied.push('重置axios默认配置')
    }

    // 3. 清除可能的错误状态
    if (window.Vue && window.Vue.config) {
      window.Vue.config.silent = false
      reset.applied.push('重置Vue错误处理配置')
    }

    console.log('✅ API配置重置完成')
    message.success('API配置已重置，请刷新页面')
    
    return reset
  } catch (error) {
    reset.failed.push('重置失败: ' + error.message)
    console.error('重置失败:', error)
    return reset
  }
}

/**
 * 生成API问题报告
 * @returns {Promise<Object>} - 问题报告
 */
export async function generateApiReport() {
  console.log('📋 生成API问题报告...')
  
  const report = {
    timestamp: new Date().toISOString(),
    environment: {
      nodeEnv: process.env.NODE_ENV,
      apiBaseUrl: process.env.VUE_APP_API_BASE_URL,
      currentUrl: window.location.href,
      userAgent: navigator.userAgent
    },
    configuration: null,
    connectivity: null,
    diagnosis: null,
    recommendations: []
  }

  try {
    // 1. 获取配置信息
    report.configuration = debugApi.checkConfig()

    // 2. 测试连接性
    report.connectivity = await debugApi.testBackend()

    // 3. 运行诊断
    report.diagnosis = await debugApi.diagnose('/enterprises/stats')

    // 4. 生成建议
    if (report.diagnosis.issues.length > 0) {
      report.recommendations.push('发现API问题，建议按照诊断结果进行修复')
    }

    if (!report.configuration.token) {
      report.recommendations.push('建议重新登录获取有效的认证令牌')
    }

    const failedConnections = report.connectivity.filter(c => !c.success)
    if (failedConnections.length > 0) {
      report.recommendations.push('部分API端点连接失败，建议检查后端服务状态')
    }

    console.log('📊 API问题报告生成完成')
    return report
  } catch (error) {
    report.error = error.message
    console.error('报告生成失败:', error)
    return report
  }
}

/**
 * 导出修复工具集合
 */
export const apiFix = {
  config: fixApiConfiguration,
  stats: fixEnterpriseStatsApi,
  quickFix: quickFixAll,
  reset: resetApiConfiguration,
  report: generateApiReport
}

// 在开发环境中将修复工具挂载到window对象
if (process.env.NODE_ENV === 'development') {
  window.apiFix = apiFix
  
  // 提供快捷命令
  window.fixApi = quickFixAll
  window.resetApi = resetApiConfiguration
  
  console.log('🛠️ API修复工具已加载到window对象')
  console.log('可用命令: window.fixApi(), window.resetApi(), window.apiFix')
}
