import moment from 'moment'
import 'moment/locale/zh-cn'

// 配置moment
moment.locale('zh-cn')

/**
 * 格式化日期
 * @param {string|Date} date - 日期
 * @param {string} format - 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate (date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '-'
  return moment(date).format(format)
}

/**
 * 格式化日期时间 (别名函数，与formatDate功能相同)
 * @param {string|Date} date - 日期
 * @param {string} format - 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDateTime (date, format = 'YYYY-MM-DD HH:mm:ss') {
  return formatDate(date, format)
}

/**
 * 格式化日期为简短格式
 * @param {string|Date} date - 日期
 * @returns {string} 格式化后的日期字符串
 */
export function formatDateShort (date) {
  return formatDate(date, 'YYYY-MM-DD')
}

/**
 * 格式化时间为相对时间
 * @param {string|Date} date - 日期
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime (date) {
  if (!date) return '-'
  return moment(date).fromNow()
}

/**
 * 格式化日期时间为友好格式
 * @param {string|Date} date - 日期
 * @returns {string} 友好格式的日期时间字符串
 */
export function formatFriendlyDateTime (date) {
  if (!date) return '-'

  const now = moment()
  const target = moment(date)
  const diffDays = now.diff(target, 'days')

  if (diffDays === 0) {
    // 今天
    return `今天 ${target.format('HH:mm')}`
  } else if (diffDays === 1) {
    // 昨天
    return `昨天 ${target.format('HH:mm')}`
  } else if (diffDays < 7) {
    // 一周内
    return target.format('dddd HH:mm')
  } else if (now.year() === target.year()) {
    // 今年
    return target.format('MM-DD HH:mm')
  } else {
    // 其他年份
    return target.format('YYYY-MM-DD HH:mm')
  }
}

/**
 * 判断日期是否为今天
 * @param {string|Date} date - 日期
 * @returns {boolean} 是否为今天
 */
export function isToday (date) {
  if (!date) return false
  return moment(date).isSame(moment(), 'day')
}

/**
 * 判断日期是否为昨天
 * @param {string|Date} date - 日期
 * @returns {boolean} 是否为昨天
 */
export function isYesterday (date) {
  if (!date) return false
  return moment(date).isSame(moment().subtract(1, 'day'), 'day')
}

/**
 * 获取日期范围的描述文本
 * @param {string|Date} startDate - 开始日期
 * @param {string|Date} endDate - 结束日期
 * @returns {string} 日期范围描述
 */
export function getDateRangeText (startDate, endDate) {
  if (!startDate && !endDate) return '全部时间'
  if (!startDate) return `截止到 ${formatDateShort(endDate)}`
  if (!endDate) return `从 ${formatDateShort(startDate)} 开始`

  const start = moment(startDate)
  const end = moment(endDate)

  if (start.isSame(end, 'day')) {
    return formatDateShort(startDate)
  }

  return `${formatDateShort(startDate)} 至 ${formatDateShort(endDate)}`
}

/**
 * 计算两个日期之间的天数差
 * @param {string|Date} startDate - 开始日期
 * @param {string|Date} endDate - 结束日期
 * @returns {number} 天数差
 */
export function getDaysDiff (startDate, endDate) {
  if (!startDate || !endDate) return 0
  return moment(endDate).diff(moment(startDate), 'days')
}

/**
 * 获取当前时间戳
 * @returns {number} 时间戳
 */
export function getCurrentTimestamp () {
  return moment().valueOf()
}

/**
 * 将时间戳转换为日期对象
 * @param {number} timestamp - 时间戳
 * @returns {Date} 日期对象
 */
export function timestampToDate (timestamp) {
  return moment(timestamp).toDate()
}

/**
 * 获取日期的开始时间（00:00:00）
 * @param {string|Date} date - 日期
 * @returns {Date} 开始时间
 */
export function getStartOfDay (date) {
  return moment(date).startOf('day').toDate()
}

/**
 * 获取日期的结束时间（23:59:59）
 * @param {string|Date} date - 日期
 * @returns {Date} 结束时间
 */
export function getEndOfDay (date) {
  return moment(date).endOf('day').toDate()
}

/**
 * 验证日期格式是否正确
 * @param {string} dateString - 日期字符串
 * @param {string} format - 期望的格式
 * @returns {boolean} 是否有效
 */
export function isValidDate (dateString, format = 'YYYY-MM-DD') {
  return moment(dateString, format, true).isValid()
}

/**
 * 获取月份的天数
 * @param {string|Date} date - 日期
 * @returns {number} 天数
 */
export function getDaysInMonth (date) {
  return moment(date).daysInMonth()
}

/**
 * 获取星期几的文本
 * @param {string|Date} date - 日期
 * @returns {string} 星期几
 */
export function getWeekdayText (date) {
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  return weekdays[moment(date).day()]
}

// 导出moment实例，供其他地方使用
export { moment }

// 默认导出
export default {
  formatDate,
  formatDateShort,
  formatRelativeTime,
  formatFriendlyDateTime,
  isToday,
  isYesterday,
  getDateRangeText,
  getDaysDiff,
  getCurrentTimestamp,
  timestampToDate,
  getStartOfDay,
  getEndOfDay,
  isValidDate,
  getDaysInMonth,
  getWeekdayText,
  moment
}
