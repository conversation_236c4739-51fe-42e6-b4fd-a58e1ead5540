/**
 * 企业ID验证和处理工具
 * 用于解决企业ID为undefined的问题
 */

import { computed } from 'vue'

/**
 * 验证企业ID是否有效
 * @param {string} id - 企业ID
 * @returns {boolean} 是否有效
 */
export function isValidEnterpriseId (id) {
  return id && id !== 'undefined' && id !== 'null' && id.trim() !== ''
}

/**
 * 从路由参数中安全获取企业ID
 * @param {Object} route - Vue Router路由对象
 * @param {string} paramName - 参数名称，默认为'id'
 * @returns {string|null} 企业ID或null
 */
export function getEnterpriseIdFromRoute (route, paramName = 'id') {
  const id = route.params[paramName]
  return isValidEnterpriseId(id) ? id : null
}

/**
 * 从查询参数中安全获取企业ID
 * @param {Object} route - Vue Router路由对象
 * @param {string} queryName - 查询参数名称，默认为'enterpriseId'
 * @returns {string|null} 企业ID或null
 */
export function getEnterpriseIdFromQuery (route, queryName = 'enterpriseId') {
  const id = route.query[queryName]
  return isValidEnterpriseId(id) ? id : null
}

/**
 * 企业ID错误处理
 * @param {Object} router - Vue Router实例
 * @param {Function} message - 消息提示函数
 */
export function handleInvalidEnterpriseId (router, message) {
  message.error('企业ID无效，请重新选择企业')
  router.push('/enterprise/list')
}

/**
 * 创建企业ID验证的组合式函数
 * @param {Object} route - Vue Router路由对象
 * @param {Object} router - Vue Router实例
 * @param {Function} message - 消息提示函数
 * @returns {Object} 包含企业ID和验证方法的对象
 */
export function useEnterpriseId (route, router, message) {
  const enterpriseId = computed(() => {
    return getEnterpriseIdFromRoute(route) || getEnterpriseIdFromQuery(route)
  })

  const validateEnterpriseId = () => {
    if (!enterpriseId.value) {
      handleInvalidEnterpriseId(router, message)
      return false
    }
    return true
  }

  const validateAndExecute = async (callback) => {
    if (!validateEnterpriseId()) {
      return false
    }
    try {
      await callback(enterpriseId.value)
      return true
    } catch (error) {
      // 记录错误信息用于调试
      if (process.env.NODE_ENV === 'development') {
        console.error('执行回调函数失败:', error)
      }
      message.error('操作失败，请稍后重试')
      return false
    }
  }

  return {
    enterpriseId,
    validateEnterpriseId,
    validateAndExecute,
    isValid: computed(() => !!enterpriseId.value)
  }
}

/**
 * 企业ID中间件 - 用于路由守卫
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 * @param {Function} next - 路由继续函数
 */
export function enterpriseIdGuard (to, from, next) {
  const enterpriseId = getEnterpriseIdFromRoute(to) || getEnterpriseIdFromQuery(to)

  if (!enterpriseId) {
    // 开发环境下记录警告信息
    if (process.env.NODE_ENV === 'development') {
      console.warn('企业ID无效，重定向到企业列表页')
    }
    next('/enterprise/list')
    return
  }

  next()
}
