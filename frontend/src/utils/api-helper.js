/**
 * API响应处理工具函数
 * 根据API_DATA_STANDARDS.md规范提供统一的API响应处理
 */

import { message } from 'ant-design-vue'

/**
 * 统一的API响应处理函数
 * @param {Object} response - API响应对象
 * @returns {any} - 处理后的数据
 * @throws {Error} - 当响应包含错误时抛出异常
 */
export function handleApiResponse (response) {
  if (!response) {
    throw new Error('响应数据为空')
  }

  // 检查响应格式
  if (typeof response !== 'object') {
    throw new Error('响应格式错误')
  }

  // 处理错误响应
  if (response.code && response.code >= 400) {
    const errorMessage = response.message || response.error || '请求失败'
    const error = new Error(errorMessage)
    error.code = response.code
    error.response = response
    throw error
  }

  // 检查业务状态码
  if (response.code === 200) {
    return response.data
  } else if (response.code === undefined && response.data !== undefined) {
    // 兼容没有code字段但有data字段的响应
    return response.data
  } else {
    const errorMessage = response.message || response.error || '请求失败'
    const error = new Error(errorMessage)
    error.code = response.code
    error.response = response
    throw error
  }
}

/**
 * 处理分页数据的统一函数
 * @param {Object} response - API响应对象
 * @returns {Object} - 标准化的分页数据
 */
export function handlePaginatedResponse (response) {
  const data = handleApiResponse(response)
  
  if (!data || typeof data !== 'object') {
    throw new Error('分页数据格式错误')
  }

  // 确保使用标准的分页字段
  return {
    items: data.items || [],
    total: data.total || 0,
    page: data.page || 1,
    pageSize: data.pageSize || 10,
    pages: data.pages || Math.ceil((data.total || 0) / (data.pageSize || 10))
  }
}

/**
 * 处理列表数据的统一函数
 * @param {Object} response - API响应对象
 * @returns {Array} - 列表数据
 */
export function handleListResponse (response) {
  const data = handleApiResponse(response)
  
  if (data && data.items && Array.isArray(data.items)) {
    return data.items
  }
  
  if (Array.isArray(data)) {
    return data
  }
  
  throw new Error('列表数据格式错误')
}

/**
 * 处理单个实体数据的统一函数
 * @param {Object} response - API响应对象
 * @returns {Object} - 实体数据
 */
export function handleEntityResponse (response) {
  const data = handleApiResponse(response)
  
  if (data && typeof data === 'object' && !Array.isArray(data)) {
    return data
  }
  
  throw new Error('实体数据格式错误')
}

/**
 * 处理操作结果的统一函数
 * @param {Object} response - API响应对象
 * @param {string} successMessage - 成功消息
 * @param {boolean} showMessage - 是否显示消息
 * @returns {boolean} - 操作是否成功
 */
export function handleOperationResponse (response, successMessage = '操作成功', showMessage = true) {
  try {
    handleApiResponse(response)
    
    if (showMessage) {
      message.success(successMessage)
    }
    
    return true
  } catch (error) {
    if (showMessage) {
      message.error(error.message)
    }
    return false
  }
}

/**
 * 安全的API调用包装器
 * @param {Function} apiFunction - API函数
 * @param {Function} responseHandler - 响应处理函数
 * @param {Object} options - 选项
 * @returns {Function} - 包装后的API函数
 */
export function createSafeApiCall (apiFunction, responseHandler = handleApiResponse, options = {}) {
  const {
    showError = true,
    showSuccess = false,
    successMessage = '操作成功',
    errorMessage = '操作失败'
  } = options

  return async (...args) => {
    try {
      const response = await apiFunction(...args)
      const result = responseHandler(response)
      
      if (showSuccess) {
        message.success(successMessage)
      }
      
      return result
    } catch (error) {
      const finalErrorMessage = error.message || errorMessage
      
      if (showError) {
        message.error(finalErrorMessage)
      }
      
      throw new Error(finalErrorMessage)
    }
  }
}

/**
 * 创建分页API调用包装器
 * @param {Function} apiFunction - 分页API函数
 * @param {Object} options - 选项
 * @returns {Function} - 包装后的分页API函数
 */
export function createPaginatedApiCall (apiFunction, options = {}) {
  return createSafeApiCall(apiFunction, handlePaginatedResponse, {
    showError: true,
    showSuccess: false,
    ...options
  })
}

/**
 * 创建列表API调用包装器
 * @param {Function} apiFunction - 列表API函数
 * @param {Object} options - 选项
 * @returns {Function} - 包装后的列表API函数
 */
export function createListApiCall (apiFunction, options = {}) {
  return createSafeApiCall(apiFunction, handleListResponse, {
    showError: true,
    showSuccess: false,
    ...options
  })
}

/**
 * 创建实体API调用包装器
 * @param {Function} apiFunction - 实体API函数
 * @param {Object} options - 选项
 * @returns {Function} - 包装后的实体API函数
 */
export function createEntityApiCall (apiFunction, options = {}) {
  return createSafeApiCall(apiFunction, handleEntityResponse, {
    showError: true,
    showSuccess: false,
    ...options
  })
}

/**
 * 创建操作API调用包装器
 * @param {Function} apiFunction - 操作API函数
 * @param {Object} options - 选项
 * @returns {Function} - 包装后的操作API函数
 */
export function createOperationApiCall (apiFunction, options = {}) {
  const {
    successMessage = '操作成功',
    showMessage = true,
    ...restOptions
  } = options

  return createSafeApiCall(
    apiFunction,
    (response) => handleOperationResponse(response, successMessage, showMessage),
    {
      showError: showMessage,
      showSuccess: false,
      ...restOptions
    }
  )
}

/**
 * 批量处理API响应的工具函数
 * @param {Array} responses - API响应数组
 * @param {Function} handler - 处理函数
 * @returns {Array} - 处理后的数据数组
 */
export function handleBatchResponses (responses, handler = handleApiResponse) {
  if (!Array.isArray(responses)) {
    throw new Error('批量响应数据必须是数组')
  }

  return responses.map((response, index) => {
    try {
      return handler(response)
    } catch (error) {
      console.error(`批量处理第${index + 1}个响应失败:`, error)
      throw error
    }
  })
}

/**
 * 验证API响应数据的工具函数
 * @param {Object} data - 要验证的数据
 * @param {Object} schema - 验证模式
 * @returns {boolean} - 验证是否通过
 */
export function validateResponseData (data, schema) {
  if (!data || !schema) {
    return false
  }

  // 简单的字段验证
  for (const [field, rules] of Object.entries(schema)) {
    const value = data[field]
    
    if (rules.required && (value === undefined || value === null)) {
      console.error(`字段 ${field} 是必需的`)
      return false
    }
    
    if (rules.type && value !== undefined && typeof value !== rules.type) {
      console.error(`字段 ${field} 类型错误，期望 ${rules.type}，实际 ${typeof value}`)
      return false
    }
  }

  return true
}

/**
 * 格式化错误信息的工具函数
 * @param {Error|string} error - 错误对象或错误消息
 * @returns {string} - 格式化后的错误消息
 */
export function formatErrorMessage (error) {
  if (typeof error === 'string') {
    return error
  }
  
  if (error && error.message) {
    return error.message
  }
  
  if (error && error.response && error.response.data) {
    const data = error.response.data
    return data.message || data.error || '请求失败'
  }
  
  return '未知错误'
}
