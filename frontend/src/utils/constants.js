/**
 * @file 前端常量定义
 * @description 定义前端应用中使用的所有常量，包括API端点、状态值、配置项等
 */

// ========== API相关常量 ==========

// API基础配置
export const API_CONFIG = {
  BASE_URL: process.env.VUE_APP_API_BASE_URL || '/api',
  TIMEOUT: 30000,
  RETRY_TIMES: 3
}

// API端点
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH_TOKEN: '/auth/refresh-token',
    PROFILE: '/auth/profile',
    CHANGE_PASSWORD: '/auth/change-password'
  },

  // 用户相关
  USER: {
    LIST: '/users',
    DETAIL: '/users/:id',
    UPDATE: '/users/:id',
    DELETE: '/users/:id'
  },

  // 企业相关
  ENTERPRISE: {
    LIST: '/enterprises',
    DETAIL: '/enterprises/:id',
    CREATE: '/enterprises',
    UPDATE: '/enterprises/:id',
    DELETE: '/enterprises/:id',
    STATS: '/enterprises/stats', // 全局企业统计
    DETAIL_STATS: '/enterprises/:id/stats', // 单个企业统计
    STATISTICS: '/enterprises/:id/statistics' // 保留原有的，避免破坏现有代码
  },

  // 发票相关
  INVOICE: {
    LIST: '/invoices',
    DETAIL: '/invoices/:id',
    CREATE: '/invoices',
    UPDATE: '/invoices/:id',
    DELETE: '/invoices/:id',
    UPLOAD: '/invoices/upload',
    SCAN: '/invoices/scan',
    IMPORT: '/invoices/import'
  },

  // 申报相关
  DECLARATION: {
    LIST: '/declarations',
    DETAIL: '/declarations/:id',
    CREATE: '/declarations',
    UPDATE: '/declarations/:id',
    DELETE: '/declarations/:id',
    SUBMIT: '/declarations/:id/submit',
    PREVIEW: '/declarations/:id/preview'
  }
}

// ========== 状态常量 ==========

// 用户状态
export const USER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  SUSPENDED: 'suspended'
}

// 企业状态
export const ENTERPRISE_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended',
  CANCELLED: 'cancelled'
}

// 发票状态
export const INVOICE_STATUS = {
  DRAFT: 'draft',
  ISSUED: 'issued',
  CANCELLED: 'cancelled',
  INVALID: 'invalid',
  RED_FLUSHED: 'red_flushed'
}

// 发票认证状态
export const INVOICE_AUTH_STATUS = {
  PENDING: 'pending',
  AUTHENTICATED: 'authenticated',
  FAILED: 'failed',
  EXPIRED: 'expired'
}

// 申报状态
export const DECLARATION_STATUS = {
  DRAFT: 'draft',
  SUBMITTED: 'submitted',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  PAID: 'paid',
  OVERDUE: 'overdue'
}

// ========== UI相关常量 ==========

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: ['10', '20', '50', '100'],
  SHOW_SIZE_CHANGER: true,
  SHOW_QUICK_JUMPER: true,
  SHOW_TOTAL: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}

// 表格配置
export const TABLE_CONFIG = {
  SCROLL_Y: 400,
  ROW_KEY: 'id',
  SIZE: 'middle'
}

// 表单配置
export const FORM_CONFIG = {
  LAYOUT: 'vertical',
  LABEL_COL: { span: 24 },
  WRAPPER_COL: { span: 24 }
}

// 文件上传配置
export const UPLOAD_CONFIG = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_COUNT: 10,
  ACCEPT: {
    IMAGE: '.jpg,.jpeg,.png,.gif,.bmp,.webp',
    DOCUMENT: '.pdf,.doc,.docx,.xls,.xlsx,.txt',
    INVOICE: '.pdf,.xml,.ofd'
  }
}

// ========== 业务常量 ==========

// 税种类型
export const TAX_TYPES = {
  VAT: 'VAT', // 增值税
  EIT: 'EIT', // 企业所得税
  IIT: 'IIT', // 个人所得税
  CIT: 'CIT', // 消费税
  BT: 'BT', // 营业税
  CT: 'CT', // 城建税
  EET: 'EET', // 教育费附加
  LET: 'LET', // 地方教育费附加
  PIT: 'PIT', // 印花税
  VT: 'VT', // 车辆税
  PT: 'PT', // 房产税
  LUT: 'LUT' // 土地使用税
}

// 纳税人类型
export const TAXPAYER_TYPES = {
  GENERAL: 'general', // 一般纳税人
  SMALL: 'small' // 小规模纳税人
}

// 发票类型
export const INVOICE_TYPES = {
  SPECIAL: 'special', // 专用发票
  ORDINARY: 'ordinary', // 普通发票
  ELECTRONIC: 'electronic', // 电子发票
  ROLL: 'roll' // 卷式发票
}

// 申报周期
export const DECLARATION_PERIODS = {
  MONTHLY: 'monthly', // 月度
  QUARTERLY: 'quarterly', // 季度
  ANNUALLY: 'annually', // 年度
  OTHER: 'other' // 其他
}

// ========== 系统配置常量 ==========

// 主题配置
export const THEME_CONFIG = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
}

// 语言配置
export const LOCALE_CONFIG = {
  ZH_CN: 'zh-CN',
  EN_US: 'en-US'
}

// 尺寸配置
export const SIZE_CONFIG = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large'
}

// 设备类型
export const DEVICE_TYPES = {
  DESKTOP: 'desktop',
  TABLET: 'tablet',
  MOBILE: 'mobile'
}

// ========== 存储键名常量 ==========

export const STORAGE_KEYS = {
  TOKEN: 'token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  THEME: 'theme',
  LOCALE: 'locale',
  SIZE: 'size',
  SIDEBAR_COLLAPSED: 'sidebarCollapsed'
}

// ========== 路由名称常量 ==========

export const ROUTE_NAMES = {
  // 认证
  LOGIN: 'login',
  REGISTER: 'register',

  // 主要页面
  DASHBOARD: 'dashboard',
  USER_PROFILE: 'user-profile',
  SETTINGS: 'settings',

  // 企业管理
  ENTERPRISE_LIST: 'enterprise-list',
  ENTERPRISE_DETAIL: 'enterprise-detail',
  ENTERPRISE_CREATE: 'enterprise-create',
  ENTERPRISE_EDIT: 'enterprise-edit',

  // 发票管理
  INVOICE_LIST: 'invoice-list',
  INVOICE_DETAIL: 'invoice-detail',
  INVOICE_UPLOAD: 'invoice-upload',
  INVOICE_SCAN: 'invoice-scan',

  // 申报管理
  DECLARATION_LIST: 'declaration-list',
  DECLARATION_DETAIL: 'declaration-detail',
  DECLARATION_CREATE: 'declaration-create',
  DECLARATION_EDIT: 'declaration-edit'
}

// ========== 消息常量 ==========

export const MESSAGES = {
  // 成功消息
  SUCCESS: {
    LOGIN: '登录成功',
    LOGOUT: '退出成功',
    SAVE: '保存成功',
    DELETE: '删除成功',
    UPDATE: '更新成功',
    CREATE: '创建成功',
    UPLOAD: '上传成功',
    SUBMIT: '提交成功'
  },

  // 错误消息
  ERROR: {
    LOGIN_FAILED: '登录失败',
    NETWORK_ERROR: '网络连接失败',
    SERVER_ERROR: '服务器错误',
    PERMISSION_DENIED: '权限不足',
    FILE_TOO_LARGE: '文件过大',
    INVALID_FILE_TYPE: '文件类型不支持'
  },

  // 确认消息
  CONFIRM: {
    DELETE: '确定要删除吗？',
    LOGOUT: '确定要退出登录吗？',
    SUBMIT: '确定要提交吗？',
    CANCEL: '确定要取消吗？'
  }
}

// ========== 正则表达式常量 ==========

export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^1[3-9]\d{9}$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  TAX_CODE: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/, // 统一社会信用代码
  INVOICE_CODE: /^\d{12}$/, // 发票代码
  INVOICE_NUMBER: /^\d{8}$/ // 发票号码
}

// ========== 默认值常量 ==========

export const DEFAULT_VALUES = {
  AVATAR: '/images/default-avatar.png',
  LOGO: '/images/logo.png',
  EMPTY_TEXT: '暂无数据',
  LOADING_TEXT: '加载中...',
  PAGE_SIZE: 10,
  TIMEOUT: 30000
}

// ========== 头像处理工具函数 ==========

/**
 * 获取完整的头像URL
 * @param {string} avatarPath - 头像路径
 * @returns {string} 完整的头像URL
 */
export function getAvatarUrl (avatarPath) {
  if (!avatarPath) {
    return DEFAULT_VALUES.AVATAR
  }

  // 如果已经是完整URL，直接返回
  if (avatarPath.startsWith('http://') || avatarPath.startsWith('https://')) {
    return avatarPath
  }

  // 如果是uploads路径，直接返回（会被Vue代理到后端）
  if (avatarPath.startsWith('/uploads/')) {
    return avatarPath
  }

  // 其他情况返回默认头像
  return DEFAULT_VALUES.AVATAR
}
