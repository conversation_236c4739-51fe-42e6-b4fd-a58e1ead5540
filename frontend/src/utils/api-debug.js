/**
 * API调试工具
 * 用于诊断和修复API请求问题
 */

import axios from 'axios'
import { API_CONFIG } from '@/utils/constants'

/**
 * 测试API连接性
 * @param {string} endpoint - API端点
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} - 测试结果
 */
export async function testApiConnection(endpoint = '/enterprises/stats', options = {}) {
  const {
    method = 'GET',
    headers = {},
    data = null,
    timeout = 10000
  } = options

  const baseURL = process.env.VUE_APP_API_BASE_URL || API_CONFIG.BASE_URL || '/api'
  const fullUrl = `${window.location.origin}${baseURL}${endpoint}`

  console.group('🔍 API连接测试')
  console.log('测试端点:', endpoint)
  console.log('完整URL:', fullUrl)
  console.log('请求方法:', method)
  console.log('请求头:', headers)

  const result = {
    success: false,
    status: null,
    data: null,
    error: null,
    timing: {
      start: Date.now(),
      end: null,
      duration: null
    },
    request: {
      url: fullUrl,
      method,
      headers,
      data
    }
  }

  try {
    const response = await axios({
      url: fullUrl,
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      data,
      timeout,
      validateStatus: () => true // 接受所有状态码
    })

    result.timing.end = Date.now()
    result.timing.duration = result.timing.end - result.timing.start
    result.status = response.status
    result.data = response.data
    result.success = response.status >= 200 && response.status < 300

    console.log('✅ 请求完成')
    console.log('状态码:', response.status)
    console.log('响应时间:', result.timing.duration + 'ms')
    console.log('响应数据:', response.data)

  } catch (error) {
    result.timing.end = Date.now()
    result.timing.duration = result.timing.end - result.timing.start
    result.error = {
      message: error.message,
      code: error.code,
      response: error.response?.data,
      status: error.response?.status
    }

    console.log('❌ 请求失败')
    console.log('错误信息:', error.message)
    console.log('错误代码:', error.code)
    if (error.response) {
      console.log('响应状态:', error.response.status)
      console.log('响应数据:', error.response.data)
    }
  }

  console.groupEnd()
  return result
}

/**
 * 测试企业统计API
 * @param {string} token - 认证令牌
 * @returns {Promise<Object>} - 测试结果
 */
export async function testEnterpriseStatsApi(token) {
  const headers = {}
  if (token) {
    headers.Authorization = `Bearer ${token}`
  }

  return await testApiConnection('/enterprises/stats', {
    method: 'GET',
    headers
  })
}

/**
 * 检查API配置
 * @returns {Object} - 配置信息
 */
export function checkApiConfig() {
  const config = {
    environment: process.env.NODE_ENV,
    baseURL: process.env.VUE_APP_API_BASE_URL || API_CONFIG.BASE_URL,
    currentOrigin: window.location.origin,
    fullApiUrl: `${window.location.origin}${process.env.VUE_APP_API_BASE_URL || API_CONFIG.BASE_URL}`,
    timeout: API_CONFIG.TIMEOUT,
    token: localStorage.getItem('token'),
    refreshToken: localStorage.getItem('refresh_token')
  }

  console.group('⚙️ API配置检查')
  console.table(config)
  console.groupEnd()

  return config
}

/**
 * 测试后端服务连接
 * @returns {Promise<Object>} - 连接测试结果
 */
export async function testBackendConnection() {
  const tests = [
    { name: '健康检查', endpoint: '/health' },
    { name: '认证端点', endpoint: '/auth/login' },
    { name: '企业列表', endpoint: '/enterprises' },
    { name: '企业统计', endpoint: '/enterprises/stats' }
  ]

  console.group('🏥 后端服务连接测试')
  
  const results = []
  for (const test of tests) {
    console.log(`测试: ${test.name}`)
    try {
      const result = await testApiConnection(test.endpoint, {
        method: 'GET',
        timeout: 5000
      })
      results.push({
        name: test.name,
        endpoint: test.endpoint,
        success: result.success,
        status: result.status,
        duration: result.timing.duration,
        error: result.error?.message
      })
    } catch (error) {
      results.push({
        name: test.name,
        endpoint: test.endpoint,
        success: false,
        status: null,
        duration: null,
        error: error.message
      })
    }
  }

  console.table(results)
  console.groupEnd()

  return results
}

/**
 * 诊断API问题
 * @param {string} endpoint - 问题端点
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} - 诊断结果
 */
export async function diagnoseApiIssue(endpoint = '/enterprises/stats', options = {}) {
  console.group('🔧 API问题诊断')
  
  const diagnosis = {
    timestamp: new Date().toISOString(),
    endpoint,
    issues: [],
    suggestions: [],
    config: null,
    connectionTest: null,
    backendTest: null
  }

  // 1. 检查配置
  console.log('1. 检查API配置...')
  diagnosis.config = checkApiConfig()

  // 2. 测试连接
  console.log('2. 测试API连接...')
  diagnosis.connectionTest = await testApiConnection(endpoint, options)

  // 3. 测试后端服务
  console.log('3. 测试后端服务...')
  diagnosis.backendTest = await testBackendConnection()

  // 4. 分析问题
  console.log('4. 分析问题...')
  
  // 检查配置问题
  if (!diagnosis.config.baseURL) {
    diagnosis.issues.push('API基础URL未配置')
    diagnosis.suggestions.push('检查环境变量VUE_APP_API_BASE_URL或constants.js中的API_CONFIG.BASE_URL')
  }

  if (!diagnosis.config.token) {
    diagnosis.issues.push('未找到认证令牌')
    diagnosis.suggestions.push('请先登录获取认证令牌')
  }

  // 检查连接问题
  if (!diagnosis.connectionTest.success) {
    if (diagnosis.connectionTest.error?.code === 'ECONNREFUSED') {
      diagnosis.issues.push('后端服务未启动或端口错误')
      diagnosis.suggestions.push('检查后端服务是否在正确端口运行')
    } else if (diagnosis.connectionTest.status === 404) {
      diagnosis.issues.push('API端点不存在')
      diagnosis.suggestions.push('检查API路由配置是否正确')
    } else if (diagnosis.connectionTest.status === 401) {
      diagnosis.issues.push('认证失败')
      diagnosis.suggestions.push('检查认证令牌是否有效')
    } else if (diagnosis.connectionTest.status === 500) {
      diagnosis.issues.push('服务器内部错误')
      diagnosis.suggestions.push('检查后端日志和数据库连接')
    }
  }

  // 检查后端服务状态
  const failedTests = diagnosis.backendTest.filter(test => !test.success)
  if (failedTests.length > 0) {
    diagnosis.issues.push(`${failedTests.length}个后端端点测试失败`)
    diagnosis.suggestions.push('检查后端服务配置和路由')
  }

  console.log('📋 诊断结果:')
  console.log('问题:', diagnosis.issues)
  console.log('建议:', diagnosis.suggestions)
  console.groupEnd()

  return diagnosis
}

/**
 * 修复常见API问题
 * @param {Object} diagnosis - 诊断结果
 * @returns {Object} - 修复结果
 */
export function fixCommonApiIssues(diagnosis) {
  console.group('🔨 尝试修复API问题')
  
  const fixes = {
    applied: [],
    failed: [],
    suggestions: []
  }

  // 修复1: 检查并设置正确的baseURL
  if (diagnosis.issues.includes('API基础URL未配置')) {
    try {
      // 尝试设置默认的baseURL
      if (window.location.port === '8080') {
        // 前端在8080端口，后端可能在8081端口
        const newBaseURL = `${window.location.protocol}//${window.location.hostname}:8081/api`
        console.log('尝试设置baseURL为:', newBaseURL)
        fixes.applied.push(`设置baseURL为${newBaseURL}`)
        fixes.suggestions.push(`在.env文件中添加: VUE_APP_API_BASE_URL=${newBaseURL}`)
      }
    } catch (error) {
      fixes.failed.push('无法自动设置baseURL: ' + error.message)
    }
  }

  // 修复2: 检查认证令牌
  if (diagnosis.issues.includes('未找到认证令牌')) {
    fixes.suggestions.push('请重新登录以获取有效的认证令牌')
  }

  // 修复3: 端口问题
  if (diagnosis.issues.includes('后端服务未启动或端口错误')) {
    fixes.suggestions.push('检查后端服务是否在8081端口运行')
    fixes.suggestions.push('运行命令: curl http://localhost:8081/api/health')
  }

  console.log('✅ 已应用的修复:', fixes.applied)
  console.log('❌ 修复失败:', fixes.failed)
  console.log('💡 建议:', fixes.suggestions)
  console.groupEnd()

  return fixes
}

/**
 * 一键诊断和修复
 * @param {string} endpoint - 问题端点
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} - 完整的诊断和修复结果
 */
export async function quickDiagnoseAndFix(endpoint = '/enterprises/stats', options = {}) {
  console.log('🚀 开始一键诊断和修复...')
  
  const diagnosis = await diagnoseApiIssue(endpoint, options)
  const fixes = fixCommonApiIssues(diagnosis)
  
  return {
    diagnosis,
    fixes,
    summary: {
      issuesFound: diagnosis.issues.length,
      fixesApplied: fixes.applied.length,
      suggestions: fixes.suggestions.length
    }
  }
}

// 导出便捷的调试函数
export const debugApi = {
  test: testApiConnection,
  testStats: testEnterpriseStatsApi,
  checkConfig: checkApiConfig,
  testBackend: testBackendConnection,
  diagnose: diagnoseApiIssue,
  fix: fixCommonApiIssues,
  quickFix: quickDiagnoseAndFix
}

// 在开发环境中将调试工具挂载到window对象
if (process.env.NODE_ENV === 'development') {
  window.debugApi = debugApi
}
