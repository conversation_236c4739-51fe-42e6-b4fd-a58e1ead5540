import axios from 'axios'
import { message, notification } from 'ant-design-vue'
import router from '@/router'
import { refreshToken } from '@/api/auth'
import { API_CONFIG, STORAGE_KEYS } from '@/utils/constants'
import { handleError, ERROR_TYPES, ERROR_LEVELS } from '@/utils/error-handler'

/**
 * 验证API响应是否符合标准格式
 * 根据API_DATA_STANDARDS.md规范验证响应结构
 * @param {Object} response - API响应对象
 * @returns {boolean} - 是否符合标准格式
 */
function isValidApiResponse (response) {
  if (!response || typeof response !== 'object') {
    return false
  }

  // 检查必需字段
  const requiredFields = ['code', 'message', 'data', 'timestamp', 'requestId']
  return requiredFields.every(field => response.hasOwnProperty(field))
}

/**
 * 将非标准响应转换为标准格式
 * @param {Object} response - 原始响应
 * @returns {Object} - 标准化后的响应
 */
function normalizeApiResponse (response) {
  // 如果已经是标准格式，直接返回
  if (isValidApiResponse(response)) {
    return response
  }

  // 处理错误响应格式
  if (response.code && response.code >= 400) {
    return {
      code: response.code,
      message: response.message || response.error || '请求失败',
      data: null,
      timestamp: response.timestamp || Date.now(),
      requestId: response.requestId || generateRequestId()
    }
  }

  // 尝试转换常见的非标准格式
  const normalized = {
    code: response.status || response.code || 200,
    message: response.message || response.msg || '操作成功',
    data: response.data || response.result || response,
    timestamp: response.timestamp || Date.now(),
    requestId: response.requestId || response.traceId || generateRequestId()
  }

  return normalized
}

/**
 * 生成请求ID
 * @returns {string} - 请求ID
 */
function generateRequestId () {
  return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

/**
 * 处理分页数据格式标准化
 * 确保分页数据使用items字段而不是data或list
 * @param {Object} data - 分页数据
 * @returns {Object} - 标准化后的分页数据
 */
function normalizePaginationData (data) {
  if (!data || typeof data !== 'object') {
    return data
  }

  // 如果已经有items字段，直接返回
  if (data.items) {
    return data
  }

  // 转换常见的分页格式
  if (data.data && Array.isArray(data.data)) {
    return {
      ...data,
      items: data.data,
      data: undefined // 移除原data字段避免混淆
    }
  }

  if (data.list && Array.isArray(data.list)) {
    return {
      ...data,
      items: data.list,
      list: undefined // 移除原list字段避免混淆
    }
  }

  // 如果data本身就是数组，包装为标准分页格式
  if (Array.isArray(data)) {
    return {
      items: data,
      total: data.length,
      page: 1,
      pageSize: data.length,
      pages: 1
    }
  }

  return data
}

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || getDefaultApiBaseURL(), // 从环境变量获取API基础URL
  timeout: API_CONFIG.TIMEOUT // 请求超时时间
})

/**
 * 获取默认的API基础URL
 * 根据当前环境和端口自动推断后端API地址
 */
function getDefaultApiBaseURL() {
  // 如果有明确配置，使用配置的值
  if (API_CONFIG.BASE_URL && API_CONFIG.BASE_URL !== '/api') {
    return API_CONFIG.BASE_URL
  }

  // 根据当前端口推断后端端口
  const currentPort = window.location.port
  const protocol = window.location.protocol
  const hostname = window.location.hostname

  // 如果前端在8080、8082端口，后端通常在8081端口
  if (currentPort === '8080' || currentPort === '8082') {
    return `${protocol}//${hostname}:8081/api`
  }

  // 如果前端在3000端口（开发环境），后端通常在8081端口
  if (currentPort === '3000') {
    return `${protocol}//${hostname}:8081/api`
  }

  // 生产环境或其他情况，使用相对路径
  return '/api'
}

// 是否正在刷新token
let isRefreshing = false
// 重试队列，每一项将是一个待执行的函数形式
let retryRequests = []

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从本地存储获取token，并添加到请求头中
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN)
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 针对不同请求方法处理数据
    if (config.method?.toUpperCase() === 'GET') {
      // 移除GET请求中的空参数
      if (config.params) {
        Object.keys(config.params).forEach(key => {
          if (config.params[key] === '' || config.params[key] === null || config.params[key] === undefined) {
            delete config.params[key]
          }
        })
      }
    }

    return config
  },
  error => {
    // 开发环境下记录错误信息
    if (process.env.NODE_ENV === 'development') {
      console.error('请求拦截器错误:', error)
    }
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 如果返回的是文件流，直接返回
    if (response.config.responseType === 'blob') {
      return response
    }

    // 正常响应直接返回数据
    let res = response.data

    // 验证响应格式是否符合API_DATA_STANDARDS.md规范
    if (!isValidApiResponse(res)) {
      console.warn('API响应格式不符合标准:', res)
      // 对于不符合标准的响应，尝试转换为标准格式
      res = normalizeApiResponse(res)
    }

    // 如果接口返回了错误码和错误信息
    if (res.code && res.code >= 400) {
      message.error(res.message || '请求失败')

      // 特定错误码处理 - 但不对登录接口进行token刷新
      if (res.code === 401 && !response.config.url.includes('/auth/login')) {
        // 尝试刷新token
        return handleTokenRefresh(response)
      }

      return Promise.reject(new Error(res.message || '请求失败'))
    }

    // 标准化分页数据格式
    if (res.data && typeof res.data === 'object') {
      res.data = normalizePaginationData(res.data)
    }

    return res
  },
  error => {
    // 特殊处理：对于401错误且不是登录接口，尝试刷新token
    if (error.response?.status === 401 && !error.config?.url?.includes('/auth/login')) {
      return handleTokenRefresh(error.response)
    }

    // 使用统一的错误处理机制
    const errorInfo = handleError(error, {
      showMessage: true,
      showNotification: false,
      logError: true,
      rethrow: false
    }, {
      source: 'axios-interceptor',
      url: error.config?.url,
      method: error.config?.method
    })

    // 对于特定的错误类型，执行额外的处理
    if (errorInfo.type === ERROR_TYPES.PERMISSION && errorInfo.code === 401) {
      // 清除本地存储的认证信息
      localStorage.removeItem(STORAGE_KEYS.TOKEN)
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
      localStorage.removeItem(STORAGE_KEYS.USER_INFO)
      // 跳转到登录页
      router.push('/login')
    }

    return Promise.reject(error)
  }
)

// 处理token刷新
async function handleTokenRefresh (response) {
  const config = response.config

  if (!isRefreshing) {
    isRefreshing = true

    try {
      // 尝试刷新token - 修复参数格式
      const refreshTokenValue = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN)
      if (!refreshTokenValue) {
        throw new Error('没有刷新令牌')
      }
      const refreshResponse = await refreshToken(refreshTokenValue)

      if (refreshResponse && refreshResponse.code === 200 && refreshResponse.data && refreshResponse.data.accessToken) {
        // 更新token
        const newToken = refreshResponse.data.accessToken
        const newRefreshToken = refreshResponse.data.refreshToken
        localStorage.setItem(STORAGE_KEYS.TOKEN, newToken)
        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, newRefreshToken)

        // 更新当前请求的Authorization头
        config.headers.Authorization = `Bearer ${newToken}`

        // 执行队列中的所有请求
        retryRequests.forEach(cb => cb(newToken))
        retryRequests = []

        // 重试当前请求
        return service(config)
      } else {
        // 刷新失败，清除token并跳转到登录页
        localStorage.removeItem(STORAGE_KEYS.TOKEN)
        localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
        router.push('/login')
        return Promise.reject(new Error('登录已过期，请重新登录'))
      }
    } catch (err) {
      // 刷新失败，清除token并跳转到登录页
      localStorage.removeItem(STORAGE_KEYS.TOKEN)
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
      router.push('/login')
      return Promise.reject(err)
    } finally {
      isRefreshing = false
    }
  } else {
    // 将请求加入队列
    return new Promise(resolve => {
      retryRequests.push(token => {
        config.headers.Authorization = `Bearer ${token}`
        resolve(service(config))
      })
    })
  }
}

export default service
