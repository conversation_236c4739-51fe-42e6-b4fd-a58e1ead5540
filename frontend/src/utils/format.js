/**
 * 格式化工具函数
 */

/**
 * 格式化数字为货币格式
 * @param {number|string|null|undefined} num - 要格式化的数字
 * @param {object} options - 格式化选项
 * @returns {string} 格式化后的字符串
 */
export function formatNumber (num, options = {}) {
  const defaultOptions = {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    locale: 'zh-CN'
  }

  const finalOptions = { ...defaultOptions, ...options }

  // 处理空值和无效值
  if (num === null || num === undefined || num === '' || isNaN(num)) {
    return '0.00'
  }

  try {
    return Number(num).toLocaleString(finalOptions.locale, {
      minimumFractionDigits: finalOptions.minimumFractionDigits,
      maximumFractionDigits: finalOptions.maximumFractionDigits
    })
  } catch (error) {
    // 开发环境下记录警告信息
    if (process.env.NODE_ENV === 'development') {
      console.warn('格式化数字失败:', error)
    }
    return '0.00'
  }
}

/**
 * 格式化货币
 * @param {number|string|null|undefined} amount - 金额
 * @param {string} currency - 货币符号，默认为 ¥
 * @returns {string} 格式化后的货币字符串
 */
export function formatCurrency (amount, currency = '¥') {
  return `${currency}${formatNumber(amount)}`
}

/**
 * 格式化百分比
 * @param {number|string|null|undefined} value - 要格式化的值
 * @param {number} decimals - 小数位数，默认为2
 * @returns {string} 格式化后的百分比字符串
 */
export function formatPercentage (value, decimals = 2) {
  if (value === null || value === undefined || value === '' || isNaN(value)) {
    return '0.00%'
  }

  try {
    return `${Number(value).toFixed(decimals)}%`
  } catch (error) {
    console.warn('格式化百分比失败:', error)
    return '0.00%'
  }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize (bytes) {
  if (bytes === 0 || bytes === null || bytes === undefined) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化日期
 * @param {string|Date} date - 日期
 * @param {string} format - 格式，默认为 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate (date, format = 'YYYY-MM-DD') {
  if (!date) return ''

  try {
    const d = new Date(date)
    if (isNaN(d.getTime())) return ''

    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  } catch (error) {
    console.warn('格式化日期失败:', error)
    return ''
  }
}

/**
 * 格式化时间戳
 * @param {number} timestamp - 时间戳
 * @param {string} format - 格式
 * @returns {string} 格式化后的时间字符串
 */
export function formatTimestamp (timestamp, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!timestamp) return ''

  try {
    const date = new Date(timestamp * 1000) // 假设是秒级时间戳
    return formatDate(date, format)
  } catch (error) {
    console.warn('格式化时间戳失败:', error)
    return ''
  }
}

/**
 * 截断文本
 * @param {string} text - 要截断的文本
 * @param {number} maxLength - 最大长度
 * @param {string} suffix - 后缀，默认为 '...'
 * @returns {string} 截断后的文本
 */
export function truncateText (text, maxLength, suffix = '...') {
  if (!text || typeof text !== 'string') return ''

  if (text.length <= maxLength) return text

  return text.substring(0, maxLength - suffix.length) + suffix
}

/**
 * 格式化手机号
 * @param {string} phone - 手机号
 * @returns {string} 格式化后的手机号
 */
export function formatPhone (phone) {
  if (!phone) return ''

  const cleaned = phone.replace(/\D/g, '')

  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
  }

  return phone
}

/**
 * 格式化身份证号
 * @param {string} idCard - 身份证号
 * @param {boolean} mask - 是否遮蔽中间部分
 * @returns {string} 格式化后的身份证号
 */
export function formatIdCard (idCard, mask = true) {
  if (!idCard) return ''

  if (mask && idCard.length === 18) {
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
  }

  return idCard
}

/**
 * 格式化银行卡号
 * @param {string} cardNumber - 银行卡号
 * @param {boolean} mask - 是否遮蔽中间部分
 * @returns {string} 格式化后的银行卡号
 */
export function formatBankCard (cardNumber, mask = true) {
  if (!cardNumber) return ''

  const cleaned = cardNumber.replace(/\s/g, '')

  if (mask && cleaned.length >= 12) {
    const start = cleaned.substring(0, 4)
    const end = cleaned.substring(cleaned.length - 4)
    const middle = '*'.repeat(cleaned.length - 8)
    return `${start} ${middle} ${end}`.replace(/(.{4})/g, '$1 ').trim()
  }

  return cleaned.replace(/(.{4})/g, '$1 ').trim()
}

/**
 * 格式化税号
 * @param {string} taxId - 税号
 * @param {boolean} mask - 是否遮蔽中间部分
 * @returns {string} 格式化后的税号
 */
export function formatTaxId (taxId, mask = false) {
  if (!taxId) return ''

  if (mask && taxId.length >= 10) {
    const start = taxId.substring(0, 3)
    const end = taxId.substring(taxId.length - 3)
    const middle = '*'.repeat(taxId.length - 6)
    return `${start}${middle}${end}`
  }

  return taxId
}

// 默认导出所有格式化函数
export default {
  formatNumber,
  formatCurrency,
  formatPercentage,
  formatFileSize,
  formatDate,
  formatTimestamp,
  truncateText,
  formatPhone,
  formatIdCard,
  formatBankCard,
  formatTaxId
}
