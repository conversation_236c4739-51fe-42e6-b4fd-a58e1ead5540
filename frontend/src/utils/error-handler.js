/**
 * 统一错误处理机制
 * 基于API_DATA_STANDARDS.md规范，提供统一的错误处理和用户提示
 */

import { message, notification } from 'ant-design-vue'
import router from '@/router'

/**
 * 错误类型枚举
 */
export const ERROR_TYPES = {
  NETWORK: 'NETWORK',           // 网络错误
  API: 'API',                   // API错误
  VALIDATION: 'VALIDATION',     // 验证错误
  PERMISSION: 'PERMISSION',     // 权限错误
  BUSINESS: 'BUSINESS',         // 业务逻辑错误
  UNKNOWN: 'UNKNOWN'            // 未知错误
}

/**
 * 错误级别枚举
 */
export const ERROR_LEVELS = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical'
}

/**
 * 错误处理配置
 */
const ERROR_CONFIG = {
  // 是否显示详细错误信息（开发环境）
  showDetailInDev: process.env.NODE_ENV === 'development',
  
  // 默认错误消息
  defaultMessages: {
    [ERROR_TYPES.NETWORK]: '网络连接失败，请检查网络设置',
    [ERROR_TYPES.API]: '服务器响应异常，请稍后重试',
    [ERROR_TYPES.VALIDATION]: '数据验证失败，请检查输入',
    [ERROR_TYPES.PERMISSION]: '权限不足，无法执行此操作',
    [ERROR_TYPES.BUSINESS]: '操作失败，请稍后重试',
    [ERROR_TYPES.UNKNOWN]: '未知错误，请联系管理员'
  },

  // 需要跳转登录页的错误码
  authErrorCodes: [401, 403],

  // 需要显示通知而不是消息的错误码
  notificationErrorCodes: [500, 502, 503, 504],

  // 静默处理的错误码（不显示用户提示）
  silentErrorCodes: []
}

/**
 * 标准化错误对象
 */
export class StandardError extends Error {
  constructor (message, type = ERROR_TYPES.UNKNOWN, level = ERROR_LEVELS.ERROR, details = {}) {
    super(message)
    this.name = 'StandardError'
    this.type = type
    this.level = level
    this.details = details
    this.timestamp = Date.now()
  }
}

/**
 * 解析错误信息
 * @param {Error|Object|string} error - 错误对象
 * @returns {Object} - 标准化的错误信息
 */
export function parseError (error) {
  let errorInfo = {
    type: ERROR_TYPES.UNKNOWN,
    level: ERROR_LEVELS.ERROR,
    message: '未知错误',
    code: null,
    details: {},
    originalError: error
  }

  if (!error) {
    return errorInfo
  }

  // 处理字符串错误
  if (typeof error === 'string') {
    errorInfo.message = error
    return errorInfo
  }

  // 处理StandardError
  if (error instanceof StandardError) {
    return {
      type: error.type,
      level: error.level,
      message: error.message,
      code: error.code,
      details: error.details,
      originalError: error
    }
  }

  // 处理网络错误
  if (error.code === 'NETWORK_ERROR' || !error.response) {
    errorInfo.type = ERROR_TYPES.NETWORK
    errorInfo.message = ERROR_CONFIG.defaultMessages[ERROR_TYPES.NETWORK]
    return errorInfo
  }

  // 处理HTTP响应错误
  if (error.response) {
    const { status, data } = error.response
    errorInfo.code = status

    // 根据状态码确定错误类型
    if (status >= 400 && status < 500) {
      if (ERROR_CONFIG.authErrorCodes.includes(status)) {
        errorInfo.type = ERROR_TYPES.PERMISSION
        errorInfo.level = ERROR_LEVELS.WARNING
      } else {
        errorInfo.type = ERROR_TYPES.VALIDATION
      }
    } else if (status >= 500) {
      errorInfo.type = ERROR_TYPES.API
      errorInfo.level = ERROR_LEVELS.CRITICAL
    }

    // 提取错误消息
    if (data) {
      if (typeof data === 'string') {
        errorInfo.message = data
      } else if (data.message) {
        errorInfo.message = data.message
      } else if (data.error) {
        errorInfo.message = data.error
      } else if (data.errors && Array.isArray(data.errors)) {
        errorInfo.message = data.errors.map(err => err.message || err).join('; ')
        errorInfo.details.validationErrors = data.errors
      }
    }

    // 如果没有提取到消息，使用默认消息
    if (!errorInfo.message || errorInfo.message === '未知错误') {
      errorInfo.message = ERROR_CONFIG.defaultMessages[errorInfo.type]
    }
  }

  // 处理直接的错误响应对象（如API返回的错误格式）
  if (error.code && error.code >= 400 && error.message) {
    errorInfo.code = error.code
    errorInfo.message = error.message

    if (error.code >= 400 && error.code < 500) {
      if (ERROR_CONFIG.authErrorCodes.includes(error.code)) {
        errorInfo.type = ERROR_TYPES.PERMISSION
        errorInfo.level = ERROR_LEVELS.WARNING
      } else {
        errorInfo.type = ERROR_TYPES.VALIDATION
      }
    } else if (error.code >= 500) {
      errorInfo.type = ERROR_TYPES.API
      errorInfo.level = ERROR_LEVELS.CRITICAL
    }
  }

  // 处理业务逻辑错误
  if (error.message) {
    errorInfo.message = error.message
    if (error.type) {
      errorInfo.type = error.type
    }
  }

  return errorInfo
}

/**
 * 显示错误提示
 * @param {Object} errorInfo - 错误信息
 * @param {Object} options - 显示选项
 */
export function showErrorMessage (errorInfo, options = {}) {
  const {
    showMessage = true,
    showNotification = false,
    duration = 4.5
  } = options

  // 检查是否需要静默处理
  if (errorInfo.code && ERROR_CONFIG.silentErrorCodes.includes(errorInfo.code)) {
    return
  }

  // 根据错误级别和代码决定显示方式
  const shouldShowNotification = showNotification || 
    (errorInfo.code && ERROR_CONFIG.notificationErrorCodes.includes(errorInfo.code)) ||
    errorInfo.level === ERROR_LEVELS.CRITICAL

  if (shouldShowNotification) {
    notification.error({
      message: '系统错误',
      description: errorInfo.message,
      duration
    })
  } else if (showMessage) {
    switch (errorInfo.level) {
      case ERROR_LEVELS.WARNING:
        message.warning(errorInfo.message, duration)
        break
      case ERROR_LEVELS.INFO:
        message.info(errorInfo.message, duration)
        break
      default:
        message.error(errorInfo.message, duration)
    }
  }
}

/**
 * 处理权限错误
 * @param {Object} errorInfo - 错误信息
 */
export function handlePermissionError (errorInfo) {
  if (errorInfo.code === 401) {
    // 未认证，跳转到登录页
    message.error('登录已过期，请重新登录')
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    router.push('/login')
  } else if (errorInfo.code === 403) {
    // 无权限
    message.error('权限不足，无法访问此资源')
  }
}

/**
 * 记录错误日志
 * @param {Object} errorInfo - 错误信息
 * @param {Object} context - 上下文信息
 */
export function logError (errorInfo, context = {}) {
  const logData = {
    timestamp: new Date().toISOString(),
    type: errorInfo.type,
    level: errorInfo.level,
    message: errorInfo.message,
    code: errorInfo.code,
    details: errorInfo.details,
    context: {
      url: window.location.href,
      userAgent: navigator.userAgent,
      ...context
    }
  }

  // 开发环境打印到控制台
  if (ERROR_CONFIG.showDetailInDev) {
    console.group(`🚨 ${errorInfo.level.toUpperCase()} - ${errorInfo.type}`)
    console.error('Message:', errorInfo.message)
    console.error('Details:', logData)
    if (errorInfo.originalError) {
      console.error('Original Error:', errorInfo.originalError)
    }
    console.groupEnd()
  }

  // 生产环境发送到日志服务
  if (process.env.NODE_ENV === 'production') {
    // 这里可以集成第三方日志服务，如 Sentry、LogRocket 等
    // sendToLogService(logData)
  }
}

/**
 * 统一错误处理函数
 * @param {Error|Object|string} error - 错误对象
 * @param {Object} options - 处理选项
 * @param {Object} context - 上下文信息
 */
export function handleError (error, options = {}, context = {}) {
  const {
    showMessage = true,
    showNotification = false,
    logError: shouldLog = true,
    rethrow = false
  } = options

  // 解析错误
  const errorInfo = parseError(error)

  // 记录错误日志
  if (shouldLog) {
    logError(errorInfo, context)
  }

  // 处理权限错误
  if (errorInfo.type === ERROR_TYPES.PERMISSION) {
    handlePermissionError(errorInfo)
    return errorInfo
  }

  // 显示错误提示
  showErrorMessage(errorInfo, { showMessage, showNotification })

  // 是否重新抛出错误
  if (rethrow) {
    throw new StandardError(
      errorInfo.message,
      errorInfo.type,
      errorInfo.level,
      errorInfo.details
    )
  }

  return errorInfo
}

/**
 * 创建错误处理装饰器
 * @param {Object} options - 装饰器选项
 * @returns {Function} - 装饰器函数
 */
export function createErrorHandler (options = {}) {
  return function (target, propertyKey, descriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args) {
      try {
        return await originalMethod.apply(this, args)
      } catch (error) {
        handleError(error, options, {
          method: `${target.constructor.name}.${propertyKey}`,
          args: args.length
        })
      }
    }

    return descriptor
  }
}

/**
 * 异步函数错误处理包装器
 * @param {Function} asyncFn - 异步函数
 * @param {Object} options - 处理选项
 * @returns {Function} - 包装后的函数
 */
export function withErrorHandler (asyncFn, options = {}) {
  return async function (...args) {
    try {
      return await asyncFn.apply(this, args)
    } catch (error) {
      return handleError(error, options, {
        function: asyncFn.name || 'anonymous',
        args: args.length
      })
    }
  }
}

/**
 * 全局错误处理器
 */
export function setupGlobalErrorHandler () {
  // 处理未捕获的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    handleError(event.reason, {
      showMessage: true,
      logError: true
    }, {
      type: 'unhandledrejection'
    })
    event.preventDefault()
  })

  // 处理JavaScript运行时错误
  window.addEventListener('error', (event) => {
    handleError(event.error || event.message, {
      showMessage: false, // 运行时错误通常不需要显示给用户
      logError: true
    }, {
      type: 'javascript',
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    })
  })

  // Vue错误处理（如果使用Vue 3）
  if (window.Vue && window.Vue.config) {
    window.Vue.config.errorHandler = (error, instance, info) => {
      handleError(error, {
        showMessage: true,
        logError: true
      }, {
        type: 'vue',
        component: instance?.$options.name || 'Unknown',
        info
      })
    }
  }
}

// 导出默认配置
export { ERROR_CONFIG }
