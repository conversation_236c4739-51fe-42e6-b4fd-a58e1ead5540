<template>
  <div class="reset-password-container">
    <div class="reset-password-box">
      <div class="reset-password-header">
        <img src="@/assets/logo.png" alt="Logo" class="reset-password-logo">
        <h1 class="reset-password-title">重置密码</h1>
        <p class="reset-password-subtitle">请输入您的新密码</p>
      </div>

      <a-form
        :model="formState"
        name="reset-password-form"
        class="reset-password-form"
        @finish="handleSubmit"
      >
        <a-alert
          v-if="errorMessage"
          :message="errorMessage"
          type="error"
          show-icon
          class="reset-password-alert"
        />

        <a-alert
          v-if="successMessage"
          :message="successMessage"
          type="success"
          show-icon
          class="reset-password-alert"
        />

        <a-form-item
          name="password"
          :rules="passwordRules"
        >
          <a-input-password
            v-model:value="formState.password"
            size="large"
            placeholder="请输入新密码"
            maxlength="50"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>

        <a-form-item
          name="confirmPassword"
          :rules="confirmPasswordRules"
        >
          <a-input-password
            v-model:value="formState.confirmPassword"
            size="large"
            placeholder="请确认新密码"
            maxlength="50"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            :loading="loading"
            :disabled="!formState.password || !formState.confirmPassword || successMessage"
            class="reset-password-button"
          >
            {{ successMessage ? '重置成功' : '重置密码' }}
          </a-button>
        </a-form-item>

        <div class="reset-password-actions">
          <router-link to="/login" class="reset-password-link">返回登录</router-link>
          <span class="divider">|</span>
          <router-link to="/forgot-password" class="reset-password-link">重新发送重置链接</router-link>
        </div>
      </a-form>
    </div>

    <div class="reset-password-footer">
      税易通 ©{{ new Date().getFullYear() }} 版权所有
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { LockOutlined } from '@ant-design/icons-vue'

import { resetPassword } from '@/api/auth'

export default defineComponent({
  name: 'AuthResetPassword',
  components: {
    LockOutlined
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const loading = ref(false)
    const errorMessage = ref('')
    const successMessage = ref('')
    const resetToken = ref('')

    const formState = reactive({
      password: '',
      confirmPassword: ''
    })

    const passwordRules = [
      { required: true, message: '请输入新密码!' },
      { min: 6, message: '密码长度至少6位!' },
      { max: 50, message: '密码长度不能超过50位!' },
      {
        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
        message: '密码必须包含大小写字母和数字!'
      }
    ]

    const confirmPasswordRules = computed(() => [
      { required: true, message: '请确认新密码!' },
      {
        validator: (rule, value) => {
          if (value && value !== formState.password) {
            return Promise.reject('两次输入的密码不一致!')
          }
          return Promise.resolve()
        }
      }
    ])

    const handleSubmit = async (values) => {
      try {
        loading.value = true
        errorMessage.value = ''
        successMessage.value = ''

        if (!resetToken.value) {
          errorMessage.value = '重置令牌无效，请重新申请密码重置'
          return
        }

        const requestData = {
          token: resetToken.value,
          password: values.password
        }

        const response = await resetPassword(requestData)

        if (response && response.code === 200) {
          successMessage.value = '密码重置成功，即将跳转到登录页'
          message.success('密码重置成功')
          
          // 2秒后自动跳转到登录页
          setTimeout(() => {
            router.push('/login')
          }, 2000)
        } else {
          errorMessage.value = response?.message || '密码重置失败，请稍后再试'
        }
      } catch (error) {
        console.error('重置密码请求出错:', error)
        const errorMsg = error.response?.data?.message ||
          error.response?.data?.error ||
          '密码重置失败，请稍后再试'
        errorMessage.value = errorMsg
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      // 从URL参数中获取重置令牌
      resetToken.value = route.query.token || ''
      
      if (!resetToken.value) {
        errorMessage.value = '重置令牌缺失，请通过忘记密码功能重新申请'
      }
    })

    return {
      formState,
      loading,
      errorMessage,
      successMessage,
      passwordRules,
      confirmPasswordRules,
      handleSubmit
    }
  }
})
</script>

<style scoped>
.reset-password-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f0f2f5;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDQwIDQwIj48ZyBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiIGZpbGwtb3BhY2l0eT0iLjQiPjxwYXRoIGQ9Ik0yMCAwaHIxMHYxMEgyMHoiLz48cGF0aCBkPSJNMCAwaDEwdjEwSDB6Ii8+PHBhdGggZD0iTTEwIDBoMTB2MTBIMTBaIi8+PHBhdGggZD0iTTMwIDBoMTB2MTBIMzB6Ii8+PHBhdGggZD0iTTAgMTBoMTB2MTBIMHoiLz48cGF0aCBkPSJNMTAgMTBoMTB2MTBIMTBaIi8+PHBhdGggZD0iTTIwIDEwaDEwdjEwSDIweiIvPjxwYXRoIGQ9Ik0zMCAxMGgxMHYxMEgzMHoiLz48cGF0aCBkPSJNMCAyMGgxMHYxMEgweiIvPjxwYXRoIGQ9Ik0xMCAyMGgxMHYxMEgxMFoiLz48cGF0aCBkPSJNMjAgMjBoMTB2MTBIMjB6Ii8+PHBhdGggZD0iTTMwIDIwaDEwdjEwSDMweiIvPjxwYXRoIGQ9Ik0wIDMwaDEwdjEwSDB6Ii8+PHBhdGggZD0iTTEwIDMwaDEwdjEwSDEwWiIvPjxwYXRoIGQ9Ik0yMCAzMGgxMHYxMEgyMHoiLz48cGF0aCBkPSJNMzAgMzBoMTB2MTBIMzB6Ii8+PC9nPjwvZz48L3N2Zz4=');
}

.reset-password-box {
  width: 400px;
  padding: 32px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.1);
}

.reset-password-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32px;
}

.reset-password-logo {
  width: 64px;
  height: 64px;
}

.reset-password-title {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

.reset-password-subtitle {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  margin: 0;
  text-align: center;
}

.reset-password-form {
  margin-top: 24px;
}

.reset-password-alert {
  margin-bottom: 24px;
}

.reset-password-button {
  width: 100%;
}

.reset-password-actions {
  text-align: center;
  margin-top: 16px;
}

.reset-password-link {
  color: #1890ff;
  text-decoration: none;
}

.reset-password-link:hover {
  color: #40a9ff;
}

.divider {
  margin: 0 12px;
  color: rgba(0, 0, 0, 0.25);
}

.reset-password-footer {
  margin-top: 48px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
