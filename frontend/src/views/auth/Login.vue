<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <img src="@/assets/logo.png" alt="Logo" class="login-logo">
        <h1 class="login-title">
          税易通
        </h1>
      </div>

      <a-form
        :model="formState"
        name="login-form"
        class="login-form"
        @finish="handleSubmit"
      >
        <a-alert
          v-if="loginError"
          :message="loginError"
          type="error"
          show-icon
          class="login-alert"
        />

        <a-form-item
          name="phone"
          :rules="[{ required: true, message: '请输入手机号!', pattern: /^1[3-9]\d{9}$/ }]"
        >
          <a-input
            v-model:value="formState.phone"
            size="large"
            placeholder="手机号"
            maxlength="11"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item
          name="password"
          :rules="[{ required: true, message: '请输入密码!' }]"
        >
          <a-input-password
            v-model:value="formState.password"
            size="large"
            placeholder="密码"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>

        <a-form-item>
          <a-row :gutter="16" align="middle">
            <a-col :span="12">
              <a-checkbox v-model:checked="formState.remember">
                记住我
              </a-checkbox>
            </a-col>
            <a-col :span="12" style="text-align: right">
              <router-link to="/forgot-password" class="login-forgot-link">忘记密码?</router-link>
            </a-col>
          </a-row>
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            :loading="loading"
            class="login-button"
          >
            登录
          </a-button>
        </a-form-item>

        <div class="login-actions">
          <span>还没有账号? <router-link to="/register">立即注册</router-link></span>
        </div>
      </a-form>
    </div>

    <div class="login-footer">
      税易通 ©{{ new Date().getFullYear() }} 版权所有
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'

import { login } from '@/api/auth'
import { useUserStore } from '@/store/user'

export default defineComponent({
  name: 'AuthLogin',
  components: {
    UserOutlined,
    LockOutlined
  },
  emits: [],
  setup () {
    const router = useRouter()
    const userStore = useUserStore()
    const loading = ref(false)
    const loginError = ref('')

    const formState = reactive({
      phone: '',
      password: '',
      remember: true
    })

    const handleSubmit = async (values) => {
      try {
        loading.value = true
        loginError.value = ''

        const response = await login({
          phone: values.phone,
          password: values.password
        })

        // login函数使用createEntityApiCall包装，直接返回data字段
        if (response && response.accessToken) {
          await userStore.login(response.accessToken, response.refreshToken)
          message.success('登录成功')
          router.push('/')
        } else {
          loginError.value = '登录失败，请检查手机号和密码'
        }
      } catch (error) {
        console.error('登录出错:', error)
        // createEntityApiCall已经处理了错误显示，这里只需要设置本地错误状态
        loginError.value = error.message || '登录失败，请稍后再试'
      } finally {
        loading.value = false
      }
    }

    return {
      formState,
      loading,
      loginError,
      handleSubmit
    }
  }
})
</script>

<style scoped>
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f0f2f5;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDQwIDQwIj48ZyBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiIGZpbGwtb3BhY2l0eT0iLjQiPjxwYXRoIGQ9Ik0yMCAwaHIxMHYxMEgyMHoiLz48cGF0aCBkPSJNMCAwaDEwdjEwSDB6Ii8+PHBhdGggZD0iTTEwIDBoMTB2MTBIMTBaIi8+PHBhdGggZD0iTTMwIDBoMTB2MTBIMzB6Ii8+PHBhdGggZD0iTTAgMTBoMTB2MTBIMHoiLz48cGF0aCBkPSJNMTAgMTBoMTB2MTBIMTBaIi8+PHBhdGggZD0iTTIwIDEwaDEwdjEwSDIweiIvPjxwYXRoIGQ9Ik0zMCAxMGgxMHYxMEgzMHoiLz48cGF0aCBkPSJNMCAyMGgxMHYxMEgweiIvPjxwYXRoIGQ9Ik0xMCAyMGgxMHYxMEgxMFoiLz48cGF0aCBkPSJNMjAgMjBoMTB2MTBIMjB6Ii8+PHBhdGggZD0iTTMwIDIwaDEwdjEwSDMweiIvPjxwYXRoIGQ9Ik0wIDMwaDEwdjEwSDB6Ii8+PHBhdGggZD0iTTEwIDMwaDEwdjEwSDEwWiIvPjxwYXRoIGQ9Ik0yMCAzMGgxMHYxMEgyMHoiLz48cGF0aCBkPSJNMzAgMzBoMTB2MTBIMzB6Ii8+PC9nPjwvZz48L3N2Zz4=');
}

.login-box {
  width: 400px;
  padding: 32px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.1);
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32px;
}

.login-logo {
  width: 64px;
  height: 64px;
}

.login-title {
  margin-top: 16px;
  font-size: 24px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

.login-form {
  margin-top: 24px;
}

.login-alert {
  margin-bottom: 24px;
}

.login-button {
  width: 100%;
}

.login-forgot-link {
  color: #1890ff;
  cursor: pointer;
}

.login-forgot-link:hover {
  color: #40a9ff;
}

.login-actions {
  text-align: center;
  margin-top: 16px;
}

.login-footer {
  margin-top: 48px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
