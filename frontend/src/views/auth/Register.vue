<template>
  <div class="register-container">
    <div class="register-box">
      <div class="register-header">
        <img src="@/assets/logo.png" alt="Logo" class="register-logo">
        <h1 class="register-title">
          税易通
        </h1>
        <p class="register-subtitle">
          创建新账户
        </p>
      </div>

      <a-form
        :model="formState"
        name="register-form"
        class="register-form"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        @finish="handleSubmit"
      >
        <a-alert
          v-if="registerError"
          :message="registerError"
          type="error"
          show-icon
          class="register-alert"
        />

        <a-alert
          v-if="registerSuccess"
          :message="registerSuccess"
          type="success"
          show-icon
          class="register-alert"
        />

        <a-form-item
          label="手机号"
          name="phone"
          :rules="[
            { required: true, message: '请输入手机号!' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式!' }
          ]"
        >
          <a-input
            v-model:value="formState.phone"
            size="large"
            placeholder="请输入手机号"
            maxlength="11"
          />
        </a-form-item>

        <a-form-item
          label="密码"
          name="password"
          :rules="[
            { required: true, message: '请输入密码!' },
            { min: 8, message: '密码长度至少8位!' }
          ]"
        >
          <a-input-password
            v-model:value="formState.password"
            size="large"
            placeholder="请输入密码（至少8位）"
          />
        </a-form-item>

        <a-form-item
          label="确认密码"
          name="confirmPassword"
          :rules="[
            { required: true, message: '请确认密码!' },
            { validator: validateConfirmPassword, trigger: 'change' }
          ]"
        >
          <a-input-password
            v-model:value="formState.confirmPassword"
            size="large"
            placeholder="请再次输入密码"
          />
        </a-form-item>

        <a-form-item
          label="姓名"
          name="name"
          :rules="[{ required: true, message: '请输入姓名!' }]"
        >
          <a-input
            v-model:value="formState.name"
            size="large"
            placeholder="请输入真实姓名"
          />
        </a-form-item>

        <a-form-item
          label="邮箱"
          name="email"
          :rules="[
            { type: 'email', message: '请输入正确的邮箱格式!' }
          ]"
        >
          <a-input
            v-model:value="formState.email"
            size="large"
            placeholder="请输入邮箱（可选）"
          />
        </a-form-item>

        <a-form-item
          label="企业名称"
          name="enterprise"
        >
          <a-input
            v-model:value="formState.enterprise"
            size="large"
            placeholder="请输入企业名称（可选）"
          />
        </a-form-item>

        <a-form-item
          label="部门"
          name="department"
        >
          <a-input
            v-model:value="formState.department"
            size="large"
            placeholder="请输入部门（可选）"
          />
        </a-form-item>

        <a-form-item
          label="职位"
          name="position"
        >
          <a-input
            v-model:value="formState.position"
            size="large"
            placeholder="请输入职位（可选）"
          />
        </a-form-item>

        <a-form-item
          :wrapper-col="{ offset: 6, span: 18 }"
        >
          <a-checkbox v-model:checked="formState.agreement">
            我已阅读并同意<a href="#" @click.prevent>《用户协议》</a>和<a href="#" @click.prevent>《隐私政策》</a>
          </a-checkbox>
        </a-form-item>

        <a-form-item
          :wrapper-col="{ offset: 6, span: 18 }"
        >
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            :loading="loading"
            :disabled="!formState.agreement"
            class="register-button"
          >
            注册
          </a-button>
        </a-form-item>

        <div class="register-actions">
          <span>已有账号? <router-link to="/login">立即登录</router-link></span>
        </div>
      </a-form>
    </div>

    <div class="register-footer">
      税易通 ©{{ new Date().getFullYear() }} 版权所有
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { register } from '@/api/auth'
import { useUserStore } from '@/store/user'

export default defineComponent({
  name: 'AuthRegister',
  setup () {
    const router = useRouter()
    const _userStore = useUserStore()
    const loading = ref(false)
    const registerError = ref('')
    const registerSuccess = ref('')

    const formState = reactive({
      phone: '',
      password: '',
      confirmPassword: '',
      name: '',
      email: '',
      enterprise: '',
      department: '',
      position: '',
      agreement: false
    })

    const validateConfirmPassword = async (rule, value) => {
      if (value === '') {
        return Promise.reject(new Error('请确认密码!'))
      } else if (value !== formState.password) {
        return Promise.reject(new Error('两次输入的密码不一致!'))
      } else {
        return Promise.resolve()
      }
    }

    const handleSubmit = async (values) => {
      try {
        loading.value = true
        registerError.value = ''
        registerSuccess.value = ''

        // register函数使用createEntityApiCall包装，返回响应数据
        const response = await register({
          phone: values.phone,
          password: values.password,
          name: values.name,
          email: values.email || '',
          enterprise: values.enterprise || '',
          department: values.department || '',
          position: values.position || ''
        })

        // 注册成功
        registerSuccess.value = '注册成功！正在跳转到登录页面...'

        setTimeout(() => {
          router.push('/login')
        }, 2000)
      } catch (error) {
        console.error('注册出错:', error)
        // 错误信息已经由createEntityApiCall处理并显示
        // 这里只需要设置本地错误状态
        registerError.value = error.message || '注册失败，请稍后再试'
      } finally {
        loading.value = false
      }
    }

    return {
      formState,
      loading,
      registerError,
      registerSuccess,
      handleSubmit,
      validateConfirmPassword
    }
  }
})
</script>

<style scoped>
.register-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f0f2f5;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDQwIDQwIj48ZyBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiIGZpbGwtb3BhY2l0eT0iLjQiPjxwYXRoIGQ9Ik0yMCAwaHIxMHYxMEgyMHoiLz48cGF0aCBkPSJNMCAwaDEwdjEwSDB6Ii8+PHBhdGggZD0iTTEwIDBoMTB2MTBIMTBaIi8+PHBhdGggZD0iTTMwIDBoMTB2MTBIMzB6Ii8+PHBhdGggZD0iTTAgMTBoMTB2MTBIMHoiLz48cGF0aCBkPSJNMTAgMTBoMTB2MTBIMTBaIi8+PHBhdGggZD0iTTIwIDEwaDEwdjEwSDIweiIvPjxwYXRoIGQ9Ik0zMCAxMGgxMHYxMEgzMHoiLz48cGF0aCBkPSJNMCAyMGgxMHYxMEgweiIvPjxwYXRoIGQ9Ik0xMCAyMGgxMHYxMEgxMFoiLz48cGF0aCBkPSJNMjAgMjBoMTB2MTBIMjB6Ii8+PHBhdGggZD0iTTMwIDIwaDEwdjEwSDMweiIvPjxwYXRoIGQ9Ik0wIDMwaDEwdjEwSDB6Ii8+PHBhdGggZD0iTTEwIDMwaDEwdjEwSDEwWiIvPjxwYXRoIGQ9Ik0yMCAzMGgxMHYxMEgyMHoiLz48cGF0aCBkPSJNMzAgMzBoMTB2MTBIMzB6Ii8+PC9nPjwvZz48L3N2Zz4=');
  padding: 20px;
}

.register-box {
  width: 600px;
  max-width: 100%;
  padding: 32px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.1);
}

.register-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32px;
}

.register-logo {
  width: 64px;
  height: 64px;
}

.register-title {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

.register-subtitle {
  color: rgba(0, 0, 0, 0.45);
  margin: 0;
}

.register-form {
  margin-top: 24px;
}

.register-alert {
  margin-bottom: 24px;
}

.register-button {
  width: 100%;
}

.register-actions {
  text-align: center;
  margin-top: 16px;
}

.register-footer {
  margin-top: 48px;
  color: rgba(0, 0, 0, 0.45);
}

@media (max-width: 768px) {
  .register-box {
    width: 100%;
    padding: 24px;
  }

  .register-form :deep(.ant-form-item-label) {
    text-align: left;
  }
}
</style>
