<template>
  <div class="forgot-password-container">
    <div class="forgot-password-box">
      <div class="forgot-password-header">
        <img src="@/assets/logo.png" alt="Logo" class="forgot-password-logo">
        <h1 class="forgot-password-title">忘记密码</h1>
        <p class="forgot-password-subtitle">请输入您的邮箱或手机号，我们将发送重置密码的链接给您</p>
      </div>

      <a-form
        :model="formState"
        name="forgot-password-form"
        class="forgot-password-form"
        @finish="handleSubmit"
      >
        <a-alert
          v-if="errorMessage"
          :message="errorMessage"
          type="error"
          show-icon
          class="forgot-password-alert"
        />

        <a-alert
          v-if="successMessage"
          :message="successMessage"
          type="success"
          show-icon
          class="forgot-password-alert"
        />

        <a-form-item
          name="identifier"
          :rules="identifierRules"
        >
          <a-input
            v-model:value="formState.identifier"
            size="large"
            :placeholder="inputPlaceholder"
            maxlength="50"
          >
            <template #prefix>
              <UserOutlined v-if="inputType === 'phone'" />
              <MailOutlined v-else />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item>
          <a-radio-group v-model:value="inputType" class="input-type-selector">
            <a-radio-button value="phone">手机号</a-radio-button>
            <a-radio-button value="email">邮箱</a-radio-button>
          </a-radio-group>
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            :loading="loading"
            :disabled="!formState.identifier || successMessage"
            class="forgot-password-button"
          >
            {{ successMessage ? '已发送' : '发送重置链接' }}
          </a-button>
        </a-form-item>

        <div class="forgot-password-actions">
          <router-link to="/login" class="forgot-password-link">返回登录</router-link>
          <span class="divider">|</span>
          <router-link to="/register" class="forgot-password-link">立即注册</router-link>
        </div>
      </a-form>
    </div>

    <div class="forgot-password-footer">
      税易通 ©{{ new Date().getFullYear() }} 版权所有
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { UserOutlined, MailOutlined } from '@ant-design/icons-vue'

import { forgotPassword } from '@/api/auth'

export default defineComponent({
  name: 'AuthForgotPassword',
  components: {
    UserOutlined,
    MailOutlined
  },
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const errorMessage = ref('')
    const successMessage = ref('')
    const inputType = ref('phone')

    const formState = reactive({
      identifier: ''
    })

    const inputPlaceholder = computed(() => {
      return inputType.value === 'phone' ? '请输入手机号' : '请输入邮箱地址'
    })

    const identifierRules = computed(() => {
      if (inputType.value === 'phone') {
        return [
          { required: true, message: '请输入手机号!' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式!' }
        ]
      } else {
        return [
          { required: true, message: '请输入邮箱地址!' },
          { type: 'email', message: '请输入正确的邮箱格式!' }
        ]
      }
    })

    const handleSubmit = async (values) => {
      try {
        loading.value = true
        errorMessage.value = ''
        successMessage.value = ''

        const requestData = {}
        if (inputType.value === 'phone') {
          requestData.phone = values.identifier
        } else {
          requestData.email = values.identifier
        }

        const response = await forgotPassword(requestData)

        if (response && response.code === 200) {
          successMessage.value = '重置密码的链接已发送，请查收'
          message.success('重置密码的链接已发送')
          
          // 可选：3秒后自动跳转到登录页
          setTimeout(() => {
            router.push('/login')
          }, 3000)
        } else {
          errorMessage.value = response?.message || '发送失败，请稍后再试'
        }
      } catch (error) {
        console.error('忘记密码请求出错:', error)
        const errorMsg = error.response?.data?.message ||
          error.response?.data?.error ||
          '发送失败，请稍后再试'
        errorMessage.value = errorMsg
      } finally {
        loading.value = false
      }
    }

    return {
      formState,
      loading,
      errorMessage,
      successMessage,
      inputType,
      inputPlaceholder,
      identifierRules,
      handleSubmit
    }
  }
})
</script>

<style scoped>
.forgot-password-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f0f2f5;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDQwIDQwIj48ZyBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiIGZpbGwtb3BhY2l0eT0iLjQiPjxwYXRoIGQ9Ik0yMCAwaHIxMHYxMEgyMHoiLz48cGF0aCBkPSJNMCAwaDEwdjEwSDB6Ii8+PHBhdGggZD0iTTEwIDBoMTB2MTBIMTBaIi8+PHBhdGggZD0iTTMwIDBoMTB2MTBIMzB6Ii8+PHBhdGggZD0iTTAgMTBoMTB2MTBIMHoiLz48cGF0aCBkPSJNMTAgMTBoMTB2MTBIMTBaIi8+PHBhdGggZD0iTTIwIDEwaDEwdjEwSDIweiIvPjxwYXRoIGQ9Ik0zMCAxMGgxMHYxMEgzMHoiLz48cGF0aCBkPSJNMCAyMGgxMHYxMEgweiIvPjxwYXRoIGQ9Ik0xMCAyMGgxMHYxMEgxMFoiLz48cGF0aCBkPSJNMjAgMjBoMTB2MTBIMjB6Ii8+PHBhdGggZD0iTTMwIDIwaDEwdjEwSDMweiIvPjxwYXRoIGQ9Ik0wIDMwaDEwdjEwSDB6Ii8+PHBhdGggZD0iTTEwIDMwaDEwdjEwSDEwWiIvPjxwYXRoIGQ9Ik0yMCAzMGgxMHYxMEgyMHoiLz48cGF0aCBkPSJNMzAgMzBoMTB2MTBIMzB6Ii8+PC9nPjwvZz48L3N2Zz4=');
}

.forgot-password-box {
  width: 400px;
  padding: 32px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.1);
}

.forgot-password-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32px;
}

.forgot-password-logo {
  width: 64px;
  height: 64px;
}

.forgot-password-title {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

.forgot-password-subtitle {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  text-align: center;
}

.forgot-password-form {
  margin-top: 24px;
}

.forgot-password-alert {
  margin-bottom: 24px;
}

.input-type-selector {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 8px;
}

.input-type-selector .ant-radio-button-wrapper {
  flex: 1;
  text-align: center;
}

.forgot-password-button {
  width: 100%;
}

.forgot-password-actions {
  text-align: center;
  margin-top: 16px;
}

.forgot-password-link {
  color: #1890ff;
  text-decoration: none;
}

.forgot-password-link:hover {
  color: #40a9ff;
}

.divider {
  margin: 0 12px;
  color: rgba(0, 0, 0, 0.25);
}

.forgot-password-footer {
  margin-top: 48px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
