<template>
  <div class="gov-integration-container">
    <a-card title="政务平台对接" :bordered="false">
      <a-alert
        message="政务平台对接配置"
        description="通过电子税务局API，实现申报表自动提交、核验结果获取和工商数据同步"
        type="info"
        show-icon
        style="margin-bottom: 24px"
      />

      <!-- 连接状态卡片 -->
      <a-card class="status-card" :loading="loading">
        <template #extra>
          <a-button v-if="!connected" type="primary" @click="showConfigDrawer">
            配置连接
          </a-button>
          <a-button
            v-else
            type="danger"
            :loading="disconnectLoading"
            @click="handleDisconnect"
          >
            断开连接
          </a-button>
        </template>

        <a-descriptions title="连接状态" bordered :column="2">
          <a-descriptions-item label="连接状态" :span="1">
            <a-badge :status="connected ? 'success' : 'error'" :text="connected ? '已连接' : '未连接'" />
          </a-descriptions-item>
          <a-descriptions-item label="最后检查时间" :span="1">
            {{ statusInfo.lastChecked || '未检查' }}
          </a-descriptions-item>
          <a-descriptions-item label="区域" :span="1">
            {{ getRegionName(statusInfo.region) || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="账号" :span="1">
            {{ statusInfo.username || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="连接时间" :span="2">
            {{ statusInfo.connectedAt || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="证书状态" :span="2">
            <template v-if="statusInfo.certStatus">
              <a-tag color="green">
                有效
              </a-tag>
              <span style="margin-left: 8px">有效期至: {{ statusInfo.certExpireDate || '-' }}</span>
            </template>
            <a-tag v-else color="red">
              无效
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 功能卡片 -->
      <a-row :gutter="16" style="margin-top: 24px">
        <a-col :span="8">
          <a-card class="feature-card" :loading="loading">
            <template #title>
              <span class="card-title">
                <AuditOutlined class="card-icon" />
                电子税务局
              </span>
            </template>
            <p class="card-desc">
              实现与电子税务局的直连对接，支持自动申报、查询申报结果、下载完税证明等功能。
            </p>
            <div class="card-footer">
              <a-button type="primary" :disabled="!connected" @click="testEtaxConnection">
                测试连接
              </a-button>
              <a-button style="margin-left: 8px" :disabled="!connected" @click="goToSubmitHistory">
                查看申报历史
              </a-button>
            </div>
          </a-card>
        </a-col>

        <a-col :span="8">
          <a-card class="feature-card" :loading="loading">
            <template #title>
              <span class="card-title">
                <BookOutlined class="card-icon" />
                工商年报对接
              </span>
            </template>
            <p class="card-desc">
              对接工商年报系统，实现企业工商信息自动更新，减少人工填报工作量和出错风险。
            </p>
            <div class="card-footer">
              <a-button type="primary" :disabled="!connected" @click="syncBusinessInfo">
                同步信息
              </a-button>
              <a-button style="margin-left: 8px" :disabled="!connected" @click="viewBusinessReport">
                查看年报
              </a-button>
            </div>
          </a-card>
        </a-col>

        <a-col :span="8">
          <a-card class="feature-card" :loading="loading">
            <template #title>
              <span class="card-title">
                <SafetyOutlined class="card-icon" />
                信用中国对接
              </span>
            </template>
            <p class="card-desc">
              获取企业信用信息，监控企业信用状况变化，及时掌握企业信用风险。
            </p>
            <div class="card-footer">
              <a-button type="primary" :disabled="!connected" @click="checkCreditStatus">
                检查信用状态
              </a-button>
              <a-button style="margin-left: 8px" :disabled="!connected" @click="setCreditMonitor">
                设置监控
              </a-button>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 近期提交记录 -->
      <a-card title="近期提交记录" style="margin-top: 24px" :loading="loading">
        <a-table
          :columns="submissionColumns"
          :data-source="submissionRecords"
          :pagination="{ pageSize: 5 }"
          :loading="tableLoading"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'operation'">
              <a-button type="link" @click="viewSubmissionDetail(record)">
                查看
              </a-button>
              <a-button v-if="record.status === 'success'" type="link" @click="downloadReceipt(record)">
                下载凭证
              </a-button>
            </template>
          </template>
        </a-table>
      </a-card>
    </a-card>

    <!-- 配置抽屉 -->
    <a-drawer
      title="政务平台连接配置"
      :visible="configVisible"
      :width="500"
      @close="configVisible = false"
    >
      <a-form
        ref="configFormRef"
        :model="configForm"
        :rules="rules"
        layout="vertical"
      >
        <a-form-item label="电子税务局账号" name="username">
          <a-input v-model:value="configForm.username" placeholder="请输入电子税务局账号" />
        </a-form-item>

        <a-form-item label="电子税务局密码" name="password">
          <a-input-password v-model:value="configForm.password" placeholder="请输入电子税务局密码" />
        </a-form-item>

        <a-form-item label="区域" name="region">
          <a-select v-model:value="configForm.region" placeholder="请选择税务局所在区域">
            <a-select-option value="beijing">
              北京市
            </a-select-option>
            <a-select-option value="shanghai">
              上海市
            </a-select-option>
            <a-select-option value="guangdong">
              广东省
            </a-select-option>
            <a-select-option value="jiangsu">
              江苏省
            </a-select-option>
            <a-select-option value="zhejiang">
              浙江省
            </a-select-option>
            <a-select-option value="sichuan">
              四川省
            </a-select-option>
            <a-select-option value="other">
              其他地区
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="证书文件" name="certFile">
          <a-upload
            name="file"
            :multiple="false"
            action="/api/upload"
            :show-upload-list="true"
            @change="handleCertFileChange"
          >
            <a-button>
              <UploadOutlined /> 上传证书
            </a-button>
            <div style="margin-top: 8px; color: rgba(0,0,0,0.45);">
              支持.cer/.p12格式，大小不超过2MB
            </div>
          </a-upload>
        </a-form-item>

        <a-form-item v-if="configForm.certFile" label="证书密码" name="certPassword">
          <a-input-password v-model:value="configForm.certPassword" placeholder="请输入证书密码" />
        </a-form-item>

        <a-form-item label="自动同步设置">
          <a-checkbox-group v-model:value="configForm.syncOptions">
            <a-checkbox value="autoSubmit">
              自动提交申报表
            </a-checkbox>
            <a-checkbox value="autoDownload">
              自动下载申报结果
            </a-checkbox>
            <a-checkbox value="autoSync">
              自动同步工商信息
            </a-checkbox>
            <a-checkbox value="creditMonitor">
              信用变更监控
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" :loading="submitLoading" @click="handleSubmitConfig">
            建立连接
          </a-button>
          <a-button style="margin-left: 8px" @click="configVisible = false">
            取消
          </a-button>
        </a-form-item>
      </a-form>
    </a-drawer>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  UploadOutlined,
  AuditOutlined,
  BookOutlined,
  SafetyOutlined
} from '@ant-design/icons-vue'
import {
  checkGovConnectionStatus,
  establishGovConnection,
  getSubmittedReports
} from '@/api/integration'

export default defineComponent({
  name: 'GovIntegration',
  components: {
    UploadOutlined,
    AuditOutlined,
    BookOutlined,
    SafetyOutlined
  },
  setup () {
    const router = useRouter()
    const loading = ref(false)
    const tableLoading = ref(false)
    const configFormRef = ref(null)
    const submitLoading = ref(false)
    const disconnectLoading = ref(false)
    const configVisible = ref(false)
    const connected = ref(false)

    // 状态信息
    const statusInfo = reactive({
      lastChecked: '',
      region: '',
      username: '',
      connectedAt: '',
      certStatus: false,
      certExpireDate: ''
    })

    // 配置表单
    const configForm = reactive({
      username: '',
      password: '',
      region: undefined,
      certFile: undefined,
      certPassword: '',
      syncOptions: ['autoDownload', 'creditMonitor']
    })

    // 表单验证规则
    const rules = {
      username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
      password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
      region: [{ required: true, message: '请选择区域', trigger: 'change' }],
      certFile: [{ required: true, message: '请上传证书文件', trigger: 'change' }],
      certPassword: [{ required: true, message: '请输入证书密码', trigger: 'blur' }]
    }

    // 表格列定义
    const submissionColumns = [
      {
        title: '提交时间',
        dataIndex: 'submitTime',
        key: 'submitTime',
        width: 180
      },
      {
        title: '报表类型',
        dataIndex: 'reportType',
        key: 'reportType',
        width: 180
      },
      {
        title: '所属期',
        dataIndex: 'period',
        key: 'period',
        width: 120
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
      },
      {
        title: '反馈结果',
        dataIndex: 'feedback',
        key: 'feedback',
        ellipsis: true
      },
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        width: 180
      }
    ]

    // 提交记录数据
    const submissionRecords = ref([])

    // 证书文件上传变更
    const handleCertFileChange = (info) => {
      if (info.file.status === 'done') {
        configForm.certFile = info.file.response.url
        message.success(`${info.file.name} 上传成功`)
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`)
      }
    }

    // 提交配置
    const handleSubmitConfig = async () => {
      try {
        if (!configFormRef.value) return

        await configFormRef.value.validate()
        submitLoading.value = true

        const res = await establishGovConnection(configForm)

        if (res.code === 0) {
          message.success('政务平台连接成功')
          configVisible.value = false
          fetchConnectionStatus()
        } else {
          message.error(res.message || '连接失败')
        }
      } catch (error) {
        console.error('提交配置失败:', error)
        message.error('提交配置失败，请检查表单填写是否正确')
      } finally {
        submitLoading.value = false
      }
    }

    // 断开连接
    const handleDisconnect = async () => {
      try {
        disconnectLoading.value = true
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟请求延迟
        message.success('已断开政务平台连接')
        connected.value = false
        Object.assign(statusInfo, {
          lastChecked: new Date().toLocaleString(),
          region: '',
          username: '',
          connectedAt: '',
          certStatus: false,
          certExpireDate: ''
        })
      } catch (error) {
        console.error('断开连接失败:', error)
        message.error('断开连接失败，请稍后重试')
      } finally {
        disconnectLoading.value = false
      }
    }

    // 获取区域名称
    const getRegionName = (code) => {
      const regionMap = {
        beijing: '北京市',
        shanghai: '上海市',
        guangdong: '广东省',
        jiangsu: '江苏省',
        zhejiang: '浙江省',
        sichuan: '四川省',
        other: '其他地区'
      }
      return regionMap[code] || code
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        success: '成功',
        pending: '处理中',
        error: '失败',
        warning: '警告'
      }
      return statusMap[status] || status
    }

    // 获取状态颜色
    const getStatusColor = (status) => {
      const colorMap = {
        success: 'green',
        pending: 'blue',
        error: 'red',
        warning: 'orange'
      }
      return colorMap[status] || 'default'
    }

    // 显示配置抽屉
    const showConfigDrawer = () => {
      configVisible.value = true
    }

    // 获取连接状态
    const fetchConnectionStatus = async () => {
      loading.value = true
      try {
        const res = await checkGovConnectionStatus()

        if (res.code === 0) {
          connected.value = res.data.connected || false

          if (connected.value) {
            Object.assign(statusInfo, {
              lastChecked: new Date().toLocaleString(),
              region: res.data.region || '',
              username: res.data.username || '',
              connectedAt: res.data.connectedAt || '',
              certStatus: res.data.certStatus || false,
              certExpireDate: res.data.certExpireDate || ''
            })
          }
        } else {
          connected.value = false
        }
      } catch (error) {
        console.error('获取连接状态失败:', error)
        connected.value = false
      } finally {
        loading.value = false
      }
    }

    // 获取提交记录
    const fetchSubmissionRecords = async () => {
      tableLoading.value = true
      try {
        const res = await getSubmittedReports()

        if (res.code === 0 && res.data && res.data.items) {
          submissionRecords.value = res.data.items
        } else {
          submissionRecords.value = []
        }
      } catch (error) {
        console.error('获取提交记录失败:', error)
        submissionRecords.value = []
      } finally {
        tableLoading.value = false
      }
    }

    // 测试电子税务局连接
    const testEtaxConnection = async () => {
      try {
        message.loading('正在测试连接...', 1)
        await new Promise(resolve => setTimeout(resolve, 1500))
        message.success('电子税务局连接正常')
      } catch (error) {
        message.error('连接测试失败')
      }
    }

    // 跳转到申报历史页面
    const goToSubmitHistory = () => {
      router.push('/declaration/history')
    }

    // 同步工商信息
    const syncBusinessInfo = async () => {
      try {
        message.loading('正在同步工商信息...', 1)
        await new Promise(resolve => setTimeout(resolve, 1500))
        message.success('工商信息同步完成')
      } catch (error) {
        message.error('信息同步失败')
      }
    }

    // 查看年报
    const viewBusinessReport = () => {
      message.info('暂无年报数据')
    }

    // 检查信用状态
    const checkCreditStatus = async () => {
      try {
        message.loading('正在检查信用状态...', 1)
        await new Promise(resolve => setTimeout(resolve, 1500))
        message.success('企业信用状态良好')
      } catch (error) {
        message.error('信用检查失败')
      }
    }

    // 设置信用监控
    const setCreditMonitor = () => {
      message.success('信用监控已设置，异常变动将通过系统通知提醒')
    }

    // 查看提交详情
    const viewSubmissionDetail = (record) => {
      message.info(`查看提交记录：${record.reportType} - ${record.period}`)
    }

    // 下载凭证
    const downloadReceipt = (_record) => {
      message.success('凭证下载中...')
    }

    onMounted(() => {
      fetchConnectionStatus()
      fetchSubmissionRecords()
    })

    return {
      loading,
      tableLoading,
      configVisible,
      connected,
      statusInfo,
      configForm,
      configFormRef,
      rules,
      submitLoading,
      disconnectLoading,
      submissionColumns,
      submissionRecords,
      getRegionName,
      getStatusText,
      getStatusColor,
      handleCertFileChange,
      handleSubmitConfig,
      handleDisconnect,
      showConfigDrawer,
      testEtaxConnection,
      goToSubmitHistory,
      syncBusinessInfo,
      viewBusinessReport,
      checkCreditStatus,
      setCreditMonitor,
      viewSubmissionDetail,
      downloadReceipt
    }
  }
})
</script>

<style scoped>
.gov-integration-container {
  padding: 0 12px;
}

.status-card {
  margin-bottom: 24px;
}

.feature-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.feature-card :deep(.ant-card-body) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  display: flex;
  align-items: center;
}

.card-icon {
  margin-right: 8px;
  font-size: 18px;
}

.card-desc {
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.65);
  flex: 1;
}

.card-footer {
  margin-top: auto;
  text-align: right;
}
</style>
