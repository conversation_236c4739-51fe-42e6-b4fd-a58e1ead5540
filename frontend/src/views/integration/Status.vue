<template>
  <div class="integration-status-container">
    <a-card :title="`${integrationTitle}状态详情`" :bordered="false">
      <template #extra>
        <a-button @click="goBack">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回
        </a-button>
      </template>

      <a-spin :spinning="loading">
        <a-empty v-if="noData" description="暂无状态数据" />

        <div v-else>
          <!-- 状态卡片 -->
          <a-card class="status-card">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-statistic
                  title="连接状态"
                  :value="statusData.connected ? '已连接' : '未连接'"
                  :value-style="{ color: statusData.connected ? '#52c41a' : '#f5222d' }"
                >
                  <template #prefix>
                    <CheckCircleOutlined v-if="statusData.connected" />
                    <CloseCircleOutlined v-else />
                  </template>
                </a-statistic>
              </a-col>
              <a-col :span="8">
                <a-statistic
                  title="最近同步时间"
                  :value="statusData.lastSyncTime || '-'"
                />
              </a-col>
              <a-col :span="8">
                <a-statistic
                  title="总同步次数"
                  :value="statusData.syncCount || 0"
                />
              </a-col>
            </a-row>
          </a-card>

          <!-- 同步记录 -->
          <a-card title="同步记录" style="margin-top: 16px">
            <a-table
              :columns="syncColumns"
              :data-source="syncRecords"
              :pagination="{ pageSize: 10 }"
              :loading="tableLoading"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'status'">
                  <a-tag :color="getStatusColor(record.status)">
                    {{ getStatusText(record.status) }}
                  </a-tag>
                </template>
              </template>
            </a-table>
          </a-card>

          <!-- 日志记录 -->
          <a-card title="系统日志" style="margin-top: 16px">
            <a-timeline>
              <a-timeline-item
                v-for="(log, index) in logRecords"
                :key="index"
                :color="getLogColor(log.type)"
              >
                <p><span class="log-time">{{ log.time }}</span> - {{ log.message }}</p>
                <p v-if="log.details" class="log-details">
                  {{ log.details }}
                </p>
              </a-timeline-item>
            </a-timeline>
          </a-card>

          <!-- 操作区 -->
          <div v-if="statusData.connected" class="action-section">
            <a-button-group>
              <a-button type="primary" @click="handleSync">
                <template #icon>
                  <SyncOutlined />
                </template>
                立即同步
              </a-button>
              <a-button @click="handleCheck">
                <template #icon>
                  <SafetyOutlined />
                </template>
                检查连接
              </a-button>
              <a-button @click="viewConfiguration">
                <template #icon>
                  <SettingOutlined />
                </template>
                查看配置
              </a-button>
            </a-button-group>
          </div>
        </div>
      </a-spin>
    </a-card>

    <!-- 配置抽屉 -->
    <a-drawer
      title="集成配置详情"
      :visible="configVisible"
      :width="500"
      @close="configVisible = false"
    >
      <a-descriptions bordered :column="1">
        <a-descriptions-item v-for="(value, key) in configDetails" :key="key" :label="getConfigLabel(key)">
          <span v-if="shouldMaskValue(key)">******</span>
          <span v-else>{{ value }}</span>
        </a-descriptions-item>
      </a-descriptions>
    </a-drawer>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ArrowLeftOutlined,
  SyncOutlined,
  SafetyOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
import {
  checkIntegrationStatus as checkStatus,
  checkGovConnectionStatus
} from '@/api/integration'

export default defineComponent({
  name: 'IntegrationStatus',
  components: {
    CheckCircleOutlined,
    CloseCircleOutlined,
    ArrowLeftOutlined,
    SyncOutlined,
    SafetyOutlined,
    SettingOutlined
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const loading = ref(true)
    const tableLoading = ref(false)
    const configVisible = ref(false)
    const noData = ref(false)

    // 获取当前集成类型
    const integrationType = computed(() => route.params.type)

    // 集成标题映射
    const integrationTitleMap = {
      gov: '政务平台',
      financial: '财务系统',
      bank: '银行系统',
      invoice: '发票平台',
      ecommerce: '电商平台',
      logistics: '物流系统'
    }

    // 获取集成标题
    const integrationTitle = computed(() =>
      integrationTitleMap[integrationType.value] || '系统集成'
    )

    // 状态数据
    const statusData = reactive({
      connected: false,
      lastSyncTime: '',
      syncCount: 0
    })

    // 同步记录列定义
    const syncColumns = [
      {
        title: '同步时间',
        dataIndex: 'syncTime',
        key: 'syncTime',
        width: 180
      },
      {
        title: '同步类型',
        dataIndex: 'syncType',
        key: 'syncType',
        width: 120
      },
      {
        title: '数据量',
        dataIndex: 'dataCount',
        key: 'dataCount',
        width: 100
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
      },
      {
        title: '备注',
        dataIndex: 'remarks',
        key: 'remarks'
      }
    ]

    // 同步记录数据
    const syncRecords = ref([])

    // 日志记录数据
    const logRecords = ref([])

    // 配置详情
    const configDetails = ref({})

    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        success: '成功',
        processing: '处理中',
        error: '失败',
        warning: '部分成功'
      }
      return statusMap[status] || status
    }

    // 获取状态颜色
    const getStatusColor = (status) => {
      const colorMap = {
        success: 'green',
        processing: 'blue',
        error: 'red',
        warning: 'orange'
      }
      return colorMap[status] || 'default'
    }

    // 获取日志颜色
    const getLogColor = (type) => {
      const colorMap = {
        info: 'blue',
        success: 'green',
        error: 'red',
        warning: 'orange'
      }
      return colorMap[type] || 'blue'
    }

    // 获取配置标签
    const getConfigLabel = (key) => {
      const labelMap = {
        serverUrl: '服务器地址',
        username: '用户名',
        password: '密码',
        apiKey: 'API密钥',
        region: '区域',
        syncPeriod: '同步周期',
        deviceSN: '设备序列号',
        accountNo: '账号',
        bankCode: '银行代码',
        platformType: '平台类型',
        taxNumber: '税号',
        accountSet: '账套'
      }
      return labelMap[key] || key
    }

    // 是否应该遮盖值
    const shouldMaskValue = (key) => {
      return ['password', 'apiKey', 'devicePassword', 'certPassword'].includes(key)
    }

    // 返回上一页
    const goBack = () => {
      router.back()
    }

    // 立即同步
    const handleSync = async () => {
      message.success('同步指令已发送')
    }

    // 检查连接
    const handleCheck = async () => {
      try {
        message.loading({ content: '正在检查连接...', key: 'checkConnection' })

        // 使用checkStatus而不是未定义的checkGovConnectionStatus
        const res = await checkStatus(integrationType.value)

        if (res && res.code === 0) {
          message.success({ content: '连接正常', key: 'checkConnection' })
        } else {
          message.error({ content: '连接检查失败: ' + (res.message || '未知错误'), key: 'checkConnection' })
        }
      } catch (error) {
        message.error({ content: '连接检查失败: ' + error.message, key: 'checkConnection' })
      }
    }

    // 查看配置
    const viewConfiguration = () => {
      configVisible.value = true
    }

    // 获取集成状态
    const fetchIntegrationStatus = async () => {
      loading.value = true
      try {
        // 根据不同的集成类型调用不同的API
        let res

        switch (integrationType.value) {
        case 'gov':
          res = await checkGovConnectionStatus()
          break
        case 'invoice':
        case 'financial':
        case 'bank':
        case 'ecommerce':
        case 'logistics':
          res = await checkStatus(integrationType.value)
          break
        default:
          res = { code: -1, message: '未知的集成类型' }
        }

        if (res.code === 0 && res.data) {
          // 更新状态数据
          Object.assign(statusData, {
            connected: res.data.connected || false,
            lastSyncTime: res.data.lastSyncTime || '未同步',
            syncCount: res.data.syncCount || 0
          })

          // 更新配置详情
          configDetails.value = res.data.config || {}

          noData.value = false
        } else {
          noData.value = true
        }
      } catch (error) {
        console.error('获取集成状态失败:', error)
        message.error('获取集成状态失败')
        noData.value = true
      } finally {
        loading.value = false
      }
    }

    // 获取同步记录
    const fetchSyncRecords = async () => {
      tableLoading.value = true
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 800))

        // 模拟数据
        syncRecords.value = [
          {
            key: '1',
            syncTime: '2023-11-05 10:30:25',
            syncType: '全量同步',
            dataCount: 128,
            status: 'success',
            remarks: '正常同步完成'
          },
          {
            key: '2',
            syncTime: '2023-11-01 09:15:18',
            syncType: '增量同步',
            dataCount: 45,
            status: 'success',
            remarks: '正常同步完成'
          },
          {
            key: '3',
            syncTime: '2023-10-28 14:22:05',
            syncType: '全量同步',
            dataCount: 122,
            status: 'warning',
            remarks: '部分数据同步失败'
          },
          {
            key: '4',
            syncTime: '2023-10-25 08:45:30',
            syncType: '增量同步',
            dataCount: 18,
            status: 'error',
            remarks: '网络连接中断'
          }
        ]
      } catch (error) {
        console.error('获取同步记录失败:', error)
        message.error('获取同步记录失败')
      } finally {
        tableLoading.value = false
      }
    }

    // 获取日志记录
    const fetchLogRecords = async () => {
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 模拟数据
        logRecords.value = [
          {
            time: '2023-11-05 10:30:25',
            type: 'success',
            message: '同步完成',
            details: '成功同步128条数据'
          },
          {
            time: '2023-11-05 10:28:12',
            type: 'info',
            message: '开始同步数据',
            details: null
          },
          {
            time: '2023-11-01 09:15:18',
            type: 'success',
            message: '同步完成',
            details: '成功同步45条数据'
          },
          {
            time: '2023-10-28 14:22:05',
            type: 'warning',
            message: '部分数据同步失败',
            details: '5条数据同步失败，错误码: E3001'
          },
          {
            time: '2023-10-25 08:45:30',
            type: 'error',
            message: '同步失败',
            details: '网络连接中断，请检查网络设置'
          }
        ]
      } catch (error) {
        console.error('获取日志记录失败:', error)
        message.error('获取日志记录失败')
      }
    }

    onMounted(() => {
      fetchIntegrationStatus()
      fetchSyncRecords()
      fetchLogRecords()
    })

    return {
      loading,
      tableLoading,
      configVisible,
      noData,
      integrationType,
      integrationTitle,
      statusData,
      syncColumns,
      syncRecords,
      logRecords,
      configDetails,
      getStatusText,
      getStatusColor,
      getLogColor,
      getConfigLabel,
      shouldMaskValue,
      goBack,
      handleSync,
      handleCheck,
      viewConfiguration
    }
  }
})
</script>

<style scoped>
.integration-status-container {
  padding: 0 12px;
}

.status-card {
  margin-bottom: 24px;
}

.action-section {
  margin-top: 24px;
  text-align: center;
}

.log-time {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.log-details {
  color: rgba(0, 0, 0, 0.65);
  margin-top: 4px;
  margin-left: 16px;
  font-size: 12px;
}
</style>
