<template>
  <div class="integration-list-container">
    <a-card title="系统集成管理" :bordered="false">
      <a-alert
        message="集成状态总览"
        description="显示所有集成服务的连接状态，确保企业数据正常同步"
        type="info"
        show-icon
        style="margin-bottom: 24px"
      />

      <a-row :gutter="24">
        <a-col v-for="(item, index) in integrationList" :key="index" :span="8">
          <a-card class="integration-card" :loading="loading">
            <a-row align="middle">
              <a-col :span="6">
                <a-avatar :size="64" :icon="item.icon" :style="{ backgroundColor: item.color }" />
              </a-col>
              <a-col :span="18">
                <div class="integration-info">
                  <div class="integration-title">
                    {{ item.title }}
                  </div>
                  <a-tag :color="getStatusColor(item.status)">
                    {{ getStatusText(item.status) }}
                  </a-tag>
                  <div class="integration-desc">
                    {{ item.description }}
                  </div>
                </div>
              </a-col>
            </a-row>
            <div class="integration-footer">
              <a-space>
                <a-tooltip :title="item.status ? '断开连接' : '建立连接'">
                  <a-button
                    :type="item.status ? 'danger' : 'primary'"
                    :loading="item.loading"
                    @click="toggleConnection(item)"
                  >
                    {{ item.status ? '断开' : '连接' }}
                  </a-button>
                </a-tooltip>
                <a-tooltip title="查看详情">
                  <a-button @click="viewDetail(item.type)">
                    <template #icon>
                      <eye-outlined />
                    </template>
                    详情
                  </a-button>
                </a-tooltip>
              </a-space>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </a-card>

    <!-- 连接配置抽屉 -->
    <a-drawer
      :title="currentIntegration ? `配置${currentIntegration.title}连接` : '配置连接'"
      :visible="configVisible"
      placement="right"
      width="500"
      @close="configVisible = false"
    >
      <a-form
        v-if="currentIntegration"
        ref="configFormRef"
        :model="configForm"
        :rules="rules"
        layout="vertical"
      >
        <template v-if="currentIntegration.type === 'gov'">
          <a-form-item label="电子税务局账号" name="username">
            <a-input v-model:value="configForm.username" placeholder="请输入电子税务局账号" />
          </a-form-item>
          <a-form-item label="电子税务局密码" name="password">
            <a-input-password v-model:value="configForm.password" placeholder="请输入电子税务局密码" />
          </a-form-item>
          <a-form-item label="区域" name="region">
            <a-select v-model:value="configForm.region" placeholder="请选择税务局所在区域">
              <a-select-option value="beijing">
                北京市
              </a-select-option>
              <a-select-option value="shanghai">
                上海市
              </a-select-option>
              <a-select-option value="guangdong">
                广东省
              </a-select-option>
              <a-select-option value="jiangsu">
                江苏省
              </a-select-option>
              <a-select-option value="zhejiang">
                浙江省
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="证书文件" name="certFile">
            <a-upload
              name="file"
              :multiple="false"
              action="/api/upload"
              :show-upload-list="true"
              @change="handleCertFileChange"
            >
              <a-button>
                <upload-outlined /> 上传证书
              </a-button>
            </a-upload>
          </a-form-item>
        </template>

        <template v-else-if="currentIntegration.type === 'finance'">
          <a-form-item label="软件类型" name="software">
            <a-radio-group v-model:value="configForm.software">
              <a-radio value="yonyou">
                用友
              </a-radio>
              <a-radio value="kingdee">
                金蝶
              </a-radio>
              <a-radio value="other">
                其他
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="API地址" name="apiUrl">
            <a-input v-model:value="configForm.apiUrl" placeholder="请输入API地址" />
          </a-form-item>
          <a-form-item label="API密钥" name="apiKey">
            <a-input v-model:value="configForm.apiKey" placeholder="请输入API密钥" />
          </a-form-item>
          <a-form-item label="对接账套" name="accountSet">
            <a-input v-model:value="configForm.accountSet" placeholder="请输入对接账套名称" />
          </a-form-item>
        </template>

        <template v-else-if="currentIntegration.type === 'bank'">
          <a-form-item label="银行选择" name="bank">
            <a-select v-model:value="configForm.bank" placeholder="请选择银行">
              <a-select-option value="icbc">
                工商银行
              </a-select-option>
              <a-select-option value="abc">
                农业银行
              </a-select-option>
              <a-select-option value="boc">
                中国银行
              </a-select-option>
              <a-select-option value="ccb">
                建设银行
              </a-select-option>
              <a-select-option value="cmb">
                招商银行
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="企业网银账号" name="bankAccount">
            <a-input v-model:value="configForm.bankAccount" placeholder="请输入企业网银账号" />
          </a-form-item>
          <a-form-item label="企业网银密码" name="bankPassword">
            <a-input-password v-model:value="configForm.bankPassword" placeholder="请输入企业网银密码" />
          </a-form-item>
          <a-form-item label="同步周期" name="syncPeriod">
            <a-radio-group v-model:value="configForm.syncPeriod">
              <a-radio value="daily">
                每日
              </a-radio>
              <a-radio value="weekly">
                每周
              </a-radio>
              <a-radio value="monthly">
                每月
              </a-radio>
            </a-radio-group>
          </a-form-item>
        </template>

        <template v-else>
          <a-form-item label="API地址" name="apiUrl">
            <a-input v-model:value="configForm.apiUrl" placeholder="请输入API地址" />
          </a-form-item>
          <a-form-item label="API密钥" name="apiKey">
            <a-input v-model:value="configForm.apiKey" placeholder="请输入API密钥" />
          </a-form-item>
          <a-form-item label="认证方式" name="authType">
            <a-radio-group v-model:value="configForm.authType">
              <a-radio value="token">
                Token
              </a-radio>
              <a-radio value="oauth">
                OAuth 2.0
              </a-radio>
              <a-radio value="apikey">
                API Key
              </a-radio>
            </a-radio-group>
          </a-form-item>
        </template>

        <a-form-item>
          <a-button type="primary" :loading="submitLoading" @click="handleSubmitConfig">
            提交配置
          </a-button>
          <a-button style="margin-left: 8px" @click="configVisible = false">
            取消
          </a-button>
        </a-form-item>
      </a-form>
    </a-drawer>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

import {
  connectService,
  disconnectService,
  checkIntegrationStatus
} from '@/api/integration'

export default defineComponent({
  name: 'IntegrationList',
  components: {
    // Removing unused components
  },
  setup () {
    const router = useRouter()
    const loading = ref(false)
    const configFormRef = ref(null)
    const submitLoading = ref(false)
    const configVisible = ref(false)
    const currentIntegration = ref(null)

    // 集成列表
    const integrationList = ref([
      {
        title: '政务平台对接',
        type: 'gov',
        status: false,
        icon: 'GlobalOutlined',
        color: '#1890ff',
        description: '对接电子税务局、工商年报、信用中国等政务平台',
        loading: false
      },
      {
        title: '财务软件集成',
        type: 'finance',
        status: false,
        icon: 'CreditCardOutlined',
        color: '#52c41a',
        description: '与用友、金蝶等财务软件对接，自动同步会计数据',
        loading: false
      },
      {
        title: '银行系统对接',
        type: 'bank',
        status: false,
        icon: 'BankOutlined',
        color: '#722ed1',
        description: '连接企业网银系统，自动导入流水进行对账',
        loading: false
      },
      {
        title: '发票平台集成',
        type: 'invoice',
        status: false,
        icon: 'CloudServerOutlined',
        color: '#fa8c16',
        description: '与税控盘、发票管理平台集成，自动获取电子发票',
        loading: false
      },
      {
        title: '电商平台集成',
        type: 'ecommerce',
        status: false,
        icon: 'ShopOutlined',
        color: '#eb2f96',
        description: '对接电商平台API，自动同步订单、销售数据',
        loading: false
      },
      {
        title: '物流系统对接',
        type: 'logistics',
        status: false,
        icon: 'CarOutlined',
        color: '#faad14',
        description: '连接物流系统，关联运单与发票数据',
        loading: false
      }
    ])

    // 配置表单
    const configForm = reactive({
      username: '',
      password: '',
      region: undefined,
      certFile: undefined,
      software: 'yonyou',
      apiUrl: '',
      apiKey: '',
      accountSet: '',
      bank: undefined,
      bankAccount: '',
      bankPassword: '',
      syncPeriod: 'daily',
      authType: 'token'
    })

    // 表单验证规则
    const rules = {
      username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
      password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
      region: [{ required: true, message: '请选择区域', trigger: 'change' }],
      software: [{ required: true, message: '请选择软件类型', trigger: 'change' }],
      apiUrl: [{ required: true, message: '请输入API地址', trigger: 'blur' }],
      apiKey: [{ required: true, message: '请输入API密钥', trigger: 'blur' }],
      accountSet: [{ required: true, message: '请输入账套名称', trigger: 'blur' }],
      bank: [{ required: true, message: '请选择银行', trigger: 'change' }],
      bankAccount: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
      bankPassword: [{ required: true, message: '请输入银行密码', trigger: 'blur' }],
      syncPeriod: [{ required: true, message: '请选择同步周期', trigger: 'change' }],
      authType: [{ required: true, message: '请选择认证方式', trigger: 'change' }]
    }

    // 获取状态文本
    const getStatusText = (status) => {
      return status ? '已连接' : '未连接'
    }

    // 获取状态颜色
    const getStatusColor = (status) => {
      return status ? 'green' : 'red'
    }

    // 证书文件上传变更
    const handleCertFileChange = (info) => {
      if (info.file.status === 'done') {
        configForm.certFile = info.file.response.url
        message.success(`${info.file.name} 上传成功`)
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`)
      }
    }

    // 切换连接状态
    const toggleConnection = async (item) => {
      if (item.status) {
        // 断开连接
        try {
          item.loading = true
          await disconnectService(item.type)
          item.status = false
          message.success(`已断开${item.title}连接`)
        } catch (error) {
          console.error('断开连接失败:', error)
          message.error('断开连接失败，请稍后重试')
        } finally {
          item.loading = false
        }
      } else {
        // 建立连接 - 显示配置抽屉
        currentIntegration.value = item
        configVisible.value = true
      }
    }

    // 提交配置
    const handleSubmitConfig = async () => {
      try {
        if (!configFormRef.value) return

        await configFormRef.value.validate()
        submitLoading.value = true

        const item = currentIntegration.value
        item.loading = true

        const res = await connectService(item.type, configForm)

        if (res.code === 0) {
          item.status = true
          message.success(`${item.title}连接成功`)
          configVisible.value = false
        } else {
          message.error(res.message || '连接失败')
        }
      } catch (error) {
        console.error('提交配置失败:', error)
        message.error('提交配置失败，请检查表单填写是否正确')
      } finally {
        submitLoading.value = false
        if (currentIntegration.value) {
          currentIntegration.value.loading = false
        }
      }
    }

    // 查看详情
    const viewDetail = (type) => {
      router.push(`/integration/status/${type}`)
    }

    // 获取集成列表和状态
    const fetchIntegrationStatus = async () => {
      loading.value = true
      try {
        // 检查各系统集成状态
        for (const item of integrationList.value) {
          try {
            const res = await checkIntegrationStatus(item.type)
            item.status = res.data.connected || false
          } catch (error) {
            console.error(`检查${item.type}状态失败:`, error)
            item.status = false
          }
        }
      } catch (error) {
        console.error('获取集成状态失败:', error)
        message.error('获取集成状态失败')
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      fetchIntegrationStatus()
    })

    return {
      loading,
      integrationList,
      configVisible,
      currentIntegration,
      configForm,
      configFormRef,
      rules,
      submitLoading,
      getStatusText,
      getStatusColor,
      toggleConnection,
      handleSubmitConfig,
      handleCertFileChange,
      viewDetail
    }
  }
})
</script>

<style scoped>
.integration-list-container {
  padding: 0 12px;
}

.integration-card {
  margin-bottom: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.integration-info {
  padding: 0 12px;
}

.integration-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.integration-desc {
  margin-top: 8px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  height: 36px;
  overflow: hidden;
}

.integration-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}
</style>
