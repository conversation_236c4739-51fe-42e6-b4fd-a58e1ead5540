<template>
  <div class="invoice-integration-container">
    <a-card title="发票平台集成" :bordered="false">
      <a-alert
        message="发票平台集成配置"
        description="与税控盘、发票管理平台集成，自动获取电子发票、进行发票验真和识别"
        type="info"
        show-icon
        style="margin-bottom: 24px"
      />

      <!-- 连接状态卡片 -->
      <a-card class="status-card" :loading="loading">
        <template #extra>
          <a-button v-if="!connected" type="primary" @click="showConfigDrawer">
            配置连接
          </a-button>
          <a-button
            v-else
            type="danger"
            :loading="disconnectLoading"
            @click="handleDisconnect"
          >
            断开连接
          </a-button>
        </template>

        <a-descriptions title="连接状态" bordered :column="2">
          <a-descriptions-item label="连接状态" :span="1">
            <a-badge :status="connected ? 'success' : 'error'" :text="connected ? '已连接' : '未连接'" />
          </a-descriptions-item>
          <a-descriptions-item label="平台类型" :span="1">
            {{ getPlatformName(platformInfo.platformType) || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="最后同步时间" :span="1">
            {{ platformInfo.lastSyncTime || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="同步频率" :span="1">
            {{ getSyncPeriodText(platformInfo.syncPeriod) || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="税盘序列号" :span="2">
            {{ platformInfo.deviceSN || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 功能卡片区域 -->
      <a-row :gutter="16" style="margin-top: 24px">
        <a-col :span="8">
          <a-card :loading="loading" class="feature-card">
            <template #title>
              <span class="feature-title">
                <ScanOutlined class="feature-icon" />
                发票识别
              </span>
            </template>
            <div class="feature-content">
              <p>上传发票图片或PDF，自动识别发票内容并录入系统</p>
              <a-upload
                :file-list="[]"
                :multiple="true"
                :disabled="!connected"
                action="/api/invoices/recognize"
                @change="handleInvoiceUpload"
              >
                <a-button :disabled="!connected">
                  <UploadOutlined /> 上传发票
                </a-button>
              </a-upload>
            </div>
          </a-card>
        </a-col>

        <a-col :span="8">
          <a-card :loading="loading" class="feature-card">
            <template #title>
              <span class="feature-title">
                <CheckCircleOutlined class="feature-icon" />
                发票验真
              </span>
            </template>
            <div class="feature-content">
              <p>验证发票真伪，自动核对发票信息与税务局数据</p>
              <a-form layout="vertical">
                <a-form-item>
                  <a-input-group compact>
                    <a-input
                      v-model:value="verifyCode"
                      placeholder="输入发票代码或识别码"
                      style="width: 70%"
                      :disabled="!connected"
                    />
                    <a-button
                      type="primary"
                      :disabled="!connected || !verifyCode"
                      style="width: 30%"
                      @click="verifyInvoice"
                    >
                      验证
                    </a-button>
                  </a-input-group>
                </a-form-item>
              </a-form>
            </div>
          </a-card>
        </a-col>

        <a-col :span="8">
          <a-card :loading="loading" class="feature-card">
            <template #title>
              <span class="feature-title">
                <CloudDownloadOutlined class="feature-icon" />
                批量获取
              </span>
            </template>
            <div class="feature-content">
              <p>从税控平台批量获取发票数据，自动导入系统</p>
              <a-space>
                <a-button type="primary" :disabled="!connected" @click="showSyncModal">
                  立即同步
                </a-button>
                <a-button :disabled="!connected" @click="showImportDrawer">
                  导入设置
                </a-button>
              </a-space>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 发票数据区域 -->
      <a-card title="发票数据" style="margin-top: 24px" :loading="loading">
        <div class="table-toolbar">
          <a-space>
            <a-range-picker
              v-model:value="dateRange"
              style="width: 240px"
              :disabled="!connected"
            />
            <a-select
              v-model:value="invoiceType"
              style="width: 120px"
              :disabled="!connected"
            >
              <a-select-option value="all">
                所有类型
              </a-select-option>
              <a-select-option value="input">
                进项发票
              </a-select-option>
              <a-select-option value="output">
                销项发票
              </a-select-option>
            </a-select>
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="搜索发票内容"
              style="width: 200px"
              :disabled="!connected"
              @search="searchInvoices"
            />
          </a-space>

          <a-space>
            <a-tooltip title="刷新">
              <a-button type="link" @click="refreshInvoices">
                <ReloadOutlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="导出">
              <a-button type="link" :disabled="!connected" @click="exportInvoices">
                <DownloadOutlined />
              </a-button>
            </a-tooltip>
          </a-space>
        </div>

        <a-table
          :columns="invoiceColumns"
          :data-source="invoices"
          :loading="tableLoading"
          :pagination="{ pageSize: 10 }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'amount'">
              {{ formatAmount(record.amount) }}
            </template>
            <template v-else-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'operation'">
              <a-space>
                <a-button type="link" @click="viewInvoiceDetail(record)">
                  详情
                </a-button>
                <a-button v-if="record.status === 'unverified'" type="link" @click="verifyInvoiceById(record.id)">
                  验真
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 统计数据 -->
      <a-card title="发票统计" style="margin-top: 24px" :loading="loading">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic
              title="本月进项发票金额"
              :value="statistics.inputAmount"
              :precision="2"
              suffix="元"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="本月销项发票金额"
              :value="statistics.outputAmount"
              :precision="2"
              suffix="元"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="累计识别发票"
              :value="statistics.totalCount"
              suffix="张"
            />
          </a-col>
        </a-row>
      </a-card>
    </a-card>

    <!-- 配置抽屉 -->
    <a-drawer
      title="发票平台连接配置"
      :visible="configVisible"
      :width="500"
      @close="configVisible = false"
    >
      <a-form
        ref="configFormRef"
        :model="configForm"
        :rules="rules"
        layout="vertical"
      >
        <a-form-item label="平台类型" name="platformType">
          <a-select v-model:value="configForm.platformType" placeholder="请选择平台类型">
            <a-select-option value="ukey">
              税务UKey直连
            </a-select-option>
            <a-select-option value="aisino">
              航天金税盘
            </a-select-option>
            <a-select-option value="vpn">
              税务VPN通道
            </a-select-option>
            <a-select-option value="third">
              第三方开票平台
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item v-if="['ukey', 'aisino'].includes(configForm.platformType)" label="税盘序列号" name="deviceSN">
          <a-input v-model:value="configForm.deviceSN" placeholder="请输入税盘序列号" />
        </a-form-item>

        <a-form-item v-if="['ukey', 'aisino'].includes(configForm.platformType)" label="税盘密码" name="devicePassword">
          <a-input-password v-model:value="configForm.devicePassword" placeholder="请输入税盘密码" />
        </a-form-item>

        <a-form-item v-if="['vpn', 'third'].includes(configForm.platformType)" label="平台地址" name="serverUrl">
          <a-input v-model:value="configForm.serverUrl" placeholder="请输入平台连接地址" />
        </a-form-item>

        <a-form-item v-if="['vpn', 'third'].includes(configForm.platformType)" label="开票账号" name="username">
          <a-input v-model:value="configForm.username" placeholder="请输入开票账号" />
        </a-form-item>

        <a-form-item v-if="['vpn', 'third'].includes(configForm.platformType)" label="开票密码" name="password">
          <a-input-password v-model:value="configForm.password" placeholder="请输入开票密码" />
        </a-form-item>

        <a-form-item label="企业税号" name="taxNumber">
          <a-input v-model:value="configForm.taxNumber" placeholder="请输入企业税号" />
        </a-form-item>

        <a-form-item label="同步周期" name="syncPeriod">
          <a-radio-group v-model:value="configForm.syncPeriod">
            <a-radio value="realtime">
              实时同步
            </a-radio>
            <a-radio value="hourly">
              每小时
            </a-radio>
            <a-radio value="daily">
              每天
            </a-radio>
            <a-radio value="manual">
              手动同步
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="自动识别设置">
          <a-checkbox-group v-model:value="configForm.autoSettings">
            <a-checkbox value="autoRecognize">
              自动识别新获取发票
            </a-checkbox>
            <a-checkbox value="autoVerify">
              自动验证发票真伪
            </a-checkbox>
            <a-checkbox value="autoImport">
              自动导入财务系统
            </a-checkbox>
            <a-checkbox value="notifyNew">
              新发票通知
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" :loading="submitLoading" @click="handleSubmitConfig">
            建立连接
          </a-button>
          <a-button style="margin-left: 8px" @click="configVisible = false">
            取消
          </a-button>
        </a-form-item>
      </a-form>
    </a-drawer>

    <!-- 同步模态框 -->
    <a-modal
      title="发票数据同步"
      :visible="syncModalVisible"
      :footer="null"
      @cancel="syncModalVisible = false"
    >
      <a-form layout="vertical">
        <a-form-item label="选择同步时间范围">
          <a-radio-group v-model:value="syncForm.dateRange">
            <a-radio value="today">
              今天
            </a-radio>
            <a-radio value="yesterday">
              昨天
            </a-radio>
            <a-radio value="thisWeek">
              本周
            </a-radio>
            <a-radio value="lastWeek">
              上周
            </a-radio>
            <a-radio value="thisMonth">
              本月
            </a-radio>
            <a-radio value="lastMonth">
              上月
            </a-radio>
            <a-radio value="custom">
              自定义
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="syncForm.dateRange === 'custom'">
          <a-range-picker v-model:value="syncForm.customDateRange" style="width: 100%" />
        </a-form-item>

        <a-form-item label="发票类型">
          <a-checkbox-group v-model:value="syncForm.invoiceTypes">
            <a-checkbox value="input">
              进项发票
            </a-checkbox>
            <a-checkbox value="output">
              销项发票
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item label="同步后自动处理">
          <a-checkbox-group v-model:value="syncForm.autoActions">
            <a-checkbox value="autoRecognize">
              自动识别
            </a-checkbox>
            <a-checkbox value="autoVerify">
              自动验真
            </a-checkbox>
            <a-checkbox value="autoImport">
              自动导入财务系统
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" :loading="syncLoading" @click="startSync">
            开始同步
          </a-button>
          <a-button style="margin-left: 8px" @click="syncModalVisible = false">
            取消
          </a-button>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 导入设置抽屉 -->
    <a-drawer
      title="发票导入设置"
      :visible="importDrawerVisible"
      :width="400"
      @close="importDrawerVisible = false"
    >
      <a-form layout="vertical">
        <a-form-item label="自动导入设置">
          <a-checkbox-group v-model:value="importSettings.autoImport">
            <a-checkbox value="input">
              自动导入进项发票
            </a-checkbox>
            <a-checkbox value="output">
              自动导入销项发票
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item label="导入规则">
          <a-radio-group v-model:value="importSettings.importRule">
            <a-radio value="all">
              导入所有新发票
            </a-radio>
            <a-radio value="verified">
              仅导入已验真发票
            </a-radio>
            <a-radio value="manual">
              手动确认后导入
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="导入周期">
          <a-radio-group v-model:value="importSettings.importPeriod">
            <a-radio value="realtime">
              实时导入
            </a-radio>
            <a-radio value="daily">
              每日导入
            </a-radio>
            <a-radio value="weekly">
              每周导入
            </a-radio>
            <a-radio value="manual">
              手动导入
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="importSettings.importPeriod !== 'manual' && importSettings.importPeriod !== 'realtime'">
          <a-time-picker
            v-model:value="importSettings.importTime"
            format="HH:mm"
            placeholder="设置导入时间"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="自动匹配规则">
          <a-checkbox-group v-model:value="importSettings.matchRules">
            <a-checkbox value="buyer">
              匹配购买方信息
            </a-checkbox>
            <a-checkbox value="seller">
              匹配销售方信息
            </a-checkbox>
            <a-checkbox value="amount">
              匹配金额
            </a-checkbox>
            <a-checkbox value="date">
              匹配日期
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" @click="saveImportSettings">
            保存设置
          </a-button>
        </a-form-item>
      </a-form>
    </a-drawer>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ScanOutlined,
  CheckCircleOutlined,
  CloudDownloadOutlined,
  UploadOutlined,
  ReloadOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'InvoiceIntegration',
  components: {
    ScanOutlined,
    CheckCircleOutlined,
    CloudDownloadOutlined,
    UploadOutlined,
    ReloadOutlined,
    DownloadOutlined
  },
  setup () {
    const loading = ref(false)
    const tableLoading = ref(false)
    const configFormRef = ref(null)
    const submitLoading = ref(false)
    const disconnectLoading = ref(false)
    const syncLoading = ref(false)

    const configVisible = ref(false)
    const syncModalVisible = ref(false)
    const importDrawerVisible = ref(false)
    const connected = ref(false)

    // 验证发票代码
    const verifyCode = ref('')

    // 发票平台信息
    const platformInfo = reactive({
      platformType: '',
      lastSyncTime: '',
      syncPeriod: '',
      deviceSN: ''
    })

    // 发票筛选
    const dateRange = ref([])
    const invoiceType = ref('all')
    const searchKeyword = ref('')

    // 配置表单
    const configForm = reactive({
      platformType: undefined,
      deviceSN: '',
      devicePassword: '',
      serverUrl: '',
      username: '',
      password: '',
      taxNumber: '',
      syncPeriod: 'daily',
      autoSettings: ['autoRecognize', 'notifyNew']
    })

    // 同步表单
    const syncForm = reactive({
      dateRange: 'thisMonth',
      customDateRange: [],
      invoiceTypes: ['input', 'output'],
      autoActions: ['autoRecognize', 'autoVerify']
    })

    // 导入设置
    const importSettings = reactive({
      autoImport: ['input'],
      importRule: 'verified',
      importPeriod: 'daily',
      importTime: null,
      matchRules: ['buyer', 'seller', 'amount']
    })

    // 表单验证规则
    const rules = {
      platformType: [{ required: true, message: '请选择平台类型', trigger: 'change' }],
      deviceSN: [{ required: true, message: '请输入税盘序列号', trigger: 'blur' }],
      devicePassword: [{ required: true, message: '请输入税盘密码', trigger: 'blur' }],
      serverUrl: [{ required: true, message: '请输入平台地址', trigger: 'blur' }],
      username: [{ required: true, message: '请输入开票账号', trigger: 'blur' }],
      password: [{ required: true, message: '请输入开票密码', trigger: 'blur' }],
      taxNumber: [{ required: true, message: '请输入企业税号', trigger: 'blur' }],
      syncPeriod: [{ required: true, message: '请选择同步周期', trigger: 'change' }]
    }

    // 发票列表列定义
    const invoiceColumns = [
      {
        title: '发票类型',
        dataIndex: 'invoiceType',
        key: 'invoiceType',
        width: 120
      },
      {
        title: '发票代码',
        dataIndex: 'invoiceCode',
        key: 'invoiceCode',
        width: 140
      },
      {
        title: '发票号码',
        dataIndex: 'invoiceNumber',
        key: 'invoiceNumber',
        width: 120
      },
      {
        title: '开票日期',
        dataIndex: 'invoiceDate',
        key: 'invoiceDate',
        width: 100
      },
      {
        title: '金额(元)',
        dataIndex: 'amount',
        key: 'amount',
        width: 120,
        align: 'right'
      },
      {
        title: '购买方',
        dataIndex: 'buyerName',
        key: 'buyerName',
        width: 180
      },
      {
        title: '销售方',
        dataIndex: 'sellerName',
        key: 'sellerName',
        width: 180
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
      },
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        width: 120,
        fixed: 'right'
      }
    ]

    // 发票数据
    const invoices = ref([])

    // 发票统计数据
    const statistics = reactive({
      inputAmount: 0,
      outputAmount: 0,
      totalCount: 0
    })

    // 获取平台名称
    const getPlatformName = (type) => {
      const platformMap = {
        ukey: '税务UKey直连',
        aisino: '航天金税盘',
        vpn: '税务VPN通道',
        third: '第三方开票平台'
      }
      return platformMap[type] || type
    }

    // 获取同步周期文本
    const getSyncPeriodText = (period) => {
      const periodMap = {
        realtime: '实时同步',
        hourly: '每小时',
        daily: '每天',
        manual: '手动同步'
      }
      return periodMap[period] || period
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        verified: '已验真',
        unverified: '未验真',
        invalid: '异常',
        imported: '已导入'
      }
      return statusMap[status] || status
    }

    // 获取状态颜色
    const getStatusColor = (status) => {
      const colorMap = {
        verified: 'green',
        unverified: 'orange',
        invalid: 'red',
        imported: 'blue'
      }
      return colorMap[status] || 'default'
    }

    // 格式化金额
    const formatAmount = (amount) => {
      return parseFloat(amount).toFixed(2)
    }

    // 显示配置抽屉
    const showConfigDrawer = () => {
      configVisible.value = true
    }

    // 显示同步模态框
    const showSyncModal = () => {
      syncModalVisible.value = true
    }

    // 显示导入设置抽屉
    const showImportDrawer = () => {
      importDrawerVisible.value = true
    }

    // 提交配置
    const handleSubmitConfig = async () => {
      try {
        if (!configFormRef.value) return

        await configFormRef.value.validate()
        submitLoading.value = true

        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1500))

        message.success('发票平台连接成功')
        configVisible.value = false

        // 模拟连接成功后的状态更新
        connected.value = true
        Object.assign(platformInfo, {
          platformType: configForm.platformType,
          lastSyncTime: new Date().toLocaleString(),
          syncPeriod: configForm.syncPeriod,
          deviceSN: configForm.deviceSN || '税务UKey-458933126'
        })

        // 获取发票数据
        refreshInvoices()
      } catch (error) {
        console.error('提交配置失败:', error)
        message.error('连接失败，请检查表单填写是否正确')
      } finally {
        submitLoading.value = false
      }
    }

    // 断开连接
    const handleDisconnect = async () => {
      try {
        disconnectLoading.value = true

        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1000))

        message.success('已断开发票平台连接')
        connected.value = false

        // 清空数据
        Object.assign(platformInfo, {
          platformType: '',
          lastSyncTime: '',
          syncPeriod: '',
          deviceSN: ''
        })
        invoices.value = []
        Object.assign(statistics, {
          inputAmount: 0,
          outputAmount: 0,
          totalCount: 0
        })
      } catch (error) {
        console.error('断开连接失败:', error)
        message.error('断开连接失败，请稍后重试')
      } finally {
        disconnectLoading.value = false
      }
    }

    // 开始同步
    const startSync = async () => {
      try {
        syncLoading.value = true

        // TODO: 实现真实的发票同步API调用
        // const response = await syncInvoicesFromPlatform(syncForm)

        // 暂时模拟API请求，实际应该调用真实API
        await new Promise(resolve => setTimeout(resolve, 2000))

        message.success('发票数据同步成功')
        syncModalVisible.value = false

        // 更新最后同步时间
        platformInfo.lastSyncTime = new Date().toLocaleString()

        // 重新获取发票数据
        refreshInvoices()
      } catch (error) {
        console.error('同步失败:', error)
        message.error('同步失败，请稍后重试')
      } finally {
        syncLoading.value = false
      }
    }

    // 发票上传处理
    const handleInvoiceUpload = (info) => {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功，识别中...`)
        // TODO: 实现真实的发票识别API调用
        // 暂时模拟识别过程，实际应该调用真实API
        setTimeout(() => {
          message.success('发票识别成功')
          refreshInvoices()
        }, 1500)
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`)
      }
    }

    // 验证发票
    const verifyInvoice = async () => {
      if (!verifyCode.value) {
        message.warning('请输入发票代码或识别码')
        return
      }

      try {
        message.loading('验证中...')

        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1500))

        message.success('发票验证成功，验证结果：真')
      } catch (error) {
        console.error('验证失败:', error)
        message.error('验证失败，请检查发票代码是否正确')
      }
    }

    // 根据ID验证发票
    const verifyInvoiceById = async (id) => {
      try {
        message.loading('验证中...')

        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1500))

        message.success('发票验证成功')

        // 更新发票状态
        const index = invoices.value.findIndex(item => item.id === id)
        if (index !== -1) {
          invoices.value[index].status = 'verified'
        }
      } catch (error) {
        console.error('验证失败:', error)
        message.error('验证失败，请稍后重试')
      }
    }

    // 刷新发票列表
    const refreshInvoices = async () => {
      tableLoading.value = true
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 模拟发票数据
        invoices.value = [
          {
            id: '1',
            invoiceType: '增值税专票',
            invoiceCode: '1234567890',
            invoiceNumber: '12345678',
            invoiceDate: '2023-10-15',
            amount: 10000.00,
            buyerName: '北京科技有限公司',
            sellerName: '上海贸易有限公司',
            status: 'verified'
          },
          {
            id: '2',
            invoiceType: '增值税普票',
            invoiceCode: '0987654321',
            invoiceNumber: '87654321',
            invoiceDate: '2023-10-18',
            amount: 5320.50,
            buyerName: '北京科技有限公司',
            sellerName: '广州电子科技有限公司',
            status: 'unverified'
          },
          {
            id: '3',
            invoiceType: '增值税电子票',
            invoiceCode: '5432167890',
            invoiceNumber: '56781234',
            invoiceDate: '2023-10-20',
            amount: 2800.00,
            buyerName: '深圳物流有限公司',
            sellerName: '北京科技有限公司',
            status: 'imported'
          },
          {
            id: '4',
            invoiceType: '增值税专票',
            invoiceCode: '1122334455',
            invoiceNumber: '12312312',
            invoiceDate: '2023-10-25',
            amount: 15800.00,
            buyerName: '北京科技有限公司',
            sellerName: '杭州电子有限公司',
            status: 'unverified'
          },
          {
            id: '5',
            invoiceType: '增值税普票',
            invoiceCode: '6677889900',
            invoiceNumber: '98765432',
            invoiceDate: '2023-10-28',
            amount: 960.00,
            buyerName: '北京科技有限公司',
            sellerName: '江苏工业设备有限公司',
            status: 'invalid'
          }
        ]

        // 更新统计数据
        Object.assign(statistics, {
          inputAmount: 34880.50,
          outputAmount: 2800.00,
          totalCount: 125
        })
      } catch (error) {
        console.error('获取发票数据失败:', error)
        message.error('获取发票数据失败')
      } finally {
        tableLoading.value = false
      }
    }

    // 搜索发票
    const searchInvoices = () => {
      tableLoading.value = true
      setTimeout(() => {
        tableLoading.value = false
        message.success(`搜索关键词: ${searchKeyword.value}`)
      }, 500)
    }

    // 导出发票
    const exportInvoices = () => {
      message.success('发票数据导出成功')
    }

    // 查看发票详情
    const viewInvoiceDetail = (record) => {
      message.info(`查看发票详情：${record.invoiceCode}-${record.invoiceNumber}`)
    }

    // 保存导入设置
    const saveImportSettings = () => {
      message.success('导入设置保存成功')
      importDrawerVisible.value = false
    }

    onMounted(() => {
      // 模拟初始检查连接状态
      loading.value = true
      setTimeout(() => {
        loading.value = false
        connected.value = false // 默认未连接
      }, 1000)
    })

    return {
      loading,
      tableLoading,
      configFormRef,
      submitLoading,
      disconnectLoading,
      syncLoading,
      configVisible,
      syncModalVisible,
      importDrawerVisible,
      connected,
      verifyCode,
      platformInfo,
      dateRange,
      invoiceType,
      searchKeyword,
      configForm,
      syncForm,
      importSettings,
      rules,
      invoiceColumns,
      invoices,
      statistics,
      getPlatformName,
      getSyncPeriodText,
      getStatusText,
      getStatusColor,
      formatAmount,
      showConfigDrawer,
      showSyncModal,
      showImportDrawer,
      handleSubmitConfig,
      handleDisconnect,
      startSync,
      handleInvoiceUpload,
      verifyInvoice,
      verifyInvoiceById,
      refreshInvoices,
      searchInvoices,
      exportInvoices,
      viewInvoiceDetail,
      saveImportSettings
    }
  }
})
</script>

<style scoped>
.invoice-integration-container {
  padding: 0 12px;
}

.status-card {
  margin-bottom: 24px;
}

.feature-card {
  height: 100%;
}

.feature-title {
  display: flex;
  align-items: center;
}

.feature-icon {
  margin-right: 8px;
  font-size: 16px;
}

.feature-content {
  padding: 12px 0;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
</style>
