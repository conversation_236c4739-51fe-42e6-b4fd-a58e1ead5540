<template>
  <div class="logistics-container">
    <a-page-header title="物流系统集成" :ghost="false">
      <template #extra>
        <a-button type="primary" @click="syncModal = true">
          <template #icon>
            <SyncOutlined />
          </template>
          同步物流数据
        </a-button>
      </template>
      <a-alert
        v-if="!connectionStatus.connected"
        message="您尚未配置物流系统连接，请完成配置以启用物流数据同步功能"
        type="warning"
        show-icon
        style="margin-bottom: 16px"
      />
    </a-page-header>

    <a-row :gutter="16">
      <!-- 连接状态 -->
      <a-col :span="24">
        <a-card title="连接状态" :bordered="false">
          <a-descriptions :column="3">
            <a-descriptions-item label="连接状态">
              <a-badge
                :status="connectionStatus.connected ? 'success' : 'error'"
                :text="connectionStatus.connected ? '已连接' : '未连接'"
              />
            </a-descriptions-item>
            <a-descriptions-item label="物流系统">
              {{ connectionStatus.systemName || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="最近同步时间">
              {{ connectionStatus.lastSyncTime || '-' }}
            </a-descriptions-item>
          </a-descriptions>

          <a-divider />

          <div class="action-buttons">
            <a-button
              v-if="!connectionStatus.connected"
              type="primary"
              @click="showConnectionModal"
            >
              配置连接
            </a-button>
            <a-button
              v-else
              danger
              @click="handleDisconnect"
            >
              断开连接
            </a-button>
            <a-button
              v-if="connectionStatus.connected"
              @click="routeToStatus"
            >
              详细状态
            </a-button>
          </div>
        </a-card>
      </a-col>

      <!-- 物流追踪 -->
      <a-col :span="24" style="margin-top: 16px">
        <a-card v-if="connectionStatus.connected" title="物流数据追踪" :bordered="false">
          <a-form layout="inline" :model="trackingForm">
            <a-form-item label="物流单号">
              <a-input
                v-model:value="trackingForm.trackingNumber"
                placeholder="请输入物流单号"
                allow-clear
              />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="handleTracking">
                <template #icon>
                  <SearchOutlined />
                </template>
                查询
              </a-button>
            </a-form-item>
          </a-form>

          <div v-if="trackingInfo.hasResult">
            <a-divider />
            <a-descriptions title="物流信息" :column="2" bordered>
              <a-descriptions-item label="物流单号">
                {{ trackingInfo.trackingNumber }}
              </a-descriptions-item>
              <a-descriptions-item label="物流公司">
                {{ trackingInfo.company }}
              </a-descriptions-item>
              <a-descriptions-item label="发货时间">
                {{ trackingInfo.sendTime }}
              </a-descriptions-item>
              <a-descriptions-item label="状态">
                <a-tag :color="getTrackingStatusColor(trackingInfo.status)">
                  {{ trackingInfo.statusText }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="预计送达">
                {{ trackingInfo.estimatedDelivery }}
              </a-descriptions-item>
              <a-descriptions-item label="收件人">
                {{ trackingInfo.receiver }}
              </a-descriptions-item>
            </a-descriptions>

            <a-divider orientation="left">
              物流动态
            </a-divider>
            <a-timeline mode="left">
              <a-timeline-item
                v-for="(item, index) in trackingInfo.traces"
                :key="index"
                :color="index === 0 ? 'green' : 'gray'"
              >
                <template v-if="index === 0" #dot>
                  <CheckCircleFilled />
                </template>
                <p>{{ item.time }}</p>
                <p>{{ item.content }}</p>
              </a-timeline-item>
            </a-timeline>
          </div>

          <a-empty v-else-if="trackingInfo.searched" description="未查询到物流信息" />
        </a-card>

        <a-card v-else title="物流数据追踪" :bordered="false">
          <a-empty description="请先配置物流系统连接" />
        </a-card>
      </a-col>

      <!-- 同步记录 -->
      <a-col :span="24" style="margin-top: 16px">
        <a-card title="同步记录" :bordered="false">
          <a-empty v-if="!connectionStatus.connected" description="请先配置物流系统连接" />

          <a-table
            v-else
            :columns="syncColumns"
            :data-source="syncRecords"
            :pagination="{ pageSize: 10 }"
            :loading="tableLoading"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'status'">
                <a-tag :color="getSyncStatusColor(record.status)">
                  {{ record.statusText }}
                </a-tag>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <a @click="viewSyncDetail(record)">详情</a>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>

      <!-- 数据概览 -->
      <a-col :span="24" style="margin-top: 16px">
        <a-card title="物流数据概览" :bordered="false">
          <a-empty v-if="!connectionStatus.connected" description="请先配置物流系统连接" />

          <a-row v-else :gutter="16">
            <a-col :span="6">
              <a-statistic
                title="今日发货"
                :value="statistics.todayShipped"
                :precision="0"
              >
                <template #prefix>
                  <CarOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="运输中"
                :value="statistics.inTransit"
                :precision="0"
              >
                <template #prefix>
                  <LoadingOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="已完成"
                :value="statistics.completed"
                :precision="0"
              >
                <template #prefix>
                  <CheckOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="异常订单"
                :value="statistics.exception"
                :precision="0"
                :value-style="{ color: statistics.exception > 0 ? '#cf1322' : '' }"
              >
                <template #prefix>
                  <WarningOutlined />
                </template>
              </a-statistic>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>

    <!-- 连接配置模态框 -->
    <a-modal
      v-model:visible="connectionModal"
      title="物流系统连接配置"
      :confirm-loading="confirmLoading"
      @ok="handleConnect"
    >
      <a-form
        ref="connectionFormRef"
        :model="connectionForm"
        :rules="connectionRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="物流系统类型" name="systemType">
          <a-select v-model:value="connectionForm.systemType">
            <a-select-option value="sf">
              顺丰物流
            </a-select-option>
            <a-select-option value="sto">
              申通快递
            </a-select-option>
            <a-select-option value="yto">
              圆通速递
            </a-select-option>
            <a-select-option value="zto">
              中通快递
            </a-select-option>
            <a-select-option value="yd">
              韵达快递
            </a-select-option>
            <a-select-option value="ems">
              EMS
            </a-select-option>
            <a-select-option value="other">
              其他物流系统
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="接口地址" name="apiUrl">
          <a-input v-model:value="connectionForm.apiUrl" placeholder="请输入物流系统API地址" />
        </a-form-item>

        <a-form-item label="AppKey" name="appKey">
          <a-input v-model:value="connectionForm.appKey" placeholder="请输入应用的AppKey" />
        </a-form-item>

        <a-form-item label="AppSecret" name="appSecret">
          <a-input-password v-model:value="connectionForm.appSecret" placeholder="请输入应用的AppSecret" />
        </a-form-item>

        <a-form-item v-if="['sf', 'sto', 'ems'].includes(connectionForm.systemType)" label="客户编码" name="customerCode">
          <a-input v-model:value="connectionForm.customerCode" placeholder="请输入客户编码" />
        </a-form-item>

        <a-form-item label="同步频率" name="syncFrequency">
          <a-select v-model:value="connectionForm.syncFrequency">
            <a-select-option value="10">
              每10分钟
            </a-select-option>
            <a-select-option value="30">
              每30分钟
            </a-select-option>
            <a-select-option value="60">
              每小时
            </a-select-option>
            <a-select-option value="360">
              每6小时
            </a-select-option>
            <a-select-option value="720">
              每12小时
            </a-select-option>
            <a-select-option value="1440">
              每天
            </a-select-option>
            <a-select-option value="manual">
              手动同步
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="自动推送订阅" name="autoNotify">
          <a-switch v-model:checked="connectionForm.autoNotify" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 同步模态框 -->
    <a-modal
      v-model:visible="syncModal"
      title="物流数据同步"
      :confirm-loading="syncLoading"
      @ok="handleSync"
    >
      <a-form
        ref="syncFormRef"
        :model="syncForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="同步类型" name="syncType">
          <a-radio-group v-model:value="syncForm.syncType">
            <a-radio value="all">
              全部订单
            </a-radio>
            <a-radio value="unfinished">
              未完成订单
            </a-radio>
            <a-radio value="time">
              按时间范围
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="syncForm.syncType === 'time'" label="时间范围">
          <a-range-picker
            v-model:value="syncForm.timeRange"
            show-time
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="同步模式">
          <a-radio-group v-model:value="syncForm.mode">
            <a-radio value="incremental">
              增量同步
            </a-radio>
            <a-radio value="full">
              全量同步
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  SyncOutlined,
  SearchOutlined,
  CarOutlined,
  LoadingOutlined,
  CheckOutlined,
  WarningOutlined,
  CheckCircleFilled
} from '@ant-design/icons-vue'
import {
  checkIntegrationStatus,
  connectService,
  disconnectService
} from '@/api/integration'

export default defineComponent({
  name: 'LogisticsIntegration',
  components: {
    SyncOutlined,
    SearchOutlined,
    CarOutlined,
    LoadingOutlined,
    CheckOutlined,
    WarningOutlined,
    CheckCircleFilled
  },
  setup () {
    const router = useRouter()
    const connectionFormRef = ref(null)
    const syncFormRef = ref(null)

    // 连接状态
    const connectionStatus = reactive({
      connected: false,
      systemName: '',
      systemType: '',
      lastSyncTime: ''
    })

    // 表格加载状态
    const tableLoading = ref(false)

    // 连接配置模态框
    const connectionModal = ref(false)
    const confirmLoading = ref(false)

    // 连接表单
    const connectionForm = reactive({
      systemType: 'sf',
      apiUrl: '',
      appKey: '',
      appSecret: '',
      customerCode: '',
      syncFrequency: '60',
      autoNotify: true
    })

    // 连接表单规则
    const connectionRules = {
      systemType: [{ required: true, message: '请选择物流系统类型' }],
      apiUrl: [{ required: true, message: '请输入接口地址', trigger: 'blur' }],
      appKey: [{ required: true, message: '请输入AppKey', trigger: 'blur' }],
      appSecret: [{ required: true, message: '请输入AppSecret', trigger: 'blur' }],
      syncFrequency: [{ required: true, message: '请选择同步频率' }]
    }

    // 同步模态框
    const syncModal = ref(false)
    const syncLoading = ref(false)

    // 同步表单
    const syncForm = reactive({
      syncType: 'unfinished',
      timeRange: [],
      mode: 'incremental'
    })

    // 物流追踪表单
    const trackingForm = reactive({
      trackingNumber: '',
      company: ''
    })

    // 物流追踪信息
    const trackingInfo = reactive({
      hasResult: false,
      searched: false,
      trackingNumber: '',
      company: '',
      status: '',
      statusText: '',
      sendTime: '',
      estimatedDelivery: '',
      receiver: '',
      traces: []
    })

    // 同步记录列定义
    const syncColumns = [
      { title: '同步时间', dataIndex: 'syncTime', key: 'syncTime', width: 180 },
      { title: '同步类型', dataIndex: 'syncType', key: 'syncType', width: 120 },
      { title: '处理订单数', dataIndex: 'orderCount', key: 'orderCount', width: 120 },
      { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
      { title: '耗时', dataIndex: 'duration', key: 'duration', width: 100 },
      { title: '操作人', dataIndex: 'operator', key: 'operator', width: 120 },
      { title: '操作', dataIndex: 'action', key: 'action', width: 80 }
    ]

    // 同步记录数据
    const syncRecords = ref([])

    // 统计数据
    const statistics = reactive({
      todayShipped: 0,
      inTransit: 0,
      completed: 0,
      exception: 0
    })

    // 获取集成状态
    const fetchConnectionStatus = async () => {
      try {
        const res = await checkIntegrationStatus('logistics')
        if (res.code === 0 && res.data) {
          const systemNames = {
            sf: '顺丰物流',
            sto: '申通快递',
            yto: '圆通速递',
            zto: '中通快递',
            yd: '韵达快递',
            ems: 'EMS',
            other: '其他物流系统'
          }

          Object.assign(connectionStatus, {
            connected: res.data.connected || false,
            systemType: res.data.systemType || '',
            systemName: res.data.systemType ? (systemNames[res.data.systemType] || res.data.systemName) : '',
            lastSyncTime: res.data.lastSyncTime || ''
          })

          if (connectionStatus.connected) {
            fetchSyncRecords()
            fetchStatistics()
          }
        }
      } catch (error) {
        console.error('获取连接状态失败:', error)
        message.error('获取连接状态失败')
      }
    }

    // 显示连接配置对话框
    const showConnectionModal = () => {
      connectionModal.value = true
    }

    // 连接服务
    const handleConnect = () => {
      connectionFormRef.value.validate().then(async () => {
        confirmLoading.value = true
        try {
          const res = await connectService('logistics', connectionForm)
          if (res.code === 0) {
            message.success('连接物流系统成功')
            connectionModal.value = false
            fetchConnectionStatus()
          } else {
            message.error(res.message || '连接失败')
          }
        } catch (error) {
          console.error('连接失败:', error)
          message.error('连接物流系统失败')
        } finally {
          confirmLoading.value = false
        }
      })
    }

    // 断开连接
    const handleDisconnect = async () => {
      try {
        const res = await disconnectService('logistics')
        if (res.code === 0) {
          message.success('已断开物流系统连接')
          Object.assign(connectionStatus, {
            connected: false,
            systemName: '',
            systemType: '',
            lastSyncTime: ''
          })
        } else {
          message.error(res.message || '断开连接失败')
        }
      } catch (error) {
        console.error('断开连接失败:', error)
        message.error('断开物流系统连接失败')
      }
    }

    // 同步数据
    const handleSync = () => {
      syncFormRef.value.validate().then(async () => {
        syncLoading.value = true
        try {
          // 模拟同步API调用
          await new Promise(resolve => setTimeout(resolve, 1500))
          message.success('数据同步任务已提交，请稍后查看同步结果')
          syncModal.value = false

          // 刷新数据
          setTimeout(() => {
            fetchSyncRecords()
            fetchStatistics()
          }, 1000)
        } catch (error) {
          console.error('同步失败:', error)
          message.error('数据同步失败')
        } finally {
          syncLoading.value = false
        }
      })
    }

    // 查询物流
    const handleTracking = async () => {
      if (!trackingForm.trackingNumber) {
        message.warning('请输入物流单号')
        return
      }

      try {
        message.loading('正在查询物流信息...')

        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 模拟查询结果
        if (trackingForm.trackingNumber.length > 5) {
          Object.assign(trackingInfo, {
            hasResult: true,
            searched: true,
            trackingNumber: trackingForm.trackingNumber,
            company: '顺丰速运',
            status: 'in_transit',
            statusText: '运输中',
            sendTime: '2023-11-05 10:30:00',
            estimatedDelivery: '2023-11-07',
            receiver: '张先生 (1388****888)',
            traces: [
              {
                time: '2023-11-05 16:30:00',
                content: '【广州市】快件已从广州转运中心发出，正在发往北京转运中心'
              },
              {
                time: '2023-11-05 15:40:00',
                content: '【广州市】快件已到达广州转运中心'
              },
              {
                time: '2023-11-05 13:20:00',
                content: '【广州市】快件已从广州天河区公司发出，正在发往广州转运中心'
              },
              {
                time: '2023-11-05 11:15:00',
                content: '【广州市】广州天河区公司已收件，取件员: 李师傅，电话: 1388****777'
              },
              {
                time: '2023-11-05 10:30:00',
                content: '【广州市】客户已下单，等待揽收'
              }
            ]
          })
          message.success('查询成功')
        } else {
          Object.assign(trackingInfo, {
            hasResult: false,
            searched: true,
            trackingNumber: '',
            company: '',
            status: '',
            statusText: '',
            sendTime: '',
            estimatedDelivery: '',
            receiver: '',
            traces: []
          })
          message.info('未查询到物流信息')
        }
      } catch (error) {
        console.error('查询物流失败:', error)
        message.error('查询物流信息失败')
      }
    }

    // 获取同步记录
    const fetchSyncRecords = async () => {
      tableLoading.value = true

      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 800))

        syncRecords.value = [
          {
            key: '1',
            syncTime: '2023-11-05 16:30:00',
            syncType: '增量同步',
            orderCount: 28,
            status: 'success',
            statusText: '成功',
            duration: '45秒',
            operator: '系统'
          },
          {
            key: '2',
            syncTime: '2023-11-05 10:30:00',
            syncType: '全量同步',
            orderCount: 156,
            status: 'success',
            statusText: '成功',
            duration: '3分25秒',
            operator: '管理员'
          },
          {
            key: '3',
            syncTime: '2023-11-04 16:30:00',
            syncType: '增量同步',
            orderCount: 37,
            status: 'success',
            statusText: '成功',
            duration: '50秒',
            operator: '系统'
          },
          {
            key: '4',
            syncTime: '2023-11-04 10:30:00',
            syncType: '增量同步',
            orderCount: 42,
            status: 'warning',
            statusText: '部分成功',
            duration: '1分15秒',
            operator: '系统'
          },
          {
            key: '5',
            syncTime: '2023-11-03 16:30:00',
            syncType: '增量同步',
            orderCount: 31,
            status: 'success',
            statusText: '成功',
            duration: '42秒',
            operator: '系统'
          }
        ]
      } catch (error) {
        console.error('获取同步记录失败:', error)
        message.error('获取同步记录失败')
      } finally {
        tableLoading.value = false
      }
    }

    // 获取统计数据
    const fetchStatistics = async () => {
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 600))

        // 模拟数据
        Object.assign(statistics, {
          todayShipped: 156,
          inTransit: 324,
          completed: 1028,
          exception: 5
        })
      } catch (error) {
        console.error('获取统计数据失败:', error)
        message.error('获取统计数据失败')
      }
    }

    // 获取同步状态颜色
    const getSyncStatusColor = (status) => {
      const colorMap = {
        success: 'green',
        processing: 'blue',
        warning: 'orange',
        error: 'red'
      }
      return colorMap[status] || 'default'
    }

    // 获取物流状态颜色
    const getTrackingStatusColor = (status) => {
      const colorMap = {
        pending: 'orange',
        in_transit: 'blue',
        delivering: 'geekblue',
        delivered: 'green',
        exception: 'red',
        returned: 'red'
      }
      return colorMap[status] || 'default'
    }

    // 查看同步详情
    const viewSyncDetail = (record) => {
      message.info(`查看同步记录: ${record.syncTime}`)
    }

    // 路由到详细状态页
    const routeToStatus = () => {
      router.push('/integration/status/logistics')
    }

    onMounted(() => {
      fetchConnectionStatus()
    })

    return {
      connectionStatus,
      tableLoading,
      connectionModal,
      confirmLoading,
      connectionForm,
      connectionRules,
      connectionFormRef,
      syncModal,
      syncLoading,
      syncForm,
      syncFormRef,
      trackingForm,
      trackingInfo,
      syncColumns,
      syncRecords,
      statistics,
      showConnectionModal,
      handleConnect,
      handleDisconnect,
      handleSync,
      handleTracking,
      getSyncStatusColor,
      getTrackingStatusColor,
      viewSyncDetail,
      routeToStatus
    }
  }
})
</script>

<style scoped>
.logistics-container {
  padding: 0 12px;
}

.action-buttons {
  display: flex;
  justify-content: flex-start;
  gap: 12px;
}
</style>
