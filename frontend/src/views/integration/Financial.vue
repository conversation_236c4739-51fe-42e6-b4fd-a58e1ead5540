<template>
  <div class="financial-integration-container">
    <a-card title="财务系统对接" :bordered="false">
      <a-alert
        message="财务系统对接配置"
        description="支持与主流财务软件对接，实现发票数据自动导入、账务自动同步、凭证自动生成等功能"
        type="info"
        show-icon
        style="margin-bottom: 24px"
      />

      <!-- 系统连接状态面板 -->
      <a-collapse v-model:active-key="activeCollapseKey" :bordered="false">
        <a-collapse-panel key="1" header="已连接的财务系统">
          <a-spin :spinning="loading">
            <a-empty v-if="!connectedSystems.length" description="暂无已连接的财务系统" />
            <div v-else>
              <a-list :data-source="connectedSystems" :grid="{ gutter: 16, column: 3 }">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-card class="system-card">
                      <div class="system-card-header">
                        <img :src="item.logoUrl" class="system-logo">
                        <a-tag color="green">
                          已连接
                        </a-tag>
                      </div>
                      <div class="system-card-content">
                        <h3>{{ item.name }}</h3>
                        <p class="desc">
                          {{ item.description }}
                        </p>
                        <p class="info">
                          连接时间: {{ item.connectedTime }}
                        </p>
                        <p class="info">
                          最后同步: {{ item.lastSyncTime }}
                        </p>
                      </div>
                      <div class="system-card-footer">
                        <a-button type="primary" size="small" @click="showSyncModal(item)">
                          立即同步
                        </a-button>
                        <a-button type="danger" size="small" @click="handleDisconnect(item)">
                          断开连接
                        </a-button>
                        <a-button type="link" size="small" @click="showSyncSettings(item)">
                          同步设置
                        </a-button>
                      </div>
                    </a-card>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-spin>
        </a-collapse-panel>
      </a-collapse>

      <!-- 添加财务系统按钮 -->
      <div class="add-system-section">
        <h3>添加财务系统</h3>
        <p class="section-desc">
          选择一个财务系统进行对接，实现数据自动同步
        </p>
        <a-row :gutter="16">
          <a-col v-for="system in availableSystems" :key="system.id" :span="8">
            <a-card hoverable class="system-select-card" @click="handleConnect(system)">
              <div class="system-select-header">
                <img :src="system.logoUrl" class="system-logo">
                <a-tag v-if="system.popular" color="blue">
                  热门
                </a-tag>
              </div>
              <div class="system-select-content">
                <h3>{{ system.name }}</h3>
                <p class="desc">
                  {{ system.description }}
                </p>
                <ul class="feature-list">
                  <li v-for="(feature, index) in system.features" :key="index">
                    <CheckOutlined style="color: #52c41a; margin-right: 8px" />
                    {{ feature }}
                  </li>
                </ul>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 数据同步状态 -->
      <a-card title="数据同步状态" style="margin-top: 24px" :loading="loading">
        <a-tabs default-active-key="1">
          <a-tab-pane key="1" tab="同步记录">
            <a-table
              :columns="syncRecordColumns"
              :data-source="syncRecords"
              :pagination="{ pageSize: 5 }"
              :loading="tableLoading"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'status'">
                  <a-tag :color="getSyncStatusColor(record.status)">
                    {{ getSyncStatusText(record.status) }}
                  </a-tag>
                </template>
                <template v-else-if="column.dataIndex === 'operation'">
                  <a-button type="link" @click="viewSyncDetail(record)">
                    查看详情
                  </a-button>
                  <a-button v-if="record.status === 'error'" type="link" @click="retrySync(record)">
                    重试
                  </a-button>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          <a-tab-pane key="2" tab="数据统计">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic title="已同步发票" :value="statistics.invoiceCount" style="margin-right: 24px" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="已同步凭证" :value="statistics.voucherCount" style="margin-right: 24px" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="已同步账套" :value="statistics.accountSetCount" style="margin-right: 24px" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="已同步客户" :value="statistics.customerCount" />
              </a-col>
            </a-row>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </a-card>

    <!-- 连接配置抽屉 -->
    <a-drawer
      title="财务系统连接配置"
      :visible="configVisible"
      :width="500"
      @close="configVisible = false"
    >
      <a-form
        v-if="currentSystem"
        ref="configFormRef"
        :model="configForm"
        :rules="rules"
        layout="vertical"
      >
        <div class="current-system-info">
          <img :src="currentSystem.logoUrl" class="system-logo">
          <h3>{{ currentSystem.name }}</h3>
        </div>

        <a-divider />

        <h4>账户信息</h4>
        <a-form-item label="系统账号" name="username">
          <a-input v-model:value="configForm.username" placeholder="请输入系统账号" />
        </a-form-item>

        <a-form-item label="账号密码" name="password">
          <a-input-password v-model:value="configForm.password" placeholder="请输入账号密码" />
        </a-form-item>

        <a-form-item v-if="currentSystem.type === 'onpremise'" label="服务器地址" name="serverUrl">
          <a-input v-model:value="configForm.serverUrl" placeholder="请输入服务器地址" />
          <div style="margin-top: 4px; color: rgba(0, 0, 0, 0.45);">
            例如：http://192.168.1.100:8080
          </div>
        </a-form-item>

        <a-form-item v-if="currentSystem.type === 'onpremise'" label="数据库连接" name="dbConfig">
          <a-textarea v-model:value="configForm.dbConfig" placeholder="请输入数据库连接信息" :rows="3" />
          <div style="margin-top: 4px; color: rgba(0, 0, 0, 0.45);">
            格式：数据库类型://用户名:密码@主机:端口/数据库名
          </div>
        </a-form-item>

        <a-form-item v-if="currentSystem.type === 'cloud'" label="选择账套" name="accountSet">
          <a-select v-model:value="configForm.accountSet" placeholder="请选择要连接的账套">
            <a-select-option value="default">
              默认账套
            </a-select-option>
            <a-select-option value="2022">
              2022年度账套
            </a-select-option>
            <a-select-option value="2023">
              2023年度账套
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-divider />

        <h4>同步设置</h4>
        <a-form-item label="同步数据范围">
          <a-checkbox-group v-model:value="configForm.syncDataTypes">
            <a-checkbox value="invoice">
              发票数据
            </a-checkbox>
            <a-checkbox value="voucher">
              会计凭证
            </a-checkbox>
            <a-checkbox value="customer">
              客户信息
            </a-checkbox>
            <a-checkbox value="supplier">
              供应商信息
            </a-checkbox>
            <a-checkbox value="account">
              会计科目
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item label="同步周期">
          <a-radio-group v-model:value="configForm.syncFrequency">
            <a-radio value="realtime">
              实时同步
            </a-radio>
            <a-radio value="hourly">
              每小时
            </a-radio>
            <a-radio value="daily">
              每天
            </a-radio>
            <a-radio value="manual">
              手动同步
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="configForm.syncFrequency === 'daily'" label="同步时间">
          <a-time-picker v-model:value="configForm.syncTime" format="HH:mm" />
        </a-form-item>

        <a-form-item>
          <a-button type="primary" :loading="submitLoading" @click="handleSubmitConfig">
            建立连接
          </a-button>
          <a-button style="margin-left: 8px" @click="configVisible = false">
            取消
          </a-button>
        </a-form-item>
      </a-form>
    </a-drawer>

    <!-- 同步抽屉 -->
    <a-drawer
      title="数据同步设置"
      :visible="syncSettingsVisible"
      :width="400"
      @close="syncSettingsVisible = false"
    >
      <a-form
        v-if="currentSystem"
        :model="syncSettingsForm"
        layout="vertical"
      >
        <a-form-item label="同步数据范围">
          <a-checkbox-group v-model:value="syncSettingsForm.syncDataTypes">
            <a-checkbox value="invoice">
              发票数据
            </a-checkbox>
            <a-checkbox value="voucher">
              会计凭证
            </a-checkbox>
            <a-checkbox value="customer">
              客户信息
            </a-checkbox>
            <a-checkbox value="supplier">
              供应商信息
            </a-checkbox>
            <a-checkbox value="account">
              会计科目
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item label="同步周期">
          <a-radio-group v-model:value="syncSettingsForm.syncFrequency">
            <a-radio value="realtime">
              实时同步
            </a-radio>
            <a-radio value="hourly">
              每小时
            </a-radio>
            <a-radio value="daily">
              每天
            </a-radio>
            <a-radio value="manual">
              手动同步
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="syncSettingsForm.syncFrequency === 'daily'" label="同步时间">
          <a-time-picker v-model:value="syncSettingsForm.syncTime" format="HH:mm" />
        </a-form-item>

        <a-form-item label="数据冲突处理">
          <a-radio-group v-model:value="syncSettingsForm.conflictStrategy">
            <a-radio value="financial">
              以财务系统为准
            </a-radio>
            <a-radio value="tax">
              以税务系统为准
            </a-radio>
            <a-radio value="newer">
              以最新修改为准
            </a-radio>
            <a-radio value="manual">
              提示手动处理
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" @click="saveSyncSettings">
            保存设置
          </a-button>
        </a-form-item>
      </a-form>
    </a-drawer>

    <!-- 同步模态框 -->
    <a-modal
      title="数据同步"
      :visible="syncModalVisible"
      :footer="null"
      @cancel="syncModalVisible = false"
    >
      <div v-if="currentSystem">
        <p>您确定要从 {{ currentSystem.name }} 同步数据吗？</p>

        <a-form layout="vertical">
          <a-form-item label="选择同步数据类型">
            <a-checkbox-group v-model:value="syncForm.dataTypes">
              <a-checkbox value="invoice">
                发票数据
              </a-checkbox>
              <a-checkbox value="voucher">
                会计凭证
              </a-checkbox>
              <a-checkbox value="customer">
                客户信息
              </a-checkbox>
              <a-checkbox value="supplier">
                供应商信息
              </a-checkbox>
              <a-checkbox value="account">
                会计科目
              </a-checkbox>
            </a-checkbox-group>
          </a-form-item>

          <a-form-item label="选择同步数据范围">
            <a-radio-group v-model:value="syncForm.dateRange">
              <a-radio value="all">
                全部数据
              </a-radio>
              <a-radio value="today">
                今天
              </a-radio>
              <a-radio value="yesterday">
                昨天
              </a-radio>
              <a-radio value="thisWeek">
                本周
              </a-radio>
              <a-radio value="thisMonth">
                本月
              </a-radio>
              <a-radio value="custom">
                自定义
              </a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item v-if="syncForm.dateRange === 'custom'">
            <a-range-picker
              v-model:value="syncForm.customDateRange"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item>
            <a-button type="primary" :loading="syncLoading" @click="startSync">
              开始同步
            </a-button>
            <a-button style="margin-left: 8px" @click="syncModalVisible = false">
              取消
            </a-button>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { CheckOutlined } from '@ant-design/icons-vue'

export default defineComponent({
  name: 'FinancialIntegration',
  components: {
    CheckOutlined
  },
  setup () {
    const loading = ref(false)
    const tableLoading = ref(false)
    const configFormRef = ref(null)
    const submitLoading = ref(false)
    const syncLoading = ref(false)

    const activeCollapseKey = ref(['1'])
    const configVisible = ref(false)
    const syncSettingsVisible = ref(false)
    const syncModalVisible = ref(false)

    const currentSystem = ref(null)
    const connectedSystems = ref([])

    // 可用的财务系统列表
    const availableSystems = ref([
      {
        id: 'kingdee',
        name: '金蝶云星空',
        type: 'cloud',
        logoUrl: 'https://img.alicdn.com/tfs/TB1SFc3fkT2gK0jSZPcXXcKkpXa-200-200.png',
        description: '金蝶提供的云端财务管理系统，支持多组织财务业务协同',
        popular: true,
        features: [
          '发票数据自动导入',
          '财务凭证自动生成',
          '客户供应商信息同步'
        ]
      },
      {
        id: 'ufida',
        name: '用友U8 cloud',
        type: 'cloud',
        logoUrl: 'https://img.alicdn.com/tfs/TB1PgYofXT7gK0jSZFpXXaTkpXa-200-200.png',
        description: '用友网络推出的企业级云服务平台，全面支持企业财务管理',
        popular: true,
        features: [
          '发票自动核销',
          '智能会计核算',
          '税务信息集成'
        ]
      },
      {
        id: 'chanjet',
        name: '畅捷通T+',
        type: 'onpremise',
        logoUrl: 'https://img.alicdn.com/tfs/TB1b.EHoRr0gK0jSZFnXXbRRXXa-200-200.png',
        description: '面向中小微企业的财务管理软件，简单易用',
        features: [
          '财务报表导出',
          '发票税务匹配',
          '增值税管理'
        ]
      }
    ])

    // 同步记录列定义
    const syncRecordColumns = [
      {
        title: '同步时间',
        dataIndex: 'syncTime',
        key: 'syncTime',
        width: 180
      },
      {
        title: '财务系统',
        dataIndex: 'systemName',
        key: 'systemName',
        width: 150
      },
      {
        title: '数据类型',
        dataIndex: 'dataType',
        key: 'dataType',
        width: 120
      },
      {
        title: '数据数量',
        dataIndex: 'dataCount',
        key: 'dataCount',
        width: 100
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
      },
      {
        title: '同步结果',
        dataIndex: 'result',
        key: 'result',
        ellipsis: true
      },
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        width: 150
      }
    ]

    // 同步记录数据
    const syncRecords = ref([])

    // 数据统计
    const statistics = reactive({
      invoiceCount: 0,
      voucherCount: 0,
      accountSetCount: 0,
      customerCount: 0
    })

    // 配置表单
    const configForm = reactive({
      username: '',
      password: '',
      serverUrl: '',
      dbConfig: '',
      accountSet: undefined,
      syncDataTypes: ['invoice', 'voucher', 'customer'],
      syncFrequency: 'daily',
      syncTime: null
    })

    // 同步设置表单
    const syncSettingsForm = reactive({
      syncDataTypes: ['invoice', 'voucher', 'customer'],
      syncFrequency: 'daily',
      syncTime: null,
      conflictStrategy: 'newer'
    })

    // 同步表单
    const syncForm = reactive({
      dataTypes: ['invoice', 'voucher'],
      dateRange: 'thisMonth',
      customDateRange: null
    })

    // 表单验证规则
    const rules = {
      username: [{ required: true, message: '请输入系统账号', trigger: 'blur' }],
      password: [{ required: true, message: '请输入账号密码', trigger: 'blur' }],
      serverUrl: [{ required: true, message: '请输入服务器地址', trigger: 'blur' }],
      accountSet: [{ required: true, message: '请选择账套', trigger: 'change' }]
    }

    // 财务系统连接处理
    const handleConnect = (system) => {
      currentSystem.value = system
      configVisible.value = true

      // 重置配置表单
      Object.assign(configForm, {
        username: '',
        password: '',
        serverUrl: '',
        dbConfig: '',
        accountSet: undefined,
        syncDataTypes: ['invoice', 'voucher', 'customer'],
        syncFrequency: 'daily',
        syncTime: null
      })
    }

    // 提交配置
    const handleSubmitConfig = async () => {
      try {
        if (!configFormRef.value) return

        await configFormRef.value.validate()
        submitLoading.value = true

        await new Promise(resolve => setTimeout(resolve, 1500)) // 模拟连接延迟

        message.success(`${currentSystem.value.name} 连接成功`)
        configVisible.value = false
        fetchConnectedSystems()
      } catch (error) {
        console.error('提交配置失败:', error)
        message.error('连接失败，请检查输入信息是否正确')
      } finally {
        submitLoading.value = false
      }
    }

    // 断开连接
    const handleDisconnect = async (system) => {
      try {
        loading.value = true
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟请求延迟

        message.success(`已断开与 ${system.name} 的连接`)
        fetchConnectedSystems()
      } catch (error) {
        console.error('断开连接失败:', error)
        message.error('断开连接失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 获取同步状态文本
    const getSyncStatusText = (status) => {
      const statusMap = {
        success: '成功',
        processing: '处理中',
        error: '失败',
        warning: '部分成功'
      }
      return statusMap[status] || status
    }

    // 获取同步状态颜色
    const getSyncStatusColor = (status) => {
      const colorMap = {
        success: 'green',
        processing: 'blue',
        error: 'red',
        warning: 'orange'
      }
      return colorMap[status] || 'default'
    }

    // 打开同步设置抽屉
    const showSyncSettings = (system) => {
      currentSystem.value = system
      syncSettingsVisible.value = true

      // 初始化同步设置表单
      Object.assign(syncSettingsForm, {
        syncDataTypes: ['invoice', 'voucher', 'customer'],
        syncFrequency: 'daily',
        syncTime: null,
        conflictStrategy: 'newer'
      })
    }

    // 保存同步设置
    const saveSyncSettings = () => {
      message.success('同步设置已保存')
      syncSettingsVisible.value = false
    }

    // 打开同步模态框
    const showSyncModal = (system) => {
      currentSystem.value = system
      syncModalVisible.value = true

      // 初始化同步表单
      Object.assign(syncForm, {
        dataTypes: ['invoice', 'voucher'],
        dateRange: 'thisMonth',
        customDateRange: null
      })
    }

    // 开始同步
    const startSync = async () => {
      try {
        syncLoading.value = true
        message.loading('正在同步数据...')

        // 模拟数据同步过程
        setTimeout(() => {
          // 更新最近同步时间
          currentSystem.value.lastSyncTime = new Date().toLocaleString()
          syncLoading.value = false
          message.success('数据同步成功')

          // 更新同步记录
          syncRecords.value.unshift({
            id: Date.now(),
            syncTime: new Date().toLocaleString(),
            status: 'success',
            statusText: '同步成功',
            remarks: '自动同步完成',
            dataCount: Math.floor(Math.random() * 100) + 50
          })
        }, 2000)
      } catch (error) {
        syncLoading.value = false
        message.error('数据同步失败: ' + error.message)
      }
    }

    // 查看同步详情
    const viewSyncDetail = (record) => {
      message.info(`查看同步记录：${record.systemName} - ${record.dataType}`)
    }

    // 重试同步
    const retrySync = (_record) => {
      message.success('同步任务已重新提交')
    }

    // 获取已连接的财务系统
    const fetchConnectedSystems = async () => {
      loading.value = true
      try {
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟请求延迟

        // 模拟数据
        connectedSystems.value = [
          {
            id: 'kingdee',
            name: '金蝶云星空',
            logoUrl: 'https://img.alicdn.com/tfs/TB1SFc3fkT2gK0jSZPcXXcKkpXa-200-200.png',
            description: '金蝶提供的云端财务管理系统',
            connectedTime: '2023-10-15 10:30:25',
            lastSyncTime: '2023-11-01 08:45:12'
          }
        ]
      } catch (error) {
        console.error('获取财务系统失败:', error)
        connectedSystems.value = []
      } finally {
        loading.value = false
      }
    }

    // 获取同步记录
    const fetchSyncRecords = async () => {
      tableLoading.value = true
      try {
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟请求延迟

        // 模拟数据
        syncRecords.value = [
          {
            key: '1',
            syncTime: '2023-11-01 08:45:12',
            systemName: '金蝶云星空',
            dataType: '发票',
            dataCount: 42,
            status: 'success',
            result: '成功同步42条发票数据'
          },
          {
            key: '2',
            syncTime: '2023-11-01 08:45:30',
            systemName: '金蝶云星空',
            dataType: '会计凭证',
            dataCount: 25,
            status: 'success',
            result: '成功同步25条凭证数据'
          },
          {
            key: '3',
            syncTime: '2023-10-31 17:15:22',
            systemName: '金蝶云星空',
            dataType: '客户信息',
            dataCount: 12,
            status: 'warning',
            result: '同步12条客户信息，其中2条存在冲突'
          },
          {
            key: '4',
            syncTime: '2023-10-30 09:32:10',
            systemName: '金蝶云星空',
            dataType: '供应商信息',
            dataCount: 8,
            status: 'error',
            result: '同步失败，网络连接中断'
          }
        ]

        // 更新统计数据
        Object.assign(statistics, {
          invoiceCount: 528,
          voucherCount: 346,
          accountSetCount: 3,
          customerCount: 65
        })
      } catch (error) {
        console.error('获取同步记录失败:', error)
        syncRecords.value = []
      } finally {
        tableLoading.value = false
      }
    }

    onMounted(() => {
      fetchConnectedSystems()
      fetchSyncRecords()
    })

    return {
      loading,
      tableLoading,
      configVisible,
      syncSettingsVisible,
      syncModalVisible,
      activeCollapseKey,
      currentSystem,
      availableSystems,
      connectedSystems,
      syncRecordColumns,
      syncRecords,
      statistics,
      configForm,
      syncSettingsForm,
      syncForm,
      configFormRef,
      rules,
      submitLoading,
      syncLoading,
      handleConnect,
      handleSubmitConfig,
      handleDisconnect,
      getSyncStatusText,
      getSyncStatusColor,
      showSyncSettings,
      saveSyncSettings,
      showSyncModal,
      startSync,
      viewSyncDetail,
      retrySync
    }
  }
})
</script>

<style scoped>
.financial-integration-container {
  padding: 0 12px;
}

.add-system-section {
  margin: 24px 0;
}

.section-desc {
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.65);
}

.system-card {
  height: 100%;
}

.system-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.system-card-content {
  margin-bottom: 12px;
}

.system-card-content h3 {
  margin: 0 0 8px 0;
}

.system-card-footer {
  display: flex;
  justify-content: space-between;
}

.system-select-card {
  height: 100%;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
}

.system-select-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.system-select-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.system-select-content h3 {
  margin: 0 0 8px 0;
}

.system-logo {
  height: 32px;
  width: 32px;
}

.desc {
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 12px;
}

.info {
  color: rgba(0, 0, 0, 0.45);
  margin: 4px 0;
  font-size: 12px;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 12px 0 0 0;
}

.feature-list li {
  margin-bottom: 4px;
}

.current-system-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.current-system-info img {
  margin-right: 12px;
}

.current-system-info h3 {
  margin: 0;
}
</style>
