<template>
  <div class="ecommerce-container">
    <a-page-header title="电商平台集成" :ghost="false">
      <template #extra>
        <a-button type="primary" @click="syncModal = true">
          <template #icon>
            <SyncOutlined />
          </template>
          数据同步
        </a-button>
      </template>
      <a-alert
        v-if="!connectionStatus.connected"
        message="您尚未配置电商平台连接，请完成配置以启用数据同步功能"
        type="warning"
        show-icon
        style="margin-bottom: 16px"
      />
    </a-page-header>

    <a-row :gutter="16">
      <!-- 连接状态 -->
      <a-col :span="24">
        <a-card title="连接状态" :bordered="false">
          <a-descriptions :column="3">
            <a-descriptions-item label="连接状态">
              <a-badge
                :status="connectionStatus.connected ? 'success' : 'error'"
                :text="connectionStatus.connected ? '已连接' : '未连接'"
              />
            </a-descriptions-item>
            <a-descriptions-item label="平台类型">
              {{ connectionStatus.platformType || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="最近同步时间">
              {{ connectionStatus.lastSyncTime || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="店铺名称">
              {{ connectionStatus.shopName || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="店铺ID">
              {{ connectionStatus.shopId || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="授权到期时间">
              {{ connectionStatus.expiryTime || '-' }}
            </a-descriptions-item>
          </a-descriptions>

          <a-divider />

          <div class="action-buttons">
            <a-button
              v-if="!connectionStatus.connected"
              type="primary"
              @click="showConnectionModal"
            >
              配置连接
            </a-button>
            <a-button
              v-else
              type="primary"
              danger
              @click="handleDisconnect"
            >
              断开连接
            </a-button>
            <a-button
              v-if="connectionStatus.connected"
              @click="routeToStatus"
            >
              详细状态
            </a-button>
          </div>
        </a-card>
      </a-col>

      <!-- 平台数据同步 -->
      <a-col :span="24" style="margin-top: 16px">
        <a-card
          title="平台数据同步"
          :bordered="false"
          :tab-list="dataTabs"
          :active-tab-key="activeTabKey"
          @tab-change="handleTabChange"
        >
          <a-empty v-if="!connectionStatus.connected" description="请先配置连接" />

          <div v-else>
            <!-- 商品数据 -->
            <div v-if="activeTabKey === 'products'">
              <a-table
                :columns="productColumns"
                :data-source="productData"
                :loading="tableLoading"
                :pagination="{ pageSize: 10 }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'status'">
                    <a-tag :color="getStatusColor(record.status)">
                      {{ record.statusText }}
                    </a-tag>
                  </template>
                  <template v-if="column.dataIndex === 'action'">
                    <a @click="viewProductDetail(record)">详情</a>
                  </template>
                </template>
              </a-table>
            </div>

            <!-- 订单数据 -->
            <div v-if="activeTabKey === 'orders'">
              <a-table
                :columns="orderColumns"
                :data-source="orderData"
                :loading="tableLoading"
                :pagination="{ pageSize: 10 }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'status'">
                    <a-tag :color="getOrderStatusColor(record.status)">
                      {{ record.statusText }}
                    </a-tag>
                  </template>
                  <template v-if="column.dataIndex === 'action'">
                    <a @click="viewOrderDetail(record)">详情</a>
                  </template>
                </template>
              </a-table>
            </div>

            <!-- 客户数据 -->
            <div v-if="activeTabKey === 'customers'">
              <a-table
                :columns="customerColumns"
                :data-source="customerData"
                :loading="tableLoading"
                :pagination="{ pageSize: 10 }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'action'">
                    <a @click="viewCustomerDetail(record)">详情</a>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- 统计数据 -->
      <a-col :span="24" style="margin-top: 16px">
        <a-card title="平台统计数据" :bordered="false">
          <a-empty v-if="!connectionStatus.connected" description="请先配置连接" />

          <div v-else>
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="商品总数"
                  :value="statistics.totalProducts"
                  :precision="0"
                >
                  <template #prefix>
                    <ShoppingOutlined />
                  </template>
                </a-statistic>
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="订单总数"
                  :value="statistics.totalOrders"
                  :precision="0"
                >
                  <template #prefix>
                    <FileTextOutlined />
                  </template>
                </a-statistic>
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="客户总数"
                  :value="statistics.totalCustomers"
                  :precision="0"
                >
                  <template #prefix>
                    <UserOutlined />
                  </template>
                </a-statistic>
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="本月收入"
                  :value="statistics.monthlyRevenue"
                  :precision="2"
                  suffix="元"
                >
                  <template #prefix>
                    <PayCircleOutlined />
                  </template>
                </a-statistic>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 连接配置模态框 -->
    <a-modal
      v-model:visible="connectionModal"
      title="电商平台连接配置"
      :confirm-loading="confirmLoading"
      width="700px"
      @ok="handleConnect"
    >
      <a-form
        ref="connectionFormRef"
        :model="connectionForm"
        :rules="connectionRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="平台类型" name="platformType">
          <a-select v-model:value="connectionForm.platformType">
            <a-select-option value="taobao">
              淘宝/天猫
            </a-select-option>
            <a-select-option value="jd">
              京东
            </a-select-option>
            <a-select-option value="pdd">
              拼多多
            </a-select-option>
            <a-select-option value="douyin">
              抖音电商
            </a-select-option>
            <a-select-option value="other">
              其他平台
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="连接方式" name="connectionType">
          <a-radio-group v-model:value="connectionForm.connectionType">
            <a-radio value="oauth">
              OAuth授权
            </a-radio>
            <a-radio value="apikey">
              API密钥
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <template v-if="connectionForm.connectionType === 'oauth'">
          <a-alert
            message="请点击下方授权按钮，在新窗口中完成平台授权"
            type="info"
            show-icon
            style="margin-bottom: 16px"
          />
          <div style="text-align: center; margin: 20px 0;">
            <a-button type="primary" @click="handleOAuthAuthorize">
              <template #icon>
                <LinkOutlined />
              </template>
              开始授权
            </a-button>
          </div>
        </template>

        <template v-else>
          <a-form-item label="AppKey" name="appKey">
            <a-input v-model:value="connectionForm.appKey" placeholder="请输入应用的AppKey" />
          </a-form-item>

          <a-form-item label="AppSecret" name="appSecret">
            <a-input-password v-model:value="connectionForm.appSecret" placeholder="请输入应用的AppSecret" />
          </a-form-item>

          <a-form-item
            v-if="['taobao', 'jd'].includes(connectionForm.platformType)"
            label="店铺ID"
            name="shopId"
          >
            <a-input v-model:value="connectionForm.shopId" placeholder="请输入店铺ID" />
          </a-form-item>

          <a-form-item
            v-if="['pdd', 'douyin'].includes(connectionForm.platformType)"
            label="授权码"
            name="authCode"
          >
            <a-input v-model:value="connectionForm.authCode" placeholder="请输入授权码" />
          </a-form-item>
        </template>

        <a-form-item label="数据同步频率" name="syncFrequency">
          <a-select v-model:value="connectionForm.syncFrequency">
            <a-select-option value="30">
              每30分钟
            </a-select-option>
            <a-select-option value="60">
              每1小时
            </a-select-option>
            <a-select-option value="360">
              每6小时
            </a-select-option>
            <a-select-option value="720">
              每12小时
            </a-select-option>
            <a-select-option value="1440">
              每天
            </a-select-option>
            <a-select-option value="manual">
              手动同步
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="同步数据类型" name="syncDataTypes">
          <a-checkbox-group v-model:value="connectionForm.syncDataTypes">
            <a-checkbox value="products">
              商品数据
            </a-checkbox>
            <a-checkbox value="orders">
              订单数据
            </a-checkbox>
            <a-checkbox value="customers">
              客户数据
            </a-checkbox>
            <a-checkbox value="inventory">
              库存数据
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 同步模态框 -->
    <a-modal
      v-model:visible="syncModal"
      title="电商数据同步"
      :confirm-loading="syncLoading"
      @ok="handleSync"
    >
      <a-form
        ref="syncFormRef"
        :model="syncForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="同步数据类型">
          <a-checkbox-group v-model:value="syncForm.dataTypes">
            <a-checkbox value="products">
              商品数据
            </a-checkbox>
            <a-checkbox value="orders">
              订单数据
            </a-checkbox>
            <a-checkbox value="customers">
              客户数据
            </a-checkbox>
            <a-checkbox value="inventory">
              库存数据
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item label="时间范围">
          <a-radio-group v-model:value="syncForm.timeRange">
            <a-radio value="today">
              今天
            </a-radio>
            <a-radio value="yesterday">
              昨天
            </a-radio>
            <a-radio value="thisWeek">
              本周
            </a-radio>
            <a-radio value="lastWeek">
              上周
            </a-radio>
            <a-radio value="thisMonth">
              本月
            </a-radio>
            <a-radio value="custom">
              自定义
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="syncForm.timeRange === 'custom'" label="自定义时间范围">
          <a-range-picker v-model:value="syncForm.dateRange" style="width: 100%" />
        </a-form-item>

        <a-form-item label="同步方式">
          <a-radio-group v-model:value="syncForm.syncType">
            <a-radio value="incremental">
              增量同步
            </a-radio>
            <a-radio value="full">
              全量同步
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  SyncOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  UserOutlined,
  PayCircleOutlined,
  LinkOutlined
} from '@ant-design/icons-vue'
import {
  checkIntegrationStatus,
  connectService,
  disconnectService
} from '@/api/integration'

export default defineComponent({
  name: 'EcommerceIntegration',
  components: {
    SyncOutlined,
    ShoppingOutlined,
    FileTextOutlined,
    UserOutlined,
    PayCircleOutlined,
    LinkOutlined
  },
  setup () {
    const router = useRouter()
    const connectionFormRef = ref(null)
    const syncFormRef = ref(null)

    // 连接状态
    const connectionStatus = reactive({
      connected: false,
      platformType: '',
      shopName: '',
      shopId: '',
      lastSyncTime: '',
      expiryTime: ''
    })

    // 表格加载状态
    const tableLoading = ref(false)

    // 连接配置模态框
    const connectionModal = ref(false)
    const confirmLoading = ref(false)

    // 连接表单
    const connectionForm = reactive({
      platformType: 'taobao',
      connectionType: 'oauth',
      appKey: '',
      appSecret: '',
      shopId: '',
      authCode: '',
      syncFrequency: '720',
      syncDataTypes: ['products', 'orders', 'customers']
    })

    // 连接表单规则
    const connectionRules = {
      platformType: [{ required: true, message: '请选择平台类型' }],
      connectionType: [{ required: true, message: '请选择连接方式' }],
      appKey: [{ required: true, message: '请输入AppKey', trigger: 'blur' }],
      appSecret: [{ required: true, message: '请输入AppSecret', trigger: 'blur' }],
      syncFrequency: [{ required: true, message: '请选择同步频率' }],
      syncDataTypes: [{ required: true, type: 'array', min: 1, message: '请至少选择一种数据类型' }]
    }

    // 同步模态框
    const syncModal = ref(false)
    const syncLoading = ref(false)

    // 同步表单
    const syncForm = reactive({
      dataTypes: ['products', 'orders', 'customers'],
      timeRange: 'thisWeek',
      dateRange: [],
      syncType: 'incremental'
    })

    // 数据标签页
    const dataTabs = [
      { key: 'products', tab: '商品数据' },
      { key: 'orders', tab: '订单数据' },
      { key: 'customers', tab: '客户数据' }
    ]
    const activeTabKey = ref('products')

    // 商品数据列定义
    const productColumns = [
      { title: '商品ID', dataIndex: 'productId', key: 'productId', width: 100 },
      { title: '商品名称', dataIndex: 'productName', key: 'productName', ellipsis: true },
      { title: '价格(元)', dataIndex: 'price', key: 'price', width: 100 },
      { title: '库存', dataIndex: 'inventory', key: 'inventory', width: 100 },
      { title: '销量', dataIndex: 'sales', key: 'sales', width: 100 },
      { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
      { title: '最后更新时间', dataIndex: 'updateTime', key: 'updateTime', width: 180 },
      { title: '操作', dataIndex: 'action', key: 'action', width: 80 }
    ]

    // 订单数据列定义
    const orderColumns = [
      { title: '订单号', dataIndex: 'orderId', key: 'orderId', width: 120 },
      { title: '客户', dataIndex: 'customer', key: 'customer', width: 120 },
      { title: '订单金额(元)', dataIndex: 'amount', key: 'amount', width: 120 },
      { title: '订单状态', dataIndex: 'status', key: 'status', width: 100 },
      { title: '下单时间', dataIndex: 'createTime', key: 'createTime', width: 180 },
      { title: '支付方式', dataIndex: 'paymentMethod', key: 'paymentMethod', width: 120 },
      { title: '操作', dataIndex: 'action', key: 'action', width: 80 }
    ]

    // 客户数据列定义
    const customerColumns = [
      { title: '客户ID', dataIndex: 'customerId', key: 'customerId', width: 100 },
      { title: '客户名称', dataIndex: 'customerName', key: 'customerName' },
      { title: '等级', dataIndex: 'level', key: 'level', width: 100 },
      { title: '注册时间', dataIndex: 'registerTime', key: 'registerTime', width: 180 },
      { title: '累计订单数', dataIndex: 'orderCount', key: 'orderCount', width: 120 },
      { title: '累计消费金额(元)', dataIndex: 'totalAmount', key: 'totalAmount', width: 150 },
      { title: '操作', dataIndex: 'action', key: 'action', width: 80 }
    ]

    // 模拟数据
    const productData = ref([])
    const orderData = ref([])
    const customerData = ref([])

    // 统计数据
    const statistics = reactive({
      totalProducts: 0,
      totalOrders: 0,
      totalCustomers: 0,
      monthlyRevenue: 0
    })

    // 获取集成状态
    const fetchConnectionStatus = async () => {
      try {
        const res = await checkIntegrationStatus('ecommerce')
        if (res.code === 0 && res.data) {
          Object.assign(connectionStatus, {
            connected: res.data.connected || false,
            platformType: res.data.platformType || '',
            shopName: res.data.shopName || '',
            shopId: res.data.shopId || '',
            lastSyncTime: res.data.lastSyncTime || '',
            expiryTime: res.data.expiryTime || ''
          })

          if (connectionStatus.connected) {
            fetchTableData()
            fetchStatistics()
          }
        }
      } catch (error) {
        console.error('获取连接状态失败:', error)
        message.error('获取连接状态失败')
      }
    }

    // 显示连接配置对话框
    const showConnectionModal = () => {
      connectionModal.value = true
    }

    // OAuth授权
    const handleOAuthAuthorize = () => {
      message.info('正在跳转到平台授权页面...')
      const platformType = connectionForm.platformType
      // 实际应用中，这里应该打开一个新窗口进行OAuth授权
      window.open(`/api/oauth/authorize?platform=${platformType}`, '_blank')
    }

    // 连接服务
    const handleConnect = () => {
      connectionFormRef.value.validate().then(async () => {
        confirmLoading.value = true
        try {
          const res = await connectService('ecommerce', connectionForm)
          if (res.code === 0) {
            message.success('连接电商平台成功')
            connectionModal.value = false
            fetchConnectionStatus()
          } else {
            message.error(res.message || '连接失败')
          }
        } catch (error) {
          console.error('连接失败:', error)
          message.error('连接电商平台失败')
        } finally {
          confirmLoading.value = false
        }
      })
    }

    // 断开连接
    const handleDisconnect = async () => {
      try {
        const res = await disconnectService('ecommerce')
        if (res.code === 0) {
          message.success('已断开电商平台连接')
          Object.assign(connectionStatus, {
            connected: false,
            platformType: '',
            shopName: '',
            shopId: '',
            lastSyncTime: '',
            expiryTime: ''
          })
        } else {
          message.error(res.message || '断开连接失败')
        }
      } catch (error) {
        console.error('断开连接失败:', error)
        message.error('断开电商平台连接失败')
      }
    }

    // 数据同步
    const handleSync = () => {
      syncFormRef.value.validate().then(async () => {
        syncLoading.value = true
        try {
          // 模拟同步API调用
          await new Promise(resolve => setTimeout(resolve, 1500))
          message.success('数据同步已开始，请稍后查看同步结果')
          syncModal.value = false
          fetchTableData()
          fetchStatistics()
        } catch (error) {
          console.error('同步失败:', error)
          message.error('数据同步失败')
        } finally {
          syncLoading.value = false
        }
      })
    }

    // 获取表格数据
    const fetchTableData = async () => {
      tableLoading.value = true

      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 800))

        // 商品数据
        productData.value = [
          {
            key: '1',
            productId: 'P0001',
            productName: '高档蓝牙耳机 - 主动降噪版',
            price: 299.00,
            inventory: 256,
            sales: 1024,
            status: 'online',
            statusText: '在售',
            updateTime: '2023-11-05 10:30:25'
          },
          {
            key: '2',
            productId: 'P0002',
            productName: '智能手表 - 健康监测版',
            price: 599.00,
            inventory: 128,
            sales: 512,
            status: 'online',
            statusText: '在售',
            updateTime: '2023-11-04 08:40:15'
          },
          {
            key: '3',
            productId: 'P0003',
            productName: '超轻薄笔记本电脑 - 商务版',
            price: 4999.00,
            inventory: 36,
            sales: 128,
            status: 'online',
            statusText: '在售',
            updateTime: '2023-11-03 15:20:45'
          },
          {
            key: '4',
            productId: 'P0004',
            productName: '智能家居套装 - 入门版',
            price: 1299.00,
            inventory: 0,
            sales: 256,
            status: 'soldout',
            statusText: '售罄',
            updateTime: '2023-11-02 09:15:30'
          },
          {
            key: '5',
            productId: 'P0005',
            productName: '高清摄像头 - 专业版',
            price: 899.00,
            inventory: 64,
            sales: 320,
            status: 'online',
            statusText: '在售',
            updateTime: '2023-11-01 11:45:10'
          }
        ]

        // 订单数据
        orderData.value = [
          {
            key: '1',
            orderId: 'ORD20231105001',
            customer: '张先生',
            amount: 598.00,
            status: 'paid',
            statusText: '已付款',
            createTime: '2023-11-05 09:25:15',
            paymentMethod: '支付宝'
          },
          {
            key: '2',
            orderId: 'ORD20231104002',
            customer: '李女士',
            amount: 4999.00,
            status: 'shipping',
            statusText: '配送中',
            createTime: '2023-11-04 14:35:40',
            paymentMethod: '微信支付'
          },
          {
            key: '3',
            orderId: 'ORD20231103003',
            customer: '王先生',
            amount: 1897.00,
            status: 'completed',
            statusText: '已完成',
            createTime: '2023-11-03 11:10:22',
            paymentMethod: '银行卡'
          },
          {
            key: '4',
            orderId: 'ORD20231102004',
            customer: '赵女士',
            amount: 2498.00,
            status: 'refunding',
            statusText: '退款中',
            createTime: '2023-11-02 16:48:30',
            paymentMethod: '支付宝'
          }
        ]

        // 客户数据
        customerData.value = [
          {
            key: '1',
            customerId: 'C0001',
            customerName: '张先生',
            level: '钻石会员',
            registerTime: '2022-05-15 10:20:15',
            orderCount: 28,
            totalAmount: 15680.50
          },
          {
            key: '2',
            customerId: 'C0002',
            customerName: '李女士',
            level: '黄金会员',
            registerTime: '2022-08-22 09:15:30',
            orderCount: 15,
            totalAmount: 9320.00
          },
          {
            key: '3',
            customerId: 'C0003',
            customerName: '王先生',
            level: '普通会员',
            registerTime: '2023-01-10 14:45:20',
            orderCount: 6,
            totalAmount: 3450.80
          },
          {
            key: '4',
            customerId: 'C0004',
            customerName: '赵女士',
            level: '白金会员',
            registerTime: '2022-11-05 16:30:45',
            orderCount: 20,
            totalAmount: 12560.30
          }
        ]
      } catch (error) {
        console.error('获取数据失败:', error)
        message.error('获取数据失败')
      } finally {
        tableLoading.value = false
      }
    }

    // 获取统计数据
    const fetchStatistics = async () => {
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 600))

        // 模拟数据
        Object.assign(statistics, {
          totalProducts: 125,
          totalOrders: 458,
          totalCustomers: 320,
          monthlyRevenue: 135680.50
        })
      } catch (error) {
        console.error('获取统计数据失败:', error)
        message.error('获取统计数据失败')
      }
    }

    // 获取状态颜色
    const getStatusColor = (status) => {
      const colorMap = {
        online: 'green',
        offline: 'gray',
        soldout: 'orange'
      }
      return colorMap[status] || 'blue'
    }

    // 获取订单状态颜色
    const getOrderStatusColor = (status) => {
      const colorMap = {
        unpaid: 'orange',
        paid: 'blue',
        shipping: 'purple',
        completed: 'green',
        refunding: 'red',
        refunded: 'gray'
      }
      return colorMap[status] || 'blue'
    }

    // 处理标签页切换
    const handleTabChange = (key) => {
      activeTabKey.value = key
    }

    // 查看商品详情
    const viewProductDetail = (record) => {
      message.info(`查看商品：${record.productName}`)
    }

    // 查看订单详情
    const viewOrderDetail = (record) => {
      message.info(`查看订单：${record.orderId}`)
    }

    // 查看客户详情
    const viewCustomerDetail = (record) => {
      message.info(`查看客户：${record.customerName}`)
    }

    // 路由到详细状态页
    const routeToStatus = () => {
      router.push('/integration/status/ecommerce')
    }

    onMounted(() => {
      fetchConnectionStatus()
    })

    return {
      connectionStatus,
      tableLoading,
      connectionModal,
      confirmLoading,
      connectionForm,
      connectionRules,
      connectionFormRef,
      syncModal,
      syncLoading,
      syncForm,
      syncFormRef,
      dataTabs,
      activeTabKey,
      productColumns,
      orderColumns,
      customerColumns,
      productData,
      orderData,
      customerData,
      statistics,
      showConnectionModal,
      handleOAuthAuthorize,
      handleConnect,
      handleDisconnect,
      handleSync,
      handleTabChange,
      getStatusColor,
      getOrderStatusColor,
      viewProductDetail,
      viewOrderDetail,
      viewCustomerDetail,
      routeToStatus
    }
  }
})
</script>

<style scoped>
.ecommerce-container {
  padding: 0 12px;
}

.action-buttons {
  display: flex;
  justify-content: flex-start;
  gap: 12px;
}
</style>
