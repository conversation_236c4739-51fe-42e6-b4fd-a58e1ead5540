<template>
  <div class="bank-integration-container">
    <a-card title="银行系统对接" :bordered="false">
      <a-alert
        message="银行系统对接配置"
        description="连接企业网银系统，自动导入银行流水，实现银行对账、资金管理等功能"
        type="info"
        show-icon
        style="margin-bottom: 24px"
      />

      <!-- 连接状态卡片 -->
      <a-card class="status-card" :loading="loading">
        <template #extra>
          <a-button v-if="!connected" type="primary" @click="showConfigDrawer">
            配置连接
          </a-button>
          <a-button
            v-else
            type="danger"
            :loading="disconnectLoading"
            @click="handleDisconnect"
          >
            断开连接
          </a-button>
        </template>

        <a-descriptions title="连接状态" bordered :column="2">
          <a-descriptions-item label="连接状态" :span="1">
            <a-badge :status="connected ? 'success' : 'error'" :text="connected ? '已连接' : '未连接'" />
          </a-descriptions-item>
          <a-descriptions-item label="银行名称" :span="1">
            {{ getBankName(bankInfo.bankCode) || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="最后同步时间" :span="1">
            {{ bankInfo.lastSyncTime || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="同步频率" :span="1">
            {{ getSyncPeriodText(bankInfo.syncPeriod) || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="账户信息" :span="2">
            {{ bankInfo.accountInfo || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 银行流水同步 -->
      <a-card title="银行流水同步" style="margin-top: 24px" :loading="loading">
        <div class="sync-header">
          <a-space>
            <a-button type="primary" :disabled="!connected" @click="showSyncModal">
              <template #icon>
                <SyncOutlined />
              </template>
              立即同步
            </a-button>
            <a-range-picker
              v-model:value="dateRange"
              :disabled-date="disabledDate"
              :disabled="!connected"
              style="width: 240px"
            />
            <a-select
              v-model:value="syncType"
              style="width: 120px"
              :disabled="!connected"
            >
              <a-select-option value="all">
                所有交易
              </a-select-option>
              <a-select-option value="in">
                收入交易
              </a-select-option>
              <a-select-option value="out">
                支出交易
              </a-select-option>
            </a-select>
          </a-space>

          <a-space>
            <a-tooltip title="刷新数据">
              <a-button type="link" @click="fetchTransactions">
                <ReloadOutlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="导出数据">
              <a-button type="link" :disabled="!connected" @click="exportTransactions">
                <DownloadOutlined />
              </a-button>
            </a-tooltip>
          </a-space>
        </div>

        <a-table
          :columns="transactionColumns"
          :data-source="transactions"
          :loading="tableLoading"
          :pagination="{ pageSize: 10 }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'amount'">
              <span :class="record.direction === 'in' ? 'amount-in' : 'amount-out'">
                {{ record.direction === 'in' ? '+' : '-' }}{{ record.amount }}
              </span>
            </template>
            <template v-else-if="column.dataIndex === 'reconciled'">
              <a-tag :color="record.reconciled ? 'green' : 'orange'">
                {{ record.reconciled ? '已核销' : '未核销' }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'operation'">
              <a-button type="link" @click="viewDetail(record)">
                详情
              </a-button>
              <a-button v-if="!record.reconciled" type="link" @click="reconcileTransaction(record)">
                核销
              </a-button>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 账户概览 -->
      <a-card title="账户概览" style="margin-top: 24px" :loading="loading">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-card :bordered="false" class="metric-card">
              <template #title>
                <span class="metric-title">
                  <BankOutlined class="metric-icon" />
                  账户余额
                </span>
              </template>
              <div class="metric-content">
                <div class="metric-value">
                  {{ accountOverview.balance || 0 }}
                </div>
                <div class="metric-change">
                  较昨日
                  <span :class="accountOverview.change >= 0 ? 'increase' : 'decrease'">
                    {{ accountOverview.change >= 0 ? '+' : '' }}{{ accountOverview.change || 0 }}
                  </span>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card :bordered="false" class="metric-card">
              <template #title>
                <span class="metric-title">
                  <RiseOutlined class="metric-icon" />
                  本月收入
                </span>
              </template>
              <div class="metric-content">
                <div class="metric-value">
                  {{ accountOverview.monthIncome || 0 }}
                </div>
                <div class="metric-change">
                  较上月
                  <span :class="accountOverview.monthIncomeChange >= 0 ? 'increase' : 'decrease'">
                    {{ accountOverview.monthIncomeChange >= 0 ? '+' : '' }}{{ accountOverview.monthIncomeChange || 0 }}
                  </span>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card :bordered="false" class="metric-card">
              <template #title>
                <span class="metric-title">
                  <FallOutlined class="metric-icon" />
                  本月支出
                </span>
              </template>
              <div class="metric-content">
                <div class="metric-value">
                  {{ accountOverview.monthExpense || 0 }}
                </div>
                <div class="metric-change">
                  较上月
                  <span :class="accountOverview.monthExpenseChange <= 0 ? 'increase' : 'decrease'">
                    {{ accountOverview.monthExpenseChange >= 0 ? '+' : '' }}{{ accountOverview.monthExpenseChange || 0 }}
                  </span>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-card>
    </a-card>

    <!-- 配置抽屉 -->
    <a-drawer
      title="银行系统连接配置"
      :visible="configVisible"
      :width="500"
      @close="configVisible = false"
    >
      <a-form
        ref="configFormRef"
        :model="configForm"
        :rules="rules"
        layout="vertical"
      >
        <a-form-item label="银行选择" name="bankCode">
          <a-select v-model:value="configForm.bankCode" placeholder="请选择银行">
            <a-select-option value="icbc">
              中国工商银行
            </a-select-option>
            <a-select-option value="abc">
              中国农业银行
            </a-select-option>
            <a-select-option value="boc">
              中国银行
            </a-select-option>
            <a-select-option value="ccb">
              中国建设银行
            </a-select-option>
            <a-select-option value="cmb">
              招商银行
            </a-select-option>
            <a-select-option value="spdb">
              浦发银行
            </a-select-option>
            <a-select-option value="cib">
              兴业银行
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="账户类型" name="accountType">
          <a-radio-group v-model:value="configForm.accountType">
            <a-radio value="basic">
              基本户
            </a-radio>
            <a-radio value="general">
              一般户
            </a-radio>
            <a-radio value="special">
              专用户
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="企业网银账号" name="username">
          <a-input v-model:value="configForm.username" placeholder="请输入企业网银账号" />
        </a-form-item>

        <a-form-item label="企业网银密码" name="password">
          <a-input-password v-model:value="configForm.password" placeholder="请输入企业网银密码" />
        </a-form-item>

        <a-form-item label="银行账号" name="accountNo">
          <a-input v-model:value="configForm.accountNo" placeholder="请输入银行账号" />
        </a-form-item>

        <a-form-item label="同步周期" name="syncPeriod">
          <a-radio-group v-model:value="configForm.syncPeriod">
            <a-radio value="daily">
              每日
            </a-radio>
            <a-radio value="weekly">
              每周
            </a-radio>
            <a-radio value="monthly">
              每月
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="自动核销设置">
          <a-checkbox-group v-model:value="configForm.autoSettings">
            <a-checkbox value="autoReconcile">
              自动核销匹配交易
            </a-checkbox>
            <a-checkbox value="autoImport">
              自动导入交易到财务系统
            </a-checkbox>
            <a-checkbox value="notifyNew">
              新交易通知
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" :loading="submitLoading" @click="handleSubmitConfig">
            建立连接
          </a-button>
          <a-button style="margin-left: 8px" @click="configVisible = false">
            取消
          </a-button>
        </a-form-item>
      </a-form>
    </a-drawer>

    <!-- 同步模态框 -->
    <a-modal
      title="银行流水同步"
      :visible="syncModalVisible"
      :footer="null"
      @cancel="syncModalVisible = false"
    >
      <a-form layout="vertical">
        <a-form-item label="选择同步时间范围">
          <a-radio-group v-model:value="syncForm.dateRange">
            <a-radio value="today">
              今天
            </a-radio>
            <a-radio value="yesterday">
              昨天
            </a-radio>
            <a-radio value="thisWeek">
              本周
            </a-radio>
            <a-radio value="lastWeek">
              上周
            </a-radio>
            <a-radio value="thisMonth">
              本月
            </a-radio>
            <a-radio value="lastMonth">
              上月
            </a-radio>
            <a-radio value="custom">
              自定义
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="syncForm.dateRange === 'custom'">
          <a-range-picker v-model:value="syncForm.customDateRange" style="width: 100%" />
        </a-form-item>

        <a-form-item label="同步后自动处理">
          <a-checkbox-group v-model:value="syncForm.autoActions">
            <a-checkbox value="autoReconcile">
              自动核销匹配的交易
            </a-checkbox>
            <a-checkbox value="autoImport">
              自动导入到财务系统
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" :loading="syncLoading" @click="startSync">
            开始同步
          </a-button>
          <a-button style="margin-left: 8px" @click="syncModalVisible = false">
            取消
          </a-button>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  SyncOutlined,
  BankOutlined,
  ReloadOutlined,
  DownloadOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'BankIntegration',
  components: {
    SyncOutlined,
    BankOutlined,
    ReloadOutlined,
    DownloadOutlined,
    RiseOutlined,
    FallOutlined
  },
  setup () {
    const loading = ref(false)
    const tableLoading = ref(false)
    const configFormRef = ref(null)
    const submitLoading = ref(false)
    const disconnectLoading = ref(false)
    const syncLoading = ref(false)

    const configVisible = ref(false)
    const syncModalVisible = ref(false)
    const connected = ref(false)

    // 银行信息
    const bankInfo = reactive({
      bankCode: '',
      lastSyncTime: '',
      syncPeriod: '',
      accountInfo: ''
    })

    // 日期范围和同步类型
    const dateRange = ref([])
    const syncType = ref('all')

    // 配置表单
    const configForm = reactive({
      bankCode: undefined,
      accountType: 'basic',
      username: '',
      password: '',
      accountNo: '',
      syncPeriod: 'daily',
      autoSettings: ['autoReconcile', 'notifyNew']
    })

    // 同步表单
    const syncForm = reactive({
      dateRange: 'thisMonth',
      customDateRange: [],
      autoActions: ['autoReconcile']
    })

    // 表单验证规则
    const rules = {
      bankCode: [{ required: true, message: '请选择银行', trigger: 'change' }],
      username: [{ required: true, message: '请输入企业网银账号', trigger: 'blur' }],
      password: [{ required: true, message: '请输入企业网银密码', trigger: 'blur' }],
      accountNo: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
      syncPeriod: [{ required: true, message: '请选择同步周期', trigger: 'change' }]
    }

    // 交易列表列定义
    const transactionColumns = [
      {
        title: '交易日期',
        dataIndex: 'transactionDate',
        key: 'transactionDate',
        width: 100
      },
      {
        title: '交易金额',
        dataIndex: 'amount',
        key: 'amount',
        width: 120,
        align: 'right'
      },
      {
        title: '对方账户名',
        dataIndex: 'counterpartyName',
        key: 'counterpartyName',
        width: 160
      },
      {
        title: '对方账号',
        dataIndex: 'counterpartyAccount',
        key: 'counterpartyAccount',
        width: 160
      },
      {
        title: '交易摘要',
        dataIndex: 'summary',
        key: 'summary',
        width: 180
      },
      {
        title: '交易流水号',
        dataIndex: 'serialNo',
        key: 'serialNo',
        width: 160
      },
      {
        title: '核销状态',
        dataIndex: 'reconciled',
        key: 'reconciled',
        width: 100
      },
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        width: 120,
        fixed: 'right'
      }
    ]

    // 交易数据
    const transactions = ref([])

    // 账户概览数据
    const accountOverview = reactive({
      balance: 0,
      change: 0,
      monthIncome: 0,
      monthIncomeChange: 0,
      monthExpense: 0,
      monthExpenseChange: 0
    })

    // 获取银行名称
    const getBankName = (code) => {
      const bankMap = {
        icbc: '中国工商银行',
        abc: '中国农业银行',
        boc: '中国银行',
        ccb: '中国建设银行',
        cmb: '招商银行',
        spdb: '浦发银行',
        cib: '兴业银行'
      }
      return bankMap[code] || code
    }

    // 获取同步周期文本
    const getSyncPeriodText = (period) => {
      const periodMap = {
        daily: '每日',
        weekly: '每周',
        monthly: '每月'
      }
      return periodMap[period] || period
    }

    // 显示配置抽屉
    const showConfigDrawer = () => {
      configVisible.value = true
    }

    // 显示同步模态框
    const showSyncModal = () => {
      syncModalVisible.value = true
    }

    // 禁用日期
    const disabledDate = (current) => {
      return current && current > new Date()
    }

    // 提交配置
    const handleSubmitConfig = async () => {
      try {
        if (!configFormRef.value) return

        await configFormRef.value.validate()
        submitLoading.value = true

        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1500))

        message.success('银行系统连接成功')
        configVisible.value = false

        // 模拟连接成功后的状态更新
        connected.value = true
        Object.assign(bankInfo, {
          bankCode: configForm.bankCode,
          lastSyncTime: new Date().toLocaleString(),
          syncPeriod: configForm.syncPeriod,
          accountInfo: `${getBankName(configForm.bankCode)} ${configForm.accountNo.substr(0, 4)}****${configForm.accountNo.substr(-4)}`
        })

        // 获取交易数据
        fetchTransactions()
        fetchAccountOverview()
      } catch (error) {
        console.error('提交配置失败:', error)
        message.error('连接失败，请检查表单填写是否正确')
      } finally {
        submitLoading.value = false
      }
    }

    // 断开连接
    const handleDisconnect = async () => {
      try {
        disconnectLoading.value = true

        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1000))

        message.success('已断开银行系统连接')
        connected.value = false

        // 清空数据
        Object.assign(bankInfo, {
          bankCode: '',
          lastSyncTime: '',
          syncPeriod: '',
          accountInfo: ''
        })
        transactions.value = []
        Object.assign(accountOverview, {
          balance: 0,
          change: 0,
          monthIncome: 0,
          monthIncomeChange: 0,
          monthExpense: 0,
          monthExpenseChange: 0
        })
      } catch (error) {
        console.error('断开连接失败:', error)
        message.error('断开连接失败，请稍后重试')
      } finally {
        disconnectLoading.value = false
      }
    }

    // 开始同步
    const startSync = async () => {
      try {
        syncLoading.value = true

        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 2000))

        message.success('银行流水同步成功')
        syncModalVisible.value = false

        // 更新最后同步时间
        bankInfo.lastSyncTime = new Date().toLocaleString()

        // 重新获取交易数据
        fetchTransactions()
        fetchAccountOverview()
      } catch (error) {
        console.error('同步失败:', error)
        message.error('同步失败，请稍后重试')
      } finally {
        syncLoading.value = false
      }
    }

    // 获取交易数据
    const fetchTransactions = async () => {
      tableLoading.value = true
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 模拟交易数据
        transactions.value = [
          {
            id: '1',
            transactionDate: '2023-11-01',
            amount: '10,000.00',
            direction: 'in',
            counterpartyName: '上海贸易有限公司',
            counterpartyAccount: '****************',
            summary: '货款',
            serialNo: '****************',
            reconciled: true
          },
          {
            id: '2',
            transactionDate: '2023-11-02',
            amount: '5,320.50',
            direction: 'out',
            counterpartyName: '北京科技有限公司',
            counterpartyAccount: '****************',
            summary: '设备采购',
            serialNo: '****************',
            reconciled: false
          },
          {
            id: '3',
            transactionDate: '2023-11-03',
            amount: '8,500.00',
            direction: 'in',
            counterpartyName: '广州贸易有限公司',
            counterpartyAccount: '****************',
            summary: '服务费',
            serialNo: '****************',
            reconciled: true
          },
          {
            id: '4',
            transactionDate: '2023-11-05',
            amount: '2,100.00',
            direction: 'out',
            counterpartyName: '深圳物流有限公司',
            counterpartyAccount: '****************',
            summary: '运费',
            serialNo: '****************',
            reconciled: false
          },
          {
            id: '5',
            transactionDate: '2023-11-07',
            amount: '15,000.00',
            direction: 'in',
            counterpartyName: '杭州电子有限公司',
            counterpartyAccount: '****************',
            summary: '货款',
            serialNo: '****************',
            reconciled: false
          }
        ]
      } catch (error) {
        console.error('获取交易数据失败:', error)
        message.error('获取交易数据失败')
      } finally {
        tableLoading.value = false
      }
    }

    // 获取账户概览数据
    const fetchAccountOverview = async () => {
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 800))

        // 模拟账户概览数据
        Object.assign(accountOverview, {
          balance: '125,800.50',
          change: '+10,500.00',
          monthIncome: '33,500.00',
          monthIncomeChange: '+5,200.00',
          monthExpense: '7,420.50',
          monthExpenseChange: '-1,500.00'
        })
      } catch (error) {
        console.error('获取账户概览失败:', error)
        message.error('获取账户概览失败')
      }
    }

    // 查看交易详情
    const viewDetail = (record) => {
      message.info(`查看交易详情：${record.serialNo}`)
    }

    // 核销交易
    const reconcileTransaction = (record) => {
      message.success(`已核销交易：${record.serialNo}`)
      record.reconciled = true
    }

    // 导出交易
    const exportTransactions = () => {
      message.success('交易数据导出成功')
    }

    onMounted(() => {
      // 模拟初始检查连接状态
      loading.value = true
      setTimeout(() => {
        loading.value = false
        connected.value = false // 默认未连接
      }, 1000)
    })

    return {
      loading,
      tableLoading,
      configFormRef,
      submitLoading,
      disconnectLoading,
      syncLoading,
      configVisible,
      syncModalVisible,
      connected,
      bankInfo,
      dateRange,
      syncType,
      configForm,
      syncForm,
      rules,
      transactionColumns,
      transactions,
      accountOverview,
      getBankName,
      getSyncPeriodText,
      showConfigDrawer,
      showSyncModal,
      disabledDate,
      handleSubmitConfig,
      handleDisconnect,
      startSync,
      fetchTransactions,
      viewDetail,
      reconcileTransaction,
      exportTransactions
    }
  }
})
</script>

<style scoped>
.bank-integration-container {
  padding: 0 12px;
}

.status-card {
  margin-bottom: 24px;
}

.sync-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.amount-in {
  color: #52c41a;
  font-weight: 500;
}

.amount-out {
  color: #f5222d;
  font-weight: 500;
}

.metric-card {
  height: 100%;
}

.metric-title {
  display: flex;
  align-items: center;
}

.metric-icon {
  margin-right: 8px;
  font-size: 16px;
}

.metric-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.metric-value {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 8px;
}

.metric-change {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}

.increase {
  color: #52c41a;
}

.decrease {
  color: #f5222d;
}
</style>
