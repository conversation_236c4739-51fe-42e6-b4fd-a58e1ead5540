<!--
  审计日志详情页面
  功能：展示单个审计日志的详细信息
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <div class="audit-log-detail-page">
    <div class="page-header">
      <a-page-header
        title="审计日志详情"
        @back="handleBack"
      >
        <template #extra>
          <a-space>
            <a-button @click="handleRefresh">
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新
            </a-button>
            <a-button type="primary" @click="handleExport">
              <template #icon>
                <DownloadOutlined />
              </template>
              导出
            </a-button>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <div class="page-content">
      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
      </div>
      
      <div v-else-if="logData" class="log-detail">
        <!-- 基本信息 -->
        <a-card title="基本信息" class="info-card">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="日志ID">
              {{ logData.id }}
            </a-descriptions-item>
            <a-descriptions-item label="操作用户">
              {{ logData.user_name || '系统' }}
            </a-descriptions-item>
            <a-descriptions-item label="操作类型">
              <a-tag :color="getActionColor(logData.action)">
                {{ getActionText(logData.action) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="资源类型">
              {{ logData.resource_type }}
            </a-descriptions-item>
            <a-descriptions-item label="资源ID">
              {{ logData.resource_id }}
            </a-descriptions-item>
            <a-descriptions-item label="操作状态">
              <a-tag :color="logData.status === 'success' ? 'green' : 'red'">
                {{ logData.status === 'success' ? '成功' : '失败' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="操作时间">
              {{ formatDate(logData.created_at) }}
            </a-descriptions-item>
            <a-descriptions-item label="IP地址">
              {{ logData.ip_address || '-' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 操作描述 -->
        <a-card v-if="logData.description" title="操作描述" class="info-card">
          <div class="content-text">{{ logData.description }}</div>
        </a-card>

        <!-- 状态对比 -->
        <a-card v-if="logData.before_state || logData.after_state" title="状态变更" class="info-card">
          <a-row :gutter="16">
            <a-col :span="12" v-if="logData.before_state">
              <h4>操作前状态</h4>
              <pre class="json-content">{{ formatJSON(logData.before_state) }}</pre>
            </a-col>
            <a-col :span="12" v-if="logData.after_state">
              <h4>操作后状态</h4>
              <pre class="json-content">{{ formatJSON(logData.after_state) }}</pre>
            </a-col>
          </a-row>
        </a-card>

        <!-- 错误信息 -->
        <a-card v-if="logData.error_message" title="错误信息" class="info-card">
          <div class="error-text">{{ logData.error_message }}</div>
        </a-card>

        <!-- 技术信息 -->
        <a-card title="技术信息" class="info-card">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="操作模块">
              {{ logData.module || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="操作耗时">
              {{ logData.duration ? `${logData.duration}ms` : '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="风险等级">
              <a-tag :color="getRiskColor(logData.risk_level)">
                {{ getRiskText(logData.risk_level) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="敏感数据">
              <a-tag :color="logData.sensitive_data ? 'orange' : 'default'">
                {{ logData.sensitive_data ? '是' : '否' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="用户代理" span="2">
              <div class="user-agent">{{ logData.user_agent || '-' }}</div>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </div>

      <div v-else class="empty-container">
        <a-empty description="未找到日志信息" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import { formatDate } from '@/utils/date'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const logData = ref(null)

// 生命周期
onMounted(() => {
  loadLogDetail()
})

// 加载日志详情
const loadLogDetail = async () => {
  const logId = route.params.id
  if (!logId) {
    message.error('缺少日志ID参数')
    return
  }
  
  loading.value = true
  try {
    // 这里应该调用API获取日志详情
    // const data = await auditLogApi.getById(logId)
    // logData.value = data
    
    // 临时模拟数据
    logData.value = {
      id: logId,
      user_name: '张三',
      action: 'update',
      resource_type: 'enterprise',
      resource_id: 'ent_001',
      status: 'success',
      description: '更新企业基本信息',
      ip_address: '*************',
      user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      module: 'enterprise',
      duration: 150,
      risk_level: 'medium',
      sensitive_data: true,
      before_state: '{"name":"旧企业名称","code":"OLD001","status":"active"}',
      after_state: '{"name":"新企业名称","code":"NEW001","status":"active"}',
      error_message: null,
      created_at: new Date().toISOString()
    }
  } catch (error) {
    message.error('加载日志详情失败：' + error.message)
    logData.value = null
  } finally {
    loading.value = false
  }
}

// 返回
const handleBack = () => {
  router.back()
}

// 刷新
const handleRefresh = () => {
  loadLogDetail()
}

// 导出
const handleExport = () => {
  message.info('导出功能开发中...')
}

// 辅助函数
const getActionColor = (action) => {
  const colors = {
    create: 'green',
    update: 'blue',
    delete: 'red',
    view: 'default',
    login: 'purple',
    logout: 'orange'
  }
  return colors[action] || 'default'
}

const getActionText = (action) => {
  const texts = {
    create: '创建',
    update: '更新',
    delete: '删除',
    view: '查看',
    login: '登录',
    logout: '登出'
  }
  return texts[action] || action
}

const getRiskColor = (level) => {
  const colors = {
    low: 'green',
    medium: 'orange',
    high: 'red',
    critical: 'purple'
  }
  return colors[level] || 'default'
}

const getRiskText = (level) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重'
  }
  return texts[level] || level
}

const formatJSON = (jsonStr) => {
  try {
    const obj = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr
    return JSON.stringify(obj, null, 2)
  } catch (error) {
    return jsonStr
  }
}
</script>

<style scoped>
.audit-log-detail-page {
  background-color: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background-color: white;
  margin-bottom: 16px;
}

.page-content {
  padding: 0 24px 24px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  background-color: white;
  border-radius: 6px;
}

.log-detail {
  max-width: 1200px;
  margin: 0 auto;
}

.info-card {
  margin-bottom: 16px;
}

.content-text {
  line-height: 1.6;
  color: #262626;
  white-space: pre-wrap;
  word-break: break-word;
}

.error-text {
  color: #ff4d4f;
  background-color: #fff2f0;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #ffccc7;
  font-family: monospace;
  white-space: pre-wrap;
}

.json-content {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
  line-height: 1.45;
  overflow-x: auto;
  white-space: pre;
  max-height: 300px;
}

.user-agent {
  font-family: monospace;
  font-size: 12px;
  color: #666;
  word-break: break-all;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  background-color: #fafafa;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}

:deep(.ant-page-header-heading-title) {
  font-size: 20px;
  font-weight: 600;
}
</style>
