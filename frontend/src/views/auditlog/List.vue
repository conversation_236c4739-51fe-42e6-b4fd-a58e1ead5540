<template>
  <div class="audit-log-list-container">
    <!-- 页面头部 -->
    <MacOSPageHeader
      :title="pageTitle"
      :breadcrumbs="breadcrumbs"
      :show-back="false"
    >
      <template #actions>
        <MacOSButton
          icon="bar-chart"
          @click="handleViewStats"
        >
          统计分析
        </MacOSButton>
        <MacOSButton
          icon="download"
          @click="handleExport"
        >
          导出日志
        </MacOSButton>
        <MacOSButton
          icon="delete"
          @click="handleCleanup"
        >
          清理日志
        </MacOSButton>
      </template>
    </MacOSPageHeader>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <MacOSCard class="stat-card">
        <a-statistic
          title="总日志数"
          :value="stats.total"
          :value-style="{ color: '#1890ff' }"
        />
      </MacOSCard>
      <MacOSCard class="stat-card">
        <a-statistic
          title="今日操作"
          :value="stats.today"
          :value-style="{ color: '#52c41a' }"
        />
      </MacOSCard>
      <MacOSCard class="stat-card">
        <a-statistic
          title="错误操作"
          :value="stats.errors"
          :value-style="{ color: '#f5222d' }"
        />
      </MacOSCard>
      <MacOSCard class="stat-card">
        <a-statistic
          title="高风险操作"
          :value="stats.highRisk"
          :value-style="{ color: '#fa8c16' }"
        />
      </MacOSCard>
    </div>

    <!-- 搜索表单 -->
    <MacOSCard class="search-card">
      <SearchForm
        :form-items="searchFormItems"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
      />
    </MacOSCard>

    <!-- 数据表格 -->
    <MacOSCard>
      <template #title>
        <div class="table-header">
          <span>审计日志</span>
          <div class="table-info">
            <a-tag color="blue">共 {{ pagination.total }} 条记录</a-tag>
          </div>
        </div>
      </template>

      <MacOSDataTable
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: 1500 }"
        @change="handleTableChange"
      />
    </MacOSCard>

    <!-- 日志详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="审计日志详情"
      :width="800"
      :footer="null"
    >
      <AuditLogDetail :audit-log="currentRecord" />
    </a-modal>

    <!-- 统计分析弹窗 -->
    <a-modal
      v-model:visible="statsModalVisible"
      title="审计日志统计分析"
      :width="1000"
      :footer="null"
    >
      <AuditLogStats :stats-data="statsData" />
    </a-modal>

    <!-- 清理日志弹窗 -->
    <a-modal
      v-model:visible="cleanupModalVisible"
      title="清理审计日志"
      :width="500"
      :confirm-loading="cleanupLoading"
      @ok="handleCleanupOk"
      @cancel="handleCleanupCancel"
    >
      <div class="cleanup-form">
        <p>清理指定天数之前的审计日志，此操作不可恢复。</p>
        <a-form-item label="保留天数">
          <a-input-number
            v-model:value="retentionDays"
            :min="1"
            :max="3650"
            placeholder="请输入保留天数"
            style="width: 100%"
          />
          <div class="form-tip">
            建议保留至少90天的日志记录，以满足审计要求
          </div>
        </a-form-item>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  MacOSPageHeader,
  MacOSCard,
  MacOSButton,
  MacOSDataTable,
  SearchForm
} from '@/components/common'
import AuditLogDetail from './components/AuditLogDetail.vue'
import AuditLogStats from './components/AuditLogStats.vue'
import {
  getAuditLogs,
  getAuditLogById,
  getAuditLogStats,
  cleanupOldAuditLogs,
  exportAuditLogs,
  getActionTypes,
  getResourceTypes,
  getRiskLevels
} from '@/api/auditLog'

export default defineComponent({
  name: 'AuditLogList',
  components: {
    MacOSPageHeader,
    MacOSCard,
    MacOSButton,
    MacOSDataTable,
    SearchForm,
    AuditLogDetail,
    AuditLogStats
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const dataSource = ref([])
    const detailModalVisible = ref(false)
    const statsModalVisible = ref(false)
    const cleanupModalVisible = ref(false)
    const cleanupLoading = ref(false)
    const currentRecord = ref({})
    const statsData = ref({})
    const retentionDays = ref(90)
    const actionTypes = ref([])
    const resourceTypes = ref([])
    const riskLevels = ref([])

    // 统计数据
    const stats = reactive({
      total: 0,
      today: 0,
      errors: 0,
      highRisk: 0
    })

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
    })

    // 搜索表单配置
    const searchForm = reactive({
      userId: '',
      action: '',
      resourceType: '',
      resourceId: '',
      status: '',
      module: '',
      riskLevel: '',
      startDate: '',
      endDate: ''
    })

    // 计算属性
    const pageTitle = computed(() => '审计日志')
    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '系统管理', path: '/system' },
      { title: '审计日志' }
    ])

    const searchFormItems = computed(() => [
      {
        type: 'input',
        field: 'userId',
        label: '用户ID',
        placeholder: '请输入用户ID'
      },
      {
        type: 'select',
        field: 'action',
        label: '操作类型',
        placeholder: '请选择操作类型',
        options: actionTypes.value,
        allowClear: true
      },
      {
        type: 'select',
        field: 'resourceType',
        label: '资源类型',
        placeholder: '请选择资源类型',
        options: resourceTypes.value,
        allowClear: true
      },
      {
        type: 'input',
        field: 'resourceId',
        label: '资源ID',
        placeholder: '请输入资源ID'
      },
      {
        type: 'select',
        field: 'status',
        label: '状态',
        placeholder: '请选择状态',
        options: [
          { label: '成功', value: 'success' },
          { label: '失败', value: 'error' },
          { label: '警告', value: 'warning' }
        ],
        allowClear: true
      },
      {
        type: 'select',
        field: 'riskLevel',
        label: '风险等级',
        placeholder: '请选择风险等级',
        options: riskLevels.value,
        allowClear: true
      },
      {
        type: 'date-range',
        field: ['startDate', 'endDate'],
        label: '操作时间',
        placeholder: ['开始日期', '结束日期']
      }
    ])

    // 表格列配置
    const columns = [
      {
        title: '用户',
        dataIndex: 'userName',
        key: 'userName',
        width: 120,
        ellipsis: true
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 100,
        customRender: ({ text }) => {
          const actionOption = actionTypes.value.find(item => item.value === text)
          return actionOption?.label || text
        }
      },
      {
        title: '资源类型',
        dataIndex: 'resourceType',
        key: 'resourceType',
        width: 100,
        customRender: ({ text }) => {
          const resourceOption = resourceTypes.value.find(item => item.value === text)
          return resourceOption?.label || text
        }
      },
      {
        title: '资源ID',
        dataIndex: 'resourceId',
        key: 'resourceId',
        width: 120,
        ellipsis: true
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        ellipsis: true,
        width: 200
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 80,
        customRender: ({ text }) => {
          const statusMap = {
            success: { color: 'green', text: '成功' },
            error: { color: 'red', text: '失败' },
            warning: { color: 'orange', text: '警告' }
          }
          const status = statusMap[text] || { color: 'default', text }
          return h('a-tag', { color: status.color }, status.text)
        }
      },
      {
        title: '风险等级',
        dataIndex: 'riskLevel',
        key: 'riskLevel',
        width: 100,
        customRender: ({ text }) => {
          const riskOption = riskLevels.value.find(item => item.value === text)
          return h('a-tag', { 
            color: riskOption?.color || 'default' 
          }, riskOption?.label || text)
        }
      },
      {
        title: 'IP地址',
        dataIndex: 'ipAddress',
        key: 'ipAddress',
        width: 120
      },
      {
        title: '操作时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 160,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleString() : '-'
        }
      },
      {
        title: '操作',
        key: 'action',
        width: 100,
        fixed: 'right',
        customRender: ({ record }) => {
          return h('a', {
            onClick: () => handleView(record)
          }, '查看详情')
        }
      }
    ]

    // 方法
    const fetchData = async () => {
      loading.value = true
      try {
        const params = {
          ...searchForm,
          page: pagination.current,
          pageSize: pagination.pageSize
        }

        const response = await getAuditLogs(params)
        if (response.code === 200) {
          dataSource.value = response.data.logs || []
          pagination.total = response.data.total || 0
        }
      } catch (error) {
        message.error('获取审计日志失败')
      } finally {
        loading.value = false
      }
    }

    const fetchStats = async () => {
      try {
        // 获取基础统计
        const today = new Date().toISOString().slice(0, 10)
        const response = await getAuditLogStats({
          startDate: today,
          endDate: today
        })

        if (response.code === 200) {
          stats.total = response.data.totalLogs || 0
          stats.today = response.data.dailyStats?.[0]?.count || 0
          stats.errors = response.data.logsByStatus?.find(s => s.status === 'error')?.count || 0
          stats.highRisk = response.data.logsByRiskLevel?.find(r => r.riskLevel === 'high')?.count || 0
        }
      } catch (error) {
        console.error('获取统计信息失败:', error)
      }
    }

    const fetchOptions = () => {
      actionTypes.value = getActionTypes()
      resourceTypes.value = getResourceTypes()
      riskLevels.value = getRiskLevels()
    }

    const handleSearch = (values) => {
      Object.assign(searchForm, values)
      pagination.current = 1
      fetchData()
    }

    const handleReset = () => {
      Object.assign(searchForm, {
        userId: '',
        action: '',
        resourceType: '',
        resourceId: '',
        status: '',
        module: '',
        riskLevel: '',
        startDate: '',
        endDate: ''
      })
      pagination.current = 1
      fetchData()
    }

    const handleTableChange = (pag) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      fetchData()
    }

    const handleView = async (record) => {
      try {
        const response = await getAuditLogById(record.id)
        if (response.code === 200) {
          currentRecord.value = response.data
          detailModalVisible.value = true
        }
      } catch (error) {
        message.error('获取日志详情失败')
      }
    }

    const handleViewStats = async () => {
      try {
        const response = await getAuditLogStats({
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10),
          endDate: new Date().toISOString().slice(0, 10)
        })
        if (response.code === 200) {
          statsData.value = response.data
          statsModalVisible.value = true
        }
      } catch (error) {
        message.error('获取统计数据失败')
      }
    }

    const handleExport = async () => {
      try {
        const blob = await exportAuditLogs('excel', searchForm)
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `审计日志_${new Date().toISOString().slice(0, 10)}.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)
        message.success('导出成功')
      } catch (error) {
        message.error('导出失败')
      }
    }

    const handleCleanup = () => {
      cleanupModalVisible.value = true
    }

    const handleCleanupOk = async () => {
      if (!retentionDays.value || retentionDays.value < 1) {
        message.error('请输入有效的保留天数')
        return
      }

      Modal.confirm({
        title: '确认清理',
        content: `确定要清理 ${retentionDays.value} 天前的审计日志吗？此操作不可恢复。`,
        onOk: async () => {
          cleanupLoading.value = true
          try {
            await cleanupOldAuditLogs(retentionDays.value)
            message.success('清理成功')
            cleanupModalVisible.value = false
            fetchData()
            fetchStats()
          } catch (error) {
            message.error('清理失败')
          } finally {
            cleanupLoading.value = false
          }
        }
      })
    }

    const handleCleanupCancel = () => {
      cleanupModalVisible.value = false
    }

    // 生命周期
    onMounted(() => {
      fetchOptions()
      fetchData()
      fetchStats()
    })

    return {
      // 响应式数据
      loading,
      dataSource,
      detailModalVisible,
      statsModalVisible,
      cleanupModalVisible,
      cleanupLoading,
      currentRecord,
      statsData,
      retentionDays,
      stats,
      pagination,
      searchForm,

      // 计算属性
      pageTitle,
      breadcrumbs,
      searchFormItems,
      columns,

      // 方法
      handleSearch,
      handleReset,
      handleTableChange,
      handleView,
      handleViewStats,
      handleExport,
      handleCleanup,
      handleCleanupOk,
      handleCleanupCancel
    }
  }
})
</script>

<style scoped>
.audit-log-list-container {
  background: #f0f2f5;
  min-height: 100vh;
  padding: 24px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.stat-card {
  text-align: center;
}

.search-card {
  margin-bottom: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-info {
  display: flex;
  gap: 8px;
}

.cleanup-form {
  padding: 16px 0;
}

.form-tip {
  margin-top: 4px;
  color: #666;
  font-size: 12px;
}
</style>
