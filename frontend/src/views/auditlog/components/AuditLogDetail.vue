<!--
  审计日志详情组件
  功能：展示审计日志详细信息
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <a-modal
    :visible="visible"
    title="审计日志详情"
    :width="800"
    :footer="null"
    @cancel="handleCancel"
  >
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    
    <div v-else-if="logData" class="log-detail">
      <!-- 基本信息 -->
      <a-card title="基本信息" class="info-card">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="日志ID">
            {{ logData.id }}
          </a-descriptions-item>
          <a-descriptions-item label="操作用户">
            {{ logData.user_name || '系统' }}
          </a-descriptions-item>
          <a-descriptions-item label="操作类型">
            <a-tag :color="getActionColor(logData.action)">
              {{ getActionText(logData.action) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="资源类型">
            {{ logData.resource_type }}
          </a-descriptions-item>
          <a-descriptions-item label="资源ID">
            {{ logData.resource_id }}
          </a-descriptions-item>
          <a-descriptions-item label="操作状态">
            <a-tag :color="logData.status === 'success' ? 'green' : 'red'">
              {{ logData.status === 'success' ? '成功' : '失败' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="操作时间">
            {{ formatDate(logData.created_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="IP地址">
            {{ logData.ip_address || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 操作描述 -->
      <a-card v-if="logData.description" title="操作描述" class="info-card">
        <div class="content-text">{{ logData.description }}</div>
      </a-card>

      <!-- 操作前状态 -->
      <a-card v-if="logData.before_state" title="操作前状态" class="info-card">
        <pre class="json-content">{{ formatJSON(logData.before_state) }}</pre>
      </a-card>

      <!-- 操作后状态 -->
      <a-card v-if="logData.after_state" title="操作后状态" class="info-card">
        <pre class="json-content">{{ formatJSON(logData.after_state) }}</pre>
      </a-card>

      <!-- 错误信息 -->
      <a-card v-if="logData.error_message" title="错误信息" class="info-card">
        <div class="error-text">{{ logData.error_message }}</div>
      </a-card>

      <!-- 技术信息 -->
      <a-card title="技术信息" class="info-card">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="操作模块">
            {{ logData.module || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="操作耗时">
            {{ logData.duration ? `${logData.duration}ms` : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="风险等级">
            <a-tag :color="getRiskColor(logData.risk_level)">
              {{ getRiskText(logData.risk_level) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="敏感数据">
            <a-tag :color="logData.sensitive_data ? 'orange' : 'default'">
              {{ logData.sensitive_data ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="用户代理" span="2">
            <div class="user-agent">{{ logData.user_agent || '-' }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>

    <div v-else class="empty-container">
      <a-empty description="未找到日志信息" />
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { formatDate } from '@/utils/date'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  logId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const loading = ref(false)
const logData = ref(null)

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal && props.logId) {
    loadLogDetail()
  }
})

watch(() => props.logId, (newVal) => {
  if (newVal && props.visible) {
    loadLogDetail()
  }
})

// 加载日志详情
const loadLogDetail = async () => {
  if (!props.logId) return
  
  loading.value = true
  try {
    // 这里应该调用API获取日志详情
    // const data = await auditLogApi.getById(props.logId)
    // logData.value = data
    
    // 临时模拟数据
    logData.value = {
      id: props.logId,
      user_name: '张三',
      action: 'create',
      resource_type: 'enterprise',
      resource_id: 'ent_001',
      status: 'success',
      description: '创建企业信息',
      ip_address: '*************',
      user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      module: 'enterprise',
      duration: 150,
      risk_level: 'low',
      sensitive_data: false,
      before_state: null,
      after_state: '{"name":"测试企业","code":"TEST001"}',
      error_message: null,
      created_at: new Date().toISOString()
    }
  } catch (error) {
    message.error('加载日志详情失败：' + error.message)
    logData.value = null
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
  logData.value = null
}

// 辅助函数
const getActionColor = (action) => {
  const colors = {
    create: 'green',
    update: 'blue',
    delete: 'red',
    view: 'default',
    login: 'purple',
    logout: 'orange'
  }
  return colors[action] || 'default'
}

const getActionText = (action) => {
  const texts = {
    create: '创建',
    update: '更新',
    delete: '删除',
    view: '查看',
    login: '登录',
    logout: '登出'
  }
  return texts[action] || action
}

const getRiskColor = (level) => {
  const colors = {
    low: 'green',
    medium: 'orange',
    high: 'red',
    critical: 'purple'
  }
  return colors[level] || 'default'
}

const getRiskText = (level) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重'
  }
  return texts[level] || level
}

const formatJSON = (jsonStr) => {
  try {
    const obj = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr
    return JSON.stringify(obj, null, 2)
  } catch (error) {
    return jsonStr
  }
}
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.log-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card {
  margin-bottom: 16px;
}

.content-text {
  line-height: 1.6;
  color: #262626;
  white-space: pre-wrap;
  word-break: break-word;
}

.error-text {
  color: #ff4d4f;
  background-color: #fff2f0;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #ffccc7;
  font-family: monospace;
  white-space: pre-wrap;
}

.json-content {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
  line-height: 1.45;
  overflow-x: auto;
  white-space: pre;
}

.user-agent {
  font-family: monospace;
  font-size: 12px;
  color: #666;
  word-break: break-all;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  background-color: #fafafa;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}
</style>
