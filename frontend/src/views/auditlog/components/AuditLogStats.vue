<!--
  审计日志统计组件
  功能：展示审计日志统计信息
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <div class="audit-log-stats">
    <!-- 统计卡片 -->
    <a-row :gutter="16" class="stats-cards">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="今日操作"
            :value="stats.todayCount"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <icon-font type="icon-today" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="本周操作"
            :value="stats.weekCount"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <icon-font type="icon-week" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="失败操作"
            :value="stats.failedCount"
            :value-style="{ color: '#ff4d4f' }"
          >
            <template #prefix>
              <icon-font type="icon-error" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="高风险操作"
            :value="stats.highRiskCount"
            :value-style="{ color: '#fa8c16' }"
          >
            <template #prefix>
              <icon-font type="icon-warning" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="16" class="charts-section">
      <a-col :span="12">
        <a-card title="操作类型分布" class="chart-card">
          <div ref="actionChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="操作趋势" class="chart-card">
          <div ref="trendChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 详细统计表格 -->
    <a-card title="详细统计" class="table-card">
      <a-table
        :columns="statsColumns"
        :data-source="statsData"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'percentage'">
            <a-progress
              :percent="record.percentage"
              size="small"
              :show-info="true"
            />
          </template>
          <template v-if="column.key === 'risk_level'">
            <a-tag :color="getRiskColor(record.risk_level)">
              {{ getRiskText(record.risk_level) }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'

// 响应式数据
const stats = ref({
  todayCount: 0,
  weekCount: 0,
  failedCount: 0,
  highRiskCount: 0
})

const statsData = ref([])
const actionChartRef = ref()
const trendChartRef = ref()

// 表格列配置
const statsColumns = [
  {
    title: '操作类型',
    dataIndex: 'action',
    key: 'action',
    width: 120
  },
  {
    title: '操作次数',
    dataIndex: 'count',
    key: 'count',
    width: 100
  },
  {
    title: '占比',
    dataIndex: 'percentage',
    key: 'percentage',
    width: 150
  },
  {
    title: '成功率',
    dataIndex: 'success_rate',
    key: 'success_rate',
    width: 100,
    customRender: ({ text }) => `${text}%`
  },
  {
    title: '风险等级',
    dataIndex: 'risk_level',
    key: 'risk_level',
    width: 100
  },
  {
    title: '最近操作',
    dataIndex: 'last_operation',
    key: 'last_operation'
  }
]

// 生命周期
onMounted(() => {
  loadStats()
  nextTick(() => {
    initCharts()
  })
})

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里应该调用API获取统计数据
    // const data = await auditLogApi.getStats()
    
    // 临时模拟数据
    stats.value = {
      todayCount: 156,
      weekCount: 1024,
      failedCount: 12,
      highRiskCount: 3
    }

    statsData.value = [
      {
        action: '创建',
        count: 245,
        percentage: 35,
        success_rate: 98.5,
        risk_level: 'low',
        last_operation: '2024-01-15 10:30:00'
      },
      {
        action: '更新',
        count: 189,
        percentage: 27,
        success_rate: 96.8,
        risk_level: 'medium',
        last_operation: '2024-01-15 10:25:00'
      },
      {
        action: '删除',
        count: 78,
        percentage: 11,
        success_rate: 94.2,
        risk_level: 'high',
        last_operation: '2024-01-15 09:45:00'
      },
      {
        action: '查看',
        count: 432,
        percentage: 62,
        success_rate: 99.8,
        risk_level: 'low',
        last_operation: '2024-01-15 10:32:00'
      },
      {
        action: '登录',
        count: 89,
        percentage: 13,
        success_rate: 92.1,
        risk_level: 'medium',
        last_operation: '2024-01-15 10:15:00'
      }
    ]
  } catch (error) {
    message.error('加载统计数据失败：' + error.message)
  }
}

// 初始化图表
const initCharts = () => {
  // 这里应该使用图表库（如ECharts）来渲染图表
  // 由于没有引入图表库，这里只是占位
  if (actionChartRef.value) {
    actionChartRef.value.innerHTML = '<div style="text-align: center; padding: 50px; color: #999;">操作类型分布图表</div>'
  }
  
  if (trendChartRef.value) {
    trendChartRef.value.innerHTML = '<div style="text-align: center; padding: 50px; color: #999;">操作趋势图表</div>'
  }
}

// 辅助函数
const getRiskColor = (level) => {
  const colors = {
    low: 'green',
    medium: 'orange',
    high: 'red',
    critical: 'purple'
  }
  return colors[level] || 'default'
}

const getRiskText = (level) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重'
  }
  return texts[level] || level
}
</script>

<style scoped>
.audit-log-stats {
  padding: 24px;
}

.stats-cards {
  margin-bottom: 24px;
}

.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.table-card {
  margin-bottom: 24px;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #666;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

:deep(.ant-progress-text) {
  font-size: 12px;
}
</style>
