<!--
  审计日志统计页面
  功能：展示审计日志的统计分析
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <div class="audit-log-stats-page">
    <div class="page-header">
      <a-page-header title="审计日志统计">
        <template #extra>
          <a-space>
            <a-range-picker
              v-model:value="dateRange"
              @change="handleDateChange"
            />
            <a-button @click="handleRefresh">
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新
            </a-button>
            <a-button type="primary" @click="handleExport">
              <template #icon>
                <DownloadOutlined />
              </template>
              导出报告
            </a-button>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <div class="page-content">
      <AuditLogStats />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import AuditLogStats from './components/AuditLogStats.vue'

// 响应式数据
const dateRange = ref([])

// 日期范围变更
const handleDateChange = (dates) => {
  if (dates && dates.length === 2) {
    message.info(`统计时间范围：${dates[0].format('YYYY-MM-DD')} 至 ${dates[1].format('YYYY-MM-DD')}`)
    // 这里可以重新加载统计数据
  }
}

// 刷新
const handleRefresh = () => {
  message.success('统计数据已刷新')
  // 这里可以触发子组件刷新
}

// 导出报告
const handleExport = () => {
  message.info('导出统计报告功能开发中...')
}
</script>

<style scoped>
.audit-log-stats-page {
  background-color: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background-color: white;
  margin-bottom: 16px;
}

.page-content {
  padding: 0 24px 24px;
}

:deep(.ant-page-header-heading-title) {
  font-size: 20px;
  font-weight: 600;
}
</style>
