<template>
  <ErrorBoundary @retry="handleErrorRetry">
    <div class="enterprise-list">
    <!-- 页面标题和操作 -->
    <PageHeader
      title="企业档案管理"
      description="管理企业基本信息、资质文件和相关档案"
      :breadcrumbs="breadcrumbItems"
    >
      <template #actions>
        <a-space>
          <a-button type="primary" @click="goToCreate">
            <PlusOutlined />
            新增企业
          </a-button>
          <a-button @click="exportData">
            <ExportOutlined />
            导出数据
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <!-- 搜索和筛选 -->
    <EnterpriseSearchForm
      v-model="searchForm"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 统计信息 -->
    <EnterpriseStatsCard
      :stats="stats"
      :loading="statsLoading"
      @refresh="fetchStats"
    />

    <!-- 数据表格 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        :scroll="{ x: 1200 }"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'logo'">
            <a-avatar :size="40">
              {{ record.name.charAt(0) }}
            </a-avatar>
          </template>

          <template v-if="column.key === 'name'">
            <div class="enterprise-name">
              <div class="name">
                {{ record.name }}
              </div>
              <div class="credit-code">
                {{ record.unified_social_credit_code }}
              </div>
            </div>
          </template>

          <template v-if="column.key === 'industry'">
            <a-tag :color="getIndustryColor(record.industry_code)">
              {{ getIndustryName(record.industry_code) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'status'">
            <a-badge
              :status="getStatusBadge(record.status)"
              :text="getStatusText(record.status)"
            />
          </template>

          <template v-if="column.key === 'employees'">
            <span>{{ record.employees || 0 }}人</span>
          </template>

          <template v-if="column.key === 'taxAmount'">
            <span class="tax-amount">¥{{ formatNumber(record.tax_amount || 0) }}</span>
          </template>

          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="editEnterprise(record)">
                编辑
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="viewDocuments(record)">
                      <FileTextOutlined />
                      查看资质
                    </a-menu-item>
                    <a-menu-item @click="viewDeclarations(record)">
                      <AuditOutlined />
                      申报记录
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item
                      :class="{ danger: record.status === 'active' }"
                      @click="toggleStatus(record)"
                    >
                      <StopOutlined v-if="record.status === 'active'" />
                      <PlayCircleOutlined v-else />
                      {{ record.status === 'active' ? '停用' : '启用' }}
                    </a-menu-item>
                    <a-menu-item class="danger" @click="deleteEnterpriseRecord(record)">
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
    </div>

    <!-- 开发环境调试面板 -->
    <ApiDebugPanel v-if="isDevelopment" />
  </ErrorBoundary>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ExportOutlined,
  DownOutlined,
  FileTextOutlined,
  AuditOutlined,
  StopOutlined,
  PlayCircleOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

import PageHeader from '@/components/common/PageHeader.vue'
import ErrorBoundary from '@/components/common/ErrorBoundary.vue'
import EnterpriseSearchForm from '@/components/business/EnterpriseSearchForm.vue'
import EnterpriseStatsCard from '@/components/business/EnterpriseStatsCard.vue'
import ApiDebugPanel from '@/components/debug/ApiDebugPanel.vue'
import { getEnterprises, deleteEnterprise, getEnterpriseStats } from '@/api/enterprise'
import { useStandardPagination, useStandardApi, useStandardOperation } from '@/composables/useStandardApi'
import { useSearchForm } from '@/composables/useForm'
    const router = useRouter()

    // 开发环境标志
    const isDevelopment = process.env.NODE_ENV === 'development'

    // 面包屑导航
    const breadcrumbItems = [
      { title: '首页', path: '/' },
      { title: '企业管理', path: '/enterprise' },
      { title: '企业列表' }
    ]

    // 使用标准化的搜索表单
    const {
      searchForm,
      searching,
      handleSearch: executeSearch,
      handleReset: executeReset,
      setSearchParams
    } = useSearchForm({
      initialValues: {
        keyword: '',
        industry: '',
        status: '',
        dateRange: null
      },
      onSearch: (params) => {
        search({
          keyword: params.keyword,
          industry: params.industry,
          status: params.status
        })
      },
      onReset: () => {
        reset()
      }
    })

    // 表格列配置
    const columns = [
      {
        title: '企业信息',
        key: 'name',
        width: 280,
        fixed: 'left'
      },
      {
        title: '行业类型',
        key: 'industry',
        width: 120
      },
      {
        title: '状态',
        key: 'status',
        width: 100
      },
      {
        title: '员工数量',
        key: 'employees',
        width: 100,
        sorter: true
      },
      {
        title: '注册资本',
        dataIndex: 'registered_capital',
        width: 120,
        sorter: true,
        customRender: ({ text }) => {
          return text ? `¥${formatNumber(text)}` : '-'
        }
      },
      {
        title: '累计税额',
        key: 'taxAmount',
        width: 120,
        sorter: true
      },
      {
        title: '注册日期',
        dataIndex: 'registered_date',
        width: 120,
        sorter: true,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : '-'
        }
      },
      {
        title: '操作',
        key: 'actions',
        width: 180,
        fixed: 'right'
      }
    ]



    // 使用标准化的分页API调用
    const {
      loading,
      dataSource,
      pagination,
      params,
      hasData,
      isEmpty,
      fetchData,
      handleTableChange: handleStandardTableChange,
      search,
      reset,
      refresh
    } = useStandardPagination(getEnterprises, {
      defaultParams: {
        keyword: '',
        industry: '',
        status: ''
      },
      pageSize: 10
    })

    // 使用标准化的API调用获取统计信息
    const {
      loading: statsLoading,
      data: stats,
      execute: fetchStats
    } = useStandardApi(getEnterpriseStats, {
      immediate: true,
      showError: true
    })

    // 使用标准化的删除操作
    const {
      loading: deleteLoading,
      execute: executeDelete
    } = useStandardOperation(deleteEnterprise, {
      successMessage: '删除企业成功',
      onSuccess: () => {
        refresh() // 刷新列表
        fetchStats() // 刷新统计信息
      }
    })

    // 搜索和重置方法（使用标准化的搜索表单）
    const handleSearch = () => {
      executeSearch()
    }

    const handleReset = () => {
      executeReset()
    }

    // 表格变化处理
    const handleTableChange = (pag, filters, sorter) => {
      handleStandardTableChange(pag, filters, sorter)
    }

    const goToCreate = () => {
      router.push('/enterprise/create')
    }

    const viewDetail = (record) => {
      router.push(`/enterprise/detail/${record.id}`)
    }

    const editEnterprise = (record) => {
      router.push(`/enterprise/edit/${record.id}`)
    }

    const viewDocuments = (record) => {
      router.push(`/enterprise/documents/${record.id}`)
    }

    const viewDeclarations = (record) => {
      router.push(`/declaration/list?enterpriseId=${record.id}`)
    }

    const toggleStatus = (record) => {
      const action = record.status === 'active' ? '停用' : '启用'
      Modal.confirm({
        title: `确认${action}企业`,
        content: `确定要${action}企业"${record.name}"吗？`,
        onOk () {
          record.status = record.status === 'active' ? 'inactive' : 'active'
          message.success(`${action}成功`)
        }
      })
    }

    const deleteEnterpriseRecord = (record) => {
      Modal.confirm({
        title: '确认删除企业',
        content: `确定要删除企业"${record.name}"吗？此操作不可恢复。`,
        okType: 'danger',
        confirmLoading: deleteLoading.value,
        async onOk () {
          await executeDelete(record.id)
        }
      })
    }

    const exportData = () => {
      message.info('导出功能开发中...')
    }

    // 辅助方法
    const getIndustryColor = (industryCode) => {
      const colors = {
        6510: 'blue', // 软件和信息技术服务业
        3311: 'green', // 金属制品业
        5191: 'orange', // 批发业
        7299: 'purple', // 其他专业技术服务业
        6420: 'cyan', // 互联网和相关服务
        6210: 'magenta', // 餐饮业
        5311: 'lime', // 道路运输业
        4711: 'volcano', // 房屋建筑业
        technology: 'blue',
        manufacturing: 'green',
        trade: 'orange',
        finance: 'purple',
        'real-estate': 'red',
        other: 'default'
      }
      return colors[industryCode] || 'default'
    }

    const getIndustryName = (industryCode) => {
      const names = {
        6510: '软件和信息技术服务业',
        3311: '金属制品业',
        5191: '批发业',
        7299: '其他专业技术服务业',
        6420: '互联网和相关服务',
        6210: '餐饮业',
        5311: '道路运输业',
        4711: '房屋建筑业',
        technology: '科技服务业',
        manufacturing: '制造业',
        trade: '批发零售业',
        finance: '金融业',
        'real-estate': '房地产业',
        other: '其他'
      }
      return names[industryCode] || '其他行业'
    }

    const getStatusBadge = (status) => {
      const badges = {
        active: 'success',
        inactive: 'default',
        suspended: 'warning',
        cancelled: 'error',
        pending: 'processing'
      }
      return badges[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        active: '正常运营',
        inactive: '停用',
        suspended: '暂停',
        cancelled: '注销',
        pending: '待审核'
      }
      return texts[status] || '未知'
    }

    const formatNumber = (num) => {
      if (num === null || num === undefined || isNaN(num)) {
        return '0'
      }
      return Number(num).toLocaleString()
    }

    // 错误重试处理
    const handleErrorRetry = () => {
      // 重新获取数据
      fetchData()
      fetchStats()
    }

    // 生命周期
    onMounted(() => {
      fetchData()
    })

</script>

<style scoped>
.enterprise-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h2 {
  margin: 0;
  color: #262626;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-section {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}

.stats-section {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.enterprise-name .name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.enterprise-name .credit-code {
  font-size: 12px;
  color: #8c8c8c;
}

.tax-amount {
  font-weight: 500;
  color: #1890ff;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
}

:deep(.danger) {
  color: #ff4d4f;
}
</style>
