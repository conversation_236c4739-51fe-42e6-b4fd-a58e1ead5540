<template>
  <div class="enterprise-documents-container">
    <PageHeader
      title="企业资质文件"
      :description="`管理 ${enterpriseName} 的相关资质文件和证照`"
      :breadcrumbs="breadcrumbs"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
          <a-button type="primary" @click="showUploadModal = true">
            <UploadOutlined />
            上传文件
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <div class="documents-content">
      <a-row :gutter="24">
        <!-- 左侧文档分类 -->
        <a-col :span="6">
          <a-card title="文档分类" class="category-card">
            <a-menu
              v-model:selected-keys="selectedCategory"
              mode="inline"
              @click="onCategoryChange"
            >
              <a-menu-item key="all">
                <FileOutlined />
                全部文件 ({{ getTotalCount() }})
              </a-menu-item>
              <a-menu-item key="license">
                <SafetyCertificateOutlined />
                营业执照 ({{ getCategoryCount('license') }})
              </a-menu-item>
              <a-menu-item key="tax">
                <MoneyCollectOutlined />
                税务证明 ({{ getCategoryCount('tax') }})
              </a-menu-item>
              <a-menu-item key="organization">
                <BankOutlined />
                组织机构代码 ({{ getCategoryCount('organization') }})
              </a-menu-item>
              <a-menu-item key="qualification">
                <TrophyOutlined />
                资质证书 ({{ getCategoryCount('qualification') }})
              </a-menu-item>
              <a-menu-item key="contract">
                <FileTextOutlined />
                合同协议 ({{ getCategoryCount('contract') }})
              </a-menu-item>
              <a-menu-item key="other">
                <FolderOutlined />
                其他文件 ({{ getCategoryCount('other') }})
              </a-menu-item>
            </a-menu>
          </a-card>
        </a-col>

        <!-- 右侧文档列表 -->
        <a-col :span="18">
          <a-card>
            <!-- 工具栏 -->
            <div class="toolbar">
              <a-row justify="space-between" align="middle">
                <a-col>
                  <a-space>
                    <a-input
                      v-model:value="searchKeyword"
                      placeholder="搜索文件名..."
                      style="width: 200px"
                      @press-enter="searchDocuments"
                    >
                      <template #prefix>
                        <SearchOutlined />
                      </template>
                    </a-input>
                    <a-select
                      v-model:value="sortBy"
                      placeholder="排序方式"
                      style="width: 120px"
                      @change="sortDocuments"
                    >
                      <a-select-option value="name">
                        按名称
                      </a-select-option>
                      <a-select-option value="date">
                        按时间
                      </a-select-option>
                      <a-select-option value="size">
                        按大小
                      </a-select-option>
                    </a-select>
                  </a-space>
                </a-col>
                <a-col>
                  <a-space>
                    <a-tooltip title="列表视图">
                      <a-button
                        :type="viewMode === 'list' ? 'primary' : 'default'"
                        @click="viewMode = 'list'"
                      >
                        <UnorderedListOutlined />
                      </a-button>
                    </a-tooltip>
                    <a-tooltip title="网格视图">
                      <a-button
                        :type="viewMode === 'grid' ? 'primary' : 'default'"
                        @click="viewMode = 'grid'"
                      >
                        <AppstoreOutlined />
                      </a-button>
                    </a-tooltip>
                    <a-button :disabled="selectedFiles.length === 0" @click="batchDelete">
                      <DeleteOutlined />
                      批量删除
                    </a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>

            <!-- 文档列表 - 列表视图 -->
            <a-table
              v-if="viewMode === 'list'"
              :columns="columns"
              :data-source="filteredDocuments"
              :loading="loading"
              :pagination="pagination"
              :row-selection="rowSelection"
              row-key="id"
              @change="handleTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'name'">
                  <div class="file-info">
                    <FileIcon :file-type="getFileType(record.name)" />
                    <div class="file-details">
                      <div class="file-name" @click="handlePreviewFile(record)">
                        {{ record.name }}
                      </div>
                      <div class="file-meta">
                        {{ formatFileSize(record.size) }} • {{ record.uploadDate }}
                      </div>
                    </div>
                  </div>
                </template>

                <template v-if="column.key === 'category'">
                  <a-tag :color="getCategoryColor(record.category)">
                    {{ getCategoryName(record.category) }}
                  </a-tag>
                </template>

                <template v-if="column.key === 'status'">
                  <a-badge
                    :status="getStatusBadge(record.status)"
                    :text="getStatusText(record.status)"
                  />
                </template>

                <template v-if="column.key === 'actions'">
                  <a-space>
                    <a-tooltip title="预览">
                      <a-button type="link" size="small" @click="handlePreviewFile(record)">
                        <EyeOutlined />
                      </a-button>
                    </a-tooltip>
                    <a-tooltip title="下载">
                      <a-button type="link" size="small" @click="downloadFile(record)">
                        <DownloadOutlined />
                      </a-button>
                    </a-tooltip>
                    <a-tooltip title="编辑">
                      <a-button type="link" size="small" @click="editFile(record)">
                        <EditOutlined />
                      </a-button>
                    </a-tooltip>
                    <a-tooltip title="删除">
                      <a-button
                        type="link"
                        size="small"
                        danger
                        @click="deleteFile(record)"
                      >
                        <DeleteOutlined />
                      </a-button>
                    </a-tooltip>
                  </a-space>
                </template>
              </template>
            </a-table>

            <!-- 文档列表 - 网格视图 -->
            <div v-if="viewMode === 'grid'" class="grid-view">
              <a-row :gutter="[16, 16]">
                <a-col
                  v-for="doc in filteredDocuments"
                  :key="doc.id"
                  :span="6"
                >
                  <a-card
                    hoverable
                    class="file-card"
                    :class="{ selected: selectedFiles.includes(doc.id) }"
                    @click="selectFile(doc)"
                  >
                    <template #cover>
                      <div class="file-preview">
                        <FileIcon :file-type="getFileType(doc.name)" :size="48" />
                        <div class="file-actions">
                          <a-space>
                            <a-button size="small" @click.stop="handlePreviewFile(doc)">
                              <EyeOutlined />
                            </a-button>
                            <a-button size="small" @click.stop="downloadFile(doc)">
                              <DownloadOutlined />
                            </a-button>
                          </a-space>
                        </div>
                      </div>
                    </template>
                    <a-card-meta>
                      <template #title>
                        <div class="file-title" :title="doc.name">
                          {{ doc.name }}
                        </div>
                      </template>
                      <template #description>
                        <div class="file-info-grid">
                          <div>{{ formatFileSize(doc.size) }}</div>
                          <div>{{ doc.uploadDate }}</div>
                          <a-tag :color="getCategoryColor(doc.category)" size="small">
                            {{ getCategoryName(doc.category) }}
                          </a-tag>
                        </div>
                      </template>
                    </a-card-meta>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 文件上传模态框 -->
    <a-modal
      v-model:open="showUploadModal"
      title="上传文件"
      :footer="null"
      width="600px"
    >
      <a-form
        :model="uploadForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="文档分类" required>
          <a-select v-model:value="uploadForm.category" placeholder="请选择文档分类">
            <a-select-option value="license">
              营业执照
            </a-select-option>
            <a-select-option value="tax">
              税务证明
            </a-select-option>
            <a-select-option value="organization">
              组织机构代码
            </a-select-option>
            <a-select-option value="qualification">
              资质证书
            </a-select-option>
            <a-select-option value="contract">
              合同协议
            </a-select-option>
            <a-select-option value="other">
              其他文件
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="文件描述">
          <a-textarea
            v-model:value="uploadForm.description"
            placeholder="请输入文件描述（可选）"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="选择文件" required>
          <a-upload-dragger
            v-model:file-list="uploadForm.fileList"
            :before-upload="beforeUpload"
            :remove="handleRemove"
            multiple
          >
            <p class="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p class="ant-upload-text">
              点击或拖拽文件到此区域上传
            </p>
            <p class="ant-upload-hint">
              支持单个或批量上传。支持常见的文档格式（PDF、Word、Excel、图片等）
            </p>
          </a-upload-dragger>
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
          <a-space>
            <a-button type="primary" :loading="uploading" @click="handleUpload">
              上传文件
            </a-button>
            <a-button @click="cancelUpload">
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 文件预览模态框 -->
    <a-modal
      v-model:open="showPreviewModal"
      :title="previewFile?.name"
      width="80%"
      :footer="null"
    >
      <div class="file-preview-content">
        <!-- 这里可以集成文件预览组件 -->
        <div class="preview-placeholder">
          <FileIcon :file-type="getFileType(previewFile?.name)" :size="64" />
          <p>文件预览功能正在开发中</p>
          <a-button type="primary" @click="downloadFile(previewFile)">
            <DownloadOutlined />
            下载文件
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import PageHeader from '@/components/common/PageHeader.vue'
import FileIcon from '@/components/common/FileIcon.vue'
import { getDocumentList, uploadDocument } from '@/api/enterprise'
import {
  ArrowLeftOutlined,
  UploadOutlined,
  SearchOutlined,
  UnorderedListOutlined,
  AppstoreOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  EditOutlined,
  FileOutlined,
  SafetyCertificateOutlined,
  MoneyCollectOutlined,
  BankOutlined,
  TrophyOutlined,
  FileTextOutlined,
  FolderOutlined,
  InboxOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'EnterpriseDocuments',
  components: {
    PageHeader,
    FileIcon,
    ArrowLeftOutlined,
    UploadOutlined,
    SearchOutlined,
    UnorderedListOutlined,
    AppstoreOutlined,
    DeleteOutlined,
    EyeOutlined,
    DownloadOutlined,
    EditOutlined,
    FileOutlined,
    SafetyCertificateOutlined,
    MoneyCollectOutlined,
    BankOutlined,
    TrophyOutlined,
    FileTextOutlined,
    FolderOutlined,
    InboxOutlined
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const loading = ref(false)
    const uploading = ref(false)
    const showUploadModal = ref(false)
    const showPreviewModal = ref(false)
    const searchKeyword = ref('')
    const sortBy = ref('date')
    const viewMode = ref('list')
    const selectedCategory = ref(['all'])
    const selectedFiles = ref([])
    const previewFile = ref(null)
    const enterpriseName = ref('')

    // 文档列表数据
    const documents = ref([
      {
        id: '1',
        name: '营业执照.pdf',
        category: 'license',
        size: 2048576,
        uploadDate: '2024-01-15',
        uploader: '张三',
        status: 'active',
        description: '企业营业执照副本'
      },
      {
        id: '2',
        name: '税务登记证.jpg',
        category: 'tax',
        size: 1024768,
        uploadDate: '2024-01-20',
        uploader: '李四',
        status: 'active',
        description: '税务登记证照片'
      },
      {
        id: '3',
        name: '组织机构代码证.pdf',
        category: 'organization',
        size: 3072896,
        uploadDate: '2024-02-01',
        uploader: '王五',
        status: 'active',
        description: '组织机构代码证'
      }
    ])

    // 上传表单
    const uploadForm = reactive({
      category: '',
      description: '',
      fileList: []
    })

    // 表格列配置
    const columns = [
      {
        title: '文件名',
        key: 'name',
        width: 300
      },
      {
        title: '分类',
        key: 'category',
        width: 120
      },
      {
        title: '上传者',
        dataIndex: 'uploader',
        width: 100
      },
      {
        title: '上传时间',
        dataIndex: 'uploadDate',
        width: 120,
        sorter: true
      },
      {
        title: '状态',
        key: 'status',
        width: 100
      },
      {
        title: '操作',
        key: 'actions',
        width: 150,
        fixed: 'right'
      }
    ]

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
    })

    // 行选择配置
    const rowSelection = {
      selectedRowKeys: selectedFiles,
      onChange: (selectedRowKeys) => {
        selectedFiles.value = selectedRowKeys
      }
    }

    // 计算属性
    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '企业档案管理', path: '/enterprise' },
      { title: '企业列表', path: '/enterprise/list' },
      { title: '企业资质文件' }
    ])

    const filteredDocuments = computed(() => {
      let filtered = documents.value

      // 按分类过滤
      if (selectedCategory.value[0] !== 'all') {
        filtered = filtered.filter(doc => doc.category === selectedCategory.value[0])
      }

      // 按关键词搜索
      if (searchKeyword.value) {
        filtered = filtered.filter(doc =>
          doc.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
          doc.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
        )
      }

      return filtered
    })

    // 方法
    const fetchDocuments = async () => {
      loading.value = true
      try {
        const response = await getDocumentList(route.params.id)
        if (response.code === 200) {
          documents.value = response.data.documents || []
          enterpriseName.value = response.data.enterpriseName || ''
          pagination.total = documents.value.length
        }
      } catch (error) {
        message.error('获取文档列表失败')
      } finally {
        loading.value = false
      }
    }

    const onCategoryChange = ({ key }) => {
      selectedCategory.value = [key]
    }

    const searchDocuments = () => {
      // 搜索逻辑已在计算属性中实现
    }

    const sortDocuments = (value) => {
      // 排序逻辑
      documents.value.sort((a, b) => {
        switch (value) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'date':
          return new Date(b.uploadDate) - new Date(a.uploadDate)
        case 'size':
          return b.size - a.size
        default:
          return 0
        }
      })
    }

    const selectFile = (file) => {
      const index = selectedFiles.value.indexOf(file.id)
      if (index > -1) {
        selectedFiles.value.splice(index, 1)
      } else {
        selectedFiles.value.push(file.id)
      }
    }

    const handlePreviewFile = (file) => {
      previewFile.value = file
      showPreviewModal.value = true
    }

    const downloadFile = (file) => {
      message.info(`下载文件: ${file.name}`)
      // 实际下载逻辑
    }

    const editFile = (_file) => {
      message.info('编辑文件功能开发中...')
    }

    const deleteFile = (file) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除文件"${file.name}"吗？`,
        okType: 'danger',
        onOk () {
          const index = documents.value.findIndex(doc => doc.id === file.id)
          if (index > -1) {
            documents.value.splice(index, 1)
            message.success('删除成功')
          }
        }
      })
    }

    const batchDelete = () => {
      if (selectedFiles.value.length === 0) return

      Modal.confirm({
        title: '批量删除',
        content: `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？`,
        okType: 'danger',
        onOk () {
          documents.value = documents.value.filter(doc => !selectedFiles.value.includes(doc.id))
          selectedFiles.value = []
          message.success('批量删除成功')
        }
      })
    }

    const beforeUpload = (file) => {
      const isValidType = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png'].includes(
        file.name.split('.').pop().toLowerCase()
      )
      if (!isValidType) {
        message.error('只支持 PDF、Word、Excel、图片格式的文件')
        return false
      }

      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        message.error('文件大小不能超过 10MB')
        return false
      }

      return false // 阻止自动上传
    }

    const handleRemove = (file) => {
      const index = uploadForm.fileList.indexOf(file)
      const newFileList = uploadForm.fileList.slice()
      newFileList.splice(index, 1)
      uploadForm.fileList = newFileList
    }

    const handleUpload = async () => {
      if (!uploadForm.category) {
        message.error('请选择文档分类')
        return
      }

      if (uploadForm.fileList.length === 0) {
        message.error('请选择要上传的文件')
        return
      }

      uploading.value = true
      try {
        const formData = new FormData()
        uploadForm.fileList.forEach(file => {
          formData.append('files', file.originFileObj)
        })
        formData.append('category', uploadForm.category)
        formData.append('description', uploadForm.description)

        const response = await uploadDocument(route.params.id, formData)
        if (response.code === 200) {
          message.success('文件上传成功')
          showUploadModal.value = false
          cancelUpload()
          fetchDocuments()
        } else {
          message.error(response.message || '上传失败')
        }
      } catch (error) {
        message.error('上传失败')
      } finally {
        uploading.value = false
      }
    }

    const cancelUpload = () => {
      uploadForm.category = ''
      uploadForm.description = ''
      uploadForm.fileList = []
      showUploadModal.value = false
    }

    const handleTableChange = (pag, _filters, _sorter) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
    }

    const goBack = () => {
      router.go(-1)
    }

    // 辅助方法
    const getTotalCount = () => documents.value.length

    const getCategoryCount = (category) => {
      return documents.value.filter(doc => doc.category === category).length
    }

    const getFileType = (filename) => {
      const ext = filename.split('.').pop().toLowerCase()
      return ext
    }

    const getCategoryColor = (category) => {
      const colors = {
        license: 'blue',
        tax: 'green',
        organization: 'orange',
        qualification: 'purple',
        contract: 'cyan',
        other: 'default'
      }
      return colors[category] || 'default'
    }

    const getCategoryName = (category) => {
      const names = {
        license: '营业执照',
        tax: '税务证明',
        organization: '组织机构代码',
        qualification: '资质证书',
        contract: '合同协议',
        other: '其他文件'
      }
      return names[category] || '未知'
    }

    const getStatusBadge = (status) => {
      const badges = {
        active: 'success',
        expired: 'warning',
        invalid: 'error'
      }
      return badges[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        active: '有效',
        expired: '已过期',
        invalid: '无效'
      }
      return texts[status] || '未知'
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 生命周期
    onMounted(() => {
      fetchDocuments()
    })

    return {
      loading,
      uploading,
      showUploadModal,
      showPreviewModal,
      searchKeyword,
      sortBy,
      viewMode,
      selectedCategory,
      selectedFiles,
      previewFile,
      enterpriseName,
      documents,
      uploadForm,
      columns,
      pagination,
      rowSelection,
      breadcrumbs,
      filteredDocuments,
      fetchDocuments,
      onCategoryChange,
      searchDocuments,
      sortDocuments,
      selectFile,
      handlePreviewFile,
      downloadFile,
      editFile,
      deleteFile,
      batchDelete,
      beforeUpload,
      handleRemove,
      handleUpload,
      cancelUpload,
      handleTableChange,
      goBack,
      getTotalCount,
      getCategoryCount,
      getFileType,
      getCategoryColor,
      getCategoryName,
      getStatusBadge,
      getStatusText,
      formatFileSize
    }
  }
})
</script>

<style scoped>
.enterprise-documents-container {
  background: #f5f5f5;
  min-height: 100vh;
}

.documents-content {
  padding: 0 24px 24px;
}

.category-card {
  margin-bottom: 16px;
}

.toolbar {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.file-info {
  display: flex;
  align-items: center;
}

.file-details {
  margin-left: 8px;
  flex: 1;
}

.file-name {
  color: #1890ff;
  cursor: pointer;
  font-weight: 500;
}

.file-name:hover {
  text-decoration: underline;
}

.file-meta {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

.grid-view {
  min-height: 400px;
}

.file-card {
  cursor: pointer;
  transition: all 0.3s;
}

.file-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.file-card.selected {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.file-preview {
  position: relative;
  padding: 20px;
  text-align: center;
  background: #fafafa;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-actions {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  padding: 8px;
}

.file-card:hover .file-actions {
  opacity: 1;
}

.file-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-info-grid {
  font-size: 12px;
  color: #8c8c8c;
}

.file-preview-content {
  text-align: center;
  padding: 40px;
}

.preview-placeholder {
  color: #8c8c8c;
}

:deep(.ant-upload-drag) {
  border: 2px dashed #d9d9d9;
}

:deep(.ant-upload-drag:hover) {
  border-color: #1890ff;
}

:deep(.ant-menu-inline .ant-menu-item) {
  margin: 4px 0;
}
</style>
