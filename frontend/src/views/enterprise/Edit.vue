<template>
  <div class="enterprise-edit-container">
    <PageHeader
      :title="isCreate ? '新增企业' : '编辑企业'"
      :description="isCreate ? '请填写企业基本信息' : '修改企业信息'"
      :breadcrumbs="breadcrumbs"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
          <a-button @click="resetForm">
            <ReloadOutlined />
            重置
          </a-button>
          <a-button
            type="primary"
            :loading="loading"
            @click="submitForm"
          >
            <SaveOutlined />
            {{ isCreate ? '创建企业' : '保存修改' }}
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <div class="edit-content">
      <a-card>
        <a-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 20 }"
          layout="horizontal"
        >
          <!-- 基本信息 -->
          <a-divider orientation="left">
            基本信息
          </a-divider>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="企业名称" name="name">
                <a-input
                  v-model:value="formData.name"
                  placeholder="请输入企业全称"
                  :maxlength="100"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="统一社会信用代码" name="creditCode">
                <a-input
                  v-model:value="formData.creditCode"
                  placeholder="请输入18位统一社会信用代码"
                  :maxlength="18"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="法定代表人" name="legalRepresentative">
                <a-input
                  v-model:value="formData.legalRepresentative"
                  placeholder="请输入法定代表人姓名"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="注册资本" name="registeredCapital">
                <a-input
                  v-model:value="formData.registeredCapital"
                  placeholder="如：1000万元"
                  addon-after="万元"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="成立日期" name="establishmentDate">
                <a-date-picker
                  v-model:value="formData.establishmentDate"
                  placeholder="请选择成立日期"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="行业类别" name="industry">
                <a-select
                  v-model:value="formData.industry"
                  placeholder="请选择行业类别"
                >
                  <a-select-option value="manufacturing">
                    制造业
                  </a-select-option>
                  <a-select-option value="technology">
                    科技服务业
                  </a-select-option>
                  <a-select-option value="trade">
                    批发零售业
                  </a-select-option>
                  <a-select-option value="finance">
                    金融业
                  </a-select-option>
                  <a-select-option value="real-estate">
                    房地产业
                  </a-select-option>
                  <a-select-option value="construction">
                    建筑业
                  </a-select-option>
                  <a-select-option value="transportation">
                    交通运输业
                  </a-select-option>
                  <a-select-option value="hospitality">
                    住宿餐饮业
                  </a-select-option>
                  <a-select-option value="other">
                    其他
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="企业性质" name="enterpriseType">
                <a-select
                  v-model:value="formData.enterpriseType"
                  placeholder="请选择企业性质"
                >
                  <a-select-option value="private">
                    私营企业
                  </a-select-option>
                  <a-select-option value="state-owned">
                    国有企业
                  </a-select-option>
                  <a-select-option value="joint-venture">
                    合资企业
                  </a-select-option>
                  <a-select-option value="foreign">
                    外商独资
                  </a-select-option>
                  <a-select-option value="collective">
                    集体企业
                  </a-select-option>
                  <a-select-option value="individual">
                    个体工商户
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="企业规模" name="enterpriseScale">
                <a-select
                  v-model:value="formData.enterpriseScale"
                  placeholder="请选择企业规模"
                >
                  <a-select-option value="large">
                    大型企业
                  </a-select-option>
                  <a-select-option value="medium">
                    中型企业
                  </a-select-option>
                  <a-select-option value="small">
                    小型企业
                  </a-select-option>
                  <a-select-option value="micro">
                    微型企业
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="注册地址" name="registeredAddress">
            <a-textarea
              v-model:value="formData.registeredAddress"
              placeholder="请输入完整的注册地址"
              :rows="2"
              :maxlength="200"
              show-count
            />
          </a-form-item>

          <a-form-item label="经营范围" name="businessScope">
            <a-textarea
              v-model:value="formData.businessScope"
              placeholder="请输入企业经营范围"
              :rows="3"
              :maxlength="500"
              show-count
            />
          </a-form-item>

          <!-- 联系信息 -->
          <a-divider orientation="left">
            联系信息
          </a-divider>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="联系人" name="contactPerson">
                <a-input
                  v-model:value="formData.contactPerson"
                  placeholder="请输入联系人姓名"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="联系电话" name="contactPhone">
                <a-input
                  v-model:value="formData.contactPhone"
                  placeholder="请输入联系电话"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="联系邮箱" name="contactEmail">
                <a-input
                  v-model:value="formData.contactEmail"
                  placeholder="请输入联系邮箱"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="传真号码" name="faxNumber">
                <a-input
                  v-model:value="formData.faxNumber"
                  placeholder="请输入传真号码（可选）"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="办公地址" name="officeAddress">
            <a-textarea
              v-model:value="formData.officeAddress"
              placeholder="请输入办公地址（如与注册地址不同）"
              :rows="2"
              :maxlength="200"
              show-count
            />
          </a-form-item>

          <!-- 财务信息 -->
          <a-divider orientation="left">
            财务信息
          </a-divider>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="开户银行" name="bankName">
                <a-input
                  v-model:value="formData.bankName"
                  placeholder="请输入开户银行名称"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="银行账号" name="bankAccount">
                <a-input
                  v-model:value="formData.bankAccount"
                  placeholder="请输入银行账号"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="纳税人识别号" name="taxNumber">
                <a-input
                  v-model:value="formData.taxNumber"
                  placeholder="请输入纳税人识别号"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="纳税人类型" name="taxpayerType">
                <a-select
                  v-model:value="formData.taxpayerType"
                  placeholder="请选择纳税人类型"
                >
                  <a-select-option value="general">
                    一般纳税人
                  </a-select-option>
                  <a-select-option value="small">
                    小规模纳税人
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 其他信息 -->
          <a-divider orientation="left">
            其他信息
          </a-divider>



          <a-form-item v-if="!isCreate" label="企业状态" name="status">
            <a-radio-group v-model:value="formData.status">
              <a-radio value="active">
                正常
              </a-radio>
              <a-radio value="inactive">
                停用
              </a-radio>
              <a-radio value="pending">
                待审核
              </a-radio>
            </a-radio-group>
          </a-form-item>

          <!-- 表单操作按钮 -->
          <a-form-item :wrapper-col="{ offset: 4, span: 20 }">
            <a-space>
              <a-button
                type="primary"
                :loading="loading"
                @click="submitForm"
              >
                {{ isCreate ? '创建企业' : '保存修改' }}
              </a-button>
              <a-button @click="resetForm">
                重置
              </a-button>
              <a-button @click="goBack">
                返回
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import PageHeader from '@/components/common/PageHeader.vue'
import { getEnterpriseById, createEnterprise, updateEnterprise } from '@/api/enterprise'
import {
  ArrowLeftOutlined,
  ReloadOutlined,
  SaveOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'EnterpriseEdit',
  components: {
    PageHeader,
    ArrowLeftOutlined,
    ReloadOutlined,
    SaveOutlined
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref()
    const loading = ref(false)

    // 判断是否为创建模式
    const isCreate = computed(() => route.name === 'enterprise-create')

    // 面包屑导航
    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '企业档案管理', path: '/enterprise' },
      { title: '企业列表', path: '/enterprise/list' },
      { title: isCreate.value ? '新增企业' : '编辑企业' }
    ])

    // 表单数据
    const formData = reactive({
      name: '',
      creditCode: '',
      legalRepresentative: '',
      registeredCapital: '',
      establishmentDate: null,
      industry: '',
      enterpriseType: '',
      enterpriseScale: '',
      registeredAddress: '',
      businessScope: '',
      contactPerson: '',
      contactPhone: '',
      contactEmail: '',
      faxNumber: '',
      officeAddress: '',
      bankName: '',
      bankAccount: '',
      taxNumber: '',
      taxpayerType: '',

      status: 'active'
    })

    // 表单验证规则
    const formRules = {
      name: [
        { required: true, message: '请输入企业名称', trigger: 'blur' },
        { min: 2, max: 100, message: '企业名称长度在2-100个字符', trigger: 'blur' }
      ],
      creditCode: [
        { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
        { pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/, message: '请输入正确的统一社会信用代码', trigger: 'blur' }
      ],
      legalRepresentative: [
        { required: true, message: '请输入法定代表人', trigger: 'blur' }
      ],
      registeredCapital: [
        { required: true, message: '请输入注册资本', trigger: 'blur' }
      ],
      establishmentDate: [
        { required: true, message: '请选择成立日期', trigger: 'change' }
      ],
      industry: [
        { required: true, message: '请选择行业类别', trigger: 'change' }
      ],
      registeredAddress: [
        { required: true, message: '请输入注册地址', trigger: 'blur' }
      ],
      businessScope: [
        { required: true, message: '请输入经营范围', trigger: 'blur' }
      ],
      contactPerson: [
        { required: true, message: '请输入联系人', trigger: 'blur' }
      ],
      contactPhone: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ],
      contactEmail: [
        { required: true, message: '请输入联系邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      taxpayerType: [
        { required: true, message: '请选择纳税人类型', trigger: 'change' }
      ]
    }

    // 方法
    const fetchEnterpriseDetail = async () => {
      if (isCreate.value) return

      const enterpriseId = route.params.id
      if (!enterpriseId || enterpriseId === 'undefined' || enterpriseId === 'null') {
        message.error('企业ID无效，请重新选择企业')
        router.push('/enterprise/list')
        return
      }

      loading.value = true
      try {
        const response = await getEnterpriseById(enterpriseId)
        if (response.code === 200) {
          Object.assign(formData, response.data)
        } else {
          message.error(response.message || '获取企业信息失败')
        }
      } catch (error) {
        console.error('获取企业信息失败:', error)
        message.error('获取企业信息失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    const submitForm = async () => {
      try {
        await formRef.value.validate()

        loading.value = true

        // 准备提交数据，映射字段名到后端期望的格式
        const submitData = {
          name: formData.name,
          creditCode: formData.creditCode,
          taxpayerType: formData.taxpayerType,
          legalRepresentative: formData.legalRepresentative,
          industry: formData.industry,
          businessScope: formData.businessScope,
          registeredCapital: formData.registeredCapital,
          establishmentDate: formData.establishmentDate ? formData.establishmentDate.toISOString() : null,
          contactPerson: formData.contactPerson,
          phone: formData.contactPhone,
          email: formData.contactEmail,
          address: formData.registeredAddress
        }

        let response
        if (isCreate.value) {
          response = await createEnterprise(submitData)
        } else {
          const enterpriseId = route.params.id
          if (!enterpriseId || enterpriseId === 'undefined' || enterpriseId === 'null') {
            message.error('企业ID无效，无法更新')
            return
          }
          response = await updateEnterprise(enterpriseId, submitData)
        }

        if (response.code === 200 || response.code === 201) {
          message.success(isCreate.value ? '企业创建成功' : '企业信息更新成功')
          router.push('/enterprise/list')
        } else {
          message.error(response.message || '操作失败')
        }
      } catch (error) {
        if (error.errorFields) {
          message.error('请检查表单信息')
        } else {
          message.error('操作失败，请稍后重试')
        }
      } finally {
        loading.value = false
      }
    }

    const resetForm = () => {
      formRef.value.resetFields()
    }

    const goBack = () => {
      router.go(-1)
    }

    // 生命周期
    onMounted(() => {
      fetchEnterpriseDetail()
    })

    return {
      formRef,
      loading,
      isCreate,
      breadcrumbs,
      formData,
      formRules,
      submitForm,
      resetForm,
      goBack
    }
  }
})
</script>

<style scoped>
.enterprise-edit-container {
  background: #f5f5f5;
  min-height: 100vh;
}

.edit-content {
  padding: 0 24px 24px;
}

:deep(.ant-form-item-label > label) {
  font-weight: 500;
}

:deep(.ant-divider-inner-text) {
  font-weight: 500;
  color: #262626;
}

:deep(.ant-card) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}
</style>
