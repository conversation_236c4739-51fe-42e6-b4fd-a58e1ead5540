<template>
  <div class="enterprise-detail-container">
    <PageHeader
      :title="pageTitle"
      :description="enterprise.description"
      :breadcrumbs="breadcrumbs"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
          <a-button type="primary" @click="editEnterprise">
            <EditOutlined />
            编辑企业
          </a-button>
          <a-dropdown>
            <a-button>
              更多操作
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="exportEnterprise">
                  <ExportOutlined />
                  导出企业信息
                </a-menu-item>
                <a-menu-item @click="refreshData">
                  <ReloadOutlined />
                  刷新数据
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item class="danger" @click="archiveEnterprise">
                  <FolderOutlined />
                  归档企业
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </template>
    </PageHeader>

    <div class="detail-content">
      <a-row :gutter="24">
        <!-- 左侧主要信息 -->
        <a-col :span="16">
          <!-- 基本信息卡片 -->
          <a-card title="企业基本信息" class="info-card">
            <a-row :gutter="16">
              <a-col :span="3">
                <a-avatar :size="80" class="enterprise-logo">
                  {{ enterprise.name?.charAt(0) }}
                </a-avatar>
              </a-col>
              <a-col :span="21">
                <a-descriptions :column="2" size="middle">
                  <a-descriptions-item label="企业名称">
                    {{ enterprise.name }}
                  </a-descriptions-item>
                  <a-descriptions-item label="统一社会信用代码">
                    {{ enterprise.unified_social_credit_code }}
                  </a-descriptions-item>
                  <a-descriptions-item label="法定代表人">
                    {{ enterprise.legal_representative }}
                  </a-descriptions-item>
                  <a-descriptions-item label="注册资本">
                    {{ enterprise.registered_capital }}
                  </a-descriptions-item>
                  <a-descriptions-item label="成立日期">
                    {{ formatDate(enterprise.establishment_date) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="企业状态">
                    <a-badge
                      :status="getStatusBadge(enterprise.status)"
                      :text="getStatusText(enterprise.status)"
                    />
                  </a-descriptions-item>
                  <a-descriptions-item label="行业类别" :span="2">
                    <a-tag :color="getIndustryColor(enterprise.industry)">
                      {{ getIndustryName(enterprise.industry) }}
                    </a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="注册地址" :span="2">
                    {{ enterprise.registeredAddress }}
                  </a-descriptions-item>
                  <a-descriptions-item label="经营范围" :span="2">
                    {{ enterprise.businessScope }}
                  </a-descriptions-item>
                </a-descriptions>
              </a-col>
            </a-row>
          </a-card>

          <!-- 联系信息 -->
          <a-card title="联系信息" class="info-card">
            <a-descriptions :column="2" size="middle">
              <a-descriptions-item label="联系人">
                {{ enterprise.contactPerson }}
              </a-descriptions-item>
              <a-descriptions-item label="联系电话">
                {{ enterprise.contactPhone }}
              </a-descriptions-item>
              <a-descriptions-item label="联系邮箱">
                {{ enterprise.contactEmail }}
              </a-descriptions-item>
              <a-descriptions-item label="办公地址" :span="2">
                {{ enterprise.officeAddress }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>

          <!-- 税种配置 -->
          <a-card title="税种配置" class="info-card">
            <template #extra>
              <a-button type="link" @click="manageTaxTypes">
                配置税种
              </a-button>
            </template>
            <div class="tax-types-list">
              <a-tag
                v-for="taxType in enterprise.taxTypes"
                :key="taxType.id"
                :color="taxType.color"
                class="tax-type-tag"
              >
                {{ taxType.name }} - {{ taxType.rate }}%
              </a-tag>
              <a-empty v-if="!enterprise.taxTypes?.length" description="暂未配置税种" />
            </div>
          </a-card>

          <!-- 申报历史 -->
          <a-card title="最近申报记录" class="info-card">
            <template #extra>
              <a-button type="link" @click="viewAllDeclarations">
                查看全部
              </a-button>
            </template>
            <a-table
              :columns="declarationColumns"
              :data-source="recentDeclarations"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-badge
                    :status="getDeclarationStatusBadge(record.status)"
                    :text="getDeclarationStatusText(record.status)"
                  />
                </template>
                <template v-if="column.key === 'actions'">
                  <a-button type="link" size="small" @click="viewDeclaration(record)">
                    查看
                  </a-button>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>

        <!-- 右侧统计和快捷操作 -->
        <a-col :span="8">
          <!-- 企业统计 -->
          <a-card title="企业统计" class="stats-card">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-statistic
                  title="累计申报"
                  :value="stats.totalDeclarations"
                  suffix="次"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :span="12">
                <a-statistic
                  title="累计税额"
                  :value="stats.totalTaxAmount"
                  prefix="¥"
                  :precision="2"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-col>
              <a-col :span="12">
                <a-statistic
                  title="本月申报"
                  :value="stats.monthlyDeclarations"
                  suffix="次"
                />
              </a-col>
              <a-col :span="12">
                <a-statistic
                  title="本月税额"
                  :value="stats.monthlyTaxAmount"
                  prefix="¥"
                  :precision="2"
                />
              </a-col>
            </a-row>
          </a-card>

          <!-- 快捷操作 -->
          <a-card title="快捷操作" class="action-card">
            <a-space direction="vertical" size="middle" style="width: 100%">
              <a-button type="primary" block @click="createDeclaration">
                <PlusOutlined />
                新建申报
              </a-button>
              <a-button block @click="uploadDocuments">
                <UploadOutlined />
                上传资质文件
              </a-button>
              <a-button block @click="viewIntegrations">
                <ApiOutlined />
                系统集成设置
              </a-button>
              <a-button block @click="generateReport">
                <FileTextOutlined />
                生成税务报告
              </a-button>
            </a-space>
          </a-card>

          <!-- 企业资质文件 -->
          <a-card title="企业资质" class="documents-card">
            <template #extra>
              <a-button type="link" @click="manageDocuments">
                管理文件
              </a-button>
            </template>
            <div class="document-list">
              <div
                v-for="doc in enterprise.documents"
                :key="doc.id"
                class="document-item"
              >
                <FileTextOutlined class="doc-icon" />
                <div class="doc-info">
                  <div class="doc-name">
                    {{ doc.name }}
                  </div>
                  <div class="doc-date">
                    {{ doc.uploadDate }}
                  </div>
                </div>
                <a-button type="link" size="small" @click="downloadDocument(doc)">
                  下载
                </a-button>
              </div>
              <a-empty v-if="!enterprise.documents?.length" description="暂无资质文件" />
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import PageHeader from '@/components/common/PageHeader.vue'
import { getEnterpriseById } from '@/api/enterprise'
import { useEnterpriseId } from '@/utils/enterprise-id-validator'
import { formatDate } from '@/utils/date'
import {
  ArrowLeftOutlined,
  EditOutlined,
  DownOutlined,
  ExportOutlined,
  ReloadOutlined,
  FolderOutlined,
  PlusOutlined,
  UploadOutlined,
  ApiOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'EnterpriseDetail',
  components: {
    PageHeader,
    ArrowLeftOutlined,
    EditOutlined,
    DownOutlined,
    ExportOutlined,
    ReloadOutlined,
    FolderOutlined,
    PlusOutlined,
    UploadOutlined,
    ApiOutlined,
    FileTextOutlined
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const loading = ref(false)

    // 企业ID验证
    const { enterpriseId, validateEnterpriseId, validateAndExecute } = useEnterpriseId(route, router, message)

    // 企业信息
    const enterprise = ref({
      id: '',
      name: '',
      unified_social_credit_code: '',

      legal_representative: '',
      registered_capital: '',
      establishment_date: '',
      status: 'active',
      industry_code: '',
      registered_address: '',
      business_scope: '',
      contact_person: '',
      contact_phone: '',
      contact_email: '',
      business_address: '',
      taxTypes: [],
      documents: []
    })

    // 统计数据
    const stats = ref({
      totalDeclarations: 0,
      totalTaxAmount: 0,
      monthlyDeclarations: 0,
      monthlyTaxAmount: 0
    })

    // 最近申报记录
    const recentDeclarations = ref([])

    // 申报记录表格列
    const declarationColumns = [
      {
        title: '申报期间',
        dataIndex: 'period',
        width: 100
      },
      {
        title: '税种',
        dataIndex: 'taxType',
        width: 80
      },
      {
        title: '状态',
        key: 'status',
        width: 80
      },
      {
        title: '申报日期',
        dataIndex: 'submitDate',
        width: 100
      },
      {
        title: '操作',
        key: 'actions',
        width: 60
      }
    ]

    // 计算属性
    const pageTitle = computed(() => {
      return enterprise.value.name || '企业详情'
    })

    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '企业档案管理', path: '/enterprise' },
      { title: '企业列表', path: '/enterprise/list' },
      { title: pageTitle.value }
    ])

    // 方法
    const fetchEnterpriseDetail = async () => {
      // 验证企业ID
      if (!validateEnterpriseId()) {
        return
      }

      loading.value = true
      try {
        const response = await getEnterpriseById(enterpriseId.value)
        if (response.code === 200) {
          enterprise.value = response.data

          // 模拟统计数据
          stats.value = {
            totalDeclarations: 24,
            totalTaxAmount: 2580000,
            monthlyDeclarations: 3,
            monthlyTaxAmount: 285000
          }

          // 模拟最近申报记录
          recentDeclarations.value = [
            {
              id: '1',
              period: '2024-11',
              taxType: '增值税',
              status: 'submitted',
              submitDate: '2024-11-15'
            },
            {
              id: '2',
              period: '2024-10',
              taxType: '企业所得税',
              status: 'approved',
              submitDate: '2024-10-25'
            }
          ]
        }
      } catch (error) {
        message.error('获取企业详情失败')
      } finally {
        loading.value = false
      }
    }

    const goBack = () => {
      router.go(-1)
    }

    const editEnterprise = () => {
      validateAndExecute((id) => {
        router.push(`/enterprise/edit/${id}`)
      })
    }

    const manageTaxTypes = () => {
      message.info('税种配置功能开发中...')
    }

    const viewAllDeclarations = () => {
      validateAndExecute((id) => {
        router.push(`/declaration/list?enterpriseId=${id}`)
      })
    }

    const viewDeclaration = (record) => {
      router.push(`/declaration/detail/${record.id}`)
    }

    const createDeclaration = () => {
      router.push(`/declaration/create?enterpriseId=${enterprise.value.id}`)
    }

    const uploadDocuments = () => {
      router.push(`/enterprise/documents/${enterprise.value.id}`)
    }

    const viewIntegrations = () => {
      router.push(`/integration/list?enterpriseId=${enterprise.value.id}`)
    }

    const generateReport = () => {
      message.info('报告生成功能开发中...')
    }

    const manageDocuments = () => {
      router.push(`/enterprise/documents/${enterprise.value.id}`)
    }

    const downloadDocument = (doc) => {
      message.info(`下载文件: ${doc.name}`)
    }

    const exportEnterprise = () => {
      message.info('导出功能开发中...')
    }

    const refreshData = () => {
      fetchEnterpriseDetail()
    }

    const archiveEnterprise = () => {
      message.info('归档功能开发中...')
    }

    // 辅助方法
    const getStatusBadge = (status) => {
      const badges = {
        active: 'success',
        inactive: 'default',
        pending: 'processing'
      }
      return badges[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        active: '正常',
        inactive: '停用',
        pending: '待审核'
      }
      return texts[status] || '未知'
    }

    const getIndustryColor = (industry) => {
      const colors = {
        technology: 'blue',
        manufacturing: 'green',
        trade: 'orange',
        finance: 'purple',
        'real-estate': 'red',
        other: 'default'
      }
      return colors[industry] || 'default'
    }

    const getIndustryName = (industry) => {
      const names = {
        technology: '科技服务业',
        manufacturing: '制造业',
        trade: '批发零售业',
        finance: '金融业',
        'real-estate': '房地产业',
        other: '其他'
      }
      return names[industry] || '未知'
    }

    const getDeclarationStatusBadge = (status) => {
      const badges = {
        draft: 'default',
        submitted: 'processing',
        approved: 'success',
        rejected: 'error'
      }
      return badges[status] || 'default'
    }

    const getDeclarationStatusText = (status) => {
      const texts = {
        draft: '草稿',
        submitted: '已提交',
        approved: '已通过',
        rejected: '已驳回'
      }
      return texts[status] || '未知'
    }

    // 生命周期
    onMounted(() => {
      fetchEnterpriseDetail()
    })

    return {
      loading,
      enterprise,
      stats,
      recentDeclarations,
      declarationColumns,
      pageTitle,
      breadcrumbs,
      goBack,
      editEnterprise,
      manageTaxTypes,
      viewAllDeclarations,
      viewDeclaration,
      createDeclaration,
      uploadDocuments,
      viewIntegrations,
      generateReport,
      manageDocuments,
      downloadDocument,
      exportEnterprise,
      refreshData,
      archiveEnterprise,
      getStatusBadge,
      getStatusText,
      getIndustryColor,
      getIndustryName,
      getDeclarationStatusBadge,
      getDeclarationStatusText,
      formatDate
    }
  }
})
</script>

<style scoped>
.enterprise-detail-container {
  background: #f5f5f5;
  min-height: 100vh;
}

.detail-content {
  padding: 0 24px 24px;
}

.info-card,
.stats-card,
.action-card,
.documents-card {
  margin-bottom: 16px;
}

.enterprise-logo {
  background: #1890ff;
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.tax-types-list {
  min-height: 60px;
}

.tax-type-tag {
  margin-bottom: 8px;
}

.document-list {
  max-height: 200px;
  overflow-y: auto;
}

.document-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.document-item:last-child {
  border-bottom: none;
}

.doc-icon {
  font-size: 16px;
  color: #1890ff;
  margin-right: 8px;
}

.doc-info {
  flex: 1;
}

.doc-name {
  font-size: 14px;
  color: #262626;
}

.doc-date {
  font-size: 12px;
  color: #8c8c8c;
}

.danger {
  color: #ff4d4f !important;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}

:deep(.ant-card-head-title) {
  font-weight: 500;
}
</style>
