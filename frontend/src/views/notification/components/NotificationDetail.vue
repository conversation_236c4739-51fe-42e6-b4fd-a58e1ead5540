<template>
  <div class="notification-detail">
    <div class="detail-header">
      <div class="header-left">
        <a-tag :color="getTypeColor(notification.type)">
          {{ getTypeLabel(notification.type) }}
        </a-tag>
        <a-badge
          :status="notification.isRead ? 'default' : 'processing'"
          :text="notification.isRead ? '已读' : '未读'"
        />
      </div>
      <div class="header-right">
        <a-button
          v-if="!notification.isRead"
          type="primary"
          size="small"
          @click="handleMarkRead"
        >
          标记已读
        </a-button>
        <a-button
          type="danger"
          size="small"
          @click="handleDelete"
        >
          删除
        </a-button>
      </div>
    </div>

    <div class="detail-content">
      <h3 class="notification-title">{{ notification.title }}</h3>
      
      <div class="notification-meta">
        <div class="meta-item">
          <span class="meta-label">创建时间：</span>
          <span class="meta-value">{{ formatTime(notification.createdAt) }}</span>
        </div>
        <div v-if="notification.readAt" class="meta-item">
          <span class="meta-label">阅读时间：</span>
          <span class="meta-value">{{ formatTime(notification.readAt) }}</span>
        </div>
        <div v-if="notification.relatedEntityType" class="meta-item">
          <span class="meta-label">关联实体：</span>
          <span class="meta-value">
            {{ getEntityTypeLabel(notification.relatedEntityType) }}
            <span v-if="notification.relatedEntityId">
              (ID: {{ notification.relatedEntityId }})
            </span>
          </span>
        </div>
      </div>

      <div class="notification-content">
        <h4>通知内容</h4>
        <div class="content-text">{{ notification.content }}</div>
      </div>

      <div v-if="notification.relatedEntityType && notification.relatedEntityId" class="related-actions">
        <h4>相关操作</h4>
        <div class="action-buttons">
          <a-button
            v-if="notification.relatedEntityType === 'invoice'"
            type="link"
            @click="goToInvoice"
          >
            查看发票详情
          </a-button>
          <a-button
            v-if="notification.relatedEntityType === 'declaration'"
            type="link"
            @click="goToDeclaration"
          >
            查看申报详情
          </a-button>
          <a-button
            v-if="notification.relatedEntityType === 'enterprise'"
            type="link"
            @click="goToEnterprise"
          >
            查看企业详情
          </a-button>
          <a-button
            v-if="notification.relatedEntityType === 'user'"
            type="link"
            @click="goToUser"
          >
            查看用户详情
          </a-button>
          <a-button
            v-if="notification.relatedEntityType === 'tax_type'"
            type="link"
            @click="goToTaxType"
          >
            查看税种详情
          </a-button>
          <a-button
            v-if="notification.relatedEntityType === 'tax_rule'"
            type="link"
            @click="goToTaxRule"
          >
            查看税则详情
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getNotificationTypes } from '@/api/notification'

export default defineComponent({
  name: 'NotificationDetail',
  props: {
    notification: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  emits: ['mark-read', 'delete'],
  setup(props, { emit }) {
    const router = useRouter()

    // 计算属性
    const notificationTypes = computed(() => getNotificationTypes())

    // 方法
    const getTypeColor = (type) => {
      const colorMap = {
        system: 'blue',
        tax_reminder: 'orange',
        declaration_deadline: 'red',
        invoice_verification: 'green',
        enterprise_update: 'purple',
        user_action: 'cyan',
        audit_alert: 'red',
        policy_update: 'gold',
        maintenance: 'gray',
        security: 'red'
      }
      return colorMap[type] || 'default'
    }

    const getTypeLabel = (type) => {
      const typeOption = notificationTypes.value.find(item => item.value === type)
      return typeOption?.label || type
    }

    const getEntityTypeLabel = (entityType) => {
      const entityTypeMap = {
        user: '用户',
        enterprise: '企业',
        invoice: '发票',
        declaration: '申报',
        tax_type: '税种',
        tax_rule: '税则',
        system: '系统'
      }
      return entityTypeMap[entityType] || entityType
    }

    const formatTime = (time) => {
      if (!time) return '-'
      return new Date(time).toLocaleString()
    }

    const handleMarkRead = () => {
      emit('mark-read', props.notification)
    }

    const handleDelete = () => {
      emit('delete', props.notification)
    }

    // 导航方法
    const goToInvoice = () => {
      router.push(`/invoice/detail/${props.notification.relatedEntityId}`)
    }

    const goToDeclaration = () => {
      router.push(`/declaration/detail/${props.notification.relatedEntityId}`)
    }

    const goToEnterprise = () => {
      router.push(`/enterprise/detail/${props.notification.relatedEntityId}`)
    }

    const goToUser = () => {
      router.push(`/user/detail/${props.notification.relatedEntityId}`)
    }

    const goToTaxType = () => {
      router.push(`/taxtype/detail/${props.notification.relatedEntityId}`)
    }

    const goToTaxRule = () => {
      router.push(`/taxrule/detail/${props.notification.relatedEntityId}`)
    }

    return {
      getTypeColor,
      getTypeLabel,
      getEntityTypeLabel,
      formatTime,
      handleMarkRead,
      handleDelete,
      goToInvoice,
      goToDeclaration,
      goToEnterprise,
      goToUser,
      goToTaxType,
      goToTaxRule
    }
  }
})
</script>

<style scoped>
.notification-detail {
  padding: 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right {
  display: flex;
  gap: 8px;
}

.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

.notification-title {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
}

.notification-meta {
  margin-bottom: 20px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 6px;
}

.meta-item {
  display: flex;
  margin-bottom: 8px;
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  min-width: 80px;
  color: #666;
  font-weight: 500;
}

.meta-value {
  color: #333;
  flex: 1;
}

.notification-content {
  margin-bottom: 20px;
}

.notification-content h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.content-text {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-break: break-word;
}

.related-actions {
  border-top: 1px solid #e8e8e8;
  padding-top: 16px;
}

.related-actions h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.action-buttons .ant-btn-link {
  padding: 4px 8px;
  height: auto;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
}

.action-buttons .ant-btn-link:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}
</style>
