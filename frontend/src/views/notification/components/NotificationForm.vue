<template>
  <div class="notification-form">
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="发送方式" name="sendType">
        <a-radio-group v-model:value="form.sendType">
          <a-radio value="single">单个用户</a-radio>
          <a-radio value="batch">批量用户</a-radio>
          <a-radio value="all">全部用户</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item
        v-if="form.sendType === 'single'"
        label="接收用户"
        name="recipientId"
      >
        <a-select
          v-model:value="form.recipientId"
          placeholder="请选择接收用户"
          show-search
          :filter-option="filterOption"
          :loading="usersLoading"
        >
          <a-select-option
            v-for="user in users"
            :key="user.id"
            :value="user.id"
          >
            {{ user.userName }} ({{ user.name }})
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item
        v-if="form.sendType === 'batch'"
        label="接收用户"
        name="recipientIds"
      >
        <a-select
          v-model:value="form.recipientIds"
          mode="multiple"
          placeholder="请选择接收用户"
          show-search
          :filter-option="filterOption"
          :loading="usersLoading"
        >
          <a-select-option
            v-for="user in users"
            :key="user.id"
            :value="user.id"
          >
            {{ user.userName }} ({{ user.name }})
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="通知类型" name="type">
        <a-select
          v-model:value="form.type"
          placeholder="请选择通知类型"
        >
          <a-select-option
            v-for="type in notificationTypes"
            :key="type.value"
            :value="type.value"
          >
            {{ type.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="通知标题" name="title">
        <a-input
          v-model:value="form.title"
          placeholder="请输入通知标题"
          :maxlength="100"
          show-count
        />
      </a-form-item>

      <a-form-item label="通知内容" name="content">
        <a-textarea
          v-model:value="form.content"
          placeholder="请输入通知内容"
          :rows="6"
          :maxlength="1000"
          show-count
        />
      </a-form-item>

      <a-form-item label="关联实体">
        <a-row :gutter="8">
          <a-col :span="12">
            <a-select
              v-model:value="form.relatedEntityType"
              placeholder="请选择实体类型"
              allow-clear
            >
              <a-select-option
                v-for="entityType in entityTypes"
                :key="entityType.value"
                :value="entityType.value"
              >
                {{ entityType.label }}
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="12">
            <a-input
              v-model:value="form.relatedEntityId"
              placeholder="请输入实体ID"
              :disabled="!form.relatedEntityType"
            />
          </a-col>
        </a-row>
      </a-form-item>

      <a-form-item label="发送时间" name="sendTime">
        <a-radio-group v-model:value="form.sendTimeType">
          <a-radio value="now">立即发送</a-radio>
          <a-radio value="scheduled">定时发送</a-radio>
        </a-radio-group>
        <a-date-picker
          v-if="form.sendTimeType === 'scheduled'"
          v-model:value="form.scheduledTime"
          show-time
          placeholder="请选择发送时间"
          style="width: 100%; margin-top: 8px"
          :disabled-date="disabledDate"
        />
      </a-form-item>

      <a-form-item label="预览">
        <div class="notification-preview">
          <div class="preview-header">
            <a-tag :color="getTypeColor(form.type)">
              {{ getTypeLabel(form.type) }}
            </a-tag>
            <span class="preview-time">
              {{ form.sendTimeType === 'now' ? '立即发送' : '定时发送' }}
            </span>
          </div>
          <div class="preview-title">
            {{ form.title || '通知标题' }}
          </div>
          <div class="preview-content">
            {{ form.content || '通知内容' }}
          </div>
          <div class="preview-recipients">
            <strong>接收者：</strong>
            <span v-if="form.sendType === 'single'">
              {{ getSelectedUserName(form.recipientId) || '未选择' }}
            </span>
            <span v-else-if="form.sendType === 'batch'">
              {{ form.recipientIds?.length || 0 }} 个用户
            </span>
            <span v-else>全部用户</span>
          </div>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { getUsers } from '@/api/user'
import {
  createNotification,
  batchCreateNotifications,
  getNotificationTypes
} from '@/api/notification'

export default defineComponent({
  name: 'NotificationForm',
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  emits: ['submit'],
  setup(props, { emit, expose }) {
    const formRef = ref(null)
    const usersLoading = ref(false)
    const users = ref([])
    const notificationTypes = ref([])

    // 表单数据
    const form = reactive({
      sendType: 'single',
      recipientId: '',
      recipientIds: [],
      type: '',
      title: '',
      content: '',
      relatedEntityType: '',
      relatedEntityId: '',
      sendTimeType: 'now',
      scheduledTime: null
    })

    // 表单验证规则
    const rules = computed(() => ({
      sendType: [
        { required: true, message: '请选择发送方式', trigger: 'change' }
      ],
      recipientId: form.sendType === 'single' ? [
        { required: true, message: '请选择接收用户', trigger: 'change' }
      ] : [],
      recipientIds: form.sendType === 'batch' ? [
        { required: true, message: '请选择接收用户', trigger: 'change' },
        { type: 'array', min: 1, message: '至少选择一个用户', trigger: 'change' }
      ] : [],
      type: [
        { required: true, message: '请选择通知类型', trigger: 'change' }
      ],
      title: [
        { required: true, message: '请输入通知标题', trigger: 'blur' },
        { min: 2, max: 100, message: '标题长度为2-100个字符', trigger: 'blur' }
      ],
      content: [
        { required: true, message: '请输入通知内容', trigger: 'blur' },
        { min: 5, max: 1000, message: '内容长度为5-1000个字符', trigger: 'blur' }
      ],
      scheduledTime: form.sendTimeType === 'scheduled' ? [
        { required: true, message: '请选择发送时间', trigger: 'change' }
      ] : []
    }))

    // 实体类型选项
    const entityTypes = [
      { value: 'user', label: '用户' },
      { value: 'enterprise', label: '企业' },
      { value: 'invoice', label: '发票' },
      { value: 'declaration', label: '申报' },
      { value: 'tax_type', label: '税种' },
      { value: 'tax_rule', label: '税则' },
      { value: 'system', label: '系统' }
    ]

    // 方法
    const fetchUsers = async () => {
      usersLoading.value = true
      try {
        const response = await getUsers({ pageSize: 1000 })
        if (response.code === 200) {
          users.value = response.data.data || []
        }
      } catch (error) {
        message.error('获取用户列表失败')
      } finally {
        usersLoading.value = false
      }
    }

    const fetchNotificationTypes = () => {
      notificationTypes.value = getNotificationTypes()
    }

    const filterOption = (input, option) => {
      return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }

    const disabledDate = (current) => {
      // 禁用过去的时间
      return current && current < dayjs().startOf('minute')
    }

    const getTypeColor = (type) => {
      const colorMap = {
        system: 'blue',
        tax_reminder: 'orange',
        declaration_deadline: 'red',
        invoice_verification: 'green',
        enterprise_update: 'purple',
        user_action: 'cyan',
        audit_alert: 'red',
        policy_update: 'gold',
        maintenance: 'gray',
        security: 'red'
      }
      return colorMap[type] || 'default'
    }

    const getTypeLabel = (type) => {
      const typeOption = notificationTypes.value.find(item => item.value === type)
      return typeOption?.label || type
    }

    const getSelectedUserName = (userId) => {
      const user = users.value.find(item => item.id === userId)
      return user ? `${user.userName} (${user.name})` : ''
    }

    const initForm = () => {
      if (props.formData && Object.keys(props.formData).length > 0) {
        Object.assign(form, {
          sendType: 'single',
          recipientId: props.formData.recipientId || '',
          recipientIds: [],
          type: props.formData.type || '',
          title: props.formData.title || '',
          content: props.formData.content || '',
          relatedEntityType: props.formData.relatedEntityType || '',
          relatedEntityId: props.formData.relatedEntityId || '',
          sendTimeType: 'now',
          scheduledTime: null
        })
      } else {
        // 重置表单
        Object.assign(form, {
          sendType: 'single',
          recipientId: '',
          recipientIds: [],
          type: '',
          title: '',
          content: '',
          relatedEntityType: '',
          relatedEntityId: '',
          sendTimeType: 'now',
          scheduledTime: null
        })
      }
    }

    const validateForm = async () => {
      try {
        await formRef.value.validate()
        return true
      } catch (error) {
        return false
      }
    }

    const submit = async () => {
      if (!(await validateForm())) {
        return
      }

      try {
        const baseData = {
          type: form.type,
          title: form.title,
          content: form.content,
          relatedEntityType: form.relatedEntityType || null,
          relatedEntityId: form.relatedEntityId || null
        }

        if (form.sendType === 'single') {
          // 单个用户
          await createNotification({
            ...baseData,
            recipientId: form.recipientId
          })
          message.success('发送通知成功')
        } else if (form.sendType === 'batch') {
          // 批量用户
          await batchCreateNotifications({
            ...baseData,
            recipientIds: form.recipientIds
          })
          message.success(`批量发送通知成功，共 ${form.recipientIds.length} 个用户`)
        } else {
          // 全部用户
          const allUserIds = users.value.map(user => user.id)
          await batchCreateNotifications({
            ...baseData,
            recipientIds: allUserIds
          })
          message.success(`发送通知成功，共 ${allUserIds.length} 个用户`)
        }

        emit('submit')
      } catch (error) {
        message.error(error.message || '发送通知失败')
      }
    }

    // 监听器
    watch(() => props.formData, initForm, { immediate: true, deep: true })

    watch(() => form.sendType, (newType) => {
      // 切换发送方式时清空接收者
      if (newType === 'single') {
        form.recipientIds = []
      } else if (newType === 'batch') {
        form.recipientId = ''
      } else {
        form.recipientId = ''
        form.recipientIds = []
      }
    })

    // 生命周期
    onMounted(() => {
      fetchUsers()
      fetchNotificationTypes()
    })

    // 暴露方法
    expose({
      submit
    })

    return {
      formRef,
      form,
      rules,
      users,
      usersLoading,
      notificationTypes,
      entityTypes,
      filterOption,
      disabledDate,
      getTypeColor,
      getTypeLabel,
      getSelectedUserName,
      submit
    }
  }
})
</script>

<style scoped>
.notification-form {
  padding: 16px 0;
}

.notification-preview {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.preview-time {
  color: #666;
  font-size: 12px;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.preview-content {
  color: #666;
  line-height: 1.6;
  margin-bottom: 12px;
  white-space: pre-wrap;
}

.preview-recipients {
  color: #666;
  font-size: 12px;
  border-top: 1px solid #e8e8e8;
  padding-top: 8px;
}
</style>
