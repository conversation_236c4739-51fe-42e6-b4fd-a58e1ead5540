<!--
  通知详情页面
  功能：展示通知详细信息
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <div class="notification-detail-page">
    <div class="page-header">
      <a-page-header
        title="通知详情"
        @back="handleBack"
      >
        <template #extra>
          <a-space>
            <a-button v-if="!notificationData?.is_read" type="primary" @click="handleMarkAsRead">
              标记为已读
            </a-button>
            <a-button @click="handleDelete" danger>
              删除
            </a-button>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <div class="page-content">
      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
      </div>
      
      <div v-else-if="notificationData" class="notification-detail">
        <!-- 通知头部 -->
        <a-card class="notification-header">
          <div class="header-content">
            <div class="title-section">
              <h2 class="notification-title">{{ notificationData.title }}</h2>
              <div class="notification-meta">
                <a-space>
                  <a-tag :color="getTypeColor(notificationData.type)">
                    {{ getTypeText(notificationData.type) }}
                  </a-tag>
                  <a-tag :color="getPriorityColor(notificationData.priority)">
                    {{ getPriorityText(notificationData.priority) }}
                  </a-tag>
                  <a-tag :color="notificationData.is_read ? 'default' : 'blue'">
                    {{ notificationData.is_read ? '已读' : '未读' }}
                  </a-tag>
                </a-space>
              </div>
            </div>
            <div class="time-section">
              <div class="send-time">
                发送时间：{{ formatDate(notificationData.created_at) }}
              </div>
              <div v-if="notificationData.read_at" class="read-time">
                阅读时间：{{ formatDate(notificationData.read_at) }}
              </div>
            </div>
          </div>
        </a-card>

        <!-- 通知内容 -->
        <a-card title="通知内容" class="content-card">
          <div class="notification-content" v-html="formatContent(notificationData.content)"></div>
        </a-card>

        <!-- 附加信息 -->
        <a-card v-if="notificationData.data" title="附加信息" class="data-card">
          <pre class="json-content">{{ formatJSON(notificationData.data) }}</pre>
        </a-card>

        <!-- 操作记录 -->
        <a-card v-if="notificationData.actions && notificationData.actions.length > 0" title="操作记录" class="actions-card">
          <a-timeline>
            <a-timeline-item
              v-for="action in notificationData.actions"
              :key="action.id"
              :color="getActionColor(action.type)"
            >
              <div class="action-item">
                <div class="action-title">{{ action.title }}</div>
                <div class="action-time">{{ formatDate(action.created_at) }}</div>
                <div v-if="action.description" class="action-description">{{ action.description }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>

        <!-- 相关链接 -->
        <a-card v-if="notificationData.links && notificationData.links.length > 0" title="相关链接" class="links-card">
          <a-list
            :data-source="notificationData.links"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <a :href="item.url" target="_blank">{{ item.title }}</a>
                  </template>
                  <template #description>
                    {{ item.description }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </div>

      <div v-else class="empty-container">
        <a-empty description="未找到通知信息" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { formatDate } from '@/utils/date'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const notificationData = ref(null)

// 生命周期
onMounted(() => {
  loadNotificationDetail()
})

// 加载通知详情
const loadNotificationDetail = async () => {
  const notificationId = route.params.id
  if (!notificationId) {
    message.error('缺少通知ID参数')
    return
  }
  
  loading.value = true
  try {
    // 临时模拟数据
    notificationData.value = {
      id: notificationId,
      title: '税务政策更新通知',
      type: 'policy_update',
      priority: 'high',
      content: `
        <p>尊敬的用户：</p>
        <p>根据最新的税务政策调整，现通知如下重要变更：</p>
        <ul>
          <li>增值税税率调整：制造业从13%调整为12%</li>
          <li>小微企业所得税优惠政策延续至2025年</li>
          <li>研发费用加计扣除比例提高至100%</li>
        </ul>
        <p>请及时关注相关政策变化，确保企业合规经营。</p>
        <p>如有疑问，请联系客服。</p>
      `,
      data: {
        policy_ids: ['policy_001', 'policy_002', 'policy_003'],
        effective_date: '2024-01-01',
        impact_level: 'high'
      },
      is_read: false,
      read_at: null,
      created_at: new Date().toISOString(),
      actions: [
        {
          id: 'action_001',
          type: 'send',
          title: '通知已发送',
          description: '系统自动发送通知',
          created_at: new Date().toISOString()
        }
      ],
      links: [
        {
          title: '查看相关政策',
          url: '/tax-policy',
          description: '点击查看最新的税务政策列表'
        },
        {
          title: '政策解读文档',
          url: '/documents/policy-guide.pdf',
          description: '下载详细的政策解读文档'
        }
      ]
    }
    
    // 如果是未读通知，自动标记为已读
    if (!notificationData.value.is_read) {
      await markAsRead()
    }
    
  } catch (error) {
    message.error('加载通知详情失败：' + error.message)
    notificationData.value = null
  } finally {
    loading.value = false
  }
}

// 返回
const handleBack = () => {
  router.back()
}

// 标记为已读
const handleMarkAsRead = async () => {
  await markAsRead()
  message.success('已标记为已读')
}

// 标记为已读的实际操作
const markAsRead = async () => {
  try {
    // 这里应该调用API标记为已读
    notificationData.value.is_read = true
    notificationData.value.read_at = new Date().toISOString()
  } catch (error) {
    console.error('标记已读失败:', error)
  }
}

// 删除通知
const handleDelete = () => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条通知吗？删除后无法恢复。',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 这里应该调用API删除通知
        message.success('通知已删除')
        router.push('/notification')
      } catch (error) {
        message.error('删除通知失败：' + error.message)
      }
    }
  })
}

// 辅助函数
const getTypeColor = (type) => {
  const colors = {
    system: 'blue',
    policy_update: 'green',
    warning: 'orange',
    error: 'red',
    info: 'default'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    system: '系统通知',
    policy_update: '政策更新',
    warning: '警告',
    error: '错误',
    info: '信息'
  }
  return texts[type] || type
}

const getPriorityColor = (priority) => {
  const colors = {
    low: 'default',
    normal: 'blue',
    high: 'orange',
    urgent: 'red'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    low: '低',
    normal: '普通',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

const getActionColor = (type) => {
  const colors = {
    send: 'blue',
    read: 'green',
    delete: 'red',
    update: 'orange'
  }
  return colors[type] || 'default'
}

const formatContent = (content) => {
  // 简单的HTML内容格式化
  return content.replace(/\n/g, '<br>')
}

const formatJSON = (data) => {
  try {
    const obj = typeof data === 'string' ? JSON.parse(data) : data
    return JSON.stringify(obj, null, 2)
  } catch (error) {
    return data
  }
}
</script>

<style scoped>
.notification-detail-page {
  background-color: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background-color: white;
  margin-bottom: 16px;
}

.page-content {
  padding: 0 24px 24px;
  max-width: 1000px;
  margin: 0 auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  background-color: white;
  border-radius: 6px;
}

.notification-detail {
  margin-bottom: 24px;
}

.notification-header {
  margin-bottom: 16px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.notification-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.notification-meta {
  margin-bottom: 8px;
}

.time-section {
  text-align: right;
  color: #666;
  font-size: 14px;
}

.send-time, .read-time {
  margin-bottom: 4px;
}

.content-card,
.data-card,
.actions-card,
.links-card {
  margin-bottom: 16px;
}

.notification-content {
  line-height: 1.8;
  color: #262626;
  font-size: 15px;
}

.notification-content :deep(p) {
  margin-bottom: 12px;
}

.notification-content :deep(ul) {
  margin: 12px 0;
  padding-left: 24px;
}

.notification-content :deep(li) {
  margin-bottom: 8px;
}

.json-content {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
  line-height: 1.45;
  overflow-x: auto;
  white-space: pre;
  max-height: 300px;
}

.action-item {
  margin-bottom: 8px;
}

.action-title {
  font-weight: 500;
  color: #262626;
}

.action-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.action-description {
  color: #666;
  margin-top: 4px;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}

:deep(.ant-page-header-heading-title) {
  font-size: 20px;
  font-weight: 600;
}

:deep(.ant-list-item-meta-title a) {
  color: #1890ff;
  text-decoration: none;
}

:deep(.ant-list-item-meta-title a:hover) {
  text-decoration: underline;
}
</style>
