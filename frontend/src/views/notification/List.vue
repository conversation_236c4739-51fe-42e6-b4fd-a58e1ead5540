<template>
  <div class="notification-list-container">
    <!-- 页面头部 -->
    <MacOSPageHeader
      :title="pageTitle"
      :breadcrumbs="breadcrumbs"
      :show-back="false"
    >
      <template #actions>
        <MacOSButton
          type="primary"
          icon="plus"
          @click="handleCreate"
        >
          发送通知
        </MacOSButton>
        <MacOSButton
          icon="check"
          @click="handleMarkAllRead"
        >
          全部已读
        </MacOSButton>
        <MacOSButton
          icon="delete"
          @click="handleClearRead"
        >
          清空已读
        </MacOSButton>
      </template>
    </MacOSPageHeader>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <MacOSCard class="stat-card">
        <a-statistic
          title="总通知数"
          :value="stats.total"
          :value-style="{ color: '#1890ff' }"
        />
      </MacOSCard>
      <MacOSCard class="stat-card">
        <a-statistic
          title="未读通知"
          :value="stats.unread"
          :value-style="{ color: '#f5222d' }"
        />
      </MacOSCard>
      <MacOSCard class="stat-card">
        <a-statistic
          title="今日新增"
          :value="stats.today"
          :value-style="{ color: '#52c41a' }"
        />
      </MacOSCard>
    </div>

    <!-- 搜索表单 -->
    <MacOSCard class="search-card">
      <SearchForm
        :form-items="searchFormItems"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
      />
    </MacOSCard>

    <!-- 数据表格 -->
    <MacOSCard>
      <template #title>
        <div class="table-header">
          <span>通知列表</span>
          <div class="table-actions">
            <MacOSButton
              v-if="selectedRowKeys.length > 0"
              type="primary"
              size="small"
              @click="handleBatchMarkRead"
            >
              标记已读 ({{ selectedRowKeys.length }})
            </MacOSButton>
            <MacOSButton
              v-if="selectedRowKeys.length > 0"
              type="danger"
              size="small"
              @click="handleBatchDelete"
            >
              批量删除 ({{ selectedRowKeys.length }})
            </MacOSButton>
          </div>
        </div>
      </template>

      <MacOSDataTable
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        :row-class-name="getRowClassName"
        @change="handleTableChange"
      />
    </MacOSCard>

    <!-- 创建通知弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :width="800"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <NotificationForm
        ref="notificationFormRef"
        :form-data="currentRecord"
        :is-edit="isEdit"
        @submit="handleFormSubmit"
      />
    </a-modal>

    <!-- 通知详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="通知详情"
      :width="600"
      :footer="null"
    >
      <NotificationDetail
        :notification="currentRecord"
        @mark-read="handleMarkRead"
        @delete="handleDelete"
      />
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  MacOSPageHeader,
  MacOSCard,
  MacOSButton,
  MacOSDataTable,
  SearchForm
} from '@/components/common'
import NotificationForm from './components/NotificationForm.vue'
import NotificationDetail from './components/NotificationDetail.vue'
import {
  getMyNotifications,
  getNotificationCount,
  markNotificationsAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  batchDeleteNotifications,
  deleteReadNotifications,
  getNotificationTypes
} from '@/api/notification'

export default defineComponent({
  name: 'NotificationList',
  components: {
    MacOSPageHeader,
    MacOSCard,
    MacOSButton,
    MacOSDataTable,
    SearchForm,
    NotificationForm,
    NotificationDetail
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const dataSource = ref([])
    const selectedRowKeys = ref([])
    const modalVisible = ref(false)
    const modalLoading = ref(false)
    const detailModalVisible = ref(false)
    const currentRecord = ref({})
    const isEdit = ref(false)
    const notificationTypes = ref([])

    // 引用
    const notificationFormRef = ref(null)

    // 统计数据
    const stats = reactive({
      total: 0,
      unread: 0,
      today: 0
    })

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
    })

    // 搜索表单配置
    const searchForm = reactive({
      type: '',
      isRead: null,
      createdStart: '',
      createdEnd: ''
    })

    // 计算属性
    const pageTitle = computed(() => '通知管理')
    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '系统管理', path: '/system' },
      { title: '通知管理' }
    ])

    const modalTitle = computed(() => isEdit.value ? '编辑通知' : '发送通知')

    const searchFormItems = computed(() => [
      {
        type: 'select',
        field: 'type',
        label: '通知类型',
        placeholder: '请选择通知类型',
        options: notificationTypes.value,
        allowClear: true
      },
      {
        type: 'select',
        field: 'isRead',
        label: '阅读状态',
        placeholder: '请选择阅读状态',
        options: [
          { label: '未读', value: false },
          { label: '已读', value: true }
        ],
        allowClear: true
      },
      {
        type: 'date-range',
        field: ['createdStart', 'createdEnd'],
        label: '创建时间',
        placeholder: ['开始日期', '结束日期']
      }
    ])

    // 表格列配置
    const columns = [
      {
        title: '状态',
        dataIndex: 'isRead',
        key: 'isRead',
        width: 80,
        customRender: ({ text }) => {
          return h('a-badge', {
            status: text ? 'default' : 'processing',
            text: text ? '已读' : '未读'
          })
        }
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        width: 120,
        customRender: ({ text }) => {
          const typeOption = notificationTypes.value.find(item => item.value === text)
          return typeOption?.label || text
        }
      },
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        ellipsis: true
      },
      {
        title: '内容',
        dataIndex: 'content',
        key: 'content',
        ellipsis: true,
        width: 300,
        customRender: ({ text }) => {
          return text && text.length > 50 ? text.substring(0, 50) + '...' : text
        }
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 160,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleString() : '-'
        }
      },
      {
        title: '阅读时间',
        dataIndex: 'readAt',
        key: 'readAt',
        width: 160,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleString() : '-'
        }
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        customRender: ({ record }) => {
          const actions = [
            h('a', {
              onClick: () => handleView(record)
            }, '查看')
          ]

          if (!record.isRead) {
            actions.push(
              h('a-divider', { type: 'vertical' }),
              h('a', {
                onClick: () => handleMarkRead(record)
              }, '标记已读')
            )
          }

          actions.push(
            h('a-divider', { type: 'vertical' }),
            h('a', {
              style: { color: '#ff4d4f' },
              onClick: () => handleDelete(record)
            }, '删除')
          )

          return actions
        }
      }
    ]

    // 行选择配置
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (keys) => {
        selectedRowKeys.value = keys
      }
    }

    // 方法
    const getRowClassName = (record) => {
      return record.isRead ? 'read-row' : 'unread-row'
    }

    const fetchData = async () => {
      loading.value = true
      try {
        const params = {
          ...searchForm,
          page: pagination.current,
          pageSize: pagination.pageSize
        }

        const response = await getMyNotifications(params)
        if (response.code === 200) {
          dataSource.value = response.data.data || []
          pagination.total = response.data.total || 0
        }
      } catch (error) {
        message.error('获取通知列表失败')
      } finally {
        loading.value = false
      }
    }

    const fetchStats = async () => {
      try {
        const response = await getNotificationCount()
        if (response.code === 200) {
          stats.total = response.data.total || 0
          stats.unread = response.data.unread || 0
          
          // 计算今日新增（简化实现）
          const today = new Date().toDateString()
          stats.today = dataSource.value.filter(item => 
            new Date(item.createdAt).toDateString() === today
          ).length
        }
      } catch (error) {
        console.error('获取统计信息失败:', error)
      }
    }

    const fetchNotificationTypes = () => {
      notificationTypes.value = getNotificationTypes()
    }

    const handleSearch = (values) => {
      Object.assign(searchForm, values)
      pagination.current = 1
      fetchData()
    }

    const handleReset = () => {
      Object.assign(searchForm, {
        type: '',
        isRead: null,
        createdStart: '',
        createdEnd: ''
      })
      pagination.current = 1
      fetchData()
    }

    const handleTableChange = (pag) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      fetchData()
    }

    const handleCreate = () => {
      currentRecord.value = {}
      isEdit.value = false
      modalVisible.value = true
    }

    const handleView = (record) => {
      currentRecord.value = { ...record }
      detailModalVisible.value = true
      
      // 如果是未读通知，自动标记为已读
      if (!record.isRead) {
        handleMarkRead(record, false)
      }
    }

    const handleMarkRead = async (record, showMessage = true) => {
      try {
        await markNotificationsAsRead([record.id])
        if (showMessage) {
          message.success('标记已读成功')
        }
        fetchData()
        fetchStats()
      } catch (error) {
        if (showMessage) {
          message.error('标记已读失败')
        }
      }
    }

    const handleDelete = (record) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除通知"${record.title}"吗？`,
        onOk: async () => {
          try {
            await deleteNotification(record.id)
            message.success('删除成功')
            fetchData()
            fetchStats()
          } catch (error) {
            message.error('删除失败')
          }
        }
      })
    }

    const handleBatchMarkRead = async () => {
      try {
        await markNotificationsAsRead(selectedRowKeys.value)
        message.success('批量标记已读成功')
        selectedRowKeys.value = []
        fetchData()
        fetchStats()
      } catch (error) {
        message.error('批量标记已读失败')
      }
    }

    const handleBatchDelete = () => {
      Modal.confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${selectedRowKeys.value.length} 条通知吗？`,
        onOk: async () => {
          try {
            await batchDeleteNotifications(selectedRowKeys.value)
            message.success('批量删除成功')
            selectedRowKeys.value = []
            fetchData()
            fetchStats()
          } catch (error) {
            message.error('批量删除失败')
          }
        }
      })
    }

    const handleMarkAllRead = async () => {
      try {
        await markAllNotificationsAsRead()
        message.success('全部标记已读成功')
        fetchData()
        fetchStats()
      } catch (error) {
        message.error('全部标记已读失败')
      }
    }

    const handleClearRead = () => {
      Modal.confirm({
        title: '确认清空已读通知',
        content: '确定要删除所有已读通知吗？此操作不可恢复。',
        onOk: async () => {
          try {
            await deleteReadNotifications()
            message.success('清空已读通知成功')
            fetchData()
            fetchStats()
          } catch (error) {
            message.error('清空已读通知失败')
          }
        }
      })
    }

    const handleModalOk = () => {
      notificationFormRef.value?.submit()
    }

    const handleModalCancel = () => {
      modalVisible.value = false
      currentRecord.value = {}
    }

    const handleFormSubmit = () => {
      modalVisible.value = false
      fetchData()
      fetchStats()
    }

    // 生命周期
    onMounted(() => {
      fetchNotificationTypes()
      fetchData()
      fetchStats()
    })

    return {
      // 响应式数据
      loading,
      dataSource,
      selectedRowKeys,
      modalVisible,
      modalLoading,
      detailModalVisible,
      currentRecord,
      isEdit,
      stats,
      pagination,
      searchForm,

      // 引用
      notificationFormRef,

      // 计算属性
      pageTitle,
      breadcrumbs,
      modalTitle,
      searchFormItems,
      columns,
      rowSelection,

      // 方法
      getRowClassName,
      handleSearch,
      handleReset,
      handleTableChange,
      handleCreate,
      handleView,
      handleMarkRead,
      handleDelete,
      handleBatchMarkRead,
      handleBatchDelete,
      handleMarkAllRead,
      handleClearRead,
      handleModalOk,
      handleModalCancel,
      handleFormSubmit
    }
  }
})
</script>

<style scoped>
.notification-list-container {
  background: #f0f2f5;
  min-height: 100vh;
  padding: 24px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.stat-card {
  text-align: center;
}

.search-card {
  margin-bottom: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 8px;
}

:deep(.unread-row) {
  background-color: #f6ffed;
  font-weight: 500;
}

:deep(.read-row) {
  opacity: 0.7;
}

:deep(.ant-table-tbody > tr.unread-row:hover > td) {
  background-color: #f6ffed !important;
}
</style>
