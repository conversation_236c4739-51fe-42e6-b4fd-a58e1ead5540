<!--
  报表管理主页面
  功能：报表模板管理、报表生成
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <div class="report-container">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">报表管理</h1>
        <p class="page-description">管理报表模板和生成各类报表</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button @click="showGenerateModal = true">
            <template #icon><FileTextOutlined /></template>
            生成报表
          </a-button>
          <a-button type="primary" @click="handleCreateTemplate">
            <template #icon><PlusOutlined /></template>
            新增模板
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="模板总数"
              :value="stats.totalTemplates"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="本月生成"
              :value="stats.monthlyGenerated"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="常用模板"
              :value="stats.popularTemplates"
              :value-style="{ color: '#fa8c16' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="自定义模板"
              :value="stats.customTemplates"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <a-card :bordered="false">
        <a-form
          :model="searchForm"
          layout="inline"
          @finish="handleSearch"
        >
          <a-form-item label="模板名称" name="keyword">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="请输入模板名称"
              style="width: 200px"
              allow-clear
            />
          </a-form-item>
          
          <a-form-item label="模板类型" name="type">
            <a-select
              v-model:value="searchForm.type"
              placeholder="请选择模板类型"
              style="width: 150px"
              allow-clear
            >
              <a-select-option
                v-for="type in reportTypes"
                :key="type.value"
                :value="type.value"
              >
                {{ type.label }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item>
            <a-button type="primary" html-type="submit">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
            <a-button style="margin-left: 8px" @click="handleReset">
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 模板列表 -->
    <div class="template-section">
      <a-card title="报表模板" :bordered="false">
        <template #extra>
          <a-space>
            <a-button
              v-if="selectedRowKeys.length > 0"
              danger
              @click="handleBatchDelete"
            >
              批量删除 ({{ selectedRowKeys.length }})
            </a-button>
          </a-space>
        </template>

        <a-table
          :columns="columns"
          :data-source="tableData"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          row-key="id"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <a @click="handleViewTemplate(record)">{{ record.name }}</a>
            </template>
            
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>

            <template v-if="column.key === 'created_at'">
              {{ formatDateTime(record.created_at) }}
            </template>

            <template v-if="column.key === 'updated_at'">
              {{ formatDateTime(record.updated_at) }}
            </template>

            <template v-if="column.key === 'creator'">
              {{ record.creator?.username || '系统' }}
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="handleGenerate(record)">
                  生成
                </a-button>
                <a-button type="link" size="small" @click="handleEditTemplate(record)">
                  编辑
                </a-button>
                <a-button type="link" size="small" @click="handleCopyTemplate(record)">
                  复制
                </a-button>
                <a-popconfirm
                  title="确定要删除这个模板吗？"
                  @confirm="handleDeleteTemplate(record)"
                >
                  <a-button type="link" size="small" danger>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 模板表单弹窗 -->
    <ReportTemplateForm
      v-model:visible="templateFormVisible"
      :form-data="currentTemplate"
      :mode="templateFormMode"
      @success="handleTemplateFormSuccess"
    />

    <!-- 报表生成弹窗 -->
    <ReportGenerateModal
      v-model:visible="showGenerateModal"
      :template-data="generateTemplate"
      @success="handleGenerateSuccess"
    />

    <!-- 模板详情弹窗 -->
    <ReportTemplateDetail
      v-model:visible="templateDetailVisible"
      :template-id="currentTemplate?.id"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import { useReportStore } from '@/store/report'
import { formatDateTime } from '@/utils/date'
import ReportTemplateForm from './components/ReportTemplateForm.vue'
import ReportGenerateModal from './components/ReportGenerateModal.vue'
import ReportTemplateDetail from './components/ReportTemplateDetail.vue'

// 状态管理
const reportStore = useReportStore()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedRowKeys = ref([])
const templateFormVisible = ref(false)
const templateDetailVisible = ref(false)
const showGenerateModal = ref(false)
const currentTemplate = ref(null)
const generateTemplate = ref(null)
const templateFormMode = ref('create')

// 统计数据
const stats = reactive({
  totalTemplates: 0,
  monthlyGenerated: 0,
  popularTemplates: 0,
  customTemplates: 0
})

// 搜索表单
const searchForm = reactive({
  keyword: '',
  type: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 报表类型
const reportTypes = ref([
  { value: 'declaration', label: '申报表', color: 'blue' },
  { value: 'invoice', label: '发票报表', color: 'green' },
  { value: 'tax', label: '税务报表', color: 'orange' },
  { value: 'financial', label: '财务报表', color: 'purple' },
  { value: 'statistics', label: '统计报表', color: 'cyan' },
  { value: 'custom', label: '自定义报表', color: 'red' }
])

// 表格列配置
const columns = [
  {
    title: '模板名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    ellipsis: true
  },
  {
    title: '模板类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 200,
    ellipsis: true
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 150
  },
  {
    title: '更新时间',
    dataIndex: 'updated_at',
    key: 'updated_at',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}))

// 初始化
onMounted(() => {
  loadData()
  loadStats()
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    const response = await reportStore.getReportTemplates(params)
    tableData.value = response.list || []
    pagination.total = response.total || 0
  } catch (error) {
    message.error('加载数据失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStats = async () => {
  try {
    // TODO: 实现统计数据加载
    stats.totalTemplates = tableData.value.length
    stats.monthlyGenerated = 0
    stats.popularTemplates = 0
    stats.customTemplates = 0
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'keyword' ? '' : undefined
  })
  pagination.current = 1
  loadData()
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

// 创建模板
const handleCreateTemplate = () => {
  currentTemplate.value = null
  templateFormMode.value = 'create'
  templateFormVisible.value = true
}

// 编辑模板
const handleEditTemplate = (record) => {
  currentTemplate.value = { ...record }
  templateFormMode.value = 'edit'
  templateFormVisible.value = true
}

// 查看模板
const handleViewTemplate = (record) => {
  currentTemplate.value = record
  templateDetailVisible.value = true
}

// 复制模板
const handleCopyTemplate = (record) => {
  const copyData = {
    ...record,
    id: undefined,
    name: record.name + ' (副本)'
  }
  currentTemplate.value = copyData
  templateFormMode.value = 'create'
  templateFormVisible.value = true
}

// 删除模板
const handleDeleteTemplate = async (record) => {
  try {
    await reportStore.deleteReportTemplate(record.id)
    message.success('删除成功')
    loadData()
  } catch (error) {
    message.error('删除失败：' + error.message)
  }
}

// 批量删除
const handleBatchDelete = () => {
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个模板吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await reportStore.batchDeleteReportTemplates(selectedRowKeys.value)
        message.success('批量删除成功')
        selectedRowKeys.value = []
        loadData()
      } catch (error) {
        message.error('批量删除失败：' + error.message)
      }
    }
  })
}

// 生成报表
const handleGenerate = (record) => {
  generateTemplate.value = record
  showGenerateModal.value = true
}

// 模板表单成功回调
const handleTemplateFormSuccess = () => {
  templateFormVisible.value = false
  loadData()
  loadStats()
  message.success(templateFormMode.value === 'create' ? '创建成功' : '更新成功')
}

// 报表生成成功回调
const handleGenerateSuccess = () => {
  showGenerateModal.value = false
  message.success('报表生成成功')
}

// 辅助函数
const getTypeColor = (type) => {
  const typeInfo = reportTypes.value.find(t => t.value === type)
  return typeInfo?.color || 'default'
}

const getTypeText = (type) => {
  const typeInfo = reportTypes.value.find(t => t.value === type)
  return typeInfo?.label || type
}
</script>

<style scoped>
.report-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.page-description {
  margin: 4px 0 0 0;
  color: #8c8c8c;
  font-size: 14px;
}

.stats-section {
  margin-bottom: 24px;
}

.search-section {
  margin-bottom: 16px;
}

.template-section {
  background: #fff;
}
</style>
