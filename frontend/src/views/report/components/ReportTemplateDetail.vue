<!--
  报表模板详情组件
  功能：展示报表模板详细信息
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <a-modal
    :visible="visible"
    title="报表模板详情"
    :width="1000"
    :footer="null"
    @cancel="handleCancel"
  >
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    
    <div v-else-if="templateData" class="template-detail">
      <!-- 模板头部信息 -->
      <div class="template-header">
        <div class="header-left">
          <h2 class="template-title">{{ templateData.name }}</h2>
          <div class="template-meta">
            <a-tag :color="getTypeColor(templateData.type)">
              {{ getTypeText(templateData.type) }}
            </a-tag>
          </div>
        </div>
      </div>

      <!-- 基本信息 -->
      <a-card title="基本信息" class="info-card">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="模板名称">
            {{ templateData.name }}
          </a-descriptions-item>
          <a-descriptions-item label="模板类型">
            <a-tag :color="getTypeColor(templateData.type)">
              {{ getTypeText(templateData.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDate(templateData.created_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ formatDate(templateData.updated_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="创建人">
            {{ templateData.creator?.username || '系统' }}
          </a-descriptions-item>
          <a-descriptions-item label="更新人">
            {{ templateData.updater?.username || '系统' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 模板描述 -->
      <a-card v-if="templateData.description" title="模板描述" class="info-card">
        <div class="content-text">{{ templateData.description }}</div>
      </a-card>

      <!-- 模板内容 -->
      <a-card title="模板内容" class="info-card">
        <div class="content-section">
          <a-typography-paragraph>
            <pre class="template-content">{{ formatTemplateContent(templateData.content) }}</pre>
          </a-typography-paragraph>
        </div>
      </a-card>

      <!-- 模板参数 -->
      <a-card v-if="templateData.parameters" title="模板参数" class="info-card">
        <div class="parameters-section">
          <div v-if="parsedParameters.length > 0">
            <a-table
              :columns="parameterColumns"
              :data-source="parsedParameters"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'type'">
                  <a-tag color="blue">{{ getParameterTypeText(record.type) }}</a-tag>
                </template>
                <template v-if="column.key === 'required'">
                  <a-tag :color="record.required ? 'red' : 'default'">
                    {{ record.required ? '必填' : '可选' }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'defaultValue'">
                  <span v-if="record.defaultValue !== undefined">{{ record.defaultValue }}</span>
                  <span v-else class="text-muted">无</span>
                </template>
              </template>
            </a-table>
          </div>
          <div v-else>
            <a-typography-paragraph>
              <pre class="parameters-content">{{ templateData.parameters }}</pre>
            </a-typography-paragraph>
          </div>
        </div>
      </a-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button type="primary" @click="handleGenerate">
            生成报表
          </a-button>
          <a-button @click="handleEdit">
            编辑模板
          </a-button>
          <a-button @click="handleCopy">
            复制模板
          </a-button>
        </a-space>
      </div>
    </div>

    <div v-else class="empty-container">
      <a-empty description="未找到模板信息" />
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { useReportStore } from '@/store/report'
import { formatDate } from '@/utils/date'
import { getReportTypeInfo, PARAMETER_TYPES } from '@/api/report'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  templateId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:visible', 'edit', 'generate', 'copy'])

// 状态管理
const reportStore = useReportStore()

// 响应式数据
const loading = ref(false)
const templateData = ref(null)

// 参数表格列配置
const parameterColumns = [
  {
    title: '参数名称',
    dataIndex: 'name',
    key: 'name',
    width: 120
  },
  {
    title: '参数标签',
    dataIndex: 'label',
    key: 'label',
    width: 120
  },
  {
    title: '参数类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '是否必填',
    dataIndex: 'required',
    key: 'required',
    width: 80
  },
  {
    title: '默认值',
    dataIndex: 'defaultValue',
    key: 'defaultValue',
    width: 120
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description'
  }
]

// 计算属性
const parsedParameters = computed(() => {
  if (!templateData.value?.parameters) return []
  
  try {
    const params = JSON.parse(templateData.value.parameters)
    return Array.isArray(params) ? params : []
  } catch (error) {
    console.error('解析模板参数失败:', error)
    return []
  }
})

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal && props.templateId) {
    loadTemplateDetail()
  }
})

watch(() => props.templateId, (newVal) => {
  if (newVal && props.visible) {
    loadTemplateDetail()
  }
})

// 加载模板详情
const loadTemplateDetail = async () => {
  if (!props.templateId) return
  
  loading.value = true
  try {
    const data = await reportStore.getReportTemplateById(props.templateId)
    templateData.value = data
  } catch (error) {
    message.error('加载模板详情失败：' + error.message)
    templateData.value = null
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
  templateData.value = null
}

// 生成报表
const handleGenerate = () => {
  emit('generate', templateData.value)
  handleCancel()
}

// 编辑模板
const handleEdit = () => {
  emit('edit', templateData.value)
  handleCancel()
}

// 复制模板
const handleCopy = () => {
  emit('copy', templateData.value)
  handleCancel()
}

// 辅助函数
const getTypeColor = (type) => {
  return getReportTypeInfo(type).color
}

const getTypeText = (type) => {
  return getReportTypeInfo(type).label
}

const getParameterTypeText = (type) => {
  const typeInfo = PARAMETER_TYPES.find(t => t.value === type)
  return typeInfo?.label || type
}

const formatTemplateContent = (content) => {
  try {
    // 尝试格式化JSON
    const parsed = JSON.parse(content)
    return JSON.stringify(parsed, null, 2)
  } catch (error) {
    // 如果不是JSON，直接返回原内容
    return content
  }
}
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.template-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.template-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.template-meta {
  display: flex;
  gap: 8px;
}

.info-card {
  margin-bottom: 16px;
}

.content-section {
  margin-bottom: 16px;
}

.content-text {
  line-height: 1.6;
  color: #262626;
  white-space: pre-wrap;
  word-break: break-word;
}

.template-content,
.parameters-content {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
  line-height: 1.45;
  overflow-x: auto;
  white-space: pre;
}

.parameters-section {
  margin-bottom: 16px;
}

.text-muted {
  color: #8c8c8c;
  font-style: italic;
}

.action-buttons {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  background-color: #fafafa;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}

:deep(.ant-typography) {
  margin-bottom: 0;
}
</style>
