<!--
  报表生成弹窗组件
  功能：生成报表
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <a-modal
    :visible="visible"
    title="生成报表"
    :width="600"
    :confirm-loading="loading"
    @ok="handleGenerate"
    @cancel="handleCancel"
  >
    <div class="generate-content">
      <!-- 模板信息 -->
      <div v-if="templateData" class="template-info">
        <h4>模板信息</h4>
        <a-descriptions :column="1" bordered size="small">
          <a-descriptions-item label="模板名称">
            {{ templateData.name }}
          </a-descriptions-item>
          <a-descriptions-item label="模板类型">
            <a-tag :color="getTypeColor(templateData.type)">
              {{ getTypeText(templateData.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item v-if="templateData.description" label="模板描述">
            {{ templateData.description }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 生成参数 -->
      <div class="generate-params">
        <h4>生成参数</h4>
        <a-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          layout="vertical"
        >
          <a-form-item label="输出格式" name="format">
            <a-select
              v-model:value="formData.format"
              placeholder="请选择输出格式"
            >
              <a-select-option
                v-for="format in reportFormats"
                :key="format.value"
                :value="format.value"
              >
                <div class="format-option">
                  <span class="format-label">{{ format.label }}</span>
                  <span class="format-ext">{{ format.extension }}</span>
                </div>
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="报表名称" name="reportName">
            <a-input
              v-model:value="formData.reportName"
              placeholder="请输入报表名称"
            />
          </a-form-item>

          <!-- 动态参数 -->
          <div v-if="templateParameters.length > 0" class="dynamic-params">
            <h5>模板参数</h5>
            <div
              v-for="param in templateParameters"
              :key="param.name"
              class="param-item"
            >
              <a-form-item
                :label="param.label || param.name"
                :name="['parameters', param.name]"
                :rules="getParamRules(param)"
              >
                <!-- 文本输入 -->
                <a-input
                  v-if="param.type === 'string'"
                  v-model:value="formData.parameters[param.name]"
                  :placeholder="param.placeholder || `请输入${param.label || param.name}`"
                />

                <!-- 数字输入 -->
                <a-input-number
                  v-else-if="param.type === 'number'"
                  v-model:value="formData.parameters[param.name]"
                  :placeholder="param.placeholder || `请输入${param.label || param.name}`"
                  style="width: 100%"
                />

                <!-- 日期选择 -->
                <a-date-picker
                  v-else-if="param.type === 'date'"
                  v-model:value="formData.parameters[param.name]"
                  :placeholder="param.placeholder || `请选择${param.label || param.name}`"
                  style="width: 100%"
                />

                <!-- 日期范围选择 -->
                <a-range-picker
                  v-else-if="param.type === 'daterange'"
                  v-model:value="formData.parameters[param.name]"
                  :placeholder="[param.startPlaceholder || '开始日期', param.endPlaceholder || '结束日期']"
                  style="width: 100%"
                />

                <!-- 下拉选择 -->
                <a-select
                  v-else-if="param.type === 'select'"
                  v-model:value="formData.parameters[param.name]"
                  :placeholder="param.placeholder || `请选择${param.label || param.name}`"
                >
                  <a-select-option
                    v-for="option in param.options"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-select-option>
                </a-select>

                <!-- 多选 -->
                <a-select
                  v-else-if="param.type === 'multiselect'"
                  v-model:value="formData.parameters[param.name]"
                  mode="multiple"
                  :placeholder="param.placeholder || `请选择${param.label || param.name}`"
                >
                  <a-select-option
                    v-for="option in param.options"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-select-option>
                </a-select>

                <!-- 布尔选择 -->
                <a-switch
                  v-else-if="param.type === 'boolean'"
                  v-model:checked="formData.parameters[param.name]"
                  :checked-children="param.trueLabel || '是'"
                  :un-checked-children="param.falseLabel || '否'"
                />

                <!-- 默认文本输入 -->
                <a-input
                  v-else
                  v-model:value="formData.parameters[param.name]"
                  :placeholder="param.placeholder || `请输入${param.label || param.name}`"
                />
              </a-form-item>
            </div>
          </div>
        </a-form>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { useReportStore } from '@/store/report'
import { REPORT_FORMATS, getReportTypeInfo } from '@/api/report'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  templateData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 状态管理
const reportStore = useReportStore()

// 响应式数据
const formRef = ref()
const loading = ref(false)
const reportFormats = ref(REPORT_FORMATS)

// 表单数据
const formData = reactive({
  format: 'excel',
  reportName: '',
  parameters: {}
})

// 模板参数
const templateParameters = computed(() => {
  if (!props.templateData?.parameters) return []
  
  try {
    const params = JSON.parse(props.templateData.parameters)
    return Array.isArray(params) ? params : []
  } catch (error) {
    console.error('解析模板参数失败:', error)
    return []
  }
})

// 表单验证规则
const formRules = {
  format: [
    { required: true, message: '请选择输出格式', trigger: 'change' }
  ],
  reportName: [
    { required: true, message: '请输入报表名称', trigger: 'blur' },
    { min: 2, max: 100, message: '报表名称长度应在2-100个字符之间', trigger: 'blur' }
  ]
}

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initFormData()
  }
})

watch(() => props.templateData, (newVal) => {
  if (newVal && props.visible) {
    initFormData()
  }
}, { deep: true })

// 初始化表单数据
const initFormData = () => {
  nextTick(() => {
    // 重置表单
    formData.format = 'excel'
    formData.reportName = props.templateData?.name ? `${props.templateData.name}_${new Date().toISOString().slice(0, 10)}` : ''
    formData.parameters = {}
    
    // 初始化参数默认值
    templateParameters.value.forEach(param => {
      if (param.defaultValue !== undefined) {
        formData.parameters[param.name] = param.defaultValue
      } else {
        // 根据类型设置默认值
        switch (param.type) {
          case 'boolean':
            formData.parameters[param.name] = false
            break
          case 'multiselect':
            formData.parameters[param.name] = []
            break
          default:
            formData.parameters[param.name] = ''
        }
      }
    })
    
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

// 获取参数验证规则
const getParamRules = (param) => {
  const rules = []
  
  if (param.required) {
    rules.push({
      required: true,
      message: `请${param.type === 'select' || param.type === 'multiselect' ? '选择' : '输入'}${param.label || param.name}`,
      trigger: param.type === 'select' || param.type === 'multiselect' ? 'change' : 'blur'
    })
  }
  
  if (param.type === 'string' && param.minLength) {
    rules.push({
      min: param.minLength,
      message: `${param.label || param.name}长度不能少于${param.minLength}个字符`,
      trigger: 'blur'
    })
  }
  
  if (param.type === 'string' && param.maxLength) {
    rules.push({
      max: param.maxLength,
      message: `${param.label || param.name}长度不能超过${param.maxLength}个字符`,
      trigger: 'blur'
    })
  }
  
  return rules
}

// 生成报表
const handleGenerate = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 准备生成数据
    const generateData = {
      template_id: props.templateData.id,
      format: formData.format,
      parameters: { ...formData.parameters }
    }
    
    // 处理日期参数
    Object.keys(generateData.parameters).forEach(key => {
      const param = templateParameters.value.find(p => p.name === key)
      if (param?.type === 'date' && generateData.parameters[key]) {
        generateData.parameters[key] = generateData.parameters[key].format('YYYY-MM-DD')
      } else if (param?.type === 'daterange' && generateData.parameters[key]) {
        generateData.parameters[key] = generateData.parameters[key].map(date => date.format('YYYY-MM-DD'))
      }
    })
    
    // 调用API生成报表
    const result = await reportStore.generateReport(generateData)
    
    emit('success', result)
    handleCancel()
    
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单填写是否正确')
    } else {
      message.error(error.message || '生成报表失败')
    }
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
}

// 辅助函数
const getTypeColor = (type) => {
  return getReportTypeInfo(type).color
}

const getTypeText = (type) => {
  return getReportTypeInfo(type).label
}
</script>

<style scoped>
.generate-content {
  max-height: 60vh;
  overflow-y: auto;
}

.template-info {
  margin-bottom: 24px;
}

.template-info h4,
.generate-params h4,
.dynamic-params h5 {
  margin: 0 0 12px 0;
  font-weight: 600;
  color: #262626;
}

.dynamic-params h5 {
  font-size: 14px;
  margin-top: 16px;
}

.param-item {
  margin-bottom: 16px;
}

.format-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.format-label {
  font-weight: 500;
}

.format-ext {
  color: #8c8c8c;
  font-size: 12px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  background-color: #fafafa;
}
</style>
