<!--
  报表模板表单组件
  功能：创建和编辑报表模板
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <a-modal
    :visible="visible"
    :title="modalTitle"
    :width="800"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="模板名称" name="name">
        <a-input
          v-model:value="formData.name"
          placeholder="请输入模板名称"
        />
      </a-form-item>

      <a-form-item label="模板类型" name="type">
        <a-select
          v-model:value="formData.type"
          placeholder="请选择模板类型"
        >
          <a-select-option
            v-for="type in reportTypes"
            :key="type.value"
            :value="type.value"
          >
            {{ type.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="模板描述" name="description">
        <a-textarea
          v-model:value="formData.description"
          placeholder="请输入模板描述"
          :rows="3"
        />
      </a-form-item>

      <a-form-item label="模板内容" name="content">
        <a-textarea
          v-model:value="formData.content"
          placeholder="请输入模板内容（支持JSON格式）"
          :rows="8"
        />
      </a-form-item>

      <a-form-item label="模板参数" name="parameters">
        <a-textarea
          v-model:value="formData.parameters"
          placeholder="请输入模板参数配置（JSON格式，可选）"
          :rows="4"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { useReportStore } from '@/store/report'
import { REPORT_TYPES } from '@/api/report'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'create',
    validator: (value) => ['create', 'edit'].includes(value)
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 状态管理
const reportStore = useReportStore()

// 响应式数据
const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = reactive({
  name: '',
  type: '',
  description: '',
  content: '',
  parameters: ''
})

// 报表类型
const reportTypes = ref(REPORT_TYPES)

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 100, message: '模板名称长度应在2-100个字符之间', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择模板类型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入模板内容', trigger: 'blur' },
    { min: 10, message: '模板内容不能少于10个字符', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && value.trim().startsWith('{')) {
          try {
            JSON.parse(value)
            return Promise.resolve()
          } catch (error) {
            return Promise.reject('模板内容JSON格式不正确')
          }
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  parameters: [
    {
      validator: (rule, value) => {
        if (value && value.trim()) {
          try {
            JSON.parse(value)
            return Promise.resolve()
          } catch (error) {
            return Promise.reject('参数配置JSON格式不正确')
          }
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const modalTitle = computed(() => {
  return props.mode === 'create' ? '新增报表模板' : '编辑报表模板'
})

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initFormData()
  }
})

watch(() => props.formData, (newVal) => {
  if (newVal && props.visible) {
    initFormData()
  }
}, { deep: true })

// 初始化表单数据
const initFormData = () => {
  nextTick(() => {
    if (props.mode === 'edit' && props.formData) {
      // 编辑模式，填充现有数据
      Object.keys(formData).forEach(key => {
        if (props.formData[key] !== undefined) {
          formData[key] = props.formData[key] || ''
        }
      })
    } else {
      // 创建模式，重置表单
      resetForm()
    }
  })
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 准备提交数据
    const submitData = { ...formData }
    
    // 调用API
    if (props.mode === 'create') {
      await reportStore.createReportTemplate(submitData)
    } else {
      await reportStore.updateReportTemplate(props.formData.id, submitData)
    }
    
    emit('success')
    handleCancel()
    
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      message.error('请检查表单填写是否正确')
    } else {
      // API错误
      message.error(error.message || '操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
  resetForm()
}
</script>

<style scoped>
:deep(.ant-form-item-label) {
  font-weight: 500;
}
</style>
