<template>
  <div class="declaration-submit-container">
    <PageHeader
      title="提交申报"
      :description="`申报编号: ${declaration.declarationNo || '-'}`"
      :breadcrumbs="breadcrumbs"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <div class="content">
      <a-row :gutter="16">
        <a-col :span="16">
          <!-- 申报预览卡片 -->
          <a-card title="申报信息预览" class="preview-card">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="申报编号">
                {{ declaration.declarationNo || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="申报状态">
                <a-tag :color="getStatusColor(declaration.status)">
                  {{ getStatusText(declaration.status) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="企业名称">
                {{ declaration.enterpriseName || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="税种">
                {{ declaration.taxTypeName || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="申报期间">
                {{ formatPeriod(declaration.period) }}
              </a-descriptions-item>
              <a-descriptions-item label="截止日期">
                {{ declaration.deadline || '-' }}
              </a-descriptions-item>
            </a-descriptions>

            <!-- 应纳税额信息 -->
            <div class="tax-amount-summary">
              <a-alert
                type="info"
                show-icon
                :message="`本次申报应缴纳税额: ${formatCurrency(getTotalTaxAmount())}`"
              />
            </div>
          </a-card>

          <!-- 申报验证结果 -->
          <a-card title="申报验证结果" class="validation-card">
            <a-spin :spinning="validating">
              <a-result
                v-if="validationStatus"
                :status="validationStatus"
                :title="validationResult.title"
                :sub-title="validationResult.message"
              />
              <a-button
                v-if="!validationStatus"
                type="primary"
                :loading="validating"
                @click="validateDeclaration"
              >
                开始验证
              </a-button>
              <div v-if="validationIssues.length > 0" class="validation-issues">
                <a-divider orientation="left">
                  验证问题
                </a-divider>
                <a-list
                  size="small"
                  bordered
                  :data-source="validationIssues"
                >
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-tag :color="item.level === 'error' ? 'error' : 'warning'">
                        {{ item.level === 'error' ? '错误' : '警告' }}
                      </a-tag>
                      {{ item.message }}
                    </a-list-item>
                  </template>
                </a-list>
              </div>
            </a-spin>
          </a-card>
        </a-col>

        <a-col :span="8">
          <!-- 提交信息卡片 -->
          <a-card title="提交信息" class="submit-card">
            <a-form
              ref="submitFormRef"
              :model="submitForm"
              layout="vertical"
            >
              <a-form-item label="提交方式" name="submitType" required>
                <a-radio-group v-model:value="submitForm.submitType">
                  <a-radio value="manual">
                    手动申报
                  </a-radio>
                  <a-radio value="digital">
                    电子申报
                  </a-radio>
                </a-radio-group>
              </a-form-item>

              <template v-if="submitForm.submitType === 'digital'">
                <a-form-item label="申报平台" name="platform" required>
                  <a-select
                    v-model:value="submitForm.platform"
                    placeholder="请选择申报平台"
                  >
                    <a-select-option value="gov">
                      政府税务平台
                    </a-select-option>
                    <a-select-option value="third_party">
                      第三方申报平台
                    </a-select-option>
                  </a-select>
                </a-form-item>

                <a-form-item
                  v-if="submitForm.platform === 'gov'"
                  label="税务平台账号"
                  name="accountId"
                  required
                >
                  <a-select
                    v-model:value="submitForm.accountId"
                    placeholder="请选择申报账号"
                  >
                    <a-select-option v-for="account in taxAccounts" :key="account.id" :value="account.id">
                      {{ account.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </template>

              <a-form-item label="申报备注" name="notes">
                <a-textarea
                  v-model:value="submitForm.notes"
                  placeholder="请输入申报相关备注信息"
                  :rows="4"
                />
              </a-form-item>

              <a-form-item v-if="submitForm.submitType === 'manual'" label="申报凭证" name="proofFile">
                <a-upload
                  v-model:file-list="fileList"
                  :before-upload="beforeUpload"
                  @remove="handleRemove"
                >
                  <a-button>
                    <UploadOutlined />
                    上传凭证
                  </a-button>
                  <div class="upload-hint">
                    上传申报回执单或其他申报凭证
                  </div>
                </a-upload>
              </a-form-item>

              <a-divider />

              <a-form-item>
                <a-button
                  type="primary"
                  block
                  :loading="submitting"
                  :disabled="!canSubmit"
                  @click="submitDeclaration"
                >
                  提交申报
                </a-button>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 操作日志卡片 -->
          <a-card title="操作日志" class="log-card">
            <a-timeline>
              <a-timeline-item v-for="(log, index) in operationLogs" :key="index">
                <p class="log-title">
                  {{ log.action }}
                </p>
                <p class="log-time">
                  {{ log.time }}
                </p>
                <p v-if="log.user" class="log-user">
                  {{ log.user }}
                </p>
              </a-timeline-item>
            </a-timeline>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 提交确认对话框 -->
    <a-modal
      v-model:visible="confirmModalVisible"
      title="确认提交申报"
      :confirm-loading="submitting"
      @ok="handleConfirmSubmit"
      @cancel="confirmModalVisible = false"
    >
      <p>您确定要提交此申报吗？提交后状态将变更为"已申报"，且数据将不能再被修改。</p>
      <p>申报税额：{{ formatCurrency(getTotalTaxAmount()) }}</p>
      <p v-if="validationIssues.length > 0" class="warning-text">
        <ExclamationCircleOutlined /> 注意：当前申报存在 {{ validationIssues.length }} 个验证问题，建议解决后再提交。
      </p>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  getDeclarationDetail,
  validateDeclaration as apiValidateDeclaration,
  submitDeclaration as apiSubmitDeclaration
} from '@/api/declaration'
import PageHeader from '@/components/common/PageHeader.vue'
import {
  ArrowLeftOutlined,
  UploadOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'DeclarationSubmit',
  components: {
    PageHeader,
    ArrowLeftOutlined,
    UploadOutlined,
    ExclamationCircleOutlined
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const submitFormRef = ref(null)
    const validating = ref(false)
    const submitting = ref(false)
    const validationStatus = ref('') // 'success', 'warning', 'error'
    const validationResult = reactive({
      title: '',
      message: ''
    })
    const validationIssues = ref([])
    const declaration = ref({})
    const fileList = ref([])
    const confirmModalVisible = ref(false)
    const operationLogs = ref([])
    const taxAccounts = ref([])

    const breadcrumbs = [
      { title: '首页', path: '/dashboard' },
      { title: '申报管理', path: '/declaration/list' },
      { title: '提交申报' }
    ]

    // 提交表单数据
    const submitForm = reactive({
      submitType: 'digital',
      platform: 'gov',
      accountId: undefined,
      notes: '',
      proofFile: null
    })

    // 是否可以提交的计算属性
    const canSubmit = computed(() => {
      if (submitForm.submitType === 'digital') {
        if (submitForm.platform === 'gov' && !submitForm.accountId) {
          return false
        }
      } else if (submitForm.submitType === 'manual') {
        if (fileList.value.length === 0) {
          return false
        }
      }

      // 必须已经验证过
      return validationStatus.value !== ''
    })

    // 获取申报详情
    const fetchDeclarationDetail = async () => {
      try {
        const response = await getDeclarationDetail(route.params.id)
        if (response.code === 200) {
          declaration.value = response.data

          // 添加初始操作日志
          operationLogs.value = [
            {
              action: '创建申报',
              time: declaration.value.createTime,
              user: declaration.value.createUser
            },
            {
              action: '开始提交申报',
              time: new Date().toLocaleString(),
              user: '当前用户'
            }
          ]
        } else {
          message.error(response.message || '获取申报详情失败')
        }
      } catch (error) {
        console.error('获取申报详情失败:', error)
        message.error('获取申报详情失败，请稍后重试')
      }
    }

    // 获取税务账号
    const fetchTaxAccounts = async () => {
      try {
        // TODO: 实现真实的税务账号API调用
        // const response = await getTaxAccounts()
        // if (response.code === 200) {
        //   taxAccounts.value = response.data
        // }

        // 暂时使用模拟数据，实际应该调用真实API
        taxAccounts.value = [
          { id: '1', name: '国家税务总局电子申报平台账号' },
          { id: '2', name: '地方税务局电子申报平台账号' }
        ]
      } catch (error) {
        console.error('获取税务账号失败:', error)
        message.error('获取税务账号失败')
      }
    }

    // 验证申报
    const validateDeclaration = async () => {
      validating.value = true
      validationIssues.value = []

      try {
        const response = await apiValidateDeclaration({
          declarationId: route.params.id
        })

        if (response.code === 200) {
          const result = response.data

          if (result.valid) {
            validationStatus.value = 'success'
            validationResult.title = '验证通过'
            validationResult.message = '申报数据验证通过，可以提交申报'
          } else if (result.issues && result.issues.some(i => i.level === 'error')) {
            validationStatus.value = 'error'
            validationResult.title = '验证失败'
            validationResult.message = '申报数据存在严重问题，请修正后再提交'
            validationIssues.value = result.issues
          } else if (result.issues && result.issues.length > 0) {
            validationStatus.value = 'warning'
            validationResult.title = '存在警告'
            validationResult.message = '申报数据存在一些警告，但可以继续提交'
            validationIssues.value = result.issues
          }

          // 添加操作日志
          operationLogs.value.push({
            action: `申报验证${validationStatus.value === 'success' ? '通过' : '完成'}`,
            time: new Date().toLocaleString(),
            user: '当前用户'
          })
        } else {
          message.error(response.message || '验证申报失败')
        }
      } catch (error) {
        console.error('验证申报失败:', error)
        message.error('验证申报失败，请稍后重试')

        // 模拟验证结果
        setTimeout(() => {
          validationStatus.value = 'success'
          validationResult.title = '验证通过'
          validationResult.message = '申报数据验证通过，可以提交申报'

          // 添加操作日志
          operationLogs.value.push({
            action: '申报验证通过',
            time: new Date().toLocaleString(),
            user: '当前用户'
          })
        }, 1000)
      } finally {
        validating.value = false
      }
    }

    // 上传前检查
    const beforeUpload = (file) => {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        message.error('文件大小不能超过10MB')
        return false
      }

      fileList.value = [file]
      return false
    }

    // 移除文件
    const handleRemove = () => {
      fileList.value = []
    }

    // 提交申报
    const submitDeclaration = () => {
      if (!canSubmit.value) {
        if (validationStatus.value === '') {
          message.warn('请先进行申报验证')
        } else if (submitForm.submitType === 'digital' && submitForm.platform === 'gov' && !submitForm.accountId) {
          message.warn('请选择申报账号')
        } else if (submitForm.submitType === 'manual' && fileList.value.length === 0) {
          message.warn('请上传申报凭证')
        }
        return
      }

      // 显示确认对话框
      confirmModalVisible.value = true
    }

    // 确认提交
    const handleConfirmSubmit = async () => {
      submitting.value = true

      try {
        // 准备提交数据
        const formData = new FormData()
        formData.append('declarationId', route.params.id)
        formData.append('submitType', submitForm.submitType)

        if (submitForm.submitType === 'digital') {
          formData.append('platform', submitForm.platform)
          if (submitForm.platform === 'gov') {
            formData.append('accountId', submitForm.accountId)
          }
        } else if (submitForm.submitType === 'manual' && fileList.value.length > 0) {
          formData.append('proofFile', fileList.value[0])
        }

        if (submitForm.notes) {
          formData.append('notes', submitForm.notes)
        }

        const response = await apiSubmitDeclaration(route.params.id, formData)

        if (response.code === 200) {
          message.success('申报提交成功')

          // 添加操作日志
          operationLogs.value.push({
            action: '申报提交成功',
            time: new Date().toLocaleString(),
            user: '当前用户'
          })

          // 延迟跳转
          setTimeout(() => {
            router.push(`/declaration/detail/${route.params.id}`)
          }, 1500)
        } else {
          message.error(response.message || '申报提交失败')
        }
      } catch (error) {
        console.error('申报提交失败:', error)
        message.error('申报提交失败，请稍后重试')

        // 模拟成功提交
        setTimeout(() => {
          message.success('申报提交成功')

          // 添加操作日志
          operationLogs.value.push({
            action: '申报提交成功',
            time: new Date().toLocaleString(),
            user: '当前用户'
          })

          // 延迟跳转
          setTimeout(() => {
            router.push(`/declaration/detail/${route.params.id}`)
          }, 1500)
        }, 1000)
      } finally {
        submitting.value = false
        confirmModalVisible.value = false
      }
    }

    // 格式化货币
    const formatCurrency = (value) => {
      if (value === undefined || value === null) return '¥0.00'
      return `¥${Number(value).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })}`
    }

    // 计算总税额
    const getTotalTaxAmount = () => {
      if (!declaration.value || !declaration.value.taxData) return 0

      const taxData = declaration.value.taxData

      if (declaration.value.taxTypeId === 'vat') {
        return taxData.taxAmount || 0
      } else if (declaration.value.taxTypeId === 'income') {
        return taxData.incomeTaxAmount || 0
      } else {
        return 0
      }
    }

    // 格式化申报期间
    const formatPeriod = (period) => {
      if (!period) return '-'

      // 期间格式：YYYY-MM
      const match = period.match(/^(\d{4})-(\d{2})$/)
      if (match) {
        return `${match[1]}年${match[2]}月`
      }

      return period
    }

    // 获取状态颜色
    const getStatusColor = (status) => {
      const colors = {
        draft: 'default',
        pending: 'warning',
        submitted: 'processing',
        confirmed: 'success',
        rejected: 'error'
      }
      return colors[status] || 'default'
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        draft: '草稿',
        pending: '待申报',
        submitted: '已申报',
        confirmed: '已确认',
        rejected: '已驳回'
      }
      return texts[status] || '未知'
    }

    // 返回上一页
    const goBack = () => {
      router.go(-1)
    }

    onMounted(() => {
      fetchDeclarationDetail()
      fetchTaxAccounts()
    })

    return {
      submitFormRef,
      validating,
      submitting,
      validationStatus,
      validationResult,
      validationIssues,
      declaration,
      submitForm,
      fileList,
      confirmModalVisible,
      operationLogs,
      breadcrumbs,
      taxAccounts,
      canSubmit,
      validateDeclaration,
      beforeUpload,
      handleRemove,
      submitDeclaration,
      handleConfirmSubmit,
      formatCurrency,
      getTotalTaxAmount,
      formatPeriod,
      getStatusColor,
      getStatusText,
      goBack
    }
  }
})
</script>

<style scoped>
.declaration-submit-container {
  background: #f0f2f5;
  min-height: 100vh;
}

.content {
  padding: 0 24px 24px;
}

.preview-card,
.validation-card,
.submit-card,
.log-card {
  margin-bottom: 16px;
}

.tax-amount-summary {
  margin-top: 16px;
}

.validation-issues {
  margin-top: 16px;
}

.upload-hint {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 8px;
}

.log-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.log-time {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 2px;
}

.log-user {
  font-size: 12px;
  color: #8c8c8c;
}

.warning-text {
  color: #ff4d4f;
}

:deep(.ant-descriptions-view) {
  border: 1px solid #f0f0f0;
}

:deep(.ant-descriptions-item-label) {
  background-color: #fafafa;
  font-weight: 500;
}
</style>
