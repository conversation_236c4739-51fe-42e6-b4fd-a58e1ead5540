<template>
  <div class="declaration-calendar-container">
    <PageHeader
      title="申报日历"
      description="查看企业申报时间安排、截止日期和申报状态"
      :breadcrumbs="breadcrumbs"
    >
      <template #actions>
        <a-space>
          <a-button @click="handleList">
            <UnorderedListOutlined />
            列表视图
          </a-button>
          <a-button @click="handleRefresh">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <div class="content">
      <a-card class="calendar-card" :bordered="false">
        <template #title>
          <div class="calendar-header">
            <div class="calendar-title">
              <a-typography-title :level="4" style="margin-bottom: 0">
                {{ currentTitle }}
              </a-typography-title>
            </div>
            <div class="calendar-actions">
              <a-space>
                <a-button @click="prevMonth">
                  <LeftOutlined />
                </a-button>
                <a-button type="primary" @click="goToday">
                  今天
                </a-button>
                <a-button @click="nextMonth">
                  <RightOutlined />
                </a-button>
              </a-space>
            </div>
          </div>
        </template>

        <!-- 简单的日历网格 -->
        <div v-loading="loading" class="simple-calendar">
          <!-- 星期标题 -->
          <div class="calendar-header-row">
            <div v-for="day in weekDays" :key="day" class="calendar-header-cell">
              {{ day }}
            </div>
          </div>

          <!-- 日期网格 -->
          <div class="calendar-body">
            <div
              v-for="week in calendarWeeks"
              :key="week.weekNumber"
              class="calendar-week"
            >
              <div
                v-for="day in week.days"
                :key="day.dateString"
                :class="[
                  'calendar-day',
                  {
                    'other-month': !day.isCurrentMonth,
                    'today': day.isToday,
                    'has-events': day.events.length > 0,
                    'urgent': getUrgencyLevel(day) === 'urgent',
                    'warning': getUrgencyLevel(day) === 'warning',
                    'normal': getUrgencyLevel(day) === 'normal'
                  }
                ]"
                @click="selectDate(day)"
              >
                <div class="day-number">
                  {{ day.date }}
                </div>
                <div class="day-events">
                  <div
                    v-for="event in day.events.slice(0, 3)"
                    :key="event.id"
                    :class="['event-item', `event-${event.status}`]"
                  >
                    {{ event.title }}
                  </div>
                  <div v-if="day.events.length > 3" class="more-events">
                    +{{ day.events.length - 3 }} 更多
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 事件详情抽屉 -->
    <a-drawer
      v-model:visible="drawerVisible"
      :title="drawerTitle"
      placement="right"
      width="400"
    >
      <div v-if="selectedEvents.length === 0" class="empty-state">
        <a-empty description="当天没有申报任务" />
      </div>
      <div v-else class="event-list">
        <a-card
          v-for="event in selectedEvents"
          :key="event.id"
          class="event-card"
          size="small"
          :bordered="true"
        >
          <template #title>
            <div class="event-header">
              <a-tag :color="getStatusColor(event.status)">
                {{ getStatusText(event.status) }}
              </a-tag>
              {{ event.title }}
            </div>
          </template>
          <div class="event-content">
            <p><strong>企业:</strong> {{ event.enterpriseName }}</p>
            <p><strong>税种:</strong> {{ event.taxTypeName }}</p>
            <p><strong>申报期间:</strong> {{ event.period }}</p>
            <p><strong>截止时间:</strong> {{ formatDate(event.deadline) }}</p>
          </div>
          <template #actions>
            <a @click="viewDeclaration(event)">查看详情</a>
            <a v-if="event.status === 'draft'" @click="submitDeclaration(event)">提交申报</a>
          </template>
        </a-card>
      </div>
    </a-drawer>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useDeclarationStore } from '@/store/declaration'
import {
  UnorderedListOutlined,
  ReloadOutlined,
  LeftOutlined,
  RightOutlined
} from '@ant-design/icons-vue'

import PageHeader from '@/components/common/PageHeader.vue'

export default defineComponent({
  name: 'DeclarationCalendarSimple',
  components: {
    PageHeader,
    UnorderedListOutlined,
    ReloadOutlined,
    LeftOutlined,
    RightOutlined
  },
  setup () {
    const router = useRouter()
    const declarationStore = useDeclarationStore()

    // 当前日期状态
    const currentDate = ref(new Date())
    const currentYear = computed(() => currentDate.value.getFullYear())
    const currentMonth = computed(() => currentDate.value.getMonth())

    // 申报数据
    const drawerVisible = ref(false)
    const selectedEvents = ref([])
    const drawerTitle = ref('')

    const weekDays = ['日', '一', '二', '三', '四', '五', '六']

    const breadcrumbs = [
      { title: '首页', path: '/dashboard' },
      { title: '申报管理', path: '/declaration/list' },
      { title: '申报日历' }
    ]

    // 当前标题
    const currentTitle = computed(() => {
      return `${currentYear.value}年${currentMonth.value + 1}月`
    })

    // 获取申报数据
    const fetchDeclarations = async () => {
      try {
        // 获取当前月份的申报数据
        const startDate = new Date(currentYear.value, currentMonth.value, 1)
        const endDate = new Date(currentYear.value, currentMonth.value + 1, 0)

        await declarationStore.fetchDeclarations({
          start_date: startDate.toISOString().split('T')[0],
          end_date: endDate.toISOString().split('T')[0]
        })
      } catch (error) {
        console.error('获取申报数据失败:', error)
        message.error('获取申报数据失败')
      }
    }

    // 生成日历周数据
    const calendarWeeks = computed(() => {
      const weeks = []
      const year = currentYear.value
      const month = currentMonth.value

      // 获取当月第一天和最后一天
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)

      // 获取第一周的开始日期（可能是上个月的日期）
      const startDate = new Date(firstDay)
      startDate.setDate(startDate.getDate() - firstDay.getDay())

      const currentWeekStart = new Date(startDate)
      let weekNumber = 0

      while (currentWeekStart <= lastDay || weekNumber < 6) {
        const week = {
          weekNumber: weekNumber++,
          days: []
        }

        for (let i = 0; i < 7; i++) {
          const date = new Date(currentWeekStart)
          date.setDate(date.getDate() + i)

          const isCurrentMonth = date.getMonth() === month
          const isToday = isDateToday(date)
          const dateString = formatDateString(date)
          const events = getEventsForDate(date)

          week.days.push({
            date: date.getDate(),
            dateString,
            fullDate: new Date(date),
            isCurrentMonth,
            isToday,
            events
          })
        }

        weeks.push(week)
        currentWeekStart.setDate(currentWeekStart.getDate() + 7)

        // 如果已经超过了当月最后一天，且已经有足够的周数，就停止
        if (currentWeekStart > lastDay && weekNumber >= 5) {
          break
        }
      }

      return weeks
    })

    // 工具函数
    const isDateToday = (date) => {
      const today = new Date()
      return date.toDateString() === today.toDateString()
    }

    const formatDateString = (date) => {
      return date.toISOString().split('T')[0]
    }

    const getEventsForDate = (date) => {
      const dateString = formatDateString(date)
      return declarationStore.declarations.filter(declaration => {
        const dueDate = new Date(declaration.due_date)
        return formatDateString(dueDate) === dateString
      }).map(declaration => ({
        id: declaration.id,
        title: `${declaration.tax_type?.name || '税务申报'}`,
        status: declaration.status,
        enterprise: declaration.enterprise?.name,
        amount: declaration.amount,
        declaration
      }))
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    // 获取紧急程度
    const getUrgencyLevel = (day) => {
      if (!day.events || day.events.length === 0) return null

      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const dayDate = new Date(day.fullDate.getFullYear(), day.fullDate.getMonth(), day.fullDate.getDate())

      // 检查是否有逾期的申报
      const hasOverdue = day.events.some(event => {
        return dayDate < today && event.status === 'draft'
      })

      if (hasOverdue) return 'urgent'

      // 检查是否有即将到期的申报（3天内）
      const threeDaysLater = new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000)
      const hasUpcoming = day.events.some(event => {
        return dayDate >= today && dayDate <= threeDaysLater && event.status === 'draft'
      })

      if (hasUpcoming) return 'warning'

      // 检查是否有正常的申报
      const hasNormal = day.events.some(event => {
        return event.status === 'submitted' || event.status === 'approved' || event.status === 'paid'
      })

      if (hasNormal) return 'normal'

      return null
    }

    // 事件处理
    const selectDate = (day) => {
      if (day.events.length > 0) {
        selectedEvents.value = day.events
        drawerTitle.value = `${day.fullDate.getFullYear()}年${day.fullDate.getMonth() + 1}月${day.fullDate.getDate()}日 申报任务`
        drawerVisible.value = true
      }
    }

    const prevMonth = () => {
      const newDate = new Date(currentDate.value)
      newDate.setMonth(newDate.getMonth() - 1)
      currentDate.value = newDate
      fetchDeclarations()
    }

    const nextMonth = () => {
      const newDate = new Date(currentDate.value)
      newDate.setMonth(newDate.getMonth() + 1)
      currentDate.value = newDate
      fetchDeclarations()
    }

    const goToday = () => {
      currentDate.value = new Date()
      fetchDeclarations()
    }

    const handleRefresh = () => {
      fetchDeclarations()
    }

    const handleList = () => {
      router.push('/declaration/list')
    }

    const viewDeclaration = (event) => {
      router.push(`/declaration/detail/${event.id}`)
    }

    const submitDeclaration = (event) => {
      router.push(`/declaration/submit/${event.id}`)
    }

    const getStatusColor = (status) => {
      const colors = {
        draft: 'default',
        pending: 'warning',
        submitted: 'processing',
        confirmed: 'success',
        rejected: 'error'
      }
      return colors[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        draft: '草稿',
        pending: '待申报',
        submitted: '已提交',
        confirmed: '已确认',
        rejected: '已驳回'
      }
      return texts[status] || '未知'
    }

    // 组件挂载时获取数据
    onMounted(() => {
      fetchDeclarations()
    })

    return {
      loading: computed(() => declarationStore.loading),
      currentTitle,
      calendarWeeks,
      weekDays,
      breadcrumbs,
      drawerVisible,
      drawerTitle,
      selectedEvents,
      selectDate,
      prevMonth,
      nextMonth,
      goToday,
      handleRefresh,
      handleList,
      viewDeclaration,
      submitDeclaration,
      getStatusColor: declarationStore.getStatusColor,
      getStatusText: declarationStore.getStatusLabel,
      formatDate,
      getUrgencyLevel
    }
  }
})
</script>

<style scoped>
.declaration-calendar-container {
  background: #f0f2f5;
  min-height: 100vh;
}

.content {
  padding: 0 24px 24px;
}

.calendar-card {
  margin-bottom: 16px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.calendar-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.simple-calendar {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.calendar-header-row {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.calendar-header-cell {
  padding: 12px 8px;
  text-align: center;
  font-weight: 500;
  color: #666;
  border-right: 1px solid #f0f0f0;
}

.calendar-header-cell:last-child {
  border-right: none;
}

.calendar-body {
  display: flex;
  flex-direction: column;
}

.calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  border-bottom: 1px solid #f0f0f0;
}

.calendar-week:last-child {
  border-bottom: none;
}

.calendar-day {
  min-height: 120px;
  padding: 8px;
  border-right: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  flex-direction: column;
}

.calendar-day:last-child {
  border-right: none;
}

.calendar-day:hover {
  background-color: #f5f5f5;
}

.calendar-day.other-month {
  background-color: #fafafa;
  color: #ccc;
}

.calendar-day.today {
  background-color: #e6f7ff;
}

.calendar-day.today .day-number {
  color: #1890ff;
  font-weight: bold;
}

.calendar-day.has-events {
  background-color: #fff7e6;
}

.day-number {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.day-events {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.event-item {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

/* 申报状态样式 */
.event-draft {
  background-color: #f6f6f6;
  color: #666;
  border: 1px solid #d9d9d9;
}

.event-submitted {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.event-approved {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.event-rejected {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffb3b3;
}

.event-paid {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.event-overdue {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffb3b3;
  animation: blink 1s infinite;
}

/* 紧急程度指示器 */
.calendar-day.urgent {
  border-left: 4px solid #ff4d4f;
}

.calendar-day.warning {
  border-left: 4px solid #fa8c16;
}

.calendar-day.normal {
  border-left: 4px solid #52c41a;
}

/* 闪烁动画用于逾期申报 */
@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.7; }
}

.more-events {
  font-size: 11px;
  color: #666;
  text-align: center;
  margin-top: 2px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.event-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.event-card {
  margin-bottom: 16px;
}

.event-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.event-content p {
  margin: 4px 0;
  font-size: 14px;
}

@media (max-width: 768px) {
  .calendar-day {
    min-height: 80px;
    padding: 4px;
  }

  .day-number {
    font-size: 14px;
  }

  .event-item {
    font-size: 10px;
    padding: 1px 4px;
  }
}
</style>
