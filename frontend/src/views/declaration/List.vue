<template>
  <div class="declaration-list-container">
    <PageHeader
      title="申报管理"
      description="管理企业税务申报数据、跟踪申报状态、查看历史记录"
      :breadcrumbs="breadcrumbs"
    >
      <template #actions>
        <a-space>
          <a-button type="primary" @click="handleCreate">
            <PlusOutlined />
            新建申报
          </a-button>
          <a-button @click="handleBatchGenerate">
            <CalendarOutlined />
            一键生成申报
          </a-button>
          <a-button @click="handleRefresh">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <div class="content">
      <!-- 搜索和筛选 -->
      <a-card class="search-card" :bordered="false">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="申报期间">
            <a-range-picker
              v-model:value="dateRange"
              :allow-clear="true"
              format="YYYY-MM"
              :value-format="'YYYY-MM'"
              :placeholder="['开始月份', '结束月份']"
              picker="month"
              style="width: 240px"
            />
          </a-form-item>
          <a-form-item label="申报状态">
            <a-select
              v-model:value="searchForm.status"
              style="width: 120px"
              placeholder="全部状态"
              allow-clear
            >
              <a-select-option value="draft">
                草稿
              </a-select-option>
              <a-select-option value="submitted">
                已提交
              </a-select-option>
              <a-select-option value="approved">
                已批准
              </a-select-option>
              <a-select-option value="rejected">
                已拒绝
              </a-select-option>
              <a-select-option value="paid">
                已缴费
              </a-select-option>
              <a-select-option value="overdue">
                已逾期
              </a-select-option>
              <a-select-option value="confirmed">
                已确认
              </a-select-option>
              <a-select-option value="rejected">
                已驳回
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="税种">
            <a-select
              v-model:value="searchForm.taxType"
              style="width: 160px"
              placeholder="全部税种"
              allow-clear
            >
              <a-select-option value="vat">
                增值税
              </a-select-option>
              <a-select-option value="income">
                企业所得税
              </a-select-option>
              <a-select-option value="individual">
                个人所得税
              </a-select-option>
              <a-select-option value="property">
                房产税
              </a-select-option>
              <a-select-option value="other">
                其他税种
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="关键词">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="输入申报号或企业名称"
              style="width: 200px"
              allow-clear
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 申报统计卡片 -->
      <a-row :gutter="16" class="stats-row">
        <a-col :span="6">
          <a-card class="stats-card">
            <a-statistic
              title="本月待申报"
              :value="stats.pendingCount"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <FileExclamationOutlined />
              </template>
            </a-statistic>
            <div class="stats-footer">
              <a @click="filterByStatus('pending')">查看详情</a>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stats-card">
            <a-statistic
              title="本月已申报"
              :value="stats.submittedCount"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <FileDoneOutlined />
              </template>
            </a-statistic>
            <div class="stats-footer">
              <a @click="filterByStatus('submitted')">查看详情</a>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stats-card">
            <a-statistic
              title="异常申报"
              :value="stats.rejectedCount"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <FileExclamationOutlined />
              </template>
            </a-statistic>
            <div class="stats-footer">
              <a @click="filterByStatus('rejected')">查看详情</a>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stats-card">
            <a-statistic
              title="总税金额"
              :value="stats.totalTaxAmount"
              :value-style="{ color: '#722ed1' }"
              :precision="2"
              prefix="¥"
            />
            <div class="stats-footer">
              <a @click="showTaxDetails">查看详情</a>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 发票数据集成 -->
      <a-card
        v-if="showInvoiceIntegration"
        class="invoice-integration-card"
        :bordered="false"
        style="margin-bottom: 16px;"
      >
        <template #title>
          <div class="card-title">
            <span>发票数据集成</span>
            <a-button
              type="text"
              size="small"
              @click="toggleInvoiceIntegration"
            >
              收起
            </a-button>
          </div>
        </template>
        <InvoiceIntegration
          :enterprise-id="selectedEnterpriseId"
          :declaration-period="selectedDeclarationPeriod"
          @invoice-selected="handleInvoiceSelected"
        />
      </a-card>

      <!-- 申报列表 -->
      <a-card class="table-card" :bordered="false">
        <template #title>
          <div class="table-title">
            <div class="title-text">
              申报清单
            </div>
            <div class="title-actions">
              <a-space>
                <a-button
                  v-if="!showInvoiceIntegration"
                  type="primary"
                  ghost
                  size="small"
                  @click="toggleInvoiceIntegration"
                >
                  <template #icon>
                    <FileTextOutlined />
                  </template>
                  发票集成
                </a-button>
                <a-button-group size="small">
                  <a-button type="primary" ghost @click="changeView('table')">
                    <UnorderedListOutlined />
                  </a-button>
                  <a-button ghost @click="changeView('calendar')">
                    <CalendarOutlined />
                  </a-button>
                </a-button-group>
              </a-space>
            </div>
          </div>
        </template>
        <a-table
          :loading="loading"
          :columns="columns"
          :data-source="declarations"
          :row-key="record => record.id"
          :pagination="pagination"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'period'">
              {{ formatPeriod(record.period) }}
            </template>
            <template v-if="column.key === 'deadline'">
              <template v-if="isDeadlineNear(record.deadline)">
                <a-badge status="error" />
              </template>
              {{ record.deadline }}
            </template>
            <template v-if="column.key === 'amount'">
              ¥{{ formatNumber(record.totalAmount) }}
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="handleDetail(record)">
                  查看
                </a-button>
                <a-button
                  v-if="record.status === 'draft'"
                  type="link"
                  size="small"
                  @click="handleEdit(record)"
                >
                  编辑
                </a-button>
                <a-button
                  v-if="record.status === 'draft' || record.status === 'pending'"
                  type="link"
                  size="small"
                  @click="handleSubmit(record)"
                >
                  提交
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  @click="handleHistory(record)"
                >
                  历史
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多 <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="handlePreview(record)">
                        预览申报表
                      </a-menu-item>
                      <a-menu-item @click="handleDownload(record)">
                        下载申报表
                      </a-menu-item>
                      <a-menu-item
                        v-if="record.status === 'draft'"
                        @click="handleDelete(record)"
                      >
                        <span style="color: #ff4d4f">删除申报</span>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
          <template #footer>
            <div class="table-footer">
              <span>共 {{ pagination.total }} 项</span>
            </div>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 一键生成申报表单 -->
    <a-modal
      v-model:visible="batchModal.visible"
      title="一键生成申报"
      :confirm-loading="batchModal.loading"
      @ok="confirmBatchGenerate"
      @cancel="cancelBatchGenerate"
    >
      <a-form :model="batchModal.form" layout="vertical">
        <a-form-item label="申报期间" required>
          <a-month-picker
            v-model:value="batchModal.form.period"
            style="width: 100%"
            format="YYYY-MM"
            placeholder="选择申报期间"
          />
        </a-form-item>
        <a-form-item label="税种" required>
          <a-select
            v-model:value="batchModal.form.taxTypes"
            mode="multiple"
            style="width: 100%"
            placeholder="选择税种"
          >
            <a-select-option value="vat">
              增值税
            </a-select-option>
            <a-select-option value="income">
              企业所得税
            </a-select-option>
            <a-select-option value="individual">
              个人所得税
            </a-select-option>
            <a-select-option value="property">
              房产税
            </a-select-option>
            <a-select-option value="other">
              其他税种
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="企业" required>
          <a-select
            v-model:value="batchModal.form.enterprises"
            mode="multiple"
            style="width: 100%"
            placeholder="选择企业"
            :options="enterpriseOptions"
          />
        </a-form-item>
        <a-form-item label="包含已生成申报">
          <a-switch v-model:checked="batchModal.form.includeExisting" />
          <div class="help-text">
            打开后将覆盖已生成的申报记录
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 申报表单弹窗 -->
    <a-modal
      v-model:visible="formModal.visible"
      :title="formModal.title"
      width="900px"
      :footer="null"
      :destroy-on-close="true"
    >
      <DeclarationForm
        :declaration="formModal.declaration"
        @success="handleFormSuccess"
        @cancel="handleFormCancel"
      />
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useDeclarationStore } from '@/store/declaration'
import {
  getDeclarationList,
  deleteDeclaration,
  generateDeclarations
} from '@/api/declaration'
import { getEnterprises } from '@/api/enterprise'
import { getTaxTypeList } from '@/api/taxType'
import PageHeader from '@/components/common/PageHeader.vue'
import DeclarationForm from '@/components/business/DeclarationForm.vue'
import InvoiceIntegration from '@/components/business/InvoiceIntegration.vue'
import {
  PlusOutlined,
  CalendarOutlined,
  ReloadOutlined,
  SearchOutlined,
  FileExclamationOutlined,
  FileDoneOutlined,
  UnorderedListOutlined,
  DownOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'DeclarationList',
  components: {
    PageHeader,
    DeclarationForm,
    InvoiceIntegration,
    PlusOutlined,
    CalendarOutlined,
    ReloadOutlined,
    SearchOutlined,
    FileExclamationOutlined,
    FileDoneOutlined,
    UnorderedListOutlined,
    DownOutlined,
    FileTextOutlined
  },
  setup () {
    const router = useRouter()
    const declarationStore = useDeclarationStore()

    const dateRange = ref([])
    const searchForm = reactive({
      period: null,
      status: undefined,
      tax_type_id: undefined,
      enterprise_id: undefined,
      keyword: ''
    })

    const enterpriseOptions = ref([])
    const taxTypeOptions = ref([])
    const breadcrumbs = [
      { title: '首页', path: '/dashboard' },
      { title: '申报管理' }
    ]

    // 申报表单弹窗
    const formModal = reactive({
      visible: false,
      title: '新建申报',
      declaration: null
    })

    // 发票集成相关状态
    const showInvoiceIntegration = ref(false)
    const selectedEnterpriseId = ref('')
    const selectedDeclarationPeriod = ref('')

    // 统计数据 - 使用store中的计算属性
    const stats = computed(() => {
      const declarations = declarationStore.declarations
      return {
        draftCount: declarations.filter(d => d.status === 'draft').length,
        submittedCount: declarations.filter(d => d.status === 'submitted').length,
        approvedCount: declarations.filter(d => d.status === 'approved').length,
        rejectedCount: declarations.filter(d => d.status === 'rejected').length,
        paidCount: declarations.filter(d => d.status === 'paid').length,
        overdueCount: declarationStore.overdueDeclarations.length,
        upcomingCount: declarationStore.upcomingDeclarations.length,
        totalAmount: declarations.reduce((sum, d) => sum + (parseFloat(d.amount) || 0), 0)
      }
    })

    // 一键生成申报弹窗
    const batchModal = reactive({
      visible: false,
      loading: false,
      form: {
        period: null,
        taxTypes: [],
        enterprises: [],
        includeExisting: false
      }
    })

    // 表格列定义
    const columns = [
      {
        title: '申报编号',
        dataIndex: 'declarationNo',
        key: 'declarationNo',
        width: 180
      },
      {
        title: '企业名称',
        dataIndex: 'enterpriseName',
        key: 'enterpriseName',
        width: 200
      },
      {
        title: '税种',
        dataIndex: 'taxTypeName',
        key: 'taxType',
        width: 120
      },
      {
        title: '申报期间',
        dataIndex: 'period',
        key: 'period',
        width: 120
      },
      {
        title: '申报截止日',
        dataIndex: 'deadline',
        key: 'deadline',
        width: 120
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
      },
      {
        title: '税金金额',
        dataIndex: 'totalAmount',
        key: 'amount',
        width: 120
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        width: 160
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 220
      }
    ]

    // 获取申报列表
    const fetchDeclarations = async () => {
      try {
        const params = {
          page: declarationStore.pagination.current,
          pageSize: declarationStore.pagination.pageSize,
          status: searchForm.status,
          tax_type_id: searchForm.tax_type_id,
          enterprise_id: searchForm.enterprise_id,
          keyword: searchForm.keyword
        }

        if (dateRange.value && dateRange.value.length === 2) {
          params.start_date = dateRange.value[0]
          params.end_date = dateRange.value[1]
        }

        await declarationStore.fetchDeclarations(params)
      } catch (error) {
        console.error('获取申报列表失败:', error)
        message.error('获取申报列表失败')
      }
    }

    // 获取企业列表
    const fetchEnterprises = async () => {
      try {
        const response = await getEnterprises({ pageSize: 1000 })
        if (response.code === 200) {
          // 适配后端返回的数据结构
          const enterprises = response.data?.data || response.data?.items || response.data || []
          enterpriseOptions.value = enterprises.map(item => ({
            label: item.name,
            value: item.id
          }))
        }
      } catch (error) {
        console.error('获取企业列表失败:', error)
      }
    }

    // 获取税种列表
    const fetchTaxTypes = async () => {
      try {
        const response = await getTaxTypeList({ pageSize: 1000 })
        if (response.code === 200) {
          taxTypeOptions.value = response.data.items?.map(item => ({
            label: item.name,
            value: item.id
          })) || []
        }
      } catch (error) {
        console.error('获取税种列表失败:', error)
      }
    }

    // 处理表格分页、排序、筛选变化
    const handleTableChange = (pag) => {
      declarationStore.setPagination({
        current: pag.current,
        pageSize: pag.pageSize
      })
      fetchDeclarations()
    }

    // 搜索
    const handleSearch = () => {
      declarationStore.setPagination({ current: 1 })
      fetchDeclarations()
    }

    // 重置搜索条件
    const resetSearch = () => {
      searchForm.status = undefined
      searchForm.tax_type_id = undefined
      searchForm.enterprise_id = undefined
      searchForm.keyword = ''
      dateRange.value = []
      declarationStore.setPagination({ current: 1 })
      declarationStore.resetFilters()
      fetchDeclarations()
    }

    // 刷新数据
    const handleRefresh = () => {
      fetchDeclarations()
    }

    // 按状态筛选
    const filterByStatus = (status) => {
      searchForm.status = status
      pagination.current = 1
      fetchDeclarations()
    }

    // 显示税金详情
    const showTaxDetails = () => {
      message.info('税金详情功能开发中')
    }

    // 切换视图模式
    const changeView = (view) => {
      if (view === 'calendar') {
        router.push('/declaration/calendar')
      }
    }

    // 新建申报
    const handleCreate = () => {
      formModal.visible = true
      formModal.title = '新建申报'
      formModal.declaration = null
    }

    // 查看申报详情
    const handleDetail = (record) => {
      router.push(`/declaration/detail/${record.id}`)
    }

    // 编辑申报
    const handleEdit = (record) => {
      formModal.visible = true
      formModal.title = '编辑申报'
      formModal.declaration = record
    }

    // 提交申报
    const handleSubmit = async (record) => {
      try {
        await declarationStore.submitDeclarationData(record.id)
        message.success('提交申报成功')
        fetchDeclarations()
      } catch (error) {
        console.error('提交申报失败:', error)
      }
    }

    // 预览申报表
    const handlePreview = (record) => {
      router.push(`/declaration/preview/${record.id}`)
    }

    // 查看申报历史
    const handleHistory = (record) => {
      router.push(`/declaration/history/${record.id}`)
    }

    // 下载申报表
    const handleDownload = (record) => {
      message.info(`申报表 ${record.declarationNo} 下载功能开发中...`)
    }

    // 删除申报
    const handleDelete = async (record) => {
      try {
        await declarationStore.deleteDeclarationData(record.id)
        fetchDeclarations()
      } catch (error) {
        console.error('删除申报失败:', error)
      }
    }

    // 表单提交成功处理
    const handleFormSuccess = () => {
      formModal.visible = false
      fetchDeclarations()
    }

    // 表单取消处理
    const handleFormCancel = () => {
      formModal.visible = false
    }

    // 一键生成申报
    const handleBatchGenerate = () => {
      batchModal.visible = true
      batchModal.form.period = null
      batchModal.form.taxTypes = []
      batchModal.form.enterprises = []
      batchModal.form.includeExisting = false
      fetchEnterprises()
    }

    // 确认生成申报
    const confirmBatchGenerate = async () => {
      if (!batchModal.form.period) {
        message.error('请选择申报期间')
        return
      }

      if (batchModal.form.taxTypes.length === 0) {
        message.error('请选择至少一个税种')
        return
      }

      if (batchModal.form.enterprises.length === 0) {
        message.error('请选择至少一个企业')
        return
      }

      batchModal.loading = true
      try {
        const data = {
          period: batchModal.form.period.format('YYYY-MM'),
          taxTypes: batchModal.form.taxTypes,
          enterpriseIds: batchModal.form.enterprises,
          includeExisting: batchModal.form.includeExisting
        }

        const response = await generateDeclarations(data)
        if (response.code === 200) {
          message.success(`成功生成 ${response.data.count || 0} 条申报记录`)
          batchModal.visible = false
          fetchDeclarations()
        } else {
          message.error(response.message || '生成申报失败')
        }
      } catch (error) {
        console.error('生成申报失败:', error)
        message.error('生成申报失败，请稍后重试')
      } finally {
        batchModal.loading = false
      }
    }

    // 取消生成申报
    const cancelBatchGenerate = () => {
      batchModal.visible = false
    }

    // 格式化数字
    const formatNumber = (num) => {
      return Number(num).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }

    // 格式化申报期间
    const formatPeriod = (period) => {
      if (!period) return '-'

      // 期间格式：YYYY-MM
      const match = period.match(/^(\d{4})-(\d{2})$/)
      if (match) {
        return `${match[1]}年${match[2]}月`
      }

      return period
    }

    // 判断截止日期是否临近（7天内）
    const isDeadlineNear = (deadlineStr) => {
      if (!deadlineStr) return false

      const deadline = new Date(deadlineStr)
      const today = new Date()
      const diffDays = Math.ceil((deadline - today) / (1000 * 60 * 60 * 24))

      return diffDays >= 0 && diffDays <= 7
    }

    // 获取状态颜色
    const getStatusColor = (status) => {
      const colors = {
        draft: 'default',
        pending: 'warning',
        submitted: 'processing',
        confirmed: 'success',
        rejected: 'error'
      }
      return colors[status] || 'default'
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        draft: '草稿',
        pending: '待申报',
        submitted: '已申报',
        confirmed: '已确认',
        rejected: '已驳回'
      }
      return texts[status] || '未知'
    }

    // 发票集成相关方法
    const toggleInvoiceIntegration = () => {
      showInvoiceIntegration.value = !showInvoiceIntegration.value

      // 如果打开发票集成，设置默认的企业和期间
      if (showInvoiceIntegration.value) {
        // 从搜索表单中获取当前选择的企业
        selectedEnterpriseId.value = searchForm.enterprise_id || ''

        // 从日期范围中获取期间，或使用当前月份
        if (dateRange.value && dateRange.value.length > 0) {
          selectedDeclarationPeriod.value = dateRange.value[0]
        } else {
          const now = new Date()
          selectedDeclarationPeriod.value = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
        }
      }
    }

    const handleInvoiceSelected = (invoiceData) => {
      console.log('选择的发票数据:', invoiceData)
      message.success(`已选择 ${invoiceData.invoices.length} 张发票`)

      // 可以在这里处理发票数据，比如自动填充到申报表单中
      // 或者跳转到申报详情页面
    }

    onMounted(() => {
      fetchDeclarations()
      fetchEnterprises()
      fetchTaxTypes()
    })

    return {
      // Store数据
      loading: computed(() => declarationStore.loading),
      declarations: computed(() => declarationStore.declarations),
      pagination: computed(() => declarationStore.pagination),

      // 本地数据
      searchForm,
      dateRange,
      columns,
      stats,
      breadcrumbs,
      batchModal,
      formModal,
      enterpriseOptions,
      taxTypeOptions,

      // 发票集成相关
      showInvoiceIntegration,
      selectedEnterpriseId,
      selectedDeclarationPeriod,

      // 方法
      handleTableChange,
      handleSearch,
      resetSearch,
      handleRefresh,
      filterByStatus,
      showTaxDetails,
      changeView,
      handleCreate,
      handleDetail,
      handleEdit,
      handleSubmit,
      handlePreview,
      handleHistory,
      handleDownload,
      handleDelete,
      handleFormSuccess,
      handleFormCancel,
      handleBatchGenerate,
      confirmBatchGenerate,
      cancelBatchGenerate,
      formatNumber,
      formatPeriod,
      isDeadlineNear,
      getStatusColor: declarationStore.getStatusColor,
      getStatusText: declarationStore.getStatusLabel,

      // 发票集成方法
      toggleInvoiceIntegration,
      handleInvoiceSelected
    }
  }
})
</script>

<style scoped>
.declaration-list-container {
  background: #f0f2f5;
  min-height: 100vh;
}

.content {
  padding: 0 24px 24px;
}

.search-card {
  margin-bottom: 16px;
}

.stats-row {
  margin-bottom: 16px;
}

.stats-card {
  border-radius: 4px;
  overflow: hidden;
}

.stats-footer {
  margin-top: 12px;
  text-align: right;
  font-size: 12px;
}

.table-card {
  margin-bottom: 16px;
}

.invoice-integration-card {
  border: 1px solid #e8f4fd;
  background: #f6fbff;
}

.invoice-integration-card .ant-card-head {
  background: #e8f4fd;
  border-bottom: 1px solid #d4edda;
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.table-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
}

.table-footer {
  text-align: right;
  color: rgba(0, 0, 0, 0.45);
}

.help-text {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-card-head) {
  min-height: 48px;
}

:deep(.ant-card-head-title) {
  padding: 12px 0;
}
</style>
