<template>
  <div class="declaration-detail-container">
    <PageHeader
      title="申报详情"
      :description="`申报编号: ${declaration.declarationNo || '-'}`"
      :breadcrumbs="breadcrumbs"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
          <a-button
            v-if="declaration.status === 'draft'"
            type="primary"
            @click="handleEdit"
          >
            <EditOutlined />
            编辑
          </a-button>
          <a-button
            v-if="declaration.status === 'draft' || declaration.status === 'pending'"
            type="primary"
            @click="handleSubmit"
          >
            <CheckOutlined />
            提交申报
          </a-button>
          <a-dropdown>
            <a-button>
              <MoreOutlined />
              更多操作
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item key="preview" @click="handlePreview">
                  <EyeOutlined />
                  预览申报表
                </a-menu-item>
                <a-menu-item key="download" @click="handleDownload">
                  <DownloadOutlined />
                  下载申报表
                </a-menu-item>
                <a-menu-item key="print" @click="handlePrint">
                  <PrinterOutlined />
                  打印申报表
                </a-menu-item>
                <a-menu-item key="history" @click="handleHistory">
                  <HistoryOutlined />
                  查看历史
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </template>
    </PageHeader>

    <div class="content">
      <a-row :gutter="16">
        <a-col :span="16">
          <!-- 基本信息卡片 -->
          <a-card title="基本信息" class="detail-card">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="申报编号">
                {{ declaration.declarationNo || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="申报状态">
                <a-tag :color="getStatusColor(declaration.status)">
                  {{ getStatusText(declaration.status) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="企业名称">
                {{ declaration.enterpriseName || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="税种">
                {{ declaration.taxTypeName || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="申报期间">
                {{ formatPeriod(declaration.period) }}
              </a-descriptions-item>
              <a-descriptions-item label="截止日期">
                <a-badge
                  v-if="isDeadlineNear(declaration.deadline)"
                  status="error"
                />
                {{ declaration.deadline || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="创建时间" :span="2">
                {{ declaration.createTime || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="更新时间" :span="2">
                {{ declaration.updateTime || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="备注说明" :span="2">
                {{ declaration.description || '无' }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>

          <!-- 增值税信息卡片 -->
          <a-card v-if="declaration.taxTypeId === 'vat'" title="增值税申报信息" class="detail-card">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="销项税额">
                {{ formatCurrency(declaration.taxData?.outputTax) }}
              </a-descriptions-item>
              <a-descriptions-item label="进项税额">
                {{ formatCurrency(declaration.taxData?.inputTax) }}
              </a-descriptions-item>
              <a-descriptions-item label="上期留抵税额">
                {{ formatCurrency(declaration.taxData?.lastPeriodTax) }}
              </a-descriptions-item>
              <a-descriptions-item label="销售额">
                {{ formatCurrency(declaration.taxData?.salesAmount) }}
              </a-descriptions-item>
              <a-descriptions-item label="应纳税额">
                <span :class="{ 'highlight-amount': declaration.taxData?.taxAmount > 0 }">
                  {{ formatCurrency(declaration.taxData?.taxAmount) }}
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="本期留抵税额">
                <span :class="{ 'highlight-amount': declaration.taxData?.currentPeriodTax > 0 }">
                  {{ formatCurrency(declaration.taxData?.currentPeriodTax) }}
                </span>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>

          <!-- 企业所得税信息卡片 -->
          <a-card v-if="declaration.taxTypeId === 'income'" title="企业所得税申报信息" class="detail-card">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="收入总额">
                {{ formatCurrency(declaration.taxData?.totalIncome) }}
              </a-descriptions-item>
              <a-descriptions-item label="成本费用总额">
                {{ formatCurrency(declaration.taxData?.totalCost) }}
              </a-descriptions-item>
              <a-descriptions-item label="税前利润">
                {{ formatCurrency(declaration.taxData?.profitBeforeTax) }}
              </a-descriptions-item>
              <a-descriptions-item label="应纳税所得额">
                {{ formatCurrency(declaration.taxData?.taxableIncome) }}
              </a-descriptions-item>
              <a-descriptions-item label="税率">
                {{ declaration.taxData?.taxRate }}%
              </a-descriptions-item>
              <a-descriptions-item label="应纳税额">
                <span :class="{ 'highlight-amount': declaration.taxData?.incomeTaxAmount > 0 }">
                  {{ formatCurrency(declaration.taxData?.incomeTaxAmount) }}
                </span>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>

          <!-- 附件列表 -->
          <a-card title="附件资料" class="detail-card">
            <a-empty v-if="!attachments.length" description="暂无附件" />
            <a-list v-else :data-source="attachments" item-layout="horizontal">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <a @click="previewAttachment(item)">{{ item.name }}</a>
                    </template>
                    <template #description>
                      <span>{{ formatFileSize(item.size) }} | 上传时间: {{ item.uploadTime }}</span>
                    </template>
                    <template #avatar>
                      <FileOutlined />
                    </template>
                  </a-list-item-meta>
                  <template #actions>
                    <a key="preview" @click="previewAttachment(item)">预览</a>
                    <a key="download" @click="downloadAttachment(item)">下载</a>
                  </template>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>

        <a-col :span="8">
          <!-- 申报状态卡片 -->
          <a-card title="申报状态" class="detail-card status-card">
            <a-steps direction="vertical" :current="getCurrentStep()" size="small">
              <a-step
                title="创建申报"
                :description="declaration.createTime || '-'"
              />
              <a-step
                title="准备申报"
                :description="getStepDescription('pending')"
              />
              <a-step
                title="已提交申报"
                :description="getStepDescription('submitted')"
              />
              <a-step
                title="税务局确认"
                :description="getStepDescription('confirmed')"
              />
            </a-steps>
          </a-card>

          <!-- 申报日历卡片 -->
          <a-card v-if="deadline" title="申报截止日" class="detail-card deadline-card">
            <div class="deadline-content">
              <a-calendar
                v-model:value="calendarValue"
                :fullscreen="false"
                :disabled-date="disabledDate"
              >
                <template #dateFullCellRender="{ current }">
                  <div
                    :class="[
                      'ant-picker-cell-inner',
                      (current.month() === calendarValue.month() && current.date() === deadline.date()) ? 'deadline-date' : ''
                    ]"
                  >
                    {{ current.date() }}
                  </div>
                </template>
              </a-calendar>
              <div class="deadline-info">
                <p>
                  <ClockCircleOutlined />
                  <span>距离申报截止还有 <b>{{ daysRemaining }}</b> 天</span>
                </p>
              </div>
            </div>
          </a-card>

          <!-- 操作记录卡片 -->
          <a-card title="操作历史" class="detail-card">
            <a-timeline>
              <a-timeline-item v-for="(item, index) in operationHistory" :key="index" :color="getTimelineColor(item.action)">
                <p class="timeline-title">
                  {{ item.actionName }}
                </p>
                <p class="timeline-content">
                  {{ item.content }}
                </p>
                <p class="timeline-time">
                  {{ item.operateTime }}
                </p>
                <p class="timeline-user">
                  操作人: {{ item.operateUser }}
                </p>
              </a-timeline-item>
            </a-timeline>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 附件预览模态框 -->
    <a-modal
      v-model:visible="previewVisible"
      title="附件预览"
      :footer="null"
      width="800px"
    >
      <img v-if="previewImage" :src="previewImage" style="width: 100%">
      <p v-else>
        此文件类型不支持在线预览
      </p>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  getDeclarationDetail,
  getDeclarationAttachments,
  getDeclarationHistory
} from '@/api/declaration'
import PageHeader from '@/components/common/PageHeader.vue'
import {
  ArrowLeftOutlined,
  EditOutlined,
  CheckOutlined,
  MoreOutlined,
  EyeOutlined,
  DownloadOutlined,
  PrinterOutlined,
  HistoryOutlined,
  FileOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import moment from 'moment'

export default defineComponent({
  name: 'DeclarationDetail',
  components: {
    PageHeader,
    ArrowLeftOutlined,
    EditOutlined,
    CheckOutlined,
    MoreOutlined,
    EyeOutlined,
    DownloadOutlined,
    PrinterOutlined,
    HistoryOutlined,
    FileOutlined,
    ClockCircleOutlined
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const declaration = ref({})
    const attachments = ref([])
    const operationHistory = ref([])
    const previewVisible = ref(false)
    const previewImage = ref('')
    const loading = ref(false)
    const calendarValue = ref(moment())
    const deadline = ref(null)

    const breadcrumbs = [
      { title: '首页', path: '/dashboard' },
      { title: '申报管理', path: '/declaration/list' },
      { title: '申报详情' }
    ]

    // 获取申报详情
    const fetchDeclarationDetail = async () => {
      loading.value = true
      try {
        const response = await getDeclarationDetail(route.params.id)
        if (response.code === 200) {
          declaration.value = response.data

          // 设置截止日期
          if (declaration.value.deadline) {
            deadline.value = moment(declaration.value.deadline)
            calendarValue.value = deadline.value.clone()
          }
        } else {
          message.error(response.message || '获取申报详情失败')
        }
      } catch (error) {
        console.error('获取申报详情失败:', error)
        message.error('获取申报详情失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 获取附件列表
    const fetchAttachments = async () => {
      try {
        const response = await getDeclarationAttachments(route.params.id)
        if (response.code === 200) {
          attachments.value = response.data || []
        }
      } catch (error) {
        console.error('获取附件列表失败:', error)
      }
    }

    // 获取操作历史
    const fetchOperationHistory = async () => {
      try {
        const response = await getDeclarationHistory(route.params.id)
        if (response.code === 200) {
          operationHistory.value = response.data || []
        }
      } catch (error) {
        console.error('获取操作历史失败:', error)
      }
    }

    // 计算距离截止日期的天数
    const daysRemaining = computed(() => {
      if (!deadline.value) return 0

      const today = moment().startOf('day')
      const deadlineDay = deadline.value.clone().startOf('day')
      return Math.max(0, deadlineDay.diff(today, 'days'))
    })

    // 禁用日期
    const disabledDate = current => {
      return current && current < moment().startOf('day')
    }

    // 格式化货币
    const formatCurrency = (value) => {
      if (value === undefined || value === null) return '¥0.00'
      return `¥${Number(value).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })}`
    }

    // 格式化文件大小
    const formatFileSize = (bytes) => {
      if (!bytes) return '0 B'

      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 格式化申报期间
    const formatPeriod = (period) => {
      if (!period) return '-'

      // 期间格式：YYYY-MM
      const match = period.match(/^(\d{4})-(\d{2})$/)
      if (match) {
        return `${match[1]}年${match[2]}月`
      }

      return period
    }

    // 获取状态颜色
    const getStatusColor = (status) => {
      const colors = {
        draft: 'default',
        pending: 'warning',
        submitted: 'processing',
        confirmed: 'success',
        rejected: 'error'
      }
      return colors[status] || 'default'
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        draft: '草稿',
        pending: '待申报',
        submitted: '已申报',
        confirmed: '已确认',
        rejected: '已驳回'
      }
      return texts[status] || '未知'
    }

    // 获取当前步骤
    const getCurrentStep = () => {
      const stepMap = {
        draft: 0,
        pending: 1,
        submitted: 2,
        confirmed: 3,
        rejected: 2 // 驳回也是在已提交之后
      }
      return stepMap[declaration.value.status] || 0
    }

    // 获取步骤描述
    const getStepDescription = (status) => {
      if (!declaration.value || declaration.value.status === 'draft') {
        return '未开始'
      }

      const currentStep = getCurrentStep()
      const stepIndex = status === 'pending' ? 1 : (status === 'submitted' ? 2 : 3)

      if (stepIndex > currentStep) {
        return '未开始'
      }

      const stepTimes = {
        pending: declaration.value.pendingTime,
        submitted: declaration.value.submittedTime,
        confirmed: declaration.value.confirmedTime,
        rejected: declaration.value.rejectedTime
      }

      return stepTimes[status] || '处理中'
    }

    // 获取时间线颜色
    const getTimelineColor = (action) => {
      const colors = {
        create: 'blue',
        update: 'green',
        submit: 'blue',
        confirm: 'green',
        reject: 'red',
        delete: 'red'
      }
      return colors[action] || 'blue'
    }

    // 判断截止日期是否临近（7天内）
    const isDeadlineNear = (deadlineStr) => {
      if (!deadlineStr) return false

      const deadlineDate = moment(deadlineStr)
      const today = moment()
      const diffDays = deadlineDate.diff(today, 'days')

      return diffDays >= 0 && diffDays <= 7
    }

    // 编辑申报
    const handleEdit = () => {
      router.push(`/declaration/edit/${route.params.id}`)
    }

    // 提交申报
    const handleSubmit = () => {
      router.push(`/declaration/submit/${route.params.id}`)
    }

    // 预览申报表
    const handlePreview = () => {
      router.push(`/declaration/preview/${route.params.id}`)
    }

    // 下载申报表
    const handleDownload = () => {
      message.info('申报表下载功能开发中...')
    }

    // 打印申报表
    const handlePrint = () => {
      message.info('申报表打印功能开发中...')
    }

    // 查看历史
    const handleHistory = () => {
      router.push(`/declaration/history/${route.params.id}`)
    }

    // 预览附件
    const previewAttachment = (attachment) => {
      // 检查文件类型，如果是图片才支持预览
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif']
      const fileExt = attachment.name.split('.').pop().toLowerCase()

      if (imageTypes.includes(fileExt)) {
        previewImage.value = attachment.url
        previewVisible.value = true
      } else {
        message.info('此文件类型不支持在线预览，请下载后查看')
      }
    }

    // 下载附件
    const downloadAttachment = (attachment) => {
      if (attachment.url) {
        window.open(attachment.url)
      } else {
        message.error('附件下载地址不存在')
      }
    }

    // 返回列表
    const goBack = () => {
      router.go(-1)
    }

    onMounted(() => {
      fetchDeclarationDetail()
      fetchAttachments()
      fetchOperationHistory()
    })

    return {
      declaration,
      attachments,
      operationHistory,
      previewVisible,
      previewImage,
      loading,
      calendarValue,
      deadline,
      daysRemaining,
      breadcrumbs,
      formatCurrency,
      formatFileSize,
      formatPeriod,
      getStatusColor,
      getStatusText,
      getCurrentStep,
      getStepDescription,
      getTimelineColor,
      isDeadlineNear,
      disabledDate,
      handleEdit,
      handleSubmit,
      handlePreview,
      handleDownload,
      handlePrint,
      handleHistory,
      previewAttachment,
      downloadAttachment,
      goBack
    }
  }
})
</script>

<style scoped>
.declaration-detail-container {
  background: #f0f2f5;
  min-height: 100vh;
}

.content {
  padding: 0 24px 24px;
}

.detail-card {
  margin-bottom: 16px;
}

.highlight-amount {
  color: #f5222d;
  font-weight: bold;
}

.status-card,
.deadline-card {
  height: fit-content;
}

.deadline-content {
  display: flex;
  flex-direction: column;
}

.deadline-info {
  margin-top: 16px;
  text-align: center;
}

.deadline-date {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ff4d4f;
  color: #fff;
  border-radius: 2px;
}

:deep(.ant-descriptions-view) {
  border: 1px solid #f0f0f0;
}

:deep(.ant-descriptions-item-label) {
  background-color: #fafafa;
  font-weight: 500;
}

.timeline-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.timeline-content {
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 2px;
}

.timeline-user {
  font-size: 12px;
  color: #8c8c8c;
}
</style>
