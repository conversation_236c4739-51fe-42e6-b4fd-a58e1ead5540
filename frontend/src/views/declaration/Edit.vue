<template>
  <div class="declaration-edit-container">
    <PageHeader
      :title="isEdit ? '编辑申报' : '新建申报'"
      :description="isEdit ? `编辑申报: ${form.declarationNo || ''}` : '创建新的税务申报单'"
      :breadcrumbs="breadcrumbs"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
          <a-button
            type="primary"
            :loading="submitting"
            @click="handleSubmit"
          >
            <SaveOutlined />
            保存
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <div class="content">
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        layout="vertical"
      >
        <a-card title="基本信息" class="info-card">
          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-item label="企业" name="enterpriseId" required>
                <a-select
                  v-model:value="form.enterpriseId"
                  placeholder="请选择企业"
                  :options="enterpriseOptions"
                  :disabled="isEdit"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="税种" name="taxTypeId" required>
                <a-select
                  v-model:value="form.taxTypeId"
                  placeholder="请选择税种"
                  :options="taxTypeOptions"
                  :disabled="isEdit"
                  @change="handleTaxTypeChange"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="申报期间" name="period" required>
                <a-month-picker
                  v-model:value="form.period"
                  style="width: 100%"
                  format="YYYY-MM"
                  placeholder="选择申报期间"
                  :disabled="isEdit"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-item label="申报编号" name="declarationNo">
                <a-input
                  v-model:value="form.declarationNo"
                  placeholder="系统自动生成"
                  disabled
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="申报截止日期" name="deadline" required>
                <a-date-picker
                  v-model:value="form.deadline"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  placeholder="选择截止日期"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="申报状态" name="status">
                <a-select v-model:value="form.status" disabled>
                  <a-select-option value="draft">
                    草稿
                  </a-select-option>
                  <a-select-option value="pending">
                    待申报
                  </a-select-option>
                  <a-select-option value="submitted">
                    已申报
                  </a-select-option>
                  <a-select-option value="confirmed">
                    已确认
                  </a-select-option>
                  <a-select-option value="rejected">
                    已驳回
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="16">
              <a-form-item label="备注说明" name="description">
                <a-textarea
                  v-model:value="form.description"
                  placeholder="请输入申报相关说明"
                  :rows="3"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="申报模板" name="templateId">
                <a-select
                  v-model:value="form.templateId"
                  placeholder="请选择申报模板"
                  :options="templateOptions"
                  @change="handleTemplateChange"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 增值税表单 -->
        <template v-if="form.taxTypeId === 'vat'">
          <a-card title="增值税申报信息" class="tax-card">
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-item label="销项税额" name="outputTax">
                  <a-input-number
                    v-model:value="form.taxData.outputTax"
                    style="width: 100%"
                    :precision="2"
                    placeholder="销项税额"
                    @change="calculateTax"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="进项税额" name="inputTax">
                  <a-input-number
                    v-model:value="form.taxData.inputTax"
                    style="width: 100%"
                    :precision="2"
                    placeholder="进项税额"
                    @change="calculateTax"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="上期留抵税额" name="lastPeriodTax">
                  <a-input-number
                    v-model:value="form.taxData.lastPeriodTax"
                    style="width: 100%"
                    :precision="2"
                    placeholder="上期留抵税额"
                    @change="calculateTax"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-item label="销售额" name="salesAmount">
                  <a-input-number
                    v-model:value="form.taxData.salesAmount"
                    style="width: 100%"
                    :precision="2"
                    placeholder="销售额"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="应纳税额" name="taxAmount">
                  <a-input-number
                    v-model:value="form.taxData.taxAmount"
                    style="width: 100%"
                    :precision="2"
                    placeholder="应纳税额"
                    disabled
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="本期留抵税额" name="currentPeriodTax">
                  <a-input-number
                    v-model:value="form.taxData.currentPeriodTax"
                    style="width: 100%"
                    :precision="2"
                    placeholder="本期留抵税额"
                    disabled
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </template>

        <!-- 企业所得税表单 -->
        <template v-if="form.taxTypeId === 'income'">
          <a-card title="企业所得税申报信息" class="tax-card">
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-item label="收入总额" name="totalIncome">
                  <a-input-number
                    v-model:value="form.taxData.totalIncome"
                    style="width: 100%"
                    :precision="2"
                    placeholder="收入总额"
                    @change="calculateIncomeTax"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="成本费用总额" name="totalCost">
                  <a-input-number
                    v-model:value="form.taxData.totalCost"
                    style="width: 100%"
                    :precision="2"
                    placeholder="成本费用总额"
                    @change="calculateIncomeTax"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="税前利润" name="profitBeforeTax">
                  <a-input-number
                    v-model:value="form.taxData.profitBeforeTax"
                    style="width: 100%"
                    :precision="2"
                    placeholder="税前利润"
                    disabled
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-item label="应纳税所得额" name="taxableIncome">
                  <a-input-number
                    v-model:value="form.taxData.taxableIncome"
                    style="width: 100%"
                    :precision="2"
                    placeholder="应纳税所得额"
                    @change="calculateIncomeTax"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="税率(%)" name="taxRate">
                  <a-input-number
                    v-model:value="form.taxData.taxRate"
                    style="width: 100%"
                    :precision="2"
                    placeholder="税率"
                    @change="calculateIncomeTax"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="应纳税额" name="incomeTaxAmount">
                  <a-input-number
                    v-model:value="form.taxData.incomeTaxAmount"
                    style="width: 100%"
                    :precision="2"
                    placeholder="应纳税额"
                    disabled
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </template>

        <!-- 附件上传 -->
        <a-card title="附件资料" class="upload-card">
          <a-upload
            :file-list="fileList"
            :before-upload="beforeUpload"
            @preview="handlePreview"
            @remove="handleRemove"
          >
            <a-button type="primary">
              <UploadOutlined />
              上传附件
            </a-button>
            <div class="upload-hint">
              上传申报相关证明文件、财务报表等附件资料，支持 PDF、Excel、Word、图片等格式
            </div>
          </a-upload>
        </a-card>
      </a-form>
    </div>

    <!-- 预览附件模态框 -->
    <a-modal
      v-model:visible="previewVisible"
      title="附件预览"
      :footer="null"
      width="800px"
    >
      <img v-if="previewImage" :src="previewImage" style="width: 100%">
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  createDeclaration,
  updateDeclaration,
  getDeclarationDetail,
  getDeclarationTemplates
} from '@/api/declaration'
import { getEnterprises } from '@/api/enterprise'
import { getTaxTypeList } from '@/api/taxType'
import PageHeader from '@/components/common/PageHeader.vue'
import {
  ArrowLeftOutlined,
  SaveOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'DeclarationEdit',
  components: {
    PageHeader,
    ArrowLeftOutlined,
    SaveOutlined,
    UploadOutlined
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref(null)
    const submitting = ref(false)
    const fileList = ref([])
    const previewVisible = ref(false)
    const previewImage = ref('')
    const enterpriseOptions = ref([])
    const taxTypeOptions = ref([])
    const templateOptions = ref([])

    const isEdit = computed(() => {
      return route.params.id !== undefined
    })

    const breadcrumbs = computed(() => [
      { title: '首页', path: '/dashboard' },
      { title: '申报管理', path: '/declaration/list' },
      { title: isEdit.value ? '编辑申报' : '新建申报' }
    ])

    // 表单数据
    const form = reactive({
      id: '',
      enterpriseId: undefined,
      enterpriseName: '',
      taxTypeId: undefined,
      taxTypeName: '',
      period: null,
      declarationNo: '',
      deadline: null,
      status: 'draft',
      description: '',
      templateId: undefined,
      taxData: {
        // 增值税
        outputTax: 0,
        inputTax: 0,
        lastPeriodTax: 0,
        salesAmount: 0,
        taxAmount: 0,
        currentPeriodTax: 0,

        // 企业所得税
        totalIncome: 0,
        totalCost: 0,
        profitBeforeTax: 0,
        taxableIncome: 0,
        taxRate: 25, // 默认企业所得税率25%
        incomeTaxAmount: 0
      }
    })

    // 表单验证规则
    const rules = {
      enterpriseId: [{ required: true, message: '请选择企业', trigger: 'change' }],
      taxTypeId: [{ required: true, message: '请选择税种', trigger: 'change' }],
      period: [{ required: true, message: '请选择申报期间', trigger: 'change' }],
      deadline: [{ required: true, message: '请选择截止日期', trigger: 'change' }]
    }

    // 获取企业列表
    const fetchEnterprises = async () => {
      try {
        const response = await getEnterprises({ pageSize: 1000 })
        if (response.code === 200) {
          enterpriseOptions.value = response.data.items.map(item => ({
            label: item.name,
            value: item.id
          }))
        }
      } catch (error) {
        console.error('获取企业列表失败:', error)
      }
    }

    // 获取税种列表
    const fetchTaxTypes = async () => {
      try {
        const response = await getTaxTypeList()
        if (response.code === 200) {
          taxTypeOptions.value = response.data.map(item => ({
            label: item.name,
            value: item.code
          }))
        }
      } catch (error) {
        console.error('获取税种列表失败:', error)
      }
    }

    // 获取申报模板
    const fetchTemplates = async () => {
      try {
        const response = await getDeclarationTemplates()
        if (response.code === 200) {
          templateOptions.value = response.data.map(item => ({
            label: item.name,
            value: item.id
          }))
        }
      } catch (error) {
        console.error('获取申报模板失败:', error)
      }
    }

    // 获取申报详情
    const fetchDeclarationDetail = async (id) => {
      try {
        const response = await getDeclarationDetail(id)
        if (response.code === 200) {
          const data = response.data

          // 更新表单数据
          form.id = data.id
          form.enterpriseId = data.enterpriseId
          form.enterpriseName = data.enterpriseName
          form.taxTypeId = data.taxTypeId
          form.taxTypeName = data.taxTypeName
          form.period = data.period
          form.declarationNo = data.declarationNo
          form.deadline = data.deadline
          form.status = data.status
          form.description = data.description
          form.templateId = data.templateId

          // 税种相关数据
          if (data.taxData) {
            form.taxData = { ...form.taxData, ...data.taxData }
          }

          // 设置附件列表
          if (data.attachments && data.attachments.length > 0) {
            fileList.value = data.attachments.map((item, index) => ({
              uid: index,
              name: item.name,
              status: 'done',
              url: item.url
            }))
          }
        }
      } catch (error) {
        console.error('获取申报详情失败:', error)
        message.error('获取申报详情失败')
      }
    }

    // 计算增值税
    const calculateTax = () => {
      const { outputTax, inputTax, lastPeriodTax } = form.taxData

      // 应纳税额 = 销项税额 - 进项税额 - 上期留抵税额
      const taxAmount = outputTax - inputTax - lastPeriodTax

      // 如果应纳税额为负，则设为0，并将负数作为本期留抵税额
      if (taxAmount < 0) {
        form.taxData.currentPeriodTax = Math.abs(taxAmount)
        form.taxData.taxAmount = 0
      } else {
        form.taxData.currentPeriodTax = 0
        form.taxData.taxAmount = taxAmount
      }
    }

    // 计算企业所得税
    const calculateIncomeTax = () => {
      const { totalIncome, totalCost, taxableIncome, taxRate } = form.taxData

      // 税前利润 = 收入总额 - 成本费用总额
      form.taxData.profitBeforeTax = totalIncome - totalCost

      // 应纳税额 = 应纳税所得额 * 税率
      if (taxableIncome !== undefined && taxRate !== undefined) {
        form.taxData.incomeTaxAmount = taxableIncome * (taxRate / 100)
      }
    }

    // 税种变更处理
    const handleTaxTypeChange = (value) => {
      // 重置税种相关数据
      form.taxData = {
        // 增值税
        outputTax: 0,
        inputTax: 0,
        lastPeriodTax: 0,
        salesAmount: 0,
        taxAmount: 0,
        currentPeriodTax: 0,

        // 企业所得税
        totalIncome: 0,
        totalCost: 0,
        profitBeforeTax: 0,
        taxableIncome: 0,
        taxRate: 25,
        incomeTaxAmount: 0
      }

      // 获取税种名称
      const selectedTaxType = taxTypeOptions.value.find(item => item.value === value)
      if (selectedTaxType) {
        form.taxTypeName = selectedTaxType.label
      }
    }

    // 申报模板变更处理
    const handleTemplateChange = () => {
      // 这里可以根据选择的模板预填表单数据
      message.info('模板应用功能开发中')
    }

    // 上传前检查
    const beforeUpload = (file) => {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        message.error('文件大小不能超过10MB')
        return false
      }

      // 添加到文件列表但不自动上传
      fileList.value = [...fileList.value, {
        uid: file.uid,
        name: file.name,
        status: 'done',
        url: URL.createObjectURL(file),
        originFile: file
      }]
      return false
    }

    // 删除附件
    const handleRemove = (file) => {
      const index = fileList.value.indexOf(file)
      const newFileList = fileList.value.slice()
      newFileList.splice(index, 1)
      fileList.value = newFileList
    }

    // 预览附件
    const handlePreview = (file) => {
      previewImage.value = file.url || file.thumbUrl
      previewVisible.value = true
    }

    // 保存申报
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()

        submitting.value = true

        // 准备提交数据
        const formData = {
          enterpriseId: form.enterpriseId,
          taxTypeId: form.taxTypeId,
          period: form.period.format('YYYY-MM'),
          deadline: form.deadline.format('YYYY-MM-DD'),
          description: form.description,
          templateId: form.templateId,
          taxData: form.taxData
        }

        try {
          let response
          if (isEdit.value) {
            response = await updateDeclaration(form.id, formData)
          } else {
            response = await createDeclaration(formData)
          }

          if (response.code === 200) {
            message.success(`${isEdit.value ? '更新' : '创建'}申报成功`)

            // 处理附件上传，这里假设上传附件的API已存在
            // 实际实现中，可能需要调用单独的附件上传API

            router.push('/declaration/list')
          } else {
            message.error(response.message || `${isEdit.value ? '更新' : '创建'}申报失败`)
          }
        } catch (error) {
          console.error(`${isEdit.value ? '更新' : '创建'}申报失败:`, error)
          message.error(`${isEdit.value ? '更新' : '创建'}申报失败，请稍后重试`)
        } finally {
          submitting.value = false
        }
      } catch (error) {
        console.error('表单验证失败:', error)
        message.error('请完善必填项')
      }
    }

    // 返回上一页
    const goBack = () => {
      router.go(-1)
    }

    onMounted(async () => {
      // 获取基础数据
      await Promise.all([
        fetchEnterprises(),
        fetchTaxTypes(),
        fetchTemplates()
      ])

      // 如果是编辑模式，获取申报详情
      if (isEdit.value && route.params.id) {
        await fetchDeclarationDetail(route.params.id)
      }
    })

    return {
      formRef,
      form,
      rules,
      isEdit,
      submitting,
      fileList,
      previewVisible,
      previewImage,
      enterpriseOptions,
      taxTypeOptions,
      templateOptions,
      breadcrumbs,
      handleTaxTypeChange,
      handleTemplateChange,
      calculateTax,
      calculateIncomeTax,
      beforeUpload,
      handleRemove,
      handlePreview,
      handleSubmit,
      goBack
    }
  }
})
</script>

<style scoped>
.declaration-edit-container {
  background: #f0f2f5;
  min-height: 100vh;
}

.content {
  padding: 0 24px 24px;
}

.info-card,
.tax-card,
.upload-card {
  margin-bottom: 16px;
}

.upload-hint {
  margin-top: 8px;
  color: #8c8c8c;
  font-size: 12px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-upload-list-picture-card .ant-upload-list-item) {
  width: 160px;
  height: 160px;
}
</style>
