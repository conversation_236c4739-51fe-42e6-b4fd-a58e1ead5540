<template>
  <div class="user-list">
    <!-- 页面标题和操作 -->
    <PageHeader
      title="用户管理"
      description="管理系统用户账户、权限和状态"
      :breadcrumbs="breadcrumbItems"
    >
      <template #actions>
        <a-space>
          <a-button type="primary" @click="goToCreate">
            <PlusOutlined />
            新增用户
          </a-button>
          <a-button @click="exportData">
            <ExportOutlined />
            导出数据
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <!-- 搜索和筛选 -->
    <a-card :bordered="false" class="search-card">
      <a-form
        :model="searchForm"
        layout="inline"
        @finish="handleSearch"
      >
        <a-form-item label="关键词">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="搜索用户名、邮箱、姓名"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>

        <a-form-item label="用户状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="">
              全部状态
            </a-select-option>
            <a-select-option value="active">
              活跃
            </a-select-option>
            <a-select-option value="inactive">
              非活跃
            </a-select-option>
            <a-select-option value="suspended">
              已停用
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="用户角色">
          <a-select
            v-model:value="searchForm.role"
            placeholder="选择角色"
            allow-clear
            style="width: 150px"
          >
            <a-select-option value="">
              全部角色
            </a-select-option>
            <a-select-option value="admin">
              管理员
            </a-select-option>
            <a-select-option value="user">
              普通用户
            </a-select-option>
            <a-select-option value="auditor">
              审核员
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button @click="handleReset">
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 统计信息 -->
    <a-card :bordered="false" class="stats-card">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic
            title="用户总数"
            :value="stats.total"
            suffix="人"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-statistic>
        </a-col>

        <a-col :span="6">
          <a-statistic
            title="活跃用户"
            :value="stats.active"
            suffix="人"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <CheckCircleOutlined />
            </template>
          </a-statistic>
        </a-col>

        <a-col :span="6">
          <a-statistic
            title="本月新增"
            :value="stats.thisMonth"
            suffix="人"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <UserAddOutlined />
            </template>
          </a-statistic>
        </a-col>

        <a-col :span="6">
          <a-statistic
            title="在线用户"
            :value="stats.online"
            suffix="人"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <GlobalOutlined />
            </template>
          </a-statistic>
        </a-col>
      </a-row>
    </a-card>

    <!-- 数据表格 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        :scroll="{ x: 1200 }"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'user'">
            <div class="user-info">
              <a-avatar
                :src="getAvatarUrl(record.avatar)"
                :size="40"
                style="margin-right: 12px"
              >
                <template #icon>
                  <UserOutlined />
                </template>
              </a-avatar>
              <div class="user-details">
                <div class="name">
                  {{ record.full_name || record.user_name }}
                </div>
                <div class="email">
                  {{ record.email }}
                </div>
              </div>
            </div>
          </template>

          <template v-if="column.key === 'role'">
            <a-tag :color="getRoleColor(record.role_id)">
              {{ getRoleName(record.role_id) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'status'">
            <a-badge
              :status="getStatusBadge(record.status)"
              :text="getStatusText(record.status)"
            />
          </template>

          <template v-if="column.key === 'last_login'">
            <span v-if="record.last_login_at">
              {{ formatDateTime(record.last_login_at) }}
            </span>
            <span v-else class="text-muted">从未登录</span>
          </template>

          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">
                <EyeOutlined />
                详情
              </a-button>
              <a-button type="link" size="small" @click="editUser(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="resetPassword(record)">
                      <KeyOutlined />
                      重置密码
                    </a-menu-item>
                    <a-menu-item @click="toggleStatus(record)">
                      <component :is="record.status === 'active' ? StopOutlined : PlayCircleOutlined" />
                      {{ record.status === 'active' ? '停用' : '启用' }}
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item danger @click="deleteUser(record)">
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ExportOutlined,
  SearchOutlined,
  UserOutlined,
  CheckCircleOutlined,
  UserAddOutlined,
  GlobalOutlined,
  EyeOutlined,
  EditOutlined,
  DownOutlined,
  KeyOutlined,
  StopOutlined,
  PlayCircleOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

import PageHeader from '@/components/common/PageHeader.vue'
import { getUserList, deleteUser, updateUser, resetUserPassword } from '@/api/user'
import { getAvatarUrl } from '@/utils/constants'

export default defineComponent({
  name: 'UserList',
  components: {
    PageHeader,
    PlusOutlined,
    ExportOutlined,
    SearchOutlined,
    UserOutlined,
    CheckCircleOutlined,
    UserAddOutlined,
    GlobalOutlined,
    EyeOutlined,
    EditOutlined,
    DownOutlined,
    KeyOutlined,
    StopOutlined,
    PlayCircleOutlined,
    DeleteOutlined
  },
  setup () {
    const router = useRouter()

    // 面包屑导航
    const breadcrumbItems = [
      { title: '首页', path: '/' },
      { title: '权限管理', path: '/permissions' },
      { title: '用户管理' }
    ]

    // 响应式数据
    const loading = ref(false)
    const dataSource = ref([])
    const stats = reactive({
      total: 0,
      active: 0,
      thisMonth: 0,
      online: 0
    })

    // 搜索表单
    const searchForm = reactive({
      keyword: '',
      status: '',
      role: ''
    })

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
    })

    // 表格列配置
    const columns = [
      {
        title: '用户信息',
        key: 'user',
        width: 250,
        fixed: 'left'
      },
      {
        title: '用户名',
        dataIndex: 'user_name',
        width: 120
      },
      {
        title: '角色',
        key: 'role',
        width: 100
      },
      {
        title: '状态',
        key: 'status',
        width: 100
      },
      {
        title: '最后登录',
        key: 'last_login',
        width: 150
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        width: 150,
        sorter: true
      },
      {
        title: '操作',
        key: 'actions',
        width: 200,
        fixed: 'right'
      }
    ]

    // 获取用户列表数据
    const fetchData = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.current,
          pageSize: pagination.pageSize,
          keyword: searchForm.keyword,
          status: searchForm.status,
          role: searchForm.role
        }

        const response = await getUserList(params)
        if (response.code === 200) {
          const data = response.data
          dataSource.value = data.users || []
          pagination.total = data.total || 0

          // 更新统计信息
          updateStats(data.users || [])
        } else {
          message.error(response.message || '获取用户列表失败')
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        message.error('获取用户列表失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 更新统计信息
    const updateStats = (users) => {
      stats.total = users.length
      stats.active = users.filter(user => user.status === 'active').length
      // 本月新增用户（简化处理）
      const thisMonth = new Date()
      thisMonth.setDate(1)
      stats.thisMonth = users.filter(user =>
        new Date(user.created_at) >= thisMonth
      ).length
      // 在线用户（模拟数据）
      stats.online = Math.floor(stats.active * 0.3)
    }

    // 搜索处理
    const handleSearch = () => {
      pagination.current = 1
      fetchData()
    }

    // 重置搜索
    const handleReset = () => {
      searchForm.keyword = ''
      searchForm.status = ''
      searchForm.role = ''
      pagination.current = 1
      fetchData()
    }

    // 表格变化处理
    const handleTableChange = (pag) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      fetchData()
    }

    // 页面操作方法
    const goToCreate = () => {
      router.push('/permissions/users/create')
    }

    const viewDetail = (record) => {
      router.push(`/permissions/users/detail/${record.id}`)
    }

    const editUser = (record) => {
      router.push(`/permissions/users/edit/${record.id}`)
    }

    const toggleStatus = async (record) => {
      try {
        const newStatus = record.status === 'active' ? 'inactive' : 'active'
        const response = await updateUser(record.id, {
          status: newStatus
        })

        if (response.code === 200) {
          message.success(`${newStatus === 'active' ? '启用' : '停用'}成功`)
          record.status = newStatus
          updateStats(dataSource.value)
        } else {
          message.error(response.message || '操作失败')
        }
      } catch (error) {
        console.error('切换状态失败:', error)
        message.error('操作失败，请稍后重试')
      }
    }

    const resetPassword = (record) => {
      Modal.confirm({
        title: '重置密码',
        content: `确定要重置用户"${record.user_name}"的密码吗？`,
        async onOk () {
          try {
            const response = await resetUserPassword(record.id)
            if (response.code === 200) {
              message.success('密码重置成功，新密码已发送到用户邮箱')
            } else {
              message.error(response.message || '重置密码失败')
            }
          } catch (error) {
            console.error('重置密码失败:', error)
            message.error('重置密码失败，请稍后重试')
          }
        }
      })
    }

    const deleteUser = (record) => {
      Modal.confirm({
        title: '确认删除用户',
        content: `确定要删除用户"${record.user_name}"吗？此操作不可恢复。`,
        okType: 'danger',
        async onOk () {
          try {
            const response = await deleteUser(record.id)
            if (response.code === 200) {
              message.success('删除成功')
              fetchData()
            } else {
              message.error(response.message || '删除失败')
            }
          } catch (error) {
            console.error('删除用户失败:', error)
            message.error('删除失败，请稍后重试')
          }
        }
      })
    }

    const exportData = () => {
      message.info('导出功能开发中...')
    }

    // 辅助方法
    const getRoleColor = (roleId) => {
      const colors = {
        admin: 'red',
        user: 'blue',
        auditor: 'green'
      }
      return colors[roleId] || 'default'
    }

    const getRoleName = (roleId) => {
      const names = {
        admin: '管理员',
        user: '普通用户',
        auditor: '审核员'
      }
      return names[roleId] || roleId
    }

    const getStatusBadge = (status) => {
      const badges = {
        active: 'success',
        inactive: 'default',
        suspended: 'error'
      }
      return badges[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        active: '活跃',
        inactive: '非活跃',
        suspended: '已停用'
      }
      return texts[status] || status
    }

    const formatDateTime = (dateTime) => {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    }

    // 生命周期
    onMounted(() => {
      fetchData()
    })

    return {
      breadcrumbItems,
      loading,
      dataSource,
      stats,
      searchForm,
      pagination,
      columns,
      fetchData,
      handleSearch,
      handleReset,
      handleTableChange,
      goToCreate,
      viewDetail,
      editUser,
      toggleStatus,
      resetPassword,
      deleteUser,
      exportData,
      getRoleColor,
      getRoleName,
      getStatusBadge,
      getStatusText,
      formatDateTime,
      getAvatarUrl
    }
  }
})
</script>

<style scoped>
.user-list {
  padding: 0;
}

.search-card,
.stats-card {
  margin-bottom: 16px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-details .name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.user-details .email {
  font-size: 12px;
  color: #8c8c8c;
}

.text-muted {
  color: #8c8c8c;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 500;
}

:deep(.ant-statistic-content-prefix) {
  margin-right: 8px;
  font-size: 20px;
}
</style>
