<template>
  <a-modal
    v-model:visible="modalVisible"
    title="用户详情"
    :footer="null"
    width="700px"
    @cancel="handleCancel"
  >
    <div v-if="user" class="user-detail-content">
      <!-- 用户基本信息 -->
      <div class="user-basic-info">
        <div class="user-header">
          <a-avatar :src="getAvatarUrl(user.avatar)" :size="80">
            {{ user.user_name?.charAt(0)?.toUpperCase() }}
          </a-avatar>
          <div class="user-info">
            <h2>{{ user.user_name }}</h2>
            <div class="user-tags">
              <a-tag :color="getRoleColor(user.role_name)">
                {{ user.role_name }}
              </a-tag>
              <a-tag v-if="user.is_owner" color="gold">
                <template #icon>
                  <CrownOutlined />
                </template>
                企业所有者
              </a-tag>
              <a-badge
                :status="getStatusBadge(user.status)"
                :text="getStatusText(user.status)"
              />
            </div>
            <div class="user-contact">
              <div v-if="user.email">
                <MailOutlined /> {{ user.email }}
              </div>
              <div v-if="user.phone">
                <PhoneOutlined /> {{ user.phone }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细信息标签页 -->
      <a-tabs v-model:active-key="activeTab" class="detail-tabs">
        <!-- 基本信息 -->
        <a-tab-pane key="basic" tab="基本信息">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="用户名">
              {{ user.user_name }}
            </a-descriptions-item>
            <a-descriptions-item label="邮箱">
              {{ user.email || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="手机号">
              {{ user.phone || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="部门">
              {{ user.department || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="职位">
              {{ user.position || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-badge
                :status="getStatusBadge(user.status)"
                :text="getStatusText(user.status)"
              />
            </a-descriptions-item>
            <a-descriptions-item label="加入时间">
              {{ formatDate(user.joined_at) }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDate(user.created_at) }}
            </a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>

        <!-- 角色权限 -->
        <a-tab-pane key="permissions" tab="角色权限">
          <div class="permissions-section">
            <div class="current-role">
              <h4>当前角色</h4>
              <div class="role-card">
                <a-tag :color="getRoleColor(user.role_name)" size="large">
                  {{ user.role_name }}
                </a-tag>
                <span class="role-description">{{ currentRoleDescription }}</span>
              </div>
            </div>

            <div v-if="userPermissions.length" class="permissions-list">
              <h4>拥有权限</h4>
              <a-row :gutter="[8, 8]">
                <a-col
                  v-for="permission in userPermissions"
                  :key="permission.id"
                  :span="12"
                >
                  <a-tag color="blue" class="permission-tag">
                    {{ permission.name }}
                  </a-tag>
                </a-col>
              </a-row>
            </div>

            <div v-else class="no-permissions">
              <a-empty description="暂无权限信息" />
            </div>
          </div>
        </a-tab-pane>

        <!-- 操作日志 -->
        <a-tab-pane key="logs" tab="操作日志">
          <div class="logs-section">
            <a-timeline>
              <a-timeline-item
                v-for="log in operationLogs"
                :key="log.id"
                :color="getLogColor(log.operation_type)"
              >
                <div class="log-item">
                  <div class="log-header">
                    <span class="log-action">{{ getLogActionText(log.operation_type) }}</span>
                    <span class="log-time">{{ formatDate(log.created_at) }}</span>
                  </div>
                  <div class="log-description">
                    {{ log.reason || getDefaultLogDescription(log.operation_type) }}
                  </div>
                  <div v-if="log.operator_name" class="log-operator">
                    操作人：{{ log.operator_name }}
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>

            <div v-if="!operationLogs.length" class="no-logs">
              <a-empty description="暂无操作日志" />
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  CrownOutlined,
  MailOutlined,
  PhoneOutlined
} from '@ant-design/icons-vue'
import { getUserPermissions, getUserRoles } from '@/api/user'
import { formatDate } from '@/utils/date'
import { getAvatarUrl } from '@/utils/constants'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  },
  enterpriseId: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const activeTab = ref('basic')
const userPermissions = ref([])
const userRoles = ref([])
const operationLogs = ref([])

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const currentRoleDescription = computed(() => {
  const role = userRoles.value.find(r => r.id === props.user?.role_id)
  return role?.description || '暂无描述'
})

// 监听器
watch(() => props.visible, (visible) => {
  if (visible && props.user) {
    loadUserDetails()
  }
})

// 生命周期
onMounted(() => {
  if (props.visible && props.user) {
    loadUserDetails()
  }
})

// 方法
const loadUserDetails = async () => {
  if (!props.user?.user_id) return

  try {
    // 加载用户权限
    const permissionsResponse = await getUserPermissions(props.user.user_id)
    userPermissions.value = permissionsResponse.data || []

    // 加载用户角色
    const rolesResponse = await getUserRoles(props.user.user_id)
    userRoles.value = rolesResponse.data || []

    // TODO: 加载操作日志
    // const logsResponse = await getUserOperationLogs(props.user.user_id)
    // operationLogs.value = logsResponse.data || []

    // 模拟操作日志数据
    operationLogs.value = [
      {
        id: '1',
        operation_type: 'user_invite',
        created_at: new Date().toISOString(),
        reason: '邀请加入企业',
        operator_name: '管理员'
      },
      {
        id: '2',
        operation_type: 'role_assign',
        created_at: new Date(Date.now() - 86400000).toISOString(),
        reason: '分配会计角色',
        operator_name: '企业所有者'
      }
    ]
  } catch (error) {
    message.error('加载用户详情失败')
  }
}

const handleCancel = () => {
  modalVisible.value = false
}

// 辅助方法
const getRoleColor = (roleName) => {
  const colorMap = {
    管理员: 'red',
    会计: 'blue',
    操作员: 'green',
    查看者: 'default'
  }
  return colorMap[roleName] || 'default'
}

const getStatusBadge = (status) => {
  const badgeMap = {
    active: 'success',
    inactive: 'default',
    pending: 'processing'
  }
  return badgeMap[status] || 'default'
}

const getStatusText = (status) => {
  const textMap = {
    active: '活跃',
    inactive: '非活跃',
    pending: '待激活'
  }
  return textMap[status] || status
}

const getLogColor = (operationType) => {
  const colorMap = {
    user_invite: 'green',
    role_assign: 'blue',
    role_revoke: 'orange',
    permission_grant: 'cyan',
    permission_revoke: 'red',
    user_remove: 'red'
  }
  return colorMap[operationType] || 'default'
}

const getLogActionText = (operationType) => {
  const textMap = {
    user_invite: '用户邀请',
    role_assign: '角色分配',
    role_revoke: '角色撤销',
    permission_grant: '权限授予',
    permission_revoke: '权限撤销',
    user_remove: '用户移除'
  }
  return textMap[operationType] || operationType
}

const getDefaultLogDescription = (operationType) => {
  const descMap = {
    user_invite: '用户被邀请加入企业',
    role_assign: '用户角色被重新分配',
    role_revoke: '用户角色被撤销',
    permission_grant: '用户获得特殊权限',
    permission_revoke: '用户特殊权限被撤销',
    user_remove: '用户被移出企业'
  }
  return descMap[operationType] || '执行了相关操作'
}
</script>

<style scoped>
.user-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.user-basic-info {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.user-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.user-info {
  flex: 1;
}

.user-info h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.user-tags {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-contact {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-contact > div {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #595959;
  font-size: 14px;
}

.detail-tabs {
  margin-top: 16px;
}

.permissions-section {
  padding: 16px 0;
}

.current-role {
  margin-bottom: 24px;
}

.current-role h4,
.permissions-list h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.role-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.role-description {
  color: #8c8c8c;
  font-size: 14px;
}

.permission-tag {
  width: 100%;
  text-align: center;
  margin-bottom: 8px;
}

.logs-section {
  padding: 16px 0;
}

.log-item {
  margin-bottom: 8px;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.log-action {
  font-weight: 500;
  color: #262626;
}

.log-time {
  font-size: 12px;
  color: #8c8c8c;
}

.log-description {
  color: #595959;
  font-size: 14px;
  margin-bottom: 4px;
}

.log-operator {
  font-size: 12px;
  color: #8c8c8c;
}

.no-permissions,
.no-logs {
  text-align: center;
  padding: 40px 0;
}
</style>
