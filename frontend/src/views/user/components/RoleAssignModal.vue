<template>
  <a-modal
    v-model:visible="modalVisible"
    title="分配角色"
    :confirm-loading="loading"
    width="500px"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div v-if="user" class="role-assign-content">
      <!-- 用户信息 -->
      <div class="user-info-section">
        <h4>用户信息</h4>
        <div class="user-card">
          <a-avatar :src="getAvatarUrl(user.avatar)" :size="48">
            {{ user.user_name?.charAt(0)?.toUpperCase() }}
          </a-avatar>
          <div class="user-details">
            <div class="user-name">
              {{ user.user_name }}
            </div>
            <div class="user-contact">
              {{ user.email || user.phone }}
            </div>
            <div class="current-role">
              当前角色：
              <a-tag :color="getRoleColor(user.role_name)">
                {{ user.role_name }}
              </a-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 角色选择 -->
      <div class="role-selection-section">
        <h4>选择新角色</h4>
        <a-form
          ref="formRef"
          :model="form"
          :rules="rules"
          layout="vertical"
        >
          <a-form-item name="roleId" required>
            <a-radio-group
              v-model:value="form.roleId"
              class="role-radio-group"
            >
              <div
                v-for="role in availableRoles"
                :key="role.id"
                class="role-option"
              >
                <a-radio :value="role.id">
                  <div class="role-content">
                    <div class="role-header">
                      <span class="role-name">{{ role.name }}</span>
                      <a-tag
                        :color="getRoleTypeColor(role.type)"
                        size="small"
                      >
                        {{ getRoleTypeText(role.type) }}
                      </a-tag>
                    </div>
                    <div class="role-description">
                      {{ role.description }}
                    </div>
                  </div>
                </a-radio>
              </div>
            </a-radio-group>
          </a-form-item>

          <!-- 变更原因 -->
          <a-form-item label="变更原因" name="reason">
            <a-textarea
              v-model:value="form.reason"
              placeholder="请说明角色变更的原因（可选）"
              :rows="3"
              :maxlength="200"
              show-count
            />
          </a-form-item>
        </a-form>
      </div>
    </div>

    <!-- 操作确认提示 -->
    <a-alert
      v-if="form.roleId && form.roleId !== user?.role_id"
      message="角色变更确认"
      type="warning"
      show-icon
      style="margin-top: 16px;"
    >
      <template #description>
        角色变更将立即生效，用户的权限将发生变化。请确认此操作。
      </template>
    </a-alert>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { updateUserRole } from '@/api/user'
import { getAvatarUrl } from '@/utils/constants'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  },
  roles: {
    type: Array,
    default: () => []
  },
  enterpriseId: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)
const formRef = ref()

// 表单数据
const form = reactive({
  roleId: undefined,
  reason: ''
})

// 表单验证规则
const rules = {
  roleId: [
    { required: true, message: '请选择新角色' }
  ]
}

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const availableRoles = computed(() => {
  // 过滤掉系统角色和当前角色
  return props.roles.filter(role =>
    role.type !== 'system' && role.id !== props.user?.role_id
  )
})

// 监听器
watch(() => props.visible, (visible) => {
  if (visible && props.user) {
    resetForm()
  }
})

// 方法
const resetForm = () => {
  form.roleId = undefined
  form.reason = ''
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    loading.value = true

    const data = {
      role_id: form.roleId
    }

    await updateUserRole(props.enterpriseId, props.user.user_id, data)

    message.success('角色分配成功')
    emit('success')
    modalVisible.value = false
  } catch (error) {
    if (error.errorFields) {
      return
    }
    message.error(error.message || '角色分配失败')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
}

// 辅助方法
const getRoleColor = (roleName) => {
  const colorMap = {
    管理员: 'red',
    会计: 'blue',
    操作员: 'green',
    查看者: 'default'
  }
  return colorMap[roleName] || 'default'
}

const getRoleTypeColor = (type) => {
  const colorMap = {
    system: 'red',
    business: 'blue',
    custom: 'green'
  }
  return colorMap[type] || 'default'
}

const getRoleTypeText = (type) => {
  const textMap = {
    system: '系统角色',
    business: '业务角色',
    custom: '自定义角色'
  }
  return textMap[type] || type
}
</script>

<style scoped>
.role-assign-content {
  max-height: 600px;
  overflow-y: auto;
}

.user-info-section {
  margin-bottom: 24px;
}

.user-info-section h4,
.role-selection-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.user-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.user-contact {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.current-role {
  font-size: 14px;
  color: #595959;
}

.role-selection-section {
  margin-bottom: 24px;
}

.role-radio-group {
  width: 100%;
}

.role-option {
  display: block;
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s;
}

.role-option:hover {
  border-color: #1890ff;
  background: #f6ffed;
}

.role-content {
  margin-left: 8px;
}

.role-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.role-name {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.role-description {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
  line-height: 1.5;
}
</style>
