<template>
  <a-modal
    v-model:visible="modalVisible"
    title="转让企业所有权"
    :confirm-loading="loading"
    width="600px"
    :ok-text="'确认转让'"
    :cancel-text="'取消'"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="transfer-ownership-content">
      <!-- 警告提示 -->
      <a-alert
        message="重要提醒"
        type="error"
        show-icon
        style="margin-bottom: 24px;"
      >
        <template #description>
          <ul style="margin: 8px 0; padding-left: 20px;">
            <li>企业所有权转让后将无法撤销</li>
            <li>您将失去企业的最高管理权限</li>
            <li>新所有者将获得企业的完全控制权</li>
            <li>请谨慎操作，确认后再提交</li>
          </ul>
        </template>
      </a-alert>

      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        layout="vertical"
      >
        <!-- 选择新所有者 -->
        <a-form-item label="选择新所有者" name="newOwnerId" required>
          <a-select
            v-model:value="form.newOwnerId"
            placeholder="请选择要转让给的用户"
            show-search
            :filter-option="filterUser"
            @change="handleUserSelect"
          >
            <a-select-option
              v-for="user in enterpriseUsers"
              :key="user.user_id"
              :value="user.user_id"
              :disabled="user.is_owner"
            >
              <div class="user-option">
                <a-avatar :src="user.avatar" :size="24">
                  {{ user.user_name?.charAt(0)?.toUpperCase() }}
                </a-avatar>
                <div class="user-info">
                  <div class="user-name">
                    {{ user.user_name }}
                  </div>
                  <div class="user-role">
                    {{ user.role_name }}
                  </div>
                </div>
                <a-tag v-if="user.is_owner" color="gold" size="small">
                  当前所有者
                </a-tag>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 转让原因 -->
        <a-form-item label="转让原因" name="reason" required>
          <a-textarea
            v-model:value="form.reason"
            placeholder="请详细说明转让企业所有权的原因"
            :rows="4"
            :maxlength="500"
            show-count
          />
        </a-form-item>

        <!-- 密码确认 -->
        <a-form-item label="确认密码" name="password" required>
          <a-input-password
            v-model:value="form.password"
            placeholder="请输入您的登录密码以确认身份"
            autocomplete="current-password"
          />
        </a-form-item>

        <!-- 确认勾选 -->
        <a-form-item name="confirmed" required>
          <a-checkbox v-model:checked="form.confirmed">
            我已充分了解转让企业所有权的后果，确认执行此操作
          </a-checkbox>
        </a-form-item>
      </a-form>

      <!-- 新所有者信息预览 -->
      <div v-if="selectedUser" class="new-owner-preview">
        <a-divider>新所有者信息</a-divider>
        <div class="owner-preview-card">
          <a-avatar :src="selectedUser.avatar" :size="48">
            {{ selectedUser.user_name?.charAt(0)?.toUpperCase() }}
          </a-avatar>
          <div class="owner-info">
            <h4>{{ selectedUser.user_name }}</h4>
            <p>{{ selectedUser.email }}</p>
            <p>{{ selectedUser.phone }}</p>
            <div class="owner-tags">
              <a-tag :color="getRoleColor(selectedUser.role_name)">
                {{ selectedUser.role_name }}
              </a-tag>
              <a-tag color="green">
                加入时间：{{ formatDate(selectedUser.joined_at) }}
              </a-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 转让流程说明 -->
      <div class="transfer-process">
        <a-divider>转让流程</a-divider>
        <a-steps :current="0" size="small">
          <a-step title="提交申请" description="填写转让信息并确认" />
          <a-step title="身份验证" description="验证当前所有者身份" />
          <a-step title="执行转让" description="系统执行所有权转让" />
          <a-step title="完成通知" description="通知相关用户转让结果" />
        </a-steps>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { transferOwnership } from '@/api/user'
import { formatDate } from '@/utils/date'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  enterpriseId: {
    type: String,
    required: true
  },
  enterpriseUsers: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)
const formRef = ref()
const selectedUser = ref(null)

// 表单数据
const form = reactive({
  newOwnerId: undefined,
  reason: '',
  password: '',
  confirmed: false
})

// 表单验证规则
const rules = {
  newOwnerId: [
    { required: true, message: '请选择新所有者' }
  ],
  reason: [
    { required: true, message: '请填写转让原因' },
    { min: 10, message: '转让原因至少需要10个字符' }
  ],
  password: [
    { required: true, message: '请输入密码确认身份' }
  ],
  confirmed: [
    {
      validator: (rule, value) => {
        if (!value) {
          return Promise.reject('请确认您已了解转让后果')
        }
        return Promise.resolve()
      }
    }
  ]
}

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    resetForm()
  }
})

// 方法
const resetForm = () => {
  form.newOwnerId = undefined
  form.reason = ''
  form.password = ''
  form.confirmed = false
  selectedUser.value = null
  formRef.value?.resetFields()
}

const filterUser = (input, option) => {
  const user = props.enterpriseUsers.find(u => u.user_id === option.value)
  if (!user) return false

  const searchText = input.toLowerCase()
  return user.user_name.toLowerCase().includes(searchText) ||
    user.email?.toLowerCase().includes(searchText) ||
    user.phone?.includes(searchText)
}

const handleUserSelect = (userId) => {
  selectedUser.value = props.enterpriseUsers.find(u => u.user_id === userId)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    // 二次确认
    const confirmed = await new Promise((resolve) => {
      Modal.confirm({
        title: '最终确认',
        content: `您确定要将企业所有权转让给 ${selectedUser.value?.user_name} 吗？此操作不可撤销！`,
        okText: '确认转让',
        cancelText: '取消',
        okType: 'danger',
        onOk: () => resolve(true),
        onCancel: () => resolve(false)
      })
    })

    if (!confirmed) return

    loading.value = true

    const data = {
      new_owner_id: form.newOwnerId,
      reason: form.reason,
      password: form.password
    }

    await transferOwnership(props.enterpriseId, data)

    message.success('企业所有权转让成功')
    emit('success')
    modalVisible.value = false
  } catch (error) {
    if (error.errorFields) {
      return
    }
    message.error(error.message || '企业所有权转让失败')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
}

// 辅助方法
const getRoleColor = (roleName) => {
  const colorMap = {
    管理员: 'red',
    会计: 'blue',
    操作员: 'green',
    查看者: 'default'
  }
  return colorMap[roleName] || 'default'
}
</script>

<style scoped>
.transfer-ownership-content {
  max-height: 70vh;
  overflow-y: auto;
}

.user-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.user-role {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

.new-owner-preview {
  margin-top: 16px;
}

.owner-preview-card {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
}

.owner-info {
  flex: 1;
}

.owner-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.owner-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #595959;
}

.owner-tags {
  margin-top: 8px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.transfer-process {
  margin-top: 24px;
}
</style>
