<template>
  <div class="settings-container">
    <a-card>
      <template #title>
        <div class="settings-header">
          <h2>系统设置</h2>
          <a-button
            type="primary"
            :loading="loading"
            @click="saveSettings"
          >
            <SaveOutlined />
            保存设置
          </a-button>
        </div>
      </template>

      <a-row :gutter="24">
        <a-col :span="24">
          <!-- 偏好设置 -->
          <a-divider orientation="left">
            系统偏好
          </a-divider>

          <a-form
            :model="settings"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
            layout="horizontal"
          >
            <a-form-item label="界面主题">
              <a-radio-group v-model:value="settings.theme">
                <a-radio value="light">
                  浅色主题
                </a-radio>
                <a-radio value="dark">
                  深色主题
                </a-radio>
                <a-radio value="auto">
                  跟随系统
                </a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item label="语言设置">
              <a-select v-model:value="settings.language" style="width: 200px">
                <a-select-option value="zh-CN">
                  简体中文
                </a-select-option>
                <a-select-option value="zh-TW">
                  繁體中文
                </a-select-option>
                <a-select-option value="en-US">
                  English
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="时区设置">
              <a-select v-model:value="settings.timezone" style="width: 300px">
                <a-select-option value="Asia/Shanghai">
                  中国标准时间 (UTC+8)
                </a-select-option>
                <a-select-option value="Asia/Hong_Kong">
                  香港时间 (UTC+8)
                </a-select-option>
                <a-select-option value="Asia/Taipei">
                  台北时间 (UTC+8)
                </a-select-option>
                <a-select-option value="UTC">
                  协调世界时 (UTC+0)
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="页面大小">
              <a-select v-model:value="settings.pageSize" style="width: 200px">
                <a-select-option :value="10">
                  10条/页
                </a-select-option>
                <a-select-option :value="20">
                  20条/页
                </a-select-option>
                <a-select-option :value="50">
                  50条/页
                </a-select-option>
                <a-select-option :value="100">
                  100条/页
                </a-select-option>
              </a-select>
            </a-form-item>

            <!-- 通知设置 -->
            <a-divider orientation="left">
              通知设置
            </a-divider>

            <a-form-item label="桌面通知">
              <a-switch
                v-model:checked="settings.notifications.desktop"
                checked-children="开启"
                un-checked-children="关闭"
              />
              <div class="setting-description">
                允许系统发送桌面通知
              </div>
            </a-form-item>

            <a-form-item label="邮件通知">
              <a-switch
                v-model:checked="settings.notifications.email"
                checked-children="开启"
                un-checked-children="关闭"
              />
              <div class="setting-description">
                接收重要事件的邮件通知
              </div>
            </a-form-item>

            <a-form-item label="申报提醒">
              <a-switch
                v-model:checked="settings.notifications.declaration"
                checked-children="开启"
                un-checked-children="关闭"
              />
              <div class="setting-description">
                申报截止日期前的提醒通知
              </div>
            </a-form-item>

            <a-form-item label="系统消息">
              <a-switch
                v-model:checked="settings.notifications.system"
                checked-children="开启"
                un-checked-children="关闭"
              />
              <div class="setting-description">
                系统维护、更新等通知消息
              </div>
            </a-form-item>

            <!-- 隐私设置 -->
            <a-divider orientation="left">
              隐私设置
            </a-divider>

            <a-form-item label="数据收集">
              <a-switch
                v-model:checked="settings.privacy.analytics"
                checked-children="允许"
                un-checked-children="禁止"
              />
              <div class="setting-description">
                允许收集匿名使用数据以改进产品
              </div>
            </a-form-item>

            <a-form-item label="错误报告">
              <a-switch
                v-model:checked="settings.privacy.errorReporting"
                checked-children="允许"
                un-checked-children="禁止"
              />
              <div class="setting-description">
                自动发送错误报告以帮助问题诊断
              </div>
            </a-form-item>

            <a-form-item label="操作日志">
              <a-switch
                v-model:checked="settings.privacy.auditLog"
                checked-children="开启"
                un-checked-children="关闭"
              />
              <div class="setting-description">
                记录用户操作日志用于审计
              </div>
            </a-form-item>

            <!-- 安全设置 -->
            <a-divider orientation="left">
              安全设置
            </a-divider>

            <a-form-item label="自动登出">
              <a-select v-model:value="settings.security.autoLogoutTime" style="width: 200px">
                <a-select-option :value="30">
                  30分钟
                </a-select-option>
                <a-select-option :value="60">
                  1小时
                </a-select-option>
                <a-select-option :value="120">
                  2小时
                </a-select-option>
                <a-select-option :value="240">
                  4小时
                </a-select-option>
                <a-select-option :value="0">
                  永不
                </a-select-option>
              </a-select>
              <div class="setting-description">
                无操作时自动登出的时间
              </div>
            </a-form-item>

            <a-form-item label="登录验证">
              <a-switch
                v-model:checked="settings.security.requirePasswordForImportant"
                checked-children="开启"
                un-checked-children="关闭"
              />
              <div class="setting-description">
                重要操作时需要重新验证密码
              </div>
            </a-form-item>

            <a-form-item label="IP限制">
              <a-switch
                v-model:checked="settings.security.ipRestriction"
                checked-children="开启"
                un-checked-children="关闭"
              />
              <div class="setting-description">
                限制只能从特定IP地址登录
              </div>
            </a-form-item>
          </a-form>

          <!-- 数据管理 -->
          <a-divider orientation="left">
            数据管理
          </a-divider>

          <div class="data-management">
            <a-space direction="vertical" size="large" style="width: 100%">
              <div class="action-item">
                <div class="action-info">
                  <h4>导出数据</h4>
                  <p>导出您的所有数据，包括企业信息、申报记录等</p>
                </div>
                <a-button type="default" @click="exportData">
                  <DownloadOutlined />
                  导出数据
                </a-button>
              </div>

              <div class="action-item">
                <div class="action-info">
                  <h4>清除缓存</h4>
                  <p>清除本地缓存数据，可能需要重新加载页面</p>
                </div>
                <a-button type="default" @click="clearCache">
                  <ClearOutlined />
                  清除缓存
                </a-button>
              </div>

              <div class="action-item dangerous">
                <div class="action-info">
                  <h4>删除账户</h4>
                  <p class="text-danger">
                    永久删除您的账户和所有相关数据，此操作不可恢复
                  </p>
                </div>
                <a-button danger @click="showDeleteConfirm">
                  <DeleteOutlined />
                  删除账户
                </a-button>
              </div>
            </a-space>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 删除账户确认对话框 -->
    <a-modal
      v-model:open="showDeleteModal"
      title="删除账户确认"
      ok-text="确认删除"
      cancel-text="取消"
      ok-type="danger"
      @ok="deleteAccount"
      @cancel="showDeleteModal = false"
    >
      <a-alert
        message="警告"
        description="此操作将永久删除您的账户和所有相关数据，包括企业信息、申报记录等。此操作不可恢复！"
        type="error"
        show-icon
        style="margin-bottom: 16px"
      />
      <p>请输入您的邮箱地址以确认删除：</p>
      <a-input
        v-model:value="deleteConfirmEmail"
        placeholder="请输入您的邮箱地址"
      />
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { useUserStore } from '@/store/user'
import {
  SaveOutlined,
  DownloadOutlined,
  ClearOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'UserSettings',
  components: {
    SaveOutlined,
    DownloadOutlined,
    ClearOutlined,
    DeleteOutlined
  },
  setup () {
    const userStore = useUserStore()
    const loading = ref(false)
    const showDeleteModal = ref(false)
    const deleteConfirmEmail = ref('')

    // 设置数据
    const settings = reactive({
      theme: 'light',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      pageSize: 20,
      notifications: {
        desktop: true,
        email: true,
        declaration: true,
        system: false
      },
      privacy: {
        analytics: true,
        errorReporting: true,
        auditLog: true
      },
      security: {
        autoLogoutTime: 60,
        requirePasswordForImportant: true,
        ipRestriction: false
      }
    })

    // 加载设置
    const loadSettings = () => {
      const savedSettings = localStorage.getItem('userSettings')
      if (savedSettings) {
        try {
          const parsed = JSON.parse(savedSettings)
          Object.assign(settings, parsed)
        } catch (error) {
          console.error('加载设置失败:', error)
        }
      }
    }

    // 保存设置
    const saveSettings = async () => {
      loading.value = true
      try {
        // 保存到本地存储
        localStorage.setItem('userSettings', JSON.stringify(settings))

        // 这里可以添加发送到服务器的逻辑
        // await updateUserSettings(settings)

        message.success('设置保存成功')

        // 应用主题设置
        applyTheme(settings.theme)

        // 应用语言设置
        applyLanguage(settings.language)
      } catch (error) {
        message.error('设置保存失败')
        console.error('保存设置失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 应用主题
    const applyTheme = (theme) => {
      // 这里可以实现主题切换逻辑
      document.documentElement.setAttribute('data-theme', theme)
    }

    // 应用语言
    const applyLanguage = (language) => {
      // 这里可以实现语言切换逻辑
      document.documentElement.setAttribute('lang', language)
    }

    // 导出数据
    const exportData = () => {
      message.info('数据导出功能正在开发中')
    }

    // 清除缓存
    const clearCache = () => {
      Modal.confirm({
        title: '确认清除缓存',
        content: '清除缓存后需要重新加载页面，是否继续？',
        onOk () {
          localStorage.clear()
          sessionStorage.clear()
          message.success('缓存已清除')
          setTimeout(() => {
            window.location.reload()
          }, 1000)
        }
      })
    }

    // 显示删除确认
    const showDeleteConfirm = () => {
      showDeleteModal.value = true
      deleteConfirmEmail.value = ''
    }

    // 删除账户
    const deleteAccount = () => {
      const userEmail = userStore.userEmail
      if (deleteConfirmEmail.value !== userEmail) {
        message.error('邮箱地址不匹配')
        return
      }

      Modal.confirm({
        title: '最后确认',
        content: '您确定要删除账户吗？此操作无法撤销！',
        okType: 'danger',
        onOk () {
          // 这里实现删除账户的逻辑
          message.info('账户删除功能正在开发中')
          showDeleteModal.value = false
        }
      })
    }

    // 生命周期
    onMounted(() => {
      loadSettings()
    })

    return {
      loading,
      settings,
      showDeleteModal,
      deleteConfirmEmail,
      saveSettings,
      exportData,
      clearCache,
      showDeleteConfirm,
      deleteAccount
    }
  }
})
</script>

<style scoped>
.settings-container {
  padding: 0;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.settings-header h2 {
  margin: 0;
  color: #262626;
}

.setting-description {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
}

.data-management {
  background: #fafafa;
  padding: 20px;
  border-radius: 6px;
}

.action-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.action-item:last-child {
  border-bottom: none;
}

.action-item.dangerous {
  border-left: 3px solid #ff4d4f;
  padding-left: 16px;
  background: #fff2f0;
  border-radius: 4px;
}

.action-info h4 {
  margin: 0 0 4px 0;
  color: #262626;
}

.action-info p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.text-danger {
  color: #ff4d4f !important;
}

:deep(.ant-divider-inner-text) {
  font-weight: 500;
  color: #262626;
}

:deep(.ant-form-item-label > label) {
  font-weight: 500;
}
</style>
