<template>
  <div class="user-list">
    <!-- 页面标题 -->
    <PageHeader
      title="用户管理"
      description="管理系统用户账户和权限"
      :breadcrumbs="breadcrumbItems"
    >
      <template #actions>
        <a-space>
          <a-button type="primary" @click="goToCreate">
            <PlusOutlined />
            新增用户
          </a-button>
          <a-button @click="exportData">
            <ExportOutlined />
            导出数据
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <!-- 搜索表单 -->
    <a-card :bordered="false" class="search-card">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="关键词">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="用户名、邮箱、姓名"
            allow-clear
            @press-enter="handleSearch"
          />
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="active">正常</a-select-option>
            <a-select-option value="inactive">禁用</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="角色">
          <a-select
            v-model:value="searchForm.role"
            placeholder="请选择角色"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="admin">管理员</a-select-option>
            <a-select-option value="user">普通用户</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch" :loading="loading">
              搜索
            </a-button>
            <a-button @click="handleReset">
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 数据表格 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        :scroll="{ x: 1000 }"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'avatar'">
            <a-avatar :size="40" :src="record.avatar">
              {{ record.name?.charAt(0) }}
            </a-avatar>
          </template>

          <template v-if="column.key === 'userInfo'">
            <div class="user-info">
              <div class="name">{{ record.name }}</div>
              <div class="username">{{ record.username }}</div>
              <div class="email">{{ record.email }}</div>
            </div>
          </template>

          <template v-if="column.key === 'status'">
            <a-badge
              :status="record.status === 'active' ? 'success' : 'error'"
              :text="record.status === 'active' ? '正常' : '禁用'"
            />
          </template>

          <template v-if="column.key === 'role'">
            <a-tag :color="getRoleColor(record.role)">
              {{ getRoleText(record.role) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'lastLogin'">
            <span>{{ formatDate(record.last_login_at) }}</span>
          </template>

          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="editUser(record)">
                编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                :danger="record.status === 'active'"
                @click="toggleStatus(record)"
              >
                {{ record.status === 'active' ? '禁用' : '启用' }}
              </a-button>
              <a-button
                type="link"
                size="small"
                danger
                @click="deleteUserRecord(record)"
              >
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ExportOutlined
} from '@ant-design/icons-vue'

import PageHeader from '@/components/common/PageHeader.vue'
import { getUserList, deleteUser } from '@/api/user'
import { useStandardPagination, useStandardOperation } from '@/composables/useStandardApi'

/**
 * @typedef {import('@/types/api').User} User
 * @typedef {import('@/types/api').UserSearchParams} UserSearchParams
 */

export default defineComponent({
  name: 'UserListExample',
  components: {
    PageHeader,
    PlusOutlined,
    ExportOutlined
  },
  setup() {
    const router = useRouter()

    // 面包屑导航
    const breadcrumbItems = [
      { title: '首页', path: '/' },
      { title: '用户管理', path: '/user' },
      { title: '用户列表' }
    ]

    // 搜索表单
    /** @type {import('vue').Reactive<UserSearchParams>} */
    const searchForm = reactive({
      keyword: '',
      status: '',
      role: ''
    })

    // 表格列配置
    const columns = [
      {
        title: '头像',
        key: 'avatar',
        width: 80
      },
      {
        title: '用户信息',
        key: 'userInfo',
        width: 200
      },
      {
        title: '角色',
        key: 'role',
        width: 100
      },
      {
        title: '状态',
        key: 'status',
        width: 100
      },
      {
        title: '最后登录',
        key: 'lastLogin',
        width: 150,
        sorter: true
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        width: 150,
        sorter: true,
        customRender: ({ text }) => formatDate(text)
      },
      {
        title: '操作',
        key: 'actions',
        width: 200,
        fixed: 'right'
      }
    ]

    // 使用标准化的分页API调用
    const {
      loading,
      dataSource,
      pagination,
      hasData,
      isEmpty,
      fetchData,
      handleTableChange: handleStandardTableChange,
      search,
      reset,
      refresh
    } = useStandardPagination(getUserList, {
      defaultParams: {
        keyword: '',
        status: '',
        role: ''
      },
      pageSize: 10
    })

    // 使用标准化的删除操作
    const {
      loading: deleteLoading,
      execute: executeDelete
    } = useStandardOperation(deleteUser, {
      successMessage: '删除用户成功',
      onSuccess: () => {
        refresh() // 刷新列表
      }
    })

    // 搜索方法
    const handleSearch = () => {
      search({
        keyword: searchForm.keyword,
        status: searchForm.status,
        role: searchForm.role
      })
    }

    // 重置方法
    const handleReset = () => {
      Object.assign(searchForm, {
        keyword: '',
        status: '',
        role: ''
      })
      reset()
    }

    // 表格变化处理
    const handleTableChange = (pag, filters, sorter) => {
      handleStandardTableChange(pag, filters, sorter)
    }

    // 页面操作方法
    const goToCreate = () => {
      router.push('/user/create')
    }

    const viewDetail = (record) => {
      router.push(`/user/detail/${record.id}`)
    }

    const editUser = (record) => {
      router.push(`/user/edit/${record.id}`)
    }

    const toggleStatus = (record) => {
      const action = record.status === 'active' ? '禁用' : '启用'
      Modal.confirm({
        title: `确认${action}用户`,
        content: `确定要${action}用户"${record.name}"吗？`,
        onOk() {
          record.status = record.status === 'active' ? 'inactive' : 'active'
          message.success(`${action}成功`)
        }
      })
    }

    const deleteUserRecord = (record) => {
      Modal.confirm({
        title: '确认删除用户',
        content: `确定要删除用户"${record.name}"吗？此操作不可恢复。`,
        okType: 'danger',
        confirmLoading: deleteLoading.value,
        async onOk() {
          await executeDelete(record.id)
        }
      })
    }

    const exportData = () => {
      message.info('导出功能开发中...')
    }

    // 辅助方法
    const getRoleColor = (role) => {
      const colors = {
        admin: 'red',
        user: 'blue',
        manager: 'green'
      }
      return colors[role] || 'default'
    }

    const getRoleText = (role) => {
      const texts = {
        admin: '管理员',
        user: '普通用户',
        manager: '经理'
      }
      return texts[role] || '未知'
    }

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString()
    }

    // 生命周期
    onMounted(() => {
      fetchData()
    })

    return {
      breadcrumbItems,
      searchForm,
      columns,
      loading,
      dataSource,
      pagination,
      hasData,
      isEmpty,
      handleSearch,
      handleReset,
      handleTableChange,
      refresh,
      goToCreate,
      viewDetail,
      editUser,
      toggleStatus,
      deleteUserRecord,
      exportData,
      getRoleColor,
      getRoleText,
      formatDate
    }
  }
})
</script>

<style scoped>
.user-list {
  padding: 24px;
}

.search-card {
  margin-bottom: 16px;
}

.user-info .name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.user-info .username {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 2px;
}

.user-info .email {
  font-size: 12px;
  color: #1890ff;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
}
</style>
