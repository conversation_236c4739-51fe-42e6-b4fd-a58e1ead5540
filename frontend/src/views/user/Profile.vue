<template>
  <div class="user-profile">
    <!-- 页面标题 -->
    <PageHeader
      title="个人信息"
      description="管理您的个人资料和账户设置"
      :breadcrumbs="breadcrumbItems"
    >
      <template #actions>
        <a-space>
          <a-button
            v-if="!editMode"
            type="primary"
            @click="toggleEditMode"
          >
            <EditOutlined />
            编辑信息
          </a-button>
          <template v-else>
            <a-button
              type="primary"
              :loading="loading"
              @click="saveProfile"
            >
              <SaveOutlined />
              保存修改
            </a-button>
            <a-button @click="cancelEdit">
              <CloseOutlined />
              取消
            </a-button>
          </template>
        </a-space>
      </template>
    </PageHeader>

    <!-- 主要内容区域 -->
    <a-row :gutter="24">
      <!-- 左侧：头像和基本信息 -->
      <a-col :span="8">
        <a-card title="个人头像" :bordered="false">
          <div class="avatar-section">
            <div class="avatar-wrapper">
              <a-upload
                v-if="editMode"
                :show-upload-list="false"
                :before-upload="beforeUpload"
                :custom-request="handleAvatarUpload"
                accept="image/*"
                class="avatar-uploader"
              >
                <div class="upload-area">
                  <a-avatar
                    :size="120"
                    :src="getAvatarUrl(userInfo.avatar)"
                    class="user-avatar"
                  >
                    <template v-if="!userInfo.avatar">
                      <UserOutlined />
                    </template>
                  </a-avatar>
                  <div class="avatar-overlay">
                    <CameraOutlined />
                    <div>更换头像</div>
                  </div>
                </div>
              </a-upload>
              <a-avatar
                v-else
                :size="120"
                :src="getAvatarUrl(userInfo.avatar)"
                class="user-avatar"
              >
                <template v-if="!userInfo.avatar">
                  <UserOutlined />
                </template>
              </a-avatar>
            </div>

            <div class="user-basic-info">
              <h3>{{ userInfo.name || '未设置姓名' }}</h3>
              <p class="user-email">
                {{ userInfo.email }}
              </p>
              <a-tag :color="getRoleColor(userInfo.role)">
                {{ getRoleName(userInfo.role) }}
              </a-tag>
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧：详细信息表单 -->
      <a-col :span="16">
        <a-card title="个人资料" :bordered="false">
          <a-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
            layout="horizontal"
          >
            <!-- 基本信息 -->
            <a-divider orientation="left">
              基本信息
            </a-divider>

            <a-form-item label="姓名" name="name">
              <a-input
                v-if="editMode"
                v-model:value="formData.name"
                placeholder="请输入姓名"
                allow-clear
              />
              <span v-else>{{ userInfo.name || '未设置' }}</span>
            </a-form-item>

            <a-form-item label="邮箱">
              <span>{{ userInfo.email }}</span>
              <a-tag
                :color="userInfo.emailVerified ? 'green' : 'orange'"
                style="margin-left: 8px"
              >
                {{ userInfo.emailVerified ? '已验证' : '未验证' }}
              </a-tag>
            </a-form-item>

            <a-form-item label="手机号" name="phone">
              <a-input
                v-if="editMode"
                v-model:value="formData.phone"
                placeholder="请输入手机号"
                allow-clear
              />
              <template v-else>
                <span>{{ userInfo.phone || '未设置' }}</span>
                <a-tag
                  v-if="userInfo.phone"
                  :color="userInfo.phoneVerified ? 'green' : 'orange'"
                  style="margin-left: 8px"
                >
                  {{ userInfo.phoneVerified ? '已验证' : '未验证' }}
                </a-tag>
              </template>
            </a-form-item>

            <!-- 工作信息 -->
            <a-divider orientation="left">
              工作信息
            </a-divider>

            <a-form-item label="部门" name="department">
              <a-input
                v-if="editMode"
                v-model:value="formData.department"
                placeholder="请输入部门"
                allow-clear
              />
              <span v-else>{{ userInfo.department || '未设置' }}</span>
            </a-form-item>

            <a-form-item label="职位" name="position">
              <a-input
                v-if="editMode"
                v-model:value="formData.position"
                placeholder="请输入职位"
                allow-clear
              />
              <span v-else>{{ userInfo.position || '未设置' }}</span>
            </a-form-item>



            <!-- 其他信息 -->
            <a-divider orientation="left">
              其他信息
            </a-divider>





            <a-form-item label="首选语言" name="preferredLang">
              <a-select
                v-if="editMode"
                v-model:value="formData.preferredLang"
                placeholder="请选择首选语言"
              >
                <a-select-option value="zh-CN">
                  简体中文
                </a-select-option>
                <a-select-option value="zh-TW">
                  繁体中文
                </a-select-option>
                <a-select-option value="en-US">
                  English
                </a-select-option>
              </a-select>
              <span v-else>{{ getLanguageName(userInfo.preferredLang) }}</span>
            </a-form-item>

            <!-- 安全设置 -->
            <a-divider orientation="left">
              安全设置
            </a-divider>

            <a-form-item label="双因素认证">
              <a-switch
                v-if="editMode"
                v-model:checked="formData.twoFactorEnabled"
                checked-children="开启"
                un-checked-children="关闭"
              />
              <a-tag
                v-else
                :color="userInfo.twoFactorEnabled ? 'green' : 'red'"
              >
                {{ userInfo.twoFactorEnabled ? '已开启' : '未开启' }}
              </a-tag>
            </a-form-item>

            <a-form-item label="账户状态">
              <a-tag :color="userInfo.isActive ? 'green' : 'red'">
                {{ userInfo.isActive ? '正常' : '已禁用' }}
              </a-tag>
            </a-form-item>

            <a-form-item label="注册时间">
              <span>{{ formatDate(userInfo.createdAt) }}</span>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
    </a-row>

    <!-- 密码修改模态框 -->
    <a-modal
      v-model:open="passwordModalVisible"
      title="修改密码"
      :confirm-loading="passwordLoading"
      @ok="handlePasswordChange"
      @cancel="resetPasswordForm"
    >
      <a-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        layout="vertical"
      >
        <a-form-item label="当前密码" name="currentPassword">
          <a-input-password
            v-model:value="passwordForm.currentPassword"
            placeholder="请输入当前密码"
          />
        </a-form-item>
        <a-form-item label="新密码" name="newPassword">
          <a-input-password
            v-model:value="passwordForm.newPassword"
            placeholder="请输入新密码"
          />
        </a-form-item>
        <a-form-item label="确认新密码" name="confirmPassword">
          <a-input-password
            v-model:value="passwordForm.confirmPassword"
            placeholder="请再次输入新密码"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  UserOutlined,
  CameraOutlined
} from '@ant-design/icons-vue'

import PageHeader from '@/components/common/PageHeader.vue'
import { getUserInfo, updateProfile, changePassword } from '@/api/auth'
import { uploadFile } from '@/api/upload'
import { useUserStore } from '@/store/user'
import { getAvatarUrl } from '@/utils/constants'

export default defineComponent({
  name: 'UserProfile',
  components: {
    PageHeader,
    EditOutlined,
    SaveOutlined,
    CloseOutlined,
    UserOutlined,
    CameraOutlined
  },
  emits: [],
  setup () {
    // Store
    const _userStore = useUserStore()

    // 响应式数据
    const loading = ref(false)
    const editMode = ref(false)
    const passwordModalVisible = ref(false)
    const passwordLoading = ref(false)
    const formRef = ref()
    const passwordFormRef = ref()

    // 面包屑导航
    const breadcrumbItems = [
      { title: '首页', path: '/' },
      { title: '个人中心' }
    ]

    // 用户信息
    const userInfo = ref({
      id: '',
      email: '',
      user_name: '',
      name: '', // 保持向后兼容
      role: '',
      phone: '',
      department: '',
      position: '',

      avatar: '',
      preferredLang: 'zh-CN',
      twoFactorEnabled: false,
      emailVerified: false,
      phoneVerified: false,
      isActive: true,
      createdAt: ''
    })

    // 表单数据
    const formData = reactive({
      name: '',
      phone: '',
      department: '',
      position: '',
      company: '',
      bio: '',
      location: '',
      preferredLang: 'zh-CN',
      twoFactorEnabled: false
    })

    // 密码表单
    const passwordForm = reactive({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })

    // 表单验证规则
    const formRules = {
      name: [
        { required: true, message: '请输入姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' },
        { pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/, message: '姓名只能包含中文、英文和空格', trigger: 'blur' }
      ],
      phone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      department: [
        { max: 50, message: '部门名称不能超过50个字符', trigger: 'blur' }
      ],
      position: [
        { max: 50, message: '职位名称不能超过50个字符', trigger: 'blur' }
      ],


    }

    // 密码验证规则
    const passwordRules = {
      currentPassword: [
        { required: true, message: '请输入当前密码', trigger: 'blur' }
      ],
      newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
        { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/, message: '密码必须包含大小写字母和数字', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认新密码', trigger: 'blur' },
        {
          validator: (rule, value) => {
            if (value !== passwordForm.newPassword) {
              return Promise.reject('两次输入的密码不一致')
            }
            return Promise.resolve()
          },
          trigger: 'blur'
        }
      ]
    }

    // 计算属性
    const isFormChanged = computed(() => {
      return Object.keys(formData).some(key => {
        return formData[key] !== userInfo.value[key]
      })
    })

    // 方法
    const fetchUserInfo = async () => {
      try {
        loading.value = true
        const response = await getUserInfo()
        if (response.code === 200) {
          const userData = response.data
          // 处理字段映射
          userInfo.value = {
            ...userData,
            name: userData.user_name || userData.fullName || userData.name || '',
            user_name: userData.user_name || userData.fullName || userData.name || ''
          }

          // 同步表单数据
          Object.keys(formData).forEach(key => {
            if (key === 'name') {
              formData[key] = userInfo.value.name
            } else if (userInfo.value[key] !== undefined) {
              formData[key] = userInfo.value[key]
            }
          })
        }
      } catch (error) {
        message.error('获取用户信息失败')
        console.error('获取用户信息失败:', error)
      } finally {
        loading.value = false
      }
    }

    const toggleEditMode = () => {
      editMode.value = !editMode.value
      if (editMode.value) {
        // 进入编辑模式时，重置表单数据
        Object.keys(formData).forEach(key => {
          if (userInfo.value[key] !== undefined) {
            formData[key] = userInfo.value[key]
          }
        })
      }
    }

    const cancelEdit = () => {
      editMode.value = false
      // 重置表单数据
      Object.keys(formData).forEach(key => {
        if (userInfo.value[key] !== undefined) {
          formData[key] = userInfo.value[key]
        }
      })
    }

    const saveProfile = async () => {
      try {
        // 验证表单
        await formRef.value.validate()
        loading.value = true

        // 准备更新数据，确保字段名称正确
        const updateData = {
          name: formData.name,
          phone: formData.phone,
          department: formData.department,
          position: formData.position,
          bio: formData.bio,
          location: formData.location,
          preferredLang: formData.preferredLang
        }

        const response = await updateProfile(updateData)
        if (response.code === 200) {
          // 处理响应数据的字段映射
          const updatedData = {
            ...response.data,
            name: response.data.user_name || response.data.fullName || response.data.name || '',
            user_name: response.data.user_name || response.data.fullName || response.data.name || ''
          }

          userInfo.value = { ...userInfo.value, ...updatedData }
          editMode.value = false
          message.success('个人信息更新成功')

          // 更新用户store
          const userStore = useUserStore()
          userStore.setUser(userInfo.value)
        } else {
          throw new Error(response.message || '更新失败')
        }
      } catch (error) {
        if (error.errorFields) {
          message.error('请检查表单输入')
          // 聚焦到第一个错误字段
          const firstErrorField = error.errorFields[0]
          if (firstErrorField && firstErrorField.name) {
            const fieldName = Array.isArray(firstErrorField.name) ? firstErrorField.name[0] : firstErrorField.name
            const fieldElement = document.querySelector(`[name="${fieldName}"]`)
            if (fieldElement) {
              fieldElement.focus()
            }
          }
        } else {
          const errorMessage = error.response?.data?.message || error.message || '更新个人信息失败'
          message.error(errorMessage)
          console.error('更新个人信息失败:', error)
        }
      } finally {
        loading.value = false
      }
    }

    // 头像上传相关
    const beforeUpload = (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
      if (!isJpgOrPng) {
        message.error('只能上传 JPG/PNG 格式的图片!')
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        message.error('图片大小不能超过 2MB!')
        return false
      }
      return true
    }

    const handleAvatarUpload = async ({ file }) => {
      try {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('type', 'avatar')

        const response = await uploadFile(formData)
        if (response.code === 200) {
          userInfo.value.avatar = response.data.url
          message.success('头像上传成功')
          // 自动保存头像
          await updateProfile({ avatar: response.data.url })
        }
      } catch (error) {
        message.error('头像上传失败')
        console.error('头像上传失败:', error)
      }
    }

    // 密码修改相关
    const showPasswordModal = () => {
      passwordModalVisible.value = true
    }

    const resetPasswordForm = () => {
      passwordForm.currentPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
      passwordFormRef.value?.resetFields()
    }

    const handlePasswordChange = async () => {
      try {
        await passwordFormRef.value.validate()
        passwordLoading.value = true

        const response = await changePassword({
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword
        })

        if (response.code === 200) {
          message.success('密码修改成功')
          passwordModalVisible.value = false
          resetPasswordForm()
        }
      } catch (error) {
        if (error.errorFields) {
          message.error('请检查表单输入')
        } else {
          message.error('密码修改失败')
          console.error('密码修改失败:', error)
        }
      } finally {
        passwordLoading.value = false
      }
    }

    // 工具方法
    const getRoleColor = (role) => {
      const roleColors = {
        admin: 'red',
        manager: 'orange',
        user: 'blue',
        finance: 'green'
      }
      return roleColors[role] || 'default'
    }

    const getRoleName = (role) => {
      const roleNames = {
        admin: '系统管理员',
        manager: '部门经理',
        user: '普通用户',
        finance: '财务人员'
      }
      return roleNames[role] || '未知角色'
    }

    const getLanguageName = (lang) => {
      const langNames = {
        'zh-CN': '简体中文',
        'zh-TW': '繁体中文',
        'en-US': 'English'
      }
      return langNames[lang] || '简体中文'
    }

    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    // 生命周期
    onMounted(() => {
      fetchUserInfo()
    })

    return {
      // 响应式数据
      breadcrumbItems,
      loading,
      editMode,
      passwordModalVisible,
      passwordLoading,
      formRef,
      passwordFormRef,
      userInfo,
      formData,
      passwordForm,
      formRules,
      passwordRules,
      isFormChanged,

      // 方法
      fetchUserInfo,
      toggleEditMode,
      cancelEdit,
      saveProfile,
      beforeUpload,
      handleAvatarUpload,
      showPasswordModal,
      resetPasswordForm,
      handlePasswordChange,
      getRoleColor,
      getRoleName,
      getLanguageName,
      formatDate,
      getAvatarUrl
    }
  }
})
</script>

<style scoped>
.user-profile {
  padding: 0;
}

.avatar-section {
  text-align: center;
  margin-bottom: 24px;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 16px;
}

.avatar-uploader {
  display: block;
}

.upload-area {
  position: relative;
  cursor: pointer;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 50%;
}

.upload-area:hover .avatar-overlay {
  opacity: 1;
}

.user-avatar {
  border: 3px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-basic-info {
  text-align: center;
}

.user-basic-info h3 {
  margin: 8px 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.user-email {
  margin: 4px 0 8px 0;
  color: #8c8c8c;
  font-size: 14px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 24px 0 16px 0;
}

:deep(.ant-divider-inner-text) {
  font-weight: 600;
  color: #262626;
}

:deep(.ant-card) {
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}

@media (max-width: 768px) {
  .user-profile :deep(.ant-col) {
    margin-bottom: 16px;
  }

  .avatar-section {
    margin-bottom: 16px;
  }

  .user-avatar {
    width: 80px !important;
    height: 80px !important;
  }
}
</style>
