<!--
  税务政策状态更新组件
  功能：更新税务政策状态
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <a-modal
    :visible="visible"
    title="更新政策状态"
    :width="500"
    :confirm-loading="loading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="status-update-content">
      <div class="current-status">
        <span class="label">当前状态：</span>
        <a-tag :color="getStatusColor(currentStatus)">
          {{ getStatusText(currentStatus) }}
        </a-tag>
      </div>

      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="新状态" name="status">
          <a-select
            v-model:value="formData.status"
            placeholder="请选择新状态"
            @change="handleStatusChange"
          >
            <a-select-option
              v-for="status in availableStatuses"
              :key="status.value"
              :value="status.value"
              :disabled="status.value === currentStatus"
            >
              <div class="status-option">
                <a-tag :color="status.color" style="margin-right: 8px">
                  {{ status.label }}
                </a-tag>
                <span class="status-description">{{ status.description }}</span>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item v-if="showReasonField" label="变更原因" name="reason">
          <a-textarea
            v-model:value="formData.reason"
            placeholder="请输入状态变更原因"
            :rows="3"
          />
        </a-form-item>
      </a-form>

      <!-- 状态变更说明 -->
      <div v-if="selectedStatusInfo" class="status-info">
        <a-alert
          :message="selectedStatusInfo.title"
          :description="selectedStatusInfo.description"
          :type="selectedStatusInfo.type"
          show-icon
        />
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { POLICY_STATUSES, getStatusInfo } from '@/api/taxPolicy'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  currentStatus: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:visible', 'confirm'])

// 响应式数据
const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = reactive({
  status: '',
  reason: ''
})

// 表单验证规则
const formRules = {
  status: [
    { required: true, message: '请选择新状态', trigger: 'change' }
  ],
  reason: [
    { 
      validator: (rule, value) => {
        if (showReasonField.value && !value) {
          return Promise.reject('请输入变更原因')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 可用状态列表
const availableStatuses = computed(() => {
  return POLICY_STATUSES.map(status => ({
    ...status,
    description: getStatusDescription(status.value)
  }))
})

// 是否显示原因字段
const showReasonField = computed(() => {
  return ['expired', 'revoked'].includes(formData.status)
})

// 选中状态信息
const selectedStatusInfo = computed(() => {
  if (!formData.status) return null
  
  const statusInfoMap = {
    draft: {
      title: '设为草稿',
      description: '政策将变为草稿状态，不会对外生效。可以继续编辑和完善。',
      type: 'info'
    },
    effective: {
      title: '设为生效',
      description: '政策将正式生效，对符合条件的纳税人产生约束力。请确保政策内容准确无误。',
      type: 'success'
    },
    expired: {
      title: '设为过期',
      description: '政策将标记为过期状态，不再适用于新的业务场景。已有业务可能仍需遵循。',
      type: 'warning'
    },
    revoked: {
      title: '设为撤销',
      description: '政策将被撤销，立即停止生效。此操作通常用于政策有误或被新政策替代的情况。',
      type: 'error'
    }
  }
  
  return statusInfoMap[formData.status] || null
})

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  formData.status = ''
  formData.reason = ''
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 状态变更处理
const handleStatusChange = (value) => {
  // 清除原因字段验证
  if (formRef.value) {
    formRef.value.clearValidate(['reason'])
  }
}

// 确认操作
const handleConfirm = async () => {
  try {
    await formRef.value.validate()
    
    if (formData.status === props.currentStatus) {
      message.warning('新状态与当前状态相同')
      return
    }
    
    loading.value = true
    
    // 发送确认事件
    emit('confirm', formData.status, formData.reason)
    
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单填写是否正确')
    }
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
  resetForm()
}

// 辅助函数
const getStatusColor = (status) => {
  return getStatusInfo(status).color
}

const getStatusText = (status) => {
  return getStatusInfo(status).label
}

const getStatusDescription = (status) => {
  const descriptions = {
    draft: '政策处于编辑状态，尚未正式发布',
    effective: '政策正式生效，具有法律约束力',
    expired: '政策已过期，不再适用于新业务',
    revoked: '政策已被撤销，立即停止生效'
  }
  return descriptions[status] || ''
}
</script>

<style scoped>
.status-update-content {
  padding: 8px 0;
}

.current-status {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;
}

.current-status .label {
  font-weight: 500;
  margin-right: 8px;
  color: #595959;
}

.status-option {
  display: flex;
  align-items: center;
  width: 100%;
}

.status-description {
  color: #8c8c8c;
  font-size: 12px;
}

.status-info {
  margin-top: 16px;
}

:deep(.ant-select-dropdown .ant-select-item) {
  padding: 8px 12px;
}

:deep(.ant-select-dropdown .ant-select-item-option-content) {
  width: 100%;
}

:deep(.ant-alert) {
  border-radius: 6px;
}

:deep(.ant-alert-message) {
  font-weight: 500;
}
</style>
