<!--
  税务政策表单组件
  功能：创建和编辑税务政策
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <a-modal
    :visible="visible"
    :title="modalTitle"
    :width="1000"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <!-- 基本信息 -->
      <a-divider orientation="left">基本信息</a-divider>
      
      <a-form-item label="政策编号" name="policy_number">
        <a-input
          v-model:value="formData.policy_number"
          placeholder="请输入政策编号"
          :disabled="mode === 'edit'"
        />
      </a-form-item>

      <a-form-item label="政策标题" name="title">
        <a-input
          v-model:value="formData.title"
          placeholder="请输入政策标题"
        />
      </a-form-item>

      <a-form-item label="政策分类" name="category">
        <a-select
          v-model:value="formData.category"
          placeholder="请选择政策分类"
        >
          <a-select-option
            v-for="category in POLICY_CATEGORIES"
            :key="category.value"
            :value="category.value"
          >
            {{ category.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="发布机构" name="issuing_authority">
        <a-input
          v-model:value="formData.issuing_authority"
          placeholder="请输入发布机构"
        />
      </a-form-item>

      <a-form-item label="优先级" name="priority_level">
        <a-select
          v-model:value="formData.priority_level"
          placeholder="请选择优先级"
        >
          <a-select-option
            v-for="priority in PRIORITY_LEVELS"
            :key="priority.value"
            :value="priority.value"
          >
            {{ priority.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <!-- 日期信息 -->
      <a-divider orientation="left">日期信息</a-divider>

      <a-form-item label="发布日期" name="issue_date">
        <a-date-picker
          v-model:value="formData.issue_date"
          style="width: 100%"
          placeholder="请选择发布日期"
        />
      </a-form-item>

      <a-form-item label="生效日期" name="effective_date">
        <a-date-picker
          v-model:value="formData.effective_date"
          style="width: 100%"
          placeholder="请选择生效日期"
        />
      </a-form-item>

      <a-form-item label="到期日期" name="expiry_date">
        <a-date-picker
          v-model:value="formData.expiry_date"
          style="width: 100%"
          placeholder="请选择到期日期（可选）"
        />
      </a-form-item>

      <!-- 适用范围 -->
      <a-divider orientation="left">适用范围</a-divider>

      <a-form-item label="适用税种" name="tax_types">
        <a-textarea
          v-model:value="formData.tax_types"
          placeholder="请输入适用税种，多个税种用逗号分隔"
          :rows="2"
        />
      </a-form-item>

      <a-form-item label="适用地区" name="applicable_regions">
        <a-textarea
          v-model:value="formData.applicable_regions"
          placeholder="请输入适用地区，多个地区用逗号分隔"
          :rows="2"
        />
      </a-form-item>

      <a-form-item label="适用行业" name="applicable_industries">
        <a-textarea
          v-model:value="formData.applicable_industries"
          placeholder="请输入适用行业，多个行业用逗号分隔"
          :rows="2"
        />
      </a-form-item>

      <a-form-item label="适用企业" name="applicable_enterprises">
        <a-textarea
          v-model:value="formData.applicable_enterprises"
          placeholder="请输入适用企业类型，多个类型用逗号分隔"
          :rows="2"
        />
      </a-form-item>

      <!-- 政策内容 -->
      <a-divider orientation="left">政策内容</a-divider>

      <a-form-item label="政策摘要" name="policy_summary">
        <a-textarea
          v-model:value="formData.policy_summary"
          placeholder="请输入政策摘要"
          :rows="3"
        />
      </a-form-item>

      <a-form-item label="政策内容" name="policy_content">
        <a-textarea
          v-model:value="formData.policy_content"
          placeholder="请输入详细的政策内容"
          :rows="6"
        />
      </a-form-item>

      <a-form-item label="法律依据" name="legal_basis">
        <a-textarea
          v-model:value="formData.legal_basis"
          placeholder="请输入法律依据"
          :rows="3"
        />
      </a-form-item>

      <!-- 其他信息 -->
      <a-divider orientation="left">其他信息</a-divider>

      <a-form-item label="影响评估" name="impact_assessment">
        <a-textarea
          v-model:value="formData.impact_assessment"
          placeholder="请输入影响评估"
          :rows="3"
        />
      </a-form-item>

      <a-form-item label="实施指导" name="implementation_guidance">
        <a-textarea
          v-model:value="formData.implementation_guidance"
          placeholder="请输入实施指导"
          :rows="3"
        />
      </a-form-item>

      <a-form-item label="相关政策" name="related_policies">
        <a-textarea
          v-model:value="formData.related_policies"
          placeholder="请输入相关政策，多个政策用逗号分隔"
          :rows="2"
        />
      </a-form-item>

      <a-form-item label="关键词" name="keywords">
        <a-input
          v-model:value="formData.keywords"
          placeholder="请输入关键词，多个关键词用逗号分隔"
        />
      </a-form-item>

      <a-form-item label="附件" name="attachments">
        <a-textarea
          v-model:value="formData.attachments"
          placeholder="请输入附件信息或链接"
          :rows="2"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import moment from 'moment'
import { useTaxPolicyStore } from '@/store/taxPolicy'
import { POLICY_CATEGORIES, PRIORITY_LEVELS } from '@/api/taxPolicy'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'create', // 'create' | 'edit'
    validator: (value) => ['create', 'edit'].includes(value)
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 状态管理
const taxPolicyStore = useTaxPolicyStore()

// 响应式数据
const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = reactive({
  policy_number: '',
  title: '',
  category: '',
  tax_types: '',
  applicable_regions: '',
  applicable_industries: '',
  applicable_enterprises: '',
  policy_content: '',
  policy_summary: '',
  legal_basis: '',
  issuing_authority: '',
  issue_date: null,
  effective_date: null,
  expiry_date: null,
  priority_level: 'normal',
  impact_assessment: '',
  implementation_guidance: '',
  related_policies: '',
  attachments: '',
  keywords: ''
})

// 表单验证规则
const formRules = {
  policy_number: [
    { required: true, message: '请输入政策编号', trigger: 'blur' },
    { min: 3, max: 50, message: '政策编号长度应在3-50个字符之间', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入政策标题', trigger: 'blur' },
    { min: 5, max: 200, message: '政策标题长度应在5-200个字符之间', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择政策分类', trigger: 'change' }
  ],
  issuing_authority: [
    { required: true, message: '请输入发布机构', trigger: 'blur' },
    { max: 100, message: '发布机构长度不能超过100个字符', trigger: 'blur' }
  ],
  issue_date: [
    { required: true, message: '请选择发布日期', trigger: 'change' }
  ],
  effective_date: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ],
  policy_content: [
    { required: true, message: '请输入政策内容', trigger: 'blur' },
    { min: 10, message: '政策内容不能少于10个字符', trigger: 'blur' }
  ],
  priority_level: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 计算属性
const modalTitle = computed(() => {
  return props.mode === 'create' ? '新增税务政策' : '编辑税务政策'
})

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initFormData()
  }
})

watch(() => props.formData, (newVal) => {
  if (newVal && props.visible) {
    initFormData()
  }
}, { deep: true })

// 初始化表单数据
const initFormData = () => {
  nextTick(() => {
    if (props.mode === 'edit' && props.formData) {
      // 编辑模式，填充现有数据
      Object.keys(formData).forEach(key => {
        if (props.formData[key] !== undefined) {
          if (key.includes('_date') && props.formData[key]) {
            formData[key] = moment(props.formData[key])
          } else {
            formData[key] = props.formData[key]
          }
        }
      })
    } else {
      // 创建模式，重置表单
      resetForm()
    }
  })
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key.includes('_date')) {
      formData[key] = null
    } else if (key === 'priority_level') {
      formData[key] = 'normal'
    } else {
      formData[key] = ''
    }
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 准备提交数据
    const submitData = { ...formData }
    
    // 转换日期格式
    if (submitData.issue_date) {
      submitData.issue_date = submitData.issue_date.format('YYYY-MM-DD')
    }
    if (submitData.effective_date) {
      submitData.effective_date = submitData.effective_date.format('YYYY-MM-DD')
    }
    if (submitData.expiry_date) {
      submitData.expiry_date = submitData.expiry_date.format('YYYY-MM-DD')
    }
    
    // 调用API
    if (props.mode === 'create') {
      await taxPolicyStore.createTaxPolicy(submitData)
    } else {
      await taxPolicyStore.updateTaxPolicy(props.formData.id, submitData)
    }
    
    emit('success')
    handleCancel()
    
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      message.error('请检查表单填写是否正确')
    } else {
      // API错误
      message.error(error.message || '操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
  resetForm()
}
</script>

<style scoped>
:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 16px 0;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left::before) {
  width: 5%;
}

:deep(.ant-divider-inner-text) {
  font-weight: 600;
  color: #1890ff;
}
</style>
