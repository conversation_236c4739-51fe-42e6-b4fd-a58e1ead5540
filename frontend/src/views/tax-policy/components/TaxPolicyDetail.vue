<!--
  税务政策详情组件
  功能：展示税务政策详细信息
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <a-modal
    :visible="visible"
    title="税务政策详情"
    :width="1200"
    :footer="null"
    @cancel="handleCancel"
  >
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    
    <div v-else-if="policyData" class="policy-detail">
      <!-- 政策头部信息 -->
      <div class="policy-header">
        <div class="header-left">
          <h2 class="policy-title">{{ policyData.title }}</h2>
          <div class="policy-meta">
            <a-tag :color="getCategoryColor(policyData.category)">
              {{ getCategoryText(policyData.category) }}
            </a-tag>
            <a-tag :color="getStatusColor(policyData.status)">
              {{ getStatusText(policyData.status) }}
            </a-tag>
            <a-tag :color="getPriorityColor(policyData.priority_level)">
              {{ getPriorityText(policyData.priority_level) }}
            </a-tag>
          </div>
        </div>
        <div class="header-right">
          <a-statistic
            title="查看次数"
            :value="policyData.view_count"
            :value-style="{ color: '#1890ff' }"
          />
        </div>
      </div>

      <!-- 基本信息 -->
      <a-card title="基本信息" class="info-card">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="政策编号">
            {{ policyData.policy_number }}
          </a-descriptions-item>
          <a-descriptions-item label="发布机构">
            {{ policyData.issuing_authority }}
          </a-descriptions-item>
          <a-descriptions-item label="发布日期">
            {{ formatDate(policyData.issue_date) }}
          </a-descriptions-item>
          <a-descriptions-item label="生效日期">
            {{ formatDate(policyData.effective_date) }}
          </a-descriptions-item>
          <a-descriptions-item label="到期日期">
            {{ policyData.expiry_date ? formatDate(policyData.expiry_date) : '永久有效' }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(policyData.created_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ formatDateTime(policyData.updated_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="创建人">
            {{ policyData.creator?.username || '系统' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 适用范围 -->
      <a-card title="适用范围" class="info-card">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="适用税种">
            <div v-if="policyData.tax_types">
              <a-tag
                v-for="taxType in policyData.tax_types.split(',')"
                :key="taxType.trim()"
                color="blue"
              >
                {{ taxType.trim() }}
              </a-tag>
            </div>
            <span v-else class="text-muted">未指定</span>
          </a-descriptions-item>
          <a-descriptions-item label="适用地区">
            <div v-if="policyData.applicable_regions">
              <a-tag
                v-for="region in policyData.applicable_regions.split(',')"
                :key="region.trim()"
                color="green"
              >
                {{ region.trim() }}
              </a-tag>
            </div>
            <span v-else class="text-muted">未指定</span>
          </a-descriptions-item>
          <a-descriptions-item label="适用行业">
            <div v-if="policyData.applicable_industries">
              <a-tag
                v-for="industry in policyData.applicable_industries.split(',')"
                :key="industry.trim()"
                color="orange"
              >
                {{ industry.trim() }}
              </a-tag>
            </div>
            <span v-else class="text-muted">未指定</span>
          </a-descriptions-item>
          <a-descriptions-item label="适用企业">
            <div v-if="policyData.applicable_enterprises">
              <a-tag
                v-for="enterprise in policyData.applicable_enterprises.split(',')"
                :key="enterprise.trim()"
                color="purple"
              >
                {{ enterprise.trim() }}
              </a-tag>
            </div>
            <span v-else class="text-muted">未指定</span>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 政策内容 -->
      <a-card title="政策内容" class="info-card">
        <div v-if="policyData.policy_summary" class="content-section">
          <h4>政策摘要</h4>
          <div class="content-text">{{ policyData.policy_summary }}</div>
        </div>
        
        <div class="content-section">
          <h4>详细内容</h4>
          <div class="content-text">{{ policyData.policy_content }}</div>
        </div>

        <div v-if="policyData.legal_basis" class="content-section">
          <h4>法律依据</h4>
          <div class="content-text">{{ policyData.legal_basis }}</div>
        </div>
      </a-card>

      <!-- 实施指导 -->
      <a-card v-if="policyData.impact_assessment || policyData.implementation_guidance" title="实施指导" class="info-card">
        <div v-if="policyData.impact_assessment" class="content-section">
          <h4>影响评估</h4>
          <div class="content-text">{{ policyData.impact_assessment }}</div>
        </div>

        <div v-if="policyData.implementation_guidance" class="content-section">
          <h4>实施指导</h4>
          <div class="content-text">{{ policyData.implementation_guidance }}</div>
        </div>
      </a-card>

      <!-- 相关信息 -->
      <a-card title="相关信息" class="info-card">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item v-if="policyData.related_policies" label="相关政策">
            <div>
              <a-tag
                v-for="policy in policyData.related_policies.split(',')"
                :key="policy.trim()"
                color="cyan"
              >
                {{ policy.trim() }}
              </a-tag>
            </div>
          </a-descriptions-item>
          <a-descriptions-item v-if="policyData.keywords" label="关键词">
            <div>
              <a-tag
                v-for="keyword in policyData.keywords.split(',')"
                :key="keyword.trim()"
                color="geekblue"
              >
                {{ keyword.trim() }}
              </a-tag>
            </div>
          </a-descriptions-item>
          <a-descriptions-item v-if="policyData.attachments" label="附件">
            <div class="content-text">{{ policyData.attachments }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button type="primary" @click="handleEdit">
            编辑政策
          </a-button>
          <a-button @click="handlePrint">
            打印
          </a-button>
          <a-button @click="handleExport">
            导出
          </a-button>
        </a-space>
      </div>
    </div>

    <div v-else class="empty-container">
      <a-empty description="未找到政策信息" />
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import { useTaxPolicyStore } from '@/store/taxPolicy'
import { formatDate, formatDateTime } from '@/utils/date'
import { getCategoryInfo, getStatusInfo, getPriorityInfo } from '@/api/taxPolicy'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  policyId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:visible', 'edit'])

// 状态管理
const taxPolicyStore = useTaxPolicyStore()

// 响应式数据
const loading = ref(false)
const policyData = ref(null)

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal && props.policyId) {
    loadPolicyDetail()
  }
})

watch(() => props.policyId, (newVal) => {
  if (newVal && props.visible) {
    loadPolicyDetail()
  }
})

// 加载政策详情
const loadPolicyDetail = async () => {
  if (!props.policyId) return
  
  loading.value = true
  try {
    const data = await taxPolicyStore.getTaxPolicyById(props.policyId)
    policyData.value = data
  } catch (error) {
    message.error('加载政策详情失败：' + error.message)
    policyData.value = null
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
  policyData.value = null
}

// 编辑政策
const handleEdit = () => {
  emit('edit', policyData.value)
  handleCancel()
}

// 打印政策
const handlePrint = () => {
  window.print()
}

// 导出政策
const handleExport = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中...')
}

// 辅助函数
const getCategoryColor = (category) => {
  return getCategoryInfo(category).color
}

const getCategoryText = (category) => {
  return getCategoryInfo(category).label
}

const getStatusColor = (status) => {
  return getStatusInfo(status).color
}

const getStatusText = (status) => {
  return getStatusInfo(status).label
}

const getPriorityColor = (priority) => {
  return getPriorityInfo(priority).color
}

const getPriorityText = (priority) => {
  return getPriorityInfo(priority).label
}
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.policy-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.policy-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.policy-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.policy-meta {
  display: flex;
  gap: 8px;
}

.info-card {
  margin-bottom: 16px;
}

.content-section {
  margin-bottom: 16px;
}

.content-section:last-child {
  margin-bottom: 0;
}

.content-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #595959;
}

.content-text {
  line-height: 1.6;
  color: #262626;
  white-space: pre-wrap;
  word-break: break-word;
}

.text-muted {
  color: #8c8c8c;
  font-style: italic;
}

.action-buttons {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  background-color: #fafafa;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}
</style>
