<!--
  税务政策管理主页面
  功能：税务政策列表展示、搜索、筛选、批量操作
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <div class="tax-policy-container">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">税务政策管理</h1>
        <p class="page-description">管理和查看税务政策信息</p>
      </div>
      <div class="header-right">
        <a-button type="primary" @click="handleCreate">
          <template #icon><PlusOutlined /></template>
          新增政策
        </a-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <a-card :bordered="false">
        <a-form
          :model="searchForm"
          layout="inline"
          @finish="handleSearch"
        >
          <a-form-item label="关键词" name="keyword">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="请输入政策标题或内容关键词"
              style="width: 200px"
              allow-clear
            />
          </a-form-item>
          
          <a-form-item label="政策分类" name="category">
            <a-select
              v-model:value="searchForm.category"
              placeholder="请选择政策分类"
              style="width: 150px"
              allow-clear
            >
              <a-select-option value="tax_law">税法法规</a-select-option>
              <a-select-option value="tax_policy">税收政策</a-select-option>
              <a-select-option value="tax_notice">税务通知</a-select-option>
              <a-select-option value="tax_guidance">税务指导</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="状态" name="status">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="effective">生效</a-select-option>
              <a-select-option value="expired">过期</a-select-option>
              <a-select-option value="revoked">撤销</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="优先级" name="priority_level">
            <a-select
              v-model:value="searchForm.priority_level"
              placeholder="请选择优先级"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="high">高</a-select-option>
              <a-select-option value="normal">普通</a-select-option>
              <a-select-option value="low">低</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item>
            <a-button type="primary" html-type="submit">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
            <a-button style="margin-left: 8px" @click="handleReset">
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedRowKeys.length > 0" class="batch-actions">
      <a-alert
        :message="`已选择 ${selectedRowKeys.length} 项`"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      >
        <template #action>
          <a-space>
            <a-button size="small" @click="handleBatchStatusUpdate('effective')">
              批量生效
            </a-button>
            <a-button size="small" @click="handleBatchStatusUpdate('revoked')">
              批量撤销
            </a-button>
            <a-button size="small" danger @click="handleBatchDelete">
              批量删除
            </a-button>
          </a-space>
        </template>
      </a-alert>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <a-card :bordered="false">
        <a-table
          :columns="columns"
          :data-source="tableData"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          row-key="id"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'title'">
              <a @click="handleView(record)">{{ record.title }}</a>
            </template>
            
            <template v-if="column.key === 'category'">
              <a-tag :color="getCategoryColor(record.category)">
                {{ getCategoryText(record.category) }}
              </a-tag>
            </template>

            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>

            <template v-if="column.key === 'priority_level'">
              <a-tag :color="getPriorityColor(record.priority_level)">
                {{ getPriorityText(record.priority_level) }}
              </a-tag>
            </template>

            <template v-if="column.key === 'effective_date'">
              {{ formatDate(record.effective_date) }}
            </template>

            <template v-if="column.key === 'expiry_date'">
              {{ record.expiry_date ? formatDate(record.expiry_date) : '永久有效' }}
            </template>

            <template v-if="column.key === 'view_count'">
              <a-statistic
                :value="record.view_count"
                :value-style="{ fontSize: '14px' }"
              />
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="handleView(record)">
                  查看
                </a-button>
                <a-button type="link" size="small" @click="handleEdit(record)">
                  编辑
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多 <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleMenuAction(key, record)">
                      <a-menu-item key="status">更新状态</a-menu-item>
                      <a-menu-item key="copy">复制政策</a-menu-item>
                      <a-menu-divider />
                      <a-menu-item key="delete" danger>删除</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 创建/编辑弹窗 -->
    <TaxPolicyForm
      v-model:visible="formVisible"
      :form-data="currentRecord"
      :mode="formMode"
      @success="handleFormSuccess"
    />

    <!-- 详情弹窗 -->
    <TaxPolicyDetail
      v-model:visible="detailVisible"
      :policy-id="currentRecord?.id"
    />

    <!-- 状态更新弹窗 -->
    <StatusUpdateModal
      v-model:visible="statusModalVisible"
      :current-status="currentRecord?.status"
      @confirm="handleStatusUpdate"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { useTaxPolicyStore } from '@/store/taxPolicy'
import { formatDate } from '@/utils/date'
import TaxPolicyForm from './components/TaxPolicyForm.vue'
import TaxPolicyDetail from './components/TaxPolicyDetail.vue'
import StatusUpdateModal from './components/StatusUpdateModal.vue'

// 状态管理
const taxPolicyStore = useTaxPolicyStore()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedRowKeys = ref([])
const formVisible = ref(false)
const detailVisible = ref(false)
const statusModalVisible = ref(false)
const currentRecord = ref(null)
const formMode = ref('create') // 'create' | 'edit'

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: undefined,
  status: undefined,
  priority_level: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列配置
const columns = [
  {
    title: '政策标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    ellipsis: true
  },
  {
    title: '政策编号',
    dataIndex: 'policy_number',
    key: 'policy_number',
    width: 120
  },
  {
    title: '分类',
    dataIndex: 'category',
    key: 'category',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '优先级',
    dataIndex: 'priority_level',
    key: 'priority_level',
    width: 80
  },
  {
    title: '发布机构',
    dataIndex: 'issuing_authority',
    key: 'issuing_authority',
    width: 120,
    ellipsis: true
  },
  {
    title: '生效日期',
    dataIndex: 'effective_date',
    key: 'effective_date',
    width: 100
  },
  {
    title: '到期日期',
    dataIndex: 'expiry_date',
    key: 'expiry_date',
    width: 100
  },
  {
    title: '查看次数',
    dataIndex: 'view_count',
    key: 'view_count',
    width: 80,
    align: 'center'
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}))

// 初始化
onMounted(() => {
  loadData()
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    const response = await taxPolicyStore.getTaxPolicies(params)
    tableData.value = response.list || []
    pagination.total = response.total || 0
  } catch (error) {
    message.error('加载数据失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'keyword' ? '' : undefined
  })
  pagination.current = 1
  loadData()
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

// 创建政策
const handleCreate = () => {
  currentRecord.value = null
  formMode.value = 'create'
  formVisible.value = true
}

// 编辑政策
const handleEdit = (record) => {
  currentRecord.value = { ...record }
  formMode.value = 'edit'
  formVisible.value = true
}

// 查看详情
const handleView = (record) => {
  currentRecord.value = record
  detailVisible.value = true
}

// 表单成功回调
const handleFormSuccess = () => {
  formVisible.value = false
  loadData()
  message.success(formMode.value === 'create' ? '创建成功' : '更新成功')
}

// 菜单操作处理
const handleMenuAction = (key, record) => {
  currentRecord.value = record
  
  switch (key) {
    case 'status':
      statusModalVisible.value = true
      break
    case 'copy':
      handleCopy(record)
      break
    case 'delete':
      handleDelete(record)
      break
  }
}

// 复制政策
const handleCopy = (record) => {
  const copyData = {
    ...record,
    id: undefined,
    policy_number: record.policy_number + '_copy',
    title: record.title + ' (副本)',
    status: 'draft'
  }
  currentRecord.value = copyData
  formMode.value = 'create'
  formVisible.value = true
}

// 删除政策
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除政策"${record.title}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await taxPolicyStore.deleteTaxPolicy(record.id)
        message.success('删除成功')
        loadData()
      } catch (error) {
        message.error('删除失败：' + error.message)
      }
    }
  })
}

// 状态更新
const handleStatusUpdate = async (status) => {
  try {
    await taxPolicyStore.updateTaxPolicyStatus(currentRecord.value.id, status)
    message.success('状态更新成功')
    statusModalVisible.value = false
    loadData()
  } catch (error) {
    message.error('状态更新失败：' + error.message)
  }
}

// 批量删除
const handleBatchDelete = () => {
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个政策吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await taxPolicyStore.batchDeleteTaxPolicies(selectedRowKeys.value)
        message.success('批量删除成功')
        selectedRowKeys.value = []
        loadData()
      } catch (error) {
        message.error('批量删除失败：' + error.message)
      }
    }
  })
}

// 批量状态更新
const handleBatchStatusUpdate = (status) => {
  const statusText = getStatusText(status)
  Modal.confirm({
    title: '确认批量操作',
    content: `确定要将选中的 ${selectedRowKeys.value.length} 个政策状态更新为"${statusText}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await taxPolicyStore.batchUpdateTaxPolicyStatus(selectedRowKeys.value, status)
        message.success('批量更新成功')
        selectedRowKeys.value = []
        loadData()
      } catch (error) {
        message.error('批量更新失败：' + error.message)
      }
    }
  })
}

// 辅助函数
const getCategoryColor = (category) => {
  const colors = {
    tax_law: 'red',
    tax_policy: 'blue',
    tax_notice: 'orange',
    tax_guidance: 'green'
  }
  return colors[category] || 'default'
}

const getCategoryText = (category) => {
  const texts = {
    tax_law: '税法法规',
    tax_policy: '税收政策',
    tax_notice: '税务通知',
    tax_guidance: '税务指导'
  }
  return texts[category] || category
}

const getStatusColor = (status) => {
  const colors = {
    draft: 'default',
    effective: 'success',
    expired: 'warning',
    revoked: 'error'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    effective: '生效',
    expired: '过期',
    revoked: '撤销'
  }
  return texts[status] || status
}

const getPriorityColor = (priority) => {
  const colors = {
    high: 'red',
    normal: 'blue',
    low: 'default'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    normal: '普通',
    low: '低'
  }
  return texts[priority] || priority
}
</script>

<style scoped>
.tax-policy-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.page-description {
  margin: 4px 0 0 0;
  color: #8c8c8c;
  font-size: 14px;
}

.search-section {
  margin-bottom: 16px;
}

.batch-actions {
  margin-bottom: 16px;
}

.table-section {
  background: #fff;
}
</style>
