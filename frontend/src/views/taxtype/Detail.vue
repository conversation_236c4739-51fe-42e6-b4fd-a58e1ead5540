<template>
  <div class="tax-type-detail">
    <!-- 页面标题 -->
    <PageHeader
      :title="taxType?.name || '税种详情'"
      description="查看税种配置详细信息"
      :breadcrumbs="breadcrumbItems"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
          <a-button type="primary" @click="editTaxType">
            <EditOutlined />
            编辑
          </a-button>
          <a-button @click="manageRules">
            <SettingOutlined />
            税则配置
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>

    <!-- 详情内容 -->
    <div v-else>
      <!-- 基本信息 -->
      <a-card title="基本信息" :bordered="false" class="detail-card">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="detail-item">
              <div class="detail-label">
                税种编码
              </div>
              <div class="detail-value">
                {{ taxType.code || '-' }}
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <div class="detail-label">
                税种名称
              </div>
              <div class="detail-value">
                {{ taxType.name || '-' }}
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <div class="detail-label">
                启用状态
              </div>
              <div class="detail-value">
                <a-badge
                  :status="taxType.is_active ? 'success' : 'default'"
                  :text="taxType.is_active ? '启用' : '禁用'"
                />
              </div>
            </div>
          </a-col>
          <a-col :span="24">
            <div class="detail-item">
              <div class="detail-label">
                税种描述
              </div>
              <div class="detail-value">
                {{ taxType.description || '-' }}
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 分类配置 -->
      <a-card title="分类配置" :bordered="false" class="detail-card">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="detail-item">
              <div class="detail-label">
                税种分类
              </div>
              <div class="detail-value">
                <a-tag :color="getCategoryColor(taxType.category)">
                  {{ getCategoryName(taxType.category) }}
                </a-tag>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <div class="detail-label">
                税率类型
              </div>
              <div class="detail-value">
                <a-tag :color="getRateTypeColor(taxType.rate_type)">
                  {{ getRateTypeName(taxType.rate_type) }}
                </a-tag>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <div class="detail-label">
                默认税率
              </div>
              <div class="detail-value">
                <span v-if="taxType.tax_rate" class="tax-rate">
                  {{ formatRate(taxType.tax_rate) }}
                </span>
                <span v-else>-</span>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 申报配置 -->
      <a-card title="申报配置" :bordered="false" class="detail-card">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="detail-item">
              <div class="detail-label">
                申报频率
              </div>
              <div class="detail-value">
                <a-tag>{{ getFilingFrequencyName(taxType.filing_frequency) }}</a-tag>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <div class="detail-label">
                申报日
              </div>
              <div class="detail-value">
                {{ taxType.filing_day ? `每月${taxType.filing_day}日` : '-' }}
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <div class="detail-label">
                缴款期限
              </div>
              <div class="detail-value">
                {{ taxType.payment_deadline ? `${taxType.payment_deadline}天` : '-' }}
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 法律依据 -->
      <a-card title="法律依据" :bordered="false" class="detail-card">
        <a-row :gutter="24">
          <a-col :span="12">
            <div class="detail-item">
              <div class="detail-label">
                法律依据
              </div>
              <div class="detail-value text-content">
                {{ taxType.legal_basis || '-' }}
              </div>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="detail-item">
              <div class="detail-label">
                征收机关
              </div>
              <div class="detail-value">
                {{ taxType.tax_authority || '-' }}
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 系统信息 -->
      <a-card title="系统信息" :bordered="false" class="detail-card">
        <a-row :gutter="24">
          <a-col :span="12">
            <div class="detail-item">
              <div class="detail-label">
                创建时间
              </div>
              <div class="detail-value">
                {{ formatDateTime(taxType.created_at) }}
              </div>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="detail-item">
              <div class="detail-label">
                更新时间
              </div>
              <div class="detail-value">
                {{ formatDateTime(taxType.updated_at) }}
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  EditOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

import PageHeader from '@/components/common/PageHeader.vue'
import { getTaxTypeDetail } from '@/api/taxType'

export default defineComponent({
  name: 'TaxTypeDetail',
  components: {
    PageHeader,
    ArrowLeftOutlined,
    EditOutlined,
    SettingOutlined
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const loading = ref(false)

    // 获取税种ID
    const taxTypeId = route.params.id

    // 面包屑导航
    const breadcrumbItems = [
      { title: '首页', path: '/' },
      { title: '税种配置中心', path: '/tax-type/list' },
      { title: '税种详情' }
    ]

    // 税种数据
    const taxType = reactive({})

    // 方法
    const fetchTaxTypeDetail = async () => {
      loading.value = true
      try {
        const response = await getTaxTypeDetail(taxTypeId)
        if (response.code === 200) {
          Object.assign(taxType, response.data)
        } else {
          message.error(response.message || '获取税种详情失败')
          router.push('/tax-type/list')
        }
      } catch (error) {
        console.error('获取税种详情失败:', error)
        if (error.response?.status === 404) {
          message.error('税种不存在')
          router.push('/tax-type/list')
        } else {
          message.error('获取税种详情失败')
        }
      } finally {
        loading.value = false
      }
    }

    const goBack = () => {
      router.go(-1)
    }

    const editTaxType = () => {
      router.push(`/tax-type/edit/${taxTypeId}`)
    }

    const manageRules = () => {
      router.push(`/tax-type/rules/${taxTypeId}`)
    }

    // 辅助方法
    const getCategoryColor = (category) => {
      const colors = {
        shared: 'blue',
        central: 'green',
        local: 'orange'
      }
      return colors[category] || 'default'
    }

    const getCategoryName = (category) => {
      const names = {
        shared: '共享税',
        central: '中央税',
        local: '地方税'
      }
      return names[category] || category
    }

    const getRateTypeColor = (rateType) => {
      const colors = {
        percentage: 'blue',
        fixed: 'green',
        progressive: 'purple'
      }
      return colors[rateType] || 'default'
    }

    const getRateTypeName = (rateType) => {
      const names = {
        percentage: '比例税率',
        fixed: '定额税率',
        progressive: '累进税率'
      }
      return names[rateType] || rateType
    }

    const getFilingFrequencyName = (frequency) => {
      const names = {
        monthly: '月报',
        quarterly: '季报',
        annually: '年报',
        other: '其他'
      }
      return names[frequency] || frequency
    }

    const formatRate = (rate) => {
      if (!rate) return '-'
      return `${(parseFloat(rate) * 100).toFixed(2)}%`
    }

    const formatDateTime = (dateTime) => {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    }

    // 生命周期
    onMounted(() => {
      fetchTaxTypeDetail()
    })

    return {
      breadcrumbItems,
      loading,
      taxType,
      goBack,
      editTaxType,
      manageRules,
      getCategoryColor,
      getCategoryName,
      getRateTypeColor,
      getRateTypeName,
      getFilingFrequencyName,
      formatRate,
      formatDateTime
    }
  }
})
</script>

<style scoped>
.tax-type-detail {
  padding: 0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-item {
  margin-bottom: 16px;
}

.detail-label {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.detail-value {
  font-size: 14px;
  color: #262626;
  word-break: break-all;
}

.text-content {
  line-height: 1.6;
  white-space: pre-wrap;
}

.tax-rate {
  font-weight: 500;
  color: #1890ff;
}
</style>
