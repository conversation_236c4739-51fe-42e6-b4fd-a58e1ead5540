<template>
  <div class="tax-rules">
    <!-- 页面标题 -->
    <PageHeader
      :title="`${taxType?.name || ''} - 计税规则配置`"
      description="管理税种的计税规则和计算公式"
      :breadcrumbs="breadcrumbItems"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
          <a-button type="primary" @click="showCreateModal">
            <PlusOutlined />
            新增规则
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <!-- 税种基本信息 -->
    <a-card :bordered="false" class="tax-type-info">
      <a-row :gutter="24">
        <a-col :span="6">
          <div class="info-item">
            <div class="info-label">
              税种编码
            </div>
            <div class="info-value">
              {{ taxType.code }}
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="info-item">
            <div class="info-label">
              税种名称
            </div>
            <div class="info-value">
              {{ taxType.name }}
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="info-item">
            <div class="info-label">
              税率类型
            </div>
            <div class="info-value">
              <a-tag :color="getRateTypeColor(taxType.rate_type)">
                {{ getRateTypeName(taxType.rate_type) }}
              </a-tag>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="info-item">
            <div class="info-label">
              默认税率
            </div>
            <div class="info-value">
              {{ formatRate(taxType.tax_rate) }}
            </div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 规则列表 -->
    <a-card :bordered="false" title="计税规则列表">
      <template #extra>
        <a-space>
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索规则名称"
            style="width: 200px"
            @search="handleSearch"
          />
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div class="rule-name">
              <div class="name">
                {{ record.name }}
              </div>
              <div class="description">
                {{ record.description || '-' }}
              </div>
            </div>
          </template>

          <template v-if="column.key === 'condition'">
            <div class="rule-condition">
              {{ record.condition || '-' }}
            </div>
          </template>

          <template v-if="column.key === 'formula'">
            <div class="rule-formula">
              <code>{{ record.formula || '-' }}</code>
            </div>
          </template>

          <template v-if="column.key === 'effective_date'">
            {{ formatDate(record.effective_date) }}
          </template>

          <template v-if="column.key === 'expiry_date'">
            {{ record.expiry_date ? formatDate(record.expiry_date) : '永久有效' }}
          </template>

          <template v-if="column.key === 'status'">
            <a-badge
              :status="getRuleStatus(record).status"
              :text="getRuleStatus(record).text"
            />
          </template>

          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewRule(record)">
                <EyeOutlined />
                查看
              </a-button>
              <a-button type="link" size="small" @click="editRule(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                danger
                @click="deleteRule(record)"
              >
                <DeleteOutlined />
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑规则弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="800px"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="modalFormRef"
        :model="modalForm"
        :rules="modalRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="规则名称" name="name">
              <a-input
                v-model:value="modalForm.name"
                placeholder="请输入规则名称"
                :maxlength="255"
              />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="规则描述" name="description">
              <a-textarea
                v-model:value="modalForm.description"
                placeholder="请输入规则描述"
                :rows="3"
                :maxlength="500"
                show-count
              />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="适用条件" name="condition">
              <a-textarea
                v-model:value="modalForm.condition"
                placeholder="请输入规则适用条件，如：销售额 > 500万"
                :rows="2"
                :maxlength="1000"
                show-count
              />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="计算公式" name="formula">
              <a-textarea
                v-model:value="modalForm.formula"
                placeholder="请输入计算公式，如：销售额 * 0.13"
                :rows="3"
                :maxlength="1000"
                show-count
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="生效日期" name="effective_date">
              <a-date-picker
                v-model:value="modalForm.effective_date"
                style="width: 100%"
                placeholder="请选择生效日期"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="失效日期" name="expiry_date">
              <a-date-picker
                v-model:value="modalForm.expiry_date"
                style="width: 100%"
                placeholder="请选择失效日期（可选）"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  ArrowLeftOutlined,
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

import PageHeader from '@/components/common/PageHeader.vue'
import {
  getTaxTypeDetail,
  getTaxRules,
  createTaxRule,
  updateTaxRule,
  deleteTaxRule
} from '@/api/taxType'

export default defineComponent({
  name: 'TaxRules',
  components: {
    PageHeader,
    ArrowLeftOutlined,
    PlusOutlined,
    EyeOutlined,
    EditOutlined,
    DeleteOutlined
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const modalFormRef = ref()
    const loading = ref(false)
    const modalVisible = ref(false)
    const modalLoading = ref(false)
    const searchKeyword = ref('')

    // 获取税种ID
    const taxTypeId = route.params.id

    // 面包屑导航
    const breadcrumbItems = [
      { title: '首页', path: '/' },
      { title: '税种配置中心', path: '/tax-type/list' },
      { title: '计税规则配置' }
    ]

    // 数据
    const taxType = reactive({})
    const dataSource = ref([])
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条记录`
    })

    // 弹窗相关
    const modalTitle = ref('')
    const modalMode = ref('create') // create | edit
    const currentRule = ref(null)
    const modalForm = reactive({
      name: '',
      description: '',
      condition: '',
      formula: '',
      effective_date: null,
      expiry_date: null
    })

    // 表格列配置
    const columns = [
      {
        title: '规则名称',
        key: 'name',
        width: 200
      },
      {
        title: '适用条件',
        key: 'condition',
        width: 200,
        ellipsis: true
      },
      {
        title: '计算公式',
        key: 'formula',
        width: 200,
        ellipsis: true
      },
      {
        title: '生效日期',
        key: 'effective_date',
        width: 120
      },
      {
        title: '失效日期',
        key: 'expiry_date',
        width: 120
      },
      {
        title: '状态',
        key: 'status',
        width: 100
      },
      {
        title: '操作',
        key: 'actions',
        width: 180,
        fixed: 'right'
      }
    ]

    // 表单验证规则
    const modalRules = {
      name: [
        { required: true, message: '请输入规则名称', trigger: 'blur' },
        { min: 2, max: 255, message: '规则名称长度为2-255个字符', trigger: 'blur' }
      ],
      effective_date: [
        { required: true, message: '请选择生效日期', trigger: 'change' }
      ]
    }

    // 方法
    const fetchTaxTypeDetail = async () => {
      try {
        const response = await getTaxTypeDetail(taxTypeId)
        if (response.code === 200) {
          Object.assign(taxType, response.data)
        }
      } catch (error) {
        console.error('获取税种详情失败:', error)
      }
    }

    const fetchRules = async () => {
      loading.value = true
      try {
        const response = await getTaxRules(taxTypeId)
        if (response.code === 200) {
          dataSource.value = response.data || []
          pagination.total = dataSource.value.length
        } else {
          message.error(response.message || '获取规则列表失败')
        }
      } catch (error) {
        console.error('获取规则列表失败:', error)
        message.error('获取规则列表失败')
      } finally {
        loading.value = false
      }
    }

    const goBack = () => {
      router.go(-1)
    }

    const handleSearch = () => {
      // 实现搜索逻辑
      fetchRules()
    }

    const handleTableChange = (pag) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      fetchRules()
    }

    // 弹窗相关方法
    const showCreateModal = () => {
      modalMode.value = 'create'
      modalTitle.value = '新增计税规则'
      resetModalForm()
      modalVisible.value = true
    }

    const resetModalForm = () => {
      Object.keys(modalForm).forEach(key => {
        if (key.includes('date')) {
          modalForm[key] = null
        } else {
          modalForm[key] = ''
        }
      })
    }

    const handleModalOk = () => {
      modalFormRef.value.submit()
    }

    const handleModalCancel = () => {
      modalVisible.value = false
      resetModalForm()
    }

    const onModalFinish = async (values) => {
      modalLoading.value = true
      try {
        // 转换日期格式
        const formData = {
          ...values,
          effective_date: values.effective_date ? values.effective_date.format('YYYY-MM-DD') : null,
          expiry_date: values.expiry_date ? values.expiry_date.format('YYYY-MM-DD') : null
        }

        let response
        if (modalMode.value === 'create') {
          response = await createTaxRule(taxTypeId, formData)
        } else {
          response = await updateTaxRule(taxTypeId, currentRule.value.id, formData)
        }

        if (response.code === 200) {
          message.success(`${modalMode.value === 'create' ? '创建' : '更新'}规则成功`)
          modalVisible.value = false
          fetchRules()
        } else {
          message.error(response.message || `${modalMode.value === 'create' ? '创建' : '更新'}规则失败`)
        }
      } catch (error) {
        console.error('操作规则失败:', error)
        message.error(`${modalMode.value === 'create' ? '创建' : '更新'}规则失败`)
      } finally {
        modalLoading.value = false
      }
    }

    const viewRule = (record) => {
      // 查看规则详情
      message.info('查看规则功能开发中')
    }

    const editRule = (record) => {
      modalMode.value = 'edit'
      modalTitle.value = '编辑计税规则'
      currentRule.value = record

      // 填充表单数据
      Object.keys(modalForm).forEach(key => {
        if (key.includes('date') && record[key]) {
          modalForm[key] = dayjs(record[key])
        } else {
          modalForm[key] = record[key] || ''
        }
      })

      modalVisible.value = true
    }

    const deleteRule = (record) => {
      Modal.confirm({
        title: '确认删除规则',
        content: `确定要删除规则"${record.name}"吗？此操作不可恢复。`,
        okType: 'danger',
        async onOk () {
          try {
            const response = await deleteTaxRule(taxTypeId, record.id)
            if (response.code === 200) {
              message.success('删除规则成功')
              fetchRules()
            } else {
              message.error(response.message || '删除规则失败')
            }
          } catch (error) {
            console.error('删除规则失败:', error)
            message.error('删除规则失败')
          }
        }
      })
    }

    // 辅助方法
    const getRateTypeColor = (rateType) => {
      const colors = {
        percentage: 'blue',
        fixed: 'green',
        progressive: 'purple'
      }
      return colors[rateType] || 'default'
    }

    const getRateTypeName = (rateType) => {
      const names = {
        percentage: '比例税率',
        fixed: '定额税率',
        progressive: '累进税率'
      }
      return names[rateType] || rateType
    }

    const formatRate = (rate) => {
      if (!rate) return '-'
      return `${(parseFloat(rate) * 100).toFixed(2)}%`
    }

    const formatDate = (date) => {
      if (!date) return '-'
      return dayjs(date).format('YYYY-MM-DD')
    }

    const getRuleStatus = (rule) => {
      const now = dayjs()
      const effectiveDate = dayjs(rule.effective_date)
      const expiryDate = rule.expiry_date ? dayjs(rule.expiry_date) : null

      if (now.isBefore(effectiveDate)) {
        return { status: 'default', text: '未生效' }
      } else if (expiryDate && now.isAfter(expiryDate)) {
        return { status: 'error', text: '已失效' }
      } else {
        return { status: 'success', text: '生效中' }
      }
    }

    // 生命周期
    onMounted(() => {
      fetchTaxTypeDetail()
      fetchRules()
    })

    return {
      breadcrumbItems,
      loading,
      taxType,
      dataSource,
      pagination,
      columns,
      searchKeyword,
      modalVisible,
      modalTitle,
      modalForm,
      modalRules,
      modalLoading,
      modalFormRef,
      goBack,
      handleSearch,
      handleTableChange,
      showCreateModal,
      handleModalOk,
      handleModalCancel,
      onModalFinish,
      viewRule,
      editRule,
      deleteRule,
      getRateTypeColor,
      getRateTypeName,
      formatRate,
      formatDate,
      getRuleStatus
    }
  }
})
</script>

<style scoped>
.tax-rules {
  padding: 0;
}

.tax-type-info {
  margin-bottom: 16px;
}

.info-item {
  margin-bottom: 8px;
}

.info-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #262626;
}

.rule-name .name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.rule-name .description {
  font-size: 12px;
  color: #8c8c8c;
}

.rule-condition,
.rule-formula {
  font-size: 12px;
  color: #595959;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.rule-formula code {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
