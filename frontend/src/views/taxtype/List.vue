<template>
  <div class="tax-type-list">
    <!-- 页面标题和操作 -->
    <PageHeader
      title="税种配置中心"
      description="管理税种基本信息、税率配置和申报规则"
      :breadcrumbs="breadcrumbItems"
    >
      <template #actions>
        <a-space>
          <a-button type="primary" @click="goToCreate">
            <PlusOutlined />
            新增税种
          </a-button>
          <a-button @click="exportData">
            <ExportOutlined />
            导出数据
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <!-- 搜索和筛选 -->
    <a-card :bordered="false" class="search-card">
      <a-form
        :model="searchForm"
        layout="inline"
        @finish="handleSearch"
      >
        <a-form-item label="关键词">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="搜索税种名称、编码"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>

        <a-form-item label="税种分类">
          <a-select
            v-model:value="searchForm.category"
            placeholder="选择分类"
            allow-clear
            style="width: 150px"
          >
            <a-select-option value="">
              全部分类
            </a-select-option>
            <a-select-option value="shared">
              共享税
            </a-select-option>
            <a-select-option value="central">
              中央税
            </a-select-option>
            <a-select-option value="local">
              地方税
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.is_active"
            placeholder="选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="">
              全部状态
            </a-select-option>
            <a-select-option :value="true">
              启用
            </a-select-option>
            <a-select-option :value="false">
              禁用
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button @click="handleReset">
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 统计信息 -->
    <a-card :bordered="false" class="stats-card">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic
            title="税种总数"
            :value="stats.total"
            suffix="个"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <ControlOutlined />
            </template>
          </a-statistic>
        </a-col>

        <a-col :span="6">
          <a-statistic
            title="启用税种"
            :value="stats.active"
            suffix="个"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <CheckCircleOutlined />
            </template>
          </a-statistic>
        </a-col>

        <a-col :span="6">
          <a-statistic
            title="共享税"
            :value="stats.shared"
            suffix="个"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <ShareAltOutlined />
            </template>
          </a-statistic>
        </a-col>

        <a-col :span="6">
          <a-statistic
            title="本月更新"
            :value="stats.thisMonth"
            suffix="个"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <CalendarOutlined />
            </template>
          </a-statistic>
        </a-col>
      </a-row>
    </a-card>

    <!-- 数据表格 -->
    <a-card :bordered="false">
      <!-- 批量操作工具栏 -->
      <div v-if="selectedRowKeys.length > 0" class="batch-actions">
        <a-alert
          :message="`已选择 ${selectedRowKeys.length} 项`"
          type="info"
          show-icon
          class="batch-alert"
        >
          <template #action>
            <a-space>
              <a-button size="small" @click="batchEnable">
                <CheckCircleOutlined />
                批量启用
              </a-button>
              <a-button size="small" @click="batchDisable">
                <StopOutlined />
                批量禁用
              </a-button>
              <a-button size="small" danger @click="batchDelete">
                <DeleteOutlined />
                批量删除
              </a-button>
              <a-button size="small" @click="clearSelection">
                清空选择
              </a-button>
            </a-space>
          </template>
        </a-alert>
      </div>

      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        row-key="id"
        :scroll="{ x: 1200 }"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div class="tax-type-name">
              <div class="name">
                {{ record.name }}
              </div>
              <div class="code">
                {{ record.code }}
              </div>
            </div>
          </template>

          <template v-if="column.key === 'category'">
            <a-tag :color="getCategoryColor(record.category)">
              {{ getCategoryName(record.category) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'rate_type'">
            <a-tag :color="getRateTypeColor(record.rate_type)">
              {{ getRateTypeName(record.rate_type) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'tax_rate'">
            <span v-if="record.tax_rate">
              {{ formatRate(record.tax_rate) }}
            </span>
            <span v-else class="text-muted">-</span>
          </template>

          <template v-if="column.key === 'filing_frequency'">
            <a-tag>{{ getFilingFrequencyName(record.filing_frequency) }}</a-tag>
          </template>

          <template v-if="column.key === 'is_active'">
            <a-badge
              :status="record.is_active ? 'success' : 'default'"
              :text="record.is_active ? '启用' : '禁用'"
            />
          </template>

          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">
                <EyeOutlined />
                详情
              </a-button>
              <a-button type="link" size="small" @click="editTaxType(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="viewRules(record)">
                      <SettingOutlined />
                      税则配置
                    </a-menu-item>
                    <a-menu-item @click="toggleStatus(record)">
                      <component :is="record.is_active ? StopOutlined : PlayCircleOutlined" />
                      {{ record.is_active ? '禁用' : '启用' }}
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item danger @click="deleteTaxType(record)">
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ExportOutlined,
  SearchOutlined,
  ControlOutlined,
  CheckCircleOutlined,
  ShareAltOutlined,
  CalendarOutlined,
  EyeOutlined,
  EditOutlined,
  DownOutlined,
  SettingOutlined,
  StopOutlined,
  PlayCircleOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

import PageHeader from '@/components/common/PageHeader.vue'
import {
  getTaxTypeList,
  deleteTaxType,
  updateTaxType,
  batchUpdateTaxTypeStatus,
  batchDeleteTaxTypes,
  exportTaxTypes
} from '@/api/taxType'

export default defineComponent({
  name: 'TaxTypeList',
  components: {
    PageHeader,
    PlusOutlined,
    ExportOutlined,
    SearchOutlined,
    ControlOutlined,
    CheckCircleOutlined,
    ShareAltOutlined,
    CalendarOutlined,
    EyeOutlined,
    EditOutlined,
    DownOutlined,
    SettingOutlined,
    StopOutlined,
    PlayCircleOutlined,
    DeleteOutlined
  },
  setup () {
    const router = useRouter()

    // 面包屑导航
    const breadcrumbItems = [
      { title: '首页', path: '/' },
      { title: '税种配置中心', path: '/tax-type' },
      { title: '税种列表' }
    ]

    // 响应式数据
    const loading = ref(false)
    const dataSource = ref([])
    const selectedRowKeys = ref([])
    const stats = reactive({
      total: 0,
      active: 0,
      shared: 0,
      thisMonth: 0
    })

    // 搜索表单
    const searchForm = reactive({
      keyword: '',
      category: '',
      is_active: ''
    })

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
    })

    // 行选择配置
    const rowSelection = {
      selectedRowKeys,
      onChange: (keys) => {
        selectedRowKeys.value = keys
      },
      onSelectAll: (selected, selectedRows, changeRows) => {
        console.log('onSelectAll', selected, selectedRows, changeRows)
      }
    }

    // 表格列配置
    const columns = [
      {
        title: '税种信息',
        key: 'name',
        width: 250,
        fixed: 'left'
      },
      {
        title: '税种分类',
        key: 'category',
        width: 120
      },
      {
        title: '税率类型',
        key: 'rate_type',
        width: 120
      },
      {
        title: '默认税率',
        key: 'tax_rate',
        width: 120
      },
      {
        title: '申报频率',
        key: 'filing_frequency',
        width: 120
      },
      {
        title: '征收机关',
        dataIndex: 'tax_authority',
        width: 150
      },
      {
        title: '状态',
        key: 'is_active',
        width: 100
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        width: 150,
        sorter: true
      },
      {
        title: '操作',
        key: 'actions',
        width: 200,
        fixed: 'right'
      }
    ]

    // 获取税种列表数据
    const fetchData = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.current,
          pageSize: pagination.pageSize,
          keyword: searchForm.keyword,
          category: searchForm.category,
          is_active: searchForm.is_active
        }

        const response = await getTaxTypeList(params)
        if (response.code === 200) {
          const data = response.data
          dataSource.value = data.taxTypes || []
          pagination.total = data.total || 0

          // 更新统计信息
          updateStats(data.taxTypes || [])
        } else {
          message.error(response.message || '获取税种列表失败')
        }
      } catch (error) {
        console.error('获取税种列表失败:', error)
        message.error('获取税种列表失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 更新统计信息
    const updateStats = (taxTypes) => {
      stats.total = taxTypes.length
      stats.active = taxTypes.filter(item => item.is_active).length
      stats.shared = taxTypes.filter(item => item.category === 'shared').length
      // 本月更新的数量（简化处理）
      const thisMonth = new Date()
      thisMonth.setDate(1)
      stats.thisMonth = taxTypes.filter(item =>
        new Date(item.updated_at) >= thisMonth
      ).length
    }

    // 搜索处理
    const handleSearch = () => {
      pagination.current = 1
      fetchData()
    }

    // 重置搜索
    const handleReset = () => {
      searchForm.keyword = ''
      searchForm.category = ''
      searchForm.is_active = ''
      pagination.current = 1
      fetchData()
    }

    // 表格变化处理
    const handleTableChange = (pag) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      fetchData()
    }

    // 页面操作方法
    const goToCreate = () => {
      router.push('/tax-type/create')
    }

    const viewDetail = (record) => {
      router.push(`/tax-type/detail/${record.id}`)
    }

    const editTaxType = (record) => {
      router.push(`/tax-type/edit/${record.id}`)
    }

    const viewRules = (record) => {
      router.push(`/tax-type/rules/${record.id}`)
    }

    const toggleStatus = async (record) => {
      try {
        const newStatus = !record.is_active
        const response = await updateTaxType(record.id, {
          is_active: newStatus
        })

        if (response.code === 200) {
          message.success(`${newStatus ? '启用' : '禁用'}成功`)
          record.is_active = newStatus
          updateStats(dataSource.value)
        } else {
          message.error(response.message || '操作失败')
        }
      } catch (error) {
        console.error('切换状态失败:', error)
        message.error('操作失败，请稍后重试')
      }
    }

    const deleteTaxType = (record) => {
      Modal.confirm({
        title: '确认删除税种',
        content: `确定要删除税种"${record.name}"吗？此操作不可恢复。`,
        okType: 'danger',
        async onOk () {
          try {
            const response = await deleteTaxType(record.id)
            if (response.code === 200) {
              message.success('删除成功')
              fetchData()
            } else {
              message.error(response.message || '删除失败')
            }
          } catch (error) {
            console.error('删除税种失败:', error)
            message.error('删除失败，请稍后重试')
          }
        }
      })
    }

    const exportData = async () => {
      try {
        const response = await exportTaxTypes(searchForm)
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `税种数据_${new Date().toISOString().slice(0, 10)}.xlsx`)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        message.error('导出失败，请稍后重试')
      }
    }

    // 批量操作方法
    const clearSelection = () => {
      selectedRowKeys.value = []
    }

    const batchEnable = () => {
      Modal.confirm({
        title: '确认批量启用',
        content: `确定要启用选中的 ${selectedRowKeys.value.length} 个税种吗？`,
        async onOk () {
          try {
            const response = await batchUpdateTaxTypeStatus(selectedRowKeys.value, true)
            if (response.code === 200) {
              message.success('批量启用成功')
              clearSelection()
              fetchData()
            } else {
              message.error(response.message || '批量启用失败')
            }
          } catch (error) {
            console.error('批量启用失败:', error)
            message.error('批量启用失败，请稍后重试')
          }
        }
      })
    }

    const batchDisable = () => {
      Modal.confirm({
        title: '确认批量禁用',
        content: `确定要禁用选中的 ${selectedRowKeys.value.length} 个税种吗？`,
        async onOk () {
          try {
            const response = await batchUpdateTaxTypeStatus(selectedRowKeys.value, false)
            if (response.code === 200) {
              message.success('批量禁用成功')
              clearSelection()
              fetchData()
            } else {
              message.error(response.message || '批量禁用失败')
            }
          } catch (error) {
            console.error('批量禁用失败:', error)
            message.error('批量禁用失败，请稍后重试')
          }
        }
      })
    }

    const batchDelete = () => {
      Modal.confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${selectedRowKeys.value.length} 个税种吗？此操作不可恢复。`,
        okType: 'danger',
        async onOk () {
          try {
            const response = await batchDeleteTaxTypes(selectedRowKeys.value)
            if (response.code === 200) {
              message.success('批量删除成功')
              clearSelection()
              fetchData()
            } else {
              message.error(response.message || '批量删除失败')
            }
          } catch (error) {
            console.error('批量删除失败:', error)
            message.error('批量删除失败，请稍后重试')
          }
        }
      })
    }

    // 辅助方法
    const getCategoryColor = (category) => {
      const colors = {
        shared: 'blue',
        central: 'green',
        local: 'orange'
      }
      return colors[category] || 'default'
    }

    const getCategoryName = (category) => {
      const names = {
        shared: '共享税',
        central: '中央税',
        local: '地方税'
      }
      return names[category] || category
    }

    const getRateTypeColor = (rateType) => {
      const colors = {
        percentage: 'blue',
        fixed: 'green',
        progressive: 'purple'
      }
      return colors[rateType] || 'default'
    }

    const getRateTypeName = (rateType) => {
      const names = {
        percentage: '比例税率',
        fixed: '定额税率',
        progressive: '累进税率'
      }
      return names[rateType] || rateType
    }

    const getFilingFrequencyName = (frequency) => {
      const names = {
        monthly: '月报',
        quarterly: '季报',
        annually: '年报',
        other: '其他'
      }
      return names[frequency] || frequency
    }

    const formatRate = (rate) => {
      if (!rate) return '-'
      return `${(parseFloat(rate) * 100).toFixed(2)}%`
    }

    // 生命周期
    onMounted(() => {
      fetchData()
    })

    return {
      breadcrumbItems,
      loading,
      dataSource,
      selectedRowKeys,
      rowSelection,
      stats,
      searchForm,
      pagination,
      columns,
      fetchData,
      handleSearch,
      handleReset,
      handleTableChange,
      goToCreate,
      viewDetail,
      editTaxType,
      viewRules,
      toggleStatus,
      deleteTaxType,
      exportData,
      clearSelection,
      batchEnable,
      batchDisable,
      batchDelete,
      getCategoryColor,
      getCategoryName,
      getRateTypeColor,
      getRateTypeName,
      getFilingFrequencyName,
      formatRate
    }
  }
})
</script>

<style scoped>
.tax-type-list {
  padding: 0;
}

.search-card,
.stats-card {
  margin-bottom: 16px;
}

.tax-type-name {
  display: flex;
  flex-direction: column;
}

.tax-type-name .name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.tax-type-name .code {
  font-size: 12px;
  color: #8c8c8c;
}

.text-muted {
  color: #8c8c8c;
}

.batch-actions {
  margin-bottom: 16px;
}

.batch-alert {
  border-radius: 6px;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 500;
}

:deep(.ant-statistic-content-prefix) {
  margin-right: 8px;
  font-size: 20px;
}
</style>
