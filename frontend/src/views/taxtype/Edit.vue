<template>
  <div class="tax-type-edit">
    <!-- 页面标题 -->
    <PageHeader
      title="编辑税种"
      description="修改税种配置信息"
      :breadcrumbs="breadcrumbItems"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
          <a-button type="primary" :loading="submitting" @click="handleSubmit">
            <SaveOutlined />
            保存
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>

    <!-- 表单内容 -->
    <a-card v-else :bordered="false">
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        layout="vertical"
        @finish="onFinish"
      >
        <a-row :gutter="24">
          <!-- 基本信息 -->
          <a-col :span="24">
            <h3 class="section-title">
              基本信息
            </h3>
          </a-col>

          <a-col :span="12">
            <a-form-item label="税种编码" name="code">
              <a-input
                v-model:value="form.code"
                placeholder="请输入税种编码，如：VAT"
                :maxlength="20"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="税种名称" name="name">
              <a-input
                v-model:value="form.name"
                placeholder="请输入税种名称，如：增值税"
                :maxlength="100"
              />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="税种描述" name="description">
              <a-textarea
                v-model:value="form.description"
                placeholder="请输入税种描述"
                :rows="3"
                :maxlength="500"
                show-count
              />
            </a-form-item>
          </a-col>

          <!-- 分类配置 -->
          <a-col :span="24">
            <h3 class="section-title">
              分类配置
            </h3>
          </a-col>

          <a-col :span="8">
            <a-form-item label="税种分类" name="category">
              <a-select
                v-model:value="form.category"
                placeholder="请选择税种分类"
              >
                <a-select-option value="shared">
                  共享税
                </a-select-option>
                <a-select-option value="central">
                  中央税
                </a-select-option>
                <a-select-option value="local">
                  地方税
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="税率类型" name="rate_type">
              <a-select
                v-model:value="form.rate_type"
                placeholder="请选择税率类型"
              >
                <a-select-option value="percentage">
                  比例税率
                </a-select-option>
                <a-select-option value="fixed">
                  定额税率
                </a-select-option>
                <a-select-option value="progressive">
                  累进税率
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="默认税率" name="tax_rate">
              <a-input-number
                v-model:value="form.tax_rate"
                placeholder="请输入默认税率"
                :min="0"
                :max="100"
                :step="0.01"
                :precision="4"
                style="width: 100%"
              >
                <template #addonAfter>
                  %
                </template>
              </a-input-number>
            </a-form-item>
          </a-col>

          <!-- 申报配置 -->
          <a-col :span="24">
            <h3 class="section-title">
              申报配置
            </h3>
          </a-col>

          <a-col :span="8">
            <a-form-item label="申报频率" name="filing_frequency">
              <a-select
                v-model:value="form.filing_frequency"
                placeholder="请选择申报频率"
              >
                <a-select-option value="monthly">
                  月报
                </a-select-option>
                <a-select-option value="quarterly">
                  季报
                </a-select-option>
                <a-select-option value="annually">
                  年报
                </a-select-option>
                <a-select-option value="other">
                  其他
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="申报日" name="filing_day">
              <a-input-number
                v-model:value="form.filing_day"
                placeholder="请输入申报日"
                :min="1"
                :max="31"
                style="width: 100%"
              >
                <template #addonAfter>
                  日
                </template>
              </a-input-number>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="缴款期限" name="payment_deadline">
              <a-input-number
                v-model:value="form.payment_deadline"
                placeholder="请输入缴款期限"
                :min="1"
                :max="365"
                style="width: 100%"
              >
                <template #addonAfter>
                  天
                </template>
              </a-input-number>
            </a-form-item>
          </a-col>

          <!-- 法律依据 -->
          <a-col :span="24">
            <h3 class="section-title">
              法律依据
            </h3>
          </a-col>

          <a-col :span="12">
            <a-form-item label="法律依据" name="legal_basis">
              <a-textarea
                v-model:value="form.legal_basis"
                placeholder="请输入法律依据"
                :rows="3"
                :maxlength="500"
                show-count
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="征收机关" name="tax_authority">
              <a-input
                v-model:value="form.tax_authority"
                placeholder="请输入征收机关"
                :maxlength="100"
              />
            </a-form-item>
          </a-col>

          <!-- 状态配置 -->
          <a-col :span="24">
            <h3 class="section-title">
              状态配置
            </h3>
          </a-col>

          <a-col :span="12">
            <a-form-item label="启用状态" name="is_active">
              <a-switch
                v-model:checked="form.is_active"
                checked-children="启用"
                un-checked-children="禁用"
              />
              <div class="form-help-text">
                禁用后，该税种将不会在系统中显示和使用
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  SaveOutlined
} from '@ant-design/icons-vue'

import PageHeader from '@/components/common/PageHeader.vue'
import { getTaxTypeDetail, updateTaxType } from '@/api/taxType'

export default defineComponent({
  name: 'TaxTypeEdit',
  components: {
    PageHeader,
    ArrowLeftOutlined,
    SaveOutlined
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref()
    const loading = ref(false)
    const submitting = ref(false)

    // 获取税种ID
    const taxTypeId = route.params.id

    // 面包屑导航
    const breadcrumbItems = [
      { title: '首页', path: '/' },
      { title: '税种配置中心', path: '/tax-type/list' },
      { title: '编辑税种' }
    ]

    // 表单数据
    const form = reactive({
      code: '',
      name: '',
      description: '',
      category: '',
      rate_type: 'percentage',
      tax_rate: null,
      filing_frequency: '',
      filing_day: null,
      payment_deadline: null,
      legal_basis: '',
      tax_authority: '',
      is_active: true
    })

    // 表单验证规则
    const rules = {
      code: [
        { required: true, message: '请输入税种编码', trigger: 'blur' },
        { min: 2, max: 20, message: '税种编码长度为2-20个字符', trigger: 'blur' },
        { pattern: /^[A-Z0-9_]+$/, message: '税种编码只能包含大写字母、数字和下划线', trigger: 'blur' }
      ],
      name: [
        { required: true, message: '请输入税种名称', trigger: 'blur' },
        { min: 2, max: 100, message: '税种名称长度为2-100个字符', trigger: 'blur' }
      ],
      category: [
        { required: true, message: '请选择税种分类', trigger: 'change' }
      ],
      rate_type: [
        { required: true, message: '请选择税率类型', trigger: 'change' }
      ],
      filing_frequency: [
        { required: true, message: '请选择申报频率', trigger: 'change' }
      ]
    }

    // 方法
    const fetchTaxTypeDetail = async () => {
      loading.value = true
      try {
        const response = await getTaxTypeDetail(taxTypeId)
        if (response.code === 200) {
          const data = response.data
          // 填充表单数据
          Object.keys(form).forEach(key => {
            if (data[key] !== undefined) {
              if (key === 'tax_rate' && data[key]) {
                // 将小数转换为百分比显示
                form[key] = parseFloat(data[key]) * 100
              } else {
                form[key] = data[key]
              }
            }
          })
        } else {
          message.error(response.message || '获取税种详情失败')
          router.push('/tax-type/list')
        }
      } catch (error) {
        console.error('获取税种详情失败:', error)
        if (error.response?.status === 404) {
          message.error('税种不存在')
          router.push('/tax-type/list')
        } else {
          message.error('获取税种详情失败')
        }
      } finally {
        loading.value = false
      }
    }

    const goBack = () => {
      router.go(-1)
    }

    const handleSubmit = () => {
      formRef.value.submit()
    }

    const onFinish = async (values) => {
      submitting.value = true
      try {
        // 转换税率为小数
        if (values.tax_rate) {
          values.tax_rate = values.tax_rate / 100
        }

        const response = await updateTaxType(taxTypeId, values)
        if (response.code === 200) {
          message.success('更新税种成功')
          router.push('/tax-type/list')
        } else {
          message.error(response.message || '更新税种失败')
        }
      } catch (error) {
        console.error('更新税种失败:', error)
        message.error('更新税种失败，请稍后重试')
      } finally {
        submitting.value = false
      }
    }

    // 生命周期
    onMounted(() => {
      fetchTaxTypeDetail()
    })

    return {
      breadcrumbItems,
      formRef,
      form,
      rules,
      loading,
      submitting,
      goBack,
      handleSubmit,
      onFinish
    }
  }
})
</script>

<style scoped>
.tax-type-edit {
  padding: 0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.section-title {
  margin: 24px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.section-title:first-of-type {
  margin-top: 0;
}

.form-help-text {
  margin-top: 4px;
  font-size: 12px;
  color: #8c8c8c;
}
</style>
