<template>
  <div class="tax-type-create">
    <!-- 页面标题 -->
    <PageHeader
      title="新增税种"
      description="创建新的税种配置"
      :breadcrumbs="breadcrumbItems"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
          <a-button type="primary" :loading="submitting" @click="handleSubmit">
            <SaveOutlined />
            保存
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <!-- 表单内容 -->
    <a-card :bordered="false">
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        layout="vertical"
        @finish="onFinish"
      >
        <a-row :gutter="24">
          <!-- 基本信息 -->
          <a-col :span="24">
            <h3 class="section-title">
              基本信息
            </h3>
          </a-col>

          <a-col :span="12">
            <a-form-item label="税种编码" name="code">
              <a-input
                v-model:value="form.code"
                placeholder="请输入税种编码，如：VAT"
                :maxlength="20"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="税种名称" name="name">
              <a-input
                v-model:value="form.name"
                placeholder="请输入税种名称，如：增值税"
                :maxlength="100"
              />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="税种描述" name="description">
              <a-textarea
                v-model:value="form.description"
                placeholder="请输入税种描述"
                :rows="3"
                :maxlength="500"
                show-count
              />
            </a-form-item>
          </a-col>

          <!-- 分类配置 -->
          <a-col :span="24">
            <h3 class="section-title">
              分类配置
            </h3>
          </a-col>

          <a-col :span="8">
            <a-form-item label="税种分类" name="category">
              <a-select
                v-model:value="form.category"
                placeholder="请选择税种分类"
              >
                <a-select-option value="shared">
                  共享税
                </a-select-option>
                <a-select-option value="central">
                  中央税
                </a-select-option>
                <a-select-option value="local">
                  地方税
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="税率类型" name="rateType">
              <a-select
                v-model:value="form.rateType"
                placeholder="请选择税率类型"
              >
                <a-select-option value="percentage">
                  比例税率
                </a-select-option>
                <a-select-option value="fixed">
                  定额税率
                </a-select-option>
                <a-select-option value="progressive">
                  累进税率
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="默认税率" name="taxRate">
              <a-input-number
                v-model:value="form.taxRate"
                placeholder="请输入默认税率"
                :min="0"
                :max="1"
                :step="0.0001"
                :precision="4"
                style="width: 100%"
              >
                <template #addonAfter>
                  %
                </template>
              </a-input-number>
            </a-form-item>
          </a-col>

          <!-- 申报配置 -->
          <a-col :span="24">
            <h3 class="section-title">
              申报配置
            </h3>
          </a-col>

          <a-col :span="8">
            <a-form-item label="申报频率" name="filingFrequency">
              <a-select
                v-model:value="form.filingFrequency"
                placeholder="请选择申报频率"
              >
                <a-select-option value="monthly">
                  月报
                </a-select-option>
                <a-select-option value="quarterly">
                  季报
                </a-select-option>
                <a-select-option value="annually">
                  年报
                </a-select-option>
                <a-select-option value="other">
                  其他
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="申报日" name="filingDay">
              <a-input-number
                v-model:value="form.filingDay"
                placeholder="请输入申报日"
                :min="1"
                :max="31"
                style="width: 100%"
              >
                <template #addonAfter>
                  日
                </template>
              </a-input-number>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="缴款期限" name="paymentDeadline">
              <a-input-number
                v-model:value="form.paymentDeadline"
                placeholder="请输入缴款期限"
                :min="1"
                :max="365"
                style="width: 100%"
              >
                <template #addonAfter>
                  天
                </template>
              </a-input-number>
            </a-form-item>
          </a-col>

          <!-- 法律依据 -->
          <a-col :span="24">
            <h3 class="section-title">
              法律依据
            </h3>
          </a-col>

          <a-col :span="12">
            <a-form-item label="法律依据" name="legalBasis">
              <a-textarea
                v-model:value="form.legalBasis"
                placeholder="请输入法律依据"
                :rows="3"
                :maxlength="500"
                show-count
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="征收机关" name="taxAuthority">
              <a-input
                v-model:value="form.taxAuthority"
                placeholder="请输入征收机关"
                :maxlength="100"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  SaveOutlined
} from '@ant-design/icons-vue'

import PageHeader from '@/components/common/PageHeader.vue'
import { createTaxType } from '@/api/taxType'

export default defineComponent({
  name: 'TaxTypeCreate',
  components: {
    PageHeader,
    ArrowLeftOutlined,
    SaveOutlined
  },
  setup () {
    const router = useRouter()
    const formRef = ref()
    const submitting = ref(false)

    // 面包屑导航
    const breadcrumbItems = [
      { title: '首页', path: '/' },
      { title: '税种配置中心', path: '/tax-type/list' },
      { title: '新增税种' }
    ]

    // 表单数据
    const form = reactive({
      code: '',
      name: '',
      description: '',
      category: '',
      rateType: 'percentage',
      taxRate: null,
      filingFrequency: '',
      filingDay: null,
      paymentDeadline: null,
      legalBasis: '',
      taxAuthority: ''
    })

    // 表单验证规则
    const rules = {
      code: [
        { required: true, message: '请输入税种编码', trigger: 'blur' },
        { min: 2, max: 20, message: '税种编码长度为2-20个字符', trigger: 'blur' },
        { pattern: /^[A-Z0-9_]+$/, message: '税种编码只能包含大写字母、数字和下划线', trigger: 'blur' }
      ],
      name: [
        { required: true, message: '请输入税种名称', trigger: 'blur' },
        { min: 2, max: 100, message: '税种名称长度为2-100个字符', trigger: 'blur' }
      ],
      category: [
        { required: true, message: '请选择税种分类', trigger: 'change' }
      ],
      rateType: [
        { required: true, message: '请选择税率类型', trigger: 'change' }
      ],
      filingFrequency: [
        { required: true, message: '请选择申报频率', trigger: 'change' }
      ]
    }

    // 方法
    const goBack = () => {
      router.go(-1)
    }

    const handleSubmit = () => {
      formRef.value.submit()
    }

    const onFinish = async (values) => {
      submitting.value = true
      try {
        // 转换税率为小数
        if (values.taxRate) {
          values.taxRate = values.taxRate / 100
        }

        const response = await createTaxType(values)
        if (response.code === 200) {
          message.success('创建税种成功')
          router.push('/tax-type/list')
        } else {
          message.error(response.message || '创建税种失败')
        }
      } catch (error) {
        console.error('创建税种失败:', error)
        message.error('创建税种失败，请稍后重试')
      } finally {
        submitting.value = false
      }
    }

    return {
      breadcrumbItems,
      formRef,
      form,
      rules,
      submitting,
      goBack,
      handleSubmit,
      onFinish
    }
  }
})
</script>

<style scoped>
.tax-type-create {
  padding: 0;
}

.section-title {
  margin: 24px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.section-title:first-of-type {
  margin-top: 0;
}
</style>
