<template>
  <div class="tax-rule-form">
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="税种" name="taxTypeId">
            <a-select
              v-model:value="form.taxTypeId"
              placeholder="请选择税种"
              :loading="taxTypesLoading"
              show-search
              :filter-option="filterOption"
            >
              <a-select-option
                v-for="taxType in taxTypes"
                :key="taxType.id"
                :value="taxType.id"
              >
                {{ taxType.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="税则名称" name="name">
            <a-input
              v-model:value="form.name"
              placeholder="请输入税则名称"
              :maxlength="100"
              show-count
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="税则描述" name="description">
        <a-textarea
          v-model:value="form.description"
          placeholder="请输入税则描述"
          :rows="3"
          :maxlength="500"
          show-count
        />
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="生效日期" name="effectiveDate">
            <a-date-picker
              v-model:value="form.effectiveDate"
              placeholder="请选择生效日期"
              style="width: 100%"
              :disabled-date="disabledDate"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="失效日期" name="expiryDate">
            <a-date-picker
              v-model:value="form.expiryDate"
              placeholder="请选择失效日期（可选）"
              style="width: 100%"
              :disabled-date="disabledExpiryDate"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="适用条件" name="condition">
        <a-textarea
          v-model:value="form.condition"
          placeholder="请输入适用条件，如：收入 > 100000"
          :rows="2"
          :maxlength="1000"
          show-count
        />
        <div class="form-tip">
          支持条件表达式，如：income > 100000, taxableAmount <= 50000 等
        </div>
      </a-form-item>

      <a-form-item label="计算公式" name="formula">
        <a-textarea
          v-model:value="form.formula"
          placeholder="请输入计算公式，如：taxableAmount * 0.25"
          :rows="2"
          :maxlength="1000"
          show-count
        />
        <div class="form-tip">
          支持数学表达式，如：amount * rate, (income - deduction) * 0.1 等
        </div>
      </a-form-item>

      <a-form-item label="规则参数">
        <div class="parameters-container">
          <div
            v-for="(param, index) in parameters"
            :key="index"
            class="parameter-item"
          >
            <a-input
              v-model:value="param.key"
              placeholder="参数名"
              style="width: 30%"
            />
            <a-select
              v-model:value="param.type"
              placeholder="类型"
              style="width: 20%; margin: 0 8px"
            >
              <a-select-option value="string">字符串</a-select-option>
              <a-select-option value="number">数字</a-select-option>
              <a-select-option value="boolean">布尔值</a-select-option>
            </a-select>
            <a-input
              v-model:value="param.value"
              placeholder="参数值"
              style="width: 35%"
            />
            <a-button
              type="text"
              danger
              @click="removeParameter(index)"
            >
              删除
            </a-button>
          </div>
          <a-button
            type="dashed"
            block
            @click="addParameter"
          >
            <template #icon>
              <PlusOutlined />
            </template>
            添加参数
          </a-button>
        </div>
      </a-form-item>

      <a-form-item label="预览">
        <div class="rule-preview">
          <div class="preview-section">
            <h4>规则摘要</h4>
            <p><strong>税种：</strong>{{ selectedTaxTypeName || '未选择' }}</p>
            <p><strong>名称：</strong>{{ form.name || '未填写' }}</p>
            <p><strong>生效期间：</strong>{{ effectivePeriod }}</p>
          </div>
          <div v-if="form.condition" class="preview-section">
            <h4>适用条件</h4>
            <code>{{ form.condition }}</code>
          </div>
          <div v-if="form.formula" class="preview-section">
            <h4>计算公式</h4>
            <code>{{ form.formula }}</code>
          </div>
          <div v-if="validParameters.length > 0" class="preview-section">
            <h4>参数配置</h4>
            <ul>
              <li v-for="param in validParameters" :key="param.key">
                <strong>{{ param.key }}</strong> ({{ param.type }}): {{ param.value }}
              </li>
            </ul>
          </div>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import { getTaxTypes } from '@/api/taxType'
import { createTaxRule, updateTaxRule, validateTaxRule } from '@/api/taxRule'

export default defineComponent({
  name: 'TaxRuleForm',
  components: {
    PlusOutlined
  },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  emits: ['submit'],
  setup(props, { emit, expose }) {
    const formRef = ref(null)
    const taxTypesLoading = ref(false)
    const taxTypes = ref([])

    // 表单数据
    const form = reactive({
      taxTypeId: '',
      name: '',
      description: '',
      condition: '',
      formula: '',
      effectiveDate: null,
      expiryDate: null
    })

    // 参数配置
    const parameters = ref([])

    // 表单验证规则
    const rules = {
      taxTypeId: [
        { required: true, message: '请选择税种', trigger: 'change' }
      ],
      name: [
        { required: true, message: '请输入税则名称', trigger: 'blur' },
        { min: 2, max: 100, message: '税则名称长度为2-100个字符', trigger: 'blur' }
      ],
      effectiveDate: [
        { required: true, message: '请选择生效日期', trigger: 'change' }
      ]
    }

    // 计算属性
    const selectedTaxTypeName = computed(() => {
      const taxType = taxTypes.value.find(item => item.id === form.taxTypeId)
      return taxType?.name || ''
    })

    const effectivePeriod = computed(() => {
      if (!form.effectiveDate) return '未设置'
      const start = dayjs(form.effectiveDate).format('YYYY-MM-DD')
      const end = form.expiryDate ? dayjs(form.expiryDate).format('YYYY-MM-DD') : '永久'
      return `${start} 至 ${end}`
    })

    const validParameters = computed(() => {
      return parameters.value.filter(param => param.key && param.value)
    })

    // 方法
    const fetchTaxTypes = async () => {
      taxTypesLoading.value = true
      try {
        const response = await getTaxTypes({ pageSize: 1000 })
        if (response.code === 200) {
          taxTypes.value = response.data.data || []
        }
      } catch (error) {
        message.error('获取税种列表失败')
      } finally {
        taxTypesLoading.value = false
      }
    }

    const filterOption = (input, option) => {
      return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }

    const disabledDate = (current) => {
      // 禁用过去的日期（除了今天）
      return current && current < dayjs().startOf('day')
    }

    const disabledExpiryDate = (current) => {
      // 失效日期不能早于生效日期
      if (!form.effectiveDate) return false
      return current && current <= dayjs(form.effectiveDate)
    }

    const addParameter = () => {
      parameters.value.push({
        key: '',
        type: 'string',
        value: ''
      })
    }

    const removeParameter = (index) => {
      parameters.value.splice(index, 1)
    }

    const initForm = () => {
      if (props.formData && Object.keys(props.formData).length > 0) {
        Object.assign(form, {
          taxTypeId: props.formData.taxTypeId || '',
          name: props.formData.name || '',
          description: props.formData.description || '',
          condition: props.formData.condition || '',
          formula: props.formData.formula || '',
          effectiveDate: props.formData.effectiveDate ? dayjs(props.formData.effectiveDate) : null,
          expiryDate: props.formData.expiryDate ? dayjs(props.formData.expiryDate) : null
        })

        // 初始化参数
        if (props.formData.parameters) {
          try {
            const params = typeof props.formData.parameters === 'string' 
              ? JSON.parse(props.formData.parameters) 
              : props.formData.parameters
            
            parameters.value = Object.entries(params).map(([key, value]) => ({
              key,
              type: typeof value,
              value: String(value)
            }))
          } catch (error) {
            console.error('解析参数失败:', error)
            parameters.value = []
          }
        }
      } else {
        // 重置表单
        Object.assign(form, {
          taxTypeId: '',
          name: '',
          description: '',
          condition: '',
          formula: '',
          effectiveDate: null,
          expiryDate: null
        })
        parameters.value = []
      }
    }

    const validateForm = async () => {
      try {
        await formRef.value.validate()
        return true
      } catch (error) {
        return false
      }
    }

    const submit = async () => {
      if (!(await validateForm())) {
        return
      }

      try {
        // 构建提交数据
        const submitData = {
          taxTypeId: form.taxTypeId,
          name: form.name,
          description: form.description || null,
          condition: form.condition || null,
          formula: form.formula || null,
          effectiveDate: form.effectiveDate.format('YYYY-MM-DD'),
          expiryDate: form.expiryDate ? form.expiryDate.format('YYYY-MM-DD') : null
        }

        // 处理参数
        if (validParameters.value.length > 0) {
          const params = {}
          validParameters.value.forEach(param => {
            let value = param.value
            if (param.type === 'number') {
              value = Number(value)
            } else if (param.type === 'boolean') {
              value = value === 'true' || value === true
            }
            params[param.key] = value
          })
          submitData.parameters = params
        }

        // 验证规则
        await validateTaxRule(submitData)

        // 提交数据
        if (props.isEdit) {
          await updateTaxRule(props.formData.id, submitData)
          message.success('更新税则成功')
        } else {
          await createTaxRule(submitData)
          message.success('创建税则成功')
        }

        emit('submit')
      } catch (error) {
        message.error(error.message || '操作失败')
      }
    }

    // 监听器
    watch(() => props.formData, initForm, { immediate: true, deep: true })

    // 生命周期
    onMounted(() => {
      fetchTaxTypes()
    })

    // 暴露方法
    expose({
      submit
    })

    return {
      formRef,
      form,
      rules,
      parameters,
      taxTypes,
      taxTypesLoading,
      selectedTaxTypeName,
      effectivePeriod,
      validParameters,
      filterOption,
      disabledDate,
      disabledExpiryDate,
      addParameter,
      removeParameter,
      submit
    }
  }
})
</script>

<style scoped>
.tax-rule-form {
  padding: 16px 0;
}

.form-tip {
  margin-top: 4px;
  color: #666;
  font-size: 12px;
}

.parameters-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.parameter-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.parameter-item:last-child {
  margin-bottom: 12px;
}

.rule-preview {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #f9f9f9;
}

.preview-section {
  margin-bottom: 16px;
}

.preview-section:last-child {
  margin-bottom: 0;
}

.preview-section h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.preview-section p {
  margin: 4px 0;
  color: #666;
  font-size: 13px;
}

.preview-section code {
  display: block;
  padding: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  color: #d63384;
}

.preview-section ul {
  margin: 0;
  padding-left: 20px;
}

.preview-section li {
  margin: 4px 0;
  color: #666;
  font-size: 13px;
}
</style>
