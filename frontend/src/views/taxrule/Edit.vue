<!--
  税务规则编辑页面
  功能：编辑现有的税务规则
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <div class="tax-rule-edit-page">
    <div class="page-header">
      <a-page-header
        title="编辑税务规则"
        @back="handleBack"
      >
        <template #extra>
          <a-space>
            <a-button @click="handleReset">
              重置
            </a-button>
            <a-button type="primary" :loading="saving" @click="handleSave">
              保存
            </a-button>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <div class="page-content">
      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
      </div>

      <div v-else>
        <a-card title="基本信息">
          <a-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            layout="vertical"
          >
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="规则名称" name="name">
                  <a-input
                    v-model:value="formData.name"
                    placeholder="请输入规则名称"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="规则编码" name="code">
                  <a-input
                    v-model:value="formData.code"
                    placeholder="请输入规则编码"
                    disabled
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="规则类型" name="type">
                  <a-select
                    v-model:value="formData.type"
                    placeholder="请选择规则类型"
                  >
                    <a-select-option value="税率计算">税率计算</a-select-option>
                    <a-select-option value="税额计算">税额计算</a-select-option>
                    <a-select-option value="优惠政策">优惠政策</a-select-option>
                    <a-select-option value="合规检查">合规检查</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="优先级" name="priority">
                  <a-input-number
                    v-model:value="formData.priority"
                    :min="1"
                    :max="999"
                    placeholder="请输入优先级"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item label="规则描述" name="description">
              <a-textarea
                v-model:value="formData.description"
                :rows="3"
                placeholder="请输入规则描述"
              />
            </a-form-item>

            <a-form-item label="是否启用" name="is_active">
              <a-switch
                v-model:checked="formData.is_active"
                checked-children="启用"
                un-checked-children="禁用"
              />
            </a-form-item>
          </a-form>
        </a-card>

        <a-card title="规则条件" class="rule-card">
          <a-form-item label="条件配置" name="conditions">
            <a-textarea
              v-model:value="conditionsText"
              :rows="8"
              placeholder="请输入JSON格式的条件配置"
            />
          </a-form-item>
        </a-card>

        <a-card title="规则动作" class="rule-card">
          <a-form-item label="动作配置" name="actions">
            <a-textarea
              v-model:value="actionsText"
              :rows="8"
              placeholder="请输入JSON格式的动作配置"
            />
          </a-form-item>
        </a-card>

        <a-card title="适用范围" class="rule-card">
          <a-form-item label="范围配置" name="scope">
            <a-textarea
              v-model:value="scopeText"
              :rows="6"
              placeholder="请输入JSON格式的范围配置"
            />
          </a-form-item>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const formRef = ref()
const loading = ref(false)
const saving = ref(false)
const conditionsText = ref('')
const actionsText = ref('')
const scopeText = ref('')
const originalData = ref(null)

// 表单数据
const formData = reactive({
  name: '',
  code: '',
  type: '',
  description: '',
  priority: 100,
  is_active: true
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 100, message: '规则名称长度应在2-100个字符之间', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择规则类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请输入优先级', trigger: 'blur' },
    { type: 'number', min: 1, max: 999, message: '优先级应在1-999之间', trigger: 'blur' }
  ]
}

// 生命周期
onMounted(() => {
  loadRuleData()
})

// 加载规则数据
const loadRuleData = async () => {
  const ruleId = route.params.id
  if (!ruleId) {
    message.error('缺少规则ID参数')
    return
  }
  
  loading.value = true
  try {
    // 临时模拟数据
    const data = {
      id: ruleId,
      name: '增值税计算规则',
      code: 'VAT_CALC_001',
      type: '税率计算',
      description: '根据企业类型和商品类别计算增值税',
      is_active: true,
      priority: 100,
      conditions: {
        enterprise_type: 'general_taxpayer',
        product_category: 'goods',
        tax_rate: 0.13
      },
      actions: {
        calculate_tax: {
          formula: 'amount * tax_rate',
          round_method: 'round_up'
        }
      },
      scope: {
        regions: ['全国'],
        industries: ['制造业', '批发零售业']
      }
    }
    
    originalData.value = data
    
    // 填充表单数据
    Object.keys(formData).forEach(key => {
      if (data[key] !== undefined) {
        formData[key] = data[key]
      }
    })
    
    // 填充JSON配置
    conditionsText.value = JSON.stringify(data.conditions, null, 2)
    actionsText.value = JSON.stringify(data.actions, null, 2)
    scopeText.value = JSON.stringify(data.scope, null, 2)
    
  } catch (error) {
    message.error('加载规则数据失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 返回
const handleBack = () => {
  router.back()
}

// 重置
const handleReset = () => {
  if (originalData.value) {
    Object.keys(formData).forEach(key => {
      if (originalData.value[key] !== undefined) {
        formData[key] = originalData.value[key]
      }
    })
    
    conditionsText.value = JSON.stringify(originalData.value.conditions, null, 2)
    actionsText.value = JSON.stringify(originalData.value.actions, null, 2)
    scopeText.value = JSON.stringify(originalData.value.scope, null, 2)
  }
}

// 保存
const handleSave = async () => {
  try {
    await formRef.value.validate()
    
    // 验证JSON格式
    let conditions, actions, scope
    try {
      conditions = conditionsText.value ? JSON.parse(conditionsText.value) : {}
      actions = actionsText.value ? JSON.parse(actionsText.value) : {}
      scope = scopeText.value ? JSON.parse(scopeText.value) : {}
    } catch (error) {
      message.error('JSON格式错误，请检查配置')
      return
    }
    
    saving.value = true
    
    const ruleData = {
      ...formData,
      conditions,
      actions,
      scope
    }
    
    // 这里应该调用API更新规则
    console.log('更新规则数据:', ruleData)
    
    message.success('规则更新成功')
    router.push('/taxrule')
    
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单填写是否正确')
    } else {
      message.error('更新规则失败：' + error.message)
    }
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.tax-rule-edit-page {
  background-color: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background-color: white;
  margin-bottom: 16px;
}

.page-content {
  padding: 0 24px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.rule-card {
  margin-top: 16px;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}

:deep(.ant-page-header-heading-title) {
  font-size: 20px;
  font-weight: 600;
}
</style>
