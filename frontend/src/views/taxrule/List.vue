<template>
  <div class="tax-rule-list-container">
    <!-- 页面头部 -->
    <MacOSPageHeader
      :title="pageTitle"
      :breadcrumbs="breadcrumbs"
      :show-back="false"
    >
      <template #actions>
        <MacOSButton
          type="primary"
          icon="plus"
          @click="handleCreate"
        >
          新增税则
        </MacOSButton>
        <MacOSButton
          icon="download"
          @click="handleExport"
        >
          导出
        </MacOSButton>
        <MacOSButton
          icon="upload"
          @click="handleImport"
        >
          导入
        </MacOSButton>
      </template>
    </MacOSPageHeader>

    <!-- 搜索表单 -->
    <MacOSCard class="search-card">
      <SearchForm
        :form-items="searchFormItems"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
      />
    </MacOSCard>

    <!-- 数据表格 -->
    <MacOSCard>
      <template #title>
        <div class="table-header">
          <span>税则列表</span>
          <div class="table-actions">
            <MacOSButton
              v-if="selectedRowKeys.length > 0"
              type="danger"
              size="small"
              @click="handleBatchDelete"
            >
              批量删除 ({{ selectedRowKeys.length }})
            </MacOSButton>
          </div>
        </div>
      </template>

      <MacOSDataTable
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        @change="handleTableChange"
      />
    </MacOSCard>

    <!-- 创建/编辑弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :width="800"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <TaxRuleForm
        ref="taxRuleFormRef"
        :form-data="currentRecord"
        :is-edit="isEdit"
        @submit="handleFormSubmit"
      />
    </a-modal>

    <!-- 导入弹窗 -->
    <a-modal
      v-model:visible="importModalVisible"
      title="导入税则"
      :width="600"
      :confirm-loading="importLoading"
      @ok="handleImportOk"
      @cancel="handleImportCancel"
    >
      <MacOSFileUpload
        ref="fileUploadRef"
        accept=".xlsx,.xls,.csv"
        :max-size="10"
        @change="handleFileChange"
      />
      <div class="import-tips">
        <p>支持的文件格式：Excel (.xlsx, .xls) 或 CSV (.csv)</p>
        <p>文件大小限制：10MB</p>
        <a href="/templates/tax_rule_template.xlsx" download>下载模板文件</a>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  MacOSPageHeader,
  MacOSCard,
  MacOSButton,
  MacOSDataTable,
  MacOSFileUpload,
  SearchForm
} from '@/components/common'
import TaxRuleForm from './components/TaxRuleForm.vue'
import {
  getTaxRules,
  deleteTaxRule,
  batchDeleteTaxRules,
  exportTaxRules,
  importTaxRules
} from '@/api/taxRule'
import { getTaxTypes } from '@/api/taxType'

export default defineComponent({
  name: 'TaxRuleList',
  components: {
    MacOSPageHeader,
    MacOSCard,
    MacOSButton,
    MacOSDataTable,
    MacOSFileUpload,
    SearchForm,
    TaxRuleForm
  },
  setup() {
    const router = useRouter()

    // 响应式数据
    const loading = ref(false)
    const dataSource = ref([])
    const selectedRowKeys = ref([])
    const modalVisible = ref(false)
    const modalLoading = ref(false)
    const importModalVisible = ref(false)
    const importLoading = ref(false)
    const currentRecord = ref({})
    const isEdit = ref(false)
    const taxTypes = ref([])
    const uploadFile = ref(null)

    // 引用
    const taxRuleFormRef = ref(null)
    const fileUploadRef = ref(null)

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
    })

    // 搜索表单配置
    const searchForm = reactive({
      taxTypeId: '',
      name: '',
      isActive: null
    })

    // 计算属性
    const pageTitle = computed(() => '税则管理')
    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '税务配置', path: '/taxtype' },
      { title: '税则管理' }
    ])

    const modalTitle = computed(() => isEdit.value ? '编辑税则' : '新增税则')

    const searchFormItems = computed(() => [
      {
        type: 'select',
        field: 'taxTypeId',
        label: '税种',
        placeholder: '请选择税种',
        options: taxTypes.value.map(item => ({
          label: item.name,
          value: item.id
        })),
        allowClear: true
      },
      {
        type: 'input',
        field: 'name',
        label: '税则名称',
        placeholder: '请输入税则名称'
      },
      {
        type: 'select',
        field: 'isActive',
        label: '状态',
        placeholder: '请选择状态',
        options: [
          { label: '有效', value: true },
          { label: '无效', value: false }
        ],
        allowClear: true
      }
    ])

    // 表格列配置
    const columns = [
      {
        title: '税则名称',
        dataIndex: 'name',
        key: 'name',
        ellipsis: true
      },
      {
        title: '所属税种',
        dataIndex: ['taxType', 'name'],
        key: 'taxTypeName',
        ellipsis: true
      },
      {
        title: '生效日期',
        dataIndex: 'effectiveDate',
        key: 'effectiveDate',
        width: 120,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : '-'
        }
      },
      {
        title: '失效日期',
        dataIndex: 'expiryDate',
        key: 'expiryDate',
        width: 120,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : '永久有效'
        }
      },
      {
        title: '状态',
        dataIndex: 'isActive',
        key: 'isActive',
        width: 80,
        customRender: ({ record }) => {
          const now = new Date()
          const effectiveDate = new Date(record.effectiveDate)
          const expiryDate = record.expiryDate ? new Date(record.expiryDate) : null
          
          const isActive = effectiveDate <= now && (!expiryDate || expiryDate > now)
          
          return h('a-tag', {
            color: isActive ? 'green' : 'red'
          }, isActive ? '有效' : '无效')
        }
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 160,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleString() : '-'
        }
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        customRender: ({ record }) => {
          return [
            h('a', {
              onClick: () => handleView(record)
            }, '查看'),
            h('a-divider', { type: 'vertical' }),
            h('a', {
              onClick: () => handleEdit(record)
            }, '编辑'),
            h('a-divider', { type: 'vertical' }),
            h('a', {
              style: { color: '#ff4d4f' },
              onClick: () => handleDelete(record)
            }, '删除')
          ]
        }
      }
    ]

    // 行选择配置
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (keys) => {
        selectedRowKeys.value = keys
      }
    }

    // 方法
    const fetchData = async () => {
      loading.value = true
      try {
        const params = {
          ...searchForm,
          page: pagination.current,
          pageSize: pagination.pageSize
        }

        const response = await getTaxRules(params)
        if (response.code === 200) {
          dataSource.value = response.data.data || []
          pagination.total = response.data.total || 0
        }
      } catch (error) {
        message.error('获取税则列表失败')
      } finally {
        loading.value = false
      }
    }

    const fetchTaxTypes = async () => {
      try {
        const response = await getTaxTypes({ pageSize: 1000 })
        if (response.code === 200) {
          taxTypes.value = response.data.data || []
        }
      } catch (error) {
        console.error('获取税种列表失败:', error)
      }
    }

    const handleSearch = (values) => {
      Object.assign(searchForm, values)
      pagination.current = 1
      fetchData()
    }

    const handleReset = () => {
      Object.assign(searchForm, {
        taxTypeId: '',
        name: '',
        isActive: null
      })
      pagination.current = 1
      fetchData()
    }

    const handleTableChange = (pag) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      fetchData()
    }

    const handleCreate = () => {
      currentRecord.value = {}
      isEdit.value = false
      modalVisible.value = true
    }

    const handleView = (record) => {
      router.push(`/taxrule/detail/${record.id}`)
    }

    const handleEdit = (record) => {
      currentRecord.value = { ...record }
      isEdit.value = true
      modalVisible.value = true
    }

    const handleDelete = (record) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除税则"${record.name}"吗？`,
        onOk: async () => {
          try {
            await deleteTaxRule(record.id)
            message.success('删除成功')
            fetchData()
          } catch (error) {
            message.error('删除失败')
          }
        }
      })
    }

    const handleBatchDelete = () => {
      Modal.confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${selectedRowKeys.value.length} 条税则吗？`,
        onOk: async () => {
          try {
            await batchDeleteTaxRules(selectedRowKeys.value)
            message.success('批量删除成功')
            selectedRowKeys.value = []
            fetchData()
          } catch (error) {
            message.error('批量删除失败')
          }
        }
      })
    }

    const handleModalOk = () => {
      taxRuleFormRef.value?.submit()
    }

    const handleModalCancel = () => {
      modalVisible.value = false
      currentRecord.value = {}
    }

    const handleFormSubmit = () => {
      modalVisible.value = false
      fetchData()
    }

    const handleExport = async () => {
      try {
        const blob = await exportTaxRules('excel')
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `税则列表_${new Date().toISOString().slice(0, 10)}.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)
        message.success('导出成功')
      } catch (error) {
        message.error('导出失败')
      }
    }

    const handleImport = () => {
      importModalVisible.value = true
    }

    const handleFileChange = (file) => {
      uploadFile.value = file
    }

    const handleImportOk = async () => {
      if (!uploadFile.value) {
        message.error('请选择要导入的文件')
        return
      }

      importLoading.value = true
      try {
        const formData = new FormData()
        formData.append('file', uploadFile.value)
        
        await importTaxRules(formData)
        message.success('导入成功')
        importModalVisible.value = false
        uploadFile.value = null
        fetchData()
      } catch (error) {
        message.error('导入失败')
      } finally {
        importLoading.value = false
      }
    }

    const handleImportCancel = () => {
      importModalVisible.value = false
      uploadFile.value = null
    }

    // 生命周期
    onMounted(() => {
      fetchTaxTypes()
      fetchData()
    })

    return {
      // 响应式数据
      loading,
      dataSource,
      selectedRowKeys,
      modalVisible,
      modalLoading,
      importModalVisible,
      importLoading,
      currentRecord,
      isEdit,
      pagination,
      searchForm,
      uploadFile,

      // 引用
      taxRuleFormRef,
      fileUploadRef,

      // 计算属性
      pageTitle,
      breadcrumbs,
      modalTitle,
      searchFormItems,
      columns,
      rowSelection,

      // 方法
      handleSearch,
      handleReset,
      handleTableChange,
      handleCreate,
      handleView,
      handleEdit,
      handleDelete,
      handleBatchDelete,
      handleModalOk,
      handleModalCancel,
      handleFormSubmit,
      handleExport,
      handleImport,
      handleFileChange,
      handleImportOk,
      handleImportCancel
    }
  }
})
</script>

<style scoped>
.tax-rule-list-container {
  background: #f0f2f5;
  min-height: 100vh;
  padding: 24px;
}

.search-card {
  margin-bottom: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.import-tips {
  margin-top: 16px;
  padding: 12px;
  background: #f6f8fa;
  border-radius: 6px;
}

.import-tips p {
  margin: 4px 0;
  color: #666;
  font-size: 12px;
}

.import-tips a {
  color: #1890ff;
  text-decoration: none;
}

.import-tips a:hover {
  text-decoration: underline;
}
</style>
