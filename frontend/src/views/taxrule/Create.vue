<!--
  税务规则创建页面
  功能：创建新的税务规则
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <div class="tax-rule-create-page">
    <div class="page-header">
      <a-page-header
        title="创建税务规则"
        @back="handleBack"
      >
        <template #extra>
          <a-space>
            <a-button @click="handleReset">
              重置
            </a-button>
            <a-button type="primary" :loading="saving" @click="handleSave">
              保存
            </a-button>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <div class="page-content">
      <a-card title="基本信息">
        <a-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          layout="vertical"
        >
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="规则名称" name="name">
                <a-input
                  v-model:value="formData.name"
                  placeholder="请输入规则名称"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="规则编码" name="code">
                <a-input
                  v-model:value="formData.code"
                  placeholder="请输入规则编码"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="规则类型" name="type">
                <a-select
                  v-model:value="formData.type"
                  placeholder="请选择规则类型"
                >
                  <a-select-option value="税率计算">税率计算</a-select-option>
                  <a-select-option value="税额计算">税额计算</a-select-option>
                  <a-select-option value="优惠政策">优惠政策</a-select-option>
                  <a-select-option value="合规检查">合规检查</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="优先级" name="priority">
                <a-input-number
                  v-model:value="formData.priority"
                  :min="1"
                  :max="999"
                  placeholder="请输入优先级"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="规则描述" name="description">
            <a-textarea
              v-model:value="formData.description"
              :rows="3"
              placeholder="请输入规则描述"
            />
          </a-form-item>

          <a-form-item label="是否启用" name="is_active">
            <a-switch
              v-model:checked="formData.is_active"
              checked-children="启用"
              un-checked-children="禁用"
            />
          </a-form-item>
        </a-form>
      </a-card>

      <a-card title="规则条件" class="rule-card">
        <a-form-item label="条件配置" name="conditions">
          <a-textarea
            v-model:value="conditionsText"
            :rows="8"
            placeholder="请输入JSON格式的条件配置"
          />
        </a-form-item>
        <div class="json-tip">
          <a-alert
            message="条件配置示例"
            :description="conditionsExample"
            type="info"
            show-icon
          />
        </div>
      </a-card>

      <a-card title="规则动作" class="rule-card">
        <a-form-item label="动作配置" name="actions">
          <a-textarea
            v-model:value="actionsText"
            :rows="8"
            placeholder="请输入JSON格式的动作配置"
          />
        </a-form-item>
        <div class="json-tip">
          <a-alert
            message="动作配置示例"
            :description="actionsExample"
            type="info"
            show-icon
          />
        </div>
      </a-card>

      <a-card title="适用范围" class="rule-card">
        <a-form-item label="范围配置" name="scope">
          <a-textarea
            v-model:value="scopeText"
            :rows="6"
            placeholder="请输入JSON格式的范围配置"
          />
        </a-form-item>
        <div class="json-tip">
          <a-alert
            message="范围配置示例"
            :description="scopeExample"
            type="info"
            show-icon
          />
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const formRef = ref()
const saving = ref(false)
const conditionsText = ref('')
const actionsText = ref('')
const scopeText = ref('')

// 表单数据
const formData = reactive({
  name: '',
  code: '',
  type: '',
  description: '',
  priority: 100,
  is_active: true
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 100, message: '规则名称长度应在2-100个字符之间', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入规则编码', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '规则编码只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择规则类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请输入优先级', trigger: 'blur' },
    { type: 'number', min: 1, max: 999, message: '优先级应在1-999之间', trigger: 'blur' }
  ]
}

// 示例配置
const conditionsExample = `{
  "enterprise_type": "general_taxpayer",
  "product_category": "goods",
  "tax_rate": 0.13,
  "amount_range": {
    "min": 0,
    "max": 1000000
  }
}`

const actionsExample = `{
  "calculate_tax": {
    "formula": "amount * tax_rate",
    "round_method": "round_up",
    "decimal_places": 2
  },
  "apply_discount": {
    "type": "percentage",
    "value": 0.1
  }
}`

const scopeExample = `{
  "regions": ["全国"],
  "industries": ["制造业", "批发零售业"],
  "enterprise_types": ["一般纳税人"],
  "effective_date": "2024-01-01",
  "expiry_date": "2024-12-31"
}`

// 生命周期
onMounted(() => {
  // 如果是复制模式，加载原规则数据
  if (route.query.copy) {
    loadCopyData(route.query.copy)
  }
})

// 加载复制数据
const loadCopyData = async (ruleId) => {
  try {
    // 这里应该调用API获取原规则数据
    message.info('正在加载原规则数据...')
  } catch (error) {
    message.error('加载原规则数据失败：' + error.message)
  }
}

// 返回
const handleBack = () => {
  router.back()
}

// 重置
const handleReset = () => {
  formRef.value.resetFields()
  conditionsText.value = ''
  actionsText.value = ''
  scopeText.value = ''
}

// 保存
const handleSave = async () => {
  try {
    await formRef.value.validate()
    
    // 验证JSON格式
    let conditions, actions, scope
    try {
      conditions = conditionsText.value ? JSON.parse(conditionsText.value) : {}
      actions = actionsText.value ? JSON.parse(actionsText.value) : {}
      scope = scopeText.value ? JSON.parse(scopeText.value) : {}
    } catch (error) {
      message.error('JSON格式错误，请检查配置')
      return
    }
    
    saving.value = true
    
    const ruleData = {
      ...formData,
      conditions,
      actions,
      scope
    }
    
    // 这里应该调用API保存规则
    console.log('保存规则数据:', ruleData)
    
    message.success('规则创建成功')
    router.push('/taxrule')
    
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单填写是否正确')
    } else {
      message.error('创建规则失败：' + error.message)
    }
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.tax-rule-create-page {
  background-color: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background-color: white;
  margin-bottom: 16px;
}

.page-content {
  padding: 0 24px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.rule-card {
  margin-top: 16px;
}

.json-tip {
  margin-top: 8px;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}

:deep(.ant-page-header-heading-title) {
  font-size: 20px;
  font-weight: 600;
}

:deep(.ant-alert-description) {
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
}
</style>
