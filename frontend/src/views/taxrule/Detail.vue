<!--
  税务规则详情页面
  功能：展示税务规则详细信息
  遵循 VUE_STANDARDS.md 规范
-->
<template>
  <div class="tax-rule-detail-page">
    <div class="page-header">
      <a-page-header
        title="税务规则详情"
        @back="handleBack"
      >
        <template #extra>
          <a-space>
            <a-button @click="handleEdit">
              <template #icon>
                <EditOutlined />
              </template>
              编辑
            </a-button>
            <a-button type="primary" @click="handleCopy">
              <template #icon>
                <CopyOutlined />
              </template>
              复制
            </a-button>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <div class="page-content">
      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
      </div>
      
      <div v-else-if="ruleData" class="rule-detail">
        <!-- 基本信息 -->
        <a-card title="基本信息" class="info-card">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="规则名称">
              {{ ruleData.name }}
            </a-descriptions-item>
            <a-descriptions-item label="规则编码">
              {{ ruleData.code }}
            </a-descriptions-item>
            <a-descriptions-item label="规则类型">
              <a-tag color="blue">{{ ruleData.type }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag :color="ruleData.is_active ? 'green' : 'red'">
                {{ ruleData.is_active ? '启用' : '禁用' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="优先级">
              {{ ruleData.priority }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDate(ruleData.created_at) }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 规则描述 -->
        <a-card v-if="ruleData.description" title="规则描述" class="info-card">
          <div class="content-text">{{ ruleData.description }}</div>
        </a-card>

        <!-- 规则条件 -->
        <a-card title="规则条件" class="info-card">
          <pre class="json-content">{{ formatJSON(ruleData.conditions) }}</pre>
        </a-card>

        <!-- 规则动作 -->
        <a-card title="规则动作" class="info-card">
          <pre class="json-content">{{ formatJSON(ruleData.actions) }}</pre>
        </a-card>

        <!-- 适用范围 -->
        <a-card v-if="ruleData.scope" title="适用范围" class="info-card">
          <pre class="json-content">{{ formatJSON(ruleData.scope) }}</pre>
        </a-card>
      </div>

      <div v-else class="empty-container">
        <a-empty description="未找到规则信息" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { EditOutlined, CopyOutlined } from '@ant-design/icons-vue'
import { formatDate } from '@/utils/date'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const ruleData = ref(null)

// 生命周期
onMounted(() => {
  loadRuleDetail()
})

// 加载规则详情
const loadRuleDetail = async () => {
  const ruleId = route.params.id
  if (!ruleId) {
    message.error('缺少规则ID参数')
    return
  }
  
  loading.value = true
  try {
    // 临时模拟数据
    ruleData.value = {
      id: ruleId,
      name: '增值税计算规则',
      code: 'VAT_CALC_001',
      type: '税率计算',
      description: '根据企业类型和商品类别计算增值税',
      is_active: true,
      priority: 100,
      conditions: {
        enterprise_type: 'general_taxpayer',
        product_category: 'goods',
        tax_rate: 0.13
      },
      actions: {
        calculate_tax: {
          formula: 'amount * tax_rate',
          round_method: 'round_up'
        }
      },
      scope: {
        regions: ['全国'],
        industries: ['制造业', '批发零售业']
      },
      created_at: new Date().toISOString()
    }
  } catch (error) {
    message.error('加载规则详情失败：' + error.message)
    ruleData.value = null
  } finally {
    loading.value = false
  }
}

// 返回
const handleBack = () => {
  router.back()
}

// 编辑
const handleEdit = () => {
  router.push(`/taxrule/edit/${route.params.id}`)
}

// 复制
const handleCopy = () => {
  router.push(`/taxrule/create?copy=${route.params.id}`)
}

// 格式化JSON
const formatJSON = (data) => {
  try {
    const obj = typeof data === 'string' ? JSON.parse(data) : data
    return JSON.stringify(obj, null, 2)
  } catch (error) {
    return data
  }
}
</script>

<style scoped>
.tax-rule-detail-page {
  background-color: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background-color: white;
  margin-bottom: 16px;
}

.page-content {
  padding: 0 24px 24px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  background-color: white;
  border-radius: 6px;
}

.rule-detail {
  max-width: 1200px;
  margin: 0 auto;
}

.info-card {
  margin-bottom: 16px;
}

.content-text {
  line-height: 1.6;
  color: #262626;
  white-space: pre-wrap;
  word-break: break-word;
}

.json-content {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
  line-height: 1.45;
  overflow-x: auto;
  white-space: pre;
  max-height: 300px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  background-color: #fafafa;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}

:deep(.ant-page-header-heading-title) {
  font-size: 20px;
  font-weight: 600;
}
</style>
