<template>
  <div class="invoice-import-container">
    <PageHeader
      title="发票批量导入"
      description="支持Excel、CSV格式的发票批量导入，快速录入大量发票数据"
      :breadcrumbs="breadcrumbs"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
          <a-button @click="downloadTemplate">
            <DownloadOutlined />
            下载模板
          </a-button>
          <a-button
            type="primary"
            :loading="importing"
            :disabled="!uploadedFile || importResult?.status === 'success'"
            @click="startImport"
          >
            <ImportOutlined />
            {{ importing ? '导入中...' : '开始导入' }}
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <div class="import-content">
      <a-row :gutter="24">
        <!-- 左侧上传和设置 -->
        <a-col :span="10">
          <!-- 文件上传 -->
          <a-card title="上传文件">
            <a-upload-dragger
              v-if="!uploadedFile"
              :before-upload="beforeUpload"
              accept=".xlsx,.xls,.csv"
              :show-upload-list="false"
            >
              <p class="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p class="ant-upload-text">
                点击或拖拽文件到此区域上传
              </p>
              <p class="ant-upload-hint">
                支持 Excel (.xlsx, .xls) 和 CSV 格式文件，文件大小不超过 50MB
              </p>
            </a-upload-dragger>

            <!-- 已上传文件信息 -->
            <div v-else class="uploaded-file">
              <div class="file-info">
                <FileExcelOutlined class="file-icon" />
                <div class="file-details">
                  <div class="file-name">
                    {{ uploadedFile.name }}
                  </div>
                  <div class="file-meta">
                    {{ formatFileSize(uploadedFile.size) }} • {{ uploadedFile.type }}
                  </div>
                </div>
                <a-button type="link" danger @click="removeFile">
                  <DeleteOutlined />
                </a-button>
              </div>

              <div v-if="filePreview" class="file-preview">
                <h4>文件预览 (前{{ filePreview.headers.length > 0 ? '10' : '0' }}行)</h4>
                <a-table
                  :columns="previewColumns"
                  :data-source="filePreview.data"
                  :pagination="false"
                  size="small"
                  :scroll="{ x: 800 }"
                />
                <div class="preview-summary">
                  总计 {{ filePreview.totalRows }} 行数据
                </div>
              </div>
            </div>

            <!-- 导入设置 -->
            <div v-if="uploadedFile">
              <a-divider orientation="left">
                导入设置
              </a-divider>
              <a-form layout="vertical">
                <a-form-item label="数据起始行">
                  <a-input-number
                    v-model:value="importSettings.startRow"
                    :min="1"
                    :max="filePreview?.totalRows || 1"
                    style="width: 100%"
                  />
                  <div class="form-help">
                    指定数据开始的行号，通常为第2行（跳过表头）
                  </div>
                </a-form-item>

                <a-form-item label="字段映射">
                  <div class="field-mapping">
                    <div
                      v-for="field in requiredFields"
                      :key="field.key"
                      class="mapping-row"
                    >
                      <div class="field-label">
                        {{ field.label }}:
                      </div>
                      <a-select
                        v-model:value="importSettings.fieldMapping[field.key]"
                        placeholder="选择列"
                        style="width: 200px"
                        :options="columnOptions"
                      />
                      <a-tag v-if="field.required" color="red" size="small">
                        必填
                      </a-tag>
                    </div>
                  </div>
                </a-form-item>

                <a-form-item label="导入选项">
                  <a-checkbox-group v-model:value="importSettings.options">
                    <a-checkbox value="skipExisting">
                      跳过已存在的发票
                    </a-checkbox>
                    <a-checkbox value="updateExisting">
                      更新已存在的发票
                    </a-checkbox>
                    <a-checkbox value="validateData">
                      严格数据验证
                    </a-checkbox>
                    <a-checkbox value="autoClassify">
                      自动分类
                    </a-checkbox>
                  </a-checkbox-group>
                </a-form-item>
              </a-form>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧进度和结果 -->
        <a-col :span="14">
          <!-- 导入进度 -->
          <a-card v-if="importing || importResult" title="导入进度">
            <div v-if="importing" class="import-progress">
              <a-progress
                :percent="importProgress.percent"
                :status="importProgress.status"
                :stroke-color="getProgressColor(importProgress.status)"
              />
              <div class="progress-details">
                <p>{{ importProgress.message }}</p>
                <div class="progress-stats">
                  <a-statistic-countdown
                    v-if="importProgress.startTime"
                    title="已用时间"
                    :value="importProgress.startTime + 60000"
                    format="mm:ss"
                  />
                  <a-statistic
                    title="处理进度"
                    :value="importProgress.processed"
                    :suffix="`/ ${importProgress.total}`"
                  />
                </div>
              </div>
            </div>

            <!-- 导入结果 -->
            <div v-if="importResult" class="import-result">
              <a-result
                :status="importResult.status"
                :title="importResult.title"
                :sub-title="importResult.description"
              >
                <template #extra>
                  <a-space>
                    <a-button
                      v-if="importResult.status === 'error'"
                      type="primary"
                      @click="retryImport"
                    >
                      重新导入
                    </a-button>
                    <a-button v-if="importResult.hasErrors" @click="downloadErrorLog">
                      下载错误日志
                    </a-button>
                    <a-button v-if="importResult.status === 'success'" @click="viewImportedData">
                      查看导入数据
                    </a-button>
                  </a-space>
                </template>
              </a-result>

              <!-- 导入统计 -->
              <div class="import-statistics">
                <a-row :gutter="16">
                  <a-col :span="6">
                    <a-statistic
                      title="总行数"
                      :value="importResult.statistics.total"
                      :value-style="{ color: '#1890ff' }"
                    />
                  </a-col>
                  <a-col :span="6">
                    <a-statistic
                      title="成功导入"
                      :value="importResult.statistics.success"
                      :value-style="{ color: '#52c41a' }"
                    />
                  </a-col>
                  <a-col :span="6">
                    <a-statistic
                      title="跳过行数"
                      :value="importResult.statistics.skipped"
                      :value-style="{ color: '#faad14' }"
                    />
                  </a-col>
                  <a-col :span="6">
                    <a-statistic
                      title="错误行数"
                      :value="importResult.statistics.errors"
                      :value-style="{ color: '#f5222d' }"
                    />
                  </a-col>
                </a-row>
              </div>

              <!-- 错误详情 -->
              <div v-if="importResult.errorDetails?.length" class="error-details">
                <h4>错误详情</h4>
                <a-table
                  :columns="errorColumns"
                  :data-source="importResult.errorDetails"
                  :pagination="{ pageSize: 5 }"
                  size="small"
                />
              </div>
            </div>
          </a-card>

          <!-- 使用说明 -->
          <a-card v-else title="使用说明">
            <div class="instructions">
              <h4>导入步骤：</h4>
              <ol>
                <li>下载并填写Excel模板文件</li>
                <li>上传填写好的文件</li>
                <li>检查数据预览，配置字段映射</li>
                <li>设置导入选项</li>
                <li>点击"开始导入"执行批量导入</li>
              </ol>

              <h4>注意事项：</h4>
              <ul>
                <li>支持 .xlsx、.xls、.csv 格式文件</li>
                <li>文件大小不能超过 50MB</li>
                <li>建议单次导入不超过 10,000 条记录</li>
                <li>发票号码和发票代码是必填字段</li>
                <li>金额字段只支持数字格式</li>
                <li>日期格式建议使用 YYYY-MM-DD</li>
              </ul>

              <h4>字段说明：</h4>
              <a-table
                :columns="fieldColumns"
                :data-source="fieldDescriptions"
                :pagination="false"
                size="small"
              />
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import PageHeader from '@/components/common/PageHeader.vue'
import { importInvoices } from '@/api/invoice'
import {
  ArrowLeftOutlined,
  DownloadOutlined,
  ImportOutlined,
  InboxOutlined,
  FileExcelOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'InvoiceImport',
  components: {
    PageHeader,
    ArrowLeftOutlined,
    DownloadOutlined,
    ImportOutlined,
    InboxOutlined,
    FileExcelOutlined,
    DeleteOutlined
  },
  setup () {
    const router = useRouter()
    const uploadedFile = ref(null)
    const filePreview = ref(null)
    const importing = ref(false)
    const importResult = ref(null)

    // 导入进度
    const importProgress = reactive({
      percent: 0,
      status: 'active',
      message: '准备导入...',
      processed: 0,
      total: 0,
      startTime: null
    })

    // 导入设置
    const importSettings = reactive({
      startRow: 2,
      fieldMapping: {},
      options: ['skipExisting', 'validateData']
    })

    // 必填字段定义
    const requiredFields = [
      { key: 'invoiceNumber', label: '发票号码', required: true },
      { key: 'invoiceCode', label: '发票代码', required: true },
      { key: 'issueDate', label: '开票日期', required: true },
      { key: 'sellerName', label: '销售方名称', required: true },
      { key: 'buyerName', label: '购买方名称', required: true },
      { key: 'totalAmount', label: '价税合计', required: true },
      { key: 'taxAmount', label: '税额', required: false },
      { key: 'amountWithoutTax', label: '不含税金额', required: false },
      { key: 'taxRate', label: '税率', required: false },
      { key: 'type', label: '发票类型', required: false }
    ]

    // 字段描述
    const fieldDescriptions = [
      { field: '发票号码', type: '文本', required: '是', example: '12345678', description: '8位数字' },
      { field: '发票代码', type: '文本', required: '是', example: '1234567890', description: '10位数字' },
      { field: '开票日期', type: '日期', required: '是', example: '2024-01-15', description: 'YYYY-MM-DD格式' },
      { field: '销售方名称', type: '文本', required: '是', example: '某某有限公司', description: '企业全称' },
      { field: '购买方名称', type: '文本', required: '是', example: '某某科技公司', description: '企业全称' },
      { field: '价税合计', type: '数字', required: '是', example: '1000.00', description: '含税总金额' }
    ]

    // 面包屑导航
    const breadcrumbs = [
      { title: '首页', path: '/' },
      { title: '票据管理', path: '/invoice' },
      { title: '发票列表', path: '/invoice/list' },
      { title: '批量导入' }
    ]

    // 计算属性
    const columnOptions = computed(() => {
      if (!filePreview.value?.headers) return []
      return filePreview.value.headers.map((header, index) => ({
        label: `列${index + 1}: ${header}`,
        value: index
      }))
    })

    const previewColumns = computed(() => {
      if (!filePreview.value?.headers) return []
      return filePreview.value.headers.map((header, index) => ({
        title: `列${index + 1}`,
        dataIndex: `col_${index}`,
        key: `col_${index}`,
        width: 120,
        ellipsis: true
      }))
    })

    // 错误详情表格列
    const errorColumns = [
      {
        title: '行号',
        dataIndex: 'row',
        key: 'row',
        width: 60
      },
      {
        title: '字段',
        dataIndex: 'field',
        key: 'field',
        width: 100
      },
      {
        title: '错误信息',
        dataIndex: 'message',
        key: 'message'
      }
    ]

    // 字段说明表格列
    const fieldColumns = [
      {
        title: '字段名',
        dataIndex: 'field',
        key: 'field'
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type'
      },
      {
        title: '必填',
        dataIndex: 'required',
        key: 'required'
      },
      {
        title: '示例',
        dataIndex: 'example',
        key: 'example'
      },
      {
        title: '说明',
        dataIndex: 'description',
        key: 'description'
      }
    ]

    // 方法
    const beforeUpload = (file) => {
      const validTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv'
      ]

      if (!validTypes.includes(file.type)) {
        message.error('只支持 Excel 和 CSV 格式文件')
        return false
      }

      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        message.error('文件大小不能超过 50MB')
        return false
      }

      uploadedFile.value = file
      parseFile(file)
      return false
    }

    const parseFile = async (_file) => {
      try {
        // 这里应该调用文件解析API
        // 暂时模拟数据
        filePreview.value = {
          headers: ['发票号码', '发票代码', '开票日期', '销售方名称', '购买方名称', '价税合计'],
          data: [
            { col_0: '12345678', col_1: '1234567890', col_2: '2024-01-15', col_3: '某某有限公司', col_4: '购买方公司', col_5: '1000.00' },
            { col_0: '12345679', col_1: '1234567890', col_2: '2024-01-16', col_3: '某某有限公司', col_4: '购买方公司2', col_5: '2000.00' }
          ],
          totalRows: 100
        }

        // 自动映射字段
        autoMapFields()

        message.success('文件解析成功')
      } catch (error) {
        message.error('文件解析失败')
      }
    }

    const autoMapFields = () => {
      const headerMapping = {
        发票号码: 'invoiceNumber',
        发票代码: 'invoiceCode',
        开票日期: 'issueDate',
        销售方名称: 'sellerName',
        购买方名称: 'buyerName',
        价税合计: 'totalAmount',
        税额: 'taxAmount',
        不含税金额: 'amountWithoutTax',
        税率: 'taxRate',
        发票类型: 'type'
      }

      filePreview.value.headers.forEach((header, index) => {
        const fieldKey = headerMapping[header]
        if (fieldKey) {
          importSettings.fieldMapping[fieldKey] = index
        }
      })
    }

    const removeFile = () => {
      uploadedFile.value = null
      filePreview.value = null
      importSettings.fieldMapping = {}
      importResult.value = null
    }

    const downloadTemplate = () => {
      // 这里应该下载模板文件
      message.success('模板下载功能开发中...')
    }

    const startImport = async () => {
      // 验证字段映射
      const requiredMappings = requiredFields.filter(field => field.required)
      const missingMappings = requiredMappings.filter(field =>
        importSettings.fieldMapping[field.key] === undefined
      )

      if (missingMappings.length > 0) {
        message.error(`请映射必填字段: ${missingMappings.map(f => f.label).join(', ')}`)
        return
      }

      importing.value = true
      importProgress.percent = 0
      importProgress.status = 'active'
      importProgress.message = '开始导入...'
      importProgress.processed = 0
      importProgress.total = filePreview.value.totalRows
      importProgress.startTime = Date.now()

      try {
        // 模拟导入进度
        const progressInterval = setInterval(() => {
          if (importProgress.percent < 90) {
            importProgress.percent += 10
            importProgress.processed = Math.floor(importProgress.total * importProgress.percent / 100)
            importProgress.message = `正在处理第 ${importProgress.processed} 行...`
          }
        }, 500)

        const formData = new FormData()
        formData.append('file', uploadedFile.value)
        formData.append('settings', JSON.stringify(importSettings))

        const response = await importInvoices(formData)

        clearInterval(progressInterval)
        importProgress.percent = 100
        importProgress.processed = importProgress.total

        if (response.code === 200) {
          importResult.value = {
            status: 'success',
            title: '导入成功',
            description: `成功导入 ${response.data.success} 条发票记录`,
            statistics: {
              total: response.data.total || 100,
              success: response.data.success || 95,
              skipped: response.data.skipped || 3,
              errors: response.data.errors || 2
            },
            hasErrors: response.data.errors > 0,
            errorDetails: response.data.errorDetails || [
              { row: 5, field: '发票号码', message: '格式不正确' },
              { row: 12, field: '开票日期', message: '日期格式错误' }
            ]
          }
          message.success('发票导入完成')
        } else {
          throw new Error(response.message || '导入失败')
        }
      } catch (error) {
        importResult.value = {
          status: 'error',
          title: '导入失败',
          description: error.message || '导入过程中出现错误',
          statistics: {
            total: 0,
            success: 0,
            skipped: 0,
            errors: 0
          },
          hasErrors: false
        }
        message.error('发票导入失败')
      } finally {
        importing.value = false
      }
    }

    const retryImport = () => {
      importResult.value = null
      startImport()
    }

    const downloadErrorLog = () => {
      message.info('下载错误日志功能开发中...')
    }

    const viewImportedData = () => {
      router.push('/invoice/list')
    }

    const goBack = () => {
      router.go(-1)
    }

    // 辅助方法
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const getProgressColor = (status) => {
      const colors = {
        active: '#1890ff',
        success: '#52c41a',
        exception: '#f5222d'
      }
      return colors[status] || '#1890ff'
    }

    return {
      uploadedFile,
      filePreview,
      importing,
      importResult,
      importProgress,
      importSettings,
      requiredFields,
      fieldDescriptions,
      breadcrumbs,
      columnOptions,
      previewColumns,
      errorColumns,
      fieldColumns,
      beforeUpload,
      removeFile,
      downloadTemplate,
      startImport,
      retryImport,
      downloadErrorLog,
      viewImportedData,
      goBack,
      formatFileSize,
      getProgressColor
    }
  }
})
</script>

<style scoped>
.invoice-import-container {
  background: #f5f5f5;
  min-height: 100vh;
}

.import-content {
  padding: 0 24px 24px;
}

.uploaded-file {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 16px;
  background: #fafafa;
}

.file-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.file-icon {
  font-size: 24px;
  color: #52c41a;
  margin-right: 12px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #262626;
}

.file-meta {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

.file-preview {
  margin-top: 16px;
}

.file-preview h4 {
  margin-bottom: 12px;
  color: #262626;
}

.preview-summary {
  margin-top: 8px;
  font-size: 12px;
  color: #8c8c8c;
  text-align: right;
}

.form-help {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
}

.field-mapping {
  background: #fafafa;
  padding: 12px;
  border-radius: 4px;
}

.mapping-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.mapping-row:last-child {
  margin-bottom: 0;
}

.field-label {
  width: 120px;
  font-weight: 500;
  color: #262626;
}

.import-progress {
  text-align: center;
  padding: 24px 0;
}

.progress-details {
  margin-top: 16px;
}

.progress-details p {
  margin-bottom: 16px;
  color: #262626;
}

.progress-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
}

.import-result {
  text-align: center;
}

.import-statistics {
  margin: 24px 0;
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
}

.error-details {
  margin-top: 24px;
  text-align: left;
}

.error-details h4 {
  margin-bottom: 12px;
  color: #262626;
}

.instructions {
  line-height: 1.6;
}

.instructions h4 {
  margin: 20px 0 12px 0;
  color: #262626;
}

.instructions ol,
.instructions ul {
  margin: 0 0 20px 20px;
}

.instructions li {
  margin-bottom: 8px;
}

:deep(.ant-upload-drag) {
  border: 2px dashed #d9d9d9;
}

:deep(.ant-upload-drag:hover) {
  border-color: #1890ff;
}

:deep(.ant-upload-drag .ant-upload-btn) {
  padding: 60px 0;
}

:deep(.ant-progress) {
  margin-bottom: 16px;
}

:deep(.ant-result) {
  margin-bottom: 24px;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
}

:deep(.ant-statistic-content) {
  font-size: 20px;
}
</style>
