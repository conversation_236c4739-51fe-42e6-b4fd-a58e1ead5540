<template>
  <div class="invoice-detail-container">
    <PageHeader
      :title="pageTitle"
      :description="invoice.description"
      :breadcrumbs="breadcrumbs"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
          <a-button
            type="primary"
            :disabled="invoice.status === 'verified'"
            @click="verifyInvoice"
          >
            <SafetyCertificateOutlined />
            核验发票
          </a-button>
          <a-dropdown>
            <a-button>
              更多操作
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="downloadInvoice">
                  <DownloadOutlined />
                  下载发票
                </a-menu-item>
                <a-menu-item @click="editInvoice">
                  <EditOutlined />
                  编辑信息
                </a-menu-item>
                <a-menu-item @click="exportData">
                  <ExportOutlined />
                  导出数据
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item class="danger" @click="deleteInvoice">
                  <DeleteOutlined />
                  删除发票
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </template>
    </PageHeader>

    <div class="detail-content">
      <a-row :gutter="24">
        <!-- 左侧主要信息 -->
        <a-col :span="16">
          <!-- 基本信息卡片 -->
          <a-card title="发票基本信息" class="info-card">
            <a-descriptions :column="2" size="middle">
              <a-descriptions-item label="发票号码">
                {{ invoice.invoiceNumber }}
              </a-descriptions-item>
              <a-descriptions-item label="发票类型">
                <a-tag :color="getTypeColor(invoice.type)">
                  {{ getTypeName(invoice.type) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="开票日期">
                {{ invoice.issueDate }}
              </a-descriptions-item>
              <a-descriptions-item label="状态">
                <a-badge
                  :status="getStatusBadge(invoice.status)"
                  :text="getStatusText(invoice.status)"
                />
              </a-descriptions-item>
              <a-descriptions-item label="发票代码">
                {{ invoice.invoiceCode }}
              </a-descriptions-item>
              <a-descriptions-item label="校验码">
                {{ invoice.checkCode }}
              </a-descriptions-item>
              <a-descriptions-item label="机器编号">
                {{ invoice.machineNumber }}
              </a-descriptions-item>
              <a-descriptions-item label="密码区">
                <span class="password-area">{{ invoice.passwordArea }}</span>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>

          <!-- 金额信息 -->
          <a-card title="金额信息" class="info-card">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-statistic
                  title="不含税金额"
                  :value="invoice.amountWithoutTax"
                  prefix="¥"
                  :precision="2"
                  :value-style="{ color: '#3f8600' }"
                />
              </a-col>
              <a-col :span="8">
                <a-statistic
                  title="税额"
                  :value="invoice.taxAmount"
                  prefix="¥"
                  :precision="2"
                  :value-style="{ color: '#cf1322' }"
                />
              </a-col>
              <a-col :span="8">
                <a-statistic
                  title="价税合计"
                  :value="invoice.totalAmount"
                  prefix="¥"
                  :precision="2"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
            </a-row>
            <a-divider />
            <a-descriptions :column="2" size="small">
              <a-descriptions-item label="税率">
                {{ invoice.taxRate }}%
              </a-descriptions-item>
              <a-descriptions-item label="币种">
                {{ invoice.currency || '人民币' }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>

          <!-- 企业信息 -->
          <a-card title="企业信息" class="info-card">
            <a-row :gutter="24">
              <a-col :span="12">
                <h4>销售方信息</h4>
                <a-descriptions :column="1" size="small">
                  <a-descriptions-item label="企业名称">
                    {{ invoice.sellerName }}
                  </a-descriptions-item>
                  <a-descriptions-item label="纳税人识别号">
                    {{ invoice.sellerTaxNumber }}
                  </a-descriptions-item>
                  <a-descriptions-item label="地址电话">
                    {{ invoice.sellerAddress }}
                  </a-descriptions-item>
                  <a-descriptions-item label="开户行及账号">
                    {{ invoice.sellerBankInfo }}
                  </a-descriptions-item>
                </a-descriptions>
              </a-col>
              <a-col :span="12">
                <h4>购买方信息</h4>
                <a-descriptions :column="1" size="small">
                  <a-descriptions-item label="企业名称">
                    {{ invoice.buyerName }}
                  </a-descriptions-item>
                  <a-descriptions-item label="纳税人识别号">
                    {{ invoice.buyerTaxNumber }}
                  </a-descriptions-item>
                  <a-descriptions-item label="地址电话">
                    {{ invoice.buyerAddress }}
                  </a-descriptions-item>
                  <a-descriptions-item label="开户行及账号">
                    {{ invoice.buyerBankInfo }}
                  </a-descriptions-item>
                </a-descriptions>
              </a-col>
            </a-row>
          </a-card>

          <!-- 商品明细 -->
          <a-card title="商品明细" class="info-card">
            <a-table
              :columns="itemColumns"
              :data-source="invoice.items"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'amount'">
                  ¥{{ formatNumber(record.amount) }}
                </template>
                <template v-if="column.key === 'taxAmount'">
                  ¥{{ formatNumber(record.taxAmount) }}
                </template>
                <template v-if="column.key === 'totalAmount'">
                  ¥{{ formatNumber(record.totalAmount) }}
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>

        <!-- 右侧操作和附件 -->
        <a-col :span="8">
          <!-- 快捷操作 -->
          <a-card title="快捷操作" class="action-card">
            <a-space direction="vertical" size="middle" style="width: 100%">
              <a-button type="primary" block @click="verifyInvoice">
                <SafetyCertificateOutlined />
                核验发票
              </a-button>
              <a-button block @click="downloadInvoice">
                <DownloadOutlined />
                下载发票
              </a-button>
              <a-button block @click="viewOriginal">
                <EyeOutlined />
                查看原图
              </a-button>
              <a-button block @click="printInvoice">
                <PrinterOutlined />
                打印发票
              </a-button>
            </a-space>
          </a-card>

          <!-- 附件管理 -->
          <a-card title="相关附件" class="attachments-card">
            <template #extra>
              <a-button type="link" @click="uploadAttachment">
                <UploadOutlined />
                上传附件
              </a-button>
            </template>
            <div class="attachment-list">
              <div
                v-for="attachment in invoice.attachments"
                :key="attachment.id"
                class="attachment-item"
              >
                <FileTextOutlined class="attachment-icon" />
                <div class="attachment-info">
                  <div class="attachment-name">
                    {{ attachment.name }}
                  </div>
                  <div class="attachment-meta">
                    {{ formatFileSize(attachment.size) }} • {{ attachment.uploadTime }}
                  </div>
                </div>
                <a-dropdown>
                  <a-button type="link" size="small">
                    <MoreOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="previewAttachment(attachment)">
                        <EyeOutlined />
                        预览
                      </a-menu-item>
                      <a-menu-item @click="downloadAttachment(attachment)">
                        <DownloadOutlined />
                        下载
                      </a-menu-item>
                      <a-menu-item @click="deleteAttachment(attachment)">
                        <DeleteOutlined />
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
              <a-empty v-if="!invoice.attachments?.length" description="暂无附件" />
            </div>
          </a-card>

          <!-- 操作历史 -->
          <a-card title="操作历史" class="history-card">
            <a-timeline size="small">
              <a-timeline-item
                v-for="history in invoice.operationHistory"
                :key="history.id"
                :color="getHistoryColor(history.action)"
              >
                <template #dot>
                  <component :is="getHistoryIcon(history.action)" />
                </template>
                <div class="history-content">
                  <div class="history-action">
                    {{ history.actionText }}
                  </div>
                  <div class="history-meta">
                    {{ history.operator }} • {{ history.time }}
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图片预览模态框 -->
    <a-modal
      v-model:open="showImageModal"
      title="发票原图"
      width="80%"
      :footer="null"
    >
      <div class="image-preview">
        <img :src="invoice.originalImage" alt="发票原图" style="width: 100%">
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import PageHeader from '@/components/common/PageHeader.vue'
import { getInvoiceById, verifyInvoice as verifyInvoiceApi } from '@/api/invoice'
import { formatNumber, formatFileSize } from '@/utils/format'
import {
  ArrowLeftOutlined,
  SafetyCertificateOutlined,
  DownOutlined,
  DownloadOutlined,
  EditOutlined,
  ExportOutlined,
  DeleteOutlined,
  EyeOutlined,
  PrinterOutlined,
  UploadOutlined,
  FileTextOutlined,
  MoreOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'InvoiceDetail',
  components: {
    PageHeader,
    ArrowLeftOutlined,
    SafetyCertificateOutlined,
    DownOutlined,
    DownloadOutlined,
    EditOutlined,
    ExportOutlined,
    DeleteOutlined,
    EyeOutlined,
    PrinterOutlined,
    UploadOutlined,
    FileTextOutlined,
    MoreOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    ExclamationCircleOutlined
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const loading = ref(false)
    const showImageModal = ref(false)

    // 发票信息
    const invoice = ref({
      id: '',
      invoiceNumber: '',
      invoiceCode: '',
      type: '',
      status: '',
      issueDate: '',
      checkCode: '',
      machineNumber: '',
      passwordArea: '',
      sellerName: '',
      sellerTaxNumber: '',
      sellerAddress: '',
      sellerBankInfo: '',
      buyerName: '',
      buyerTaxNumber: '',
      buyerAddress: '',
      buyerBankInfo: '',
      amountWithoutTax: 0,
      taxAmount: 0,
      totalAmount: 0,
      taxRate: 0,
      currency: '人民币',
      items: [],
      attachments: [],
      operationHistory: [],
      originalImage: ''
    })

    // 商品明细表格列
    const itemColumns = [
      {
        title: '货物或应税劳务名称',
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: '规格型号',
        dataIndex: 'specification',
        key: 'specification'
      },
      {
        title: '单位',
        dataIndex: 'unit',
        key: 'unit'
      },
      {
        title: '数量',
        dataIndex: 'quantity',
        key: 'quantity'
      },
      {
        title: '单价',
        dataIndex: 'unitPrice',
        key: 'unitPrice'
      },
      {
        title: '金额',
        key: 'amount'
      },
      {
        title: '税率',
        dataIndex: 'taxRate',
        key: 'taxRate',
        render: (text) => `${text}%`
      },
      {
        title: '税额',
        key: 'taxAmount'
      }
    ]

    // 计算属性
    const pageTitle = computed(() => {
      return invoice.value.invoiceNumber || '发票详情'
    })

    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '票据管理', path: '/invoice' },
      { title: '发票列表', path: '/invoice/list' },
      { title: pageTitle.value }
    ])

    // 方法
    const fetchInvoiceDetail = async () => {
      loading.value = true
      try {
        const response = await getInvoiceById(route.params.id)
        if (response.code === 200) {
          invoice.value = response.data
        } else {
          message.error(response.message || '获取发票详情失败')
        }
      } catch (error) {
        console.error('获取发票详情失败:', error)
        if (error.response?.status === 403) {
          message.error('无权限查看该发票')
          router.push('/invoice/list')
        } else if (error.response?.status === 404) {
          message.error('发票不存在')
          router.push('/invoice/list')
        } else {
          message.error('获取发票详情失败')
        }
      } finally {
        loading.value = false
      }
    }

    const goBack = () => {
      router.go(-1)
    }

    const verifyInvoice = async () => {
      try {
        const response = await verifyInvoiceApi(invoice.value.id)
        if (response.code === 200) {
          message.success('发票核验成功')
          invoice.value.status = 'verified'
          // 添加操作历史
          invoice.value.operationHistory.unshift({
            id: Date.now(),
            action: 'verify',
            actionText: '核验发票',
            operator: '当前用户',
            time: new Date().toLocaleString()
          })
        }
      } catch (error) {
        message.error('发票核验失败')
      }
    }

    const downloadInvoice = () => {
      message.info('发票下载功能开发中...')
    }

    const editInvoice = () => {
      message.info('编辑功能开发中...')
    }

    const exportData = () => {
      message.info('导出功能开发中...')
    }

    const deleteInvoice = () => {
      message.info('删除功能开发中...')
    }

    const viewOriginal = () => {
      showImageModal.value = true
    }

    const printInvoice = () => {
      window.print()
    }

    const uploadAttachment = () => {
      message.info('上传附件功能开发中...')
    }

    const previewAttachment = (attachment) => {
      message.info(`预览附件: ${attachment.name}`)
    }

    const downloadAttachment = (attachment) => {
      message.info(`下载附件: ${attachment.name}`)
    }

    const deleteAttachment = (attachment) => {
      message.info(`删除附件: ${attachment.name}`)
    }

    // 辅助方法
    const getTypeColor = (type) => {
      const colors = {
        'vat-special': 'blue',
        'vat-ordinary': 'green',
        electronic: 'purple',
        receipt: 'orange'
      }
      return colors[type] || 'default'
    }

    const getTypeName = (type) => {
      const names = {
        'vat-special': '增值税专用发票',
        'vat-ordinary': '增值税普通发票',
        electronic: '电子发票',
        receipt: '收据'
      }
      return names[type] || '未知类型'
    }

    const getStatusBadge = (status) => {
      const badges = {
        pending: 'processing',
        verified: 'success',
        used: 'default',
        invalid: 'error'
      }
      return badges[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        pending: '待处理',
        verified: '已核验',
        used: '已使用',
        invalid: '无效'
      }
      return texts[status] || '未知'
    }

    const getHistoryColor = (action) => {
      const colors = {
        create: 'blue',
        verify: 'green',
        edit: 'orange',
        delete: 'red'
      }
      return colors[action] || 'gray'
    }

    const getHistoryIcon = (action) => {
      const icons = {
        create: 'PlusCircleOutlined',
        verify: 'CheckCircleOutlined',
        edit: 'EditOutlined',
        delete: 'DeleteOutlined'
      }
      return icons[action] || 'ClockCircleOutlined'
    }

    // 生命周期
    onMounted(() => {
      fetchInvoiceDetail()
    })

    return {
      loading,
      showImageModal,
      invoice,
      itemColumns,
      pageTitle,
      breadcrumbs,
      goBack,
      verifyInvoice,
      downloadInvoice,
      editInvoice,
      exportData,
      deleteInvoice,
      viewOriginal,
      printInvoice,
      uploadAttachment,
      previewAttachment,
      downloadAttachment,
      deleteAttachment,
      getTypeColor,
      getTypeName,
      getStatusBadge,
      getStatusText,
      getHistoryColor,
      getHistoryIcon,
      formatNumber,
      formatFileSize
    }
  }
})
</script>

<style scoped>
.invoice-detail-container {
  background: #f5f5f5;
  min-height: 100vh;
}

.detail-content {
  padding: 0 24px 24px;
}

.info-card,
.action-card,
.attachments-card,
.history-card {
  margin-bottom: 16px;
}

.password-area {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #8c8c8c;
  word-break: break-all;
}

.attachment-list {
  max-height: 300px;
  overflow-y: auto;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.attachment-item:last-child {
  border-bottom: none;
}

.attachment-icon {
  font-size: 16px;
  color: #1890ff;
  margin-right: 8px;
}

.attachment-info {
  flex: 1;
}

.attachment-name {
  font-size: 14px;
  color: #262626;
}

.attachment-meta {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

.history-content {
  margin-left: 8px;
}

.history-action {
  font-weight: 500;
  color: #262626;
}

.history-meta {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

.image-preview {
  text-align: center;
}

.danger {
  color: #ff4d4f !important;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}

:deep(.ant-card-head-title) {
  font-weight: 500;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
}

:deep(.ant-statistic-content) {
  font-size: 20px;
}
</style>
