<template>
  <div class="invoice-upload">
    <a-card>
      <template #title>
        <div class="page-header">
          <h2>发票上传</h2>
          <div class="header-actions">
            <a-button @click="goToList">
              <ArrowLeftOutlined />
              返回列表
            </a-button>
          </div>
        </div>
      </template>

      <!-- 上传方式选择 -->
      <div class="upload-method-section">
        <a-radio-group v-model:value="uploadMethod" size="large">
          <a-radio-button value="file">
            文件上传
          </a-radio-button>
          <a-radio-button value="scan">
            扫描识别
          </a-radio-button>
          <a-radio-button value="manual">
            手动录入
          </a-radio-button>
        </a-radio-group>
      </div>

      <!-- 文件上传模式 -->
      <div v-if="uploadMethod === 'file'" class="file-upload-section">
        <a-row :gutter="24">
          <a-col :span="16">
            <a-card title="文件上传" class="upload-card">
              <a-upload-dragger
                v-model:file-list="fileList"
                name="file"
                :multiple="true"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :before-upload="beforeUpload"
                accept=".pdf,.jpg,.jpeg,.png,.gif"
                :max-count="10"
                @change="handleUploadChange"
                @drop="handleDrop"
              >
                <p class="ant-upload-drag-icon">
                  <InboxOutlined style="font-size: 48px; color: #1890ff" />
                </p>
                <p class="ant-upload-text">
                  点击或拖拽文件到此区域上传
                </p>
                <p class="ant-upload-hint">
                  支持单个或批量上传。支持 PDF、JPG、PNG 等格式，单个文件不超过 10MB
                </p>
              </a-upload-dragger>

              <!-- 上传进度 -->
              <div v-if="uploading" class="upload-progress">
                <a-progress
                  :percent="uploadProgress"
                  :status="uploadStatus"
                  :show-info="true"
                />
                <p class="progress-text">
                  正在上传并识别发票信息...
                </p>
              </div>

              <!-- 批量操作 -->
              <div v-if="fileList.length > 0" class="batch-operations">
                <a-space>
                  <a-button
                    type="primary"
                    :loading="uploading"
                    :disabled="fileList.length === 0"
                    @click="startBatchUpload"
                  >
                    <UploadOutlined />
                    开始上传 ({{ fileList.length }} 个文件)
                  </a-button>
                  <a-button @click="clearFiles">
                    <DeleteOutlined />
                    清空文件
                  </a-button>
                  <a-checkbox v-model:checked="autoRecognition">
                    自动识别发票信息
                  </a-checkbox>
                </a-space>
              </div>
            </a-card>
          </a-col>

          <a-col :span="8">
            <a-card title="上传说明" class="help-card">
              <div class="help-content">
                <h4>🎯 支持的文件格式</h4>
                <ul>
                  <li>PDF 文档 (.pdf)</li>
                  <li>图片文件 (.jpg, .jpeg, .png, .gif)</li>
                  <li>单个文件不超过 10MB</li>
                  <li>同时最多上传 10 个文件</li>
                </ul>

                <h4>🔍 智能识别功能</h4>
                <ul>
                  <li>自动识别发票类型</li>
                  <li>提取发票号码、金额</li>
                  <li>识别企业信息</li>
                  <li>计算税额和税率</li>
                </ul>

                <h4>📋 上传后操作</h4>
                <ul>
                  <li>确认识别结果</li>
                  <li>手动修正错误信息</li>
                  <li>批量审核和入库</li>
                  <li>导出或打印清单</li>
                </ul>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 扫描识别模式 -->
      <div v-if="uploadMethod === 'scan'" class="scan-section">
        <a-card title="扫描识别" class="scan-card">
          <div class="scan-area">
            <div v-if="!scanning" class="scan-placeholder">
              <CameraOutlined style="font-size: 64px; color: #1890ff" />
              <h3>扫描发票</h3>
              <p>使用摄像头扫描纸质发票，系统将自动识别并录入信息</p>
              <a-button type="primary" size="large" @click="startScan">
                <CameraOutlined />
                开始扫描
              </a-button>
            </div>
            <div v-else class="scan-camera">
              <div class="camera-view">
                <div class="scan-frame" />
                <p>请将发票对准扫描框</p>
              </div>
              <div class="scan-controls">
                <a-space>
                  <a-button type="primary" @click="captureImage">
                    <CameraOutlined />
                    拍摄
                  </a-button>
                  <a-button @click="stopScan">
                    <StopOutlined />
                    停止扫描
                  </a-button>
                </a-space>
              </div>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 手动录入模式 -->
      <div v-if="uploadMethod === 'manual'" class="manual-input-section">
        <a-card title="手动录入发票信息">
          <a-form
            :model="manualForm"
            :rules="manualRules"
            layout="vertical"
            @finish="handleManualSubmit"
          >
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="发票类型" name="type">
                  <a-select v-model:value="manualForm.type" placeholder="选择发票类型">
                    <a-select-option value="vat-special">
                      增值税专用发票
                    </a-select-option>
                    <a-select-option value="vat-ordinary">
                      增值税普通发票
                    </a-select-option>
                    <a-select-option value="electronic">
                      电子发票
                    </a-select-option>
                    <a-select-option value="receipt">
                      收据
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="发票号码" name="invoiceNumber">
                  <a-input v-model:value="manualForm.invoiceNumber" placeholder="请输入发票号码" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="开票日期" name="issueDate">
                  <a-date-picker
                    v-model:value="manualForm.issueDate"
                    style="width: 100%"
                    placeholder="选择开票日期"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="不含税金额" name="amountWithoutTax">
                  <a-input-number
                    v-model:value="manualForm.amountWithoutTax"
                    style="width: 100%"
                    placeholder="不含税金额"
                    :precision="2"
                    :min="0"
                    @change="calculateTax"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="税率 (%)" name="taxRate">
                  <a-select
                    v-model:value="manualForm.taxRate"
                    placeholder="选择税率"
                    @change="calculateTax"
                  >
                    <a-select-option :value="0">
                      0%
                    </a-select-option>
                    <a-select-option :value="3">
                      3%
                    </a-select-option>
                    <a-select-option :value="6">
                      6%
                    </a-select-option>
                    <a-select-option :value="9">
                      9%
                    </a-select-option>
                    <a-select-option :value="13">
                      13%
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="税额" name="taxAmount">
                  <a-input-number
                    v-model:value="manualForm.taxAmount"
                    style="width: 100%"
                    placeholder="税额"
                    :precision="2"
                    :min="0"
                    disabled
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="价税合计" name="totalAmount">
                  <a-input-number
                    v-model:value="manualForm.totalAmount"
                    style="width: 100%"
                    placeholder="价税合计"
                    :precision="2"
                    :min="0"
                    disabled
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="所属企业" name="enterprise">
                  <a-select v-model:value="manualForm.enterprise" placeholder="选择企业">
                    <a-select-option
                      v-for="enterprise in enterpriseList"
                      :key="enterprise.id"
                      :value="enterprise.id"
                    >
                      {{ enterprise.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-divider orientation="left">
              销售方信息
            </a-divider>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="销售方名称" name="sellerName">
                  <a-input v-model:value="manualForm.sellerName" placeholder="销售方名称" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="销售方税号" name="sellerTaxNumber">
                  <a-input v-model:value="manualForm.sellerTaxNumber" placeholder="销售方税号" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-divider orientation="left">
              购买方信息
            </a-divider>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="购买方名称" name="buyerName">
                  <a-input v-model:value="manualForm.buyerName" placeholder="购买方名称" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="购买方税号" name="buyerTaxNumber">
                  <a-input v-model:value="manualForm.buyerTaxNumber" placeholder="购买方税号" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item label="备注" name="remarks">
              <a-textarea
                v-model:value="manualForm.remarks"
                placeholder="备注信息"
                :rows="3"
              />
            </a-form-item>

            <a-form-item>
              <a-space>
                <a-button type="primary" html-type="submit" :loading="submitting">
                  <SaveOutlined />
                  保存发票
                </a-button>
                <a-button @click="resetManualForm">
                  <ReloadOutlined />
                  重置表单
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>
      </div>

      <!-- 识别结果 -->
      <div v-if="recognitionResults.length > 0" class="recognition-results">
        <a-card title="识别结果" class="results-card">
          <div class="results-header">
            <h3>共识别到 {{ recognitionResults.length }} 张发票</h3>
            <a-space>
              <a-button type="primary" @click="batchSave">
                <SaveOutlined />
                批量保存
              </a-button>
              <a-button @click="clearResults">
                <DeleteOutlined />
                清空结果
              </a-button>
            </a-space>
          </div>

          <div class="results-list">
            <a-card
              v-for="(result, index) in recognitionResults"
              :key="index"
              size="small"
              class="result-item"
              :class="{ 'has-error': result.hasError }"
            >
              <template #title>
                <div class="result-title">
                  <span>发票 {{ index + 1 }}</span>
                  <a-tag :color="result.confidence > 0.9 ? 'green' : 'orange'">
                    识别置信度: {{ (result.confidence * 100).toFixed(1) }}%
                  </a-tag>
                </div>
              </template>

              <template #extra>
                <a-space>
                  <a-button size="small" @click="editResult(index)">
                    编辑
                  </a-button>
                  <a-button size="small" danger @click="removeResult(index)">
                    删除
                  </a-button>
                </a-space>
              </template>

              <a-descriptions :column="3" size="small">
                <a-descriptions-item label="发票号码">
                  {{ result.invoiceNumber }}
                </a-descriptions-item>
                <a-descriptions-item label="发票类型">
                  {{ getTypeName(result.type) }}
                </a-descriptions-item>
                <a-descriptions-item label="开票日期">
                  {{ result.issueDate }}
                </a-descriptions-item>
                <a-descriptions-item label="不含税金额">
                  ¥{{ formatNumber(result.amountWithoutTax) }}
                </a-descriptions-item>
                <a-descriptions-item label="税额">
                  ¥{{ formatNumber(result.taxAmount) }}
                </a-descriptions-item>
                <a-descriptions-item label="价税合计">
                  ¥{{ formatNumber(result.totalAmount) }}
                </a-descriptions-item>
              </a-descriptions>

              <div v-if="result.hasError" class="error-info">
                <a-alert
                  type="warning"
                  :message="result.errorMessage"
                  show-icon
                  banner
                />
              </div>
            </a-card>
          </div>
        </a-card>
      </div>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getEnterprises } from '@/api/enterprise'
import { createInvoice } from '@/api/invoice'
import { formatNumber } from '@/utils/format'
import {
  ArrowLeftOutlined,
  InboxOutlined,
  UploadOutlined,
  DeleteOutlined,
  CameraOutlined,
  StopOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'InvoiceUpload',
  components: {
    ArrowLeftOutlined,
    InboxOutlined,
    UploadOutlined,
    DeleteOutlined,
    CameraOutlined,
    StopOutlined,
    SaveOutlined,
    ReloadOutlined
  },
  setup () {
    const router = useRouter()

    // 上传方式
    const uploadMethod = ref('file')

    // 文件上传相关
    const fileList = ref([])
    const uploading = ref(false)
    const uploadProgress = ref(0)
    const uploadStatus = ref('active')
    const autoRecognition = ref(true)

    // 扫描相关
    const scanning = ref(false)

    // 手动录入表单
    const manualForm = reactive({
      type: '',
      invoiceNumber: '',
      issueDate: null,
      amountWithoutTax: null,
      taxRate: null,
      taxAmount: null,
      totalAmount: null,
      enterprise: '',
      sellerName: '',
      sellerTaxNumber: '',
      buyerName: '',
      buyerTaxNumber: '',
      remarks: ''
    })

    const submitting = ref(false)

    // 表单验证规则
    const manualRules = {
      type: [{ required: true, message: '请选择发票类型' }],
      invoiceNumber: [{ required: true, message: '请输入发票号码' }],
      issueDate: [{ required: true, message: '请选择开票日期' }],
      amountWithoutTax: [{ required: true, message: '请输入不含税金额' }],
      taxRate: [{ required: true, message: '请选择税率' }],
      enterprise: [{ required: true, message: '请选择所属企业' }]
    }

    // 企业列表
    const enterpriseList = ref([])

    // 识别结果
    const recognitionResults = ref([])

    // 计算属性
    const uploadUrl = computed(() => {
      return process.env.VUE_APP_API_BASE_URL + '/invoice/upload'
    })

    const uploadHeaders = computed(() => {
      return {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    })

    // 方法
    const goToList = () => {
      router.push('/invoice/list')
    }

    const beforeUpload = (file) => {
      const isValidType = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif'].includes(file.type)
      if (!isValidType) {
        message.error('只能上传 PDF 或图片文件!')
        return false
      }
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        message.error('文件大小不能超过 10MB!')
        return false
      }
      return false // 阻止默认上传，使用自定义上传
    }

    const handleUploadChange = (info) => {
      console.log('Upload change:', info)
    }

    const handleDrop = (e) => {
      console.log('Drop files:', e.dataTransfer.files)
    }

    const startBatchUpload = () => {
      if (fileList.value.length === 0) {
        message.warning('请先选择文件')
        return
      }

      uploading.value = true
      uploadProgress.value = 0
      uploadStatus.value = 'active'

      // 模拟上传进度
      const interval = setInterval(() => {
        uploadProgress.value += 10
        if (uploadProgress.value >= 100) {
          clearInterval(interval)
          uploading.value = false
          uploadStatus.value = 'success'

          if (autoRecognition.value) {
            // 模拟识别结果
            setTimeout(() => {
              recognitionResults.value = fileList.value.map((file, index) => ({
                fileName: file.name,
                invoiceNumber: `12345678901234567${890 + index}`,
                type: 'vat-special',
                issueDate: '2024-11-20',
                amountWithoutTax: 100000 + index * 10000,
                taxAmount: 13000 + index * 1300,
                totalAmount: 113000 + index * 11300,
                taxRate: 13,
                confidence: 0.95 - index * 0.05,
                hasError: index > 2,
                errorMessage: index > 2 ? '发票号码识别不清晰，请手动确认' : ''
              }))
              message.success('文件上传和识别完成!')
            }, 1000)
          } else {
            message.success('文件上传完成!')
          }
        }
      }, 200)
    }

    const clearFiles = () => {
      fileList.value = []
      uploadProgress.value = 0
      recognitionResults.value = []
    }

    const startScan = () => {
      scanning.value = true
      message.info('摄像头扫描功能开发中...')
    }

    const stopScan = () => {
      scanning.value = false
    }

    const captureImage = () => {
      message.info('图像捕获功能开发中...')
    }

    const calculateTax = () => {
      if (manualForm.amountWithoutTax && manualForm.taxRate !== null) {
        manualForm.taxAmount = (manualForm.amountWithoutTax * manualForm.taxRate / 100).toFixed(2)
        manualForm.totalAmount = (parseFloat(manualForm.amountWithoutTax) + parseFloat(manualForm.taxAmount)).toFixed(2)
      }
    }

    const handleManualSubmit = async (values) => {
      submitting.value = true
      try {
        // 构造符合后端API期望的数据格式
        const invoiceData = {
          invoiceNumber: values.invoiceNumber,
          invoiceCode: values.invoiceCode || '',
          type: values.type,
          issueDate: values.issueDate,
          sellerName: values.sellerName,
          sellerTaxNumber: values.sellerTaxNumber,
          buyerName: values.buyerName,
          buyerTaxNumber: values.buyerTaxNumber,
          totalAmount: parseFloat(values.totalAmount) || 0,
          taxAmount: parseFloat(values.taxAmount) || 0,
          enterpriseId: values.enterprise,
          remarks: values.remarks || ''
        }

        console.log('Submitting invoice data:', invoiceData)

        const response = await createInvoice(invoiceData)
        if (response.code === 200) {
          message.success('发票创建成功!')
          resetManualForm()
          // 可以选择跳转到发票列表页面
          // router.push('/invoice/list')
        } else {
          message.error(response.message || '发票创建失败')
        }
      } catch (error) {
        console.error('创建发票失败:', error)
        if (error.response?.status === 403) {
          message.error('无权限创建该企业的发票')
        } else {
          message.error('发票创建失败，请稍后重试')
        }
      } finally {
        submitting.value = false
      }
    }

    const resetManualForm = () => {
      Object.assign(manualForm, {
        type: '',
        invoiceNumber: '',
        issueDate: null,
        amountWithoutTax: null,
        taxRate: null,
        taxAmount: null,
        totalAmount: null,
        enterprise: '',
        sellerName: '',
        sellerTaxNumber: '',
        buyerName: '',
        buyerTaxNumber: '',
        remarks: ''
      })
    }

    const editResult = (index) => {
      message.info(`编辑识别结果 ${index + 1}`)
    }

    const removeResult = (index) => {
      recognitionResults.value.splice(index, 1)
      message.success('已删除识别结果')
    }

    const batchSave = () => {
      message.success(`批量保存 ${recognitionResults.value.length} 张发票`)
      recognitionResults.value = []
    }

    const clearResults = () => {
      recognitionResults.value = []
    }

    const getTypeName = (type) => {
      const names = {
        'vat-special': '增值税专用发票',
        'vat-ordinary': '增值税普通发票',
        electronic: '电子发票',
        receipt: '收据'
      }
      return names[type] || '未知类型'
    }

    // 获取企业列表
    const fetchEnterprises = async () => {
      try {
        const response = await getEnterprises()
        if (response.code === 200) {
          enterpriseList.value = response.data.data || response.data.list || []
        }
      } catch (error) {
        console.error('获取企业列表失败:', error)
        message.error('获取企业列表失败')
      }
    }

    // 生命周期
    onMounted(() => {
      fetchEnterprises()
    })

    return {
      uploadMethod,
      fileList,
      uploading,
      uploadProgress,
      uploadStatus,
      autoRecognition,
      scanning,
      manualForm,
      manualRules,
      submitting,
      enterpriseList,
      recognitionResults,
      uploadUrl,
      uploadHeaders,
      goToList,
      beforeUpload,
      handleUploadChange,
      handleDrop,
      startBatchUpload,
      clearFiles,
      startScan,
      stopScan,
      captureImage,
      calculateTax,
      handleManualSubmit,
      resetManualForm,
      editResult,
      removeResult,
      batchSave,
      clearResults,
      getTypeName,
      formatNumber
    }
  }
})
</script>

<style scoped>
.invoice-upload {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h2 {
  margin: 0;
  color: #262626;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.upload-method-section {
  margin-bottom: 24px;
  text-align: center;
}

.file-upload-section {
  margin-bottom: 24px;
}

.upload-card {
  height: 100%;
}

.help-card {
  height: 100%;
}

.help-content h4 {
  color: #1890ff;
  margin-top: 16px;
  margin-bottom: 8px;
}

.help-content ul {
  margin-left: 16px;
}

.upload-progress {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  color: #666;
}

.batch-operations {
  margin-top: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}

.scan-section {
  margin-bottom: 24px;
}

.scan-card {
  text-align: center;
}

.scan-area {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-placeholder h3 {
  margin: 16px 0 8px 0;
  color: #262626;
}

.scan-camera {
  width: 100%;
}

.camera-view {
  width: 300px;
  height: 200px;
  margin: 0 auto 16px auto;
  border: 2px dashed #1890ff;
  border-radius: 8px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f2f5;
}

.scan-frame {
  width: 80%;
  height: 80%;
  border: 2px solid #1890ff;
  border-radius: 4px;
}

.manual-input-section {
  margin-bottom: 24px;
}

.recognition-results {
  margin-top: 24px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.results-header h3 {
  margin: 0;
  color: #262626;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-item {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.result-item.has-error {
  border-color: #ff7875;
}

.result-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-info {
  margin-top: 12px;
}

:deep(.ant-upload-drag) {
  border-color: #d9d9d9;
}

:deep(.ant-upload-drag:hover) {
  border-color: #40a9ff;
}

:deep(.ant-upload-drag-icon) {
  margin-bottom: 16px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}
</style>
