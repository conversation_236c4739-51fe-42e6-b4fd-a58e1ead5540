<template>
  <div class="invoice-verify-container">
    <PageHeader
      title="发票核验"
      :description="`核验发票: ${invoice.invoiceNumber}`"
      :breadcrumbs="breadcrumbs"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
          <a-button
            type="primary"
            :loading="verifying"
            :disabled="invoice.status === 'verified'"
            @click="startVerification"
          >
            <SafetyCertificateOutlined />
            {{ verifying ? '核验中...' : '开始核验' }}
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <div class="verify-content">
      <a-row :gutter="24">
        <!-- 左侧发票信息 -->
        <a-col :span="14">
          <a-card title="发票信息" class="invoice-info-card">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="发票号码">
                {{ invoice.invoiceNumber }}
              </a-descriptions-item>
              <a-descriptions-item label="发票代码">
                {{ invoice.invoiceCode }}
              </a-descriptions-item>
              <a-descriptions-item label="开票日期">
                {{ invoice.issueDate }}
              </a-descriptions-item>
              <a-descriptions-item label="发票类型">
                <a-tag :color="getTypeColor(invoice.type)">
                  {{ getTypeName(invoice.type) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="销售方名称">
                {{ invoice.sellerName }}
              </a-descriptions-item>
              <a-descriptions-item label="销售方税号">
                {{ invoice.sellerTaxNumber }}
              </a-descriptions-item>
              <a-descriptions-item label="购买方名称">
                {{ invoice.buyerName }}
              </a-descriptions-item>
              <a-descriptions-item label="购买方税号">
                {{ invoice.buyerTaxNumber }}
              </a-descriptions-item>
              <a-descriptions-item label="不含税金额">
                ¥{{ formatNumber(invoice.amountWithoutTax) }}
              </a-descriptions-item>
              <a-descriptions-item label="税额">
                ¥{{ formatNumber(invoice.taxAmount) }}
              </a-descriptions-item>
              <a-descriptions-item label="价税合计">
                ¥{{ formatNumber(invoice.totalAmount) }}
              </a-descriptions-item>
              <a-descriptions-item label="校验码">
                {{ invoice.checkCode }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>

          <!-- 核验进度 -->
          <a-card v-if="verifying || verificationSteps.length > 0" title="核验进度" class="progress-card">
            <a-steps :current="currentStep" direction="vertical" size="small">
              <a-step
                v-for="(step, index) in verificationSteps"
                :key="index"
                :title="step.title"
                :description="step.description"
                :status="step.status"
              />
            </a-steps>
          </a-card>

          <!-- 核验结果 -->
          <a-card v-if="verificationResult" title="核验结果" class="result-card">
            <a-result
              :status="verificationResult.success ? 'success' : 'error'"
              :title="verificationResult.title"
              :sub-title="verificationResult.description"
            >
              <template #extra>
                <a-space>
                  <a-button v-if="!verificationResult.success" type="primary" @click="retryVerification">
                    重新核验
                  </a-button>
                  <a-button v-if="verificationResult.success" @click="downloadCertificate">
                    下载核验证明
                  </a-button>
                </a-space>
              </template>
            </a-result>

            <!-- 详细核验信息 -->
            <div v-if="verificationResult.details" class="verify-details">
              <h4>详细信息</h4>
              <a-descriptions :column="2" size="small" bordered>
                <a-descriptions-item
                  v-for="(value, key) in verificationResult.details"
                  :key="key"
                  :label="getDetailLabel(key)"
                >
                  <a-tag
                    v-if="typeof value === 'object'"
                    :color="value.valid ? 'success' : 'error'"
                  >
                    {{ value.valid ? '验证通过' : '验证失败' }}
                  </a-tag>
                  <span v-else>{{ value }}</span>
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧操作和历史 -->
        <a-col :span="10">
          <!-- 核验状态卡片 -->
          <a-card title="核验状态" class="status-card">
            <div class="status-content">
              <div class="current-status">
                <a-badge
                  :status="getStatusBadge(invoice.status)"
                  :text="getStatusText(invoice.status)"
                  style="font-size: 16px"
                />
              </div>

              <div v-if="invoice.status === 'verified'" class="verified-info">
                <a-divider />
                <a-descriptions :column="1" size="small">
                  <a-descriptions-item label="核验时间">
                    {{ invoice.verifyTime }}
                  </a-descriptions-item>
                  <a-descriptions-item label="核验人">
                    {{ invoice.verifyUser }}
                  </a-descriptions-item>
                  <a-descriptions-item label="核验方式">
                    {{ invoice.verifyMethod }}
                  </a-descriptions-item>
                </a-descriptions>
              </div>

              <div v-if="invoice.status === 'error'" class="error-info">
                <a-divider />
                <a-alert
                  type="error"
                  :message="invoice.errorMessage"
                  :description="invoice.errorDetail"
                  show-icon
                />
              </div>
            </div>
          </a-card>

          <!-- 核验选项 -->
          <a-card title="核验选项" class="options-card">
            <a-form layout="vertical">
              <a-form-item label="核验方式">
                <a-radio-group v-model:value="verifyOptions.method">
                  <a-radio value="online">
                    在线核验
                  </a-radio>
                  <a-radio value="offline">
                    离线核验
                  </a-radio>
                </a-radio-group>
              </a-form-item>

              <a-form-item label="核验项目">
                <a-checkbox-group v-model:value="verifyOptions.items">
                  <a-checkbox value="format">
                    格式校验
                  </a-checkbox>
                  <a-checkbox value="signature">
                    数字签名
                  </a-checkbox>
                  <a-checkbox value="issuer">
                    开票方验证
                  </a-checkbox>
                  <a-checkbox value="amount">
                    金额验证
                  </a-checkbox>
                  <a-checkbox value="duplicate">
                    重复验证
                  </a-checkbox>
                </a-checkbox-group>
              </a-form-item>

              <a-form-item>
                <a-checkbox v-model:checked="verifyOptions.saveResult">
                  保存核验结果
                </a-checkbox>
              </a-form-item>

              <a-form-item>
                <a-checkbox v-model:checked="verifyOptions.generateCertificate">
                  生成核验证明
                </a-checkbox>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 核验历史 -->
          <a-card title="核验历史" class="history-card">
            <a-timeline size="small">
              <a-timeline-item
                v-for="history in invoice.verifyHistory"
                :key="history.id"
                :color="getHistoryColor(history.result)"
              >
                <template #dot>
                  <component :is="getHistoryIcon(history.result)" />
                </template>
                <div class="history-content">
                  <div class="history-action">
                    {{ history.action }}
                  </div>
                  <div class="history-result">
                    <a-tag :color="history.result === 'success' ? 'success' : 'error'" size="small">
                      {{ history.result === 'success' ? '成功' : '失败' }}
                    </a-tag>
                  </div>
                  <div class="history-meta">
                    {{ history.operator }} • {{ history.time }}
                  </div>
                </div>
              </a-timeline-item>
              <a-timeline-item v-if="!invoice.verifyHistory?.length">
                <div class="no-history">
                  暂无核验记录
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import PageHeader from '@/components/common/PageHeader.vue'
import { getInvoiceById, verifyInvoice } from '@/api/invoice'
import {
  ArrowLeftOutlined,
  SafetyCertificateOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'InvoiceVerify',
  components: {
    PageHeader,
    ArrowLeftOutlined,
    SafetyCertificateOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    ExclamationCircleOutlined
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const verifying = ref(false)
    const currentStep = ref(0)
    const verificationResult = ref(null)

    // 发票信息
    const invoice = ref({
      id: '',
      invoiceNumber: '',
      invoiceCode: '',
      type: '',
      status: '',
      issueDate: '',
      sellerName: '',
      sellerTaxNumber: '',
      buyerName: '',
      buyerTaxNumber: '',
      amountWithoutTax: 0,
      taxAmount: 0,
      totalAmount: 0,
      checkCode: '',
      verifyTime: '',
      verifyUser: '',
      verifyMethod: '',
      errorMessage: '',
      errorDetail: '',
      verifyHistory: []
    })

    // 核验选项
    const verifyOptions = reactive({
      method: 'online',
      items: ['format', 'signature', 'issuer', 'amount'],
      saveResult: true,
      generateCertificate: true
    })

    // 核验步骤
    const verificationSteps = ref([])

    // 面包屑导航
    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '票据管理', path: '/invoice' },
      { title: '发票列表', path: '/invoice/list' },
      { title: '发票核验' }
    ])

    // 方法
    const fetchInvoiceDetail = async () => {
      try {
        const response = await getInvoiceById(route.params.id)
        if (response.code === 200) {
          invoice.value = response.data

          // 模拟历史记录
          invoice.value.verifyHistory = [
            {
              id: 1,
              action: '创建发票记录',
              result: 'success',
              operator: '系统',
              time: '2024-01-15 10:30:00'
            }
          ]
        }
      } catch (error) {
        message.error('获取发票详情失败')
      }
    }

    const startVerification = async () => {
      verifying.value = true
      currentStep.value = 0
      verificationResult.value = null

      // 初始化核验步骤
      verificationSteps.value = [
        { title: '连接核验服务', description: '正在连接税务系统...', status: 'process' },
        { title: '格式校验', description: '校验发票格式和基本信息', status: 'wait' },
        { title: '数字签名验证', description: '验证发票数字签名', status: 'wait' },
        { title: '开票方验证', description: '验证开票方信息', status: 'wait' },
        { title: '金额验证', description: '验证发票金额信息', status: 'wait' },
        { title: '生成结果', description: '生成核验报告', status: 'wait' }
      ]

      try {
        // 模拟步骤执行
        for (let i = 0; i < verificationSteps.value.length; i++) {
          currentStep.value = i
          verificationSteps.value[i].status = 'process'

          // 模拟异步处理
          await new Promise(resolve => setTimeout(resolve, 1000))

          verificationSteps.value[i].status = 'finish'
          verificationSteps.value[i].description = '完成'
        }

        // 调用API进行实际核验
        const response = await verifyInvoice(invoice.value.id)

        if (response.code === 200) {
          verificationResult.value = {
            success: true,
            title: '核验成功',
            description: '发票信息验证通过，发票有效',
            details: {
              format: { valid: true },
              signature: { valid: true },
              issuer: { valid: true },
              amount: { valid: true },
              duplicate: { valid: true }
            }
          }

          invoice.value.status = 'verified'
          invoice.value.verifyTime = new Date().toLocaleString()
          invoice.value.verifyUser = '当前用户'
          invoice.value.verifyMethod = verifyOptions.method === 'online' ? '在线核验' : '离线核验'

          // 添加核验历史
          invoice.value.verifyHistory.unshift({
            id: Date.now(),
            action: '发票核验',
            result: 'success',
            operator: '当前用户',
            time: new Date().toLocaleString()
          })

          message.success('发票核验成功')
        } else {
          throw new Error(response.message || '核验失败')
        }
      } catch (error) {
        verificationResult.value = {
          success: false,
          title: '核验失败',
          description: error.message || '发票核验过程中出现错误',
          details: null
        }

        invoice.value.status = 'error'
        invoice.value.errorMessage = '核验失败'
        invoice.value.errorDetail = error.message || '发票核验过程中出现错误'

        // 添加失败记录
        invoice.value.verifyHistory.unshift({
          id: Date.now(),
          action: '发票核验',
          result: 'error',
          operator: '当前用户',
          time: new Date().toLocaleString()
        })

        message.error('发票核验失败')
      } finally {
        verifying.value = false
      }
    }

    const retryVerification = () => {
      startVerification()
    }

    const downloadCertificate = () => {
      message.info('下载核验证明功能开发中...')
    }

    const goBack = () => {
      router.go(-1)
    }

    // 辅助方法
    const getTypeColor = (type) => {
      const colors = {
        'vat-special': 'blue',
        'vat-ordinary': 'green',
        electronic: 'purple',
        receipt: 'orange'
      }
      return colors[type] || 'default'
    }

    const getTypeName = (type) => {
      const names = {
        'vat-special': '增值税专用发票',
        'vat-ordinary': '增值税普通发票',
        electronic: '电子发票',
        receipt: '收据'
      }
      return names[type] || '未知类型'
    }

    const getStatusBadge = (status) => {
      const badges = {
        pending: 'processing',
        verified: 'success',
        error: 'error',
        invalid: 'error'
      }
      return badges[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        pending: '待核验',
        verified: '已核验',
        error: '核验失败',
        invalid: '无效'
      }
      return texts[status] || '未知'
    }

    const getHistoryColor = (result) => {
      return result === 'success' ? 'green' : 'red'
    }

    const getHistoryIcon = (result) => {
      return result === 'success' ? 'CheckCircleOutlined' : 'CloseCircleOutlined'
    }

    const getDetailLabel = (key) => {
      const labels = {
        format: '格式校验',
        signature: '数字签名',
        issuer: '开票方验证',
        amount: '金额验证',
        duplicate: '重复验证'
      }
      return labels[key] || key
    }

    const formatNumber = (num) => {
      return Number(num).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }

    // 生命周期
    onMounted(() => {
      fetchInvoiceDetail()
    })

    return {
      verifying,
      currentStep,
      verificationResult,
      invoice,
      verifyOptions,
      verificationSteps,
      breadcrumbs,
      startVerification,
      retryVerification,
      downloadCertificate,
      goBack,
      getTypeColor,
      getTypeName,
      getStatusBadge,
      getStatusText,
      getHistoryColor,
      getHistoryIcon,
      getDetailLabel,
      formatNumber
    }
  }
})
</script>

<style scoped>
.invoice-verify-container {
  background: #f5f5f5;
  min-height: 100vh;
}

.verify-content {
  padding: 0 24px 24px;
}

.invoice-info-card,
.progress-card,
.result-card,
.status-card,
.options-card,
.history-card {
  margin-bottom: 16px;
}

.status-content {
  text-align: center;
  padding: 16px 0;
}

.current-status {
  font-size: 18px;
  margin-bottom: 8px;
}

.verified-info,
.error-info {
  text-align: left;
}

.verify-details {
  margin-top: 24px;
}

.verify-details h4 {
  margin-bottom: 16px;
  color: #262626;
}

.history-content {
  margin-left: 8px;
}

.history-action {
  font-weight: 500;
  color: #262626;
}

.history-result {
  margin: 4px 0;
}

.history-meta {
  font-size: 12px;
  color: #8c8c8c;
}

.no-history {
  color: #8c8c8c;
  font-style: italic;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}

:deep(.ant-card-head-title) {
  font-weight: 500;
}

:deep(.ant-result-title) {
  color: #262626;
}

:deep(.ant-steps-item-description) {
  color: #8c8c8c;
}
</style>
