<template>
  <div class="invoice-list">
    <a-card>
      <!-- 页面标题和操作 -->
      <template #title>
        <div class="page-header">
          <h2>发票票据管理</h2>
          <div class="header-actions">
            <a-button type="primary" @click="goToUpload">
              <UploadOutlined />
              上传发票
            </a-button>
            <a-button @click="goToScan">
              <ScanOutlined />
              扫描识别
            </a-button>
            <a-button @click="batchImport">
              <ImportOutlined />
              批量导入
            </a-button>
            <a-dropdown>
              <a-button>
                更多操作
                <DownOutlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="exportSelected">
                    <ExportOutlined />
                    导出选中
                  </a-menu-item>
                  <a-menu-item @click="batchVerify">
                    <CheckCircleOutlined />
                    批量核验
                  </a-menu-item>
                  <a-menu-item @click="batchDelete">
                    <DeleteOutlined />
                    批量删除
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </template>

      <!-- 搜索和筛选 -->
      <div class="search-section">
        <a-row :gutter="16">
          <a-col :span="4">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="发票号码、企业名称"
              @press-enter="handleSearch"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
          </a-col>
          <a-col :span="3">
            <a-select
              v-model:value="searchForm.type"
              placeholder="发票类型"
              allow-clear
            >
              <a-select-option value="">
                全部类型
              </a-select-option>
              <a-select-option value="vat-special">
                增值税专用发票
              </a-select-option>
              <a-select-option value="vat-ordinary">
                增值税普通发票
              </a-select-option>
              <a-select-option value="electronic">
                电子发票
              </a-select-option>
              <a-select-option value="receipt">
                收据
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="3">
            <a-select
              v-model:value="searchForm.status"
              placeholder="状态"
              allow-clear
            >
              <a-select-option value="">
                全部状态
              </a-select-option>
              <a-select-option value="pending">
                待处理
              </a-select-option>
              <a-select-option value="verified">
                已核验
              </a-select-option>
              <a-select-option value="used">
                已使用
              </a-select-option>
              <a-select-option value="invalid">
                无效
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-range-picker
              v-model:value="searchForm.dateRange"
              placeholder="开票日期"
            />
          </a-col>
          <a-col :span="4">
            <a-input-number
              v-model:value="searchForm.minAmount"
              placeholder="最小金额"
              style="width: 48%; margin-right: 4%"
              :min="0"
              :precision="2"
            />
            <a-input-number
              v-model:value="searchForm.maxAmount"
              placeholder="最大金额"
              style="width: 48%"
              :min="0"
              :precision="2"
            />
          </a-col>
          <a-col :span="6">
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <SearchOutlined />
                搜索
              </a-button>
              <a-button @click="handleReset">
                <ReloadOutlined />
                重置
              </a-button>
              <a-button @click="showAdvancedSearch = !showAdvancedSearch">
                {{ showAdvancedSearch ? '简单搜索' : '高级搜索' }}
              </a-button>
            </a-space>
          </a-col>
        </a-row>

        <!-- 高级搜索 -->
        <div v-if="showAdvancedSearch" class="advanced-search">
          <a-divider orientation="left">
            高级搜索
          </a-divider>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-select
                v-model:value="searchForm.enterprise"
                placeholder="选择企业"
                allow-clear
                show-search
                :filter-option="filterOption"
              >
                <a-select-option
                  v-for="enterprise in enterpriseList"
                  :key="enterprise.id"
                  :value="enterprise.id"
                >
                  {{ enterprise.name }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="searchForm.taxRate"
                placeholder="税率"
                allow-clear
              >
                <a-select-option value="">
                  全部税率
                </a-select-option>
                <a-select-option value="0">
                  0%
                </a-select-option>
                <a-select-option value="3">
                  3%
                </a-select-option>
                <a-select-option value="6">
                  6%
                </a-select-option>
                <a-select-option value="9">
                  9%
                </a-select-option>
                <a-select-option value="13">
                  13%
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-input
                v-model:value="searchForm.seller"
                placeholder="销售方名称"
              />
            </a-col>
            <a-col :span="4">
              <a-input
                v-model:value="searchForm.buyer"
                placeholder="购买方名称"
              />
            </a-col>
            <a-col :span="6">
              <a-checkbox-group v-model:value="searchForm.flags">
                <a-checkbox value="hasAttachment">
                  有附件
                </a-checkbox>
                <a-checkbox value="isDeductible">
                  可抵扣
                </a-checkbox>
                <a-checkbox value="isAuthenticated">
                  已认证
                </a-checkbox>
              </a-checkbox-group>
            </a-col>
          </a-row>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <a-row :gutter="16">
          <a-col
            :xs="12"
            :sm="8"
            :md="6"
            :lg="4"
          >
            <div class="stat-card">
              <div class="stat-icon total">
                <PaperClipOutlined />
              </div>
              <div class="stat-content">
                <div class="stat-value">
                  {{ formatNumber(stats.total) }}
                </div>
                <div class="stat-title">
                  发票总数
                </div>
                <div class="stat-unit">
                  张
                </div>
              </div>
            </div>
          </a-col>
          <a-col
            :xs="12"
            :sm="8"
            :md="6"
            :lg="4"
          >
            <div class="stat-card">
              <div class="stat-icon verified">
                <CheckCircleOutlined />
              </div>
              <div class="stat-content">
                <div class="stat-value">
                  {{ formatNumber(stats.verified) }}
                </div>
                <div class="stat-title">
                  已核验
                </div>
                <div class="stat-unit">
                  张
                </div>
              </div>
            </div>
          </a-col>
          <a-col
            :xs="12"
            :sm="8"
            :md="6"
            :lg="4"
          >
            <div class="stat-card">
              <div class="stat-icon pending">
                <HistoryOutlined />
              </div>
              <div class="stat-content">
                <div class="stat-value">
                  {{ formatNumber(stats.pending) }}
                </div>
                <div class="stat-title">
                  待处理
                </div>
                <div class="stat-unit">
                  张
                </div>
              </div>
            </div>
          </a-col>
          <a-col
            :xs="12"
            :sm="8"
            :md="6"
            :lg="4"
          >
            <div class="stat-card">
              <div class="stat-icon amount">
                <DollarOutlined />
              </div>
              <div class="stat-content">
                <div class="stat-value">
                  {{ formatCurrency(stats.totalAmount) }}
                </div>
                <div class="stat-title">
                  总金额
                </div>
                <div class="stat-unit">
                  元
                </div>
              </div>
            </div>
          </a-col>
          <a-col
            :xs="12"
            :sm="8"
            :md="6"
            :lg="4"
          >
            <div class="stat-card">
              <div class="stat-icon tax">
                <CalculatorOutlined />
              </div>
              <div class="stat-content">
                <div class="stat-value">
                  {{ formatCurrency(stats.totalTax) }}
                </div>
                <div class="stat-title">
                  总税额
                </div>
                <div class="stat-unit">
                  元
                </div>
              </div>
            </div>
          </a-col>
          <a-col
            :xs="12"
            :sm="8"
            :md="6"
            :lg="4"
          >
            <div class="stat-card">
              <div class="stat-icon monthly">
                <CalendarOutlined />
              </div>
              <div class="stat-content">
                <div class="stat-value">
                  {{ formatNumber(stats.thisMonth) }}
                </div>
                <div class="stat-title">
                  本月新增
                </div>
                <div class="stat-unit">
                  张
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 批量操作栏 -->
      <div v-if="selectedRowKeys.length > 0" class="batch-actions">
        <a-alert
          :message="`已选择 ${selectedRowKeys.length} 项`"
          type="info"
          show-icon
        >
          <template #action>
            <a-space>
              <a-button size="small" @click="clearSelection">
                取消选择
              </a-button>
              <a-button size="small" type="primary" @click="batchVerify">
                批量核验
              </a-button>
              <a-button size="small" @click="batchExport">
                批量导出
              </a-button>
              <a-button size="small" danger @click="batchDelete">
                批量删除
              </a-button>
            </a-space>
          </template>
        </a-alert>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        row-key="id"
        :scroll="{ x: 1400 }"
        size="middle"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'invoiceInfo'">
            <div class="invoice-info">
              <div class="invoice-number">
                {{ record.invoiceNumber }}
              </div>
              <div class="invoice-type">
                <a-tag :color="getTypeColor(record.type)">
                  {{ getTypeName(record.type) }}
                </a-tag>
              </div>
            </div>
          </template>

          <template v-if="column.key === 'enterprise'">
            <div class="enterprise-info">
              <div class="enterprise-name">
                <a-tooltip :title="record.enterprise?.name || '未知企业'">
                  {{ record.enterprise?.name || '未知企业' }}
                </a-tooltip>
              </div>
              <div class="enterprise-details">
                <div class="detail-row">
                  <span class="label">销售方:</span>
                  <a-tooltip :title="record.sellerName">
                    <span class="value">{{ truncateText(record.sellerName, 15) }}</span>
                  </a-tooltip>
                </div>
                <div class="detail-row">
                  <span class="label">购买方:</span>
                  <a-tooltip :title="record.buyerName">
                    <span class="value">{{ truncateText(record.buyerName, 15) }}</span>
                  </a-tooltip>
                </div>
              </div>
            </div>
          </template>

          <template v-if="column.key === 'amount'">
            <div class="amount-info">
              <div class="total-amount">
                <span class="currency">¥</span>
                <span class="amount-value">{{ formatTableCurrency(record.totalAmountWithTax) }}</span>
              </div>
              <div class="amount-details">
                <div class="detail-item">
                  <span class="detail-label">不含税:</span>
                  <span class="detail-value">¥{{ formatTableCurrency(record.totalAmount) }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">税额:</span>
                  <span class="detail-value">¥{{ formatTableCurrency(record.totalTax) }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">税率:</span>
                  <span class="detail-value">{{ formatPercent(record.taxRate) }}</span>
                </div>
              </div>
            </div>
          </template>

          <template v-if="column.key === 'status'">
            <a-badge
              :status="getStatusBadge(record.status)"
              :text="getStatusText(record.status)"
            />
            <div v-if="record.hasAttachment" class="attachment-indicator">
              <PaperClipOutlined style="color: #1890ff" title="有附件" />
            </div>
          </template>

          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">
                查看
              </a-button>
              <a-button
                type="link"
                size="small"
                :disabled="record.status === 'verified'"
                @click="verifyInvoice(record)"
              >
                核验
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="downloadInvoice(record)">
                      <DownloadOutlined />
                      下载
                    </a-menu-item>
                    <a-menu-item @click="editInvoice(record)">
                      <EditOutlined />
                      编辑
                    </a-menu-item>
                    <a-menu-item @click="viewHistory(record)">
                      <HistoryOutlined />
                      操作历史
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item class="danger" @click="deleteInvoice(record)">
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 发票详情抽屉 -->
    <a-drawer
      v-model:open="detailDrawerVisible"
      title="发票详情"
      width="600"
      :mask-closable="false"
    >
      <div v-if="selectedInvoice" class="invoice-detail">
        <!-- 发票基本信息 -->
        <a-descriptions title="基本信息" :column="2" bordered>
          <a-descriptions-item label="发票号码">
            {{ selectedInvoice.invoiceNumber }}
          </a-descriptions-item>
          <a-descriptions-item label="发票类型">
            <a-tag :color="getTypeColor(selectedInvoice.type)">
              {{ getTypeName(selectedInvoice.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="开票日期">
            {{ selectedInvoice.issueDate }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-badge
              :status="getStatusBadge(selectedInvoice.status)"
              :text="getStatusText(selectedInvoice.status)"
            />
          </a-descriptions-item>
        </a-descriptions>

        <!-- 金额信息 -->
        <a-descriptions
          title="金额信息"
          :column="2"
          bordered
          style="margin-top: 16px"
        >
          <a-descriptions-item label="不含税金额">
            ¥{{ formatNumber(selectedInvoice.amountWithoutTax) }}
          </a-descriptions-item>
          <a-descriptions-item label="税额">
            ¥{{ formatNumber(selectedInvoice.taxAmount) }}
          </a-descriptions-item>
          <a-descriptions-item label="价税合计">
            ¥{{ formatNumber(selectedInvoice.totalAmount) }}
          </a-descriptions-item>
          <a-descriptions-item label="税率">
            {{ selectedInvoice.taxRate }}%
          </a-descriptions-item>
        </a-descriptions>

        <!-- 企业信息 -->
        <a-descriptions
          title="企业信息"
          :column="1"
          bordered
          style="margin-top: 16px"
        >
          <a-descriptions-item label="销售方">
            <div>
              <div>{{ selectedInvoice.sellerName }}</div>
              <div style="color: #8c8c8c; font-size: 12px">
                {{ selectedInvoice.sellerTaxNumber }}
              </div>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="购买方">
            <div>
              <div>{{ selectedInvoice.buyerName }}</div>
              <div style="color: #8c8c8c; font-size: 12px">
                {{ selectedInvoice.buyerTaxNumber }}
              </div>
            </div>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 附件信息 -->
        <div v-if="selectedInvoice.attachments && selectedInvoice.attachments.length > 0">
          <a-divider orientation="left">
            附件信息
          </a-divider>
          <a-list
            :data-source="selectedInvoice.attachments"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <a @click="previewAttachment(item)">{{ item.name }}</a>
                  </template>
                  <template #description>
                    {{ item.size }} - {{ item.uploadTime }}
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a @click="downloadAttachment(item)">下载</a>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { getInvoices, getInvoiceStats, batchVerifyInvoices, batchDeleteInvoices } from '@/api/invoice'
import { getEnterprises } from '@/api/enterprise'
import { formatNumber } from '@/utils/format'
import {
  UploadOutlined,
  ScanOutlined,
  ImportOutlined,
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  DownOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  HistoryOutlined,
  PaperClipOutlined,
  DollarOutlined,
  CalculatorOutlined,
  CalendarOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'InvoiceList',
  components: {
    UploadOutlined,
    ScanOutlined,
    ImportOutlined,
    SearchOutlined,
    ReloadOutlined,
    ExportOutlined,
    DownOutlined,
    CheckCircleOutlined,
    DeleteOutlined,
    DownloadOutlined,
    EditOutlined,
    HistoryOutlined,
    PaperClipOutlined,
    DollarOutlined,
    CalculatorOutlined,
    CalendarOutlined
  },
  setup () {
    const router = useRouter()
    const loading = ref(false)
    const showAdvancedSearch = ref(false)
    const detailDrawerVisible = ref(false)
    const selectedInvoice = ref(null)
    const selectedRowKeys = ref([])

    // 搜索表单
    const searchForm = reactive({
      keyword: '',
      type: '',
      status: '',
      dateRange: null,
      minAmount: null,
      maxAmount: null,
      enterprise: '',
      taxRate: '',
      seller: '',
      buyer: '',
      flags: []
    })

    // 企业列表
    const enterpriseList = ref([])

    // 统计数据
    const stats = ref({
      total: 0,
      verified: 0,
      pending: 0,
      totalAmount: 0,
      totalTax: 0,
      thisMonth: 0
    })

    // 表格列配置
    const columns = [
      {
        title: '发票信息',
        key: 'invoiceInfo',
        width: 200,
        fixed: 'left'
      },
      {
        title: '企业信息',
        key: 'enterprise',
        width: 250
      },
      {
        title: '金额信息',
        key: 'amount',
        width: 150,
        sorter: true
      },
      {
        title: '开票日期',
        dataIndex: 'issueDate',
        width: 120,
        sorter: true
      },
      {
        title: '状态',
        key: 'status',
        width: 120
      },
      {
        title: '操作',
        key: 'actions',
        width: 180,
        fixed: 'right'
      }
    ]

    // 表格数据
    const dataSource = ref([])

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
    })

    // 行选择配置
    const rowSelection = computed(() => ({
      selectedRowKeys: selectedRowKeys.value,
      onChange: (keys) => {
        selectedRowKeys.value = keys
      },
      onSelectAll: (selected, selectedRows, changeRows) => {
        console.log(selected, selectedRows, changeRows)
      }
    }))

    // 方法
    const handleSearch = () => {
      console.log('搜索:', searchForm)
      fetchData()
    }

    const handleReset = () => {
      Object.assign(searchForm, {
        keyword: '',
        type: '',
        status: '',
        dateRange: null,
        minAmount: null,
        maxAmount: null,
        enterprise: '',
        taxRate: '',
        seller: '',
        buyer: '',
        flags: []
      })
      fetchData()
    }

    const handleTableChange = (pag, _filters, _sorter) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      fetchData()
    }

    // 获取发票数据
    const fetchData = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.current,
          pageSize: pagination.pageSize,
          keyword: searchForm.keyword,
          type: searchForm.type,
          status: searchForm.status,
          enterpriseId: searchForm.enterprise, // 修正参数名
          minAmount: searchForm.minAmount,
          maxAmount: searchForm.maxAmount,
          startDate: searchForm.dateRange?.[0],
          endDate: searchForm.dateRange?.[1]
        }

        // 过滤掉空值参数
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === null || params[key] === undefined) {
            delete params[key]
          }
        })

        const response = await getInvoices(params)
        if (response.code === 200) {
          dataSource.value = response.data.data || response.data.items || []
          pagination.total = response.data.total || 0
        } else {
          message.error(response.message || '获取发票列表失败')
        }
      } catch (error) {
        console.error('获取发票列表失败:', error)
        if (error.response?.status === 403) {
          message.error('无权限查看发票数据')
        } else {
          message.error('获取发票列表失败，请稍后重试')
        }
      } finally {
        loading.value = false
      }
    }

    // 获取统计数据
    const fetchStats = async () => {
      try {
        const response = await getInvoiceStats()
        if (response.code === 200) {
          stats.value = response.data
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    }

    // 获取企业列表
    const fetchEnterprises = async () => {
      try {
        const response = await getEnterprises({ pageSize: 1000 })
        if (response.code === 200) {
          enterpriseList.value = response.data.items?.map(item => ({
            id: item.id,
            name: item.name
          })) || []
        }
      } catch (error) {
        console.error('获取企业列表失败:', error)
      }
    }

    const clearSelection = () => {
      selectedRowKeys.value = []
    }

    const filterOption = (input, option) => {
      return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }

    // 页面跳转方法
    const goToUpload = () => {
      router.push('/invoice/upload')
    }

    const goToScan = () => {
      router.push('/invoice/scan')
    }

    const batchImport = () => {
      router.push('/invoice/import')
    }

    // 发票操作方法
    const viewDetail = (record) => {
      selectedInvoice.value = record
      detailDrawerVisible.value = true
    }

    const verifyInvoice = (record) => {
      router.push(`/invoice/verify/${record.id}`)
    }

    const downloadInvoice = (record) => {
      message.info(`下载发票: ${record.invoiceNumber}`)
    }

    const editInvoice = (record) => {
      message.info(`编辑发票: ${record.invoiceNumber}`)
    }

    const viewHistory = (record) => {
      message.info(`查看操作历史: ${record.invoiceNumber}`)
    }

    const deleteInvoice = (record) => {
      Modal.confirm({
        title: '确认删除发票',
        content: `确定要删除发票"${record.invoiceNumber}"吗？`,
        okType: 'danger',
        onOk () {
          const index = dataSource.value.findIndex(item => item.id === record.id)
          if (index > -1) {
            dataSource.value.splice(index, 1)
            message.success('删除成功')
          }
        }
      })
    }

    // 批量操作方法
    const batchVerify = async () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning('请先选择要核验的发票')
        return
      }

      try {
        const response = await batchVerifyInvoices(selectedRowKeys.value)
        if (response.code === 200) {
          message.success(`成功核验 ${selectedRowKeys.value.length} 张发票`)
          selectedRowKeys.value = []
          fetchData()
          fetchStats()
        } else {
          message.error(response.message || '批量核验失败')
        }
      } catch (error) {
        console.error('批量核验失败:', error)
        message.error('批量核验失败，请稍后重试')
      }
    }

    const batchExport = () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning('请先选择要导出的发票')
        return
      }
      message.info(`批量导出 ${selectedRowKeys.value.length} 张发票`)
    }

    const batchDelete = async () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning('请先选择要删除的发票')
        return
      }

      Modal.confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${selectedRowKeys.value.length} 张发票吗？此操作不可恢复。`,
        okType: 'danger',
        async onOk () {
          try {
            const response = await batchDeleteInvoices(selectedRowKeys.value)
            if (response.code === 200) {
              message.success(`成功删除 ${selectedRowKeys.value.length} 张发票`)
              selectedRowKeys.value = []
              fetchData()
              fetchStats()
            } else {
              message.error(response.message || '批量删除失败')
            }
          } catch (error) {
            console.error('批量删除失败:', error)
            message.error('批量删除失败，请稍后重试')
          }
        }
      })
    }

    const exportSelected = () => {
      message.info('导出选中发票')
    }

    // 附件操作方法
    const previewAttachment = (attachment) => {
      message.info(`预览附件: ${attachment.name}`)
    }

    const downloadAttachment = (attachment) => {
      message.info(`下载附件: ${attachment.name}`)
    }

    // 辅助方法
    const getTypeColor = (type) => {
      const colors = {
        'vat-special': 'red',
        'vat-ordinary': 'blue',
        electronic: 'green',
        receipt: 'orange'
      }
      return colors[type] || 'default'
    }

    const getTypeName = (type) => {
      const names = {
        'vat-special': '增值税专用发票',
        'vat-ordinary': '增值税普通发票',
        electronic: '电子发票',
        receipt: '收据'
      }
      return names[type] || '未知类型'
    }

    const getStatusBadge = (status) => {
      const badges = {
        pending: 'processing',
        verified: 'success',
        used: 'default',
        invalid: 'error'
      }
      return badges[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        pending: '待处理',
        verified: '已核验',
        used: '已使用',
        invalid: '无效'
      }
      return texts[status] || '未知'
    }

    // 格式化货币
    const formatCurrency = (value) => {
      if (!value && value !== 0) return '0.00'
      const num = Number(value)

      // 如果数值很大，使用万为单位
      if (num >= 10000) {
        return (num / 10000).toFixed(2) + '万'
      }

      return num.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }

    // 格式化百分比
    const formatPercent = (value) => {
      if (!value && value !== 0) return '0%'
      return `${Number(value).toFixed(1)}%`
    }

    // 截断文本
    const truncateText = (text, maxLength) => {
      if (!text) return ''
      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
    }

    // 格式化表格中的金额（更紧凑）
    const formatTableCurrency = (value) => {
      if (!value && value !== 0) return '0.00'
      const num = Number(value)

      // 如果数值很大，使用万为单位
      if (num >= 100000) {
        return (num / 10000).toFixed(1) + '万'
      } else if (num >= 10000) {
        return (num / 10000).toFixed(2) + '万'
      }

      return num.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }

    // 生命周期
    onMounted(() => {
      fetchData()
      fetchStats()
      fetchEnterprises()
    })

    return {
      loading,
      showAdvancedSearch,
      detailDrawerVisible,
      selectedInvoice,
      selectedRowKeys,
      searchForm,
      enterpriseList,
      stats,
      columns,
      dataSource,
      pagination,
      rowSelection,
      handleSearch,
      handleReset,
      handleTableChange,
      clearSelection,
      filterOption,
      goToUpload,
      goToScan,
      batchImport,
      viewDetail,
      verifyInvoice,
      downloadInvoice,
      editInvoice,
      viewHistory,
      deleteInvoice,
      batchVerify,
      batchExport,
      batchDelete,
      exportSelected,
      previewAttachment,
      downloadAttachment,
      getTypeColor,
      getTypeName,
      getStatusBadge,
      getStatusText,
      formatNumber,
      formatCurrency,
      formatTableCurrency,
      formatPercent,
      truncateText
    }
  }
})
</script>

<style scoped>
.invoice-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h2 {
  margin: 0;
  color: #262626;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-section {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}

.advanced-search {
  margin-top: 16px;
}

.stats-section {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  height: 80px;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.verified {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
}

.stat-icon.amount {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

.stat-icon.tax {
  background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
}

.stat-icon.monthly {
  background: linear-gradient(135deg, #13c2c2 0%, #08979c 100%);
}

.stat-content {
  flex: 1;
  min-width: 0; /* 允许flex子项收缩 */
  overflow: hidden;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.stat-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-top: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.stat-unit {
  font-size: 12px;
  color: #bfbfbf;
}

.batch-actions {
  margin-bottom: 16px;
}

.invoice-info .invoice-number {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  margin-bottom: 4px;
}

.enterprise-info .enterprise-name {
  font-weight: 600;
  font-size: 14px;
  color: #262626;
  margin-bottom: 8px;
  line-height: 1.4;
}

.enterprise-details {
  font-size: 12px;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  line-height: 1.4;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row .label {
  color: #8c8c8c;
  font-weight: 500;
  min-width: 50px;
  margin-right: 6px;
}

.detail-row .value {
  color: #595959;
  flex: 1;
}

.amount-info {
  text-align: right;
  min-width: 120px;
  max-width: 150px;
}

.amount-info .total-amount {
  margin-bottom: 8px;
  overflow: hidden;
}

.amount-info .currency {
  font-size: 12px;
  color: #8c8c8c;
  margin-right: 2px;
}

.amount-info .amount-value {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  max-width: 100px;
}

.amount-details {
  font-size: 11px;
  max-width: 140px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
  line-height: 1.3;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #8c8c8c;
  flex-shrink: 0;
  margin-right: 4px;
}

.detail-value {
  color: #595959;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .search-section .ant-row {
    flex-direction: column;
  }

  .search-section .ant-col {
    width: 100% !important;
    margin-bottom: 8px;
  }

  .stat-card {
    height: auto;
    min-height: 70px;
    padding: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin-right: 10px;
  }

  .stat-value {
    font-size: 18px;
  }

  .enterprise-info .enterprise-name {
    font-size: 13px;
  }

  .amount-info .amount-value {
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .stats-section .ant-col {
    margin-bottom: 8px;
  }

  .stat-card {
    padding: 10px;
    height: auto;
    min-height: 60px;
  }

  .stat-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
    margin-right: 8px;
  }

  .stat-value {
    font-size: 16px;
  }

  .stat-title {
    font-size: 12px;
  }
}

.amount-info .tax-amount,
.amount-info .tax-rate {
  font-size: 12px;
  color: #8c8c8c;
}

.attachment-indicator {
  margin-top: 4px;
}

.invoice-detail .ant-descriptions {
  margin-bottom: 16px;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
}

:deep(.danger) {
  color: #ff4d4f;
}
</style>
