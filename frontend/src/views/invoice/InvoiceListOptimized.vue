<template>
  <div class="invoice-list">
    <!-- 页面头部 -->
    <PageHeader
      title="发票管理"
      description="管理企业进项和销项发票"
      :breadcrumbs="breadcrumbItems"
    >
      <template #actions>
        <a-space>
          <a-button type="primary" @click="handleCreate">
            <PlusOutlined />
            新增发票
          </a-button>
          <a-button @click="handleUpload">
            <UploadOutlined />
            批量上传
          </a-button>
          <a-button 
            :disabled="!hasSelection" 
            @click="handleBatchVerify"
            :loading="batchVerifyLoading"
          >
            <CheckCircleOutlined />
            批量核验
          </a-button>
          <a-button 
            :disabled="!hasSelection" 
            danger
            @click="handleBatchDelete"
            :loading="batchDeleteLoading"
          >
            <DeleteOutlined />
            批量删除
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <!-- 统计卡片 -->
    <a-row :gutter="16" class="stats-row">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="总发票数"
            :value="stats?.total || 0"
            :loading="statsLoading"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="已核验"
            :value="stats?.verified || 0"
            :loading="statsLoading"
            value-style="color: #52c41a"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="待核验"
            :value="stats?.pending || 0"
            :loading="statsLoading"
            value-style="color: #faad14"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="本月金额"
            :value="stats?.monthlyAmount || 0"
            :precision="2"
            :loading="statsLoading"
            prefix="¥"
            value-style="color: #1890ff"
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索表单 -->
    <InvoiceSearchForm
      v-model:form-data="searchForm"
      :loading="searching"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <a-card :bordered="false" class="table-card">
      <a-table
        :columns="tableColumns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        row-key="id"
        :scroll="{ x: 1200 }"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'invoiceInfo'">
            <div class="invoice-info">
              <div class="invoice-number">{{ record.invoice_number }}</div>
              <div class="invoice-date">{{ formatDate(record.invoice_date) }}</div>
            </div>
          </template>

          <template v-if="column.key === 'amount'">
            <div class="amount-info">
              <div class="total-amount">¥{{ formatCurrency(record.amount) }}</div>
              <div class="tax-amount">税额: ¥{{ formatCurrency(record.tax_amount) }}</div>
            </div>
          </template>

          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'status'">
            <a-badge
              :status="getStatusBadge(record.status)"
              :text="getStatusText(record.status)"
            />
          </template>

          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                :disabled="record.status === 'verified'"
                @click="handleVerify(record)"
              >
                核验
              </a-button>
              <a-button
                type="link"
                size="small"
                danger
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  UploadOutlined,
  CheckCircleOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// 组件导入
import PageHeader from '@/components/common/PageHeader.vue'
import InvoiceSearchForm from '@/components/business/InvoiceSearchForm.vue'

// API导入
import {
  getInvoices,
  getInvoiceStats,
  verifyInvoice,
  batchVerifyInvoices,
  deleteInvoice,
  batchDeleteInvoices
} from '@/api/invoice'

// Composable导入
import { useStandardPagination, useStandardApi, useStandardOperation } from '@/composables/useStandardApi'
import { useSearchForm } from '@/composables/useForm'
import { useTable } from '@/composables/useTable'

/**
 * @typedef {import('@/types/api').Invoice} Invoice
 * @typedef {import('@/types/api').SearchParams} InvoiceSearchParams
 */

export default defineComponent({
  name: 'InvoiceListOptimized',
  components: {
    PageHeader,
    InvoiceSearchForm,
    PlusOutlined,
    UploadOutlined,
    CheckCircleOutlined,
    DeleteOutlined
  },
  setup() {
    const router = useRouter()

    // 面包屑导航
    const breadcrumbItems = [
      { title: '首页', path: '/' },
      { title: '发票管理', path: '/invoice' },
      { title: '发票列表' }
    ]

    // 表格列配置
    const columns = [
      {
        title: '发票信息',
        key: 'invoiceInfo',
        width: 200
      },
      {
        title: '企业名称',
        dataIndex: 'enterprise_name',
        width: 200,
        ellipsis: true
      },
      {
        title: '金额信息',
        key: 'amount',
        width: 150,
        sorter: true
      },
      {
        title: '类型',
        key: 'type',
        width: 100,
        filters: [
          { text: '进项', value: 'input' },
          { text: '销项', value: 'output' }
        ]
      },
      {
        title: '状态',
        key: 'status',
        width: 120,
        filters: [
          { text: '正常', value: 'normal' },
          { text: '已核验', value: 'verified' },
          { text: '已作废', value: 'cancelled' }
        ]
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        width: 150,
        sorter: true,
        customRender: ({ text }) => formatDate(text)
      },
      {
        title: '操作',
        key: 'actions',
        width: 200,
        fixed: 'right'
      }
    ]

    // 使用标准化的表格处理
    const {
      loading,
      dataSource,
      pagination,
      selectedRowKeys,
      selectedRows,
      hasSelection,
      tableColumns,
      loadData,
      handleTableChange: handleStandardTableChange,
      search,
      reset,
      refresh,
      batchConfirm,
      rowConfirm
    } = useTable({
      columns,
      fetchData: getInvoices,
      defaultParams: {
        keyword: '',
        type: '',
        status: '',
        enterprise_id: ''
      },
      pageSize: 10,
      showSelection: true
    })

    // 使用标准化的搜索表单
    const {
      searchForm,
      searching,
      handleSearch: executeSearch,
      handleReset: executeReset
    } = useSearchForm({
      initialValues: {
        keyword: '',
        type: '',
        status: '',
        enterprise_id: '',
        dateRange: null
      },
      onSearch: (params) => {
        search({
          keyword: params.keyword,
          type: params.type,
          status: params.status,
          enterprise_id: params.enterprise_id
        })
      },
      onReset: () => {
        reset()
      }
    })

    // 使用标准化的API调用获取统计信息
    const {
      loading: statsLoading,
      data: stats,
      execute: fetchStats
    } = useStandardApi(getInvoiceStats, {
      immediate: true,
      showError: true
    })

    // 使用标准化的操作
    const {
      loading: verifyLoading,
      execute: executeVerify
    } = useStandardOperation(verifyInvoice, {
      successMessage: '发票核验成功',
      onSuccess: () => {
        refresh()
        fetchStats()
      }
    })

    const {
      loading: batchVerifyLoading,
      execute: executeBatchVerify
    } = useStandardOperation(batchVerifyInvoices, {
      successMessage: '批量核验完成',
      onSuccess: () => {
        refresh()
        fetchStats()
      }
    })

    const {
      loading: deleteLoading,
      execute: executeDelete
    } = useStandardOperation(deleteInvoice, {
      successMessage: '删除发票成功',
      onSuccess: () => {
        refresh()
        fetchStats()
      }
    })

    const {
      loading: batchDeleteLoading,
      execute: executeBatchDelete
    } = useStandardOperation(batchDeleteInvoices, {
      successMessage: '批量删除完成',
      onSuccess: () => {
        refresh()
        fetchStats()
      }
    })

    // 计算属性
    const rowSelection = computed(() => ({
      selectedRowKeys: selectedRowKeys.value,
      onChange: (keys, rows) => {
        selectedRowKeys.value = keys
        selectedRows.value = rows
      }
    }))

    // 方法定义
    const handleSearch = () => {
      executeSearch()
    }

    const handleReset = () => {
      executeReset()
    }

    const handleTableChange = (pag, filters, sorter) => {
      handleStandardTableChange(pag, filters, sorter)
    }

    const handleCreate = () => {
      router.push('/invoice/create')
    }

    const handleUpload = () => {
      router.push('/invoice/upload')
    }

    const handleView = (record) => {
      router.push(`/invoice/detail/${record.id}`)
    }

    const handleEdit = (record) => {
      router.push(`/invoice/edit/${record.id}`)
    }

    const handleVerify = (record) => {
      rowConfirm(
        record,
        '确认核验发票',
        `确定要核验发票"${record.invoice_number}"吗？`,
        () => executeVerify(record.id)
      )
    }

    const handleDelete = (record) => {
      rowConfirm(
        record,
        '确认删除发票',
        `确定要删除发票"${record.invoice_number}"吗？此操作不可恢复。`,
        () => executeDelete(record.id)
      )
    }

    const handleBatchVerify = () => {
      batchConfirm(
        '批量核验发票',
        `确定要核验选中的 ${selectedRowKeys.value.length} 张发票吗？`,
        (keys) => executeBatchVerify(keys)
      )
    }

    const handleBatchDelete = () => {
      batchConfirm(
        '批量删除发票',
        `确定要删除选中的 ${selectedRowKeys.value.length} 张发票吗？此操作不可恢复。`,
        (keys) => executeBatchDelete(keys)
      )
    }

    // 辅助方法
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleDateString()
    }

    const formatCurrency = (amount) => {
      if (!amount) return '0.00'
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }

    const getTypeColor = (type) => {
      const colors = {
        input: 'blue',
        output: 'green'
      }
      return colors[type] || 'default'
    }

    const getTypeText = (type) => {
      const texts = {
        input: '进项',
        output: '销项'
      }
      return texts[type] || '未知'
    }

    const getStatusBadge = (status) => {
      const badges = {
        normal: 'processing',
        verified: 'success',
        cancelled: 'error'
      }
      return badges[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        normal: '正常',
        verified: '已核验',
        cancelled: '已作废'
      }
      return texts[status] || '未知'
    }

    // 生命周期
    onMounted(() => {
      loadData()
    })

    return {
      // 数据
      breadcrumbItems,
      searchForm,
      tableColumns,
      dataSource,
      pagination,
      stats,
      rowSelection,

      // 状态
      loading,
      searching,
      statsLoading,
      verifyLoading,
      batchVerifyLoading,
      deleteLoading,
      batchDeleteLoading,
      hasSelection,

      // 方法
      handleSearch,
      handleReset,
      handleTableChange,
      handleCreate,
      handleUpload,
      handleView,
      handleEdit,
      handleVerify,
      handleDelete,
      handleBatchVerify,
      handleBatchDelete,
      formatDate,
      formatCurrency,
      getTypeColor,
      getTypeText,
      getStatusBadge,
      getStatusText
    }
  }
})
</script>

<style scoped>
.invoice-list {
  padding: 24px;
}

.stats-row {
  margin-bottom: 16px;
}

.table-card {
  margin-top: 16px;
}

.invoice-info .invoice-number {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.invoice-info .invoice-date {
  font-size: 12px;
  color: #8c8c8c;
}

.amount-info .total-amount {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.amount-info .tax-amount {
  font-size: 12px;
  color: #8c8c8c;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
}

:deep(.ant-statistic-content) {
  font-size: 20px;
}
</style>
