<template>
  <div class="invoice-scan-container">
    <PageHeader
      title="发票扫描识别"
      description="上传发票图片进行OCR识别，自动提取发票信息"
      :breadcrumbs="breadcrumbs"
    >
      <template #actions>
        <a-space>
          <a-button @click="goBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
          <a-button type="primary" :disabled="fileList.length === 0" @click="batchScan">
            <ScanOutlined />
            批量识别
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <div class="scan-content">
      <a-row :gutter="24">
        <!-- 左侧上传和设置 -->
        <a-col :span="10">
          <a-card title="上传发票图片">
            <a-upload-dragger
              v-model:file-list="fileList"
              :before-upload="beforeUpload"
              multiple
              accept="image/*,.pdf"
              :max-count="10"
              @remove="handleRemove"
            >
              <p class="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p class="ant-upload-text">
                点击或拖拽文件到此区域上传
              </p>
              <p class="ant-upload-hint">
                支持 JPG、PNG、PDF 格式，单次最多上传10个文件，单个文件不超过10MB
              </p>
            </a-upload-dragger>

            <!-- 识别设置 -->
            <a-divider orientation="left">
              识别设置
            </a-divider>
            <a-form layout="vertical">
              <a-form-item label="发票类型">
                <a-select v-model:value="scanSettings.invoiceType" placeholder="自动识别">
                  <a-select-option value="">
                    自动识别
                  </a-select-option>
                  <a-select-option value="vat-special">
                    增值税专用发票
                  </a-select-option>
                  <a-select-option value="vat-ordinary">
                    增值税普通发票
                  </a-select-option>
                  <a-select-option value="electronic">
                    电子发票
                  </a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="识别精度">
                <a-radio-group v-model:value="scanSettings.accuracy">
                  <a-radio value="fast">
                    快速识别
                  </a-radio>
                  <a-radio value="accurate">
                    精确识别
                  </a-radio>
                </a-radio-group>
              </a-form-item>

              <a-form-item>
                <a-checkbox v-model:checked="scanSettings.autoCorrect">
                  自动纠错
                </a-checkbox>
              </a-form-item>

              <a-form-item>
                <a-checkbox v-model:checked="scanSettings.extractItems">
                  提取商品明细
                </a-checkbox>
              </a-form-item>
            </a-form>

            <!-- 批量操作 -->
            <div class="batch-actions">
              <a-space>
                <a-button
                  type="primary"
                  :loading="scanning"
                  :disabled="fileList.length === 0"
                  @click="startScan"
                >
                  <ScanOutlined />
                  开始识别
                </a-button>
                <a-button :disabled="fileList.length === 0" @click="clearAll">
                  清空列表
                </a-button>
              </a-space>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧预览和结果 -->
        <a-col :span="14">
          <a-card v-if="currentFile" title="识别结果">
            <template #extra>
              <a-space>
                <a-button size="small" @click="retryRecognition">
                  <ReloadOutlined />
                  重新识别
                </a-button>
                <a-button
                  size="small"
                  type="primary"
                  :disabled="!currentResult"
                  @click="saveResult"
                >
                  <SaveOutlined />
                  保存
                </a-button>
              </a-space>
            </template>

            <a-row :gutter="16">
              <!-- 图片预览 -->
              <a-col :span="10">
                <div class="image-preview">
                  <h4>原始图片</h4>
                  <div class="preview-container">
                    <img :src="currentFile.url" alt="发票图片">
                    <div v-if="recognitionProgress > 0" class="progress-overlay">
                      <a-progress
                        type="circle"
                        :percent="recognitionProgress"
                        :status="recognitionProgress === 100 ? 'success' : 'active'"
                      />
                      <p>识别中...</p>
                    </div>
                  </div>
                </div>
              </a-col>

              <!-- 识别结果 -->
              <a-col :span="14">
                <div v-if="currentResult" class="recognition-result">
                  <h4>识别信息</h4>
                  <a-form :model="currentResult" layout="vertical" size="small">
                    <a-row :gutter="12">
                      <a-col :span="12">
                        <a-form-item label="发票号码">
                          <a-input
                            v-model:value="currentResult.invoiceNumber"
                            :class="{ 'confidence-low': currentResult.confidence?.invoiceNumber < 0.8 }"
                          />
                        </a-form-item>
                      </a-col>
                      <a-col :span="12">
                        <a-form-item label="发票代码">
                          <a-input
                            v-model:value="currentResult.invoiceCode"
                            :class="{ 'confidence-low': currentResult.confidence?.invoiceCode < 0.8 }"
                          />
                        </a-form-item>
                      </a-col>
                    </a-row>

                    <a-row :gutter="12">
                      <a-col :span="12">
                        <a-form-item label="开票日期">
                          <a-date-picker
                            v-model:value="currentResult.issueDate"
                            style="width: 100%"
                            format="YYYY-MM-DD"
                          />
                        </a-form-item>
                      </a-col>
                      <a-col :span="12">
                        <a-form-item label="发票类型">
                          <a-select v-model:value="currentResult.type">
                            <a-select-option value="vat-special">
                              增值税专用发票
                            </a-select-option>
                            <a-select-option value="vat-ordinary">
                              增值税普通发票
                            </a-select-option>
                            <a-select-option value="electronic">
                              电子发票
                            </a-select-option>
                          </a-select>
                        </a-form-item>
                      </a-col>
                    </a-row>

                    <a-form-item label="销售方名称">
                      <a-input
                        v-model:value="currentResult.sellerName"
                        :class="{ 'confidence-low': currentResult.confidence?.sellerName < 0.8 }"
                      />
                    </a-form-item>

                    <a-form-item label="购买方名称">
                      <a-input
                        v-model:value="currentResult.buyerName"
                        :class="{ 'confidence-low': currentResult.confidence?.buyerName < 0.8 }"
                      />
                    </a-form-item>

                    <a-row :gutter="12">
                      <a-col :span="8">
                        <a-form-item label="不含税金额">
                          <a-input-number
                            v-model:value="currentResult.amountWithoutTax"
                            style="width: 100%"
                            :precision="2"
                            :class="{ 'confidence-low': currentResult.confidence?.amountWithoutTax < 0.8 }"
                          />
                        </a-form-item>
                      </a-col>
                      <a-col :span="8">
                        <a-form-item label="税额">
                          <a-input-number
                            v-model:value="currentResult.taxAmount"
                            style="width: 100%"
                            :precision="2"
                            :class="{ 'confidence-low': currentResult.confidence?.taxAmount < 0.8 }"
                          />
                        </a-form-item>
                      </a-col>
                      <a-col :span="8">
                        <a-form-item label="价税合计">
                          <a-input-number
                            v-model:value="currentResult.totalAmount"
                            style="width: 100%"
                            :precision="2"
                            :class="{ 'confidence-low': currentResult.confidence?.totalAmount < 0.8 }"
                          />
                        </a-form-item>
                      </a-col>
                    </a-row>

                    <!-- 置信度提示 -->
                    <a-alert
                      v-if="hasLowConfidence"
                      message="部分字段识别置信度较低，请仔细核对红色标记的字段"
                      type="warning"
                      show-icon
                      style="margin-bottom: 16px"
                    />

                    <!-- 商品明细 -->
                    <div v-if="scanSettings.extractItems && currentResult.items?.length">
                      <h4>商品明细</h4>
                      <a-table
                        :columns="itemColumns"
                        :data-source="currentResult.items"
                        :pagination="false"
                        size="small"
                      />
                    </div>
                  </a-form>
                </div>

                <div v-else-if="!scanning" class="no-result">
                  <a-empty description="选择文件开始识别" />
                </div>
              </a-col>
            </a-row>
          </a-card>

          <!-- 文件列表 -->
          <a-card v-else title="待处理文件">
            <a-list
              :data-source="fileList"
              :loading="scanning"
            >
              <template #renderItem="{ item, index }">
                <a-list-item>
                  <template #actions>
                    <a @click="selectFile(item, index)">识别</a>
                    <a @click="removeFile(index)">删除</a>
                  </template>
                  <a-list-item-meta>
                    <template #title>
                      <span>{{ item.name }}</span>
                      <a-tag v-if="item.status" :color="getStatusColor(item.status)" size="small">
                        {{ getStatusText(item.status) }}
                      </a-tag>
                    </template>
                    <template #description>
                      文件大小: {{ formatFileSize(item.size) }}
                    </template>
                    <template #avatar>
                      <a-avatar shape="square" :src="item.url" />
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import PageHeader from '@/components/common/PageHeader.vue'
import { scanInvoice } from '@/api/invoice'
import { useEnterpriseId } from '@/utils/enterprise-id-validator'
import {
  ArrowLeftOutlined,
  ScanOutlined,
  InboxOutlined,
  ReloadOutlined,
  SaveOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'InvoiceScan',
  components: {
    PageHeader,
    ArrowLeftOutlined,
    ScanOutlined,
    InboxOutlined,
    ReloadOutlined,
    SaveOutlined
  },
  setup () {
    const router = useRouter()
    const route = useRoute()

    // 企业ID验证
    const { enterpriseId, validateEnterpriseId } = useEnterpriseId(route, router, message)
    const fileList = ref([])
    const scanning = ref(false)
    const recognitionProgress = ref(0)
    const currentFile = ref(null)
    const currentResult = ref(null)
    const currentFileIndex = ref(-1)

    // 识别设置
    const scanSettings = reactive({
      invoiceType: '',
      accuracy: 'accurate',
      autoCorrect: true,
      extractItems: true
    })

    // 商品明细表格列
    const itemColumns = [
      {
        title: '商品名称',
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: '数量',
        dataIndex: 'quantity',
        key: 'quantity'
      },
      {
        title: '单价',
        dataIndex: 'unitPrice',
        key: 'unitPrice'
      },
      {
        title: '金额',
        dataIndex: 'amount',
        key: 'amount'
      }
    ]

    // 面包屑导航
    const breadcrumbs = [
      { title: '首页', path: '/' },
      { title: '票据管理', path: '/invoice' },
      { title: '发票列表', path: '/invoice/list' },
      { title: '扫描识别' }
    ]

    // 计算属性
    const hasLowConfidence = computed(() => {
      if (!currentResult.value?.confidence) return false
      return Object.values(currentResult.value.confidence).some(conf => conf < 0.8)
    })

    // 方法
    const beforeUpload = (file) => {
      const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)
      if (!isValidType) {
        message.error('只支持 JPG、PNG、PDF 格式的文件')
        return false
      }

      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        message.error('文件大小不能超过 10MB')
        return false
      }

      // 创建预览URL
      file.url = URL.createObjectURL(file)
      file.status = 'ready'

      return false // 阻止自动上传
    }

    const handleRemove = (file) => {
      const index = fileList.value.indexOf(file)
      if (index > -1) {
        URL.revokeObjectURL(file.url)
        fileList.value.splice(index, 1)
      }
    }

    const selectFile = (file, index) => {
      currentFile.value = file
      currentFileIndex.value = index
      currentResult.value = null
      recognizeSingleFile(file)
    }

    const removeFile = (index) => {
      const file = fileList.value[index]
      URL.revokeObjectURL(file.url)
      fileList.value.splice(index, 1)

      if (index === currentFileIndex.value) {
        currentFile.value = null
        currentResult.value = null
        currentFileIndex.value = -1
      }
    }

    const clearAll = () => {
      fileList.value.forEach(file => {
        URL.revokeObjectURL(file.url)
      })
      fileList.value = []
      currentFile.value = null
      currentResult.value = null
      currentFileIndex.value = -1
    }

    const startScan = () => {
      if (fileList.value.length === 0) return

      // 选择第一个文件开始识别
      selectFile(fileList.value[0], 0)
    }

    const batchScan = async () => {
      if (fileList.value.length === 0) return

      scanning.value = true
      for (let i = 0; i < fileList.value.length; i++) {
        const file = fileList.value[i]
        await recognizeSingleFile(file, false)
      }
      scanning.value = false
      message.success('批量识别完成')
    }

    const recognizeSingleFile = async (file, showProgress = true) => {
      if (showProgress) {
        scanning.value = true
        recognitionProgress.value = 0
      }

      try {
        file.status = 'processing'

        // 模拟进度更新
        if (showProgress) {
          const progressInterval = setInterval(() => {
            recognitionProgress.value += 10
            if (recognitionProgress.value >= 90) {
              clearInterval(progressInterval)
            }
          }, 200)
        }

        const formData = new FormData()
        formData.append('file', file.originFileObj || file)
        formData.append('settings', JSON.stringify(scanSettings))
        // 验证并添加企业ID
        if (!validateEnterpriseId()) {
          return
        }
        formData.append('enterpriseId', enterpriseId.value)

        const response = await scanInvoice(formData)

        if (response.code === 200) {
          const result = response.data
          file.status = 'success'

          if (showProgress) {
            recognitionProgress.value = 100
            // 使用真实的OCR结果
            currentResult.value = {
              // 基本发票信息
              invoiceNumber: result.ocrResult?.invoiceNumber || '',
              invoiceCode: result.ocrResult?.invoiceCode || '',
              invoiceType: result.ocrResult?.invoiceType || '',
              issueDate: result.ocrResult?.issueDate || '',
              sellerName: result.ocrResult?.sellerName || '',
              sellerTaxNumber: result.ocrResult?.sellerTaxNumber || '',
              buyerName: result.ocrResult?.buyerName || '',
              buyerTaxNumber: result.ocrResult?.buyerTaxNumber || '',
              totalAmount: result.ocrResult?.totalAmount || 0,
              taxAmount: result.ocrResult?.taxAmount || 0,
              totalWithTax: result.ocrResult?.totalWithTax || 0,
              verificationCode: result.ocrResult?.verificationCode || '',
              // 置信度信息
              confidence: result.ocrResult?.confidence || {},
              // 商品明细
              items: result.ocrResult?.items || [],
              // 发票ID
              invoiceId: result.invoiceId,
              // 识别状态
              success: result.success,
              message: result.message
            }
          }

          message.success(`${file.name} 识别完成`)
        } else {
          file.status = 'error'
          message.error(`${file.name} 识别失败: ${response.message}`)
        }
      } catch (error) {
        file.status = 'error'
        message.error(`${file.name} 识别失败`)
      } finally {
        if (showProgress) {
          scanning.value = false
          recognitionProgress.value = 0
        }
      }
    }

    const retryRecognition = () => {
      if (currentFile.value) {
        recognizeSingleFile(currentFile.value)
      }
    }

    const saveResult = () => {
      if (currentResult.value) {
        // 这里可以调用保存API
        message.success('发票信息已保存')

        // 标记当前文件为已处理
        if (currentFile.value) {
          currentFile.value.status = 'saved'
        }

        // 自动选择下一个文件
        const nextIndex = currentFileIndex.value + 1
        if (nextIndex < fileList.value.length) {
          selectFile(fileList.value[nextIndex], nextIndex)
        }
      }
    }

    const goBack = () => {
      router.go(-1)
    }

    // 辅助方法
    const getStatusColor = (status) => {
      const colors = {
        ready: 'default',
        processing: 'processing',
        success: 'success',
        error: 'error',
        saved: 'green'
      }
      return colors[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        ready: '待识别',
        processing: '识别中',
        success: '识别完成',
        error: '识别失败',
        saved: '已保存'
      }
      return texts[status] || '未知'
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    return {
      fileList,
      scanning,
      recognitionProgress,
      currentFile,
      currentResult,
      currentFileIndex,
      scanSettings,
      itemColumns,
      breadcrumbs,
      hasLowConfidence,
      beforeUpload,
      handleRemove,
      selectFile,
      removeFile,
      clearAll,
      startScan,
      batchScan,
      retryRecognition,
      saveResult,
      goBack,
      getStatusColor,
      getStatusText,
      formatFileSize
    }
  }
})
</script>

<style scoped>
.invoice-scan-container {
  background: #f5f5f5;
  min-height: 100vh;
}

.scan-content {
  padding: 0 24px 24px;
}

.batch-actions {
  margin-top: 16px;
}

.image-preview {
  text-align: center;
}

.preview-container {
  position: relative;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
  background: #fafafa;
}

.preview-container img {
  width: 100%;
  max-height: 400px;
  object-fit: contain;
}

.progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-overlay p {
  margin-top: 16px;
  color: #1890ff;
  font-weight: 500;
}

.recognition-result {
  max-height: 500px;
  overflow-y: auto;
}

.confidence-low {
  border-color: #ff4d4f !important;
  background-color: #fff2f0;
}

.no-result {
  text-align: center;
  padding: 40px 0;
}

:deep(.ant-upload-drag) {
  border: 2px dashed #d9d9d9;
}

:deep(.ant-upload-drag:hover) {
  border-color: #1890ff;
}

:deep(.ant-upload-drag .ant-upload-btn) {
  padding: 40px 0;
}

:deep(.ant-list-item-meta-avatar) {
  width: 48px;
  height: 48px;
}

:deep(.ant-avatar-square) {
  border-radius: 4px;
}
</style>
