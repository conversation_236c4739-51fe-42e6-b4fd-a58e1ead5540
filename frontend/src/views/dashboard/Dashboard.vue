<template>
  <div class="dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <a-row :gutter="24">
        <a-col :span="16">
          <a-card class="welcome-card">
            <template #title>
              <div class="welcome-title">

                <div class="welcome-text">
                  <h2>欢迎回来，{{ userInfo.name || '用户' }}！</h2>
                  <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
                </div>
              </div>
            </template>
            <div class="quick-stats">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-statistic title="今日发票" :value="todayInvoices" suffix="张" />
                </a-col>
                <a-col :span="6">
                  <a-statistic title="待申报" :value="pendingDeclarations" suffix="项" />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="本月税额"
                    :value="monthlyTax"
                    precision="2"
                    prefix="¥"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic title="企业数量" :value="enterpriseCount" suffix="家" />
                </a-col>
              </a-row>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="快速操作" class="quick-actions">
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <a-button
                  type="primary"
                  block
                  size="large"
                  @click="goToInvoiceUpload"
                >
                  <UploadOutlined />
                  上传发票
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button
                  type="primary"
                  block
                  size="large"
                  @click="goToDeclarationCreate"
                >
                  <FileAddOutlined />
                  新建申报
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button block size="large" @click="goToEnterpriseCreate">
                  <BankOutlined />
                  添加企业
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button block size="large" @click="goToSettings">
                  <SettingOutlined />
                  系统设置
                </a-button>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 数据概览 -->
    <div class="overview-section">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="企业总数"
              :value="overviewData.totalEnterprises"
              :value-style="{ color: '#3f8600' }"
              suffix="家"
            >
              <template #prefix>
                <BankOutlined />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">较上月 +12%</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="发票总数"
              :value="overviewData.totalInvoices"
              :value-style="{ color: '#1890ff' }"
              suffix="张"
            >
              <template #prefix>
                <FileTextOutlined />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">较上月 +23%</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="申报完成"
              :value="overviewData.completedDeclarations"
              :value-style="{ color: '#722ed1' }"
              suffix="项"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">本月完成率 96%</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="累计税额"
              :value="overviewData.totalTaxAmount"
              :value-style="{ color: '#fa541c' }"
              precision="2"
              prefix="¥"
            >
              <template #prefix>
                <MoneyCollectOutlined />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">较去年同期 +18%</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表和最近活动 -->
    <div class="charts-section">
      <a-row :gutter="24">
        <a-col :span="16">
          <a-card title="月度申报趋势" class="chart-card">
            <div id="monthlyChart" style="height: 400px;" />
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="最近活动" class="activity-card">
            <a-timeline>
              <a-timeline-item
                v-for="activity in recentActivities"
                :key="activity.id"
                :color="activity.color"
              >
                <div class="activity-item">
                  <div class="activity-title">
                    {{ activity.title }}
                  </div>
                  <div class="activity-time">
                    {{ activity.time }}
                  </div>
                  <div class="activity-desc">
                    {{ activity.description }}
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
            <div class="activity-more">
              <a-button type="link" @click="goToActivityLog">
                查看更多活动
              </a-button>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 待办事项和通知 -->
    <div class="todo-section">
      <a-row :gutter="24">
        <a-col :span="12">
          <a-card title="待办事项">
            <a-list item-layout="horizontal" :data-source="todoList">
              <template #renderItem="{ item }">
                <a-list-item>
                  <template #actions>
                    <a-button size="small" @click="markTodoComplete(item.id)">
                      完成
                    </a-button>
                  </template>
                  <a-list-item-meta>
                    <template #title>
                      <span :class="{ 'todo-urgent': item.urgent }">{{ item.title }}</span>
                    </template>
                    <template #description>
                      <div>
                        <div>{{ item.description }}</div>
                        <div class="todo-deadline">
                          截止时间: {{ item.deadline }}
                        </div>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="系统通知">
            <a-list item-layout="horizontal" :data-source="notifications">
              <template #renderItem="{ item }">
                <a-list-item>
                  <template #actions>
                    <a-button size="small" type="link" @click="markNotificationRead(item.id)">
                      标记已读
                    </a-button>
                  </template>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-badge :dot="!item.read">
                        <a-icon :type="item.icon" />
                      </a-badge>
                    </template>
                    <template #title>
                      {{ item.title }}
                    </template>
                    <template #description>
                      <div>{{ item.content }}</div>
                      <div class="notification-time">
                        {{ item.time }}
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'
import { getInvoiceStats } from '@/api/invoice'
import { getEnterpriseStats } from '@/api/enterprise'
import { getDeclarationStats } from '@/api/declaration'
import {
  UploadOutlined,
  FileAddOutlined,
  BankOutlined,
  SettingOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  MoneyCollectOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'DashboardMain',
  components: {
    UploadOutlined,
    FileAddOutlined,
    BankOutlined,
    SettingOutlined,
    FileTextOutlined,
    CheckCircleOutlined,
    MoneyCollectOutlined
  },
  setup () {
    const router = useRouter()
    const userStore = useUserStore()

    // 响应式数据
    const todayInvoices = ref(0)
    const pendingDeclarations = ref(0)
    const monthlyTax = ref(0)
    const enterpriseCount = ref(0)

    const overviewData = reactive({
      totalEnterprises: 0,
      totalInvoices: 0,
      completedDeclarations: 0,
      totalTaxAmount: 0
    })

    const recentActivities = ref([
      {
        id: 1,
        title: '增值税申报已提交',
        description: '企业A的2024年11月增值税申报已成功提交',
        time: '2小时前',
        color: 'green'
      },
      {
        id: 2,
        title: '发票批量上传完成',
        description: '共上传了45张发票，已自动识别并分类',
        time: '4小时前',
        color: 'blue'
      },
      {
        id: 3,
        title: '新企业注册',
        description: '企业B已成功注册并完成基础信息配置',
        time: '1天前',
        color: 'orange'
      },
      {
        id: 4,
        title: '系统维护通知',
        description: '系统将在今晚23:00-01:00进行例行维护',
        time: '2天前',
        color: 'red'
      }
    ])

    const todoList = ref([
      {
        id: 1,
        title: '企业所得税申报',
        description: '企业A的第三季度企业所得税申报',
        deadline: '2024-11-30',
        urgent: true
      },
      {
        id: 2,
        title: '发票核验',
        description: '10月份进项发票需要核验',
        deadline: '2024-11-25',
        urgent: false
      },
      {
        id: 3,
        title: '税务登记更新',
        description: '企业B的税务登记信息需要更新',
        deadline: '2024-11-28',
        urgent: false
      }
    ])

    const notifications = ref([
      {
        id: 1,
        title: '申报提醒',
        content: '您有3笔申报即将到期，请及时处理',
        time: '1小时前',
        icon: 'bell',
        read: false
      },
      {
        id: 2,
        title: '系统更新',
        content: '系统已更新至v2.1.0，新增了批量处理功能',
        time: '2天前',
        icon: 'notification',
        read: false
      },
      {
        id: 3,
        title: '政策提醒',
        content: '新的税收优惠政策已发布，建议及时了解',
        time: '3天前',
        icon: 'info-circle',
        read: true
      }
    ])

    // 计算属性
    const currentDate = computed(() => {
      return new Date().toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    })

    const userInfo = computed(() => {
      return userStore.userInfo || {}
    })

    // 方法
    const goToInvoiceUpload = () => {
      router.push('/invoice/upload')
    }

    const goToDeclarationCreate = () => {
      router.push('/declaration/create')
    }

    const goToEnterpriseCreate = () => {
      router.push('/enterprise/create')
    }

    const goToSettings = () => {
      router.push('/settings')
    }

    const goToActivityLog = () => {
      // 跳转到活动日志页面
      router.push('/activity-log')
    }

    const markTodoComplete = (id) => {
      const index = todoList.value.findIndex(item => item.id === id)
      if (index > -1) {
        todoList.value.splice(index, 1)
      }
    }

    const markNotificationRead = (id) => {
      const notification = notifications.value.find(item => item.id === id)
      if (notification) {
        notification.read = true
      }
    }

    // 获取仪表盘数据
    const fetchDashboardData = async () => {
      try {
        // 获取发票统计
        const invoiceStatsResponse = await getInvoiceStats()
        if (invoiceStatsResponse.code === 200) {
          const invoiceStats = invoiceStatsResponse.data
          todayInvoices.value = invoiceStats.todayCount || 0
          monthlyTax.value = invoiceStats.monthlyTax || 0
          overviewData.totalInvoices = invoiceStats.total || 0
        }

        // 获取企业统计
        const enterpriseStatsResponse = await getEnterpriseStats()
        if (enterpriseStatsResponse.code === 200) {
          const enterpriseStats = enterpriseStatsResponse.data
          enterpriseCount.value = enterpriseStats.total || 0
          overviewData.totalEnterprises = enterpriseStats.total || 0
        }

        // 获取申报统计
        const declarationStatsResponse = await getDeclarationStats()
        if (declarationStatsResponse.code === 200) {
          const declarationStats = declarationStatsResponse.data
          pendingDeclarations.value = declarationStats.pending || 0
          overviewData.completedDeclarations = declarationStats.completed || 0
          overviewData.totalTaxAmount = declarationStats.totalTaxAmount || 0
        }
      } catch (error) {
        console.error('获取仪表盘数据失败:', error)
      }
    }

    // 初始化用户信息
    const initUserInfo = async () => {
      if (userStore.token && !userStore.userInfo) {
        try {
          await userStore.fetchUserInfo()
        } catch (error) {
          console.error('获取用户信息失败:', error)
        }
      }
    }

    // 生命周期
    onMounted(async () => {
      await initUserInfo()
      fetchDashboardData()
      initializeChart()
    })

    const initializeChart = () => {
      // 这里可以集成图表库如ECharts
      // 初始化图表配置和数据
    }

    return {
      currentDate,
      userInfo,
      todayInvoices,
      pendingDeclarations,
      monthlyTax,
      enterpriseCount,
      overviewData,
      recentActivities,
      todoList,
      notifications,
      goToInvoiceUpload,
      goToDeclarationCreate,
      goToEnterpriseCreate,
      goToSettings,
      goToActivityLog,
      markTodoComplete,
      markNotificationRead
    }
  }
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.welcome-section {
  margin-bottom: 24px;
}

.welcome-card {
  height: 100%;
}

.welcome-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.welcome-text {
  margin-left: 16px;
}

.welcome-text h2 {
  margin: 0;
  color: #1890ff;
}

.welcome-text p {
  margin: 4px 0 0 0;
  color: #666;
}

.quick-stats {
  margin-top: 16px;
}

.quick-actions {
  height: 100%;
}

.overview-section {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  height: 100%;
}

.stat-trend {
  margin-top: 8px;
}

.trend-text {
  font-size: 12px;
  color: #52c41a;
}

.charts-section {
  margin-bottom: 24px;
}

.chart-card,
.activity-card {
  height: 480px;
}

.activity-item {
  padding-right: 16px;
}

.activity-title {
  font-weight: 500;
  color: #262626;
}

.activity-time {
  font-size: 12px;
  color: #8c8c8c;
  margin: 2px 0;
}

.activity-desc {
  font-size: 13px;
  color: #595959;
}

.activity-more {
  text-align: center;
  margin-top: 16px;
}

.todo-section {
  margin-bottom: 24px;
}

.todo-urgent {
  color: #ff4d4f;
  font-weight: 500;
}

.todo-deadline {
  font-size: 12px;
  color: #ff7875;
  margin-top: 4px;
}

.notification-time {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
}
</style>
