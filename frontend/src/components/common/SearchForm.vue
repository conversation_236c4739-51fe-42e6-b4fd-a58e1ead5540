<template>
  <div class="search-form">
    <a-card :bordered="bordered" size="small">
      <a-form
        :model="formData"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
        layout="horizontal"
        @finish="handleSearch"
      >
        <a-row :gutter="gutter">
          <slot :form-data="formData" />

          <a-col
            v-if="showActions"
            :span="actionSpan"
            :style="{ textAlign: 'right' }"
          >
            <a-space>
              <a-button
                type="primary"
                html-type="submit"
                :loading="loading"
              >
                <SearchOutlined />
                {{ searchText }}
              </a-button>
              <a-button @click="handleReset">
                <ReloadOutlined />
                {{ resetText }}
              </a-button>
              <a-button
                v-if="showExpand && expandable"
                type="link"
                @click="toggleExpand"
              >
                {{ expanded ? '收起' : '展开' }}
                <UpOutlined v-if="expanded" />
                <DownOutlined v-else />
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, watch } from 'vue'
import { SearchOutlined, ReloadOutlined, UpOutlined, DownOutlined } from '@ant-design/icons-vue'

export default defineComponent({
  name: 'SearchForm',
  components: {
    SearchOutlined,
    ReloadOutlined,
    UpOutlined,
    DownOutlined
  },
  props: {
    modelValue: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    },
    bordered: {
      type: Boolean,
      default: true
    },
    gutter: {
      type: Number,
      default: 16
    },
    labelCol: {
      type: Object,
      default: () => ({ span: 6 })
    },
    wrapperCol: {
      type: Object,
      default: () => ({ span: 18 })
    },
    showActions: {
      type: Boolean,
      default: true
    },
    actionSpan: {
      type: Number,
      default: 6
    },
    searchText: {
      type: String,
      default: '搜索'
    },
    resetText: {
      type: String,
      default: '重置'
    },
    expandable: {
      type: Boolean,
      default: false
    },
    showExpand: {
      type: Boolean,
      default: false
    },
    defaultExpanded: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'search', 'reset', 'expand'],
  setup (props, { emit }) {
    const formData = reactive({ ...props.modelValue })
    const expanded = ref(props.defaultExpanded)

    // 监听外部数据变化
    watch(
      () => props.modelValue,
      (newValue) => {
        Object.assign(formData, newValue)
      },
      { deep: true }
    )

    // 监听内部数据变化
    watch(
      formData,
      (newValue) => {
        emit('update:modelValue', { ...newValue })
      },
      { deep: true }
    )

    const handleSearch = () => {
      emit('search', { ...formData })
    }

    const handleReset = () => {
      // 重置表单数据
      Object.keys(formData).forEach(key => {
        formData[key] = undefined
      })
      emit('reset')
      emit('search', { ...formData })
    }

    const toggleExpand = () => {
      expanded.value = !expanded.value
      emit('expand', expanded.value)
    }

    return {
      formData,
      expanded,
      handleSearch,
      handleReset,
      toggleExpand
    }
  }
})
</script>

<style scoped>
.search-form {
  margin-bottom: 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}
</style>
