<template>
  <div v-if="hasError" class="error-boundary">
    <a-result
      status="error"
      :title="errorTitle"
      :sub-title="errorMessage"
    >
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleRetry">
            重试
          </a-button>
          <a-button @click="handleReset">
            重置
          </a-button>
          <a-button v-if="showDetails" type="link" @click="toggleDetails">
            {{ showErrorDetails ? '隐藏详情' : '显示详情' }}
          </a-button>
        </a-space>
      </template>
    </a-result>
    
    <!-- 错误详情 -->
    <div v-if="showDetails && showErrorDetails" class="error-details">
      <a-card title="错误详情" size="small">
        <pre>{{ errorDetails }}</pre>
      </a-card>
    </div>
  </div>
  
  <div v-else>
    <slot />
  </div>
</template>

<script>
import { defineComponent, ref, onErrorCaptured, onMounted } from 'vue'
import { handleError, ERROR_TYPES } from '@/utils/error-handler'

export default defineComponent({
  name: 'ErrorBoundary',
  props: {
    /**
     * 是否显示错误详情按钮
     */
    showDetails: {
      type: Boolean,
      default: process.env.NODE_ENV === 'development'
    },
    /**
     * 自定义错误标题
     */
    title: {
      type: String,
      default: '页面出现错误'
    },
    /**
     * 重试回调函数
     */
    onRetry: {
      type: Function,
      default: null
    },
    /**
     * 重置回调函数
     */
    onReset: {
      type: Function,
      default: null
    }
  },
  emits: ['error', 'retry', 'reset'],
  setup(props, { emit }) {
    const hasError = ref(false)
    const errorTitle = ref(props.title)
    const errorMessage = ref('')
    const errorDetails = ref('')
    const showErrorDetails = ref(false)

    // 捕获子组件错误
    onErrorCaptured((error, instance, info) => {
      console.error('ErrorBoundary caught error:', error, info)
      
      // 处理错误
      const errorInfo = handleError(error, {
        showMessage: false, // 不显示消息，由错误边界处理
        logError: true,
        rethrow: false
      }, {
        component: instance?.$options.name || 'Unknown',
        info
      })

      // 设置错误状态
      hasError.value = true
      errorMessage.value = errorInfo.message
      errorDetails.value = JSON.stringify({
        message: error.message,
        stack: error.stack,
        component: instance?.$options.name,
        info,
        timestamp: new Date().toISOString()
      }, null, 2)

      // 发出错误事件
      emit('error', {
        error,
        errorInfo,
        instance,
        info
      })

      // 阻止错误继续向上传播
      return false
    })

    // 处理重试
    const handleRetry = () => {
      if (props.onRetry && typeof props.onRetry === 'function') {
        props.onRetry()
      }
      
      // 重置错误状态
      hasError.value = false
      errorMessage.value = ''
      errorDetails.value = ''
      showErrorDetails.value = false
      
      emit('retry')
    }

    // 处理重置
    const handleReset = () => {
      if (props.onReset && typeof props.onReset === 'function') {
        props.onReset()
      }
      
      // 重置错误状态
      hasError.value = false
      errorMessage.value = ''
      errorDetails.value = ''
      showErrorDetails.value = false
      
      emit('reset')
      
      // 刷新页面
      window.location.reload()
    }

    // 切换错误详情显示
    const toggleDetails = () => {
      showErrorDetails.value = !showErrorDetails.value
    }

    // 监听全局错误
    onMounted(() => {
      // 监听未处理的Promise错误
      const handleUnhandledRejection = (event) => {
        console.error('Unhandled promise rejection:', event.reason)
        
        hasError.value = true
        errorMessage.value = '页面加载出现异常，请稍后重试'
        errorDetails.value = JSON.stringify({
          reason: event.reason?.message || event.reason,
          stack: event.reason?.stack,
          timestamp: new Date().toISOString()
        }, null, 2)
        
        emit('error', {
          error: event.reason,
          type: 'unhandledrejection'
        })
      }

      window.addEventListener('unhandledrejection', handleUnhandledRejection)

      // 清理函数
      return () => {
        window.removeEventListener('unhandledrejection', handleUnhandledRejection)
      }
    })

    return {
      hasError,
      errorTitle,
      errorMessage,
      errorDetails,
      showErrorDetails,
      handleRetry,
      handleReset,
      toggleDetails
    }
  }
})
</script>

<style scoped>
.error-boundary {
  padding: 24px;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.error-details {
  margin-top: 24px;
  width: 100%;
  max-width: 800px;
}

.error-details pre {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
  color: #666;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.ant-result-title) {
  color: #ff4d4f;
}

:deep(.ant-result-subtitle) {
  color: #666;
  margin-bottom: 24px;
}
</style>
