<template>
  <span
    :class="[
      'macos-status-badge',
      `macos-status-badge--${status}`,
      `macos-status-badge--${size}`,
      {
        'macos-status-badge--dot': dot,
        'macos-status-badge--outlined': outlined
      }
    ]"
  >
    <span v-if="dot" class="status-dot" />
    <span class="status-text">
      <slot>{{ text }}</slot>
    </span>
  </span>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'MacOSStatusBadge',
  props: {
    // 状态类型
    status: {
      type: String,
      default: 'default',
      validator: (value) => {
        return ['default', 'success', 'warning', 'danger', 'info', 'processing'].includes(value)
      }
    },
    // 尺寸
    size: {
      type: String,
      default: 'default',
      validator: (value) => {
        return ['small', 'default', 'large'].includes(value)
      }
    },
    // 显示文本
    text: {
      type: String,
      default: ''
    },
    // 是否显示圆点
    dot: {
      type: Boolean,
      default: false
    },
    // 是否为轮廓样式
    outlined: {
      type: Boolean,
      default: false
    }
  }
})
</script>

<style scoped>
/* macOS 风格状态标签 - 符合 macOS UI 风格 */
.macos-status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-large);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
  white-space: nowrap;
}

/* 尺寸变体 */
.macos-status-badge--small {
  padding: 2px var(--spacing-xs);
  font-size: 10px;
  border-radius: var(--spacing-sm);
}

.macos-status-badge--large {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-xlarge);
}

/* 状态颜色 - 默认实心样式 */
.macos-status-badge--default {
  background: var(--color-bg-quaternary);
  color: var(--color-text-primary);
}

.macos-status-badge--success {
  background: var(--color-success);
  color: white;
}

.macos-status-badge--warning {
  background: var(--color-warning);
  color: white;
}

.macos-status-badge--danger {
  background: var(--color-danger);
  color: white;
}

.macos-status-badge--info {
  background: var(--color-primary);
  color: white;
}

.macos-status-badge--processing {
  background: var(--color-primary);
  color: white;
  animation: macos-pulse 1.5s ease-in-out infinite;
}

/* 轮廓样式 */
.macos-status-badge--outlined.macos-status-badge--default {
  background: transparent;
  border-color: var(--color-bg-quaternary);
  color: var(--color-text-secondary);
}

.macos-status-badge--outlined.macos-status-badge--success {
  background: rgba(52, 199, 89, 0.1);
  border-color: var(--color-success);
  color: var(--color-success);
}

.macos-status-badge--outlined.macos-status-badge--warning {
  background: rgba(255, 149, 0, 0.1);
  border-color: var(--color-warning);
  color: var(--color-warning);
}

.macos-status-badge--outlined.macos-status-badge--danger {
  background: rgba(255, 59, 48, 0.1);
  border-color: var(--color-danger);
  color: var(--color-danger);
}

.macos-status-badge--outlined.macos-status-badge--info {
  background: rgba(0, 122, 255, 0.1);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.macos-status-badge--outlined.macos-status-badge--processing {
  background: rgba(0, 122, 255, 0.1);
  border-color: var(--color-primary);
  color: var(--color-primary);
  animation: macos-pulse 1.5s ease-in-out infinite;
}

/* 圆点样式 */
.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: var(--spacing-xs);
  background: currentColor;
  flex-shrink: 0;
}

.macos-status-badge--small .status-dot {
  width: 4px;
  height: 4px;
}

.macos-status-badge--large .status-dot {
  width: 8px;
  height: 8px;
}

/* 文本内容 */
.status-text {
  flex: 1;
}

/* 脉冲动画 */
@keyframes macos-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 悬停效果 */
.macos-status-badge:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .macos-status-badge {
    font-size: 10px;
    padding: 2px var(--spacing-xs);
  }

  .macos-status-badge--large {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
  }
}
</style>
