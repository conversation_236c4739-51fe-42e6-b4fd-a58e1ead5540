<template>
  <div class="macos-data-table">
    <!-- 工具栏 -->
    <div v-if="$slots.toolbar" class="data-table__toolbar">
      <slot name="toolbar" />
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="paginationConfig"
        :row-selection="rowSelection"
        :scroll="scroll"
        :size="size"
        :bordered="false"
        :row-key="rowKey"
        :class="['macos-table', { 'macos-table--striped': striped }]"
        v-bind="$attrs"
        @change="handleTableChange"
      >
        <template
          v-for="(_, name) in $slots"
          #[name]="slotData"
        >
          <slot
            :name="name"
            v-bind="slotData"
          />
        </template>
      </a-table>
    </div>
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue'

export default defineComponent({
  name: 'MacOSDataTable',
  inheritAttrs: false,
  props: {
    columns: {
      type: Array,
      required: true
    },
    dataSource: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    pagination: {
      type: [Object, Boolean],
      default: () => ({})
    },
    rowSelection: {
      type: Object,
      default: null
    },
    scroll: {
      type: Object,
      default: null
    },
    size: {
      type: String,
      default: 'middle',
      validator: (value) => ['small', 'middle', 'large'].includes(value)
    },
    striped: {
      type: Boolean,
      default: true
    },
    rowKey: {
      type: [String, Function],
      default: 'id'
    },
    showSizeChanger: {
      type: Boolean,
      default: true
    },
    showQuickJumper: {
      type: Boolean,
      default: true
    },
    showTotal: {
      type: [Boolean, Function],
      default: true,
      validator: (value) => typeof value === 'boolean' || typeof value === 'function'
    }
  },
  emits: ['change', 'pageChange', 'pageSizeChange', 'sortChange', 'filterChange'],
  setup (props, { emit }) {
    const paginationConfig = computed(() => {
      if (props.pagination === false) {
        return false
      }

      const defaultPagination = {
        showSizeChanger: props.showSizeChanger,
        showQuickJumper: props.showQuickJumper,
        showTotal: props.showTotal === true
          ? (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          : props.showTotal,
        pageSizeOptions: ['10', '20', '50', '100'],
        size: 'default'
      }

      return {
        ...defaultPagination,
        ...props.pagination
      }
    })

    const handleTableChange = (pagination, filters, sorter, extra) => {
      emit('change', { pagination, filters, sorter, extra })

      if (pagination) {
        emit('pageChange', pagination.current, pagination.pageSize)
        if (pagination.pageSize !== props.pagination?.pageSize) {
          emit('pageSizeChange', pagination.pageSize)
        }
      }

      if (sorter && (sorter.order || sorter.field)) {
        emit('sortChange', sorter)
      }

      if (filters && Object.keys(filters).length > 0) {
        emit('filterChange', filters)
      }
    }

    return {
      paginationConfig,
      handleTableChange
    }
  }
})
</script>

<style scoped>
/* macOS 风格数据表格 */
.macos-data-table {
  background: var(--color-bg-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-card);
  overflow: hidden;
}

.data-table__toolbar {
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-bg-quaternary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
}

.table-container {
  overflow: hidden;
}

/* 表格样式 */
.macos-table {
  :deep(.ant-table) {
    background: transparent;
    border-radius: 0;
  }

  :deep(.ant-table-container) {
    border-radius: 0;
  }

  :deep(.ant-table-thead > tr > th) {
    background: var(--color-bg-secondary);
    border-bottom: 1px solid var(--color-bg-quaternary);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    font-size: var(--font-size-sm);
    padding: var(--spacing-md) var(--spacing-lg);

    &:first-child {
      border-top-left-radius: 0;
    }

    &:last-child {
      border-top-right-radius: 0;
    }
  }

  :deep(.ant-table-tbody > tr > td) {
    border-bottom: 1px solid var(--color-bg-quaternary);
    padding: var(--spacing-md) var(--spacing-lg);
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background: var(--color-bg-secondary);
  }

  :deep(.ant-table-tbody > tr:last-child > td) {
    border-bottom: none;
  }

  /* 条纹样式 */
  &.macos-table--striped {
    :deep(.ant-table-tbody > tr:nth-child(even) > td) {
      background: rgba(0, 0, 0, 0.02);
    }

    :deep(.ant-table-tbody > tr:nth-child(even):hover > td) {
      background: var(--color-bg-secondary);
    }
  }

  /* 选择行样式 */
  :deep(.ant-table-tbody > tr.ant-table-row-selected > td) {
    background: rgba(0, 122, 255, 0.05);
  }

  :deep(.ant-table-tbody > tr.ant-table-row-selected:hover > td) {
    background: rgba(0, 122, 255, 0.1);
  }

  /* 排序图标 */
  :deep(.ant-table-column-sorter) {
    color: var(--color-text-tertiary);

    &.ant-table-column-sorter-up.active,
    &.ant-table-column-sorter-down.active {
      color: var(--color-primary);
    }
  }

  /* 筛选图标 */
  :deep(.ant-table-filter-trigger) {
    color: var(--color-text-tertiary);

    &.active {
      color: var(--color-primary);
    }
  }
}

/* 分页器样式 */
:deep(.ant-pagination) {
  margin: var(--spacing-md) var(--spacing-lg);
  text-align: right;

  .ant-pagination-item {
    border-radius: var(--radius-base);
    border: 1px solid var(--color-bg-quaternary);
    transition: all var(--transition-fast);

    &:hover {
      border-color: var(--color-primary);
      color: var(--color-primary);
    }

    &.ant-pagination-item-active {
      background: var(--color-primary);
      border-color: var(--color-primary);
      color: white;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    border-radius: var(--radius-base);
    border: 1px solid var(--color-bg-quaternary);
    transition: all var(--transition-fast);

    &:hover {
      border-color: var(--color-primary);
      color: var(--color-primary);
    }
  }

  .ant-pagination-options {
    .ant-select-selector {
      border-radius: var(--radius-base);
      border: 1px solid var(--color-bg-quaternary);
    }
  }
}

/* 加载状态 */
:deep(.ant-spin-container) {
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-table__toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .macos-table {
    :deep(.ant-table-thead > tr > th),
    :deep(.ant-table-tbody > tr > td) {
      padding: var(--spacing-sm) var(--spacing-md);
      font-size: var(--font-size-xs);
    }
  }

  :deep(.ant-pagination) {
    margin: var(--spacing-sm) var(--spacing-md);
    text-align: center;
  }
}
</style>
