<template>
  <div
    :class="[
      'macos-file-upload',
      `macos-file-upload--${type}`,
      {
        'macos-file-upload--disabled': disabled,
        'macos-file-upload--dragover': dragOver
      }
    ]"
  >
    <!-- 拖拽上传区域 -->
    <div
      v-if="type === 'drag'"
      class="upload-drag-area"
      @click="triggerFileSelect"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <div class="drag-content">
        <div class="drag-icon">
          <CloudUploadOutlined />
        </div>
        <div class="drag-text">
          <div class="drag-title">
            {{ dragTitle }}
          </div>
          <div class="drag-hint">
            {{ dragHint }}
          </div>
        </div>
      </div>
    </div>

    <!-- 按钮上传 -->
    <MacOSButton
      v-else-if="type === 'button'"
      :type="buttonType"
      :size="buttonSize"
      :loading="uploading"
      :disabled="disabled"
      @click="triggerFileSelect"
    >
      <template #icon>
        <UploadOutlined />
      </template>
      {{ buttonText }}
    </MacOSButton>

    <!-- 头像上传 -->
    <div
      v-else-if="type === 'avatar'"
      class="upload-avatar"
      @click="triggerFileSelect"
    >
      <img
        v-if="avatarUrl"
        :src="avatarUrl"
        :alt="avatarAlt"
        class="avatar-image"
      >
      <div v-else class="avatar-placeholder">
        <UserOutlined class="avatar-icon" />
      </div>
      <div class="avatar-overlay">
        <CameraOutlined class="camera-icon" />
      </div>
    </div>

    <!-- 隐藏的文件输入框 -->
    <input
      ref="fileInputRef"
      type="file"
      :accept="accept"
      :multiple="multiple"
      :disabled="disabled"
      style="display: none;"
      @change="handleFileSelect"
    >

    <!-- 文件列表 -->
    <div v-if="showFileList && fileList.length > 0" class="file-list">
      <div
        v-for="(file, index) in fileList"
        :key="file.uid || index"
        class="file-item"
      >
        <div class="file-info">
          <div class="file-icon">
            <component :is="getFileIcon(file)" />
          </div>
          <div class="file-details">
            <div class="file-name" :title="file.name">
              {{ file.name }}
            </div>
            <div class="file-size">
              {{ formatFileSize(file.size) }}
            </div>
          </div>
        </div>

        <div class="file-status">
          <!-- 上传进度 -->
          <div v-if="file.status === 'uploading'" class="upload-progress">
            <a-progress
              :percent="file.percent || 0"
              :size="'small'"
              :show-info="false"
            />
          </div>

          <!-- 状态图标 -->
          <div v-else class="status-icon">
            <CheckCircleOutlined
              v-if="file.status === 'done'"
              class="success-icon"
            />
            <CloseCircleOutlined
              v-else-if="file.status === 'error'"
              class="error-icon"
            />
          </div>

          <!-- 删除按钮 -->
          <MacOSButton
            type="text"
            size="small"
            :disabled="file.status === 'uploading'"
            class="remove-button"
            @click="removeFile(index)"
          >
            <template #icon>
              <DeleteOutlined />
            </template>
          </MacOSButton>
        </div>
      </div>
    </div>

    <!-- 上传提示 -->
    <div v-if="showTips && tips" class="upload-tips">
      {{ tips }}
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue'
import {
  CloudUploadOutlined,
  UploadOutlined,
  UserOutlined,
  CameraOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  FileTextOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FileOutlined
} from '@ant-design/icons-vue'
import MacOSButton from './MacOSButton.vue'

export default defineComponent({
  name: 'MacOSFileUpload',
  components: {
    MacOSButton,
    CloudUploadOutlined,
    UploadOutlined,
    UserOutlined,
    CameraOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    DeleteOutlined,
    FileTextOutlined,
    FileImageOutlined,
    FilePdfOutlined,
    FileExcelOutlined,
    FileWordOutlined,
    FileOutlined
  },
  props: {
    // 上传类型
    type: {
      type: String,
      default: 'drag',
      validator: (value) => {
        return ['drag', 'button', 'avatar'].includes(value)
      }
    },
    // 接受的文件类型
    accept: {
      type: String,
      default: '*'
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 文件大小限制（MB）
    maxSize: {
      type: Number,
      default: 10
    },
    // 文件数量限制
    maxCount: {
      type: Number,
      default: 1
    },
    // 是否显示文件列表
    showFileList: {
      type: Boolean,
      default: true
    },
    // 是否显示提示
    showTips: {
      type: Boolean,
      default: true
    },
    // 提示文本
    tips: {
      type: String,
      default: ''
    },
    // 拖拽区域标题
    dragTitle: {
      type: String,
      default: '点击或拖拽文件到此区域上传'
    },
    // 拖拽区域提示
    dragHint: {
      type: String,
      default: '支持单个或批量上传'
    },
    // 按钮文本
    buttonText: {
      type: String,
      default: '上传文件'
    },
    // 按钮类型
    buttonType: {
      type: String,
      default: 'default'
    },
    // 按钮尺寸
    buttonSize: {
      type: String,
      default: 'middle'
    },
    // 头像URL
    avatarUrl: {
      type: String,
      default: ''
    },
    // 头像alt文本
    avatarAlt: {
      type: String,
      default: '头像'
    },
    // 文件列表
    fileList: {
      type: Array,
      default: () => []
    },
    // 自定义上传函数
    customUpload: {
      type: Function,
      default: null
    }
  },
  emits: ['change', 'upload', 'remove', 'preview'],
  setup (props, { emit }) {
    const fileInputRef = ref(null)
    const dragOver = ref(false)
    const uploading = ref(false)

    // 计算提示文本
    const computedTips = computed(() => {
      if (props.tips) {
        return props.tips
      }

      const sizeText = `文件大小不超过 ${props.maxSize}MB`
      const countText = props.multiple ? `最多上传 ${props.maxCount} 个文件` : ''
      const acceptText = props.accept !== '*' ? `支持格式：${props.accept}` : ''

      return [sizeText, countText, acceptText].filter(Boolean).join('，')
    })

    // 触发文件选择
    const triggerFileSelect = () => {
      if (props.disabled) return
      fileInputRef.value?.click()
    }

    // 处理拖拽悬停
    const handleDragOver = (_e) => {
      if (props.disabled) return
      dragOver.value = true
    }

    // 处理拖拽离开
    const handleDragLeave = (_e) => {
      dragOver.value = false
    }

    // 处理文件拖拽
    const handleDrop = (e) => {
      if (props.disabled) return
      dragOver.value = false

      const files = Array.from(e.dataTransfer.files)
      processFiles(files)
    }

    // 处理文件选择
    const handleFileSelect = (e) => {
      const files = Array.from(e.target.files)
      processFiles(files)
      // 清空input值，允许重复选择同一文件
      e.target.value = ''
    }

    // 处理文件
    const processFiles = (files) => {
      if (!files.length) return

      // 检查文件数量限制
      if (!props.multiple && files.length > 1) {
        files = files.slice(0, 1)
      }

      if (props.fileList.length + files.length > props.maxCount) {
        const allowedCount = props.maxCount - props.fileList.length
        files = files.slice(0, allowedCount)
      }

      // 验证文件
      const validFiles = files.filter(file => validateFile(file))

      if (validFiles.length > 0) {
        emit('change', validFiles)

        // 如果有自定义上传函数，执行上传
        if (props.customUpload) {
          uploadFiles(validFiles)
        }
      }
    }

    // 验证文件
    const validateFile = (file) => {
      // 检查文件大小
      if (file.size > props.maxSize * 1024 * 1024) {
        console.warn(`文件 ${file.name} 大小超过限制`)
        return false
      }

      // 检查文件类型
      if (props.accept !== '*') {
        const acceptTypes = props.accept.split(',')
        const fileType = file.type
        const fileName = file.name

        const isValidType = acceptTypes.some(type => {
          type = type.trim()
          if (type.startsWith('.')) {
            return fileName.toLowerCase().endsWith(type.toLowerCase())
          }
          return fileType.includes(type.replace('*', ''))
        })

        if (!isValidType) {
          console.warn(`文件 ${file.name} 类型不支持`)
          return false
        }
      }

      return true
    }

    // 上传文件
    const uploadFiles = async (files) => {
      uploading.value = true

      try {
        for (const file of files) {
          await props.customUpload(file)
        }
      } catch (error) {
        console.error('文件上传失败:', error)
      } finally {
        uploading.value = false
      }
    }

    // 移除文件
    const removeFile = (index) => {
      emit('remove', index)
    }

    // 格式化文件大小
    const formatFileSize = (size) => {
      if (!size) return '0 B'

      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0

      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }

      return `${size.toFixed(index === 0 ? 0 : 1)} ${units[index]}`
    }

    // 获取文件图标
    const getFileIcon = (file) => {
      const fileName = file.name.toLowerCase()
      const fileType = file.type

      if (fileType.startsWith('image/')) {
        return FileImageOutlined
      }

      if (fileName.endsWith('.pdf')) {
        return FilePdfOutlined
      }

      if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        return FileExcelOutlined
      }

      if (fileName.endsWith('.docx') || fileName.endsWith('.doc')) {
        return FileWordOutlined
      }

      if (fileName.endsWith('.txt')) {
        return FileTextOutlined
      }

      return FileOutlined
    }

    return {
      fileInputRef,
      dragOver,
      uploading,
      computedTips,
      triggerFileSelect,
      handleDragOver,
      handleDragLeave,
      handleDrop,
      handleFileSelect,
      removeFile,
      formatFileSize,
      getFileIcon
    }
  }
})
</script>

<style scoped>
/* macOS 风格文件上传 - 符合 macOS UI 风格 */
.macos-file-upload {
  width: 100%;
}

/* 拖拽上传区域 */
.upload-drag-area {
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-base);
  background: var(--color-bg-container);
  padding: var(--spacing-xxl);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.upload-drag-area:hover,
.macos-file-upload--dragover .upload-drag-area {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.macos-file-upload--disabled .upload-drag-area {
  border-color: var(--color-border-light);
  background: var(--color-bg-disabled);
  cursor: not-allowed;
  opacity: 0.6;
}

.drag-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.drag-icon {
  font-size: 48px;
  color: var(--color-text-tertiary);
  transition: color var(--transition-fast);
}

.upload-drag-area:hover .drag-icon,
.macos-file-upload--dragover .drag-icon {
  color: var(--color-primary);
}

.drag-text {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.drag-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.drag-hint {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* 头像上传 */
.upload-avatar {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid var(--color-border-light);
  transition: all var(--transition-normal);
}

.upload-avatar:hover {
  border-color: var(--color-primary);
  transform: scale(1.05);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: var(--color-bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 32px;
  color: var(--color-text-tertiary);
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.upload-avatar:hover .avatar-overlay {
  opacity: 1;
}

.camera-icon {
  font-size: 24px;
  color: white;
}

/* 文件列表 */
.file-list {
  margin-top: var(--spacing-md);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-base);
  overflow: hidden;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border-light);
  transition: background var(--transition-fast);
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background: var(--color-bg-secondary);
}

.file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
  min-width: 0;
}

.file-icon {
  font-size: 20px;
  color: var(--color-primary);
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  margin-top: 2px;
}

.file-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.upload-progress {
  width: 100px;
}

.status-icon {
  font-size: 16px;
}

.success-icon {
  color: var(--color-success);
}

.error-icon {
  color: var(--color-danger);
}

.remove-button {
  opacity: 0.6;
  transition: opacity var(--transition-fast);
}

.remove-button:hover {
  opacity: 1;
}

/* 上传提示 */
.upload-tips {
  margin-top: var(--spacing-sm);
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-drag-area {
    padding: var(--spacing-xl);
  }

  .drag-icon {
    font-size: 36px;
  }

  .drag-title {
    font-size: var(--font-size-sm);
  }

  .drag-hint {
    font-size: var(--font-size-xs);
  }

  .upload-avatar {
    width: 80px;
    height: 80px;
  }

  .avatar-icon {
    font-size: 24px;
  }

  .camera-icon {
    font-size: 18px;
  }

  .file-item {
    padding: var(--spacing-sm);
  }

  .upload-progress {
    width: 80px;
  }
}

/* 动画效果 */
.file-item {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 进度条样式覆盖 */
:deep(.ant-progress-line) {
  margin: 0;
}

:deep(.ant-progress-bg) {
  background: var(--color-primary);
  border-radius: var(--radius-small);
}

:deep(.ant-progress-outer) {
  background: var(--color-bg-tertiary);
  border-radius: var(--radius-small);
}
</style>
