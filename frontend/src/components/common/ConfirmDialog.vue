<template>
  <a-modal
    v-model:open="visible"
    :title="null"
    :footer="null"
    :width="width"
    :centered="true"
    :closable="false"
    :mask-closable="maskClosable"
    :destroy-on-close="true"
    wrap-class-name="macos-confirm-dialog-wrap"
    @cancel="handleCancel"
  >
    <div class="macos-confirm-dialog">
      <!-- 图标区域 -->
      <div v-if="icon || type" class="dialog-icon">
        <component
          :is="icon"
          v-if="icon"
          :class="['icon-component', `icon-${type}`]"
        />
        <component
          :is="typeIcon"
          v-else
          :class="['icon-component', `icon-${type}`]"
        />
      </div>

      <!-- 内容区域 -->
      <div class="dialog-content">
        <!-- 标题 -->
        <div v-if="title" class="dialog-title">
          {{ title }}
        </div>

        <!-- 内容 -->
        <div class="dialog-message">
          <slot>
            <div v-if="content" v-html="content" />
          </slot>
        </div>

        <!-- 输入框（用于确认输入） -->
        <div v-if="requireInput" class="dialog-input">
          <a-input
            ref="inputRef"
            v-model:value="inputValue"
            :placeholder="inputPlaceholder"
            :maxlength="inputMaxLength"
            @keyup.enter="handleConfirm"
          />
          <div v-if="inputError" class="input-error">
            {{ inputError }}
          </div>
        </div>
      </div>

      <!-- 按钮区域 -->
      <div class="dialog-actions">
        <MacOSButton
          v-if="showCancel"
          type="default"
          :size="buttonSize"
          :disabled="loading"
          @click="handleCancel"
        >
          {{ cancelText }}
        </MacOSButton>

        <MacOSButton
          :type="confirmButtonType"
          :size="buttonSize"
          :loading="loading"
          :disabled="confirmDisabled"
          @click="handleConfirm"
        >
          {{ confirmText }}
        </MacOSButton>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { defineComponent, ref, computed, watch, nextTick } from 'vue'
import {
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons-vue'
import MacOSButton from './MacOSButton.vue'

export default defineComponent({
  name: 'MacOSConfirmDialog',
  components: {
    MacOSButton,
    ExclamationCircleOutlined,
    InfoCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    QuestionCircleOutlined
  },
  props: {
    // 显示状态
    open: {
      type: Boolean,
      default: false
    },
    // 对话框类型
    type: {
      type: String,
      default: 'warning',
      validator: (value) => {
        return ['info', 'success', 'warning', 'error', 'confirm'].includes(value)
      }
    },
    // 自定义图标
    icon: {
      type: [String, Object],
      default: null
    },
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 内容
    content: {
      type: String,
      default: ''
    },
    // 确认按钮文本
    confirmText: {
      type: String,
      default: '确认'
    },
    // 取消按钮文本
    cancelText: {
      type: String,
      default: '取消'
    },
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      default: true
    },
    // 对话框宽度
    width: {
      type: [String, Number],
      default: 416
    },
    // 是否可以点击遮罩关闭
    maskClosable: {
      type: Boolean,
      default: false
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 是否需要输入确认
    requireInput: {
      type: Boolean,
      default: false
    },
    // 输入框占位符
    inputPlaceholder: {
      type: String,
      default: '请输入确认内容'
    },
    // 输入框最大长度
    inputMaxLength: {
      type: Number,
      default: 100
    },
    // 输入验证函数
    inputValidator: {
      type: Function,
      default: null
    },
    // 按钮尺寸
    buttonSize: {
      type: String,
      default: 'middle'
    }
  },
  emits: ['update:open', 'confirm', 'cancel'],
  setup (props, { emit }) {
    const visible = ref(props.open)
    const inputValue = ref('')
    const inputError = ref('')
    const inputRef = ref(null)

    // 监听 open 属性变化
    watch(() => props.open, (newVal) => {
      visible.value = newVal
      if (newVal && props.requireInput) {
        nextTick(() => {
          inputRef.value?.focus()
        })
      }
    })

    // 监听 visible 变化
    watch(visible, (newVal) => {
      emit('update:open', newVal)
      if (!newVal) {
        // 重置输入状态
        inputValue.value = ''
        inputError.value = ''
      }
    })

    // 类型对应的图标
    const typeIcon = computed(() => {
      const iconMap = {
        info: InfoCircleOutlined,
        success: CheckCircleOutlined,
        warning: ExclamationCircleOutlined,
        error: CloseCircleOutlined,
        confirm: QuestionCircleOutlined
      }
      return iconMap[props.type] || ExclamationCircleOutlined
    })

    // 确认按钮类型
    const confirmButtonType = computed(() => {
      const typeMap = {
        info: 'primary',
        success: 'primary',
        warning: 'primary',
        error: 'danger',
        confirm: 'primary'
      }
      return typeMap[props.type] || 'primary'
    })

    // 确认按钮是否禁用
    const confirmDisabled = computed(() => {
      if (!props.requireInput) {
        return false
      }
      return !inputValue.value.trim() || !!inputError.value
    })

    // 验证输入
    const validateInput = () => {
      if (!props.requireInput) {
        return true
      }

      const value = inputValue.value.trim()
      if (!value) {
        inputError.value = '请输入确认内容'
        return false
      }

      if (props.inputValidator) {
        const result = props.inputValidator(value)
        if (result !== true) {
          inputError.value = result || '输入内容不符合要求'
          return false
        }
      }

      inputError.value = ''
      return true
    }

    // 处理确认
    const handleConfirm = () => {
      if (!validateInput()) {
        return
      }

      const result = {
        type: 'confirm',
        inputValue: props.requireInput ? inputValue.value.trim() : undefined
      }

      emit('confirm', result)
    }

    // 处理取消
    const handleCancel = () => {
      visible.value = false
      emit('cancel', { type: 'cancel' })
    }

    // 监听输入变化
    watch(inputValue, () => {
      if (inputError.value) {
        validateInput()
      }
    })

    return {
      visible,
      inputValue,
      inputError,
      inputRef,
      typeIcon,
      confirmButtonType,
      confirmDisabled,
      handleConfirm,
      handleCancel
    }
  }
})
</script>

<style scoped>
/* macOS 风格确认对话框 - 符合 macOS UI 风格 */
:deep(.macos-confirm-dialog-wrap .ant-modal) {
  padding: 0;
}

:deep(.macos-confirm-dialog-wrap .ant-modal-content) {
  background: var(--color-bg-elevated);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-heavy);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--color-border-light);
  overflow: hidden;
}

:deep(.macos-confirm-dialog-wrap .ant-modal-body) {
  padding: 0;
}

.macos-confirm-dialog {
  padding: var(--spacing-xl);
  text-align: center;
}

/* 图标区域 */
.dialog-icon {
  margin-bottom: var(--spacing-lg);
}

.icon-component {
  font-size: 48px;
}

.icon-info {
  color: var(--color-primary);
}

.icon-success {
  color: var(--color-success);
}

.icon-warning {
  color: var(--color-warning);
}

.icon-error {
  color: var(--color-danger);
}

.icon-confirm {
  color: var(--color-primary);
}

/* 内容区域 */
.dialog-content {
  margin-bottom: var(--spacing-xl);
}

/* 标题 */
.dialog-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
  line-height: 1.4;
}

/* 消息内容 */
.dialog-message {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: 1.6;
  word-break: break-word;
}

/* 输入框区域 */
.dialog-input {
  margin-top: var(--spacing-lg);
  text-align: left;
}

.dialog-input :deep(.ant-input) {
  border-radius: var(--radius-base);
  border-color: var(--color-border);
  font-size: var(--font-size-base);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: all var(--transition-fast);
}

.dialog-input :deep(.ant-input:focus) {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

.input-error {
  color: var(--color-danger);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
  text-align: left;
}

/* 按钮区域 */
.dialog-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.dialog-actions :deep(.macos-button) {
  min-width: 80px;
  font-weight: var(--font-weight-medium);
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.macos-confirm-dialog-wrap .ant-modal) {
    margin: var(--spacing-lg);
    max-width: calc(100vw - 32px);
  }

  .macos-confirm-dialog {
    padding: var(--spacing-lg);
  }

  .icon-component {
    font-size: 40px;
  }

  .dialog-title {
    font-size: var(--font-size-base);
  }

  .dialog-message {
    font-size: var(--font-size-sm);
  }

  .dialog-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .dialog-actions :deep(.macos-button) {
    width: 100%;
  }
}

/* 动画效果 */
:deep(.macos-confirm-dialog-wrap .ant-modal-content) {
  animation: dialogFadeIn 0.2s ease-out;
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 遮罩动画 */
:deep(.macos-confirm-dialog-wrap .ant-modal-mask) {
  background: rgba(0, 0, 0, 0.45);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  :deep(.macos-confirm-dialog-wrap .ant-modal-content) {
    background: var(--color-bg-elevated-dark, #2d2d2d);
    border-color: var(--color-border-dark, #404040);
  }

  :deep(.macos-confirm-dialog-wrap .ant-modal-mask) {
    background: rgba(0, 0, 0, 0.6);
  }
}
</style>
