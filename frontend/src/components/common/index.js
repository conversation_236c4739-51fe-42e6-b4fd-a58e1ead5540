// macOS 风格通用组件导出文件

// macOS 风格组件
export { default as MacOSPageHeader } from './PageHeader.vue'
export { default as MacOSDataTable } from './DataTable.vue'
export { default as MacOSButton } from './MacOSButton.vue'
export { default as MacOSCard } from './MacOSCard.vue'
export { default as MacOSStatusBadge } from './StatusBadge.vue'
export { default as MacOSActionButtons } from './ActionButtons.vue'
export { default as MacOSStatCard } from './StatCard.vue'
export { default as MacOSConfirmDialog } from './ConfirmDialog.vue'
export { default as MacOSFileUpload } from './FileUpload.vue'

// 其他通用组件
export { default as SearchForm } from './SearchForm.vue'
export { default as FileIcon } from './FileIcon.vue'

// 兼容性导出（保持向后兼容）
export { default as PageHeader } from './PageHeader.vue'
export { default as DataTable } from './DataTable.vue'
export { default as StatusBadge } from './StatusBadge.vue'
export { default as ActionButtons } from './ActionButtons.vue'
export { default as StatCard } from './StatCard.vue'
export { default as ConfirmDialog } from './ConfirmDialog.vue'
export { default as FileUpload } from './FileUpload.vue'
