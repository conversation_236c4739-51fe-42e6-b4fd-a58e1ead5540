<template>
  <div class="macos-page-header">
    <!-- 面包屑导航 -->
    <div v-if="showBreadcrumb && breadcrumbs.length > 0" class="breadcrumb-container">
      <a-breadcrumb class="macos-breadcrumb">
        <a-breadcrumb-item v-for="item in breadcrumbs" :key="item.path || item.title">
          <router-link v-if="item.path" :to="item.path" class="breadcrumb-link">
            {{ item.title }}
          </router-link>
          <span v-else class="breadcrumb-current">{{ item.title }}</span>
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>

    <!-- 页面标题区域 -->
    <div class="header-content">
      <div class="header-left">
        <h1 class="page-title">
          {{ title }}
        </h1>
        <p v-if="description" class="page-description">
          {{ description }}
        </p>
      </div>
      <div v-if="$slots.actions" class="header-right">
        <div class="actions-container">
          <slot name="actions" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'MacOSPageHeader',
  props: {
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      default: ''
    },
    showBreadcrumb: {
      type: Boolean,
      default: true
    },
    breadcrumbs: {
      type: Array,
      default: () => [],
      validator: (value) => Array.isArray(value)
    }
  },
  setup () {
    return {}
  }
})
</script>

<style scoped>
/* macOS 风格页面头部 */
.macos-page-header {
  background: var(--color-bg-primary);
  padding: 0;
  margin-bottom: var(--spacing-lg);
}

/* 面包屑导航 */
.breadcrumb-container {
  padding: var(--spacing-sm) 0;
  margin-bottom: var(--spacing-sm);
}

.macos-breadcrumb {
  :deep(.ant-breadcrumb-link) {
    color: var(--color-text-secondary);
    transition: color var(--transition-fast);

    &:hover {
      color: var(--color-primary);
    }
  }

  :deep(.ant-breadcrumb-separator) {
    color: var(--color-text-tertiary);
  }
}

.breadcrumb-link {
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);

  &:hover {
    color: var(--color-primary);
  }
}

.breadcrumb-current {
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
}

/* 页面标题区域 */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.header-left {
  flex: 1;
  min-width: 0;
}

.page-title {
  margin: 0;
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  line-height: 1.2;
  font-family: var(--font-family-base);
}

.page-description {
  margin: var(--spacing-xs) 0 0 0;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.4;
}

.header-right {
  flex-shrink: 0;
  display: flex;
  align-items: flex-start;
}

.actions-container {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .header-right {
    width: 100%;
  }

  .actions-container {
    width: 100%;
    justify-content: flex-start;
  }

  .page-title {
    font-size: var(--font-size-xl);
  }

  .breadcrumb-container {
    padding: var(--spacing-xs) 0;
  }
}

@media (max-width: 480px) {
  .actions-container {
    flex-direction: column;
    align-items: stretch;

    :deep(.ant-btn) {
      width: 100%;
    }
  }

  .page-title {
    font-size: var(--font-size-lg);
  }
}
</style>
