<template>
  <div
    class="optimized-component"
    :class="{ 'dark-mode': isDark }"
  >
    <!-- 搜索输入框 -->
    <a-input
      v-model:value="searchTerm"
      type="search"
      placeholder="搜索..."
      class="search-input"
      @input="handleSearch"
    />

    <!-- 数据列表 -->
    <div class="data-list">
      <div
        v-for="item in filteredItems"
        :key="item.id"
        class="list-item"
        :class="{ active: item.id === selectedId }"
        @click="selectItem(item)"
      >
        <div class="item-content">
          <h3 class="item-title">{{ item.title }}</h3>
          <p class="item-description">{{ item.description }}</p>
        </div>
        <a-tag
          :color="getStatusColor(item.status)"
          class="item-status"
        >
          {{ item.status }}
        </a-tag>
      </div>
    </div>

    <!-- 空状态 -->
    <a-empty
      v-if="isEmpty"
      description="暂无数据"
      class="empty-state"
    />

    <!-- 加载状态 -->
    <a-spin
      v-if="loading"
      size="large"
      class="loading-spinner"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'

// Props定义
const props = defineProps({
  items: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  isDark: {
    type: Boolean,
    default: false
  }
})

// Emits定义
const emit = defineEmits(['select', 'search'])

// 响应式数据
const searchTerm = ref('')
const selectedId = ref(null)

// 计算属性
const filteredItems = computed(() => {
  if (!searchTerm.value) return props.items
  
  return props.items.filter(item =>
    item.title.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
    item.description.toLowerCase().includes(searchTerm.value.toLowerCase())
  )
})

const isEmpty = computed(() => {
  return !props.loading && filteredItems.value.length === 0
})

// 方法
const handleSearch = () => {
  emit('search', searchTerm.value)
}

const selectItem = (item) => {
  selectedId.value = item.id
  emit('select', item)
  message.success(`已选择: ${item.title}`)
}

const getStatusColor = (status) => {
  const colorMap = {
    active: 'green',
    inactive: 'red',
    pending: 'orange',
    draft: 'blue'
  }
  return colorMap[status] || 'default'
}

// 生命周期
onMounted(() => {
  console.log('OptimizedComponent mounted')
})
</script>

<style scoped>
.optimized-component {
  padding: var(--spacing-unit, 16px);
  background: var(--color-bg, #ffffff);
  border-radius: var(--radius-base, 6px);
  transition: var(--transition-base, all 150ms ease);
}

.dark-mode {
  background: var(--color-bg-dark, #1c1c1e);
  color: var(--color-text-dark, #efeff4);
}

.search-input {
  width: 100%;
  margin-bottom: var(--spacing-unit, 16px);
  border-radius: var(--radius-base, 6px);
  transition: var(--transition-base, all 150ms ease);
}

.search-input:focus {
  border-color: var(--color-primary, #0a84ff);
  box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.2);
}

.data-list {
  display: flex;
  flex-direction: column;
  gap: calc(var(--spacing-unit, 16px) / 2);
}

.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-unit, 16px);
  background: var(--color-surface, rgba(255, 255, 255, 0.8));
  border: 1px solid var(--color-divider, rgba(0, 0, 0, 0.12));
  border-radius: var(--radius-base, 6px);
  cursor: pointer;
  transition: var(--transition-base, all 150ms ease);
}

.list-item:hover {
  background: var(--color-surface-hover, rgba(10, 132, 255, 0.05));
  border-color: var(--color-primary, #0a84ff);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.list-item.active {
  background: var(--color-primary-light, rgba(10, 132, 255, 0.1));
  border-color: var(--color-primary, #0a84ff);
}

.item-content {
  flex: 1;
}

.item-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text, #1c1c1e);
  line-height: 1.4;
}

.item-description {
  margin: 0;
  font-size: 14px;
  color: var(--color-text-secondary, #8e8e93);
  line-height: 1.4;
}

.item-status {
  margin-left: var(--spacing-unit, 16px);
}

.empty-state {
  padding: calc(var(--spacing-unit, 16px) * 2);
  text-align: center;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: calc(var(--spacing-unit, 16px) * 2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .list-item {
    flex-direction: column;
    align-items: flex-start;
    gap: calc(var(--spacing-unit, 16px) / 2);
  }

  .item-status {
    margin-left: 0;
    align-self: flex-end;
  }
}

/* 深色模式适配 */
.dark-mode .list-item {
  background: var(--color-surface-dark, rgba(28, 28, 30, 0.8));
  border-color: var(--color-divider-dark, rgba(255, 255, 255, 0.12));
}

.dark-mode .item-title {
  color: var(--color-text-dark, #efeff4);
}

.dark-mode .item-description {
  color: var(--color-text-secondary-dark, #8e8e93);
}
</style>
