<template>
  <a-button
    :class="[
      'macos-button',
      `macos-button--${type}`,
      `macos-button--${size}`,
      {
        'macos-button--loading': loading,
        'macos-button--disabled': disabled
      }
    ]"
    :type="antdType"
    :size="size"
    :loading="loading"
    :disabled="disabled"
    :ghost="ghost"
    :block="block"
    @click="handleClick"
  >
    <template v-if="icon && !loading" #icon>
      <component :is="icon" />
    </template>
    <slot />
  </a-button>
</template>

<script>
import { defineComponent, computed } from 'vue'

export default defineComponent({
  name: 'MacOSButton',
  props: {
    type: {
      type: String,
      default: 'default',
      validator: (value) => ['primary', 'default', 'danger', 'success', 'warning', 'text'].includes(value)
    },
    size: {
      type: String,
      default: 'middle',
      validator: (value) => ['small', 'middle', 'large'].includes(value)
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    },
    ghost: {
      type: Boolean,
      default: false
    },
    block: {
      type: Boolean,
      default: false
    },
    icon: {
      type: [String, Object],
      default: null
    }
  },
  emits: ['click'],
  setup (props, { emit }) {
    // 映射到 Ant Design 的 type
    const antdType = computed(() => {
      const typeMap = {
        primary: 'primary',
        default: 'default',
        danger: 'primary',
        success: 'primary',
        warning: 'primary',
        text: 'text'
      }
      return typeMap[props.type] || 'default'
    })

    const handleClick = (event) => {
      if (!props.disabled && !props.loading) {
        emit('click', event)
      }
    }

    return {
      antdType,
      handleClick
    }
  }
})
</script>

<style scoped>
.macos-button {
  border-radius: var(--radius-base);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  border: none;
  font-family: var(--font-family-base);

  &:hover:not(.macos-button--disabled):not(.macos-button--loading) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
  }

  &:active:not(.macos-button--disabled):not(.macos-button--loading) {
    transform: translateY(0);
    box-shadow: var(--shadow-light);
  }

  /* 按钮类型样式 */
  &.macos-button--primary {
    background: var(--color-primary);
    color: white;

    &:hover:not(.macos-button--disabled) {
      background: var(--color-primary-hover);
    }

    &:active:not(.macos-button--disabled) {
      background: var(--color-primary-active);
    }
  }

  &.macos-button--default {
    background: var(--color-bg-secondary);
    color: var(--color-text-primary);

    &:hover:not(.macos-button--disabled) {
      background: var(--color-bg-tertiary);
      color: var(--color-primary);
    }
  }

  &.macos-button--danger {
    background: var(--color-danger);
    color: white;

    &:hover:not(.macos-button--disabled) {
      background: var(--color-danger-hover);
    }
  }

  &.macos-button--success {
    background: var(--color-success);
    color: white;

    &:hover:not(.macos-button--disabled) {
      background: var(--color-success-hover);
    }
  }

  &.macos-button--warning {
    background: var(--color-warning);
    color: white;

    &:hover:not(.macos-button--disabled) {
      background: var(--color-warning-hover);
    }
  }

  &.macos-button--text {
    background: transparent;
    color: var(--color-text-primary);

    &:hover:not(.macos-button--disabled) {
      background: rgba(0, 122, 255, 0.08);
      color: var(--color-primary);
      transform: none;
      box-shadow: none;
    }
  }

  /* 按钮尺寸样式 */
  &.macos-button--small {
    height: 28px;
    padding: 0 var(--spacing-sm);
    font-size: var(--font-size-xs);
    border-radius: var(--radius-small);
  }

  &.macos-button--middle {
    height: 36px;
    padding: 0 var(--spacing-md);
    font-size: var(--font-size-sm);
  }

  &.macos-button--large {
    height: 44px;
    padding: 0 var(--spacing-lg);
    font-size: var(--font-size-base);
    border-radius: var(--radius-large);
  }

  /* 禁用状态 */
  &.macos-button--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }

  /* 加载状态 */
  &.macos-button--loading {
    cursor: default;
  }
}

/* 覆盖 Ant Design 的默认样式 */
:deep(.ant-btn) {
  border: none;
  box-shadow: none;

  &:focus {
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
  }

  &.ant-btn-loading {
    .ant-btn-loading-icon {
      margin-right: var(--spacing-xs);
    }
  }
}
</style>
