<template>
  <div
    :class="[
      'macos-stat-card',
      `macos-stat-card--${size}`,
      {
        'macos-stat-card--loading': loading,
        'macos-stat-card--clickable': clickable,
        'macos-stat-card--trend-up': trend === 'up',
        'macos-stat-card--trend-down': trend === 'down'
      }
    ]"
    @click="handleClick"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="stat-loading">
      <a-spin size="small" />
    </div>

    <!-- 正常内容 -->
    <template v-else>
      <!-- 图标区域 -->
      <div v-if="icon || $slots.icon" class="stat-icon">
        <slot name="icon">
          <component :is="icon" class="icon-component" />
        </slot>
      </div>

      <!-- 主要内容 -->
      <div class="stat-content">
        <!-- 标题 -->
        <div class="stat-title">
          <slot name="title">
            {{ title }}
          </slot>
        </div>

        <!-- 数值 -->
        <div class="stat-value">
          <slot name="value">
            <span class="value-number">{{ formattedValue }}</span>
            <span v-if="unit" class="value-unit">{{ unit }}</span>
          </slot>
        </div>

        <!-- 趋势和描述 -->
        <div v-if="trend || description || $slots.description" class="stat-footer">
          <!-- 趋势指示器 -->
          <div v-if="trend" class="stat-trend">
            <component
              :is="trendIcon"
              class="trend-icon"
            />
            <span class="trend-text">{{ trendText }}</span>
          </div>

          <!-- 描述文本 -->
          <div v-if="description || $slots.description" class="stat-description">
            <slot name="description">
              {{ description }}
            </slot>
          </div>
        </div>
      </div>

      <!-- 操作区域 -->
      <div v-if="$slots.actions" class="stat-actions">
        <slot name="actions" />
      </div>
    </template>
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue'
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons-vue'

export default defineComponent({
  name: 'MacOSStatCard',
  components: {
    ArrowUpOutlined,
    ArrowDownOutlined
  },
  props: {
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 数值
    value: {
      type: [String, Number],
      default: 0
    },
    // 单位
    unit: {
      type: String,
      default: ''
    },
    // 图标
    icon: {
      type: [String, Object],
      default: null
    },
    // 趋势方向
    trend: {
      type: String,
      validator: (value) => {
        return ['up', 'down', ''].includes(value)
      }
    },
    // 趋势文本
    trendText: {
      type: String,
      default: ''
    },
    // 描述文本
    description: {
      type: String,
      default: ''
    },
    // 尺寸
    size: {
      type: String,
      default: 'default',
      validator: (value) => {
        return ['small', 'default', 'large'].includes(value)
      }
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      default: false
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 数值格式化选项
    precision: {
      type: Number,
      default: 0
    },
    // 是否使用千分位分隔符
    useGrouping: {
      type: Boolean,
      default: true
    }
  },
  emits: ['click'],
  setup (props, { emit }) {
    // 格式化数值
    const formattedValue = computed(() => {
      if (typeof props.value === 'string') {
        return props.value
      }

      const num = Number(props.value)
      if (isNaN(num)) {
        return '0'
      }

      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: props.precision,
        maximumFractionDigits: props.precision,
        useGrouping: props.useGrouping
      }).format(num)
    })

    // 趋势图标
    const trendIcon = computed(() => {
      return props.trend === 'up' ? ArrowUpOutlined : ArrowDownOutlined
    })

    // 点击处理
    const handleClick = () => {
      if (props.clickable && !props.loading) {
        emit('click')
      }
    }

    return {
      formattedValue,
      trendIcon,
      handleClick
    }
  }
})
</script>

<style scoped>
/* macOS 风格统计卡片 - 符合 macOS UI 风格 */
.macos-stat-card {
  background: var(--color-bg-container);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-base);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

/* 尺寸变体 */
.macos-stat-card--small {
  padding: var(--spacing-md);
}

.macos-stat-card--large {
  padding: var(--spacing-xl);
}

/* 可点击状态 */
.macos-stat-card--clickable {
  cursor: pointer;
}

.macos-stat-card--clickable:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  border-color: var(--color-primary-light);
}

.macos-stat-card--clickable:active {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

/* 趋势状态 */
.macos-stat-card--trend-up {
  border-left: 3px solid var(--color-success);
}

.macos-stat-card--trend-down {
  border-left: 3px solid var(--color-danger);
}

/* 加载状态 */
.macos-stat-card--loading {
  min-height: 120px;
}

.stat-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 80px;
}

/* 图标区域 */
.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--color-primary-light);
  border-radius: var(--radius-base);
  margin-bottom: var(--spacing-md);
}

.macos-stat-card--small .stat-icon {
  width: 36px;
  height: 36px;
  margin-bottom: var(--spacing-sm);
}

.macos-stat-card--large .stat-icon {
  width: 56px;
  height: 56px;
  margin-bottom: var(--spacing-lg);
}

.icon-component {
  font-size: 24px;
  color: var(--color-primary);
}

.macos-stat-card--small .icon-component {
  font-size: 18px;
}

.macos-stat-card--large .icon-component {
  font-size: 28px;
}

/* 内容区域 */
.stat-content {
  flex: 1;
}

/* 标题 */
.stat-title {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.macos-stat-card--small .stat-title {
  font-size: var(--font-size-xs);
}

.macos-stat-card--large .stat-title {
  font-size: var(--font-size-base);
}

/* 数值 */
.stat-value {
  display: flex;
  align-items: baseline;
  margin-bottom: var(--spacing-sm);
}

.value-number {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  line-height: 1;
}

.macos-stat-card--small .value-number {
  font-size: var(--font-size-xl);
}

.macos-stat-card--large .value-number {
  font-size: 32px;
}

.value-unit {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-left: var(--spacing-xs);
  font-weight: var(--font-weight-normal);
}

/* 底部区域 */
.stat-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

/* 趋势指示器 */
.stat-trend {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.trend-icon {
  font-size: 12px;
}

.macos-stat-card--trend-up .trend-icon,
.macos-stat-card--trend-up .trend-text {
  color: var(--color-success);
}

.macos-stat-card--trend-down .trend-icon,
.macos-stat-card--trend-down .trend-text {
  color: var(--color-danger);
}

.trend-text {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

/* 描述文本 */
.stat-description {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  line-height: 1.4;
}

/* 操作区域 */
.stat-actions {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .macos-stat-card {
    padding: var(--spacing-md);
  }

  .macos-stat-card--large {
    padding: var(--spacing-lg);
  }

  .stat-icon {
    width: 40px;
    height: 40px;
  }

  .icon-component {
    font-size: 20px;
  }

  .value-number {
    font-size: var(--font-size-xl);
  }

  .macos-stat-card--large .value-number {
    font-size: var(--font-size-xxl);
  }

  .stat-footer {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* 动画效果 */
.macos-stat-card {
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 数值变化动画 */
.value-number {
  transition: all var(--transition-normal);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .macos-stat-card {
    background: var(--color-bg-container-dark, #1e1e1e);
    border-color: var(--color-border-dark, #333);
  }

  .stat-icon {
    background: var(--color-primary-dark, rgba(0, 122, 255, 0.2));
  }
}
</style>
