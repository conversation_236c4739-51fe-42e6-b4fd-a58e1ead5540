<template>
  <div
    class="macos-action-buttons"
    :class="{
      [`macos-action-buttons--${size}`]: size,
      'macos-action-buttons--vertical': vertical,
      'macos-action-buttons--compact': compact
    }"
  >
    <!-- 主要操作按钮 -->
    <div v-if="actions.length > 0" class="macos-action-buttons__primary">
      <a-button
        v-for="(action, index) in actions.slice(0, maxPrimary)"
        :key="index"
        :type="action.type || 'default'"
        :size="size"
        :loading="action.loading"
        :disabled="action.disabled"
        :danger="action.danger"
        class="macos-action-buttons__button"
        @click="handleAction(action)"
      >
        <template v-if="action.icon" #icon>
          <component :is="action.icon" />
        </template>
        {{ action.label }}
      </a-button>
    </div>

    <!-- 更多操作下拉菜单 -->
    <div v-if="moreActions.length > 0" class="macos-action-buttons__more">
      <a-dropdown
        :trigger="['click']"
        placement="bottomRight"
        overlay-class-name="macos-action-buttons__dropdown"
      >
        <a-button :size="size" class="macos-action-buttons__more-button">
          更多
          <DownOutlined />
        </a-button>
        <template #overlay>
          <a-menu class="macos-action-buttons__menu">
            <a-menu-item
              v-for="(action, index) in moreActions"
              :key="index"
              :disabled="action.disabled"
              class="macos-action-buttons__menu-item"
              @click="handleAction(action)"
            >
              <template v-if="action.icon" #icon>
                <component :is="action.icon" />
              </template>
              <span :class="{ 'text-danger': action.danger }">{{ action.label }}</span>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'

export default defineComponent({
  name: 'MacOSActionButtons',
  components: {
    DownOutlined
  },
  props: {
    // 主要操作按钮列表
    actions: {
      type: Array,
      default: () => [],
      validator: (actions) => {
        return actions.every(action =>
          typeof action === 'object' &&
          typeof action.label === 'string'
        )
      }
    },
    // 最大主要按钮数量
    maxPrimary: {
      type: Number,
      default: 3
    },
    // 按钮尺寸
    size: {
      type: String,
      default: 'middle',
      validator: (value) => {
        return ['small', 'middle', 'large'].includes(value)
      }
    },
    // 是否垂直排列
    vertical: {
      type: Boolean,
      default: false
    },
    // 是否紧凑模式
    compact: {
      type: Boolean,
      default: false
    }
  },
  emits: ['action'],
  setup (props, { emit }) {
    // 计算更多操作按钮
    const moreActions = computed(() => {
      return props.actions.slice(props.maxPrimary)
    })

    const handleAction = (action) => {
      if (action.disabled || action.loading) {
        return
      }

      // 触发自定义事件
      emit('action', {
        action,
        key: action.key || action.label
      })

      // 执行按钮的回调函数
      if (typeof action.onClick === 'function') {
        action.onClick(action)
      }
    }

    return {
      moreActions,
      handleAction
    }
  }
})
</script>

<style lang="scss" scoped>
/* macOS 风格操作按钮组样式 */
.macos-action-buttons {
  display: flex;
  align-items: center;
  gap: var(--spacing-unit);

  /* 垂直排列 */
  &--vertical {
    flex-direction: column;
    align-items: stretch;

    .macos-action-buttons__button {
      width: 100%;
    }
  }

  /* 紧凑模式 */
  &--compact {
    gap: calc(var(--spacing-unit) / 2);
  }

  /* 尺寸变体 */
  &--small {
    .macos-action-buttons__button {
      font-size: 12px;
      padding: 4px 8px;
    }
  }

  &--large {
    .macos-action-buttons__button {
      font-size: 16px;
      padding: 12px 24px;
    }
  }
}

.macos-action-buttons__primary {
  display: flex;
  align-items: center;
  gap: var(--spacing-unit);
}

/* 操作按钮样式 */
.macos-action-buttons__button {
  /* 符合 macOS UI 风格的按钮样式 */
  border-radius: var(--radius-base);
  transition: all var(--transition-normal);
  font-weight: 500;

  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
  }

  &:active {
    transform: translateY(0);
  }
}

/* 更多操作按钮 */
.macos-action-buttons__more-button {
  /* 符合 macOS UI 风格 */
  .anticon {
    font-size: 12px;
  }
}

/* 更多操作菜单样式 */
:deep(.macos-action-buttons__dropdown) {
  .ant-dropdown-menu {
    /* 符合 macOS UI 风格的下拉菜单 */
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-base);
    box-shadow: var(--shadow-medium);
    padding: 4px;
  }
}

.macos-action-buttons__menu-item {
  border-radius: calc(var(--radius-base) - 2px);
  margin: 2px 0;
  transition: all var(--transition-fast);

  &:hover {
    background: rgba(var(--color-primary-rgb), 0.1);
  }

  .text-danger {
    color: var(--color-danger);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .macos-action-buttons {
    /* 移动端自动垂直排列 */
    flex-direction: column;
    align-items: stretch;

    .macos-action-buttons__primary {
      flex-direction: column;
      width: 100%;
    }

    .macos-action-buttons__button {
      width: 100%;
      justify-content: center;
    }
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  :deep(.macos-action-buttons__dropdown) {
    .ant-dropdown-menu {
      background: rgba(30, 30, 30, 0.8);
      border-color: rgba(255, 255, 255, 0.1);
    }
  }
}
</style>

<!--
macOS 风格操作按钮组组件

功能特性：
- 支持主要操作和更多操作的组合
- 毛玻璃效果的下拉菜单
- 响应式设计，移动端垂直排列
- 统一的事件处理机制
- 支持按钮状态管理（加载、禁用、危险）

使用示例：
<MacOSActionButtons
  :actions="[
    { label: '编辑', type: 'primary', icon: EditOutlined },
    { label: '删除', danger: true, icon: DeleteOutlined }
  ]"
  @action="handleAction"
/>
-->
