<template>
  <div class="file-icon" :style="{ fontSize: size + 'px' }">
    <component :is="iconComponent" :style="{ color: iconColor }" />
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue'
import {
  FileTextOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FilePptOutlined,
  FileZipOutlined,
  FileOutlined,
  PlayCircleOutlined,
  SoundOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'FileIcon',
  components: {
    FileTextOutlined,
    FilePdfOutlined,
    FileImageOutlined,
    FileExcelOutlined,
    FileWordOutlined,
    FilePptOutlined,
    FileZipOutlined,
    FileOutlined,
    PlayCircleOutlined,
    SoundOutlined
  },
  props: {
    fileType: {
      type: String,
      required: true
    },
    size: {
      type: Number,
      default: 16
    }
  },
  setup (props) {
    const iconComponent = computed(() => {
      const type = props.fileType.toLowerCase()

      // PDF 文件
      if (type === 'pdf') {
        return 'FilePdfOutlined'
      }

      // Word 文件
      if (['doc', 'docx'].includes(type)) {
        return 'FileWordOutlined'
      }

      // Excel 文件
      if (['xls', 'xlsx', 'csv'].includes(type)) {
        return 'FileExcelOutlined'
      }

      // PowerPoint 文件
      if (['ppt', 'pptx'].includes(type)) {
        return 'FilePptOutlined'
      }

      // 图片文件
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(type)) {
        return 'FileImageOutlined'
      }

      // 压缩文件
      if (['zip', 'rar', '7z', 'tar', 'gz'].includes(type)) {
        return 'FileZipOutlined'
      }

      // 视频文件
      if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(type)) {
        return 'PlayCircleOutlined'
      }

      // 音频文件
      if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(type)) {
        return 'SoundOutlined'
      }

      // 文本文件
      if (['txt', 'md', 'json', 'xml', 'js', 'css', 'html'].includes(type)) {
        return 'FileTextOutlined'
      }

      // 默认文件图标
      return 'FileOutlined'
    })

    const iconColor = computed(() => {
      const type = props.fileType.toLowerCase()

      // 定义文件类型对应的颜色
      const colorMap = {
        pdf: '#f40',
        doc: '#1890ff',
        docx: '#1890ff',
        xls: '#52c41a',
        xlsx: '#52c41a',
        csv: '#52c41a',
        ppt: '#fa8c16',
        pptx: '#fa8c16',
        jpg: '#722ed1',
        jpeg: '#722ed1',
        png: '#722ed1',
        gif: '#722ed1',
        bmp: '#722ed1',
        svg: '#722ed1',
        webp: '#722ed1',
        zip: '#8c8c8c',
        rar: '#8c8c8c',
        '7z': '#8c8c8c',
        tar: '#8c8c8c',
        gz: '#8c8c8c',
        mp4: '#eb2f96',
        avi: '#eb2f96',
        mov: '#eb2f96',
        wmv: '#eb2f96',
        flv: '#eb2f96',
        mkv: '#eb2f96',
        mp3: '#13c2c2',
        wav: '#13c2c2',
        flac: '#13c2c2',
        aac: '#13c2c2',
        ogg: '#13c2c2',
        txt: '#595959',
        md: '#595959',
        json: '#595959',
        xml: '#595959',
        js: '#faad14',
        css: '#1890ff',
        html: '#fa541c'
      }

      return colorMap[type] || '#8c8c8c'
    })

    return {
      iconComponent,
      iconColor
    }
  }
})
</script>

<style scoped>
.file-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
