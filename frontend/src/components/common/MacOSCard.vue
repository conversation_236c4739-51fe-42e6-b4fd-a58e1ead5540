<template>
  <a-card
    :class="[
      'macos-card',
      `macos-card--${size}`,
      {
        'macos-card--hoverable': hoverable,
        'macos-card--bordered': bordered,
        'macos-card--loading': loading
      }
    ]"
    :bordered="false"
    :loading="loading"
    :hoverable="false"
  >
    <template v-if="title || $slots.title" #title>
      <div class="macos-card-title">
        <slot name="title">
          {{ title }}
        </slot>
      </div>
    </template>

    <template v-if="$slots.extra" #extra>
      <div class="macos-card-extra">
        <slot name="extra" />
      </div>
    </template>

    <template v-if="$slots.actions" #actions>
      <div class="macos-card-actions">
        <slot name="actions" />
      </div>
    </template>

    <div class="macos-card-body">
      <slot />
    </div>
  </a-card>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'MacOSCard',
  props: {
    title: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'default',
      validator: (value) => ['small', 'default', 'large'].includes(value)
    },
    hoverable: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  }
})
</script>

<style scoped>
.macos-card {
  background: var(--color-bg-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-card);
  border: none;
  transition: all var(--transition-normal);
  overflow: hidden;

  /* 悬停效果 */
  &.macos-card--hoverable:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
  }

  /* 边框样式 */
  &.macos-card--bordered {
    border: 1px solid var(--color-bg-quaternary);
  }

  /* 尺寸变体 */
  &.macos-card--small {
    border-radius: var(--radius-base);

    .macos-card-body {
      padding: var(--spacing-md);
    }

    .macos-card-title {
      font-size: var(--font-size-sm);
      padding: var(--spacing-md) var(--spacing-md) 0;
    }
  }

  &.macos-card--default {
    .macos-card-body {
      padding: var(--spacing-lg);
    }

    .macos-card-title {
      font-size: var(--font-size-base);
      padding: var(--spacing-lg) var(--spacing-lg) 0;
    }
  }

  &.macos-card--large {
    border-radius: var(--radius-xlarge);

    .macos-card-body {
      padding: var(--spacing-xl);
    }

    .macos-card-title {
      font-size: var(--font-size-lg);
      padding: var(--spacing-xl) var(--spacing-xl) 0;
    }
  }
}

.macos-card-title {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.macos-card-extra {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.macos-card-body {
  color: var(--color-text-secondary);
  line-height: 1.6;
}

.macos-card-actions {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--color-bg-quaternary);
  background: var(--color-bg-secondary);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

/* 覆盖 Ant Design 默认样式 */
:deep(.ant-card) {
  border: none;
  box-shadow: none;
  background: transparent;
}

:deep(.ant-card-head) {
  border-bottom: 1px solid var(--color-bg-quaternary);
  padding: 0;
  min-height: auto;

  .ant-card-head-wrapper {
    padding: var(--spacing-lg);
  }

  .ant-card-head-title {
    padding: 0;
    font-size: inherit;
    font-weight: inherit;
    color: inherit;
  }

  .ant-card-extra {
    padding: 0;
    font-size: inherit;
  }
}

:deep(.ant-card-body) {
  padding: 0;
}

:deep(.ant-card-actions) {
  border-top: none;
  background: transparent;
  padding: 0;

  > li {
    margin: 0;

    &:not(:last-child) {
      border-right: none;
    }
  }
}

/* 加载状态 */
:deep(.ant-card-loading-content) {
  .ant-card-loading-block {
    background: var(--color-bg-tertiary);
    border-radius: var(--radius-small);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .macos-card {
    border-radius: var(--radius-base);

    &.macos-card--large {
      border-radius: var(--radius-large);
    }

    .macos-card-body {
      padding: var(--spacing-md);
    }

    .macos-card-title {
      padding: var(--spacing-md) var(--spacing-md) 0;
      font-size: var(--font-size-sm);
    }

    .macos-card-actions {
      padding: var(--spacing-sm) var(--spacing-md);
      flex-direction: column;

      :deep(.ant-btn) {
        width: 100%;
      }
    }
  }
}
</style>
