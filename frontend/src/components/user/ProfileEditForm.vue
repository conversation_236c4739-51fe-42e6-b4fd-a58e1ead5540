<template>
  <div class="profile-edit-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      layout="horizontal"
      @finish="handleSubmit"
    >
      <!-- 基本信息 -->
      <a-divider orientation="left">
        基本信息
      </a-divider>

      <a-form-item label="用户名" name="user_name">
        <a-input
          v-model:value="formData.user_name"
          placeholder="请输入用户名"
          :disabled="!editable"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="邮箱" name="email">
        <a-input
          v-model:value="formData.email"
          placeholder="请输入邮箱"
          :disabled="true"
          type="email"
        />
        <template #extra>
          <span class="form-extra-text">邮箱不可修改</span>
        </template>
      </a-form-item>

      <a-form-item label="手机号" name="phone">
        <a-input
          v-model:value="formData.phone"
          placeholder="请输入手机号"
          :disabled="!editable"
          allow-clear
        />
      </a-form-item>

      <!-- 工作信息 -->
      <a-divider orientation="left">
        工作信息
      </a-divider>

      <a-form-item label="部门" name="department">
        <a-input
          v-model:value="formData.department"
          placeholder="请输入部门"
          :disabled="!editable"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="职位" name="position">
        <a-input
          v-model:value="formData.position"
          placeholder="请输入职位"
          :disabled="!editable"
          allow-clear
        />
      </a-form-item>

      <!-- 个人信息 -->
      <a-divider orientation="left">
        个人信息
      </a-divider>





      <!-- 偏好设置 -->
      <a-divider orientation="left">
        偏好设置
      </a-divider>

      <a-form-item label="首选语言" name="preferredLang">
        <a-select
          v-model:value="formData.preferredLang"
          placeholder="请选择首选语言"
          :disabled="!editable"
        >
          <a-select-option value="zh-CN">
            简体中文
          </a-select-option>
          <a-select-option value="zh-TW">
            繁体中文
          </a-select-option>
          <a-select-option value="en-US">
            English
          </a-select-option>
        </a-select>
      </a-form-item>

      <!-- 操作按钮 -->
      <a-form-item v-if="editable" :wrapper-col="{ offset: 6, span: 18 }">
        <a-space>
          <a-button
            type="primary"
            html-type="submit"
            :loading="loading"
          >
            <SaveOutlined />
            保存修改
          </a-button>
          <a-button @click="handleCancel">
            <CloseOutlined />
            取消
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { SaveOutlined, CloseOutlined } from '@ant-design/icons-vue'

export default defineComponent({
  name: 'ProfileEditForm',
  components: {
    SaveOutlined,
    CloseOutlined
  },
  props: {
    userInfo: {
      type: Object,
      default: () => ({})
    },
    editable: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['submit', 'cancel'],
  setup (props, { emit }) {
    const formRef = ref()

    // 表单数据
    const formData = reactive({
      user_name: '',
      email: '',
      phone: '',
      department: '',
      position: '',

      preferredLang: 'zh-CN'
    })

    // 表单验证规则
    const formRules = {
      user_name: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' },
        { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/, message: '用户名只能包含中文、英文、数字和下划线', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      phone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ],
      department: [
        { max: 50, message: '部门名称不能超过50个字符', trigger: 'blur' }
      ],
      position: [
        { max: 50, message: '职位名称不能超过50个字符', trigger: 'blur' }
      ],
      bio: [
        { max: 200, message: '个人简介不能超过200个字符', trigger: 'blur' }
      ],
      location: [
        { max: 100, message: '所在地不能超过100个字符', trigger: 'blur' }
      ]
    }

    // 监听用户信息变化，更新表单数据
    watch(
      () => props.userInfo,
      (newUserInfo) => {
        if (newUserInfo) {
          Object.keys(formData).forEach(key => {
            if (key === 'user_name') {
              formData[key] = newUserInfo.user_name || newUserInfo.name || ''
            } else if (newUserInfo[key] !== undefined) {
              formData[key] = newUserInfo[key]
            }
          })
        }
      },
      { immediate: true, deep: true }
    )

    // 处理表单提交
    const handleSubmit = async (values) => {
      try {
        emit('submit', values)
      } catch (error) {
        console.error('表单提交失败:', error)
      }
    }

    // 处理取消操作
    const handleCancel = () => {
      // 重置表单数据
      Object.keys(formData).forEach(key => {
        if (key === 'user_name') {
          formData[key] = props.userInfo.user_name || props.userInfo.name || ''
        } else if (props.userInfo[key] !== undefined) {
          formData[key] = props.userInfo[key]
        }
      })

      // 清除验证状态
      nextTick(() => {
        formRef.value?.clearValidate()
      })

      emit('cancel')
    }

    return {
      formRef,
      formData,
      formRules,
      handleSubmit,
      handleCancel
    }
  }
})
</script>

<style scoped>
.profile-edit-form {
  padding: 24px;
  background: #fff;
  border-radius: 6px;
}

.form-extra-text {
  color: #999;
  font-size: 12px;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 24px 0 16px 0;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left::before) {
  width: 5%;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-input:disabled),
:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #666;
}
</style>
