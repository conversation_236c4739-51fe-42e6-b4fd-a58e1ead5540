<template>
  <a-breadcrumb class="breadcrumb">
    <a-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="index">
      <router-link v-if="item.path && index < breadcrumbList.length - 1" :to="item.path">
        {{ item.title }}
      </router-link>
      <span v-else>{{ item.title }}</span>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script>
import { defineComponent, ref, watch } from 'vue'
import { useRoute } from 'vue-router'

export default defineComponent({
  name: 'BreadCrumb',
  setup () {
    const route = useRoute()
    const breadcrumbList = ref([])

    // 路由与菜单的映射
    const routeMap = {
      '/dashboard': { title: '仪表盘' },

      '/enterprise/list': { title: '企业管理', parent: '/dashboard' },
      '/enterprise/create': { title: '添加企业', parent: '/enterprise/list' },
      '/enterprise/detail/:id': { title: '企业详情', parent: '/enterprise/list' },
      '/enterprise/edit/:id': { title: '编辑企业', parent: '/enterprise/detail/:id' },

      // Keep old paths as they'll be redirected
      '/enterprises': { title: '企业管理', parent: '/dashboard' },
      '/enterprises/add': { title: '添加企业', parent: '/enterprises' },
      '/enterprises/:id': { title: '企业详情', parent: '/enterprises' },
      '/enterprises/:id/edit': { title: '编辑企业', parent: '/enterprises/:id' },

      '/invoice/list': { title: '发票管理', parent: '/dashboard' },
      '/invoice/upload': { title: '上传发票', parent: '/invoice/list' },
      '/invoice/scan': { title: '扫描识别', parent: '/invoice/list' },
      '/invoice/detail/:id': { title: '发票详情', parent: '/invoice/list' },

      // Keep old paths as they'll be redirected
      '/invoices': { title: '发票管理', parent: '/dashboard' },
      '/invoices/upload': { title: '上传发票', parent: '/invoices' },
      '/invoices/scan': { title: '扫描识别', parent: '/invoices' },
      '/invoices/:id': { title: '发票详情', parent: '/invoices' },

      '/declarations': { title: '申报管理', parent: '/dashboard' },
      '/declarations/create': { title: '创建申报', parent: '/declarations' },
      '/declarations/calendar': { title: '申报日历', parent: '/declarations' },
      '/declarations/:id': { title: '申报详情', parent: '/declarations' },

      '/tax-types': { title: '税种管理', parent: '/dashboard' },
      '/tax-calculator': { title: '税费计算器', parent: '/tax-types' },
      '/tax-types/:id': { title: '税种详情', parent: '/tax-types' },

      '/integration/list': { title: '系统集成', parent: '/dashboard' },
      '/integrations': { title: '系统集成', parent: '/dashboard' },
      '/users': { title: '用户管理', parent: '/dashboard' },
      '/settings': { title: '系统设置', parent: '/dashboard' },

      '/profile': { title: '个人中心', parent: '/dashboard' }
    }

    // 根据当前路由计算面包屑
    const generateBreadcrumb = (currentPath) => {
      const result = []
      let current = currentPath

      // 处理动态路由参数
      let matchedRoute = null
      for (const path in routeMap) {
        const dynamicSegments = path.match(/:[^/]+/g) || []
        if (dynamicSegments.length > 0) {
          const pattern = new RegExp(path.replace(/:[^/]+/g, '([^/]+)'))
          const match = currentPath.match(pattern)

          if (match) {
            matchedRoute = {
              path,
              params: dynamicSegments.reduce((acc, segment, index) => {
                acc[segment.slice(1)] = match[index + 1]
                return acc
              }, {})
            }
            break
          }
        } else if (path === currentPath) {
          matchedRoute = { path }
          break
        }
      }

      // 如果找到匹配的路由
      if (matchedRoute) {
        current = matchedRoute.path

        // 获取当前路径的面包屑配置
        const currentConfig = routeMap[current]
        if (currentConfig) {
          // 替换动态参数
          let title = currentConfig.title
          let path = current

          if (matchedRoute.params) {
            // 替换路径中的参数
            for (const key in matchedRoute.params) {
              path = path.replace(`:${key}`, matchedRoute.params[key])
            }

            // 如果是详情页，尝试从路由元数据获取名称
            if (title.includes('详情') && route.meta && route.meta.title) {
              title = route.meta.title
            }
          }

          result.unshift({ title, path })

          // 递归获取父路由的面包屑
          let parent = currentConfig.parent
          while (parent) {
            const parentConfig = routeMap[parent]
            if (parentConfig) {
              let parentPath = parent

              // 替换父路径中的参数
              if (matchedRoute.params) {
                for (const key in matchedRoute.params) {
                  parentPath = parentPath.replace(`:${key}`, matchedRoute.params[key])
                }
              }

              result.unshift({ title: parentConfig.title, path: parentPath })
              parent = parentConfig.parent
            } else {
              break
            }
          }
        }
      }

      return result
    }

    // 更新面包屑
    const updateBreadcrumb = () => {
      breadcrumbList.value = generateBreadcrumb(route.path)
    }

    // 监听路由变化
    watch(() => route.path, () => {
      updateBreadcrumb()
    }, { immediate: true })

    // 监听路由元数据变化
    watch(() => route.meta, () => {
      updateBreadcrumb()
    }, { deep: true })

    return {
      breadcrumbList
    }
  }
})
</script>

<style scoped>
.breadcrumb {
  margin-left: 8px;
}
</style>
