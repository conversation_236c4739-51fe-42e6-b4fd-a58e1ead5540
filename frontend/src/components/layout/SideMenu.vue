<template>
  <div class="macos-side-menu">
    <a-menu
      mode="inline"
      :selected-keys="selectedKeys"
      :open-keys="openKeys"
      :inline-collapsed="collapsed"
      class="macos-menu"
      :theme="'light'"
      @select="onSelect"
      @open-change="onOpenChange"
    >
      <template v-for="menu in menuList" :key="menu.key">
        <!-- 有子菜单的情况 -->
        <template v-if="menu.children && menu.children.length > 0">
          <a-sub-menu :key="menu.key" class="macos-submenu">
            <template #title>
              <div class="menu-item-content">
                <component :is="menu.icon" class="menu-icon" />
                <span v-if="!collapsed" class="menu-title">{{ menu.title }}</span>
              </div>
            </template>
            <a-menu-item
              v-for="child in menu.children"
              :key="child.key"
              class="macos-menu-item"
            >
              <router-link :to="child.path" class="menu-link">
                <component :is="child.icon" v-if="child.icon" class="menu-icon" />
                <span class="menu-title">{{ child.title }}</span>
              </router-link>
            </a-menu-item>
          </a-sub-menu>
        </template>

        <!-- 没有子菜单的情况 -->
        <template v-else>
          <a-menu-item :key="menu.key" class="macos-menu-item">
            <router-link :to="menu.path" class="menu-link">
              <div class="menu-item-content">
                <component :is="menu.icon" class="menu-icon" />
                <span v-if="!collapsed" class="menu-title">{{ menu.title }}</span>
              </div>
            </router-link>
          </a-menu-item>
        </template>
      </template>
    </a-menu>
  </div>
</template>

<script>
import { defineComponent, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import {
  DashboardOutlined,
  BankOutlined,
  FileTextOutlined,
  AuditOutlined,
  SettingOutlined,
  TeamOutlined,
  ApiOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'SideMenu',
  components: {
    DashboardOutlined,
    BankOutlined,
    FileTextOutlined,
    AuditOutlined,
    SettingOutlined,
    TeamOutlined,
    ApiOutlined,
    BarChartOutlined
  },
  props: {
    collapsed: {
      type: Boolean,
      default: false
    }
  },
  setup (props) {
    const route = useRoute()
    const selectedKeys = ref([])
    const openKeys = ref([])

    // 定义菜单数据
    const menuList = [
      {
        key: 'dashboard',
        title: '仪表盘',
        icon: 'DashboardOutlined',
        path: '/dashboard'
      },
      {
        key: 'enterprise',
        title: '企业管理',
        icon: 'BankOutlined',
        children: [
          {
            key: 'enterprise-list',
            title: '企业列表',
            path: '/enterprise/list'
          },
          {
            key: 'enterprise-add',
            title: '添加企业',
            path: '/enterprise/create'
          }
        ]
      },
      {
        key: 'invoice',
        title: '发票管理',
        icon: 'FileTextOutlined',
        children: [
          {
            key: 'invoice-list',
            title: '发票列表',
            path: '/invoice/list'
          },
          {
            key: 'invoice-upload',
            title: '上传发票',
            path: '/invoice/upload'
          },
          {
            key: 'invoice-scan',
            title: '扫描识别',
            path: '/invoice/scan'
          }
        ]
      },
      {
        key: 'declaration',
        title: '申报管理',
        icon: 'AuditOutlined',
        children: [
          {
            key: 'declaration-list',
            title: '申报列表',
            path: '/declaration/list'
          },
          {
            key: 'declaration-create',
            title: '创建申报',
            path: '/declaration/create'
          },
          {
            key: 'declaration-calendar',
            title: '申报日历',
            path: '/declaration/calendar'
          }
        ]
      },
      {
        key: 'tax',
        title: '税种管理',
        icon: 'BarChartOutlined',
        children: [
          {
            key: 'tax-type-list',
            title: '税种列表',
            path: '/tax-types'
          },
          {
            key: 'tax-calculator',
            title: '税费计算器',
            path: '/tax-calculator'
          }
        ]
      },
      {
        key: 'integration',
        title: '系统集成',
        icon: 'ApiOutlined',
        path: '/integration/list'
      },
      {
        key: 'user',
        title: '用户管理',
        icon: 'TeamOutlined',
        path: '/users'
      },
      {
        key: 'settings',
        title: '系统设置',
        icon: 'SettingOutlined',
        path: '/settings'
      }
    ]

    // 根据当前路径计算选中的菜单
    const updateSelectedKeys = () => {
      const path = route.path

      // 查找匹配当前路径的菜单项
      const findMenuKey = (menus) => {
        for (const menu of menus) {
          // 查找当前菜单
          if (menu.path === path) {
            return menu.key
          }

          // 查找子菜单
          if (menu.children && menu.children.length > 0) {
            const childKey = findMenuKey(menu.children)
            if (childKey) {
              // 同时更新openKeys，打开父菜单
              if (!openKeys.value.includes(menu.key) && !props.collapsed) {
                openKeys.value.push(menu.key)
              }
              return childKey
            }
          }
        }
        return null
      }

      const activeKey = findMenuKey(menuList)
      if (activeKey) {
        selectedKeys.value = [activeKey]
      }
    }

    // 菜单选择事件
    const onSelect = ({ key }) => {
      selectedKeys.value = [key]
    }

    // 展开/收起子菜单事件
    const onOpenChange = (keys) => {
      openKeys.value = keys
    }

    // 监听路由变化
    watch(() => route.path, () => {
      updateSelectedKeys()
    })

    // 监听折叠状态变化
    watch(() => props.collapsed, (collapsed) => {
      openKeys.value = collapsed ? [] : openKeys.value
    })

    // 初始化选中菜单
    updateSelectedKeys()

    return {
      menuList,
      selectedKeys,
      openKeys,
      onSelect,
      onOpenChange
    }
  }
})
</script>

<style scoped>
/* macOS 风格侧边栏菜单 */
.macos-side-menu {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 菜单容器 */
.macos-menu {
  background: transparent;
  border: none;

  /* 菜单项基础样式 */
  :deep(.ant-menu-item) {
    border-radius: var(--radius-base);
    margin: var(--spacing-xs) 0;
    padding: 0;
    height: 40px;
    line-height: 40px;
    transition: all var(--transition-fast);
    border: none;

    &:hover {
      background: rgba(0, 122, 255, 0.08);
      color: var(--color-primary);
    }

    &.ant-menu-item-selected {
      background: var(--color-primary);
      color: white;

      &::after {
        display: none;
      }

      .menu-icon {
        color: white;
      }
    }
  }

  /* 子菜单样式 */
  :deep(.ant-menu-submenu) {
    .ant-menu-submenu-title {
      border-radius: var(--radius-base);
      margin: var(--spacing-xs) 0;
      padding: 0 var(--spacing-md);
      height: 40px;
      line-height: 40px;
      transition: all var(--transition-fast);

      &:hover {
        background: rgba(0, 122, 255, 0.08);
        color: var(--color-primary);
      }
    }

    .ant-menu-sub {
      background: transparent;

      .ant-menu-item {
        padding-left: var(--spacing-xl);

        &:hover {
          background: rgba(0, 122, 255, 0.05);
        }

        &.ant-menu-item-selected {
          background: rgba(0, 122, 255, 0.15);
          color: var(--color-primary);

          .menu-icon {
            color: var(--color-primary);
          }
        }
      }
    }
  }
}

/* 菜单项内容 */
.menu-item-content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 var(--spacing-md);
}

.menu-link {
  display: flex;
  align-items: center;
  width: 100%;
  color: inherit;
  text-decoration: none;
  padding: 0 var(--spacing-md);

  &:hover {
    color: inherit;
  }
}

.menu-icon {
  font-size: 16px;
  min-width: 16px;
  transition: all var(--transition-fast);
}

.menu-title {
  margin-left: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  white-space: nowrap;
  overflow: hidden;
}

/* 折叠状态样式 */
.macos-menu.ant-menu-inline-collapsed {
  :deep(.ant-menu-item),
  :deep(.ant-menu-submenu-title) {
    padding: 0;
    text-align: center;

    .menu-item-content {
      justify-content: center;
      padding: 0;
    }

    .menu-link {
      justify-content: center;
      padding: 0;
    }
  }

  :deep(.ant-menu-submenu-arrow) {
    display: none;
  }
}

/* 滚动条样式 */
.macos-side-menu::-webkit-scrollbar {
  width: 4px;
}

.macos-side-menu::-webkit-scrollbar-track {
  background: transparent;
}

.macos-side-menu::-webkit-scrollbar-thumb {
  background: var(--color-bg-quaternary);
  border-radius: 2px;

  &:hover {
    background: var(--color-text-tertiary);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .macos-side-menu {
    width: 100%;
  }

  .menu-title {
    font-size: var(--font-size-xs);
  }
}
</style>
