<template>
  <a-layout class="macos-layout-container">
    <!-- macOS 风格顶部 Header -->
    <a-layout-header class="macos-header glass-effect">
      <div class="macos-header-content">
        <div class="logo-container">
          <img src="@/assets/logo.png" alt="Logo" class="logo">
          <h1 class="site-title">
            税易通
          </h1>
        </div>

        <!-- macOS 风格用户菜单 -->
        <div class="header-right">
          <a-dropdown placement="bottomRight" :trigger="['click']">
            <a class="user-dropdown macos-user-menu">
              <a-avatar
                :size="32"
                :src="getAvatarUrl(userStore.userAvatar)"
                :style="{ backgroundColor: 'var(--color-primary)' }"
              >
                <template v-if="!userStore.userAvatar" #icon>
                  <UserOutlined />
                </template>
              </a-avatar>
              <span class="username">{{ username }}</span>
            </a>
            <template #overlay>
              <a-menu class="macos-dropdown-menu">
                <a-menu-item key="profile" @click="handleProfile">
                  <UserOutlined />
                  个人信息
                </a-menu-item>
                <a-menu-item key="settings" @click="handleSettings">
                  <SettingOutlined />
                  账户设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" class="logout-item" @click="handleLogout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </a-layout-header>

    <a-layout class="macos-main-layout">
      <!-- macOS 风格侧边栏 -->
      <a-layout-sider
        v-model:collapsed="collapsed"
        :trigger="null"
        :width="280"
        :collapsed-width="64"
        collapsible
        class="macos-sidebar"
      >
        <div class="sidebar-content">
          <!-- 侧边栏搜索框 -->
          <div v-if="!collapsed" class="sidebar-search">
            <a-input
              v-model:value="searchKeyword"
              placeholder="搜索功能..."
              class="macos-search-input"
              :bordered="false"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
          </div>

          <!-- 导航菜单 -->
          <SideMenu :collapsed="collapsed" class="macos-menu" />
        </div>
      </a-layout-sider>

      <!-- 主内容区域 -->
      <a-layout class="macos-content-layout">
        <!-- 内容区域 Header -->
        <a-layout-header class="macos-content-header glass-effect">
          <div class="content-header-left">
            <a-button
              class="macos-trigger-button"
              type="text"
              :icon="collapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
              @click="toggleSidebar"
            />
            <BreadCrumb />
          </div>
        </a-layout-header>

        <!-- 主内容 -->
        <a-layout-content class="macos-content">
          <div class="content-wrapper">
            <router-view />
          </div>
        </a-layout-content>

        <!-- Footer -->
        <a-layout-footer class="macos-footer">
          <div class="footer-content">
            税易通 ©{{ new Date().getFullYear() }} 版权所有
          </div>
        </a-layout-footer>
      </a-layout>
    </a-layout>
  </a-layout>
</template>

<script>
import { defineComponent, ref, computed, h, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'

import { useUserStore } from '@/store/user'
import { useAppStore } from '@/store/app'
import { getAvatarUrl } from '@/utils/constants'
import SideMenu from './SideMenu.vue'
import BreadCrumb from './BreadCrumb.vue'

export default defineComponent({
  name: 'MacOSMainLayout',
  components: {
    SideMenu,
    BreadCrumb,
    UserOutlined,
    SettingOutlined,
    LogoutOutlined,
    SearchOutlined
  },
  setup () {
    const router = useRouter()
    const userStore = useUserStore()
    const appStore = useAppStore()

    // 响应式状态
    const collapsed = ref(appStore.sidebar.collapsed)
    const searchKeyword = ref('')

    // 计算属性
    const username = computed(() => {
      return userStore.userInfo?.name || '用户'
    })

    // 方法
    const toggleSidebar = () => {
      collapsed.value = !collapsed.value
      appStore.toggleSidebar()
    }

    const handleProfile = () => {
      router.push('/user/profile')
    }

    const handleSettings = () => {
      router.push('/user/settings')
    }

    const handleLogout = async () => {
      try {
        await userStore.logout()
        message.success('已退出登录')
        router.push('/login')
      } catch (error) {
        console.error('退出登录失败:', error)
        message.error('退出登录失败')
      }
    }

    // 初始化用户信息
    const initUserInfo = async () => {
      if (userStore.token && !userStore.userInfo) {
        try {
          await userStore.fetchUserInfo()
        } catch (error) {
          console.error('获取用户信息失败:', error)
          // 如果获取用户信息失败，可能token已过期，跳转到登录页
          if (error.response?.status === 401) {
            await userStore.logout()
            router.push('/login')
          }
        }
      }
    }

    // 组件挂载时初始化用户信息
    onMounted(() => {
      initUserInfo()
    })

    return {
      h,
      collapsed,
      searchKeyword,
      username,
      userStore,
      toggleSidebar,
      handleProfile,
      handleSettings,
      handleLogout,
      getAvatarUrl,
      MenuFoldOutlined,
      MenuUnfoldOutlined
    }
  }
})
</script>

<style scoped>
/* macOS Aqua UI 风格布局样式 */
.macos-layout-container {
  min-height: 100vh;
  background: var(--color-bg-secondary);
  font-family: var(--font-family-base);
}

/* 顶部 Header - 毛玻璃效果 */
.macos-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  z-index: 1000;
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0;
}

.macos-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 var(--spacing-lg);
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  height: 28px;
  margin-right: var(--spacing-sm);
}

.site-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

/* 用户菜单 */
.header-right {
  display: flex;
  align-items: center;
}

.macos-user-menu {
  display: flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-base);
  transition: all var(--transition-fast);
  color: var(--color-text-primary);
  text-decoration: none;

  &:hover {
    background: rgba(0, 122, 255, 0.1);
    color: var(--color-primary);
  }
}

.username {
  margin-left: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
}

/* 下拉菜单 */
.macos-dropdown-menu {
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-heavy);
  border: none;
  overflow: hidden;

  :deep(.ant-menu-item) {
    border-radius: 0;
    margin: 0;

    &:hover {
      background: var(--color-bg-secondary);
    }

    &.logout-item:hover {
      background: rgba(255, 59, 48, 0.1);
      color: var(--color-danger);
    }
  }
}

/* 主布局 */
.macos-main-layout {
  margin-top: 60px;
  min-height: calc(100vh - 60px);
}

/* 侧边栏 */
.macos-sidebar {
  background: var(--color-bg-primary);
  border-right: 1px solid var(--color-bg-quaternary);
  box-shadow: var(--shadow-light);

  :deep(.ant-layout-sider-children) {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--spacing-md);
}

.sidebar-search {
  margin-bottom: var(--spacing-md);
}

.macos-search-input {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-base);

  :deep(.ant-input) {
    background: transparent;
    border: none;
    box-shadow: none;

    &:focus {
      box-shadow: none;
    }
  }
}

/* 内容区域 */
.macos-content-layout {
  background: transparent;
}

.macos-content-header {
  height: 48px;
  padding: 0 var(--spacing-lg);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.7);
}

.content-header-left {
  display: flex;
  align-items: center;
  height: 100%;
}

.macos-trigger-button {
  margin-right: var(--spacing-md);
  border-radius: var(--radius-base);

  &:hover {
    background: rgba(0, 122, 255, 0.1);
    color: var(--color-primary);
  }
}

.macos-content {
  padding: var(--spacing-lg);
  min-height: calc(100vh - 60px - 48px - 60px);
}

.content-wrapper {
  background: var(--color-bg-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-card);
  padding: var(--spacing-lg);
  min-height: 100%;
}

/* Footer */
.macos-footer {
  background: transparent;
  border-top: 1px solid var(--color-bg-quaternary);
  padding: var(--spacing-md) var(--spacing-lg);
}

.footer-content {
  text-align: center;
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .macos-header-content {
    padding: 0 var(--spacing-md);
  }

  .site-title {
    font-size: var(--font-size-base);
  }

  .username {
    display: none;
  }

  .macos-content {
    padding: var(--spacing-md);
  }

  .content-wrapper {
    padding: var(--spacing-md);
    border-radius: var(--radius-base);
  }
}
</style>
