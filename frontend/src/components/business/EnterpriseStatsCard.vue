<template>
  <a-card
    class="enterprise-stats-card"
    :bordered="false"
    :loading="loading"
  >
    <template #title>
      <span class="stats-title">企业统计</span>
    </template>

    <a-row :gutter="16">
      <a-col :span="6">
        <a-statistic
          title="企业总数"
          :value="stats.total"
          suffix="家"
          :value-style="{ color: '#1890ff' }"
        >
          <template #prefix>
            <BankOutlined />
          </template>
        </a-statistic>
      </a-col>

      <a-col :span="6">
        <a-statistic
          title="正常运营"
          :value="stats.active"
          suffix="家"
          :value-style="{ color: '#52c41a' }"
        >
          <template #prefix>
            <CheckCircleOutlined />
          </template>
        </a-statistic>
      </a-col>

      <a-col :span="6">
        <a-statistic
          title="待审核"
          :value="stats.pending"
          suffix="家"
          :value-style="{ color: '#faad14' }"
        >
          <template #prefix>
            <ClockCircleOutlined />
          </template>
        </a-statistic>
      </a-col>

      <a-col :span="6">
        <a-statistic
          title="本月新增"
          :value="stats.thisMonth"
          suffix="家"
          :value-style="{ color: '#722ed1' }"
        >
          <template #prefix>
            <PlusCircleOutlined />
          </template>
        </a-statistic>
      </a-col>
    </a-row>

    <!-- 趋势图表区域 -->
    <div v-if="showChart" class="stats-chart">
      <a-divider />
      <div class="chart-container">
        <span class="chart-placeholder">
          📊 企业增长趋势图表（可接入图表组件）
        </span>
      </div>
    </div>
  </a-card>
</template>

<script>
import { defineComponent, computed } from 'vue'
import {
  BankOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  PlusCircleOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'EnterpriseStatsCard',
  components: {
    BankOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    PlusCircleOutlined
  },
  props: {
    stats: {
      type: Object,
      default: () => ({
        total: 0,
        active: 0,
        pending: 0,
        thisMonth: 0
      }),
      validator: (value) => {
        return typeof value === 'object' &&
          typeof value.total === 'number' &&
          typeof value.active === 'number' &&
          typeof value.pending === 'number' &&
          typeof value.thisMonth === 'number'
      }
    },
    loading: {
      type: Boolean,
      default: false
    },
    showChart: {
      type: Boolean,
      default: false
    }
  },
  emits: ['refresh'],
  setup (props, { emit }) {
    // 计算活跃率
    const activeRate = computed(() => {
      if (props.stats.total === 0) return 0
      return Math.round((props.stats.active / props.stats.total) * 100)
    })

    // 计算待审核率
    const pendingRate = computed(() => {
      if (props.stats.total === 0) return 0
      return Math.round((props.stats.pending / props.stats.total) * 100)
    })

    // 计算增长率（需要历史数据）
    const growthRate = computed(() => {
      // 这里可以根据实际需求计算增长率
      return 0
    })

    const handleRefresh = () => {
      emit('refresh')
    }

    return {
      activeRate,
      pendingRate,
      growthRate,
      handleRefresh
    }
  }
})
</script>

<style scoped>
.enterprise-stats-card {
  margin-bottom: 16px;
}

.stats-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.stats-chart {
  margin-top: 16px;
}

.chart-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
}

.chart-placeholder {
  color: #8c8c8c;
  font-size: 14px;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 500;
}

:deep(.ant-statistic-content-prefix) {
  margin-right: 8px;
  font-size: 20px;
}
</style>
