<template>
  <div class="invoice-item-editor">
    <a-card title="发票明细管理" :bordered="false">
      <!-- 操作按钮 -->
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAddItem">
            <template #icon><PlusOutlined /></template>
            添加明细
          </a-button>
          <a-button @click="handleBatchDelete" :disabled="selectedRowKeys.length === 0">
            <template #icon><DeleteOutlined /></template>
            批量删除
          </a-button>
          <a-button @click="handleSaveAll" :loading="saving">
            <template #icon><SaveOutlined /></template>
            保存全部
          </a-button>
        </a-space>
      </template>

      <!-- 明细表格 -->
      <a-table
        :columns="columns"
        :data-source="items"
        :loading="loading"
        :pagination="false"
        :row-selection="rowSelection"
        :scroll="{ x: 1200 }"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, record, index }">
          <!-- 商品名称 -->
          <template v-if="column.key === 'item_name'">
            <a-input
              v-model:value="record.item_name"
              placeholder="请输入商品名称"
              @change="handleItemChange(index)"
            />
          </template>

          <!-- 规格型号 -->
          <template v-else-if="column.key === 'specification'">
            <a-input
              v-model:value="record.specification"
              placeholder="规格型号"
              @change="handleItemChange(index)"
            />
          </template>

          <!-- 单位 -->
          <template v-else-if="column.key === 'unit'">
            <a-select
              v-model:value="record.unit"
              placeholder="单位"
              style="width: 80px"
              @change="handleItemChange(index)"
            >
              <a-select-option
                v-for="unit in COMMON_UNITS"
                :key="unit.value"
                :value="unit.value"
              >
                {{ unit.label }}
              </a-select-option>
            </a-select>
          </template>

          <!-- 数量 -->
          <template v-else-if="column.key === 'quantity'">
            <a-input-number
              v-model:value="record.quantity"
              :min="0"
              :precision="4"
              style="width: 100px"
              @change="handleItemChange(index)"
            />
          </template>

          <!-- 单价 -->
          <template v-else-if="column.key === 'unit_price'">
            <a-input-number
              v-model:value="record.unit_price"
              :min="0"
              :precision="6"
              style="width: 120px"
              @change="handleItemChange(index)"
            />
          </template>

          <!-- 税率 -->
          <template v-else-if="column.key === 'tax_rate'">
            <a-select
              v-model:value="record.tax_rate"
              style="width: 80px"
              @change="handleItemChange(index)"
            >
              <a-select-option
                v-for="rate in COMMON_TAX_RATES"
                :key="rate.value"
                :value="rate.value"
              >
                {{ rate.label }}
              </a-select-option>
            </a-select>
          </template>

          <!-- 金额 -->
          <template v-else-if="column.key === 'amount'">
            <span>¥{{ formatNumber(record.amount) }}</span>
          </template>

          <!-- 税额 -->
          <template v-else-if="column.key === 'tax_amount'">
            <span>¥{{ formatNumber(record.tax_amount) }}</span>
          </template>

          <!-- 合计 -->
          <template v-else-if="column.key === 'total_amount'">
            <span>¥{{ formatNumber(record.total_amount) }}</span>
          </template>

          <!-- 备注 -->
          <template v-else-if="column.key === 'remarks'">
            <a-input
              v-model:value="record.remarks"
              placeholder="备注"
              @change="handleItemChange(index)"
            />
          </template>

          <!-- 操作 -->
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button
                type="link"
                size="small"
                danger
                @click="handleDeleteItem(index)"
              >
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 汇总信息 -->
      <div class="summary-info" style="margin-top: 16px; padding: 16px; background: #fafafa; border-radius: 6px;">
        <a-row :gutter="24">
          <a-col :span="6">
            <a-statistic title="明细数量" :value="items.length" suffix="项" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="金额合计" :value="totalAmount" prefix="¥" :precision="2" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="税额合计" :value="totalTaxAmount" prefix="¥" :precision="2" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="价税合计" :value="grandTotal" prefix="¥" :precision="2" />
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, DeleteOutlined, SaveOutlined } from '@ant-design/icons-vue'
import {
  getInvoiceItems,
  batchCreateInvoiceItems,
  batchUpdateInvoiceItems,
  calculateInvoiceItemAmounts,
  validateInvoiceItem,
  COMMON_UNITS,
  COMMON_TAX_RATES
} from '@/api/invoice'
import { formatNumber } from '@/utils/format'

export default defineComponent({
  name: 'InvoiceItemEditor',
  components: {
    PlusOutlined,
    DeleteOutlined,
    SaveOutlined
  },
  props: {
    invoiceId: {
      type: String,
      required: true
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  emits: ['change', 'save'],
  setup (props, { emit }) {
    const loading = ref(false)
    const saving = ref(false)
    const items = ref([])
    const selectedRowKeys = ref([])

    // 表格列定义
    const columns = [
      { title: '商品名称', dataIndex: 'item_name', key: 'item_name', width: 200, fixed: 'left' },
      { title: '规格型号', dataIndex: 'specification', key: 'specification', width: 120 },
      { title: '单位', dataIndex: 'unit', key: 'unit', width: 80 },
      { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100 },
      { title: '单价', dataIndex: 'unit_price', key: 'unit_price', width: 120 },
      { title: '税率', dataIndex: 'tax_rate', key: 'tax_rate', width: 80 },
      { title: '金额', dataIndex: 'amount', key: 'amount', width: 100 },
      { title: '税额', dataIndex: 'tax_amount', key: 'tax_amount', width: 100 },
      { title: '合计', dataIndex: 'total_amount', key: 'total_amount', width: 100 },
      { title: '备注', dataIndex: 'remarks', key: 'remarks', width: 120 },
      { title: '操作', key: 'action', width: 80, fixed: 'right' }
    ]

    // 行选择配置
    const rowSelection = computed(() => ({
      selectedRowKeys: selectedRowKeys.value,
      onChange: (keys) => {
        selectedRowKeys.value = keys
      }
    }))

    // 计算汇总信息
    const totalAmount = computed(() => {
      return items.value.reduce((sum, item) => sum + (item.amount || 0), 0)
    })

    const totalTaxAmount = computed(() => {
      return items.value.reduce((sum, item) => sum + (item.tax_amount || 0), 0)
    })

    const grandTotal = computed(() => {
      return items.value.reduce((sum, item) => sum + (item.total_amount || 0), 0)
    })

    // 加载发票明细
    const loadItems = async () => {
      if (!props.invoiceId) return

      try {
        loading.value = true
        const response = await getInvoiceItems(props.invoiceId)
        if (response.code === 200) {
          items.value = response.data.map((item, index) => ({
            ...item,
            key: item.id || `temp_${index}`,
            isNew: !item.id
          }))
        }
      } catch (error) {
        console.error('加载发票明细失败:', error)
        message.error('加载发票明细失败')
      } finally {
        loading.value = false
      }
    }

    // 添加明细项
    const handleAddItem = () => {
      const newItem = {
        key: `temp_${Date.now()}`,
        invoice_id: props.invoiceId,
        item_name: '',
        specification: '',
        unit: '件',
        quantity: 1,
        unit_price: 0,
        tax_rate: 0.13,
        amount: 0,
        tax_amount: 0,
        total_amount: 0,
        remarks: '',
        isNew: true
      }
      items.value.push(newItem)
    }

    // 删除明细项
    const handleDeleteItem = (index) => {
      items.value.splice(index, 1)
      emit('change', items.value)
    }

    // 批量删除
    const handleBatchDelete = () => {
      const indicesToDelete = []
      selectedRowKeys.value.forEach(key => {
        const index = items.value.findIndex(item => item.key === key)
        if (index !== -1) {
          indicesToDelete.push(index)
        }
      })

      indicesToDelete.sort((a, b) => b - a).forEach(index => {
        items.value.splice(index, 1)
      })

      selectedRowKeys.value = []
      emit('change', items.value)
    }

    // 明细项变化处理
    const handleItemChange = (index) => {
      const item = items.value[index]
      if (item.quantity && item.unit_price && item.tax_rate !== undefined) {
        const amounts = calculateInvoiceItemAmounts(item.quantity, item.unit_price, item.tax_rate)
        item.amount = amounts.amount
        item.tax_amount = amounts.taxAmount
        item.total_amount = amounts.totalAmount
      }
      emit('change', items.value)
    }

    // 保存全部
    const handleSaveAll = async () => {
      for (let i = 0; i < items.value.length; i++) {
        const validation = validateInvoiceItem(items.value[i])
        if (!validation.isValid) {
          message.error(`第${i + 1}行：${validation.errors.join(', ')}`)
          return
        }
      }

      try {
        saving.value = true
        
        const newItems = items.value.filter(item => item.isNew)
        const existingItems = items.value.filter(item => !item.isNew)

        if (newItems.length > 0) {
          await batchCreateInvoiceItems(props.invoiceId, newItems)
        }

        if (existingItems.length > 0) {
          await batchUpdateInvoiceItems(props.invoiceId, existingItems)
        }

        message.success('保存成功')
        emit('save', items.value)
        await loadItems()
      } catch (error) {
        console.error('保存失败:', error)
        message.error('保存失败')
      } finally {
        saving.value = false
      }
    }

    watch(() => props.invoiceId, loadItems, { immediate: true })

    return {
      loading,
      saving,
      items,
      selectedRowKeys,
      columns,
      rowSelection,
      totalAmount,
      totalTaxAmount,
      grandTotal,
      COMMON_UNITS,
      COMMON_TAX_RATES,
      formatNumber,
      handleAddItem,
      handleDeleteItem,
      handleBatchDelete,
      handleItemChange,
      handleSaveAll
    }
  }
})
</script>

<style scoped>
.invoice-item-editor {
  .summary-info {
    border: 1px solid #d9d9d9;
  }
}
</style>
