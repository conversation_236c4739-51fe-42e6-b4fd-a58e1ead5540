<template>
  <a-card :bordered="false" class="search-form-card">
    <a-form layout="inline" :model="formData">
      <a-form-item label="关键词">
        <a-input
          v-model:value="formData.keyword"
          placeholder="发票号码、企业名称"
          allow-clear
          style="width: 200px"
          @press-enter="handleSearch"
        />
      </a-form-item>

      <a-form-item label="发票类型">
        <a-select
          v-model:value="formData.type"
          placeholder="请选择类型"
          allow-clear
          style="width: 120px"
        >
          <a-select-option value="input">进项</a-select-option>
          <a-select-option value="output">销项</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="状态">
        <a-select
          v-model:value="formData.status"
          placeholder="请选择状态"
          allow-clear
          style="width: 120px"
        >
          <a-select-option value="normal">正常</a-select-option>
          <a-select-option value="verified">已核验</a-select-option>
          <a-select-option value="cancelled">已作废</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="企业">
        <a-select
          v-model:value="formData.enterprise_id"
          placeholder="请选择企业"
          allow-clear
          show-search
          :filter-option="filterEnterpriseOption"
          style="width: 200px"
          :loading="enterpriseLoading"
        >
          <a-select-option
            v-for="enterprise in enterpriseList"
            :key="enterprise.id"
            :value="enterprise.id"
          >
            {{ enterprise.name }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="开票日期">
        <a-range-picker
          v-model:value="formData.dateRange"
          style="width: 240px"
          :placeholder="['开始日期', '结束日期']"
        />
      </a-form-item>

      <a-form-item>
        <a-space>
          <a-button type="primary" @click="handleSearch" :loading="loading">
            <SearchOutlined />
            搜索
          </a-button>
          <a-button @click="handleReset">
            <ReloadOutlined />
            重置
          </a-button>
          <a-button type="link" @click="toggleAdvanced">
            {{ showAdvanced ? '收起' : '展开' }}
            <DownOutlined :class="{ 'rotate-180': showAdvanced }" />
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>

    <!-- 高级搜索 -->
    <div v-show="showAdvanced" class="advanced-search">
      <a-divider />
      <a-form layout="inline" :model="formData">
        <a-form-item label="金额范围">
          <a-input-group compact>
            <a-input-number
              v-model:value="formData.minAmount"
              placeholder="最小金额"
              :min="0"
              :precision="2"
              style="width: 120px"
            />
            <a-input
              style="width: 30px; text-align: center; pointer-events: none"
              placeholder="~"
              disabled
            />
            <a-input-number
              v-model:value="formData.maxAmount"
              placeholder="最大金额"
              :min="0"
              :precision="2"
              style="width: 120px"
            />
          </a-input-group>
        </a-form-item>

        <a-form-item label="税率">
          <a-select
            v-model:value="formData.taxRate"
            placeholder="请选择税率"
            allow-clear
            style="width: 120px"
          >
            <a-select-option :value="0">0%</a-select-option>
            <a-select-option :value="0.03">3%</a-select-option>
            <a-select-option :value="0.06">6%</a-select-option>
            <a-select-option :value="0.09">9%</a-select-option>
            <a-select-option :value="0.13">13%</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="认证状态">
          <a-select
            v-model:value="formData.authStatus"
            placeholder="请选择认证状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="authenticated">已认证</a-select-option>
            <a-select-option value="unauthenticated">未认证</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="申报状态">
          <a-select
            v-model:value="formData.declarationStatus"
            placeholder="请选择申报状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="declared">已申报</a-select-option>
            <a-select-option value="undeclared">未申报</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </div>
  </a-card>
</template>

<script>
import { defineComponent, ref, watch, onMounted } from 'vue'
import { SearchOutlined, ReloadOutlined, DownOutlined } from '@ant-design/icons-vue'
import { getEnterprises } from '@/api/enterprise'
import { useStandardList } from '@/composables/useStandardApi'

/**
 * @typedef {Object} InvoiceSearchFormData
 * @property {string} keyword - 关键词
 * @property {string} type - 发票类型
 * @property {string} status - 状态
 * @property {string} enterprise_id - 企业ID
 * @property {Array} dateRange - 日期范围
 * @property {number} minAmount - 最小金额
 * @property {number} maxAmount - 最大金额
 * @property {number} taxRate - 税率
 * @property {string} authStatus - 认证状态
 * @property {string} declarationStatus - 申报状态
 */

export default defineComponent({
  name: 'InvoiceSearchForm',
  components: {
    SearchOutlined,
    ReloadOutlined,
    DownOutlined
  },
  props: {
    /**
     * 表单数据
     * @type {InvoiceSearchFormData}
     */
    formData: {
      type: Object,
      required: true
    },
    /**
     * 加载状态
     */
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['search', 'reset', 'update:formData'],
  setup(props, { emit }) {
    // 响应式数据
    const showAdvanced = ref(false)

    // 获取企业列表
    const {
      loading: enterpriseLoading,
      list: enterpriseList,
      fetchList: fetchEnterprises
    } = useStandardList(getEnterprises, {
      immediate: true
    })

    // 方法
    const handleSearch = () => {
      emit('search')
    }

    const handleReset = () => {
      emit('reset')
    }

    const toggleAdvanced = () => {
      showAdvanced.value = !showAdvanced.value
    }

    /**
     * 企业选项过滤
     * @param {string} input - 输入值
     * @param {Object} option - 选项
     * @returns {boolean} - 是否匹配
     */
    const filterEnterpriseOption = (input, option) => {
      const enterprise = enterpriseList.value.find(e => e.id === option.value)
      if (!enterprise) return false
      
      return enterprise.name.toLowerCase().includes(input.toLowerCase()) ||
             enterprise.unified_social_credit_code?.includes(input)
    }

    // 监听表单数据变化，同步到父组件
    watch(
      () => props.formData,
      (newValue) => {
        emit('update:formData', newValue)
      },
      { deep: true }
    )

    // 生命周期
    onMounted(() => {
      // 获取企业列表用于下拉选择
      fetchEnterprises({ pageSize: 1000 }) // 获取所有企业
    })

    return {
      // 响应式数据
      showAdvanced,
      enterpriseLoading,
      enterpriseList,

      // 方法
      handleSearch,
      handleReset,
      toggleAdvanced,
      filterEnterpriseOption
    }
  }
})
</script>

<style scoped>
.search-form-card {
  margin-bottom: 16px;
}

.advanced-search {
  padding-top: 16px;
}

.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  width: 80px;
  text-align: right;
}

:deep(.ant-divider) {
  margin: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.ant-form-item) {
    margin-bottom: 8px;
  }
  
  :deep(.ant-form-item-label) {
    width: 60px;
  }
}
</style>
