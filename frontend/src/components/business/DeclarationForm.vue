<template>
  <a-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    layout="vertical"
    @finish="handleSubmit"
  >
    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="企业" name="enterprise_id">
          <a-select
            v-model:value="formData.enterprise_id"
            placeholder="请选择企业"
            show-search
            :filter-option="filterOption"
            :loading="enterpriseLoading"
          >
            <a-select-option
              v-for="enterprise in enterprises"
              :key="enterprise.id"
              :value="enterprise.id"
            >
              {{ enterprise.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-col>

      <a-col :span="12">
        <a-form-item label="税种" name="tax_type_id">
          <a-select
            v-model:value="formData.tax_type_id"
            placeholder="请选择税种"
            show-search
            :filter-option="filterOption"
            :loading="taxTypeLoading"
          >
            <a-select-option
              v-for="taxType in taxTypes"
              :key="taxType.id"
              :value="taxType.id"
            >
              {{ taxType.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>

    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="申报日期" name="declaration_date">
          <a-date-picker
            v-model:value="formData.declaration_date"
            style="width: 100%"
            placeholder="请选择申报日期"
          />
        </a-form-item>
      </a-col>

      <a-col :span="12">
        <a-form-item label="截止日期" name="due_date">
          <a-date-picker
            v-model:value="formData.due_date"
            style="width: 100%"
            placeholder="请选择截止日期"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <a-row :gutter="16">
      <a-col :span="8">
        <a-form-item label="期间类型" name="period_type">
          <a-select
            v-model:value="formData.period_type"
            placeholder="请选择期间类型"
            @change="handlePeriodTypeChange"
          >
            <a-select-option value="monthly">
              月报
            </a-select-option>
            <a-select-option value="quarterly">
              季报
            </a-select-option>
            <a-select-option value="annually">
              年报
            </a-select-option>
            <a-select-option value="other">
              其他
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-col>

      <a-col :span="8">
        <a-form-item label="年度" name="year">
          <a-input-number
            v-model:value="formData.year"
            :min="2020"
            :max="2030"
            style="width: 100%"
            placeholder="请输入年度"
          />
        </a-form-item>
      </a-col>

      <a-col :span="8">
        <a-form-item
          v-if="formData.period_type === 'monthly'"
          label="月份"
          name="month"
        >
          <a-input-number
            v-model:value="formData.month"
            :min="1"
            :max="12"
            style="width: 100%"
            placeholder="请输入月份"
          />
        </a-form-item>

        <a-form-item
          v-else-if="formData.period_type === 'quarterly'"
          label="季度"
          name="quarter"
        >
          <a-select
            v-model:value="formData.quarter"
            placeholder="请选择季度"
          >
            <a-select-option :value="1">
              第一季度
            </a-select-option>
            <a-select-option :value="2">
              第二季度
            </a-select-option>
            <a-select-option :value="3">
              第三季度
            </a-select-option>
            <a-select-option :value="4">
              第四季度
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>

    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="计税期间开始" name="tax_period_start">
          <a-date-picker
            v-model:value="formData.tax_period_start"
            style="width: 100%"
            placeholder="请选择计税期间开始日期"
          />
        </a-form-item>
      </a-col>

      <a-col :span="12">
        <a-form-item label="计税期间结束" name="tax_period_end">
          <a-date-picker
            v-model:value="formData.tax_period_end"
            style="width: 100%"
            placeholder="请选择计税期间结束日期"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="申报金额" name="amount">
          <a-input-number
            v-model:value="formData.amount"
            :min="0"
            :precision="2"
            style="width: 100%"
            placeholder="请输入申报金额"
          >
            <template #addonAfter>
              元
            </template>
          </a-input-number>
        </a-form-item>
      </a-col>

      <a-col v-if="isEdit" :span="12">
        <a-form-item label="申报状态" name="status">
          <a-select
            v-model:value="formData.status"
            placeholder="请选择申报状态"
          >
            <a-select-option value="draft">
              草稿
            </a-select-option>
            <a-select-option value="submitted">
              已提交
            </a-select-option>
            <a-select-option value="approved">
              已批准
            </a-select-option>
            <a-select-option value="rejected">
              已拒绝
            </a-select-option>
            <a-select-option value="paid">
              已缴费
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>

    <a-form-item>
      <a-space>
        <a-button type="primary" html-type="submit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}申报
        </a-button>
        <a-button @click="handleReset">
          重置
        </a-button>
        <a-button @click="handleCancel">
          取消
        </a-button>
      </a-space>
    </a-form-item>
  </a-form>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { useDeclarationStore } from '@/store/declaration'
import { getEnterprises } from '@/api/enterprise'
import { getTaxTypeList } from '@/api/taxType'
import dayjs from 'dayjs'

export default defineComponent({
  name: 'DeclarationForm',
  props: {
    declaration: {
      type: Object,
      default: null
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['success', 'cancel'],
  setup (props, { emit }) {
    const formRef = ref()
    const declarationStore = useDeclarationStore()

    const loading = ref(false)
    const enterpriseLoading = ref(false)
    const taxTypeLoading = ref(false)
    const enterprises = ref([])
    const taxTypes = ref([])

    const isEdit = computed(() => !!props.declaration?.id)

    // 表单数据
    const formData = reactive({
      enterprise_id: '',
      tax_type_id: '',
      declaration_date: null,
      due_date: null,
      period_type: 'monthly',
      year: new Date().getFullYear(),
      month: null,
      quarter: null,
      tax_period_start: null,
      tax_period_end: null,
      amount: 0,
      status: 'draft'
    })

    // 表单验证规则
    const rules = {
      enterprise_id: [
        { required: true, message: '请选择企业', trigger: 'change' }
      ],
      tax_type_id: [
        { required: true, message: '请选择税种', trigger: 'change' }
      ],
      declaration_date: [
        { required: true, message: '请选择申报日期', trigger: 'change' }
      ],
      due_date: [
        { required: true, message: '请选择截止日期', trigger: 'change' }
      ],
      period_type: [
        { required: true, message: '请选择期间类型', trigger: 'change' }
      ],
      year: [
        { required: true, message: '请输入年度', trigger: 'blur' }
      ],
      tax_period_start: [
        { required: true, message: '请选择计税期间开始日期', trigger: 'change' }
      ],
      tax_period_end: [
        { required: true, message: '请选择计税期间结束日期', trigger: 'change' }
      ]
    }

    // 获取企业列表
    const fetchEnterprises = async () => {
      try {
        enterpriseLoading.value = true
        const response = await getEnterprises({ pageSize: 1000 })
        if (response.code === 200) {
          enterprises.value = response.data.items || response.data.list || response.data.data || []
        }
      } catch (error) {
        console.error('获取企业列表失败:', error)
        message.error('获取企业列表失败')
      } finally {
        enterpriseLoading.value = false
      }
    }

    // 获取税种列表
    const fetchTaxTypes = async () => {
      try {
        taxTypeLoading.value = true
        const response = await getTaxTypeList({ pageSize: 1000 })
        if (response.code === 200) {
          taxTypes.value = response.data.items || response.data.list || response.data.data || []
        }
      } catch (error) {
        console.error('获取税种列表失败:', error)
        message.error('获取税种列表失败')
      } finally {
        taxTypeLoading.value = false
      }
    }

    // 过滤选项
    const filterOption = (input, option) => {
      return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }

    // 期间类型变化处理
    const handlePeriodTypeChange = (value) => {
      if (value !== 'monthly') {
        formData.month = null
      }
      if (value !== 'quarterly') {
        formData.quarter = null
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        loading.value = true

        const submitData = {
          ...formData,
          declaration_date: formData.declaration_date?.format('YYYY-MM-DD'),
          due_date: formData.due_date?.format('YYYY-MM-DD'),
          tax_period_start: formData.tax_period_start?.format('YYYY-MM-DD'),
          tax_period_end: formData.tax_period_end?.format('YYYY-MM-DD')
        }

        if (isEdit.value) {
          await declarationStore.updateDeclarationData(props.declaration.id, submitData)
        } else {
          await declarationStore.createNewDeclaration(submitData)
        }

        emit('success')
      } catch (error) {
        console.error('提交失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 重置表单
    const handleReset = () => {
      formRef.value?.resetFields()
      initFormData()
    }

    // 取消
    const handleCancel = () => {
      emit('cancel')
    }

    // 初始化表单数据
    const initFormData = () => {
      if (props.declaration) {
        // 编辑模式：回填数据
        Object.keys(formData).forEach(key => {
          if (props.declaration[key] !== undefined) {
            if (['declaration_date', 'due_date', 'tax_period_start', 'tax_period_end'].includes(key)) {
              formData[key] = props.declaration[key] ? dayjs(props.declaration[key]) : null
            } else {
              formData[key] = props.declaration[key]
            }
          }
        })
      } else {
        // 创建模式：重置为默认值
        Object.assign(formData, {
          enterprise_id: '',
          tax_type_id: '',
          declaration_date: null,
          due_date: null,
          period_type: 'monthly',
          year: new Date().getFullYear(),
          month: new Date().getMonth() + 1,
          quarter: null,
          tax_period_start: null,
          tax_period_end: null,
          amount: 0,
          status: 'draft'
        })
      }
    }

    // 监听props变化
    watch(() => props.declaration, initFormData, { immediate: true })

    // 组件挂载时获取数据
    onMounted(() => {
      fetchEnterprises()
      fetchTaxTypes()
    })

    return {
      formRef,
      formData,
      rules,
      loading,
      enterpriseLoading,
      taxTypeLoading,
      enterprises,
      taxTypes,
      isEdit,
      filterOption,
      handlePeriodTypeChange,
      handleSubmit,
      handleReset,
      handleCancel
    }
  }
})
</script>

<style scoped>
.ant-form {
  max-width: 800px;
}
</style>
