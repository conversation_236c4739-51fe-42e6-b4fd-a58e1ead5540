<template>
  <div class="invoice-integration">
    <!-- 发票税务汇总卡片 -->
    <a-card title="发票税务汇总" class="summary-card" :loading="summaryLoading">
      <template #extra>
        <a-space>
          <a-select
            v-model:value="selectedPeriod"
            style="width: 120px"
            placeholder="选择期间"
            @change="handlePeriodChange"
          >
            <a-select-option
              v-for="period in availablePeriods"
              :key="period.value"
              :value="period.value"
            >
              {{ period.label }}
            </a-select-option>
          </a-select>
          <a-button :loading="summaryLoading" @click="refreshSummary">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <a-row v-if="taxSummary" :gutter="16">
        <a-col :span="6">
          <a-statistic
            title="发票总数"
            :value="taxSummary.total_invoice_count"
            suffix="张"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="总金额"
            :value="taxSummary.total_amount"
            :precision="2"
            suffix="元"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="总税额"
            :value="taxSummary.total_tax"
            :precision="2"
            suffix="元"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="含税总额"
            :value="taxSummary.total_amount_with_tax"
            :precision="2"
            suffix="元"
          />
        </a-col>
      </a-row>

      <a-divider />

      <a-row v-if="taxSummary" :gutter="16">
        <a-col :span="8">
          <a-statistic
            title="已认证"
            :value="taxSummary.authenticated_count"
            suffix="张"
            :value-style="{ color: '#52c41a' }"
          />
        </a-col>
        <a-col :span="8">
          <a-statistic
            title="未申报"
            :value="taxSummary.undeclared_count"
            suffix="张"
            :value-style="{ color: '#fa8c16' }"
          />
        </a-col>
        <a-col :span="8">
          <a-statistic
            title="申报完成率"
            :value="declarationRate"
            suffix="%"
            :precision="1"
            :value-style="{ color: declarationRate >= 80 ? '#52c41a' : '#fa8c16' }"
          />
        </a-col>
      </a-row>
    </a-card>

    <!-- 按税种汇总表格 -->
    <a-card title="按税种汇总" class="tax-type-summary" style="margin-top: 16px;">
      <a-table
        :columns="taxTypeColumns"
        :data-source="taxTypeSummary"
        :pagination="false"
        size="small"
        :loading="summaryLoading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'tax_type_name'">
            <a-tag :color="getTaxTypeColor(record.tax_type_code)">
              {{ record.tax_type_name }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'total_amount'">
            ¥{{ formatNumber(record.total_amount) }}
          </template>
          <template v-else-if="column.key === 'total_tax'">
            ¥{{ formatNumber(record.total_tax) }}
          </template>
          <template v-else-if="column.key === 'average_tax_rate'">
            {{ (record.average_tax_rate * 100).toFixed(2) }}%
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 发票状态管理 -->
    <a-card title="发票状态管理" style="margin-top: 16px;">
      <a-space style="margin-bottom: 16px;">
        <a-button type="primary" @click="showInvoiceList">
          <template #icon>
            <UnorderedListOutlined />
          </template>
          查看发票列表
        </a-button>
        <a-button :disabled="!hasSelectedInvoices" @click="batchAuthenticate">
          <template #icon>
            <SafetyCertificateOutlined />
          </template>
          批量认证
        </a-button>
        <a-button :disabled="!hasSelectedInvoices" @click="batchDeclare">
          <template #icon>
            <FileTextOutlined />
          </template>
          批量申报
        </a-button>
      </a-space>

      <a-table
        :columns="invoiceColumns"
        :data-source="invoices"
        :pagination="pagination"
        :loading="invoiceLoading"
        :row-selection="rowSelection"
        size="small"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'authentication_status'">
            <a-tag :color="getStatusColor(record.authentication_status, 'auth')">
              {{ getStatusLabel(record.authentication_status, 'auth') }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'declaration_status'">
            <a-tag :color="getStatusColor(record.declaration_status, 'decl')">
              {{ getStatusLabel(record.declaration_status, 'decl') }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'total_amount_with_tax'">
            ¥{{ formatNumber(record.total_amount_with_tax) }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button
                type="link"
                size="small"
                :disabled="record.authentication_status === 'authenticated'"
                @click="updateAuthStatus(record)"
              >
                认证
              </a-button>
              <a-button
                type="link"
                size="small"
                :disabled="record.declaration_status === 'confirmed'"
                @click="updateDeclStatus(record)"
              >
                申报
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { useInvoiceStore } from '@/store/invoice'
import { formatNumber } from '@/utils/format'
import {
  ReloadOutlined,
  UnorderedListOutlined,
  SafetyCertificateOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'InvoiceIntegration',
  components: {
    ReloadOutlined,
    UnorderedListOutlined,
    SafetyCertificateOutlined,
    FileTextOutlined
  },
  props: {
    enterpriseId: {
      type: String,
      required: true
    },
    declarationPeriod: {
      type: String,
      default: ''
    }
  },
  emits: ['invoice-selected'],
  setup (props, { emit }) {
    const invoiceStore = useInvoiceStore()

    // 状态
    const summaryLoading = ref(false)
    const invoiceLoading = ref(false)
    const taxSummary = ref(null)
    const selectedPeriod = ref(props.declarationPeriod || getCurrentPeriod())
    const selectedRowKeys = ref([])

    // 可用期间选项
    const availablePeriods = ref([
      { value: getCurrentPeriod(), label: '本月' },
      { value: getLastPeriod(), label: '上月' },
      { value: getLastPeriod(2), label: '上上月' }
    ])

    // 计算属性
    const invoices = computed(() => invoiceStore.invoices)
    const pagination = computed(() => invoiceStore.pagination)

    const taxTypeSummary = computed(() => {
      return taxSummary.value?.tax_type_summary || []
    })

    const declarationRate = computed(() => {
      if (!taxSummary.value) return 0
      const total = taxSummary.value.total_invoice_count
      const undeclared = taxSummary.value.undeclared_count
      return total > 0 ? ((total - undeclared) / total) * 100 : 0
    })

    const hasSelectedInvoices = computed(() => {
      return selectedRowKeys.value.length > 0
    })

    // 表格列定义
    const taxTypeColumns = [
      { title: '税种', dataIndex: 'tax_type_name', key: 'tax_type_name' },
      { title: '发票数量', dataIndex: 'invoice_count', key: 'invoice_count' },
      { title: '总金额', dataIndex: 'total_amount', key: 'total_amount' },
      { title: '总税额', dataIndex: 'total_tax', key: 'total_tax' },
      { title: '平均税率', dataIndex: 'average_tax_rate', key: 'average_tax_rate' }
    ]

    const invoiceColumns = [
      { title: '发票号码', dataIndex: 'invoice_number', key: 'invoice_number', width: 150 },
      { title: '开票日期', dataIndex: 'issue_date', key: 'issue_date', width: 100 },
      { title: '认证状态', dataIndex: 'authentication_status', key: 'authentication_status', width: 100 },
      { title: '申报状态', dataIndex: 'declaration_status', key: 'declaration_status', width: 100 },
      { title: '含税金额', dataIndex: 'total_amount_with_tax', key: 'total_amount_with_tax', width: 120 },
      { title: '操作', key: 'action', width: 120 }
    ]

    // 行选择配置
    const rowSelection = {
      selectedRowKeys,
      onChange: (keys) => {
        selectedRowKeys.value = keys
      }
    }

    // 监听企业ID和申报期间变化
    watch([() => props.enterpriseId, selectedPeriod], async ([newEnterpriseId, newPeriod]) => {
      if (newEnterpriseId && newPeriod) {
        await refreshSummary()
        await fetchInvoices()
      }
    }, { immediate: true })

    // 方法实现
    const refreshSummary = async () => {
      if (!props.enterpriseId || !selectedPeriod.value) return

      try {
        summaryLoading.value = true
        const summary = await invoiceStore.fetchTaxSummary(props.enterpriseId, selectedPeriod.value)
        taxSummary.value = summary
      } catch (error) {
        console.error('刷新汇总数据失败:', error)
      } finally {
        summaryLoading.value = false
      }
    }

    const handlePeriodChange = async (period) => {
      selectedPeriod.value = period
      await refreshSummary()
      await fetchInvoices()
    }

    const fetchInvoices = async () => {
      if (!props.enterpriseId || !selectedPeriod.value) return

      try {
        invoiceLoading.value = true
        await invoiceStore.fetchInvoices({
          enterpriseId: props.enterpriseId, // 修正参数名
          declaration_period: selectedPeriod.value
        })
      } catch (error) {
        console.error('获取发票列表失败:', error)
        if (error.response?.status === 403) {
          message.error('无权限查看该企业的发票数据')
        }
      } finally {
        invoiceLoading.value = false
      }
    }

    const updateAuthStatus = async (record) => {
      try {
        await invoiceStore.updateAuthStatus(record.id, 'authenticated')
        await refreshSummary()
      } catch (error) {
        console.error('更新认证状态失败:', error)
      }
    }

    const updateDeclStatus = async (record) => {
      try {
        await invoiceStore.updateDeclStatus(record.id, 'declared', selectedPeriod.value)
        await refreshSummary()
      } catch (error) {
        console.error('更新申报状态失败:', error)
      }
    }

    const batchAuthenticate = async () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning('请选择要认证的发票')
        return
      }

      try {
        for (const id of selectedRowKeys.value) {
          await invoiceStore.updateAuthStatus(id, 'authenticated')
        }
        selectedRowKeys.value = []
        await refreshSummary()
        message.success('批量认证成功')
      } catch (error) {
        console.error('批量认证失败:', error)
        message.error('批量认证失败')
      }
    }

    const batchDeclare = async () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning('请选择要申报的发票')
        return
      }

      try {
        for (const id of selectedRowKeys.value) {
          await invoiceStore.updateDeclStatus(id, 'declared', selectedPeriod.value)
        }
        selectedRowKeys.value = []
        await refreshSummary()
        message.success('批量申报成功')
      } catch (error) {
        console.error('批量申报失败:', error)
        message.error('批量申报失败')
      }
    }

    const showInvoiceList = () => {
      emit('invoice-selected', {
        enterpriseId: props.enterpriseId,
        period: selectedPeriod.value,
        invoices: invoices.value
      })
    }

    const handleTableChange = (pagination) => {
      invoiceStore.setPagination({
        current: pagination.current,
        pageSize: pagination.pageSize
      })
      fetchInvoices()
    }

    // 工具方法
    const getTaxTypeColor = (taxTypeCode) => {
      const colorMap = {
        VAT: 'blue',
        INCOME: 'green',
        INDIVIDUAL: 'orange',
        PROPERTY: 'purple',
        STAMP: 'cyan'
      }
      return colorMap[taxTypeCode] || 'default'
    }

    const getStatusColor = (status, type) => {
      if (type === 'auth') {
        return invoiceStore.getStatusColor(status)
      } else if (type === 'decl') {
        return invoiceStore.getStatusColor(status)
      }
      return 'default'
    }

    const getStatusLabel = (status, _type) => {
      return invoiceStore.getStatusLabel(status)
    }

    return {
      // 状态
      summaryLoading,
      invoiceLoading,
      taxSummary,
      selectedPeriod,
      selectedRowKeys,
      availablePeriods,

      // 计算属性
      invoices,
      pagination,
      taxTypeSummary,
      declarationRate,
      hasSelectedInvoices,

      // 表格配置
      taxTypeColumns,
      invoiceColumns,
      rowSelection,

      // 方法
      refreshSummary,
      handlePeriodChange,
      fetchInvoices,
      updateAuthStatus,
      updateDeclStatus,
      batchAuthenticate,
      batchDeclare,
      showInvoiceList,
      handleTableChange,
      getTaxTypeColor,
      getStatusColor,
      getStatusLabel,
      formatNumber
    }
  }
})

// 辅助函数
function getCurrentPeriod () {
  const now = new Date()
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
}

function getLastPeriod (monthsAgo = 1) {
  const now = new Date()
  now.setMonth(now.getMonth() - monthsAgo)
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
}
</script>

<style scoped>
.invoice-integration {
  padding: 16px;
}

.summary-card .ant-statistic {
  text-align: center;
}

.tax-type-summary .ant-table {
  margin-top: 16px;
}
</style>
