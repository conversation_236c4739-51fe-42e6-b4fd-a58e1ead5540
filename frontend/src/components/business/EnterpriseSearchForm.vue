<template>
  <SearchForm
    v-model="searchForm"
    :loading="loading"
    @search="handleSearch"
    @reset="handleReset"
  >
    <a-col :span="6">
      <a-form-item label="关键词">
        <a-input
          v-model:value="searchForm.keyword"
          placeholder="搜索企业名称、统一社会信用代码"
          allow-clear
        />
      </a-form-item>
    </a-col>

    <a-col :span="6">
      <a-form-item label="行业">
        <a-select
          v-model:value="searchForm.industry"
          placeholder="选择行业"
          allow-clear
        >
          <a-select-option value="">
            全部行业
          </a-select-option>
          <a-select-option
            v-for="industry in industryOptions"
            :key="industry.value"
            :value="industry.value"
          >
            {{ industry.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-col>

    <a-col :span="6">
      <a-form-item label="状态">
        <a-select
          v-model:value="searchForm.status"
          placeholder="选择状态"
          allow-clear
        >
          <a-select-option value="">
            全部状态
          </a-select-option>
          <a-select-option
            v-for="status in statusOptions"
            :key="status.value"
            :value="status.value"
          >
            {{ status.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-col>

    <a-col :span="6">
      <a-form-item label="注册日期">
        <a-range-picker
          v-model:value="searchForm.dateRange"
          placeholder="选择注册日期"
          style="width: 100%"
        />
      </a-form-item>
    </a-col>
  </SearchForm>
</template>

<script>
import { defineComponent, reactive, computed } from 'vue'
import SearchForm from '@/components/common/SearchForm.vue'

export default defineComponent({
  name: 'EnterpriseSearchForm',
  components: {
    SearchForm
  },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue', 'search', 'reset'],
  setup (props, { emit }) {
    const searchForm = reactive({
      keyword: '',
      industry: '',
      status: '',
      dateRange: null,
      ...props.modelValue
    })

    // 行业选项
    const industryOptions = computed(() => [
      { value: 'manufacturing', label: '制造业' },
      { value: 'technology', label: '科技服务业' },
      { value: 'trade', label: '批发零售业' },
      { value: 'finance', label: '金融业' },
      { value: 'real-estate', label: '房地产业' },
      { value: 'construction', label: '建筑业' },
      { value: 'transportation', label: '交通运输业' },
      { value: 'education', label: '教育业' },
      { value: 'healthcare', label: '医疗卫生业' },
      { value: 'other', label: '其他' }
    ])

    // 状态选项
    const statusOptions = computed(() => [
      { value: 'active', label: '正常' },
      { value: 'inactive', label: '停用' },
      { value: 'pending', label: '待审核' },
      { value: 'suspended', label: '暂停' }
    ])

    const handleSearch = () => {
      emit('update:modelValue', { ...searchForm })
      emit('search', { ...searchForm })
    }

    const handleReset = () => {
      Object.keys(searchForm).forEach(key => {
        if (key === 'dateRange') {
          searchForm[key] = null
        } else {
          searchForm[key] = ''
        }
      })
      emit('update:modelValue', { ...searchForm })
      emit('reset')
    }

    return {
      searchForm,
      industryOptions,
      statusOptions,
      handleSearch,
      handleReset
    }
  }
})
</script>

<style scoped>
/* 组件特定样式 */
</style>
