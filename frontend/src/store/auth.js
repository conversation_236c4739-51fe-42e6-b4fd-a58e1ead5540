import { defineStore } from 'pinia'
import { login as login<PERSON>pi, logout as logout<PERSON><PERSON>, refreshToken as refreshTokenApi } from '@/api/auth'
import { useUserStore } from './user'

/**
 * 认证状态管理
 */
export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || null,
    refreshToken: localStorage.getItem('refresh_token') || null,
    isLoggingIn: false,
    isLoggingOut: false
  }),

  getters: {
    /**
     * 是否已登录
     */
    isLoggedIn: (state) => !!state.token,

    /**
     * 是否有刷新令牌
     */
    hasRefreshToken: (state) => !!state.refreshToken,

    /**
     * 认证头信息
     */
    authHeader: (state) => state.token ? `Bearer ${state.token}` : null
  },

  actions: {
    /**
     * 用户登录
     * @param {Object} credentials - 登录凭据
     * @param {string} credentials.email - 邮箱
     * @param {string} credentials.password - 密码
     * @param {boolean} credentials.remember - 是否记住登录状态
     */
    async login (credentials) {
      try {
        this.isLoggingIn = true

        const response = await loginApi(credentials)

        if (response && response.code === 200) {
          const { accessToken, refreshToken } = response.data

          // 设置令牌
          this.setTokens(accessToken, refreshToken)

          // 获取用户信息
          const userStore = useUserStore()
          await userStore.fetchUserInfo()

          return response.data
        } else {
          throw new Error(response?.message || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)
        throw error
      } finally {
        this.isLoggingIn = false
      }
    },

    /**
     * 用户登出
     */
    async logout () {
      try {
        this.isLoggingOut = true

        // 调用登出API
        if (this.token) {
          await logoutApi()
        }
      } catch (error) {
        console.error('登出API调用失败:', error)
        // 即使API调用失败，也要清除本地状态
      } finally {
        // 清除认证状态
        this.clearAuth()

        // 清除用户信息
        const userStore = useUserStore()
        userStore.clearUser()

        this.isLoggingOut = false
      }
    },

    /**
     * 刷新访问令牌
     */
    async refreshAccessToken () {
      try {
        if (!this.refreshToken) {
          throw new Error('没有刷新令牌')
        }

        const response = await refreshTokenApi(this.refreshToken)

        if (response && response.code === 200) {
          const { accessToken, refreshToken } = response.data
          this.setTokens(accessToken, refreshToken)
          return accessToken
        } else {
          throw new Error(response?.message || '刷新令牌失败')
        }
      } catch (error) {
        console.error('刷新令牌失败:', error)
        // 刷新失败，清除认证状态
        this.clearAuth()
        throw error
      }
    },

    /**
     * 设置令牌
     * @param {string} accessToken - 访问令牌
     * @param {string} refreshToken - 刷新令牌
     */
    setTokens (accessToken, refreshToken) {
      this.token = accessToken
      this.refreshToken = refreshToken || this.refreshToken

      // 存储到本地存储
      localStorage.setItem('token', accessToken)
      if (refreshToken) {
        localStorage.setItem('refresh_token', refreshToken)
      }
    },

    /**
     * 清除认证状态
     */
    clearAuth () {
      this.token = null
      this.refreshToken = null

      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('refresh_token')
    },

    /**
     * 检查令牌是否即将过期
     * @param {number} threshold - 提前刷新的时间阈值（秒）
     */
    shouldRefreshToken (threshold = 300) {
      if (!this.token) {
        return false
      }

      try {
        // 解析JWT令牌获取过期时间
        const payload = JSON.parse(atob(this.token.split('.')[1]))
        const expirationTime = payload.exp * 1000 // 转换为毫秒
        const currentTime = Date.now()
        const timeUntilExpiry = expirationTime - currentTime

        return timeUntilExpiry < threshold * 1000
      } catch (error) {
        console.error('解析令牌失败:', error)
        return true // 解析失败时建议刷新
      }
    },

    /**
     * 初始化认证状态
     * 应用启动时调用，检查本地存储的令牌
     */
    async initAuth () {
      const token = localStorage.getItem('token')
      const refreshToken = localStorage.getItem('refresh_token')

      if (token) {
        this.token = token
        this.refreshToken = refreshToken

        // 检查令牌是否需要刷新
        if (this.shouldRefreshToken()) {
          try {
            await this.refreshAccessToken()
          } catch (error) {
            console.error('初始化时刷新令牌失败:', error)
            this.clearAuth()
            return false
          }
        }

        // 获取用户信息
        try {
          const userStore = useUserStore()
          await userStore.fetchUserInfo()
          return true
        } catch (error) {
          console.error('获取用户信息失败:', error)
          this.clearAuth()
          return false
        }
      }

      return false
    }
  }
})
