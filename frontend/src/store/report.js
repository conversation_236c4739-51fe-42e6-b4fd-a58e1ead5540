/**
 * 报表管理状态管理
 * 遵循 VUE_STANDARDS.md 规范
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { reportApi } from '@/api/report'

export const useReportStore = defineStore('report', () => {
  // 状态
  const reportTemplates = ref([])
  const currentTemplate = ref(null)
  const loading = ref(false)
  const generatedReports = ref([])

  // 计算属性
  const templatesByType = computed(() => {
    const grouped = {}
    reportTemplates.value.forEach(template => {
      if (!grouped[template.type]) {
        grouped[template.type] = []
      }
      grouped[template.type].push(template)
    })
    return grouped
  })

  const totalTemplates = computed(() => reportTemplates.value.length)

  const customTemplates = computed(() => 
    reportTemplates.value.filter(template => template.type === 'custom')
  )

  // Actions

  /**
   * 获取报表模板列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 模板列表和分页信息
   */
  const getReportTemplates = async (params = {}) => {
    loading.value = true
    try {
      const response = await reportApi.getReportTemplates(params)
      if (response.success) {
        reportTemplates.value = response.data.list || []
        return response.data
      }
      throw new Error(response.message || '获取报表模板列表失败')
    } catch (error) {
      console.error('获取报表模板列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据ID获取报表模板详情
   * @param {string} id - 模板ID
   * @returns {Promise<Object>} 模板详情
   */
  const getReportTemplateById = async (id) => {
    loading.value = true
    try {
      const response = await reportApi.getReportTemplateById(id)
      if (response.success) {
        currentTemplate.value = response.data
        return response.data
      }
      throw new Error(response.message || '获取报表模板详情失败')
    } catch (error) {
      console.error('获取报表模板详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建报表模板
   * @param {Object} templateData - 模板数据
   * @returns {Promise<Object>} 创建的模板
   */
  const createReportTemplate = async (templateData) => {
    loading.value = true
    try {
      const response = await reportApi.createReportTemplate(templateData)
      if (response.success) {
        // 更新本地状态
        reportTemplates.value.unshift(response.data)
        return response.data
      }
      throw new Error(response.message || '创建报表模板失败')
    } catch (error) {
      console.error('创建报表模板失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新报表模板
   * @param {string} id - 模板ID
   * @param {Object} templateData - 更新的模板数据
   * @returns {Promise<Object>} 更新后的模板
   */
  const updateReportTemplate = async (id, templateData) => {
    loading.value = true
    try {
      const response = await reportApi.updateReportTemplate(id, templateData)
      if (response.success) {
        // 更新本地状态
        const index = reportTemplates.value.findIndex(template => template.id === id)
        if (index !== -1) {
          reportTemplates.value[index] = response.data
        }
        if (currentTemplate.value?.id === id) {
          currentTemplate.value = response.data
        }
        return response.data
      }
      throw new Error(response.message || '更新报表模板失败')
    } catch (error) {
      console.error('更新报表模板失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除报表模板
   * @param {string} id - 模板ID
   * @returns {Promise<void>}
   */
  const deleteReportTemplate = async (id) => {
    loading.value = true
    try {
      const response = await reportApi.deleteReportTemplate(id)
      if (response.success) {
        // 更新本地状态
        const index = reportTemplates.value.findIndex(template => template.id === id)
        if (index !== -1) {
          reportTemplates.value.splice(index, 1)
        }
        if (currentTemplate.value?.id === id) {
          currentTemplate.value = null
        }
      } else {
        throw new Error(response.message || '删除报表模板失败')
      }
    } catch (error) {
      console.error('删除报表模板失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量删除报表模板
   * @param {Array<string>} ids - 模板ID数组
   * @returns {Promise<void>}
   */
  const batchDeleteReportTemplates = async (ids) => {
    loading.value = true
    try {
      const response = await reportApi.batchDeleteReportTemplates(ids)
      if (response.success) {
        // 更新本地状态
        reportTemplates.value = reportTemplates.value.filter(template => !ids.includes(template.id))
      } else {
        throw new Error(response.message || '批量删除报表模板失败')
      }
    } catch (error) {
      console.error('批量删除报表模板失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据类型获取报表模板
   * @param {string} type - 模板类型
   * @returns {Promise<Array>} 模板列表
   */
  const getReportTemplatesByType = async (type = '') => {
    loading.value = true
    try {
      const response = await reportApi.getReportTemplatesByType(type)
      if (response.success) {
        return response.data
      }
      throw new Error(response.message || '获取报表模板失败')
    } catch (error) {
      console.error('获取报表模板失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 生成报表
   * @param {Object} generateData - 生成参数
   * @returns {Promise<Object>} 生成的报表
   */
  const generateReport = async (generateData) => {
    loading.value = true
    try {
      const response = await reportApi.generateReport(generateData)
      if (response.success) {
        // 添加到生成历史
        generatedReports.value.unshift({
          ...response.data,
          generated_at: new Date().toISOString()
        })
        return response.data
      }
      throw new Error(response.message || '生成报表失败')
    } catch (error) {
      console.error('生成报表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取报表类型列表
   * @returns {Promise<Array>} 报表类型列表
   */
  const getReportTypes = async () => {
    try {
      const response = await reportApi.getReportTypes()
      if (response.success) {
        return response.data
      }
      throw new Error(response.message || '获取报表类型失败')
    } catch (error) {
      console.error('获取报表类型失败:', error)
      throw error
    }
  }

  /**
   * 获取报表格式列表
   * @returns {Promise<Array>} 报表格式列表
   */
  const getReportFormats = async () => {
    try {
      const response = await reportApi.getReportFormats()
      if (response.success) {
        return response.data
      }
      throw new Error(response.message || '获取报表格式失败')
    } catch (error) {
      console.error('获取报表格式失败:', error)
      throw error
    }
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    reportTemplates.value = []
    currentTemplate.value = null
    generatedReports.value = []
    loading.value = false
  }

  /**
   * 设置当前模板
   * @param {Object} template - 模板对象
   */
  const setCurrentTemplate = (template) => {
    currentTemplate.value = template
  }

  /**
   * 添加生成的报表到历史记录
   * @param {Object} report - 报表对象
   */
  const addGeneratedReport = (report) => {
    generatedReports.value.unshift(report)
  }

  /**
   * 清除生成历史
   */
  const clearGeneratedReports = () => {
    generatedReports.value = []
  }

  return {
    // 状态
    reportTemplates,
    currentTemplate,
    loading,
    generatedReports,
    
    // 计算属性
    templatesByType,
    totalTemplates,
    customTemplates,
    
    // Actions
    getReportTemplates,
    getReportTemplateById,
    createReportTemplate,
    updateReportTemplate,
    deleteReportTemplate,
    batchDeleteReportTemplates,
    getReportTemplatesByType,
    generateReport,
    getReportTypes,
    getReportFormats,
    resetState,
    setCurrentTemplate,
    addGeneratedReport,
    clearGeneratedReports
  }
})
