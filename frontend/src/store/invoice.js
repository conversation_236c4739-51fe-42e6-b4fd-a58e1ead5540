import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  getInvoiceList,
  getInvoiceDetail,
  createInvoice,
  updateInvoice,
  deleteInvoice,
  getInvoicesByDeclarationPeriod,
  updateInvoiceAuthenticationStatus,
  updateInvoiceDeclarationStatus,
  getInvoiceTaxSummary
} from '@/api/invoice'

/**
 * 发票状态管理
 */
export const useInvoiceStore = defineStore('invoice', () => {
  // 状态
  const invoices = ref([])
  const currentInvoice = ref(null)
  const loading = ref(false)
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  })
  const filters = ref({
    enterprise_id: '',
    type: '',
    status: '',
    authentication_status: '',
    declaration_status: '',
    start_date: '',
    end_date: '',
    keyword: ''
  })

  // 发票状态常量
  const INVOICE_STATUS = {
    DRAFT: 'draft',
    ISSUED: 'issued',
    CANCELLED: 'cancelled',
    INVALID: 'invalid',
    RED_FLUSHED: 'red_flushed'
  }

  // 认证状态常量
  const AUTH_STATUS = {
    PENDING: 'pending',
    AUTHENTICATED: 'authenticated',
    FAILED: 'failed',
    EXPIRED: 'expired'
  }

  // 申报状态常量
  const DECLARATION_STATUS = {
    UNDECLARED: 'undeclared',
    DECLARED: 'declared',
    CONFIRMED: 'confirmed'
  }

  // 状态标签映射
  const statusLabels = {
    // 发票状态
    [INVOICE_STATUS.DRAFT]: '草稿',
    [INVOICE_STATUS.ISSUED]: '已开具',
    [INVOICE_STATUS.CANCELLED]: '已作废',
    [INVOICE_STATUS.INVALID]: '无效',
    [INVOICE_STATUS.RED_FLUSHED]: '已红冲',

    // 认证状态
    [AUTH_STATUS.PENDING]: '待认证',
    [AUTH_STATUS.AUTHENTICATED]: '已认证',
    [AUTH_STATUS.FAILED]: '认证失败',
    [AUTH_STATUS.EXPIRED]: '已过期',

    // 申报状态
    [DECLARATION_STATUS.UNDECLARED]: '未申报',
    [DECLARATION_STATUS.DECLARED]: '已申报',
    [DECLARATION_STATUS.CONFIRMED]: '已确认'
  }

  // 状态颜色映射
  const statusColors = {
    // 发票状态
    [INVOICE_STATUS.DRAFT]: 'default',
    [INVOICE_STATUS.ISSUED]: 'success',
    [INVOICE_STATUS.CANCELLED]: 'error',
    [INVOICE_STATUS.INVALID]: 'error',
    [INVOICE_STATUS.RED_FLUSHED]: 'warning',

    // 认证状态
    [AUTH_STATUS.PENDING]: 'processing',
    [AUTH_STATUS.AUTHENTICATED]: 'success',
    [AUTH_STATUS.FAILED]: 'error',
    [AUTH_STATUS.EXPIRED]: 'error',

    // 申报状态
    [DECLARATION_STATUS.UNDECLARED]: 'warning',
    [DECLARATION_STATUS.DECLARED]: 'processing',
    [DECLARATION_STATUS.CONFIRMED]: 'success'
  }

  // 计算属性
  const invoicesByStatus = computed(() => {
    const result = {}
    Object.values(INVOICE_STATUS).forEach(status => {
      result[status] = invoices.value.filter(inv => inv.status === status)
    })
    return result
  })

  const invoicesByAuthStatus = computed(() => {
    const result = {}
    Object.values(AUTH_STATUS).forEach(status => {
      result[status] = invoices.value.filter(inv => inv.authentication_status === status)
    })
    return result
  })

  const invoicesByDeclStatus = computed(() => {
    const result = {}
    Object.values(DECLARATION_STATUS).forEach(status => {
      result[status] = invoices.value.filter(inv => inv.declaration_status === status)
    })
    return result
  })

  const pendingAuthInvoices = computed(() => {
    return invoices.value.filter(inv => inv.authentication_status === AUTH_STATUS.PENDING)
  })

  const undeclaredInvoices = computed(() => {
    return invoices.value.filter(inv => inv.declaration_status === DECLARATION_STATUS.UNDECLARED)
  })

  // Actions
  const fetchInvoices = async (params = {}) => {
    try {
      loading.value = true
      const requestParams = {
        page: pagination.value.current,
        pageSize: pagination.value.pageSize,
        ...filters.value,
        ...params
      }

      // 过滤掉空值参数
      Object.keys(requestParams).forEach(key => {
        if (requestParams[key] === '' || requestParams[key] === null || requestParams[key] === undefined) {
          delete requestParams[key]
        }
      })

      const response = await getInvoiceList(requestParams)

      if (response.code === 200) {
        invoices.value = response.data.data || response.data.list || []
        pagination.value.total = response.data.total || 0
        pagination.value.current = response.data.page || pagination.value.current
      }
    } catch (error) {
      console.error('获取发票列表失败:', error)
      if (error.response?.status === 403) {
        message.error('无权限查看发票数据')
      } else {
        message.error('获取发票列表失败')
      }
    } finally {
      loading.value = false
    }
  }

  const fetchInvoiceDetail = async (id) => {
    try {
      loading.value = true
      const response = await getInvoiceDetail(id)

      if (response.code === 200) {
        currentInvoice.value = response.data
        return response.data
      }
    } catch (error) {
      console.error('获取发票详情失败:', error)
      message.error('获取发票详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const createNewInvoice = async (data) => {
    try {
      loading.value = true
      const response = await createInvoice(data)

      if (response.code === 200) {
        message.success('创建发票成功')
        await fetchInvoices()
        return response.data
      }
    } catch (error) {
      console.error('创建发票失败:', error)
      message.error('创建发票失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateInvoiceData = async (id, data) => {
    try {
      loading.value = true
      const response = await updateInvoice(id, data)

      if (response.code === 200) {
        message.success('更新发票成功')
        await fetchInvoices()
        return response.data
      }
    } catch (error) {
      console.error('更新发票失败:', error)
      message.error('更新发票失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteInvoiceData = async (id) => {
    try {
      loading.value = true
      const response = await deleteInvoice(id)

      if (response.code === 200) {
        message.success('删除发票成功')
        await fetchInvoices()
      }
    } catch (error) {
      console.error('删除发票失败:', error)
      message.error('删除发票失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchInvoicesByPeriod = async (enterpriseId, period) => {
    try {
      loading.value = true
      const response = await getInvoicesByDeclarationPeriod(enterpriseId, period)

      if (response.code === 200) {
        return response.data
      }
    } catch (error) {
      console.error('获取申报期间发票失败:', error)
      message.error('获取申报期间发票失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateAuthStatus = async (id, status) => {
    try {
      const response = await updateInvoiceAuthenticationStatus(id, { status })

      if (response.code === 200) {
        message.success('更新认证状态成功')
        await fetchInvoices()
      }
    } catch (error) {
      console.error('更新认证状态失败:', error)
      message.error('更新认证状态失败')
      throw error
    }
  }

  const updateDeclStatus = async (id, status, period = '') => {
    try {
      const response = await updateInvoiceDeclarationStatus(id, { status, period })

      if (response.code === 200) {
        message.success('更新申报状态成功')
        await fetchInvoices()
      }
    } catch (error) {
      console.error('更新申报状态失败:', error)
      message.error('更新申报状态失败')
      throw error
    }
  }

  const fetchTaxSummary = async (enterpriseId, period = '') => {
    try {
      const response = await getInvoiceTaxSummary(enterpriseId, period)

      if (response.code === 200) {
        return response.data
      }
    } catch (error) {
      console.error('获取税务汇总失败:', error)
      message.error('获取税务汇总失败')
      throw error
    }
  }

  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const setPagination = (newPagination) => {
    pagination.value = { ...pagination.value, ...newPagination }
  }

  const resetFilters = () => {
    filters.value = {
      enterprise_id: '',
      type: '',
      status: '',
      authentication_status: '',
      declaration_status: '',
      start_date: '',
      end_date: '',
      keyword: ''
    }
  }

  const getStatusLabel = (status, _type = '') => {
    // 如果指定了类型，可以根据类型进行特殊处理
    return statusLabels[status] || status
  }

  const getStatusColor = (status, _type = '') => {
    // 如果指定了类型，可以根据类型进行特殊处理
    return statusColors[status] || 'default'
  }

  return {
    // 状态
    invoices,
    currentInvoice,
    loading,
    pagination,
    filters,

    // 常量
    INVOICE_STATUS,
    AUTH_STATUS,
    DECLARATION_STATUS,
    statusLabels,
    statusColors,

    // 计算属性
    invoicesByStatus,
    invoicesByAuthStatus,
    invoicesByDeclStatus,
    pendingAuthInvoices,
    undeclaredInvoices,

    // Actions
    fetchInvoices,
    fetchInvoiceDetail,
    createNewInvoice,
    updateInvoiceData,
    deleteInvoiceData,
    fetchInvoicesByPeriod,
    updateAuthStatus,
    updateDeclStatus,
    fetchTaxSummary,
    setFilters,
    setPagination,
    resetFilters,
    getStatusLabel,
    getStatusColor
  }
})
