import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    sidebar: {
      collapsed: localStorage.getItem('sidebarCollapsed') === 'true' || false
    },
    device: 'desktop',
    theme: localStorage.getItem('theme') || 'light',
    size: localStorage.getItem('size') || 'medium',
    locale: localStorage.getItem('locale') || 'zh-CN'
  }),

  getters: {
    isMobile: (state) => state.device === 'mobile',
    isDarkTheme: (state) => state.theme === 'dark'
  },

  actions: {
    toggleSidebar () {
      this.sidebar.collapsed = !this.sidebar.collapsed
      localStorage.setItem('sidebarCollapsed', this.sidebar.collapsed.toString())
    },

    setSidebarCollapsed (collapsed) {
      this.sidebar.collapsed = collapsed
      localStorage.setItem('sidebarCollapsed', collapsed.toString())
    },

    setDevice (device) {
      this.device = device
    },

    setTheme (theme) {
      this.theme = theme
      localStorage.setItem('theme', theme)
    },

    setSize (size) {
      this.size = size
      localStorage.setItem('size', size)
    },

    setLocale (locale) {
      this.locale = locale
      localStorage.setItem('locale', locale)
    }
  }
})
