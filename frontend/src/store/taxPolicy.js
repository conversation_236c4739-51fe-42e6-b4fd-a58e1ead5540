/**
 * 税务政策管理状态管理
 * 遵循 VUE_STANDARDS.md 规范
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { taxPolicyApi } from '@/api/taxPolicy'

export const useTaxPolicyStore = defineStore('taxPolicy', () => {
  // 状态
  const taxPolicies = ref([])
  const currentPolicy = ref(null)
  const loading = ref(false)
  const stats = ref({})

  // 计算属性
  const effectivePolicies = computed(() => 
    taxPolicies.value.filter(policy => policy.status === 'effective')
  )

  const draftPolicies = computed(() => 
    taxPolicies.value.filter(policy => policy.status === 'draft')
  )

  const expiredPolicies = computed(() => 
    taxPolicies.value.filter(policy => policy.status === 'expired')
  )

  const totalCount = computed(() => taxPolicies.value.length)

  // Actions
  
  /**
   * 获取税务政策列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 政策列表和分页信息
   */
  const getTaxPolicies = async (params = {}) => {
    loading.value = true
    try {
      const response = await taxPolicyApi.getTaxPolicies(params)
      if (response.success) {
        taxPolicies.value = response.data.list || []
        return response.data
      }
      throw new Error(response.message || '获取税务政策列表失败')
    } catch (error) {
      console.error('获取税务政策列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据ID获取税务政策详情
   * @param {string} id - 政策ID
   * @returns {Promise<Object>} 政策详情
   */
  const getTaxPolicyById = async (id) => {
    loading.value = true
    try {
      const response = await taxPolicyApi.getTaxPolicyById(id)
      if (response.success) {
        currentPolicy.value = response.data
        return response.data
      }
      throw new Error(response.message || '获取税务政策详情失败')
    } catch (error) {
      console.error('获取税务政策详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建税务政策
   * @param {Object} policyData - 政策数据
   * @returns {Promise<Object>} 创建的政策
   */
  const createTaxPolicy = async (policyData) => {
    loading.value = true
    try {
      const response = await taxPolicyApi.createTaxPolicy(policyData)
      if (response.success) {
        // 更新本地状态
        taxPolicies.value.unshift(response.data)
        return response.data
      }
      throw new Error(response.message || '创建税务政策失败')
    } catch (error) {
      console.error('创建税务政策失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新税务政策
   * @param {string} id - 政策ID
   * @param {Object} policyData - 更新的政策数据
   * @returns {Promise<Object>} 更新后的政策
   */
  const updateTaxPolicy = async (id, policyData) => {
    loading.value = true
    try {
      const response = await taxPolicyApi.updateTaxPolicy(id, policyData)
      if (response.success) {
        // 更新本地状态
        const index = taxPolicies.value.findIndex(policy => policy.id === id)
        if (index !== -1) {
          taxPolicies.value[index] = response.data
        }
        if (currentPolicy.value?.id === id) {
          currentPolicy.value = response.data
        }
        return response.data
      }
      throw new Error(response.message || '更新税务政策失败')
    } catch (error) {
      console.error('更新税务政策失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除税务政策
   * @param {string} id - 政策ID
   * @returns {Promise<void>}
   */
  const deleteTaxPolicy = async (id) => {
    loading.value = true
    try {
      const response = await taxPolicyApi.deleteTaxPolicy(id)
      if (response.success) {
        // 更新本地状态
        const index = taxPolicies.value.findIndex(policy => policy.id === id)
        if (index !== -1) {
          taxPolicies.value.splice(index, 1)
        }
        if (currentPolicy.value?.id === id) {
          currentPolicy.value = null
        }
      } else {
        throw new Error(response.message || '删除税务政策失败')
      }
    } catch (error) {
      console.error('删除税务政策失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量删除税务政策
   * @param {Array<string>} ids - 政策ID数组
   * @returns {Promise<void>}
   */
  const batchDeleteTaxPolicies = async (ids) => {
    loading.value = true
    try {
      const response = await taxPolicyApi.batchDeleteTaxPolicies(ids)
      if (response.success) {
        // 更新本地状态
        taxPolicies.value = taxPolicies.value.filter(policy => !ids.includes(policy.id))
      } else {
        throw new Error(response.message || '批量删除税务政策失败')
      }
    } catch (error) {
      console.error('批量删除税务政策失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新税务政策状态
   * @param {string} id - 政策ID
   * @param {string} status - 新状态
   * @returns {Promise<void>}
   */
  const updateTaxPolicyStatus = async (id, status) => {
    loading.value = true
    try {
      const response = await taxPolicyApi.updateTaxPolicyStatus(id, status)
      if (response.success) {
        // 更新本地状态
        const index = taxPolicies.value.findIndex(policy => policy.id === id)
        if (index !== -1) {
          taxPolicies.value[index].status = status
        }
        if (currentPolicy.value?.id === id) {
          currentPolicy.value.status = status
        }
      } else {
        throw new Error(response.message || '更新税务政策状态失败')
      }
    } catch (error) {
      console.error('更新税务政策状态失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量更新税务政策状态
   * @param {Array<string>} ids - 政策ID数组
   * @param {string} status - 新状态
   * @returns {Promise<void>}
   */
  const batchUpdateTaxPolicyStatus = async (ids, status) => {
    loading.value = true
    try {
      const response = await taxPolicyApi.batchUpdateTaxPolicyStatus(ids, status)
      if (response.success) {
        // 更新本地状态
        taxPolicies.value.forEach(policy => {
          if (ids.includes(policy.id)) {
            policy.status = status
          }
        })
      } else {
        throw new Error(response.message || '批量更新税务政策状态失败')
      }
    } catch (error) {
      console.error('批量更新税务政策状态失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取税务政策统计信息
   * @returns {Promise<Object>} 统计信息
   */
  const getTaxPolicyStats = async () => {
    loading.value = true
    try {
      const response = await taxPolicyApi.getTaxPolicyStats()
      if (response.success) {
        stats.value = response.data
        return response.data
      }
      throw new Error(response.message || '获取税务政策统计信息失败')
    } catch (error) {
      console.error('获取税务政策统计信息失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 搜索税务政策
   * @param {string} keyword - 搜索关键词
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 搜索结果
   */
  const searchTaxPolicies = async (keyword, limit = 10) => {
    loading.value = true
    try {
      const response = await taxPolicyApi.searchTaxPolicies(keyword, limit)
      if (response.success) {
        return response.data
      }
      throw new Error(response.message || '搜索税务政策失败')
    } catch (error) {
      console.error('搜索税务政策失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取有效的税务政策
   * @param {string} category - 政策分类
   * @returns {Promise<Array>} 有效政策列表
   */
  const getEffectiveTaxPolicies = async (category = '') => {
    loading.value = true
    try {
      const response = await taxPolicyApi.getEffectiveTaxPolicies(category)
      if (response.success) {
        return response.data
      }
      throw new Error(response.message || '获取有效税务政策失败')
    } catch (error) {
      console.error('获取有效税务政策失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    taxPolicies.value = []
    currentPolicy.value = null
    stats.value = {}
    loading.value = false
  }

  /**
   * 设置当前政策
   * @param {Object} policy - 政策对象
   */
  const setCurrentPolicy = (policy) => {
    currentPolicy.value = policy
  }

  return {
    // 状态
    taxPolicies,
    currentPolicy,
    loading,
    stats,
    
    // 计算属性
    effectivePolicies,
    draftPolicies,
    expiredPolicies,
    totalCount,
    
    // Actions
    getTaxPolicies,
    getTaxPolicyById,
    createTaxPolicy,
    updateTaxPolicy,
    deleteTaxPolicy,
    batchDeleteTaxPolicies,
    updateTaxPolicyStatus,
    batchUpdateTaxPolicyStatus,
    getTaxPolicyStats,
    searchTaxPolicies,
    getEffectiveTaxPolicies,
    resetState,
    setCurrentPolicy
  }
})
