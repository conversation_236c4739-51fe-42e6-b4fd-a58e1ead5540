import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  getDeclarationList,
  getDeclarationDetail,
  createDeclaration,
  updateDeclaration,
  deleteDeclaration,
  submitDeclaration
} from '@/api/declaration'

/**
 * 申报状态管理
 */
export const useDeclarationStore = defineStore('declaration', () => {
  // 状态
  const declarations = ref([])
  const currentDeclaration = ref(null)
  const loading = ref(false)
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  })
  const filters = ref({
    enterprise_id: '',
    tax_type_id: '',
    status: '',
    year: null,
    month: null,
    start_date: '',
    end_date: ''
  })

  // 申报状态常量
  const DECLARATION_STATUS = {
    DRAFT: 'draft',
    SUBMITTED: 'submitted',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    PAID: 'paid',
    OVERDUE: 'overdue'
  }

  // 申报状态标签映射
  const statusLabels = {
    [DECLARATION_STATUS.DRAFT]: '草稿',
    [DECLARATION_STATUS.SUBMITTED]: '已提交',
    [DECLARATION_STATUS.APPROVED]: '已批准',
    [DECLARATION_STATUS.REJECTED]: '已拒绝',
    [DECLARATION_STATUS.PAID]: '已缴费',
    [DECLARATION_STATUS.OVERDUE]: '已逾期'
  }

  // 申报状态颜色映射
  const statusColors = {
    [DECLARATION_STATUS.DRAFT]: 'default',
    [DECLARATION_STATUS.SUBMITTED]: 'processing',
    [DECLARATION_STATUS.APPROVED]: 'success',
    [DECLARATION_STATUS.REJECTED]: 'error',
    [DECLARATION_STATUS.PAID]: 'success',
    [DECLARATION_STATUS.OVERDUE]: 'error'
  }

  // 计算属性
  const declarationsByStatus = computed(() => {
    const result = {}
    Object.values(DECLARATION_STATUS).forEach(status => {
      result[status] = declarations.value.filter(d => d.status === status)
    })
    return result
  })

  const overdueDeclarations = computed(() => {
    const now = new Date()
    return declarations.value.filter(d => {
      const dueDate = new Date(d.due_date)
      return dueDate < now && d.status !== DECLARATION_STATUS.PAID
    })
  })

  const upcomingDeclarations = computed(() => {
    const now = new Date()
    const threeDaysLater = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000)
    return declarations.value.filter(d => {
      const dueDate = new Date(d.due_date)
      return dueDate >= now && dueDate <= threeDaysLater && d.status === DECLARATION_STATUS.DRAFT
    })
  })

  // Actions
  const fetchDeclarations = async (params = {}) => {
    try {
      loading.value = true
      const requestParams = {
        page: pagination.value.current,
        pageSize: pagination.value.pageSize,
        ...filters.value,
        ...params
      }

      const response = await getDeclarationList(requestParams)

      if (response.code === 200) {
        declarations.value = response.data.data || response.data.list || []
        pagination.value.total = response.data.total || 0
        pagination.value.current = response.data.page || pagination.value.current
      }
    } catch (error) {
      console.error('获取申报列表失败:', error)
      message.error('获取申报列表失败')
    } finally {
      loading.value = false
    }
  }

  const fetchDeclarationDetail = async (id) => {
    try {
      loading.value = true
      const response = await getDeclarationDetail(id)

      if (response.code === 200) {
        currentDeclaration.value = response.data
        return response.data
      }
    } catch (error) {
      console.error('获取申报详情失败:', error)
      message.error('获取申报详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const createNewDeclaration = async (data) => {
    try {
      loading.value = true
      const response = await createDeclaration(data)

      if (response.code === 200) {
        message.success('创建申报成功')
        await fetchDeclarations()
        return response.data
      }
    } catch (error) {
      console.error('创建申报失败:', error)
      message.error('创建申报失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateDeclarationData = async (id, data) => {
    try {
      loading.value = true
      const response = await updateDeclaration(id, data)

      if (response.code === 200) {
        message.success('更新申报成功')
        await fetchDeclarations()
        return response.data
      }
    } catch (error) {
      console.error('更新申报失败:', error)
      message.error('更新申报失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteDeclarationData = async (id) => {
    try {
      loading.value = true
      const response = await deleteDeclaration(id)

      if (response.code === 200) {
        message.success('删除申报成功')
        await fetchDeclarations()
      }
    } catch (error) {
      console.error('删除申报失败:', error)
      message.error('删除申报失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const submitDeclarationData = async (id) => {
    try {
      loading.value = true
      const response = await submitDeclaration(id)

      if (response.code === 200) {
        message.success('提交申报成功')
        await fetchDeclarations()
        return response.data
      }
    } catch (error) {
      console.error('提交申报失败:', error)
      message.error('提交申报失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const setPagination = (newPagination) => {
    pagination.value = { ...pagination.value, ...newPagination }
  }

  const resetFilters = () => {
    filters.value = {
      enterprise_id: '',
      tax_type_id: '',
      status: '',
      year: null,
      month: null,
      start_date: '',
      end_date: ''
    }
  }

  const getStatusLabel = (status) => {
    return statusLabels[status] || status
  }

  const getStatusColor = (status) => {
    return statusColors[status] || 'default'
  }

  return {
    // 状态
    declarations,
    currentDeclaration,
    loading,
    pagination,
    filters,

    // 常量
    DECLARATION_STATUS,
    statusLabels,
    statusColors,

    // 计算属性
    declarationsByStatus,
    overdueDeclarations,
    upcomingDeclarations,

    // Actions
    fetchDeclarations,
    fetchDeclarationDetail,
    createNewDeclaration,
    updateDeclarationData,
    deleteDeclarationData,
    submitDeclarationData,
    setFilters,
    setPagination,
    resetFilters,
    getStatusLabel,
    getStatusColor
  }
})
