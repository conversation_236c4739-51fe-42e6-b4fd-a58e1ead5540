import { defineStore } from 'pinia'
import { getUserInfo, updateProfile, logout as logoutApi } from '@/api/auth'

/**
 * 用户信息状态管理
 */
export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    isLoading: false,
    lastFetchTime: null,
    token: localStorage.getItem('token') || null,
    refreshToken: localStorage.getItem('refresh_token') || null
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,
    userName: (state) => state.userInfo?.name || '用户',
    userRole: (state) => state.userInfo?.role || 'user',
    userEmail: (state) => state.userInfo?.email || '',
    userAvatar: (state) => state.userInfo?.avatar || '',
    user: (state) => state.userInfo,
    hasUserInfo: (state) => !!state.userInfo,
    displayName: (state) => {
      if (!state.userInfo) return '用户'
      return state.userInfo.nickname || state.userInfo.name || '用户'
    },
    isAdmin: (state) => state.userInfo?.role === 'admin',
    authHeader: (state) => state.token ? `Bearer ${state.token}` : null
  },

  actions: {
    /**
     * 用户登录
     * @param {string} accessToken - 访问令牌
     * @param {string} refreshToken - 刷新令牌
     */
    async login (accessToken, refreshToken = null) {
      try {
        // 设置令牌
        this.token = accessToken
        this.refreshToken = refreshToken

        // 保存到本地存储
        localStorage.setItem('token', accessToken)
        if (refreshToken) {
          localStorage.setItem('refresh_token', refreshToken)
        }

        // 获取用户信息
        await this.fetchUserInfo()

        return true
      } catch (error) {
        console.error('登录失败:', error)
        this.clearAuth()
        throw error
      }
    },

    /**
     * 用户登出
     */
    async logout () {
      try {
        // 调用登出API
        if (this.token) {
          await logoutApi()
        }
      } catch (error) {
        console.error('登出API调用失败:', error)
        // 即使API调用失败，也要清除本地状态
      } finally {
        this.clearAuth()
      }
    },

    /**
     * 清除认证信息
     */
    clearAuth () {
      this.token = null
      this.refreshToken = null
      this.userInfo = null
      this.lastFetchTime = null
      this.isLoading = false

      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('refresh_token')
    },

    /**
     * 获取用户信息
     */
    async fetchUserInfo (force = false) {
      if (!force && this.lastFetchTime && Date.now() - this.lastFetchTime < 60000) {
        return this.userInfo
      }

      try {
        this.isLoading = true
        const response = await getUserInfo()

        if (response && response.code === 200) {
          const userData = response.data
          // 处理字段映射，确保向后兼容
          this.userInfo = {
            ...userData,
            name: userData.user_name || userData.fullName || userData.name || '',
            user_name: userData.user_name || userData.fullName || userData.name || ''
          }
          this.lastFetchTime = Date.now()
          return this.userInfo
        } else {
          throw new Error(response?.message || '获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 设置用户信息
     */
    setUser (userInfo) {
      this.userInfo = userInfo
      this.lastFetchTime = Date.now()
    },

    /**
     * 更新用户信息
     */
    async updateUserInfo (updates) {
      try {
        this.isLoading = true

        const response = await updateProfile(updates)

        if (response && response.code === 200) {
          const userData = response.data
          // 处理字段映射
          const updatedUserInfo = {
            ...userData,
            name: userData.user_name || userData.fullName || userData.name || '',
            user_name: userData.user_name || userData.fullName || userData.name || ''
          }
          this.userInfo = { ...this.userInfo, ...updatedUserInfo }
          this.lastFetchTime = Date.now()
          return this.userInfo
        } else {
          throw new Error(response?.message || '更新用户信息失败')
        }
      } catch (error) {
        console.error('更新用户信息失败:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 清除用户信息（保持向后兼容）
     */
    clearUser () {
      this.clearAuth()
    },

    /**
     * 检查用户权限
     */
    hasPermission (permission) {
      if (!this.userInfo || !this.userInfo.permissions) {
        return false
      }
      return this.userInfo.permissions.includes(permission)
    }
  }
})
