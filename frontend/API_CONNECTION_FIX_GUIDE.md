# 🔧 API连接问题修复指南

## 📋 问题描述

前端应用出现 "Failed to fetch" 错误，无法连接到后端API服务。

## ✅ 已完成的修复

### 1. 更新前端API配置

**文件**: `frontend/src/utils/request.js`

**修复内容**: 添加了对8082端口的支持
```javascript
// 修复前
if (currentPort === '8080') {
    return `${protocol}//${hostname}:8081/api`
}

// 修复后  
if (currentPort === '8080' || currentPort === '8082') {
    return `${protocol}//${hostname}:8081/api`
}
```

### 2. 更新后端CORS配置

**文件**: `backend/middleware/auth.go`

**修复内容**: 添加了8082端口到允许的源列表
```go
AllowOrigins: []string{
    "http://localhost:3000",
    "http://localhost:8000", 
    "http://localhost:8080",
    "http://localhost:8082",  // 新增
    "http://127.0.0.1:3000",
    "http://127.0.0.1:8000",
    "http://127.0.0.1:8080", 
    "http://127.0.0.1:8082",  // 新增
},
```

### 3. 重启后端服务

**操作**: 重启后端服务以应用CORS配置更改
```bash
cd backend
go run main_tax_system.go
```

## 🧪 验证步骤

### 1. 检查服务状态

**后端服务** (端口8081):
```bash
curl http://localhost:8081/api/system/health
```

**前端服务** (端口8082):
```bash
curl http://localhost:8082
```

### 2. 测试API连接

**使用调试工具**:
打开 `frontend/debug-api-connection.html` 进行连接测试

**手动测试**:
```bash
curl -X GET "http://localhost:8081/api/enterprises/stats" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:8082"
```

### 3. 浏览器开发者工具检查

1. 打开浏览器开发者工具 (F12)
2. 查看 Network 标签页
3. 检查API请求是否成功
4. 查看 Console 标签页检查错误信息

## 🔍 常见问题排查

### 问题1: CORS错误
**症状**: 浏览器控制台显示CORS相关错误
**解决方案**: 
- 确认后端CORS配置包含前端端口
- 重启后端服务应用配置

### 问题2: 网络连接失败
**症状**: "Failed to fetch" 或 "Network Error"
**解决方案**:
- 检查后端服务是否在8081端口运行
- 检查防火墙设置
- 验证API基础URL配置

### 问题3: 认证失败
**症状**: 401 Unauthorized 错误
**解决方案**:
- 检查JWT token是否有效
- 确认Authorization头格式正确
- 重新登录获取新token

### 问题4: 端口配置错误
**症状**: 连接到错误的端口
**解决方案**:
- 检查环境变量配置
- 验证request.js中的端口映射逻辑

## 🛠️ 调试工具

### 1. API连接调试页面
**文件**: `frontend/debug-api-connection.html`
**功能**: 
- 显示当前配置信息
- 测试API连接状态
- 提供详细的错误信息

### 2. 浏览器控制台调试
```javascript
// 检查当前API配置
console.log('API Base URL:', process.env.VUE_APP_API_BASE_URL)

// 测试API连接
fetch('http://localhost:8081/api/system/health')
  .then(response => response.json())
  .then(data => console.log('API连接成功:', data))
  .catch(error => console.error('API连接失败:', error))
```

### 3. 网络请求监控
```javascript
// 在main.js中添加请求拦截器
import axios from 'axios'

axios.interceptors.request.use(config => {
  console.log('发送请求:', config.url, config)
  return config
})

axios.interceptors.response.use(
  response => {
    console.log('收到响应:', response.config.url, response.status)
    return response
  },
  error => {
    console.error('请求失败:', error.config?.url, error.message)
    return Promise.reject(error)
  }
)
```

## 📊 配置检查清单

### 前端配置
- [ ] `.env.development` 中的 `VUE_APP_API_BASE_URL` 正确
- [ ] `request.js` 中的端口映射包含8082
- [ ] 前端服务运行在正确端口

### 后端配置
- [ ] CORS中间件包含前端端口
- [ ] 后端服务运行在8081端口
- [ ] 健康检查端点可访问

### 网络配置
- [ ] 防火墙允许8081和8082端口
- [ ] 本地DNS解析正常
- [ ] 代理设置不影响本地连接

## 🚀 快速修复命令

```bash
# 1. 重启后端服务
cd backend
pkill -f main_tax_system
go run main_tax_system.go

# 2. 重启前端服务  
cd frontend
npm run serve

# 3. 测试连接
curl http://localhost:8081/api/system/health
curl http://localhost:8082

# 4. 打开调试页面
open debug-api-connection.html
```

## 📞 获取帮助

如果问题仍然存在，请：

1. **收集信息**:
   - 浏览器控制台错误信息
   - 网络请求详情
   - 服务运行状态

2. **检查日志**:
   - 后端服务日志
   - 前端开发服务器日志
   - 浏览器开发者工具

3. **验证环境**:
   - Node.js和Go版本
   - 依赖包版本
   - 系统环境配置

---

**最后更新**: 2025年1月20日  
**状态**: ✅ 问题已修复  
**验证**: API连接正常工作
