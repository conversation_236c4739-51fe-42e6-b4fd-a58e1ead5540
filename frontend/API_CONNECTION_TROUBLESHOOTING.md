# API连接问题排查指南

## 🚨 问题描述

您遇到的问题是无法正确请求企业统计信息API (`/api/enterprises/stats`)，这通常是由以下几个原因造成的：

1. **后端服务未启动或端口配置错误**
2. **API路径配置不正确**
3. **认证令牌问题**
4. **CORS跨域问题**
5. **网络连接问题**

## 🔧 快速解决方案

### 方案1: 使用自动诊断工具

我们已经为您创建了自动诊断和修复工具：

1. **在浏览器控制台中运行**：
```javascript
// 一键诊断和修复
window.fixApi()

// 或者分步骤诊断
window.debugApi.quickFix()
```

2. **在企业列表页面**：
   - 开发环境下会显示"API调试面板"
   - 点击"快速诊断"按钮
   - 查看诊断结果和修复建议

### 方案2: 手动检查和修复

#### 步骤1: 检查后端服务

```bash
# 检查后端服务是否运行在8081端口
curl http://localhost:8081/api/health

# 如果上面失败，尝试其他端口
curl http://localhost:8080/api/health
curl http://localhost:3000/api/health
```

#### 步骤2: 检查API配置

1. **检查环境变量文件** (`.env.development`):
```env
VUE_APP_API_BASE_URL=http://localhost:8081/api
```

2. **检查constants.js配置**:
```javascript
export const API_CONFIG = {
  BASE_URL: '/api',  // 或者完整URL
  TIMEOUT: 30000
}
```

#### 步骤3: 检查认证令牌

```javascript
// 在浏览器控制台检查令牌
console.log('Token:', localStorage.getItem('token'))
console.log('Refresh Token:', localStorage.getItem('refresh_token'))
```

如果令牌为空或过期，请重新登录。

#### 步骤4: 测试API端点

```bash
# 使用您提供的curl命令测试
curl 'http://localhost:8080/api/enterprises/stats' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'

# 或者尝试直接访问后端
curl 'http://localhost:8081/api/enterprises/stats' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'
```

## 🛠️ 详细修复步骤

### 修复1: 端口配置问题

**问题**: 前端在8080端口，但API请求也发送到8080端口

**解决方案**:
1. 创建或更新 `.env.development` 文件：
```env
VUE_APP_API_BASE_URL=http://localhost:8081/api
```

2. 重启前端开发服务器：
```bash
npm run serve
# 或
yarn serve
```

### 修复2: 后端服务未启动

**检查后端服务**:
```bash
# 进入后端目录
cd backend

# 启动后端服务
go run main.go
# 或
./tax-filing-service
```

**确认后端端口**:
- 检查后端配置文件中的端口设置
- 通常应该是8081端口

### 修复3: 认证问题

**重新获取令牌**:
1. 清除旧令牌：
```javascript
localStorage.removeItem('token')
localStorage.removeItem('refresh_token')
```

2. 重新登录获取新令牌

3. 确认令牌格式正确（JWT格式）

### 修复4: CORS问题

**后端CORS配置** (Go Gin):
```go
// 在后端添加CORS中间件
router.Use(func(c *gin.Context) {
    c.Header("Access-Control-Allow-Origin", "http://localhost:8080")
    c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
    c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
    
    if c.Request.Method == "OPTIONS" {
        c.AbortWithStatus(204)
        return
    }
    
    c.Next()
})
```

### 修复5: 代理配置

**Vue开发服务器代理** (`vue.config.js`):
```javascript
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        }
      }
    }
  }
}
```

## 🔍 调试工具使用

### 1. API调试面板

在开发环境的企业列表页面底部会显示API调试面板：

- **配置检查**: 显示当前API配置
- **连接测试**: 测试各个API端点
- **快速诊断**: 自动诊断问题并提供修复建议

### 2. 浏览器控制台工具

```javascript
// 检查API配置
window.debugApi.checkConfig()

// 测试企业统计API
window.debugApi.testStats('YOUR_TOKEN')

// 测试后端连接
window.debugApi.testBackend()

// 一键修复
window.fixApi()
```

### 3. 网络面板调试

1. 打开浏览器开发者工具
2. 切换到"Network"面板
3. 刷新页面或触发API请求
4. 查看请求详情：
   - 请求URL是否正确
   - 请求头是否包含Authorization
   - 响应状态码和内容

## 📋 常见问题解答

### Q1: 为什么API请求发送到8080端口而不是8081？

**A**: 这是因为前端的API基础URL配置不正确。解决方案：
1. 设置环境变量 `VUE_APP_API_BASE_URL=http://localhost:8081/api`
2. 或者配置开发服务器代理

### Q2: 认证令牌从哪里获取？

**A**: 认证令牌通过登录接口获取：
1. 调用 `/auth/login` 接口
2. 成功后会返回 `accessToken`
3. 将令牌存储在 `localStorage` 中

### Q3: 如何确认后端服务正常运行？

**A**: 可以通过以下方式检查：
```bash
# 检查健康状态
curl http://localhost:8081/api/health

# 检查进程
ps aux | grep tax-filing-service

# 检查端口占用
lsof -i :8081
```

### Q4: CORS错误如何解决？

**A**: 需要在后端配置CORS：
1. 允许前端域名 (http://localhost:8080)
2. 允许必要的HTTP方法和头部
3. 处理OPTIONS预检请求

## 🚀 预防措施

1. **环境配置标准化**:
   - 使用 `.env` 文件管理环境变量
   - 为不同环境创建不同的配置文件

2. **API文档维护**:
   - 保持API文档更新
   - 记录端口和路径变更

3. **自动化测试**:
   - 添加API连接测试
   - 集成到CI/CD流程

4. **监控和日志**:
   - 添加API请求日志
   - 监控后端服务状态

## 📞 获取帮助

如果问题仍然存在：

1. **查看调试工具输出**: 使用我们提供的调试工具获取详细信息
2. **检查后端日志**: 查看后端服务的错误日志
3. **网络抓包**: 使用Wireshark等工具分析网络请求
4. **联系开发团队**: 提供完整的错误信息和环境配置

## 🎯 总结

通过以上步骤，您应该能够解决API连接问题。关键点：

1. ✅ 确认后端服务在正确端口运行
2. ✅ 配置正确的API基础URL
3. ✅ 确保认证令牌有效
4. ✅ 解决CORS跨域问题
5. ✅ 使用调试工具辅助排查

记住：**先使用自动诊断工具 `window.fixApi()`，它会帮您快速定位和解决大部分问题！**
