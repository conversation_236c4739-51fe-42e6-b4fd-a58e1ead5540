<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册功能调试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f7;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .form-group {
            margin-bottom: 16px;
        }
        label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
        }
        input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        button {
            background: #007aff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px 4px;
            font-size: 14px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 12px;
            border-radius: 8px;
            margin: 8px 0;
            font-weight: 500;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 注册功能调试工具</h1>
        
        <div class="status info">
            <strong>说明:</strong> 此工具用于测试注册API的连接和响应
        </div>

        <h2>📝 注册表单</h2>
        <form id="registerForm">
            <div class="form-group">
                <label for="phone">手机号 *</label>
                <input type="tel" id="phone" name="phone" value="13800138000" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码 *</label>
                <input type="password" id="password" name="password" value="123456" required>
            </div>
            
            <div class="form-group">
                <label for="name">姓名 *</label>
                <input type="text" id="name" name="name" value="测试用户" required>
            </div>
            
            <div class="form-group">
                <label for="email">邮箱</label>
                <input type="email" id="email" name="email" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="enterprise">企业名称</label>
                <input type="text" id="enterprise" name="enterprise" value="测试企业">
            </div>
            
            <div class="form-group">
                <label for="department">部门</label>
                <input type="text" id="department" name="department" value="技术部">
            </div>
            
            <div class="form-group">
                <label for="position">职位</label>
                <input type="text" id="position" name="position" value="开发工程师">
            </div>
            
            <button type="submit" id="submitBtn">测试注册</button>
            <button type="button" onclick="clearLog()">清空日志</button>
        </form>

        <div id="results"></div>
        <div class="log" id="log"></div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#0c5460',
                success: '#155724',
                error: '#721c24',
                warning: '#856404'
            };
            logElement.innerHTML += `<div style="color: ${colors[type]};">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('results').innerHTML = '';
        }

        function showResult(type, title, message) {
            const resultsElement = document.getElementById('results');
            resultsElement.innerHTML = `
                <div class="status ${type}">
                    <strong>${title}:</strong> ${message}
                </div>
            `;
        }

        // 获取API基础URL
        function getApiBaseUrl() {
            const currentPort = window.location.port;
            const protocol = window.location.protocol;
            const hostname = window.location.hostname;

            if (currentPort === '8080' || currentPort === '8082') {
                return `${protocol}//${hostname}:8081/api`;
            }
            if (currentPort === '3000') {
                return `${protocol}//${hostname}:8081/api`;
            }
            return '/api';
        }

        // 测试注册API
        async function testRegister(formData) {
            const apiBaseUrl = getApiBaseUrl();
            const registerUrl = `${apiBaseUrl}/auth/register`;
            
            log(`开始测试注册API...`, 'info');
            log(`请求URL: ${registerUrl}`, 'info');
            log(`请求数据: ${JSON.stringify(formData, null, 2)}`, 'info');
            
            try {
                const response = await fetch(registerUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json, text/plain, */*'
                    },
                    body: JSON.stringify(formData)
                });

                log(`响应状态: ${response.status} ${response.statusText}`, 'info');
                
                const responseData = await response.json();
                log(`响应数据: ${JSON.stringify(responseData, null, 2)}`, 'info');

                if (response.ok) {
                    log(`✅ 注册API调用成功!`, 'success');
                    showResult('success', '注册成功', `状态码: ${response.status}`);
                    
                    if (responseData.data && responseData.data.accessToken) {
                        log(`✅ 获取到访问令牌`, 'success');
                    }
                } else {
                    log(`❌ 注册API调用失败!`, 'error');
                    showResult('error', '注册失败', responseData.message || `状态码: ${response.status}`);
                }
                
                return responseData;
            } catch (error) {
                log(`❌ 网络请求失败: ${error.message}`, 'error');
                showResult('error', '网络错误', error.message);
                
                // 提供解决建议
                if (error.message.includes('Failed to fetch')) {
                    log('💡 可能的解决方案:', 'warning');
                    log('1. 检查后端服务是否在8081端口运行', 'warning');
                    log('2. 检查CORS配置是否正确', 'warning');
                    log('3. 检查网络连接', 'warning');
                }
                
                throw error;
            }
        }

        // 表单提交处理
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = '测试中...';
            
            try {
                const formData = new FormData(e.target);
                const data = {
                    phone: formData.get('phone'),
                    password: formData.get('password'),
                    name: formData.get('name'),
                    email: formData.get('email') || '',
                    enterprise: formData.get('enterprise') || '',
                    department: formData.get('department') || '',
                    position: formData.get('position') || ''
                };
                
                await testRegister(data);
            } catch (error) {
                console.error('测试失败:', error);
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '测试注册';
            }
        });

        // 页面加载时显示配置
        window.onload = function() {
            log('注册功能调试工具已加载', 'info');
            log(`API基础URL: ${getApiBaseUrl()}`, 'info');
            log('填写表单信息并点击"测试注册"开始测试', 'info');
        };
    </script>
</body>
</html>
