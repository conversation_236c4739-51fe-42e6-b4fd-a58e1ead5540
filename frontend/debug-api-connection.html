<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接调试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f7;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .status {
            padding: 12px;
            border-radius: 8px;
            margin: 8px 0;
            font-weight: 500;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007aff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px 4px;
            font-size: 14px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .config-item:last-child { border-bottom: none; }
        .config-label { font-weight: 500; }
        .config-value { font-family: monospace; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 API连接调试工具</h1>
        
        <div class="status info">
            <strong>当前状态:</strong> 正在检查API连接...
        </div>

        <h2>📋 当前配置</h2>
        <div id="config">
            <div class="config-item">
                <span class="config-label">当前URL:</span>
                <span class="config-value" id="currentUrl">-</span>
            </div>
            <div class="config-item">
                <span class="config-label">前端端口:</span>
                <span class="config-value" id="frontendPort">-</span>
            </div>
            <div class="config-item">
                <span class="config-label">推断的API基础URL:</span>
                <span class="config-value" id="apiBaseUrl">-</span>
            </div>
            <div class="config-item">
                <span class="config-label">环境变量API URL:</span>
                <span class="config-value" id="envApiUrl">-</span>
            </div>
        </div>

        <h2>🧪 连接测试</h2>
        <div>
            <button onclick="testApiConnection()">测试API连接</button>
            <button onclick="testEnterpriseStats()">测试企业统计API</button>
            <button onclick="testWithDifferentPorts()">测试不同端口</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div id="results"></div>
        <div class="log" id="log"></div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#0c5460',
                success: '#155724',
                error: '#721c24',
                warning: '#856404'
            };
            logElement.innerHTML += `<div style="color: ${colors[type]};">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('results').innerHTML = '';
        }

        // 获取API基础URL
        function getApiBaseUrl() {
            const currentPort = window.location.port;
            const protocol = window.location.protocol;
            const hostname = window.location.hostname;

            // 模拟前端的逻辑
            if (currentPort === '8080' || currentPort === '8082') {
                return `${protocol}//${hostname}:8081/api`;
            }
            if (currentPort === '3000') {
                return `${protocol}//${hostname}:8081/api`;
            }
            return '/api';
        }

        // 显示当前配置
        function showCurrentConfig() {
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('frontendPort').textContent = window.location.port || '80';
            document.getElementById('apiBaseUrl').textContent = getApiBaseUrl();
            document.getElementById('envApiUrl').textContent = 'http://localhost:8081/api (配置值)';
        }

        // 测试API连接
        async function testApiConnection() {
            log('开始测试API连接...', 'info');
            
            const apiBaseUrl = getApiBaseUrl();
            const testUrl = `${apiBaseUrl}/enterprises/stats`;
            
            try {
                log(`测试URL: ${testUrl}`, 'info');
                
                const response = await fetch(testUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbWFpbCI6ImFkbWluMDA0QHNtZWFzeS5jb20iLCJleHAiOjE3NTMwNzg5NTEsImZ1bGxfbmFtZSI6Iui1teWFrSIsImlhdCI6MTc1Mjk5MjU1MSwiaWQiOiJ1c2VyXzAwNCIsImlzcyI6InRheC1zeXN0ZW0iLCJyb2xlX2lkIjoiIiwidHlwZSI6ImFjY2VzcyJ9.YUWtlyCPt0sjRKkM4GwSWT71bD6mewWuJYOQDTimjxo'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ API连接成功! 状态码: ${response.status}`, 'success');
                    log(`响应数据: ${JSON.stringify(data, null, 2)}`, 'success');
                    showResult('success', 'API连接正常', `成功连接到 ${testUrl}`);
                } else {
                    log(`❌ API响应错误! 状态码: ${response.status}`, 'error');
                    const errorText = await response.text();
                    log(`错误响应: ${errorText}`, 'error');
                    showResult('error', 'API响应错误', `状态码: ${response.status}`);
                }
            } catch (error) {
                log(`❌ 网络连接失败: ${error.message}`, 'error');
                showResult('error', '网络连接失败', error.message);
                
                // 提供解决建议
                if (error.message.includes('Failed to fetch')) {
                    log('💡 可能的解决方案:', 'warning');
                    log('1. 检查后端服务是否在8081端口运行', 'warning');
                    log('2. 检查CORS配置是否正确', 'warning');
                    log('3. 检查防火墙设置', 'warning');
                }
            }
        }

        // 测试企业统计API
        async function testEnterpriseStats() {
            log('测试企业统计API...', 'info');
            await testApiConnection();
        }

        // 测试不同端口
        async function testWithDifferentPorts() {
            log('测试不同端口的API连接...', 'info');
            
            const ports = [8081, 8080, 3000, 8082];
            const protocol = window.location.protocol;
            const hostname = window.location.hostname;
            
            for (const port of ports) {
                const testUrl = `${protocol}//${hostname}:${port}/api/enterprises/stats`;
                log(`测试端口 ${port}: ${testUrl}`, 'info');
                
                try {
                    const response = await fetch(testUrl, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbWFpbCI6ImFkbWluMDA0QHNtZWFzeS5jb20iLCJleHAiOjE3NTMwNzg5NTEsImZ1bGxfbmFtZSI6Iui1teWFrSIsImlhdCI6MTc1Mjk5MjU1MSwiaWQiOiJ1c2VyXzAwNCIsImlzcyI6InRheC1zeXN0ZW0iLCJyb2xlX2lkIjoiIiwidHlwZSI6ImFjY2VzcyJ9.YUWtlyCPt0sjRKkM4GwSWT71bD6mewWuJYOQDTimjxo'
                        },
                        timeout: 5000
                    });
                    
                    if (response.ok) {
                        log(`✅ 端口 ${port} 连接成功!`, 'success');
                    } else {
                        log(`❌ 端口 ${port} 响应错误: ${response.status}`, 'error');
                    }
                } catch (error) {
                    log(`❌ 端口 ${port} 连接失败: ${error.message}`, 'error');
                }
            }
        }

        // 显示结果
        function showResult(type, title, message) {
            const resultsElement = document.getElementById('results');
            resultsElement.innerHTML = `
                <div class="status ${type}">
                    <strong>${title}:</strong> ${message}
                </div>
            `;
        }

        // 页面加载时显示配置
        window.onload = function() {
            showCurrentConfig();
            log('API连接调试工具已加载', 'info');
            log('点击上方按钮开始测试API连接', 'info');
        };
    </script>
</body>
</html>
