#!/usr/bin/env node

/**
 * 项目结构验证脚本
 * 验证前端项目规范化重构后的目录结构和文件引用是否正确
 */

const fs = require('fs')
const path = require('path')

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 项目根目录
const projectRoot = path.join(__dirname, '..')

// 验证结果
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  errors: []
}

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath, description) {
  const fullPath = path.join(projectRoot, filePath)
  if (fs.existsSync(fullPath)) {
    log(`✅ ${description}: ${filePath}`, 'green')
    results.passed++
    return true
  } else {
    log(`❌ ${description}: ${filePath}`, 'red')
    results.failed++
    results.errors.push(`Missing file: ${filePath}`)
    return false
  }
}

/**
 * 检查文件不存在（应该被删除的文件）
 */
function checkFileNotExists(filePath, description) {
  const fullPath = path.join(projectRoot, filePath)
  if (!fs.existsSync(fullPath)) {
    log(`✅ ${description}: ${filePath}`, 'green')
    results.passed++
    return true
  } else {
    log(`⚠️ ${description}: ${filePath}`, 'yellow')
    results.warnings++
    return false
  }
}

/**
 * 检查文件内容是否包含指定字符串
 */
function checkFileContent(filePath, searchString, description) {
  const fullPath = path.join(projectRoot, filePath)
  if (!fs.existsSync(fullPath)) {
    log(`❌ ${description}: 文件不存在 ${filePath}`, 'red')
    results.failed++
    return false
  }

  try {
    const content = fs.readFileSync(fullPath, 'utf8')
    if (content.includes(searchString)) {
      log(`✅ ${description}: ${filePath}`, 'green')
      results.passed++
      return true
    } else {
      log(`❌ ${description}: ${filePath}`, 'red')
      results.failed++
      results.errors.push(`File ${filePath} does not contain: ${searchString}`)
      return false
    }
  } catch (error) {
    log(`❌ ${description}: 读取文件失败 ${filePath}`, 'red')
    results.failed++
    results.errors.push(`Cannot read file: ${filePath}`)
    return false
  }
}

/**
 * 检查文件内容不包含指定字符串（已删除的引用）
 */
function checkFileNotContains(filePath, searchString, description) {
  const fullPath = path.join(projectRoot, filePath)
  if (!fs.existsSync(fullPath)) {
    log(`⚠️ ${description}: 文件不存在 ${filePath}`, 'yellow')
    results.warnings++
    return false
  }

  try {
    const content = fs.readFileSync(fullPath, 'utf8')
    if (!content.includes(searchString)) {
      log(`✅ ${description}: ${filePath}`, 'green')
      results.passed++
      return true
    } else {
      log(`❌ ${description}: ${filePath}`, 'red')
      results.failed++
      results.errors.push(`File ${filePath} still contains: ${searchString}`)
      return false
    }
  } catch (error) {
    log(`❌ ${description}: 读取文件失败 ${filePath}`, 'red')
    results.failed++
    return false
  }
}

/**
 * 主验证函数
 */
function validateProjectStructure() {
  log('🔍 开始验证项目结构...', 'blue')
  log('')

  // 1. 验证合并后的API文件存在
  log('📁 验证API文件结构:', 'blue')
  checkFileExists('src/api/user.js', '用户管理API（已合并）')
  checkFileExists('src/api/invoice.js', '发票管理API（已合并）')
  
  // 2. 验证删除的API文件不存在
  log('\n🗑️ 验证已删除的API文件:', 'blue')
  checkFileNotExists('src/api/userManagement.js', '已删除的用户管理API')
  checkFileNotExists('src/api/invoiceItem.js', '已删除的发票明细API')

  // 3. 验证布局组件位置
  log('\n🏗️ 验证布局组件结构:', 'blue')
  checkFileExists('src/components/layout/Layout.vue', '主布局组件')
  checkFileExists('src/components/layout/SideMenu.vue', '侧边菜单组件')
  checkFileExists('src/components/layout/BreadCrumb.vue', '面包屑组件')
  checkFileExists('src/components/layout/RouteView.vue', '路由视图组件')

  // 4. 验证删除的布局目录
  checkFileNotExists('src/views/layout', '已删除的views/layout目录')

  // 5. 验证用户管理目录结构
  log('\n👥 验证用户管理目录结构:', 'blue')
  checkFileExists('src/views/user/UserList.vue', '用户列表页面')
  checkFileExists('src/views/user/RoleManagement.vue', '角色管理页面')
  checkFileExists('src/views/user/PermissionManagement.vue', '权限管理页面')
  checkFileExists('src/views/user/components/InviteUserModal.vue', '邀请用户模态框')

  // 6. 验证删除的用户管理目录
  checkFileNotExists('src/views/user-management', '已删除的user-management目录')
  checkFileNotExists('src/views/permissions', '已删除的permissions目录')

  // 7. 验证删除的空目录
  log('\n📂 验证已删除的空目录:', 'blue')
  checkFileNotExists('src/components/enterprise', '已删除的enterprise目录')
  checkFileNotExists('src/components/form', '已删除的form目录')
  checkFileNotExists('src/views/examples', '已删除的examples目录')
  checkFileNotExists('src/views/test', '已删除的test目录')

  // 8. 验证路由引用更新
  log('\n🛣️ 验证路由引用更新:', 'blue')
  checkFileContent('src/router/index.js', '@/components/layout/Layout.vue', '布局组件路由引用')
  checkFileContent('src/router/index.js', '@/views/user/UserList.vue', '用户列表路由引用')
  checkFileNotContains('src/router/index.js', '@/views/layout/', '旧布局路径引用已删除')
  checkFileNotContains('src/router/index.js', '@/views/user-management/', '旧用户管理路径引用已删除')

  // 9. 验证API引用更新
  log('\n🔗 验证API引用更新:', 'blue')
  checkFileNotContains('src/views/user/RoleManagement.vue', '@/api/userManagement', '角色管理API引用已更新')
  checkFileNotContains('src/views/user/PermissionManagement.vue', '@/api/userManagement', '权限管理API引用已更新')
  checkFileNotContains('src/components/business/InvoiceItemEditor.vue', '@/api/invoiceItem', '发票明细API引用已更新')

  // 10. 验证合并后的API文件内容
  log('\n📄 验证合并后的API文件内容:', 'blue')
  checkFileContent('src/api/user.js', 'getEnterpriseUsers', '用户API包含企业用户管理功能')
  checkFileContent('src/api/user.js', 'inviteUserToEnterprise', '用户API包含邀请用户功能')
  checkFileContent('src/api/invoice.js', 'createInvoiceItem', '发票API包含明细管理功能')
  checkFileContent('src/api/invoice.js', 'batchCreateInvoiceItems', '发票API包含批量操作功能')

  // 输出验证结果
  log('\n📊 验证结果统计:', 'blue')
  log(`✅ 通过: ${results.passed}`, 'green')
  log(`❌ 失败: ${results.failed}`, 'red')
  log(`⚠️ 警告: ${results.warnings}`, 'yellow')

  if (results.errors.length > 0) {
    log('\n❌ 错误详情:', 'red')
    results.errors.forEach(error => {
      log(`  - ${error}`, 'red')
    })
  }

  if (results.failed === 0) {
    log('\n🎉 项目结构验证通过！', 'green')
    return true
  } else {
    log('\n💥 项目结构验证失败，请检查上述错误。', 'red')
    return false
  }
}

// 运行验证
if (require.main === module) {
  const success = validateProjectStructure()
  process.exit(success ? 0 : 1)
}

module.exports = { validateProjectStructure }
