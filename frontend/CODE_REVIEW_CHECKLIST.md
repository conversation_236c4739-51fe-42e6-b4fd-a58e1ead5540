# 前端代码审查清单

## 📋 概述

本清单基于 `API_DATA_STANDARDS.md`、`VUE_STANDARDS.md` 和 `FRONTEND_DEVELOPMENT_GUIDE.md` 规范，用于确保代码质量和一致性。

## 🔍 API调用审查

### ✅ 必须检查项

- [ ] **使用标准化API工具函数**
  - 使用 `createPaginatedApiCall`、`createEntityApiCall`、`createOperationApiCall` 等
  - 避免直接在组件中处理API响应格式

- [ ] **API响应格式符合标准**
  - 所有API返回统一的 `{code, message, data, timestamp, requestId}` 格式
  - 分页数据使用 `items` 字段，不使用 `data` 或 `list`

- [ ] **错误处理统一**
  - 使用统一的错误处理机制
  - 避免重复的 `try-catch` 和错误提示代码

- [ ] **JSDoc注释完整**
  - API函数有完整的参数和返回值注释
  - 使用类型定义提供IDE提示

### ❌ 禁止的做法

```javascript
// ❌ 错误：直接处理API响应
const response = await getEnterprises(params)
if (response.code === 200) {
  dataSource.value = response.data.data || response.data.items || []
}

// ❌ 错误：兼容性处理代码
const items = response.data?.items || response.data?.data || response.data || []

// ❌ 错误：重复的错误处理
try {
  const response = await api()
  if (response.code === 200) {
    // 处理成功
  } else {
    message.error(response.message)
  }
} catch (error) {
  message.error('请求失败')
}
```

### ✅ 推荐的做法

```javascript
// ✅ 正确：使用标准化API工具
export const getEnterprises = createPaginatedApiCall(_getEnterprises)

// ✅ 正确：使用标准化Composable
const {
  loading,
  dataSource,
  pagination,
  fetchData,
  handleTableChange,
  search,
  reset
} = useStandardPagination(getEnterprises, {
  defaultParams: { status: 'active' }
})
```

## 🏗️ 组件结构审查

### ✅ 必须检查项

- [ ] **组件命名规范**
  - 文件名使用 PascalCase（如 `UserList.vue`）
  - 组件名使用多词命名避免与HTML元素冲突

- [ ] **模板结构清晰**
  - 模板、脚本、样式按顺序排列
  - 使用语义化HTML元素

- [ ] **Props和Emits声明**
  - 明确声明所有props和emits
  - 添加类型注释和默认值

- [ ] **Composition API规范**
  - 按推荐顺序组织setup函数内容
  - 使用标准化的Composable函数

### ✅ 组件结构模板

```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue'

/**
 * @typedef {import('@/types/api').User} User
 */

export default defineComponent({
  name: 'ComponentName',
  components: {
    // 组件注册
  },
  props: {
    // props定义
  },
  emits: ['update', 'delete'],
  setup(props, { emit }) {
    // 1. 导入的组合式函数
    // 2. 响应式数据
    // 3. 计算属性
    // 4. 方法
    // 5. 生命周期钩子
    // 6. 返回语句
  }
})
</script>

<style scoped>
/* 样式内容 */
</style>
```

## 🎨 样式审查

### ✅ 必须检查项

- [ ] **使用scoped样式**
  - 所有组件样式使用 `scoped` 属性
  - 避免全局样式污染

- [ ] **BEM命名规范**
  - 使用 Block__Element--Modifier 模式
  - 类名语义化且易于理解

- [ ] **CSS变量使用**
  - 使用全局CSS变量定义颜色、间距等
  - 保持设计系统一致性

### ✅ 样式规范示例

```scss
.user-list {
  // Block
  padding: var(--spacing-lg);
  
  &__header {
    // Element
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
  }
  
  &__search {
    // Element
    background: var(--color-surface);
    border-radius: var(--radius-base);
  }
  
  &--loading {
    // Modifier
    opacity: 0.6;
    pointer-events: none;
  }
}
```

## 📊 数据处理审查

### ✅ 必须检查项

- [ ] **响应式数据正确使用**
  - 简单类型使用 `ref`
  - 复杂对象使用 `reactive`
  - 只读数据使用 `readonly`

- [ ] **计算属性vs方法**
  - 复杂数据处理使用计算属性
  - 避免在模板中使用复杂表达式

- [ ] **状态管理规范**
  - 合理使用Pinia store
  - 避免过度使用全局状态

### ✅ 数据处理示例

```javascript
// ✅ 正确：响应式数据使用
const loading = ref(false)
const searchForm = reactive({
  keyword: '',
  status: ''
})

// ✅ 正确：计算属性
const filteredUsers = computed(() => {
  return users.value.filter(user => 
    user.name.includes(searchForm.keyword)
  )
})
```

## 🔧 性能审查

### ✅ 必须检查项

- [ ] **组件懒加载**
  - 路由组件使用动态导入
  - 非关键组件使用 `defineAsyncComponent`

- [ ] **避免不必要的重渲染**
  - 合理使用 `v-memo`
  - 避免在模板中创建新对象

- [ ] **列表渲染优化**
  - 使用正确的 `key` 值
  - 大列表考虑虚拟滚动

### ✅ 性能优化示例

```javascript
// ✅ 正确：路由懒加载
const routes = [
  {
    path: '/user',
    component: () => import('@/views/user/List.vue')
  }
]

// ✅ 正确：异步组件
const AsyncChart = defineAsyncComponent(() => import('./Chart.vue'))
```

## 🧪 测试审查

### ✅ 必须检查项

- [ ] **单元测试覆盖**
  - 关键业务逻辑有单元测试
  - 组件渲染测试

- [ ] **API Mock正确**
  - 测试中正确Mock API调用
  - 测试不同的响应场景

- [ ] **用户交互测试**
  - 测试用户操作流程
  - 测试错误处理

### ✅ 测试示例

```javascript
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import UserList from '@/views/user/List.vue'
import * as userApi from '@/api/user'

vi.mock('@/api/user')

describe('UserList', () => {
  it('should render user list correctly', async () => {
    userApi.getUserList.mockResolvedValue({
      code: 200,
      data: {
        items: [{ id: '1', name: '测试用户' }],
        total: 1
      }
    })

    const wrapper = mount(UserList)
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('.user-list').exists()).toBe(true)
  })
})
```

## 🔒 安全审查

### ✅ 必须检查项

- [ ] **输入验证**
  - 前端表单验证完整
  - 使用白名单验证方式

- [ ] **XSS防护**
  - 避免使用 `v-html`
  - 用户输入内容正确转义

- [ ] **敏感信息处理**
  - 不在前端存储敏感数据
  - 使用HTTPS传输

## 📝 文档审查

### ✅ 必须检查项

- [ ] **JSDoc注释完整**
  - 函数有完整的参数和返回值说明
  - 复杂逻辑有详细注释

- [ ] **组件文档**
  - 组件有使用示例
  - Props和Events有说明

- [ ] **README更新**
  - 新功能更新文档
  - API变更说明

## 🎯 代码质量评分

### 优秀 (90-100分)
- [ ] 完全符合所有规范要求
- [ ] 代码结构清晰，可读性强
- [ ] 有完整的测试覆盖
- [ ] 性能优化到位

### 良好 (80-89分)
- [ ] 基本符合规范要求
- [ ] 代码结构合理
- [ ] 有基本的测试覆盖
- [ ] 无明显性能问题

### 需要改进 (60-79分)
- [ ] 部分不符合规范
- [ ] 代码结构需要优化
- [ ] 测试覆盖不足
- [ ] 存在性能问题

### 不合格 (<60分)
- [ ] 严重不符合规范
- [ ] 代码结构混乱
- [ ] 无测试覆盖
- [ ] 存在安全风险

## 📋 审查流程

1. **自检阶段**：开发者使用此清单自检代码
2. **同行审查**：团队成员进行代码审查
3. **技术负责人审查**：最终技术审查
4. **自动化检查**：ESLint、测试等自动化工具检查

## 🔧 工具支持

- **ESLint**: 代码规范检查
- **Prettier**: 代码格式化
- **Vitest**: 单元测试
- **Vue DevTools**: 调试工具
- **Lighthouse**: 性能检查

通过严格执行这套代码审查清单，确保税易通系统前端代码的高质量和一致性。
