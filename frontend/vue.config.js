module.exports = {
  lintOnSave: false,
  devServer: {
    port: 8080,
    historyApiFallback: true,
    client: {
      overlay: {
        warnings: true,
        errors: true
      }
    },
    proxy: {
      '/api': {
        target: 'http://localhost:8081',
        changeOrigin: true,
      },
      '/uploads': {
        target: 'http://localhost:8081',
        changeOrigin: true,
      }
    }
  },
  css: {
    loaderOptions: {
      sass: {
        // 使用新的Sass API
        implementation: require('sass'),
        sassOptions: {
          indentedSyntax: false
        }
      }
    }
  },
  configureWebpack: {
    ignoreWarnings: [
      warning =>
        typeof warning.message === 'string' &&
        warning.message.includes('The legacy JS API is deprecated')
    ]
  }
} 