# 前端开发规范指南

## 📋 概述

本文档基于 `API_DATA_STANDARDS.md` 和 `VUE_STANDARDS.md` 规范，为税易通系统前端开发提供统一的开发指南和最佳实践。

## 🎯 核心原则

1. **统一性**: 所有API调用使用统一的响应处理方式
2. **可预测性**: 前端可以依赖固定的数据结构和处理流程
3. **可维护性**: 减少重复代码，提高代码复用性
4. **类型安全**: 使用JSDoc提供类型提示和文档

## 🔧 API调用规范

### 1. 使用标准化的API工具函数

**推荐做法**：
```javascript
import { createPaginatedApiCall, createEntityApiCall, createOperationApiCall } from '@/utils/api-helper'

// 原始API函数
function _getEnterprises(params) {
  return request({ url: '/enterprises', method: 'get', params })
}

// 标准化的API函数
export const getEnterprises = createPaginatedApiCall(_getEnterprises)
export const getEnterpriseById = createEntityApiCall(_getEnterpriseById)
export const createEnterprise = createOperationApiCall(_createEnterprise, {
  successMessage: '创建企业成功'
})
```

**禁止做法**：
```javascript
// ❌ 直接在组件中处理API响应
const response = await getEnterprises(params)
if (response.code === 200) {
  dataSource.value = response.data.data || response.data.items || []
}
```

### 2. 使用标准化的Composable函数

**推荐做法**：
```javascript
import { useStandardPagination, useStandardApi, useStandardOperation } from '@/composables/useStandardApi'

// 分页数据
const {
  loading,
  dataSource,
  pagination,
  fetchData,
  handleTableChange,
  search,
  reset,
  refresh
} = useStandardPagination(getEnterprises, {
  defaultParams: { status: 'active' },
  pageSize: 10
})

// 单个实体
const {
  loading: detailLoading,
  entity,
  fetchEntity
} = useStandardEntity(getEnterpriseById)

// 操作
const {
  loading: deleteLoading,
  execute: executeDelete
} = useStandardOperation(deleteEnterprise, {
  successMessage: '删除成功',
  onSuccess: () => refresh()
})
```

## 📊 数据格式规范

### 1. API响应格式

所有API响应必须遵循以下格式：
```javascript
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1642694400,
  "requestId": "req_123456"
}
```

### 2. 分页数据格式

分页数据必须使用 `items` 字段：
```javascript
{
  "code": 200,
  "message": "获取数据成功",
  "data": {
    "items": [],      // 统一使用items字段
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "pages": 10
  }
}
```

### 3. 禁止的格式

```javascript
// ❌ 错误格式 - 使用data字段
{
  "data": {
    "data": [],  // 应该使用items
    "total": 100
  }
}

// ❌ 错误格式 - 使用list字段
{
  "data": {
    "list": [],  // 应该使用items
    "total": 100
  }
}
```

## 🏗️ 组件开发规范

### 1. 组件结构

```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { useStandardPagination } from '@/composables/useStandardApi'

/**
 * @typedef {import('@/types/api').Enterprise} Enterprise
 * @typedef {import('@/types/api').EnterpriseSearchParams} EnterpriseSearchParams
 */

export default defineComponent({
  name: 'ComponentName',
  components: {
    // 组件注册
  },
  props: {
    // props定义
  },
  emits: ['update', 'delete'],
  setup(props, { emit }) {
    // 1. 导入的组合式函数
    // 2. 响应式数据
    // 3. 计算属性
    // 4. 方法
    // 5. 生命周期钩子
    // 6. 返回语句
  }
})
</script>

<style scoped>
/* 样式内容 */
</style>
```

### 2. JSDoc注释规范

```javascript
/**
 * 获取企业列表
 * @param {EnterpriseSearchParams} params - 搜索参数
 * @returns {Promise<PaginatedResponse<Enterprise>>} 分页企业数据
 */
export function getEnterprises(params) {
  return request({
    url: '/enterprises',
    method: 'get',
    params
  })
}

/**
 * 企业管理组件
 * @component
 * @example
 * <EnterpriseList />
 */
export default defineComponent({
  name: 'EnterpriseList',
  setup() {
    /**
     * 搜索表单数据
     * @type {import('vue').Reactive<EnterpriseSearchParams>}
     */
    const searchForm = reactive({
      keyword: '',
      industry: '',
      status: ''
    })

    return {
      searchForm
    }
  }
})
```

## 🔄 状态管理规范

### 1. Pinia Store结构

```javascript
import { defineStore } from 'pinia'
import { getEnterprises, getEnterpriseStats } from '@/api/enterprise'
import { handleApiResponse, handlePaginatedResponse } from '@/utils/api-helper'

/**
 * 企业管理Store
 */
export const useEnterpriseStore = defineStore('enterprise', {
  state: () => ({
    /** @type {Enterprise[]} */
    enterprises: [],
    /** @type {EnterpriseStats|null} */
    stats: null,
    loading: false
  }),

  getters: {
    activeEnterprises: (state) => state.enterprises.filter(e => e.status === 'active'),
    totalCount: (state) => state.stats?.total || 0
  },

  actions: {
    /**
     * 获取企业列表
     * @param {EnterpriseSearchParams} params - 搜索参数
     */
    async fetchEnterprises(params) {
      try {
        this.loading = true
        const response = await getEnterprises(params)
        const data = handlePaginatedResponse(response)
        this.enterprises = data.items
        return data
      } catch (error) {
        console.error('获取企业列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取统计信息
     */
    async fetchStats() {
      try {
        const response = await getEnterpriseStats()
        this.stats = handleApiResponse(response)
      } catch (error) {
        console.error('获取统计信息失败:', error)
        throw error
      }
    }
  }
})
```

## 🎨 样式规范

### 1. CSS变量使用

```scss
// 使用全局CSS变量
.enterprise-card {
  background: var(--color-surface);
  border-radius: var(--radius-base);
  padding: var(--spacing-md);
  transition: var(--transition-base);
}
```

### 2. BEM命名规范

```scss
.enterprise-list {
  // Block
  
  &__header {
    // Element
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  &__search {
    // Element
    margin-bottom: var(--spacing-md);
  }
  
  &--loading {
    // Modifier
    opacity: 0.6;
    pointer-events: none;
  }
}
```

## 🧪 测试规范

### 1. 组件测试

```javascript
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import EnterpriseList from '@/views/enterprise/List.vue'
import * as enterpriseApi from '@/api/enterprise'

// Mock API
vi.mock('@/api/enterprise')

describe('EnterpriseList', () => {
  it('should render enterprise list correctly', async () => {
    // Mock API response
    enterpriseApi.getEnterprises.mockResolvedValue({
      code: 200,
      data: {
        items: [
          { id: '1', name: '测试企业', status: 'active' }
        ],
        total: 1,
        page: 1,
        pageSize: 10,
        pages: 1
      }
    })

    const wrapper = mount(EnterpriseList)
    
    // 等待异步操作完成
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('.enterprise-list').exists()).toBe(true)
    expect(enterpriseApi.getEnterprises).toHaveBeenCalled()
  })
})
```

## 🚨 错误处理规范

### 1. 统一错误处理

```javascript
// 在api-helper.js中统一处理
export function handleApiResponse(response) {
  if (!response) {
    throw new Error('响应数据为空')
  }

  if (response.code === 200) {
    return response.data
  } else {
    const errorMessage = response.message || '请求失败'
    throw new Error(errorMessage)
  }
}

// 在组件中使用
try {
  const data = await executeOperation(params)
  // 处理成功逻辑
} catch (error) {
  // 错误已经在api-helper中统一处理和显示
  console.error('操作失败:', error)
}
```

## 📋 代码审查清单

### API调用检查
- [ ] 使用标准化的API工具函数
- [ ] 使用统一的响应处理方式
- [ ] 分页数据使用`items`字段
- [ ] 错误处理统一且用户友好

### 组件检查
- [ ] 组件使用多词命名
- [ ] 文件名使用PascalCase
- [ ] Props和emits明确声明
- [ ] 使用scoped样式
- [ ] 添加JSDoc注释

### 代码质量检查
- [ ] 使用标准化的Composable函数
- [ ] 避免重复的API响应处理代码
- [ ] 遵循Vue 3 Composition API规范
- [ ] CSS使用BEM命名方法论

### 性能检查
- [ ] 路由组件使用懒加载
- [ ] 复杂逻辑使用计算属性
- [ ] 避免不必要的响应式数据

## 🔧 开发工具配置

### 1. VSCode设置

```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "vetur.validation.template": false,
  "vetur.validation.script": false,
  "vetur.validation.style": false
}
```

### 2. ESLint规则

```javascript
module.exports = {
  extends: [
    'plugin:vue/vue3-essential',
    '@vue/standard'
  ],
  rules: {
    'vue/multi-word-component-names': 'error',
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/component-name-in-template-casing': ['error', 'PascalCase'],
    'vue/prop-name-casing': ['error', 'camelCase']
  }
}
```

## 📚 参考资源

- [API_DATA_STANDARDS.md](./API_DATA_STANDARDS.md) - API数据标准
- [VUE_STANDARDS.md](./VUE_STANDARDS.md) - Vue开发规范
- [Vue 3官方文档](https://vuejs.org/)
- [Ant Design Vue文档](https://antdv.com/)
- [Pinia文档](https://pinia.vuejs.org/)

## 🎯 总结

通过遵循这套前端开发规范，我们实现了：

1. **统一的API调用方式**: 消除了兼容性处理代码
2. **标准化的数据格式**: 确保所有分页数据使用`items`字段
3. **可复用的组件逻辑**: 通过Composable函数减少重复代码
4. **类型安全**: 使用JSDoc提供完整的类型提示
5. **一致的错误处理**: 统一的错误处理和用户提示

这套规范确保了税易通系统前端代码的一致性、可维护性和开发效率。
