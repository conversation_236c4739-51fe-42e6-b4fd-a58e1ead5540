<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 200px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>登录功能调试页面</h1>
    
    <div class="test-section">
        <h2>1. 测试后端登录接口</h2>
        <div>
            <input type="text" id="phone" placeholder="手机号" value="13800138023">
            <input type="password" id="password" placeholder="密码" value="12345678">
            <button onclick="testBackendLogin()">测试后端登录</button>
        </div>
        <div id="backend-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 测试前端API调用</h2>
        <button onclick="testFrontendLogin()">测试前端登录API</button>
        <div id="frontend-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 检查本地存储</h2>
        <button onclick="checkLocalStorage()">检查本地存储</button>
        <div id="storage-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. 清除本地存储</h2>
        <button onclick="clearLocalStorage()">清除本地存储</button>
        <div id="clear-result" class="result"></div>
    </div>

    <script>
        // 测试后端登录接口
        async function testBackendLogin() {
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('backend-result');
            
            try {
                const response = await fetch('http://localhost:8081/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ phone, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>后端登录成功</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>后端登录失败</h4>
                        <p>状态码: ${response.status}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>请求失败</h4>
                    <p>${error.message}</p>
                `;
            }
        }

        // 测试前端API调用（模拟）
        async function testFrontendLogin() {
            const resultDiv = document.getElementById('frontend-result');
            
            try {
                // 模拟前端API调用逻辑
                const response = await fetch('http://localhost:8081/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        phone: '13800138023', 
                        password: '12345678' 
                    })
                });
                
                const data = await response.json();
                
                // 模拟api-helper.js的处理逻辑
                if (data.code && data.code >= 200 && data.code < 300) {
                    const responseData = data.data;
                    
                    if (responseData && responseData.accessToken) {
                        // 模拟存储token
                        localStorage.setItem('token', responseData.accessToken);
                        localStorage.setItem('refresh_token', responseData.refreshToken);
                        
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            <h4>前端登录处理成功</h4>
                            <p>Token已保存到本地存储</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = `
                            <h4>前端登录处理失败</h4>
                            <p>响应数据中没有accessToken</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        `;
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>前端登录处理失败</h4>
                        <p>状态码不在成功范围内: ${data.code}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>前端API调用失败</h4>
                    <p>${error.message}</p>
                `;
            }
        }

        // 检查本地存储
        function checkLocalStorage() {
            const resultDiv = document.getElementById('storage-result');
            
            const token = localStorage.getItem('token');
            const refreshToken = localStorage.getItem('refresh_token');
            const userInfo = localStorage.getItem('user_info');
            
            resultDiv.className = 'result info';
            resultDiv.innerHTML = `
                <h4>本地存储状态</h4>
                <p><strong>Token:</strong> ${token ? '已设置' : '未设置'}</p>
                <p><strong>Refresh Token:</strong> ${refreshToken ? '已设置' : '未设置'}</p>
                <p><strong>User Info:</strong> ${userInfo ? '已设置' : '未设置'}</p>
                ${token ? `<pre>Token: ${token.substring(0, 50)}...</pre>` : ''}
            `;
        }

        // 清除本地存储
        function clearLocalStorage() {
            localStorage.removeItem('token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user_info');
            
            const resultDiv = document.getElementById('clear-result');
            resultDiv.className = 'result success';
            resultDiv.innerHTML = '<h4>本地存储已清除</h4>';
        }

        // 页面加载时检查本地存储
        window.onload = function() {
            checkLocalStorage();
        };
    </script>
</body>
</html>
