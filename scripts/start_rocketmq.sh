#!/bin/bash

# RocketMQ启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "Docker环境检查通过"
}

# 检查端口是否被占用
check_ports() {
    local ports=(9876 10909 10911 8080 6379 3306)
    local occupied_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        log_warn "以下端口被占用: ${occupied_ports[*]}"
        log_warn "请确保这些端口可用，或修改docker-compose.yml中的端口映射"
        read -p "是否继续启动? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "启动已取消"
            exit 0
        fi
    else
        log_info "端口检查通过"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    local dirs=(
        "docker/rocketmq"
        "logs/rocketmq"
        "data/rocketmq"
        "data/mysql"
        "data/redis"
    )
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_debug "创建目录: $dir"
        fi
    done
}

# 启动RocketMQ服务
start_rocketmq() {
    log_info "启动RocketMQ服务..."
    
    # 切换到项目根目录
    cd "$(dirname "$0")/.."
    
    # 启动服务
    docker-compose -f docker/docker-compose.rocketmq.yml up -d
    
    if [ $? -eq 0 ]; then
        log_info "RocketMQ服务启动成功"
    else
        log_error "RocketMQ服务启动失败"
        exit 1
    fi
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待NameServer就绪
    log_debug "等待NameServer就绪..."
    local nameserver_ready=false
    for i in {1..30}; do
        if docker exec rocketmq-nameserver sh -c "netstat -ln | grep :9876" >/dev/null 2>&1; then
            nameserver_ready=true
            break
        fi
        sleep 2
        echo -n "."
    done
    echo
    
    if [ "$nameserver_ready" = true ]; then
        log_info "NameServer已就绪"
    else
        log_error "NameServer启动超时"
        return 1
    fi
    
    # 等待Broker就绪
    log_debug "等待Broker就绪..."
    local broker_ready=false
    for i in {1..30}; do
        if docker exec rocketmq-broker sh -c "netstat -ln | grep :10911" >/dev/null 2>&1; then
            broker_ready=true
            break
        fi
        sleep 2
        echo -n "."
    done
    echo
    
    if [ "$broker_ready" = true ]; then
        log_info "Broker已就绪"
    else
        log_error "Broker启动超时"
        return 1
    fi
    
    # 等待MySQL就绪
    log_debug "等待MySQL就绪..."
    local mysql_ready=false
    for i in {1..60}; do
        if docker exec tax-mysql mysqladmin ping -h localhost -u root -pAa123456@ >/dev/null 2>&1; then
            mysql_ready=true
            break
        fi
        sleep 2
        echo -n "."
    done
    echo
    
    if [ "$mysql_ready" = true ]; then
        log_info "MySQL已就绪"
    else
        log_error "MySQL启动超时"
        return 1
    fi
    
    # 等待Redis就绪
    log_debug "等待Redis就绪..."
    local redis_ready=false
    for i in {1..30}; do
        if docker exec tax-redis redis-cli -a redis123 ping >/dev/null 2>&1; then
            redis_ready=true
            break
        fi
        sleep 2
        echo -n "."
    done
    echo
    
    if [ "$redis_ready" = true ]; then
        log_info "Redis已就绪"
    else
        log_error "Redis启动超时"
        return 1
    fi
}

# 创建RocketMQ主题
create_topics() {
    log_info "创建RocketMQ主题..."
    
    local topics=(
        "tax_filing_submission"
        "tax_filing_batch"
        "tax_filing_sync"
        "tax_filing_callback"
        "tax_filing_notification"
        "tax_filing_dead_letter"
    )
    
    for topic in "${topics[@]}"; do
        log_debug "创建主题: $topic"
        docker exec rocketmq-broker sh -c "
            cd /opt/rocketmq-4.9.4/bin && 
            ./mqadmin updateTopic -n rocketmq-nameserver:9876 -t $topic -c DefaultCluster
        " >/dev/null 2>&1
        
        if [ $? -eq 0 ]; then
            log_debug "主题 $topic 创建成功"
        else
            log_warn "主题 $topic 创建失败或已存在"
        fi
    done
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    echo
    docker-compose -f docker/docker-compose.rocketmq.yml ps
    echo
    
    log_info "服务访问地址:"
    echo "🚀 RocketMQ Console: http://localhost:8080"
    echo "📊 NameServer: localhost:9876"
    echo "🔄 Broker: localhost:10911"
    echo "🗄️  MySQL: localhost:3306 (root/Aa123456@)"
    echo "⚡ Redis: localhost:6379 (redis123)"
    echo
    
    log_info "测试连接:"
    echo "# 测试NameServer连接"
    echo "telnet localhost 9876"
    echo
    echo "# 测试MySQL连接"
    echo "mysql -h localhost -P 3306 -u root -pAa123456@ -D smeasy_tax"
    echo
    echo "# 测试Redis连接"
    echo "redis-cli -h localhost -p 6379 -a redis123"
}

# 主函数
main() {
    log_info "🚀 启动RocketMQ和相关服务..."
    echo
    
    # 检查环境
    check_docker
    check_ports
    
    # 创建目录
    create_directories
    
    # 启动服务
    start_rocketmq
    
    # 等待服务就绪
    if wait_for_services; then
        # 创建主题
        create_topics
        
        # 显示状态
        show_status
        
        log_info "✅ 所有服务启动完成!"
    else
        log_error "❌ 服务启动失败"
        exit 1
    fi
}

# 处理中断信号
trap 'log_warn "启动被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
