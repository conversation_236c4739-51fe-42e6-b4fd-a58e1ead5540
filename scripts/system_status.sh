#!/bin/bash

# 系统状态检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 显示标题
show_header() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    📊 税易通系统状态检查                      ║"
    echo "║                                                              ║"
    echo "║  检查所有服务的运行状态和连接性                                ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 检查Go后端服务
check_backend() {
    log_header "1. 检查Go后端服务"
    
    if curl -s http://localhost:8081/api/system/health >/dev/null 2>&1; then
        log_success "Go后端服务运行正常"
        
        # 获取健康检查详情
        local health_response=$(curl -s http://localhost:8081/api/system/health)
        echo "   健康检查响应: $health_response"
        
        # 检查税务申报API
        if curl -s http://localhost:8081/api/tax-filing/submissions >/dev/null 2>&1; then
            log_success "税务申报API可访问 (需要认证)"
        else
            log_warn "税务申报API检查失败"
        fi
        
    else
        log_error "Go后端服务未运行"
        return 1
    fi
}

# 检查数据库连接
check_database() {
    log_header "2. 检查MySQL数据库"
    
    if command -v mysql >/dev/null 2>&1; then
        if mysql -h localhost -P 3306 -u root -pAa123456@ -e "SELECT 1;" >/dev/null 2>&1; then
            log_success "MySQL数据库连接正常"
            
            # 检查数据库和表
            local db_info=$(mysql -h localhost -P 3306 -u root -pAa123456@ -D smeasy_tax -e "SHOW TABLES;" 2>/dev/null | wc -l)
            echo "   数据库表数量: $((db_info - 1))"
            
        else
            log_error "MySQL数据库连接失败"
            return 1
        fi
    else
        log_warn "MySQL客户端未安装，跳过数据库检查"
    fi
}

# 检查Redis连接
check_redis() {
    log_header "3. 检查Redis缓存"
    
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli -h localhost -p 6379 -a redis123 ping >/dev/null 2>&1; then
            log_success "Redis缓存连接正常"
            
            # 获取Redis信息
            local redis_info=$(redis-cli -h localhost -p 6379 -a redis123 info server 2>/dev/null | grep redis_version | cut -d: -f2 | tr -d '\r')
            echo "   Redis版本: $redis_info"
            
        else
            log_error "Redis缓存连接失败"
            return 1
        fi
    else
        log_warn "Redis客户端未安装，跳过Redis检查"
    fi
}

# 检查RocketMQ
check_rocketmq() {
    log_header "4. 检查RocketMQ消息队列"
    
    # 检查NameServer
    if nc -z localhost 9876 >/dev/null 2>&1; then
        log_success "RocketMQ NameServer运行正常"
    else
        log_error "RocketMQ NameServer未运行"
        return 1
    fi
    
    # 检查Broker
    if nc -z localhost 10911 >/dev/null 2>&1; then
        log_success "RocketMQ Broker运行正常"
    else
        log_error "RocketMQ Broker未运行"
        return 1
    fi
    
    # 检查控制台
    if curl -s http://localhost:8080 >/dev/null 2>&1; then
        log_success "RocketMQ控制台可访问"
    else
        log_warn "RocketMQ控制台不可访问"
    fi
}

# 检查Docker容器
check_docker_containers() {
    log_header "5. 检查Docker容器状态"
    
    if command -v docker >/dev/null 2>&1; then
        local containers=(
            "rocketmq-nameserver"
            "rocketmq-broker"
            "rocketmq-console"
            "tax-mysql"
            "tax-redis"
        )
        
        for container in "${containers[@]}"; do
            if docker ps --format "table {{.Names}}" | grep -q "^${container}$"; then
                local status=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep "^${container}" | awk '{print $2}')
                log_success "容器 $container: $status"
            else
                log_warn "容器 $container: 未运行"
            fi
        done
    else
        log_warn "Docker未安装，跳过容器检查"
    fi
}

# 检查端口占用
check_ports() {
    log_header "6. 检查端口占用情况"
    
    local ports=(
        "8081:Go后端服务"
        "3306:MySQL数据库"
        "6379:Redis缓存"
        "9876:RocketMQ NameServer"
        "10911:RocketMQ Broker"
        "8080:RocketMQ控制台"
    )
    
    for port_info in "${ports[@]}"; do
        local port=$(echo $port_info | cut -d: -f1)
        local service=$(echo $port_info | cut -d: -f2)
        
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_success "端口 $port ($service): 已占用"
        else
            log_warn "端口 $port ($service): 未占用"
        fi
    done
}

# 运行API测试
run_api_tests() {
    log_header "7. 运行API测试"
    
    if [ -f "test_tax_filing_api.sh" ]; then
        log_info "运行税务申报API测试..."
        if ./test_tax_filing_api.sh >/dev/null 2>&1; then
            log_success "API测试通过"
        else
            log_warn "API测试失败"
        fi
    else
        log_warn "API测试脚本不存在"
    fi
}

# 运行单元测试
run_unit_tests() {
    log_header "8. 运行单元测试"
    
    if [ -d "backend" ]; then
        cd backend
        log_info "运行通知服务单元测试..."
        if go test ./service -run TestNotificationSenderService >/dev/null 2>&1; then
            log_success "单元测试通过"
        else
            log_warn "单元测试失败"
        fi
        cd ..
    else
        log_warn "后端目录不存在"
    fi
}

# 显示系统信息
show_system_info() {
    log_header "9. 系统信息"
    
    echo "   操作系统: $(uname -s)"
    echo "   架构: $(uname -m)"
    
    if command -v go >/dev/null 2>&1; then
        echo "   Go版本: $(go version | awk '{print $3}')"
    fi
    
    if command -v docker >/dev/null 2>&1; then
        echo "   Docker版本: $(docker --version | awk '{print $3}' | sed 's/,//')"
    fi
    
    echo "   当前时间: $(date)"
}

# 显示访问地址
show_access_urls() {
    log_header "10. 服务访问地址"
    
    echo "   🏢 Go后端服务:        http://localhost:8081"
    echo "   📊 系统健康检查:      http://localhost:8081/api/system/health"
    echo "   📋 税务申报API:       http://localhost:8081/api/tax-filing"
    echo "   🚀 RocketMQ控制台:    http://localhost:8080"
    echo "   🗄️  MySQL数据库:       localhost:3306 (root/Aa123456@)"
    echo "   ⚡ Redis缓存:         localhost:6379 (redis123)"
}

# 生成状态报告
generate_report() {
    log_header "11. 生成状态报告"
    
    local report_file="system_status_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "税易通系统状态报告"
        echo "生成时间: $(date)"
        echo "=========================="
        echo
        
        # 重新运行检查并记录结果
        check_backend 2>&1
        echo
        check_database 2>&1
        echo
        check_redis 2>&1
        echo
        check_rocketmq 2>&1
        echo
        check_docker_containers 2>&1
        echo
        check_ports 2>&1
        echo
        show_system_info 2>&1
        echo
        show_access_urls 2>&1
        
    } > "$report_file"
    
    log_success "状态报告已生成: $report_file"
}

# 主函数
main() {
    show_header
    
    local failed_checks=0
    
    # 运行所有检查
    check_backend || ((failed_checks++))
    echo
    
    check_database || ((failed_checks++))
    echo
    
    check_redis || ((failed_checks++))
    echo
    
    check_rocketmq || ((failed_checks++))
    echo
    
    check_docker_containers
    echo
    
    check_ports
    echo
    
    run_api_tests
    echo
    
    run_unit_tests
    echo
    
    show_system_info
    echo
    
    show_access_urls
    echo
    
    # 生成报告
    generate_report
    echo
    
    # 显示总结
    if [ $failed_checks -eq 0 ]; then
        log_success "🎉 所有核心服务运行正常！"
    else
        log_warn "⚠️  有 $failed_checks 个服务检查失败"
    fi
    
    echo
    log_info "系统状态检查完成！"
}

# 执行主函数
main "$@"
