#!/bin/bash

# RocketMQ停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 停止RocketMQ服务
stop_rocketmq() {
    log_info "停止RocketMQ服务..."
    
    # 切换到项目根目录
    cd "$(dirname "$0")/.."
    
    # 停止服务
    docker-compose -f docker/docker-compose.rocketmq.yml down
    
    if [ $? -eq 0 ]; then
        log_info "RocketMQ服务停止成功"
    else
        log_error "RocketMQ服务停止失败"
        exit 1
    fi
}

# 清理数据（可选）
cleanup_data() {
    read -p "是否清理所有数据? 这将删除所有消息、数据库数据等 (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_warn "清理数据..."
        
        # 切换到项目根目录
        cd "$(dirname "$0")/.."
        
        # 删除Docker卷
        docker-compose -f docker/docker-compose.rocketmq.yml down -v
        
        # 删除数据目录
        if [ -d "data" ]; then
            rm -rf data
            log_debug "删除数据目录"
        fi
        
        # 删除日志目录
        if [ -d "logs" ]; then
            rm -rf logs
            log_debug "删除日志目录"
        fi
        
        log_info "数据清理完成"
    else
        log_info "保留数据"
    fi
}

# 显示状态
show_status() {
    log_info "检查服务状态..."
    
    # 切换到项目根目录
    cd "$(dirname "$0")/.."
    
    # 显示容器状态
    docker-compose -f docker/docker-compose.rocketmq.yml ps
    
    # 检查端口占用
    local ports=(9876 10909 10911 8080 6379 3306)
    local occupied_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        log_warn "以下端口仍被占用: ${occupied_ports[*]}"
    else
        log_info "所有相关端口已释放"
    fi
}

# 强制清理
force_cleanup() {
    log_warn "执行强制清理..."
    
    # 停止所有相关容器
    local containers=(
        "rocketmq-nameserver"
        "rocketmq-broker"
        "rocketmq-console"
        "tax-redis"
        "tax-mysql"
    )
    
    for container in "${containers[@]}"; do
        if docker ps -a --format "table {{.Names}}" | grep -q "^${container}$"; then
            log_debug "停止容器: $container"
            docker stop "$container" >/dev/null 2>&1 || true
            docker rm "$container" >/dev/null 2>&1 || true
        fi
    done
    
    # 删除相关网络
    if docker network ls --format "table {{.Name}}" | grep -q "rocketmq-network"; then
        log_debug "删除网络: rocketmq-network"
        docker network rm rocketmq-network >/dev/null 2>&1 || true
    fi
    
    # 删除相关卷
    local volumes=(
        "rocketmq-nameserver-data"
        "rocketmq-broker-data"
        "rocketmq-broker-store"
        "redis-data"
        "mysql-data"
    )
    
    for volume in "${volumes[@]}"; do
        if docker volume ls --format "table {{.Name}}" | grep -q "^.*${volume}$"; then
            log_debug "删除卷: $volume"
            docker volume rm $(docker volume ls -q | grep "$volume") >/dev/null 2>&1 || true
        fi
    done
    
    log_info "强制清理完成"
}

# 主函数
main() {
    log_info "🛑 停止RocketMQ和相关服务..."
    echo
    
    # 解析参数
    local force_mode=false
    local cleanup_mode=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--force)
                force_mode=true
                shift
                ;;
            -c|--cleanup)
                cleanup_mode=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  -f, --force    强制停止并清理所有相关资源"
                echo "  -c, --cleanup  停止后清理数据"
                echo "  -h, --help     显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    if [ "$force_mode" = true ]; then
        force_cleanup
    else
        # 正常停止
        stop_rocketmq
        
        if [ "$cleanup_mode" = true ]; then
            cleanup_data
        fi
    fi
    
    # 显示状态
    show_status
    
    log_info "✅ 停止操作完成!"
}

# 处理中断信号
trap 'log_warn "停止被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
