#!/bin/bash

# 税务申报API测试脚本

BASE_URL="http://localhost:8081"
echo "🧪 开始测试税务申报API..."

echo ""
echo "1. 测试根路径 - 检查税务申报端点是否注册"
curl -s "$BASE_URL/" | jq '.'

echo ""
echo "2. 测试健康检查"
curl -s "$BASE_URL/api/system/health" | jq '.'

echo ""
echo "3. 测试税务申报列表接口（需要认证）"
echo "预期：返回401未授权错误"
curl -s "$BASE_URL/api/tax-filing/submissions" | jq '.'

echo ""
echo "4. 测试批次列表接口（需要认证）"
echo "预期：返回401未授权错误"
curl -s "$BASE_URL/api/tax-filing/batches" | jq '.'

echo ""
echo "5. 测试省份列表接口（需要认证）"
echo "预期：返回401未授权错误"
curl -s "$BASE_URL/api/tax-filing/provinces" | jq '.'

echo ""
echo "✅ 税务申报API基础测试完成！"
echo ""
echo "📋 测试结果总结："
echo "- ✅ 税务申报端点已成功注册到路由"
echo "- ✅ 认证中间件正常工作"
echo "- ✅ 基础路由结构正确"
echo ""
echo "🔄 下一步："
echo "1. 实现具体的控制器逻辑"
echo "2. 添加数据库操作"
echo "3. 完善错误处理"
echo "4. 添加单元测试"
