#!/bin/bash

# 完整项目启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🚀 税易通系统启动器                        ║"
    echo "║                                                              ║"
    echo "║  这个脚本将启动完整的税务申报系统，包括：                      ║"
    echo "║  • Go后端服务 (端口8081)                                     ║"
    echo "║  • RocketMQ消息队列 (端口9876, 10911)                       ║"
    echo "║  • MySQL数据库 (端口3306)                                   ║"
    echo "║  • Redis缓存 (端口6379)                                     ║"
    echo "║  • RocketMQ管理界面 (端口8080)                              ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 检查环境
check_environment() {
    log_step "1. 检查运行环境"
    
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        log_error "Go未安装，请先安装Go 1.19+"
        exit 1
    fi
    
    local go_version=$(go version | grep -oE 'go[0-9]+\.[0-9]+' | sed 's/go//')
    log_debug "Go版本: $go_version"
    
    # 检查Docker环境
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_debug "Docker版本: $(docker --version)"
    log_debug "Docker Compose版本: $(docker-compose --version)"
    
    log_success "环境检查通过"
}

# 构建Go后端
build_backend() {
    log_step "2. 构建Go后端服务"
    
    cd backend
    
    # 下载依赖
    log_debug "下载Go模块依赖..."
    go mod tidy
    
    # 构建应用
    log_debug "构建Go应用..."
    go build -o tax_system main_tax_system.go
    
    if [ $? -eq 0 ]; then
        log_success "Go后端构建成功"
    else
        log_error "Go后端构建失败"
        exit 1
    fi
    
    cd ..
}

# 启动基础设施
start_infrastructure() {
    log_step "3. 启动基础设施服务"
    
    # 启动RocketMQ和相关服务
    log_debug "启动RocketMQ、MySQL、Redis..."
    chmod +x scripts/start_rocketmq.sh
    ./scripts/start_rocketmq.sh
    
    if [ $? -eq 0 ]; then
        log_success "基础设施服务启动成功"
    else
        log_error "基础设施服务启动失败"
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_step "4. 初始化数据库"
    
    # 等待MySQL完全启动
    log_debug "等待MySQL服务就绪..."
    for i in {1..30}; do
        if docker exec tax-mysql mysqladmin ping -h localhost -u root -pAa123456@ >/dev/null 2>&1; then
            break
        fi
        sleep 2
        echo -n "."
    done
    echo
    
    # 执行数据库初始化脚本
    log_debug "执行数据库初始化脚本..."
    
    # 检查是否已经初始化
    local table_count=$(docker exec tax-mysql mysql -h localhost -u root -pAa123456@ -D smeasy_tax -e "SHOW TABLES;" 2>/dev/null | wc -l)
    
    if [ $table_count -gt 1 ]; then
        log_debug "数据库已初始化，跳过初始化步骤"
    else
        # 执行初始化脚本
        if [ -f "backend/sql/tax_filing_schema.sql" ]; then
            docker exec -i tax-mysql mysql -h localhost -u root -pAa123456@ -D smeasy_tax < backend/sql/tax_filing_schema.sql
            log_debug "数据库表结构创建完成"
        fi
        
        if [ -f "backend/sql/tax_filing_init_data.sql" ]; then
            docker exec -i tax-mysql mysql -h localhost -u root -pAa123456@ -D smeasy_tax < backend/sql/tax_filing_init_data.sql
            log_debug "初始数据插入完成"
        fi
    fi
    
    log_success "数据库初始化完成"
}

# 启动Go后端服务
start_backend() {
    log_step "5. 启动Go后端服务"
    
    cd backend
    
    # 设置环境变量
    export DB_HOST=localhost
    export DB_PORT=3306
    export DB_USER=root
    export DB_PASSWORD=Aa123456@
    export DB_NAME=smeasy_tax
    export REDIS_HOST=localhost
    export REDIS_PORT=6379
    export REDIS_PASSWORD=redis123
    export ROCKETMQ_NAME_SERVERS=localhost:9876
    
    # 启动服务
    log_debug "启动Go后端服务..."
    nohup ./tax_system > ../logs/backend.log 2>&1 &
    local backend_pid=$!
    
    # 等待服务启动
    log_debug "等待后端服务就绪..."
    for i in {1..30}; do
        if curl -s http://localhost:8081/api/system/health >/dev/null 2>&1; then
            log_success "Go后端服务启动成功 (PID: $backend_pid)"
            echo $backend_pid > ../logs/backend.pid
            break
        fi
        sleep 2
        echo -n "."
    done
    echo
    
    cd ..
    
    # 检查服务是否真正启动
    if ! curl -s http://localhost:8081/api/system/health >/dev/null 2>&1; then
        log_error "Go后端服务启动失败"
        exit 1
    fi
}

# 运行测试
run_tests() {
    log_step "6. 运行基础测试"
    
    # 测试后端API
    log_debug "测试后端API..."
    chmod +x test_tax_filing_api.sh
    ./test_tax_filing_api.sh
    
    log_success "基础测试完成"
}

# 显示服务状态
show_services_status() {
    log_step "7. 服务状态总览"
    
    echo
    echo -e "${CYAN}🎯 服务访问地址:${NC}"
    echo "┌─────────────────────────────────────────────────────────────┐"
    echo "│ 🏢 Go后端服务:        http://localhost:8081                 │"
    echo "│ 📊 系统健康检查:      http://localhost:8081/api/system/health│"
    echo "│ 📋 税务申报API:       http://localhost:8081/api/tax-filing   │"
    echo "│ 🚀 RocketMQ控制台:    http://localhost:8080                 │"
    echo "│ 🗄️  MySQL数据库:       localhost:3306 (root/Aa123456@)      │"
    echo "│ ⚡ Redis缓存:         localhost:6379 (redis123)            │"
    echo "└─────────────────────────────────────────────────────────────┘"
    echo
    
    echo -e "${CYAN}📊 服务状态:${NC}"
    echo "┌─────────────────────────────────────────────────────────────┐"
    
    # 检查Go后端
    if curl -s http://localhost:8081/api/system/health >/dev/null 2>&1; then
        echo "│ ✅ Go后端服务:        运行中                                │"
    else
        echo "│ ❌ Go后端服务:        未运行                                │"
    fi
    
    # 检查RocketMQ
    if docker ps --format "table {{.Names}}" | grep -q "rocketmq-nameserver"; then
        echo "│ ✅ RocketMQ:         运行中                                │"
    else
        echo "│ ❌ RocketMQ:         未运行                                │"
    fi
    
    # 检查MySQL
    if docker exec tax-mysql mysqladmin ping -h localhost -u root -pAa123456@ >/dev/null 2>&1; then
        echo "│ ✅ MySQL数据库:       运行中                                │"
    else
        echo "│ ❌ MySQL数据库:       未运行                                │"
    fi
    
    # 检查Redis
    if docker exec tax-redis redis-cli -a redis123 ping >/dev/null 2>&1; then
        echo "│ ✅ Redis缓存:        运行中                                │"
    else
        echo "│ ❌ Redis缓存:        未运行                                │"
    fi
    
    echo "└─────────────────────────────────────────────────────────────┘"
    echo
    
    echo -e "${CYAN}🔧 管理命令:${NC}"
    echo "┌─────────────────────────────────────────────────────────────┐"
    echo "│ 停止所有服务:        ./scripts/stop_all.sh                  │"
    echo "│ 停止基础设施:        ./scripts/stop_rocketmq.sh              │"
    echo "│ 查看后端日志:        tail -f logs/backend.log                │"
    echo "│ 重启后端服务:        ./scripts/restart_backend.sh            │"
    echo "│ 运行测试:           ./test_tax_filing_api.sh                │"
    echo "└─────────────────────────────────────────────────────────────┘"
}

# 创建日志目录
create_log_directory() {
    if [ ! -d "logs" ]; then
        mkdir -p logs
        log_debug "创建日志目录"
    fi
}

# 主函数
main() {
    # 显示欢迎信息
    show_welcome
    
    # 创建日志目录
    create_log_directory
    
    # 检查环境
    check_environment
    
    # 构建后端
    build_backend
    
    # 启动基础设施
    start_infrastructure
    
    # 初始化数据库
    init_database
    
    # 启动后端服务
    start_backend
    
    # 运行测试
    run_tests
    
    # 显示服务状态
    show_services_status
    
    echo
    log_success "🎉 税易通系统启动完成！"
    echo
    echo -e "${GREEN}系统已就绪，您可以开始使用税务申报功能了！${NC}"
    echo
}

# 处理中断信号
trap 'log_warn "启动被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
