# 用户管理模块文档

## 概述

用户管理模块是税务管理系统的核心功能之一，提供完整的企业用户管理、角色权限控制和企业所有权管理功能。

## 功能特性

### 🔐 权限控制体系
- **企业级权限隔离**：确保不同企业间的数据安全
- **角色权限管理**：支持灵活的角色定义和权限分配
- **Owner特殊权限**：企业所有者拥有最高管理权限
- **细粒度权限控制**：支持功能级、数据级、字段级权限控制

### 👥 用户管理功能
- **用户邀请**：支持通过邮箱、手机号邀请用户加入企业
- **角色分配**：为用户分配不同的角色和权限
- **用户搜索**：快速搜索和筛选用户
- **用户状态管理**：管理用户的激活、停用状态

### 🏢 企业管理功能
- **企业所有权转让**：支持安全的所有权转让流程
- **企业用户列表**：查看和管理企业内所有用户
- **用户移除**：从企业中移除不需要的用户
- **操作日志**：记录所有用户管理相关的操作

### 🎭 角色权限管理
- **角色创建**：创建自定义角色
- **权限分配**：为角色分配具体权限
- **权限查看**：查看系统所有权限和使用情况
- **角色统计**：查看角色使用统计信息

## 技术架构

### 后端架构
```
├── model/
│   ├── enterprise_user.go      # 企业用户关联模型
│   ├── user.go                 # 用户模型
│   ├── enterprise.go           # 企业模型
│   ├── role.go                 # 角色模型
│   └── permission.go           # 权限模型
├── service/
│   ├── user_management_service.go  # 用户管理服务
│   └── permission_service.go       # 权限服务
├── api/
│   └── user_management_handler.go  # 用户管理API处理器
├── middleware/
│   └── permission_middleware.go    # 权限控制中间件
└── test/
    └── user_management_test.go     # 单元测试
```

### 前端架构
```
├── views/user/                         # 用户管理页面（已整合）
│   ├── UserList.vue                    # 用户列表页面
│   ├── RoleManagement.vue              # 角色管理页面
│   ├── PermissionManagement.vue        # 权限管理页面
│   ├── Profile.vue                     # 用户资料页面
│   ├── Settings.vue                    # 用户设置页面
│   └── components/
│       ├── InviteUserModal.vue         # 邀请用户模态框
│       ├── RoleAssignModal.vue         # 角色分配模态框
│       ├── UserDetailModal.vue         # 用户详情模态框
│       └── TransferOwnershipModal.vue  # 所有权转让模态框
├── api/
│   └── user.js                         # 用户管理API接口（已合并）
├── utils/
│   ├── date.js                         # 日期工具函数
│   └── common.js                       # 通用工具函数
```

## 数据库设计

### 核心表结构

#### 企业用户关联表 (enterprise_users)
```sql
CREATE TABLE enterprise_users (
    id VARCHAR(36) PRIMARY KEY,
    enterprise_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    role_id VARCHAR(36) NOT NULL,
    status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
    joined_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
    invited_by VARCHAR(36),
    is_owner TINYINT(1) DEFAULT 0,
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
    updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    UNIQUE KEY uk_enterprise_users (enterprise_id, user_id)
);
```

#### 用户特殊权限表 (user_permissions)
```sql
CREATE TABLE user_permissions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    permission_id VARCHAR(36) NOT NULL,
    enterprise_id VARCHAR(36),
    granted_by VARCHAR(36) NOT NULL,
    granted_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
    expires_at DATETIME(3),
    is_active TINYINT(1) DEFAULT 1,
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
    updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)
);
```

#### 权限操作日志表 (permission_logs)
```sql
CREATE TABLE permission_logs (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    target_user_id VARCHAR(36),
    enterprise_id VARCHAR(36),
    operation_type ENUM('role_assign', 'role_revoke', 'permission_grant', 'permission_revoke', 'owner_transfer', 'user_invite', 'user_remove') NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(36) NOT NULL,
    old_value JSON,
    new_value JSON,
    reason VARCHAR(500),
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)
);
```

## API接口

### 企业用户管理
- `GET /enterprises/:enterpriseId/users` - 获取企业用户列表
- `POST /enterprises/:enterpriseId/users` - 邀请用户加入企业
- `DELETE /enterprises/:enterpriseId/users/:userId` - 从企业中移除用户
- `PUT /enterprises/:enterpriseId/users/:userId/role` - 更新用户角色
- `POST /enterprises/:enterpriseId/transfer-ownership` - 转让企业所有权

### 用户管理
- `GET /users/search` - 搜索用户
- `GET /users/:userId/permissions` - 获取用户权限
- `GET /users/:userId/roles` - 获取用户角色

### 角色权限管理
- `GET /roles` - 获取角色列表
- `POST /roles` - 创建角色
- `PUT /roles/:roleId` - 更新角色
- `DELETE /roles/:roleId` - 删除角色
- `GET /permissions` - 获取权限列表

## 权限控制机制

### 权限检查流程
1. **企业所有者检查**：首先检查用户是否为企业所有者
2. **角色权限检查**：检查用户角色是否拥有所需权限
3. **特殊权限检查**：检查用户是否被单独授予特殊权限
4. **权限有效期检查**：验证权限是否在有效期内

### 权限中间件
```go
// 需要特定权限
router.Use(permissionMiddleware.RequirePermission("user", "view"))

// 需要企业所有权
router.Use(permissionMiddleware.RequireOwnership())

// 需要企业访问权限
router.Use(permissionMiddleware.RequireEnterpriseAccess())
```

## 使用指南

### 1. 邀请用户加入企业
1. 进入用户管理页面
2. 点击"邀请用户"按钮
3. 搜索并选择要邀请的用户
4. 分配适当的角色
5. 填写邀请说明（可选）
6. 确认邀请

### 2. 管理用户角色
1. 在用户列表中找到目标用户
2. 点击"分配角色"按钮
3. 选择新的角色
4. 填写变更原因（可选）
5. 确认变更

### 3. 转让企业所有权
1. 进入用户管理页面
2. 点击"转让所有权"按钮（仅所有者可见）
3. 选择新的所有者
4. 填写转让原因
5. 输入当前密码确认
6. 确认转让操作

### 4. 管理角色权限
1. 进入角色管理页面
2. 查看现有角色或创建新角色
3. 为角色分配具体权限
4. 保存角色配置

## 安全特性

### 数据安全
- **企业级数据隔离**：确保不同企业间数据完全隔离
- **权限最小化原则**：用户默认拥有最小必要权限
- **操作审计**：记录所有敏感操作的详细日志

### 身份验证
- **密码确认**：重要操作需要密码二次确认
- **会话管理**：安全的用户会话管理机制
- **权限缓存**：合理的权限缓存策略

### 防护机制
- **频率限制**：防止恶意的频繁操作
- **异常监控**：监控异常的权限操作
- **实时通知**：重要操作的实时通知机制

## 测试

### 后端测试
```bash
# 运行单元测试
go test ./test/user_management_test.go -v

# 运行集成测试
go test ./test/... -v
```

### 前端测试
访问 `/test/user-management` 页面进行功能测试，包括：
- API接口测试
- 组件功能测试
- 工具函数测试

## 故障排除

### 常见问题

1. **权限检查失败**
   - 检查用户是否属于对应企业
   - 验证角色权限配置是否正确
   - 确认权限是否在有效期内

2. **邀请用户失败**
   - 确认目标用户是否存在
   - 检查用户是否已在企业中
   - 验证邀请者权限

3. **所有权转让失败**
   - 确认当前用户是否为所有者
   - 检查目标用户是否为企业成员
   - 验证密码是否正确

### 日志查看
- 权限操作日志：`permission_logs` 表
- 系统错误日志：应用程序日志文件
- 数据库操作日志：数据库日志文件

## 扩展开发

### 添加新权限
1. 在 `permissions` 表中添加新权限记录
2. 更新相关角色的权限配置
3. 在代码中添加权限检查逻辑
4. 更新前端权限控制

### 自定义角色
1. 使用角色管理界面创建新角色
2. 为角色分配适当的权限
3. 将角色分配给相应用户
4. 测试角色权限是否正常工作

## 版本历史

- **v1.0.0** - 初始版本，包含基础用户管理功能
- **v1.1.0** - 添加企业所有权转让功能
- **v1.2.0** - 完善权限控制体系
- **v1.3.0** - 添加操作日志和审计功能

## 贡献指南

请参考项目根目录的 `GOLANG_STANDARDS.md` 和 `VUE_STANDARDS.md` 文件，确保代码符合项目规范。

## 许可证

本项目采用 MIT 许可证，详情请参考 LICENSE 文件。
