# 阿里云邮件推送服务配置指南

本文档提供了如何配置阿里云邮件推送服务（Direct Mail）以与税易通系统集成的详细说明。

## 目录

1. [阿里云账号准备](#阿里云账号准备)
2. [开通邮件推送服务](#开通邮件推送服务)
3. [创建AccessKey](#创建accesskey)
4. [配置发信域名和地址](#配置发信域名和地址)
5. [环境变量配置](#环境变量配置)
6. [配置文件说明](#配置文件说明)
7. [测试邮件发送](#测试邮件发送)
8. [常见问题](#常见问题)

## 阿里云账号准备

1. 如果您还没有阿里云账号，请先在[阿里云官网](https://www.aliyun.com/)注册账号
2. 完成实名认证，这是使用阿里云服务的必要步骤

## 开通邮件推送服务

1. 登录阿里云控制台
2. 搜索"邮件推送"或直接访问[邮件推送服务控制台](https://dm.console.aliyun.com/)
3. 点击"开通服务"并按照提示完成开通流程
4. 开通成功后，您将进入邮件推送服务控制台

## 创建AccessKey

为了安全起见，建议为邮件推送服务创建专用的RAM用户和AccessKey：

1. 访问[RAM控制台](https://ram.console.aliyun.com/)
2. 创建一个新的RAM用户，例如"email-service"
3. 为该用户创建AccessKey，并保存AccessKeyID和AccessKeySecret
4. 为该用户授予"AliyunDirectMailFullAccess"权限

## 配置发信域名和地址

1. 在邮件推送服务控制台中，选择"发信域名"菜单
2. 点击"创建域名"，输入您拥有的域名（例如your-domain.com）
3. 按照提示完成域名验证流程（通常需要添加DNS记录）
4. 域名验证通过后，选择"发信地址"菜单
5. 点击"创建发信地址"，使用已验证的域名创建发信地址（例如no<EMAIL>）
6. 设置发信人昵称（例如"税易通系统"）

## 环境变量配置

为了安全地管理阿里云凭证，我们建议使用环境变量。以下是需要设置的环境变量：

### Linux/macOS

```bash
# 在~/.bashrc或~/.zshrc中添加以下内容
export ALIYUN_ACCESS_KEY_ID="您的AccessKeyID"
export ALIYUN_ACCESS_KEY_SECRET="您的AccessKeySecret"
export EMAIL_FROM_ADDRESS="您的发信地址，例如no<EMAIL>"
export EMAIL_FROM_NAME="发信人昵称，例如税易通系统"
export EMAIL_REPLY_TO="回复地址，例如***********************"
```

然后执行：

```bash
source ~/.bashrc  # 或 source ~/.zshrc
```

### Windows

```cmd
setx ALIYUN_ACCESS_KEY_ID "您的AccessKeyID"
setx ALIYUN_ACCESS_KEY_SECRET "您的AccessKeySecret"
setx EMAIL_FROM_ADDRESS "您的发信地址，例如no<EMAIL>"
setx EMAIL_FROM_NAME "发信人昵称，例如税易通系统"
setx EMAIL_REPLY_TO "回复地址，例如***********************"
```

设置后需要重新打开命令提示符或重启系统。

## 配置文件说明

税易通系统的配置文件中已经包含了阿里云邮件推送服务的配置部分。以下是各环境配置文件中的相关部分：

### 开发环境 (config.dev.yaml)

```yaml
# Email service - 开发环境邮件服务（使用阿里云邮件推送）
email:
  provider: aliyun
  accessKeyID: ${ALIYUN_ACCESS_KEY_ID}  # 从环境变量获取
  accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET}  # 从环境变量获取
  regionID: cn-hangzhou
  endpoint: dm.aliyuncs.com
  fromAddress: ${EMAIL_FROM_ADDRESS:<EMAIL>}  # 需要在阿里云配置的发信地址
  fromName: ${EMAIL_FROM_NAME:税易通系统（开发环境）}
  replyToAddress: ${EMAIL_REPLY_TO:<EMAIL>}
```

### 测试环境 (config.test.yaml)

```yaml
# Email service - 测试环境邮件服务（使用阿里云邮件推送）
email:
  provider: aliyun
  accessKeyID: ${ALIYUN_ACCESS_KEY_ID}  # 从环境变量获取
  accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET}  # 从环境变量获取
  regionID: cn-hangzhou
  endpoint: dm.aliyuncs.com
  fromAddress: ${EMAIL_FROM_ADDRESS:<EMAIL>}  # 需要在阿里云配置的发信地址
  fromName: ${EMAIL_FROM_NAME:税易通系统（测试环境）}
  replyToAddress: ${EMAIL_REPLY_TO:<EMAIL>}
```

### 生产环境 (config.prod.yaml)

```yaml
# Email service - 生产环境邮件服务（阿里云邮件推送）
email:
  provider: aliyun
  accessKeyID: ${ALIYUN_ACCESS_KEY_ID}  # 从环境变量获取
  accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET}  # 从环境变量获取
  regionID: cn-hangzhou
  endpoint: dm.aliyuncs.com
  fromAddress: ${EMAIL_FROM_ADDRESS:<EMAIL>}  # 需要在阿里云配置的发信地址
  fromName: ${EMAIL_FROM_NAME:税易通系统}
  replyToAddress: ${EMAIL_REPLY_TO:<EMAIL>}
```

## 测试邮件发送

配置完成后，您可以通过以下步骤测试邮件发送功能：

1. 启动税易通系统
2. 访问登录页面
3. 点击"忘记密码"链接
4. 输入您的邮箱地址
5. 提交后，检查您的邮箱是否收到密码重置邮件

## 常见问题

### 邮件发送失败

1. 检查环境变量是否正确设置
2. 确认阿里云AccessKey是否有效且具有邮件推送服务权限
3. 验证发信域名和地址是否已通过验证
4. 查看阿里云邮件推送服务控制台中的发送记录和错误信息

### 邮件被标记为垃圾邮件

1. 确保您的发信域名已完成SPF、DKIM和DMARC记录设置
2. 检查邮件内容是否包含可能触发垃圾邮件过滤的内容
3. 在阿里云邮件推送服务控制台中查看邮件送达率和投诉率

### 发信额度限制

阿里云邮件推送服务对不同账户有不同的发信额度限制：

1. 新用户通常有较低的每日发信额度
2. 可以通过工单申请提高发信额度
3. 建议在系统中实现发信频率限制，避免超出额度

### 安全建议

1. 定期轮换AccessKey
2. 使用最小权限原则，只为邮件服务RAM用户授予必要的权限
3. 监控邮件发送日志，及时发现异常情况

## 更多资源

- [阿里云邮件推送服务文档](https://help.aliyun.com/zh/direct-mail)
- [API参考](https://help.aliyun.com/zh/direct-mail/api-reference)
- [最佳实践](https://help.aliyun.com/zh/direct-mail/best-practices)

如有任何问题，请联系系统管理员或阿里云技术支持。
