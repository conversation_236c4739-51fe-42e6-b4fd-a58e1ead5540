# Go语言后端编码规范

## 📋 概述

本文档为智能税务管理系统Go后端代码制定统一的编码规范，基于项目现有的Go 1.24 + Gin + GORM技术栈，确保代码质量、可维护性和团队协作效率。

### 文档版本

| 版本号 | 更新日期 | 更新内容 | 作者 |
|-------|---------|---------|------|
| v1.0.0 | 2024-01-01 | 初始版本 | 开发团队 |
| v1.1.0 | 2024-06-01 | 增强测试规范 | 开发团队 |

## 📁 文件组织与项目结构

### 文件编码
- **强制要求**：所有源文件必须使用UTF-8编码
- **换行符**：统一使用Unix风格换行符（LF）

### 项目结构
按功能模块组织，遵循项目现有结构：

```
tax/                       # 项目根目录
├── backend/               # 后端代码目录
│   ├── api/               # HTTP处理器层
│   │   ├── auth_handler.go       # 认证相关处理器
│   │   ├── user_management_handler.go  # 用户管理处理器
│   │   └── ...           # 其他处理器
│   ├── service/          # 业务逻辑层
│   │   ├── auth_service.go       # 认证相关服务
│   │   ├── user_management_service.go  # 用户管理服务
│   │   └── ...           # 其他服务
│   ├── model/            # 数据模型层
│   │   ├── user.go       # 用户模型
│   │   ├── enterprise.go # 企业模型
│   │   └── ...           # 其他模型
│   ├── middleware/       # 中间件
│   │   ├── auth.go       # 认证中间件
│   │   └── ...           # 其他中间件
│   ├── config/           # 配置管理
│   │   ├── config.go     # 配置加载
│   │   └── config.yaml   # 配置文件
│   ├── bootstrap/        # 应用初始化
│   │   ├── app.go        # 应用启动
│   │   └── database.go   # 数据库初始化
│   ├── util/             # 工具函数
│   │   ├── response.go   # 响应工具
│   │   ├── logger.go     # 日志工具
│   │   └── ...           # 其他工具
│   ├── test/             # 测试函数
│   │   ├── helpers.go    # 测试辅助函数
│   │   └── ...           # 测试文件
│   ├── migrations/       # 数据库迁移
│   │   └── ...           # 迁移文件
│   ├── sql/              # SQL脚本
│   │   └── smeasy_tax.sql # 数据库初始化脚本
│   ├── go.mod            # Go模块定义
│   ├── go.sum            # Go依赖校验
│   └── main_tax_system.go # 应用入口
├── frontend/             # 前端代码目录
├── docs/                 # 项目文档
└── GOLANG_STANDARDS.md   # Go编码规范
```

### 模块职责划分

- **api**: 处理HTTP请求，参数验证，调用service层，返回响应
- **service**: 实现业务逻辑，调用model层，不直接处理HTTP请求
- **model**: 定义数据结构，数据库操作，不包含业务逻辑
- **middleware**: 实现HTTP中间件，如认证、日志、跨域等
- **config**: 管理应用配置，支持多环境配置
- **bootstrap**: 负责应用初始化，如数据库连接、路由注册等
- **util**: 提供通用工具函数，如ID生成、响应格式化等
- **test**: 包含测试辅助函数和集成测试
- **migrations**: 管理数据库结构变更

### 导入顺序
**推荐**：
```go
import (
    // 1. 标准库
    "context"
    "fmt"
    "time"
    
    // 2. 第三方库
    "github.com/gin-gonic/gin"
    "gorm.io/gorm"
    
    // 3. 项目内部包
    "backend/config"
    "backend/model"
    "backend/service"
)
```

**不推荐**：
```go
import (
    "backend/model"
    "fmt"
    "github.com/gin-gonic/gin"
    "time"
)
```

### 文件命名
- **规则**：使用小写字母和下划线分隔
- **示例**：`auth_handler.go`, `user_service.go`, `enterprise_model.go`

## 🏷️ 命名规范

### 包名
- **规则**：使用小写单词，不使用下划线或混合大小写
- **推荐**：`api`, `service`, `model`, `util`
- **不推荐**：`apiHandler`, `user_service`, `ModelData`

### 变量命名
- **私有变量**：使用lowerCamelCase
- **公开变量**：使用UpperCamelCase

**推荐**：
```go
var userID string                    // 私有变量
var MaxRetryCount = 3               // 公开变量
var dbConnection *gorm.DB           // 私有变量
```

**不推荐**：
```go
var user_id string                  // 使用下划线
var maxretrycount = 3              // 全小写
var DBConnection *gorm.DB          // 缩写全大写
```

### 函数和方法命名
- **公开函数**：使用UpperCamelCase
- **私有函数**：使用lowerCamelCase
- **接收器名称**：使用类型名的首字母小写

**推荐**：
```go
// 公开函数
func NewAuthHandler(service *service.AuthService) *AuthHandler {
    return &AuthHandler{authService: service}
}

// 私有函数
func validateUserInput(req model.LoginRequest) error {
    return nil
}

// 方法接收器
func (h *AuthHandler) Login(c *gin.Context) {
    // 实现
}
```

### 接口命名
- **规则**：通常以"er"结尾，描述行为
- **示例**：`UserService`, `TokenGenerator`, `DataValidator`

### 常量命名
- **规则**：使用全大写和下划线分隔
- **分组**：相关常量使用const块组织

**推荐**：
```go
const (
    // Token types
    TOKEN_TYPE_ACCESS  = "access"
    TOKEN_TYPE_REFRESH = "refresh"
    TOKEN_TYPE_RESET   = "reset"
    
    // User roles
    ROLE_ADMIN = "admin"
    ROLE_USER  = "user"
)
```

### 类型命名
- **规则**：使用UpperCamelCase
- **结构体**：名词形式，描述实体
- **接口**：动词形式，描述行为

**推荐**：
```go
type User struct {
    ID    string `json:"id"`
    Email string `json:"email"`
}

type UserService interface {
    CreateUser(ctx context.Context, req model.RegisterRequest) error
    GetUser(ctx context.Context, id string) (*model.User, error)
}
```

## 🎨 代码格式

### 缩进和空格
- **缩进**：使用制表符（tab），与项目现有代码保持一致
- **行长度**：建议不超过120个字符
- **运算符**：前后添加空格

**推荐**：
```go
func calculateTax(amount float64, rate float64) float64 {
	if amount <= 0 || rate < 0 {
		return 0
	}
	return amount * rate
}
```

### 空行使用
- **函数间**：使用一个空行分隔
- **逻辑块间**：使用空行分隔不同的逻辑块
- **导入后**：在导入语句后添加空行

**推荐**：
```go
func (h *AuthHandler) Login(c *gin.Context) {
	var req model.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "无效的请求参数",
		})
		return
	}

	authResponse, err := h.authService.Login(c.Request.Context(), req)
	if err != nil {
		// 错误处理逻辑
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "登录成功",
		"data":    authResponse,
	})
}
```

### 括号位置
- **控制结构**：左括号不换行
- **函数定义**：左括号不换行

**推荐**：
```go
if condition {
    // 代码
}

func processData() {
    // 代码
}
```

## 📝 注释规范

### 包注释
每个包必须有包注释，描述包的用途：

```go
// Package api provides HTTP handlers for the tax management system.
// It includes authentication, enterprise management, and invoice processing endpoints.
package api
```

### 函数文档
所有导出函数必须有文档注释：

**推荐**：
```go
// NewAuthHandler creates a new authentication handler with the provided service and configuration.
// It returns a configured AuthHandler instance ready to handle HTTP requests.
func NewAuthHandler(authService *service.AuthService, cfg *config.Config) *AuthHandler {
    return &AuthHandler{
        authService: authService,
        config:      cfg,
    }
}

// Login 处理用户登录请求，验证用户凭据并返回JWT令牌
func (h *AuthHandler) Login(c *gin.Context) {
    // 实现
}
```

### 代码注释
- **复杂逻辑**：添加内联注释说明
- **语言**：中英文保持一致，项目中主要使用中文
- **TODO/FIXME**：使用统一格式

**推荐**：
```go
// TODO(zhangsan): 2024-01-15 - 添加密码强度验证
// FIXME(lisi): 2024-01-10 - 修复并发访问问题

func validatePassword(password string) error {
    // 检查密码长度（最少8位）
    if len(password) < 8 {
        return errors.New("密码长度不能少于8位")
    }
    
    // 检查是否包含数字和字母
    hasNumber := false
    hasLetter := false
    for _, char := range password {
        if char >= '0' && char <= '9' {
            hasNumber = true
        }
        if (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') {
            hasLetter = true
        }
    }
    
    return nil
}
```

## ⚠️ 错误处理

### 错误传播
使用`fmt.Errorf`或`errors.Wrap`添加上下文：

**推荐**：
```go
func (s *AuthService) Login(ctx context.Context, req model.LoginRequest) (*model.AuthResponse, error) {
    var user model.User
    err := s.db.Where("email = ?", req.Email).First(&user).Error
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, ErrInvalidCredentials
        }
        return nil, fmt.Errorf("查询用户失败: %w", err)
    }
    
    // 其他逻辑
    return authResponse, nil
}
```

### 自定义错误类型
为业务场景定义专用错误：

```go
var (
    ErrInvalidCredentials = errors.New("用户名或密码错误")
    ErrUserInactive      = errors.New("用户账户已被禁用")
    ErrUserExists        = errors.New("用户已存在")
)
```

### panic/recover使用
- **限制使用**：仅在初始化阶段或不可恢复的情况下使用
- **Web处理器**：使用Gin的Recovery中间件

### 错误处理完整示例

1. **定义自定义错误类型**

```go
// model/errors.go
package model

import "fmt"

// 错误类型常量
const (
    ErrNotFound      = "NotFound"
    ErrInvalidInput  = "InvalidInput"
    ErrUnauthorized  = "Unauthorized"
    ErrInternal      = "InternalError"
    ErrDuplicate     = "DuplicateEntry"
)

// AppError 应用错误结构
type AppError struct {
    Type    string // 错误类型
    Message string // 错误消息
    Err     error  // 原始错误
}

// Error 实现error接口
func (e *AppError) Error() string {
    if e.Err != nil {
        return fmt.Sprintf("%s: %s: %v", e.Type, e.Message, e.Err)
    }
    return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// Unwrap 支持errors.Is和errors.As
func (e *AppError) Unwrap() error {
    return e.Err
}

// NewNotFoundError 创建NotFound错误
func NewNotFoundError(entity string, id interface{}, err error) *AppError {
    return &AppError{
        Type:    ErrNotFound,
        Message: fmt.Sprintf("%s with ID %v not found", entity, id),
        Err:     err,
    }
}

// NewInvalidInputError 创建InvalidInput错误
func NewInvalidInputError(message string, err error) *AppError {
    return &AppError{
        Type:    ErrInvalidInput,
        Message: message,
        Err:     err,
    }
}

// NewUnauthorizedError 创建Unauthorized错误
func NewUnauthorizedError(message string, err error) *AppError {
    return &AppError{
        Type:    ErrUnauthorized,
        Message: message,
        Err:     err,
    }
}

// NewInternalError 创建Internal错误
func NewInternalError(message string, err error) *AppError {
    return &AppError{
        Type:    ErrInternal,
        Message: message,
        Err:     err,
    }
}

// NewDuplicateError 创建Duplicate错误
func NewDuplicateError(entity string, field string, value interface{}, err error) *AppError {
    return &AppError{
        Type:    ErrDuplicate,
        Message: fmt.Sprintf("%s with %s %v already exists", entity, field, value),
        Err:     err,
    }
}
```

2. **在服务层使用自定义错误**

```go
// service/user_service.go
func (s *UserService) GetUserByID(id uint) (*model.User, error) {
    var user model.User
    result := s.db.First(&user, id)
    if result.Error != nil {
        if errors.Is(result.Error, gorm.ErrRecordNotFound) {
            return nil, model.NewNotFoundError("User", id, result.Error)
        }
        return nil, model.NewInternalError("Failed to get user", result.Error)
    }
    return &user, nil
}

func (s *UserService) CreateUser(user *model.User) error {
    // 验证输入
    if user.Name == "" {
        return model.NewInvalidInputError("Name cannot be empty", nil)
    }
    
    // 检查重复
    var count int64
    s.db.Model(&model.User{}).Where("email = ?", user.Email).Count(&count)
    if count > 0 {
        return model.NewDuplicateError("User", "email", user.Email, nil)
    }
    
    // 创建用户
    if err := s.db.Create(user).Error; err != nil {
        return model.NewInternalError("Failed to create user", err)
    }
    
    return nil
}
```

3. **在API层处理错误**

```go
// api/user_handler.go
func (h *UserHandler) GetUser(c *gin.Context) {
    id, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid user ID", err)
        return
    }
    
    user, err := h.userService.GetUserByID(uint(id))
    if err != nil {
        var appErr *model.AppError
        if errors.As(err, &appErr) {
            switch appErr.Type {
            case model.ErrNotFound:
                response.Error(c, http.StatusNotFound, appErr.Message, nil)
            case model.ErrInvalidInput:
                response.Error(c, http.StatusBadRequest, appErr.Message, nil)
            case model.ErrUnauthorized:
                response.Error(c, http.StatusUnauthorized, appErr.Message, nil)
            default:
                response.Error(c, http.StatusInternalServerError, "Internal server error", nil)
                // 记录详细错误日志
                h.logger.WithError(err).Error("Failed to get user")
            }
            return
        }
        
        // 未知错误类型
        response.Error(c, http.StatusInternalServerError, "Internal server error", nil)
        h.logger.WithError(err).Error("Unknown error when getting user")
        return
    }
    
    response.Success(c, http.StatusOK, "User retrieved successfully", user)
}
```

4. **中间件统一错误处理**

```go
// middleware/error_handler.go
func ErrorHandler() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Next()
        
        // 检查是否有错误
        if len(c.Errors) > 0 {
            err := c.Errors.Last().Err
            var appErr *model.AppError
            
            if errors.As(err, &appErr) {
                switch appErr.Type {
                case model.ErrNotFound:
                    response.Error(c, http.StatusNotFound, appErr.Message, nil)
                case model.ErrInvalidInput:
                    response.Error(c, http.StatusBadRequest, appErr.Message, nil)
                case model.ErrUnauthorized:
                    response.Error(c, http.StatusUnauthorized, appErr.Message, nil)
                case model.ErrDuplicate:
                    response.Error(c, http.StatusConflict, appErr.Message, nil)
                default:
                    response.Error(c, http.StatusInternalServerError, "Internal server error", nil)
                    // 记录详细错误日志
                    log.WithError(err).Error("Unhandled application error")
                }
                return
            }
            
            // 未知错误类型
            response.Error(c, http.StatusInternalServerError, "Internal server error", nil)
            log.WithError(err).Error("Unknown error")
        }
    }
}
```

## 🔄 并发编程

### goroutine使用
明确生命周期和退出条件：

**推荐**：
```go
func (s *NotificationService) StartWorker(ctx context.Context) {
    go func() {
        ticker := time.NewTicker(30 * time.Second)
        defer ticker.Stop()
        
        for {
            select {
            case <-ctx.Done():
                return // 优雅退出
            case <-ticker.C:
                s.processNotifications()
            }
        }
    }()
}
```

### context传递
所有长时间运行的操作必须接受context参数：

```go
func (s *InvoiceService) ProcessInvoice(ctx context.Context, invoiceID string) error {
    // 使用context进行超时控制和取消操作
    return nil
}
```

## 🏗️ 项目特定约定

### API响应格式
使用统一的JSON响应结构：

```go
// 成功响应
c.JSON(http.StatusOK, gin.H{
    "code":    http.StatusOK,
    "message": "操作成功",
    "data":    responseData,
})

// 错误响应
c.JSON(statusCode, gin.H{
    "code":    statusCode,
    "message": errorMessage,
    "error":   err.Error(),
})
```

### 服务层设计
使用接口定义服务，实现依赖注入：

```go
type AuthService interface {
    Login(ctx context.Context, req model.LoginRequest) (*model.AuthResponse, error)
    Register(ctx context.Context, req model.RegisterRequest) (*model.AuthResponse, error)
}

type authService struct {
    db     *gorm.DB
    config *config.Config
}

func NewAuthService(db *gorm.DB, cfg *config.Config) AuthService {
    return &authService{
        db:     db,
        config: cfg,
    }
}
```

### 配置管理
使用Viper加载配置，支持环境变量覆盖：

```go
func LoadConfig(path string) (*Config, error) {
    viper.SetConfigName("config")
    viper.SetConfigType("yaml")
    viper.AddConfigPath(path)
    viper.AutomaticEnv() // 自动读取环境变量
    
    if err := viper.ReadInConfig(); err != nil {
        return nil, fmt.Errorf("读取配置文件失败: %w", err)
    }
    
    var config Config
    if err := viper.Unmarshal(&config); err != nil {
        return nil, fmt.Errorf("解析配置失败: %w", err)
    }
    
    return &config, nil
}
```

## 📦 依赖管理

### 依赖管理工具

- 使用Go Modules (`go.mod` 和 `go.sum`) 管理项目依赖
- 明确指定依赖版本，避免使用最新版本 (`@latest`)
- 定期更新依赖以修复安全漏洞

### 添加新依赖

- 添加新依赖前，先在团队内讨论是否必要
- 优先使用标准库，其次考虑成熟的第三方库
- 添加依赖后，运行 `go mod tidy` 清理未使用的依赖
- 提交代码时，同时提交更新后的 `go.mod` 和 `go.sum` 文件

### 依赖审查

- 定期使用 `go list -m all` 审查所有依赖
- 使用 `go mod why <package>` 了解为什么需要某个依赖
- 考虑使用 `govulncheck` 工具检查依赖中的安全漏洞

## 🔄 版本控制与代码提交

### 分支管理

- **主分支**：`main` - 稳定版本，随时可部署
- **开发分支**：`develop` - 开发中的版本，功能完成后合并
- **功能分支**：`feature/功能名称` - 从 `develop` 分支创建，完成后合并回 `develop`
- **修复分支**：`bugfix/问题描述` - 修复开发中的问题
- **热修复分支**：`hotfix/问题描述` - 修复生产环境的紧急问题，从 `main` 创建，合并回 `main` 和 `develop`
- **发布分支**：`release/版本号` - 准备发布的版本，只修复bug，不添加新功能

### 提交规范

提交信息格式：

```
<类型>(<范围>): <简短描述>

<详细描述>

<关联的问题>
```

类型包括：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码风格修改（不影响功能）
- `refactor`: 代码重构（不是新功能也不是修复bug）
- `perf`: 性能优化
- `test`: 添加或修改测试
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(user): 添加用户认证功能

实现了基于JWT的用户认证系统，包括登录、注销和token刷新功能。

Resolves: #123
```

### 代码审查

- 所有代码必须经过至少一名团队成员的审查
- 代码审查应关注：功能正确性、代码质量、性能问题、安全隐患
- 使用合并请求（Merge Request/Pull Request）进行代码审查
- 确保CI/CD流程通过后再合并代码

### 版本发布

使用语义化版本（Semantic Versioning）：

- **主版本号**：不兼容的API变更（X.y.z）
- **次版本号**：向下兼容的功能性新增（x.Y.z）
- **修订号**：向下兼容的问题修正（x.y.Z）

## 📝 日志规范

### 日志级别

- **DEBUG**: 详细的开发信息，仅在开发环境使用
- **INFO**: 一般操作信息，表示应用正常运行
- **WARN**: 潜在问题警告，不影响主要功能
- **ERROR**: 错误信息，影响特定功能但应用仍可运行
- **FATAL**: 严重错误，导致应用无法继续运行

### 日志内容

日志应包含以下信息：
- 时间戳（精确到毫秒）
- 日志级别
- 请求ID（用于跟踪单个请求的完整流程）
- 模块/组件名称
- 详细信息（包括操作、结果、错误原因等）

### 日志最佳实践

- 不要在日志中包含敏感信息（密码、token等）
- 使用结构化日志格式（如JSON），便于日志分析
- 在关键操作点记录INFO级别日志
- 捕获的异常必须记录ERROR级别日志，并包含堆栈信息
- 避免过多的DEBUG日志影响性能
- 使用统一的日志工具（如项目中的`util/logger.go`）

### 日志示例

```go
// 正确的日志记录
logger.WithFields(log.Fields{
    "user_id": userID,
    "action": "login",
    "request_id": ctx.GetString("request_id"),
}).Info("用户登录成功")

// 错误日志记录
logger.WithFields(log.Fields{
    "user_id": userID,
    "error": err.Error(),
    "request_id": ctx.GetString("request_id"),
}).Error("用户登录失败")
```

## 🧪 测试规范

### 测试命名
使用`Test`前缀加被测试函数名：

```go
func TestAuthService_Login(t *testing.T) {
    // 测试实现
}

func TestValidatePassword(t *testing.T) {
    // 测试实现
}
```

### 表驱动测试
使用结构体切片定义测试用例：

```go
func TestCalculateTax(t *testing.T) {
    tests := []struct {
        name     string
        amount   float64
        rate     float64
        expected float64
    }{
        {"正常计算", 1000.0, 0.13, 130.0},
        {"零金额", 0.0, 0.13, 0.0},
        {"负税率", 1000.0, -0.1, 0.0},
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := calculateTax(tt.amount, tt.rate)
            if result != tt.expected {
                t.Errorf("calculateTax() = %v, want %v", result, tt.expected)
            }
        })
    }
}
```

## 📚 API文档规范

### Swagger/OpenAPI

- 使用Swagger/OpenAPI规范记录API接口
- 在代码中使用注释生成Swagger文档
- 所有API端点必须有文档注释

### API注释规范

使用[swaggo/swag](https://github.com/swaggo/swag)工具生成Swagger文档，在处理器函数上添加注释：

```go
// @Summary 用户登录
// @Description 验证用户凭据并返回JWT令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body LoginRequest true "登录请求参数"
// @Success 200 {object} response.Response{data=LoginResponse} "登录成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "认证失败"
// @Router /api/v1/auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
    // 处理逻辑...
}
```

### 文档生成与维护

- 代码变更时同步更新API文档
- 使用以下命令生成最新文档：

```bash
swag init -g main_tax_system.go -o docs
```

- 在应用中集成Swagger UI，方便开发和测试：

```go
// 在main.go中
import (
    "github.com/swaggo/gin-swagger"
    "github.com/swaggo/gin-swagger/swaggerFiles"
    _ "your-project/docs" // 导入生成的文档
)

// 注册Swagger路由
router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
```

## 🔄 持续集成与部署(CI/CD)

### 持续集成

- 每次提交代码后自动运行以下流程：
  - 代码格式检查 (`go fmt`)
  - 静态代码分析 (`golangci-lint`)
  - 单元测试 (`go test`)
  - 构建检查 (`go build`)

### 持续部署

- 开发环境：合并到`develop`分支后自动部署
- 测试环境：合并到`release`分支后自动部署
- 生产环境：合并到`main`分支并手动确认后部署

### CI/CD配置示例

```yaml
# .gitlab-ci.yml 示例
stages:
  - lint
  - test
  - build
  - deploy

lint:
  stage: lint
  script:
    - go fmt ./...
    - golangci-lint run

test:
  stage: test
  script:
    - go test -v -race -coverprofile=coverage.txt ./...
    - go tool cover -func=coverage.txt

build:
  stage: build
  script:
    - go build -o tax-system ./main_tax_system.go
  artifacts:
    paths:
      - tax-system

deploy_dev:
  stage: deploy
  script:
    - deploy_script.sh dev
  only:
    - develop

deploy_prod:
  stage: deploy
  script:
    - deploy_script.sh prod
  only:
    - main
  when: manual
```

## 🔧 工具配置

### golangci-lint配置
创建`.golangci.yml`文件：

```yaml
linters-settings:
  gofmt:
    simplify: true
  goimports:
    local-prefixes: backend
  govet:
    check-shadowing: true
  misspell:
    locale: US

linters:
  enable:
    - gofmt
    - goimports
    - govet
    - ineffassign
    - misspell
    - unconvert
    - unused

run:
  timeout: 5m
  skip-dirs:
    - vendor
```

### VS Code配置
在`.vscode/settings.json`中添加：

```json
{
  "go.formatTool": "goimports",
  "go.lintTool": "golangci-lint",
  "go.lintOnSave": "package",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  }
}
```

## 🔄 代码重构指南

### 何时进行重构

- 添加新功能前，先重构相关代码以便更容易添加功能
- 修复bug前，重构相关代码以使bug更明显
- 代码审查过程中发现需要改进的地方
- 发现重复代码
- 函数或方法过长（超过50行）
- 函数参数过多（超过3个）
- 过度嵌套的条件语句
- 性能测试发现瓶颈

### 重构技巧

1. **提取函数/方法**：将代码片段提取为独立函数

```go
// 重构前
func ProcessUser(user *User) error {
    // 验证用户
    if user.Name == "" {
        return errors.New("name cannot be empty")
    }
    if user.Age < 18 {
        return errors.New("user must be at least 18 years old")
    }
    
    // 处理用户
    // ...
}

// 重构后
func ValidateUser(user *User) error {
    if user.Name == "" {
        return errors.New("name cannot be empty")
    }
    if user.Age < 18 {
        return errors.New("user must be at least 18 years old")
    }
    return nil
}

func ProcessUser(user *User) error {
    if err := ValidateUser(user); err != nil {
        return err
    }
    
    // 处理用户
    // ...
}
```

2. **简化条件表达式**：使用提前返回减少嵌套

```go
// 重构前
func ProcessOrder(order *Order) error {
    if order != nil {
        if order.Status == "pending" {
            if order.Amount > 0 {
                // 处理订单
                return nil
            } else {
                return errors.New("invalid amount")
            }
        } else {
            return errors.New("order not pending")
        }
    } else {
        return errors.New("nil order")
    }
}

// 重构后
func ProcessOrder(order *Order) error {
    if order == nil {
        return errors.New("nil order")
    }
    
    if order.Status != "pending" {
        return errors.New("order not pending")
    }
    
    if order.Amount <= 0 {
        return errors.New("invalid amount")
    }
    
    // 处理订单
    return nil
}
```

3. **引入接口**：使代码更灵活，便于测试

```go
// 重构前
func SendNotification(user *User, message string) error {
    // 直接发送邮件
    return sendEmail(user.Email, message)
}

// 重构后
type Notifier interface {
    Notify(email, message string) error
}

type EmailNotifier struct{}

func (n EmailNotifier) Notify(email, message string) error {
    return sendEmail(email, message)
}

func SendNotification(user *User, message string, notifier Notifier) error {
    return notifier.Notify(user.Email, message)
}
```

4. **使用函数选项模式**：处理多参数函数

```go
// 重构前
func NewServer(addr string, port int, timeout time.Duration, maxConn int) *Server {
    // ...
}

// 重构后
type ServerOption func(*Server)

func WithTimeout(timeout time.Duration) ServerOption {
    return func(s *Server) {
        s.timeout = timeout
    }
}

func WithMaxConnections(maxConn int) ServerOption {
    return func(s *Server) {
        s.maxConn = maxConn
    }
}

func NewServer(addr string, port int, opts ...ServerOption) *Server {
    s := &Server{
        addr:    addr,
        port:    port,
        timeout: defaultTimeout,
        maxConn: defaultMaxConn,
    }
    
    for _, opt := range opts {
        opt(s)
    }
    
    return s
}

// 使用
server := NewServer("localhost", 8080, WithTimeout(time.Second*30), WithMaxConnections(100))
```

### 重构注意事项

- 小步重构，每次只改动一小部分代码
- 重构前确保有足够的测试覆盖
- 每次重构后运行测试，确保功能正常
- 重构和添加新功能分开进行，不要同时做
- 使用版本控制，便于回滚
- 重构后进行代码审查

## 🔍 代码审查清单

- [ ] 文件使用UTF-8编码
- [ ] 导入语句按标准库、第三方库、项目内部包顺序排列
- [ ] 函数和变量命名符合Go命名约定
- [ ] 所有导出函数都有文档注释
- [ ] 错误处理得当，不忽略错误
- [ ] 使用context进行超时控制
- [ ] API响应格式统一
- [ ] 测试覆盖核心业务逻辑
- [ ] 代码通过golangci-lint检查

## 🚀 最佳实践示例

### 完整的Handler示例
```go
// api/enterprise_handler.go
package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"backend/model"
	"backend/service"
	"backend/util"
)

type EnterpriseHandler struct {
	enterpriseService service.EnterpriseService
}

func NewEnterpriseHandler(service service.EnterpriseService) *EnterpriseHandler {
	return &EnterpriseHandler{
		enterpriseService: service,
	}
}

// GetEnterprises 获取企业列表
func (h *EnterpriseHandler) GetEnterprises(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	filter := model.EnterpriseFilter{
		Page:     page,
		PageSize: pageSize,
		Status:   c.Query("status"),
		Industry: c.Query("industry"),
	}

	// 调用服务层
	enterprises, total, err := h.enterpriseService.GetEnterprises(c.Request.Context(), filter)
	if err != nil {
		util.ErrorResponse(c, http.StatusInternalServerError, "获取企业列表失败", err)
		return
	}

	// 返回成功响应
	util.SuccessResponse(c, gin.H{
		"enterprises": enterprises,
		"total":       total,
		"page":        page,
		"pageSize":    pageSize,
	})
}
```

### 完整的Service示例
```go
// service/enterprise_service.go
package service

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"backend/model"
	"backend/util"
)

type EnterpriseService interface {
	GetEnterprises(ctx context.Context, filter model.EnterpriseFilter) ([]model.Enterprise, int64, error)
	CreateEnterprise(ctx context.Context, req model.EnterpriseCreateRequest) (*model.Enterprise, error)
}

type enterpriseService struct {
	db *gorm.DB
}

func NewEnterpriseService(db *gorm.DB) EnterpriseService {
	return &enterpriseService{db: db}
}

func (s *enterpriseService) GetEnterprises(ctx context.Context, filter model.EnterpriseFilter) ([]model.Enterprise, int64, error) {
	var enterprises []model.Enterprise
	var total int64

	query := s.db.WithContext(ctx).Model(&model.Enterprise{})

	// 应用过滤条件
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.Industry != "" {
		query = query.Where("industry = ?", filter.Industry)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("统计企业数量失败: %w", err)
	}

	// 分页查询
	offset := (filter.Page - 1) * filter.PageSize
	if err := query.Offset(offset).Limit(filter.PageSize).Find(&enterprises).Error; err != nil {
		return nil, 0, fmt.Errorf("查询企业列表失败: %w", err)
	}

	return enterprises, total, nil
}
```

### 统一响应工具
```go
// util/response.go
package util

import (
	"github.com/gin-gonic/gin"
)

// SuccessResponse 成功响应
func SuccessResponse(c *gin.Context, data interface{}) {
	c.JSON(200, gin.H{
		"code":    200,
		"message": "操作成功",
		"data":    data,
	})
}

// ErrorResponse 错误响应
func ErrorResponse(c *gin.Context, code int, message string, err error) {
	response := gin.H{
		"code":    code,
		"message": message,
	}

	if err != nil {
		response["error"] = err.Error()
	}

	c.JSON(code, response)
}

// ValidationErrorResponse 验证错误响应
func ValidationErrorResponse(c *gin.Context, errors map[string]string) {
	c.JSON(400, gin.H{
		"code":    400,
		"message": "请求参数验证失败",
		"errors":  errors,
	})
}
```

## 🔒 安全最佳实践

### 输入验证
```go
func validateEnterpriseRequest(req model.EnterpriseCreateRequest) error {
	if req.Name == "" {
		return errors.New("企业名称不能为空")
	}

	if len(req.UnifiedSocialCreditCode) != 18 {
		return errors.New("统一社会信用代码必须为18位")
	}

	// 验证信用代码格式
	if !isValidCreditCode(req.UnifiedSocialCreditCode) {
		return errors.New("统一社会信用代码格式不正确")
	}

	return nil
}
```

### SQL注入防护
```go
// 推荐：使用参数化查询
func (s *userService) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	err := s.db.WithContext(ctx).Where("email = ?", email).First(&user).Error
	return &user, err
}

// 不推荐：字符串拼接
func (s *userService) GetUserByEmailUnsafe(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	query := fmt.Sprintf("SELECT * FROM users WHERE email = '%s'", email) // 危险！
	err := s.db.WithContext(ctx).Raw(query).First(&user).Error
	return &user, err
}
```

### 敏感信息处理
```go
type User struct {
	ID           string `json:"id"`
	Email        string `json:"email"`
	PasswordHash string `json:"-" gorm:"not null"` // 不返回给客户端
	Name         string `json:"name"`
}

// 清理敏感信息
func (u *User) ToResponse() model.UserResponse {
	return model.UserResponse{
		ID:    u.ID,
		Email: u.Email,
		Name:  u.Name,
		// 不包含PasswordHash
	}
}
```

## 📊 性能优化指南

### 数据库查询优化
```go
// 预加载关联数据
func (s *enterpriseService) GetEnterpriseWithInvoices(ctx context.Context, id string) (*model.Enterprise, error) {
	var enterprise model.Enterprise
	err := s.db.WithContext(ctx).
		Preload("Invoices").
		Where("id = ?", id).
		First(&enterprise).Error
	return &enterprise, err
}

// 选择特定字段
func (s *enterpriseService) GetEnterpriseNames(ctx context.Context) ([]model.Enterprise, error) {
	var enterprises []model.Enterprise
	err := s.db.WithContext(ctx).
		Select("id, name").
		Find(&enterprises).Error
	return enterprises, err
}

// 批量操作
func (s *enterpriseService) CreateEnterprises(ctx context.Context, enterprises []model.Enterprise) error {
	return s.db.WithContext(ctx).CreateInBatches(enterprises, 100).Error
}
```

### 内存管理
```go
// 使用对象池减少GC压力
var bufferPool = sync.Pool{
	New: func() interface{} {
		return make([]byte, 0, 1024)
	},
}

func processLargeData(data []byte) []byte {
	buf := bufferPool.Get().([]byte)
	defer bufferPool.Put(buf[:0])

	// 处理数据
	result := append(buf, data...)

	// 返回副本
	return append([]byte(nil), result...)
}
```

## 🗄️ 数据库连接

### 数据库配置管理

使用环境变量或配置文件管理数据库连接信息，避免硬编码敏感信息：

```go
// config/database.go
type DatabaseConfig struct {
    Host         string
    Port         int
    Username     string
    Password     string
    Database     string
    MaxIdleConns int
    MaxOpenConns int
    MaxLifetime  time.Duration
}

func LoadDatabaseConfig() DatabaseConfig {
    return DatabaseConfig{
        Host:         getEnvOrDefault("DB_HOST", "localhost"),
        Port:         getEnvAsInt("DB_PORT", 3306),
        Username:     getEnvOrDefault("DB_USERNAME", "root"),
        Password:     getEnvOrDefault("DB_PASSWORD", ""),
        Database:     getEnvOrDefault("DB_DATABASE", "smeasy_tax"),
        MaxIdleConns: getEnvAsInt("DB_MAX_IDLE_CONNS", 10),
        MaxOpenConns: getEnvAsInt("DB_MAX_OPEN_CONNS", 100),
        MaxLifetime:  getEnvAsDuration("DB_MAX_LIFETIME", time.Hour),
    }
}
```

### 数据库初始化

```go
// bootstrap/database.go
func InitDB() *gorm.DB {
    config := config.LoadDatabaseConfig()
    
    dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
        config.Username, config.Password, config.Host, config.Port, config.Database)
    
    logMode := logger.Silent
    if os.Getenv("APP_ENV") == "development" {
        logMode = logger.Info
    }
    
    db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
        Logger: logger.Default.LogMode(logMode),
    })
    if err != nil {
        log.Fatalf("Failed to connect to database: %v", err)
    }
    
    // 设置连接池
    sqlDB, _ := db.DB()
    sqlDB.SetMaxIdleConns(config.MaxIdleConns)
    sqlDB.SetMaxOpenConns(config.MaxOpenConns)
    sqlDB.SetConnMaxLifetime(config.MaxLifetime)
    
    return db
}
```

### 环境配置示例

```bash
# .env.example (不包含实际密码)
APP_ENV=development
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=
DB_DATABASE=smeasy_tax_dev  # 开发环境数据库
# DB_DATABASE=smeasy_tax    # 生产环境数据库
```

**注意**：实际的`.env`文件不应提交到版本控制系统中，应该添加到`.gitignore`文件中。数据模型文件存放在backend/sql目录中。

## 🧪 测试规范详解

### 测试工具和框架
- **标准库**：优先使用Go标准库`testing`包进行单元测试
- **断言库**：可选择使用`github.com/stretchr/testify/assert`提高测试可读性
- **模拟库**：推荐使用`github.com/golang/mock`或`github.com/stretchr/testify/mock`

### 测试文件组织
- **命名规则**：测试文件以`_test.go`结尾，与被测试文件放在同一目录
- **测试函数**：使用`Test`前缀加被测试函数名
- **基准测试**：使用`Benchmark`前缀命名
- **示例测试**：使用`Example`前缀命名

```go
// user_service.go的测试文件应命名为user_service_test.go

// 单元测试
func TestUserService_GetByID(t *testing.T) {...}

// 基准测试
func BenchmarkUserService_GetByID(b *testing.B) {...}

// 示例测试
func ExampleUserService_GetByID() {...}
```

### 测试覆盖率要求
- **核心业务逻辑**：测试覆盖率应达到80%以上
- **工具函数**：测试覆盖率应达到70%以上
- **命令**：运行`go test -cover ./...`检查测试覆盖率
- **报告生成**：使用`go test -coverprofile=coverage.out ./...`生成覆盖率报告

### 单元测试最佳实践

#### 表驱动测试
使用结构体切片定义测试用例，提高测试可维护性：

```go
func TestCalculateTax(t *testing.T) {
	tests := []struct {
		name     string
		amount   float64
		rate     float64
		expected float64
	}{
		{"正常计算", 1000.0, 0.13, 130.0},
		{"零金额", 0.0, 0.13, 0.0},
		{"负税率", 1000.0, -0.1, 0.0},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := calculateTax(tt.amount, tt.rate)
			if result != tt.expected {
				t.Errorf("calculateTax() = %v, want %v", result, tt.expected)
			}
		})
	}
}
```

#### 使用子测试
使用`t.Run()`创建子测试，便于单独运行特定测试：

```go
func TestUserService(t *testing.T) {
	// 通用测试设置
	db := setupTestDB(t)
	service := NewUserService(db)
	
	t.Run("GetByID", func(t *testing.T) {
		// GetByID测试逻辑
	})
	
	t.Run("Create", func(t *testing.T) {
		// Create测试逻辑
	})
}
```

#### 模拟外部依赖
使用模拟对象隔离被测试代码：

```go
// 使用testify/mock
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) GetByID(ctx context.Context, id string) (*model.User, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func TestUserService_GetByID(t *testing.T) {
	repo := new(MockUserRepository)
	service := NewUserService(repo)
	
	// 设置期望
	repo.On("GetByID", mock.Anything, "123").Return(&model.User{ID: "123", Name: "测试用户"}, nil)
	
	// 执行测试
	user, err := service.GetByID(context.Background(), "123")
	
	// 断言结果
	assert.NoError(t, err)
	assert.Equal(t, "123", user.ID)
	assert.Equal(t, "测试用户", user.Name)
	
	// 验证期望被调用
	repo.AssertExpectations(t)
}
```

### 集成测试

#### 数据库集成测试
使用测试数据库或内存数据库进行集成测试：

```go
func setupTestDB(t *testing.T) *gorm.DB {
	// 使用测试配置连接数据库
	db, err := gorm.Open(mysql.Open("root:Aa123456@tcp(localhost:3306)/smeasy_tax_test?charset=utf8mb4&parseTime=True&loc=Local"))
	if err != nil {
		t.Fatalf("连接测试数据库失败: %v", err)
	}
	
	// 清理测试数据
	db.Exec("TRUNCATE TABLE users")
	
	// 初始化测试数据
	db.Create(&model.User{ID: "test1", Name: "测试用户1"})
	
	return db
}

func TestUserRepository_GetByID_Integration(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过集成测试")
	}
	
	db := setupTestDB(t)
	repo := NewUserRepository(db)
	
	user, err := repo.GetByID(context.Background(), "test1")
	
	assert.NoError(t, err)
	assert.Equal(t, "test1", user.ID)
	assert.Equal(t, "测试用户1", user.Name)
}
```

#### HTTP处理器测试
使用Gin的测试工具测试HTTP处理器：

```go
func TestAuthHandler_Login(t *testing.T) {
	// 设置模拟服务
	mockService := new(MockAuthService)
	mockService.On("Login", mock.Anything, mock.MatchedBy(func(req model.LoginRequest) bool {
		return req.Email == "<EMAIL>" && req.Password == "password123"
	})).Return(&model.AuthResponse{Token: "test-token"}, nil)
	
	// 创建测试路由
	router := gin.New()
	handler := NewAuthHandler(mockService)
	router.POST("/login", handler.Login)
	
	// 创建测试请求
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/login", strings.NewReader(`{"email":"<EMAIL>","password":"password123"}`)) 
	req.Header.Set("Content-Type", "application/json")
	
	// 执行请求
	router.ServeHTTP(w, req)
	
	// 断言响应
	assert.Equal(t, http.StatusOK, w.Code)
	
	// 解析响应体
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	
	// 验证响应内容
	assert.Equal(t, float64(200), response["code"])
	assert.Equal(t, "登录成功", response["message"])
	
	data, ok := response["data"].(map[string]interface{})
	assert.True(t, ok)
	assert.Equal(t, "test-token", data["token"])
	
	// 验证期望被调用
	mockService.AssertExpectations(t)
}
```

### 测试辅助函数
创建测试辅助函数提高测试代码复用性：

```go
// test/helpers.go
package test

import (
	"testing"
	
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	
	"backend/model"
)

// SetupTestDB 初始化测试数据库
func SetupTestDB(t *testing.T) *gorm.DB {
	// 实现...
}

// CreateTestUser 创建测试用户
func CreateTestUser(t *testing.T, db *gorm.DB) *model.User {
	user := &model.User{
		ID:    util.GenerateID(),
		Name:  "测试用户",
		Email: "<EMAIL>",
	}
	
	if err := db.Create(user).Error; err != nil {
		t.Fatalf("创建测试用户失败: %v", err)
	}
	
	return user
}
```

### 测试环境配置
使用环境变量或配置文件区分测试环境：

```go
// config/config.go
func LoadConfig() (*Config, error) {
	// 检查是否在测试环境
	if os.Getenv("GO_ENV") == "test" {
		return &Config{
			Database: DatabaseConfig{
				Host:     "localhost",
				Port:     3306,
				Username: "root",
				Password: "Aa123456",
				DBName:   "smeasy_tax_test",
			},
			// 其他测试配置
		}, nil
	}
	
	// 正常环境配置加载逻辑
	// ...
}
```

### 测试运行脚本
创建测试运行脚本简化测试执行：

```bash
#!/bin/bash
# test/run_tests.sh

# 设置测试环境变量
export GO_ENV=test

# 运行所有测试
go test -v ./...

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html
```



## 🌐 API响应格式规范

### 统一响应结构
所有API接口必须返回统一的响应格式：

```go
type StandardResponse struct {
    Code      int         `json:"code"`      // 响应状态码
    Message   string      `json:"message"`   // 响应消息
    Data      interface{} `json:"data"`      // 响应数据
    Timestamp int64       `json:"timestamp"` // 响应时间戳
    RequestID string      `json:"requestId"` // 请求ID
}
```

### 分页响应格式
分页数据必须使用统一的结构：

```go
type PaginatedData struct {
    Items    interface{} `json:"items"`    // 数据列表，统一使用items字段
    Total    int64       `json:"total"`    // 总记录数
    Page     int         `json:"page"`     // 当前页码
    PageSize int         `json:"pageSize"` // 每页大小
    Pages    int         `json:"pages"`    // 总页数
}
```

### 响应工具函数
使用统一的响应工具函数：

```go
// 成功响应
util.Success(c, data, "操作成功")

// 分页响应
util.SuccessWithPagination(c, items, total, page, pageSize, "获取数据成功")

// 列表响应（非分页）
util.SuccessWithList(c, items, "获取列表成功")

// 错误响应
util.BadRequest(c, "参数错误", err)
util.Unauthorized(c, "未授权", err)
util.NotFound(c, "资源不存在", err)
util.InternalServerError(c, "服务器错误", err)
```

### 状态码规范
- **200**: 操作成功
- **201**: 创建成功
- **400**: 请求参数错误
- **401**: 未授权访问
- **403**: 禁止访问
- **404**: 资源不存在
- **422**: 数据验证失败
- **500**: 服务器内部错误

### 数据字段命名规范
- 分页数据列表统一使用 `items` 字段
- 总记录数使用 `total` 字段
- 当前页码使用 `page` 字段
- 每页大小使用 `pageSize` 字段
- 总页数使用 `pages` 字段

### 示例响应

**成功响应示例**：
```json
{
  "code": 200,
  "message": "获取企业列表成功",
  "data": {
    "items": [
      {
        "id": "ent_001",
        "name": "测试企业",
        "code": "TEST001"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "pages": 1
  },
  "timestamp": 1642694400,
  "requestId": "req_123456"
}
```

**错误响应示例**：
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null,
  "timestamp": 1642694400,
  "requestId": "req_123456"
}
```

## 📚 参考资源

- [Go官方代码风格指南](https://golang.org/doc/effective_go.html)
- [Uber Go语言编码规范](https://github.com/uber-go/guide)
- [golangci-lint文档](https://golangci-lint.run/)
- [Gin框架文档](https://gin-gonic.com/docs/)
- [GORM文档](https://gorm.io/docs/)
- [Go语言安全编程指南](https://github.com/securego/gosec)
- [Go性能优化指南](https://github.com/dgryski/go-perfbook)
