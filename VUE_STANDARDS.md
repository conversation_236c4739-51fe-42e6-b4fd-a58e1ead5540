# Vue 3前端编码规范

## 📋 概述

本文档为智能税务管理系统Vue 3前端代码制定统一的编码规范，基于项目现有的Vue 3 + Composition API + Ant Design Vue + Pinia技术栈，确保代码质量、可维护性和团队协作效率。

### 文档版本

| 版本号 | 更新日期 | 更新内容 | 作者 |
|-------|---------|---------|------|
| v1.0.0 | 2024-01-01 | 初始版本 | 前端团队 |
| v1.1.0 | 2024-06-01 | 增强规范，添加测试、国际化、安全等内容 | 前端团队 |

## 📁 文件组织

### 文件编码
- **强制要求**：所有源文件必须使用UTF-8编码
- **换行符**：统一使用Unix风格换行符（LF）

### 单文件组件结构
**推荐顺序**：template → script → style

```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
// 脚本内容
</script>

<style scoped>
/* 样式内容 */
</style>
```

### 目录结构
按功能模块组织，遵循项目现有结构：

```
frontend/src/
├── views/                  # 页面组件
│   ├── auth/              # 认证相关页面
│   ├── user/              # 用户相关页面
│   ├── enterprise/        # 企业管理页面
│   ├── invoice/           # 发票管理页面
│   ├── dashboard/         # 仪表盘页面
│   └── layout/            # 布局组件
├── components/            # 可复用组件
│   ├── common/           # 全局通用组件
│   └── business/         # 业务特定组件
├── api/                  # API服务层
├── store/                # Pinia状态管理
├── router/               # 路由配置
├── utils/                # 工具函数
└── assets/               # 静态资源
```

### 目录命名
- **规则**：使用kebab-case（短横线分隔）
- **示例**：`user-profile`, `invoice-management`, `tax-declaration`

## 🏷️ 组件命名规范

### 组件名称
- **文件名**：使用PascalCase
- **组件名**：与文件名保持一致
- **多词组件**：始终使用多词命名避免与HTML元素冲突

**推荐**：
```vue
<!-- UserProfile.vue -->
<script>
export default defineComponent({
  name: 'UserProfile',
  // ...
})
</script>
```

**不推荐**：
```vue
<!-- profile.vue -->
<script>
export default defineComponent({
  name: 'Profile', // 单词命名
  // ...
})
</script>
```

### 组件引用
在模板中使用PascalCase或kebab-case：

**推荐**：
```vue
<template>
  <UserProfile />
  <!-- 或 -->
  <user-profile />
</template>
```



## 🎨 UI框架规范

### macOS (Aqua) 风格

### 全局样式变量（CSS 变量）
- **颜色**
    - `--color-bg`: 页面背景色（Light: `#FFFFFF`，Dark: `#1C1C1E`）
    - `--color-surface`: 容器背景（Light: `rgba(255,255,255,0.8)`，Dark: `rgba(28,28,30,0.8)`）
    - `--color-text`: 主文本色（Light: `#1C1C1E`，Dark: `#EFEFF4`）
    - `--color-primary`: 系统蓝 `#0A84FF`
    - `--color-divider`: 分割线色 `rgba(0,0,0,0.12)`

- **圆角与间距**
    - `--radius-base`: `6px`
    - `--spacing-unit`: `8px`

- **动效**
    - `--transition-base`: `all 150ms cubic-bezier(0.4,0,0.2,1)`

---

### 组件属性顺序规范
- **指令顺序**：`v-model` → `v-bind` → `v-on` → 其他属性
- **类名顺序**：`base-class` → `modifier-class` → `utility-class`
- **样式顺序**：外部样式表 → 模块化样式 → 内联样式

---

### 推荐 Vue 3 示例

```vue
<template>
  <div
      class="layout"
      :class="{ 'dark-mode': isDark }"
  >
    <!-- Sidebar -->
    <aside
        class="sidebar"
        @mouseover="expandSidebar"
        @mouseleave="collapseSidebar"
    >
      <input
          v-model="filter"
          type="search"
          placeholder="搜索…"
          class="input-search"
      />
      <ul class="nav-list">
        <li
            v-for="item in filteredItems"
            :key="item.id"
            @click="selectItem(item)"
            :class="['nav-item', { active: item.id === selectedId }]"
        >
          {{ item.label }}
        </li>
      </ul>
    </aside>

    <!-- Main Content -->
    <section class="content">
      <header class="header">
        <h1 class="title">{{ pageTitle }}</h1>
        <button
            class="btn-primary"
            @click="handleAction"
        >
          操作按钮
        </button>
      </header>
      <div class="card">
        <p class="text">这里是内容区块。</p>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'

  const isDark = ref(false)
  const filter = ref('')
  const items = ref([{ id: 1, label: '首页' }, { id: 2, label: '设置' }])
  const selectedId = ref(1)
  const pageTitle = ref('Dashboard')

  const filteredItems = computed(() =>
      items.value.filter(i => i.label.includes(filter.value))
  )

  function expandSidebar() { /* … */ }
  function collapseSidebar() { /* … */ }
  function selectItem(item: any) { selectedId.value = item.id }
  function handleAction() { /* … */ }
</script>

<style scoped>
  .layout {
    display: flex;
    background: var(--color-bg);
    transition: var(--transition-base);
  }
  .dark-mode {
    background: var(--color-bg, #1C1C1E);
  }
  .sidebar {
    width: 240px;
    backdrop-filter: blur(20px);
    background: var(--color-surface);
    padding: var(--spacing-unit);
    border-right: 1px solid var(--color-divider);
    transition: width 200ms;
  }
  .input-search {
    width: 100%;
    padding: var(--spacing-unit);
    border: 1px solid var(--color-divider);
    border-radius: var(--radius-base);
    transition: var(--transition-base);
  }
  .nav-item {
    padding: var(--spacing-unit) 0;
    cursor: pointer;
    transition: var(--transition-base);
  }
  .nav-item.active,
  .nav-item:hover {
    color: var(--color-primary);
  }
  .content {
    flex: 1;
    padding: calc(var(--spacing-unit) * 2);
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-unit);
    backdrop-filter: blur(20px);
    background: var(--color-surface);
    padding: var(--spacing-unit);
    border-bottom: 1px solid var(--color-divider);
  }
  .btn-primary {
    padding: var(--spacing-unit) var(--spacing-unit * 1.5);
    border: none;
    border-radius: var(--radius-base);
    background: var(--color-primary);
    color: #fff;
    cursor: pointer;
    transition: var(--transition-base);
  }
  .btn-primary:active {
    transform: scale(0.97);
  }
  .card {
    background: var(--color-surface);
    border-radius: var(--radius-base);
    padding: var(--spacing-unit * 2);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: var(--transition-base);
  }
  .text {
    color: var(--color-text);
    line-height: 1.6;
  }
</style>

### Ant Design Vue使用
- **组件前缀**：统一使用`a-`前缀
- **属性顺序**：v-model → v-bind → v-on → 其他属性

**推荐**：
```vue
<template>
  <a-form
    v-model:value="formData"
    :rules="formRules"
    @submit="handleSubmit"
    layout="vertical"
    class="user-form"
  >
    <a-form-item
      name="email"
      label="邮箱"
      :rules="[{ required: true, type: 'email', message: '请输入有效邮箱' }]"
    >
      <a-input
        v-model:value="formData.email"
        placeholder="请输入邮箱"
        size="large"
      />
    </a-form-item>
  </a-form>
</template>
```

### 自定义组件
继承Ant Design Vue的设计语言：

```vue
<template>
  <div class="custom-card">
    <div class="custom-card__header">
      <slot name="header" />
    </div>
    <div class="custom-card__content">
      <slot />
    </div>
  </div>
</template>

<style scoped>
.custom-card {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
}
</style>
```

## 🔧 Composition API规范

### setup函数组织
**推荐顺序**：

```vue
<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'

export default defineComponent({
  name: 'UserProfile',
  components: {
    // 组件注册
  },
  props: {
    // props定义
  },
  emits: ['update', 'delete'],
  setup(props, { emit }) {
    // 1. 导入的组合式函数
    const router = useRouter()
    const userStore = useUserStore()
    
    // 2. 响应式数据
    const loading = ref(false)
    const formData = reactive({
      name: '',
      email: ''
    })
    
    // 3. 计算属性
    const isFormValid = computed(() => {
      return formData.name && formData.email
    })
    
    // 4. 方法
    const handleSubmit = async () => {
      loading.value = true
      try {
        await userStore.updateProfile(formData)
        emit('update', formData)
      } catch (error) {
        console.error('更新失败:', error)
      } finally {
        loading.value = false
      }
    }
    
    // 5. 生命周期钩子
    onMounted(() => {
      fetchUserData()
    })
    
    // 6. 返回语句
    return {
      loading,
      formData,
      isFormValid,
      handleSubmit
    }
  }
})
</script>
```

### 响应式数据选择
- **简单数据类型**：使用`ref`
- **复杂对象**：使用`reactive`
- **不变数据**：使用`readonly`

**推荐**：
```javascript
// 简单类型
const count = ref(0)
const loading = ref(false)
const userName = ref('')

// 复杂对象
const formData = reactive({
  user: {
    name: '',
    email: '',
    profile: {
      bio: '',
      location: ''
    }
  }
})

// 只读数据
const config = readonly({
  apiUrl: 'https://api.example.com',
  timeout: 5000
})
```

### 组合式函数
使用`use`前缀命名：

```javascript
// composables/useUserProfile.js
import { ref, reactive } from 'vue'
import { updateUserProfile } from '@/api/user'

export function useUserProfile() {
  const loading = ref(false)
  const userInfo = reactive({
    name: '',
    email: '',
    phone: ''
  })
  
  const updateProfile = async (data) => {
    loading.value = true
    try {
      const response = await updateUserProfile(data)
      Object.assign(userInfo, response.data)
      return response
    } finally {
      loading.value = false
    }
  }
  
  return {
    loading,
    userInfo,
    updateProfile
  }
}
```

## 📂 目录结构约定

### views目录
页面级组件，对应路由：

```
views/
├── auth/
│   ├── Login.vue          # 登录页面
│   └── Register.vue       # 注册页面
├── user/
│   ├── Profile.vue        # 用户资料
│   └── Settings.vue       # 用户设置
├── enterprise/
│   ├── List.vue           # 企业列表
│   ├── Detail.vue         # 企业详情
│   └── Edit.vue           # 企业编辑
└── dashboard/
    └── Dashboard.vue      # 仪表盘
```

### components目录
可复用组件，按功能分类：

```
components/
├── common/                # 全局通用组件
│   ├── PageHeader.vue     # 页面标题
│   ├── DataTable.vue      # 数据表格
│   └── StatusBadge.vue    # 状态标签
└── business/              # 业务特定组件
    ├── InvoiceCard.vue    # 发票卡片
    └── TaxCalculator.vue  # 税额计算器
```

## 🎨 CSS/SCSS规范

### 作用域样式
默认使用scoped属性：

```vue
<style scoped>
.user-profile {
  padding: 24px;
}

.user-profile__header {
  margin-bottom: 16px;
}

.user-profile__content {
  background: #fff;
}
</style>
```

### BEM命名方法论
使用Block__Element--Modifier模式：

```scss
// Block
.invoice-card {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  
  // Element
  &__header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  &__content {
    padding: 16px;
  }
  
  &__footer {
    padding: 16px;
    text-align: right;
  }
  
  // Modifier
  &--highlighted {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
  
  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}
```

### SCSS变量
使用变量定义常用值：

```scss
// variables.scss
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;

$border-radius-base: 6px;
$border-color-base: #d9d9d9;

$font-size-base: 14px;
$font-size-lg: 16px;
$font-size-sm: 12px;

$spacing-xs: 8px;
$spacing-sm: 12px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
```

## 🗃️ 状态管理（Pinia）

### Store组织
按功能模块划分store：

```javascript
// store/user.js
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    token: localStorage.getItem('token') || null,
    refreshToken: localStorage.getItem('refresh_token') || null
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,
    userName: (state) => state.userInfo?.name || '用户',
    userRole: (state) => state.userInfo?.role || 'user'
  },

  actions: {
    async login(accessToken, refreshToken) {
      this.token = accessToken
      this.refreshToken = refreshToken
      
      localStorage.setItem('token', accessToken)
      if (refreshToken) {
        localStorage.setItem('refresh_token', refreshToken)
      }
      
      await this.fetchUserInfo()
    },

    async fetchUserInfo() {
      try {
        const response = await getUserInfo()
        if (response?.code === 200) {
          this.userInfo = response.data
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },

    logout() {
      this.userInfo = null
      this.token = null
      this.refreshToken = null
      
      localStorage.removeItem('token')
      localStorage.removeItem('refresh_token')
    }
  }
})
```

### 组件中使用Store
使用storeToRefs获取响应式状态：

```vue
<script>
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store/user'

export default defineComponent({
  setup() {
    const userStore = useUserStore()
    
    // 获取响应式状态
    const { userInfo, isLoggedIn } = storeToRefs(userStore)
    
    // 调用actions
    const handleLogout = () => {
      userStore.logout()
    }
    
    return {
      userInfo,
      isLoggedIn,
      handleLogout
    }
  }
})
</script>
```

## 🛣️ 路由组织

### 路由命名
使用kebab-case的路由名称：

```javascript
const routes = [
  {
    path: '/user/profile',
    name: 'user-profile',
    component: () => import('@/views/user/Profile.vue'),
    meta: { 
      title: '个人资料',
      requiresAuth: true 
    }
  },
  {
    path: '/enterprise/detail/:id',
    name: 'enterprise-detail',
    component: () => import('@/views/enterprise/Detail.vue'),
    meta: { 
      title: '企业详情',
      requiresAuth: true 
    }
  }
]
```

### 嵌套路由
按UI层次结构组织：

```javascript
{
  path: '/',
  component: Layout,
  children: [
    {
      path: 'dashboard',
      name: 'dashboard',
      component: () => import('@/views/dashboard/Dashboard.vue')
    },
    {
      path: 'enterprise',
      component: RouteView,
      children: [
        {
          path: 'list',
          name: 'enterprise-list',
          component: () => import('@/views/enterprise/List.vue')
        },
        {
          path: 'detail/:id',
          name: 'enterprise-detail',
          component: () => import('@/views/enterprise/Detail.vue')
        }
      ]
    }
  ]
}
```

### 路由守卫
使用全局前置守卫处理认证：

```javascript
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 税易通` : '税易通'
  
  // 检查认证
  if (to.meta.requiresAuth && !localStorage.getItem('token')) {
    next('/login')
    return
  }
  
  next()
})
```

## 🔗 组件通信

### Props/Emits声明
明确声明props和emits：

```vue
<script>
export default defineComponent({
  name: 'UserCard',
  props: {
    user: {
      type: Object,
      required: true
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['edit', 'delete', 'update'],
  setup(props, { emit }) {
    const handleEdit = () => {
      emit('edit', props.user.id)
    }
    
    const handleDelete = () => {
      emit('delete', props.user.id)
    }
    
    return {
      handleEdit,
      handleDelete
    }
  }
})
</script>
```

### Provide/Inject
用于深层组件通信：

```javascript
// 父组件
import { provide, ref } from 'vue'

export default defineComponent({
  setup() {
    const theme = ref('light')
    
    provide('theme', theme)
    
    return { theme }
  }
})

// 子组件
import { inject } from 'vue'

export default defineComponent({
  setup() {
    const theme = inject('theme', 'light') // 默认值
    
    return { theme }
  }
})
```

## ⚡ 性能优化

### 组件懒加载
对路由组件使用动态导入：

```javascript
const routes = [
  {
    path: '/enterprise',
    component: () => import('@/views/enterprise/List.vue')
  }
]
```

### 计算属性vs方法
复杂数据处理使用计算属性：

**推荐**：
```javascript
const filteredUsers = computed(() => {
  return users.value.filter(user => 
    user.name.toLowerCase().includes(searchTerm.value.toLowerCase())
  )
})
```

**不推荐**：
```javascript
const getFilteredUsers = () => {
  return users.value.filter(user => 
    user.name.toLowerCase().includes(searchTerm.value.toLowerCase())
  )
}
```

### 异步组件
使用defineAsyncComponent加载非关键组件：

```javascript
import { defineAsyncComponent } from 'vue'

const AsyncChart = defineAsyncComponent(() => import('./Chart.vue'))
```

## 🔧 工具配置

### ESLint配置
在`.eslintrc.js`中配置：

```javascript
module.exports = {
  extends: [
    'plugin:vue/vue3-essential',
    '@vue/standard'
  ],
  rules: {
    'vue/multi-word-component-names': 'error',
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/component-name-in-template-casing': ['error', 'PascalCase'],
    'vue/prop-name-casing': ['error', 'camelCase']
  }
}
```

### Prettier配置
在`.prettierrc`中配置：

```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "none",
  "printWidth": 100,
  "vueIndentScriptAndStyle": true
}
```

## ✅ 代码审查清单

- [ ] 组件使用多词命名
- [ ] 文件名使用PascalCase
- [ ] Props和emits明确声明
- [ ] 使用scoped样式
- [ ] CSS类名使用BEM方法论
- [ ] 路由组件使用懒加载
- [ ] 复杂逻辑使用计算属性
- [ ] Store按功能模块划分
- [ ] 代码通过ESLint检查
- [ ] 组件具有良好的可复用性

## 🚀 最佳实践示例

### 完整的页面组件示例
```vue
<!-- views/enterprise/Detail.vue -->
<template>
  <div class="enterprise-detail">
    <PageHeader
      :title="enterprise?.name || '企业详情'"
      :breadcrumb="breadcrumbItems"
    >
      <template #extra>
        <a-space>
          <a-button @click="handleEdit">
            <EditOutlined />
            编辑
          </a-button>
          <a-button type="primary" @click="handleCreateDeclaration">
            <PlusOutlined />
            新建申报
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <a-spin :spinning="loading">
      <a-row :gutter="24">
        <a-col :span="16">
          <a-card title="基本信息" class="enterprise-detail__card">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="企业名称">
                {{ enterprise?.name }}
              </a-descriptions-item>
              <a-descriptions-item label="统一社会信用代码">
                {{ enterprise?.creditCode }}
              </a-descriptions-item>
              <a-descriptions-item label="法定代表人">
                {{ enterprise?.legalRepresentative }}
              </a-descriptions-item>
              <a-descriptions-item label="注册资本">
                {{ enterprise?.registeredCapital }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>

        <a-col :span="8">
          <a-card title="统计信息" class="enterprise-detail__card">
            <div class="stats-grid">
              <div class="stats-item">
                <div class="stats-item__value">{{ statistics.declarationCount }}</div>
                <div class="stats-item__label">申报次数</div>
              </div>
              <div class="stats-item">
                <div class="stats-item__value">¥{{ formatCurrency(statistics.totalTax) }}</div>
                <div class="stats-item__label">累计税额</div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </a-spin>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { EditOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

import PageHeader from '@/components/common/PageHeader.vue'
import { getEnterpriseDetail, getEnterpriseStatistics } from '@/api/enterprise'
import { formatCurrency } from '@/utils/format'

export default defineComponent({
  name: 'EnterpriseDetail',
  components: {
    PageHeader,
    EditOutlined,
    PlusOutlined
  },
  setup() {
    const route = useRoute()
    const router = useRouter()

    // 响应式数据
    const loading = ref(false)
    const enterprise = ref(null)
    const statistics = reactive({
      declarationCount: 0,
      totalTax: 0,
      invoiceCount: 0
    })

    // 计算属性
    const breadcrumbItems = computed(() => [
      { title: '首页', path: '/' },
      { title: '企业管理', path: '/enterprise' },
      { title: '企业详情' }
    ])

    // 方法
    const fetchEnterpriseDetail = async () => {
      try {
        loading.value = true
        const response = await getEnterpriseDetail(route.params.id)
        if (response.code === 200) {
          enterprise.value = response.data
        }
      } catch (error) {
        console.error('获取企业详情失败:', error)
        message.error('获取企业详情失败')
      } finally {
        loading.value = false
      }
    }

    const fetchStatistics = async () => {
      try {
        const response = await getEnterpriseStatistics(route.params.id)
        if (response.code === 200) {
          Object.assign(statistics, response.data)
        }
      } catch (error) {
        console.error('获取统计信息失败:', error)
      }
    }

    const handleEdit = () => {
      router.push(`/enterprise/edit/${route.params.id}`)
    }

    const handleCreateDeclaration = () => {
      router.push({
        name: 'declaration-create',
        query: { enterpriseId: route.params.id }
      })
    }

    // 生命周期
    onMounted(() => {
      fetchEnterpriseDetail()
      fetchStatistics()
    })

    return {
      loading,
      enterprise,
      statistics,
      breadcrumbItems,
      handleEdit,
      handleCreateDeclaration,
      formatCurrency
    }
  }
})
</script>

<style scoped>
.enterprise-detail {
  padding: 24px;
}

.enterprise-detail__card {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stats-item {
  text-align: center;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.stats-item__value {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.stats-item__label {
  font-size: 14px;
  color: #666;
}
</style>
```

### 可复用组件示例
```vue
<!-- components/common/DataTable.vue -->
<template>
  <div class="data-table">
    <div class="data-table__toolbar" v-if="$slots.toolbar">
      <slot name="toolbar" />
    </div>

    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="paginationConfig"
      :row-selection="rowSelection"
      :scroll="scroll"
      @change="handleTableChange"
      v-bind="$attrs"
    >
      <template v-for="(_, name) in $slots" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>
    </a-table>
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue'

export default defineComponent({
  name: 'DataTable',
  inheritAttrs: false,
  props: {
    columns: {
      type: Array,
      required: true
    },
    dataSource: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    pagination: {
      type: [Object, Boolean],
      default: () => ({})
    },
    rowSelection: {
      type: Object,
      default: null
    },
    scroll: {
      type: Object,
      default: null
    }
  },
  emits: ['change', 'pageChange', 'pageSizeChange'],
  setup(props, { emit }) {
    const paginationConfig = computed(() => {
      if (props.pagination === false) {
        return false
      }

      return {
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        ...props.pagination
      }
    })

    const handleTableChange = (pagination, filters, sorter) => {
      emit('change', { pagination, filters, sorter })

      if (pagination) {
        emit('pageChange', pagination.current)
        emit('pageSizeChange', pagination.pageSize)
      }
    }

    return {
      paginationConfig,
      handleTableChange
    }
  }
})
</script>

<style scoped>
.data-table__toolbar {
  margin-bottom: 16px;
}
</style>
```

### 组合式函数示例
```javascript
// composables/useTableData.js
import { ref, reactive, computed } from 'vue'

export function useTableData(fetchFunction, options = {}) {
  const loading = ref(false)
  const dataSource = ref([])
  const total = ref(0)

  const pagination = reactive({
    current: 1,
    pageSize: options.pageSize || 10,
    total: 0
  })

  const filters = reactive({})
  const sorter = reactive({})

  // 计算属性
  const hasData = computed(() => dataSource.value.length > 0)
  const isEmpty = computed(() => !loading.value && !hasData.value)

  // 获取数据
  const fetchData = async (params = {}) => {
    try {
      loading.value = true

      const requestParams = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...filters,
        ...params
      }

      const response = await fetchFunction(requestParams)

      if (response.code === 200) {
        dataSource.value = response.data.list || response.data
        total.value = response.data.total || 0
        pagination.total = total.value
      }
    } catch (error) {
      console.error('获取数据失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 刷新数据
  const refresh = () => {
    fetchData()
  }

  // 重置并刷新
  const reset = () => {
    pagination.current = 1
    Object.keys(filters).forEach(key => {
      delete filters[key]
    })
    Object.keys(sorter).forEach(key => {
      delete sorter[key]
    })
    fetchData()
  }

  // 处理表格变化
  const handleTableChange = ({ pagination: pag, filters: fil, sorter: sor }) => {
    if (pag) {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
    }

    Object.assign(filters, fil)
    Object.assign(sorter, sor)

    fetchData()
  }

  // 处理搜索
  const handleSearch = (searchParams) => {
    pagination.current = 1
    Object.assign(filters, searchParams)
    fetchData()
  }

  return {
    loading,
    dataSource,
    total,
    pagination,
    filters,
    sorter,
    hasData,
    isEmpty,
    fetchData,
    refresh,
    reset,
    handleTableChange,
    handleSearch
  }
}
```

## 🎯 表单处理最佳实践

### 表单验证
```vue
<template>
  <a-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    layout="vertical"
    @finish="handleSubmit"
  >
    <a-form-item
      name="email"
      label="邮箱"
    >
      <a-input
        v-model:value="formData.email"
        placeholder="请输入邮箱"
      />
    </a-form-item>

    <a-form-item
      name="password"
      label="密码"
    >
      <a-input-password
        v-model:value="formData.password"
        placeholder="请输入密码"
      />
    </a-form-item>

    <a-form-item>
      <a-button
        type="primary"
        html-type="submit"
        :loading="submitting"
      >
        提交
      </a-button>
    </a-form-item>
  </a-form>
</template>

<script>
import { defineComponent, ref, reactive } from 'vue'
import { message } from 'ant-design-vue'

export default defineComponent({
  name: 'UserForm',
  setup() {
    const formRef = ref()
    const submitting = ref(false)

    const formData = reactive({
      email: '',
      password: ''
    })

    const formRules = {
      email: [
        { required: true, message: '请输入邮箱' },
        { type: 'email', message: '请输入有效的邮箱地址' }
      ],
      password: [
        { required: true, message: '请输入密码' },
        { min: 8, message: '密码长度不能少于8位' },
        {
          pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
          message: '密码必须包含大小写字母和数字'
        }
      ]
    }

    const handleSubmit = async (values) => {
      try {
        submitting.value = true

        // 调用API
        await submitForm(values)

        message.success('提交成功')

        // 重置表单
        formRef.value.resetFields()
      } catch (error) {
        console.error('提交失败:', error)
        message.error('提交失败，请重试')
      } finally {
        submitting.value = false
      }
    }

    return {
      formRef,
      formData,
      formRules,
      submitting,
      handleSubmit
    }
  }
})
</script>
```

## 🔧 开发工具配置

### Vite配置优化
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/assets/styles/variables.scss";`
      }
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          antd: ['ant-design-vue']
        }
      }
    }
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8081',
        changeOrigin: true
      }
    }
  }
})
```

### TypeScript支持
```typescript
// types/api.ts
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface User {
  id: string
  email: string
  name: string
  role: string
  createdAt: string
}

export interface Enterprise {
  id: string
  name: string
  creditCode: string
  legalRepresentative: string
  status: 'active' | 'inactive'
}

// composables/useApi.ts
import { ref } from 'vue'
import type { ApiResponse } from '@/types/api'

export function useApi<T>(apiFunction: (...args: any[]) => Promise<ApiResponse<T>>) {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const data = ref<T | null>(null)

  const execute = async (...args: any[]) => {
    try {
      loading.value = true
      error.value = null

      const response = await apiFunction(...args)

      if (response.code === 200) {
        data.value = response.data
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '请求失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    data,
    execute
  }
}
```

## 🌐 国际化(i18n)规范

### 基本原则

- 使用Vue I18n作为国际化解决方案
- 所有面向用户的文本必须使用国际化方案处理，不允许硬编码
- 翻译文件按功能模块组织，避免单个大文件
- 支持中文和英文作为基本语言，可扩展其他语言

### 目录结构

```
src/
  locales/
    index.js          # i18n配置和实例创建
    zh-CN/            # 中文翻译
      common.js       # 通用翻译
      login.js        # 登录模块翻译
      enterprise.js   # 企业模块翻译
      ...
    en-US/            # 英文翻译
      common.js
      login.js
      enterprise.js
      ...
```

### 翻译文件格式

```javascript
// locales/zh-CN/enterprise.js
export default {
  title: '企业管理',
  form: {
    name: '企业名称',
    creditCode: '统一社会信用代码',
    legalRepresentative: '法定代表人',
    status: {
      label: '状态',
      active: '正常',
      inactive: '停用'
    }
  },
  actions: {
    create: '新建企业',
    edit: '编辑',
    delete: '删除',
    view: '查看详情'
  },
  messages: {
    createSuccess: '企业创建成功',
    updateSuccess: '企业信息更新成功',
    deleteConfirm: '确定要删除该企业吗？',
    deleteSuccess: '企业删除成功'
  }
}
```

### 使用方式

```vue
<template>
  <div>
    <h1>{{ t('enterprise.title') }}</h1>
    <a-form>
      <a-form-item :label="t('enterprise.form.name')">
        <a-input v-model:value="form.name" />
      </a-form-item>
      
      <a-form-item :label="t('enterprise.form.status.label')">
        <a-select v-model:value="form.status">
          <a-select-option value="active">
            {{ t('enterprise.form.status.active') }}
          </a-select-option>
          <a-select-option value="inactive">
            {{ t('enterprise.form.status.inactive') }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import { defineComponent, reactive } from 'vue'
import { useI18n } from 'vue-i18n'

export default defineComponent({
  setup() {
    const { t } = useI18n()
    
    const form = reactive({
      name: '',
      status: 'active'
    })
    
    return {
      t,
      form
    }
  }
})
</script>
```

### 语言切换

```vue
<template>
  <a-dropdown>
    <a-button>
      {{ currentLang === 'zh-CN' ? '中文' : 'English' }}
      <down-outlined />
    </a-button>
    <template #overlay>
      <a-menu @click="changeLang">
        <a-menu-item key="zh-CN">中文</a-menu-item>
        <a-menu-item key="en-US">English</a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script>
import { defineComponent, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { DownOutlined } from '@ant-design/icons-vue'

export default defineComponent({
  components: {
    DownOutlined
  },
  setup() {
    const { locale } = useI18n()
    
    const currentLang = computed(() => locale.value)
    
    const changeLang = ({ key }) => {
      locale.value = key
      localStorage.setItem('lang', key)
    }
    
    return {
      currentLang,
      changeLang
    }
  }
})
</script>
```

### 日期和数字格式化

使用Vue I18n的日期和数字格式化功能，确保不同语言环境下的一致显示：

```javascript
// 日期格式化
t('date', new Date(), { dateStyle: 'full' })

// 数字格式化
t('currency', 1000, { style: 'currency', currency: 'CNY' })
```

## 🧪 测试规范

### 单元测试

- 使用Vitest作为单元测试框架
- 使用Vue Test Utils进行组件测试
- 关键业务逻辑和组件必须编写单元测试
- 测试文件与源文件同目录，使用`.spec.js`或`.test.js`后缀

#### 组件测试示例

```javascript
// components/common/DataTable.test.js
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import DataTable from './DataTable.vue'

describe('DataTable', () => {
  it('renders correctly with props', () => {
    const columns = [
      { title: '名称', dataIndex: 'name' },
      { title: '年龄', dataIndex: 'age' }
    ]
    const dataSource = [
      { id: 1, name: '张三', age: 30 },
      { id: 2, name: '李四', age: 25 }
    ]
    
    const wrapper = mount(DataTable, {
      props: {
        columns,
        dataSource,
        loading: false
      }
    })
    
    expect(wrapper.find('.data-table').exists()).toBe(true)
    expect(wrapper.findAll('tbody tr').length).toBe(2)
  })
  
  it('emits change event when table changes', async () => {
    const wrapper = mount(DataTable, {
      props: {
        columns: [{ title: '名称', dataIndex: 'name' }],
        dataSource: [{ id: 1, name: '张三' }],
        pagination: { current: 1, pageSize: 10 }
      }
    })
    
    await wrapper.vm.handleTableChange(
      { current: 2, pageSize: 10 },
      {},
      {}
    )
    
    expect(wrapper.emitted().change).toBeTruthy()
    expect(wrapper.emitted().pageChange[0]).toEqual([2])
  })
})
```

#### Composable函数测试示例

```javascript
// composables/useTableData.test.js
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useTableData } from './useTableData'

describe('useTableData', () => {
  const mockFetchFunction = vi.fn()
  let tableData
  
  beforeEach(() => {
    vi.resetAllMocks()
    mockFetchFunction.mockResolvedValue({
      code: 200,
      data: {
        list: [{ id: 1, name: '测试数据' }],
        total: 1
      }
    })
    
    tableData = useTableData(mockFetchFunction, { pageSize: 10 })
  })
  
  it('initializes with default values', () => {
    expect(tableData.loading.value).toBe(false)
    expect(tableData.dataSource.value).toEqual([])
    expect(tableData.pagination.current).toBe(1)
    expect(tableData.pagination.pageSize).toBe(10)
  })
  
  it('fetches data correctly', async () => {
    await tableData.fetchData()
    
    expect(mockFetchFunction).toHaveBeenCalledWith({
      page: 1,
      pageSize: 10
    })
    expect(tableData.dataSource.value).toEqual([{ id: 1, name: '测试数据' }])
    expect(tableData.total.value).toBe(1)
  })
  
  it('handles search correctly', async () => {
    await tableData.handleSearch({ keyword: '测试' })
    
    expect(tableData.pagination.current).toBe(1)
    expect(tableData.filters.keyword).toBe('测试')
    expect(mockFetchFunction).toHaveBeenCalledWith({
      page: 1,
      pageSize: 10,
      keyword: '测试'
    })
  })
})
```

### 端到端测试

- 使用Cypress进行端到端测试
- 覆盖关键用户流程和功能
- 测试文件放置在`cypress/e2e`目录下

#### 端到端测试示例

```javascript
// cypress/e2e/login.cy.js
describe('登录功能', () => {
  beforeEach(() => {
    cy.visit('/login')
  })
  
  it('显示登录表单', () => {
    cy.get('[data-cy=login-form]').should('be.visible')
    cy.get('[data-cy=email-input]').should('be.visible')
    cy.get('[data-cy=password-input]').should('be.visible')
    cy.get('[data-cy=login-button]').should('be.visible')
  })
  
  it('验证表单输入', () => {
    cy.get('[data-cy=login-button]').click()
    cy.get('[data-cy=email-error]').should('be.visible')
    
    cy.get('[data-cy=email-input]').type('invalid-email')
    cy.get('[data-cy=login-button]').click()
    cy.get('[data-cy=email-error]').should('contain', '有效的邮箱')
  })
  
  it('成功登录后跳转到首页', () => {
    cy.intercept('POST', '/api/auth/login', {
      statusCode: 200,
      body: {
        code: 200,
        data: {
          token: 'fake-token',
          user: { id: 1, name: '测试用户' }
        }
      }
    }).as('loginRequest')
    
    cy.get('[data-cy=email-input]').type('<EMAIL>')
    cy.get('[data-cy=password-input]').type('Password123')
    cy.get('[data-cy=login-button]').click()
    
    cy.wait('@loginRequest')
    cy.url().should('include', '/dashboard')
    cy.get('[data-cy=user-name]').should('contain', '测试用户')
  })
})
```

## 🔒 安全最佳实践

### 防止XSS攻击

- 使用Vue的内置机制处理HTML内容，避免使用`v-html`指令
- 如必须使用`v-html`，确保内容已经过严格过滤
- 使用`DOMPurify`等库过滤用户输入的HTML内容

```javascript
import DOMPurify from 'dompurify'

export function useSafeHtml() {
  const sanitize = (html) => {
    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: ['p', 'b', 'i', 'em', 'strong', 'a', 'ul', 'ol', 'li'],
      ALLOWED_ATTR: ['href', 'target']
    })
  }
  
  return { sanitize }
}
```

### CSRF保护

- 在API请求中包含CSRF令牌
- 使用HTTP-only cookies存储敏感信息

```javascript
// api/request.js
import axios from 'axios'

const request = axios.create({
  baseURL: '/api',
  timeout: 10000,
  withCredentials: true
})

// 从meta标签获取CSRF令牌
const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')

request.interceptors.request.use(config => {
  if (csrfToken) {
    config.headers['X-CSRF-TOKEN'] = csrfToken
  }
  return config
})

export default request
```

### 敏感数据处理

- 不在前端存储敏感数据
- 使用HTTPS传输所有数据
- 使用安全的存储机制（如HTTP-only cookies）存储认证令牌

### 输入验证

- 在前端和后端都进行输入验证
- 使用白名单方式验证输入，而不是黑名单

```javascript
// 表单验证示例
const validateTaxCode = (rule, value) => {
  // 统一社会信用代码验证（18位）
  const pattern = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/
  if (!value) {
    return Promise.reject('请输入统一社会信用代码')
  }
  if (!pattern.test(value)) {
    return Promise.reject('请输入有效的统一社会信用代码')
  }
  return Promise.resolve()
}
```

## 📱 移动端适配规范

### 响应式设计

- 使用媒体查询和弹性布局适配不同屏幕尺寸
- 使用相对单位（rem, em, vw, vh）而非固定像素值
- 设置合适的视口配置

```html
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
```

### 移动优先策略

- 采用移动优先的CSS编写方式
- 使用媒体查询从小屏幕向大屏幕适配

```scss
.container {
  padding: 10px;
  
  @media (min-width: 768px) {
    padding: 20px;
  }
  
  @media (min-width: 1200px) {
    padding: 30px;
    max-width: 1140px;
    margin: 0 auto;
  }
}
```

### 触摸友好设计

- 确保交互元素有足够大的点击区域（至少44x44px）
- 避免依赖悬停状态的交互
- 实现适合触摸的交互模式

```scss
.button {
  padding: 12px 20px;
  min-height: 44px;
  min-width: 44px;
  
  // 增加点击区域
  &::before {
    content: '';
    position: absolute;
    top: -10px;
    right: -10px;
    bottom: -10px;
    left: -10px;
  }
}
```

## 🚨 错误处理和异常捕获

### 全局错误处理

- 实现全局错误处理机制捕获未处理的异常
- 提供用户友好的错误提示
- 记录错误信息以便调试

```javascript
// main.js
import { createApp } from 'vue'
import App from './App.vue'
import { message } from 'ant-design-vue'
import * as Sentry from '@sentry/vue'

const app = createApp(App)

// Sentry错误监控集成
if (import.meta.env.PROD) {
  Sentry.init({
    app,
    dsn: 'YOUR_SENTRY_DSN',
    integrations: [
      new Sentry.BrowserTracing({
        routingInstrumentation: Sentry.vueRouterInstrumentation(router)
      })
    ],
    tracesSampleRate: 1.0
  })
}

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('全局错误:', err)
  
  // 向用户显示友好错误
  message.error('操作失败，请稍后重试')
  
  // 在生产环境中上报错误
  if (import.meta.env.PROD) {
    Sentry.captureException(err)
  }
}

app.mount('#app')
```

### API错误处理

- 统一处理API错误响应
- 区分不同类型的错误（网络错误、认证错误、业务错误等）

```javascript
// api/request.js
import axios from 'axios'
import { message } from 'ant-design-vue'
import router from '@/router'

const request = axios.create({
  baseURL: '/api',
  timeout: 10000
})

// 响应拦截器
request.interceptors.response.use(
  response => {
    const { code, data, message: msg } = response.data
    
    // 业务成功
    if (code === 200) {
      return response.data
    }
    
    // 业务错误
    message.error(msg || '操作失败')
    return Promise.reject(new Error(msg || '操作失败'))
  },
  error => {
    if (error.response) {
      const { status } = error.response
      
      // 处理不同HTTP状态码
      switch (status) {
        case 401:
          message.error('登录已过期，请重新登录')
          router.push('/login')
          break
        case 403:
          message.error('没有操作权限')
          break
        case 404:
          message.error('请求的资源不存在')
          break
        case 500:
          message.error('服务器错误，请稍后重试')
          break
        default:
          message.error('请求失败，请稍后重试')
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      message.error('网络连接失败，请检查网络')
    } else {
      // 请求配置错误
      message.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

export default request
```

### 组件级错误边界

- 使用Vue 3的`onErrorCaptured`生命周期钩子实现错误边界组件
- 防止整个应用因局部错误而崩溃

```vue
<!-- components/common/ErrorBoundary.vue -->
<template>
  <div>
    <div v-if="error" class="error-boundary">
      <h3>出错了</h3>
      <p>{{ errorMessage }}</p>
      <a-button @click="reset">重试</a-button>
    </div>
    <slot v-else></slot>
  </div>
</template>

<script>
import { defineComponent, ref, onErrorCaptured } from 'vue'

export default defineComponent({
  name: 'ErrorBoundary',
  emits: ['error'],
  setup(props, { emit, slots }) {
    const error = ref(null)
    const errorMessage = ref('')
    
    onErrorCaptured((err, instance, info) => {
      error.value = err
      errorMessage.value = '组件渲染出错，请稍后重试'
      emit('error', { err, instance, info })
      return true // 阻止错误继续传播
    })
    
    const reset = () => {
      error.value = null
      errorMessage.value = ''
    }
    
    return {
      error,
      errorMessage,
      reset
    }
  }
})
</script>

<style scoped>
.error-boundary {
  padding: 20px;
  border: 1px solid #f5222d;
  border-radius: 4px;
  background-color: #fff1f0;
  text-align: center;
}
</style>
```

## 📝 代码注释规范

### 文件头注释

每个文件顶部应包含文件说明、作者、创建日期等信息：

```javascript
/**
 * @file 企业管理模块 - 企业详情页面
 * @description 显示企业详细信息、统计数据和相关操作
 * <AUTHOR> <<EMAIL>>
 * @created 2024-01-15
 */
```

### 组件注释

组件应包含功能说明、props和emits描述：

```javascript
/**
 * @component DataTable
 * @description 通用数据表格组件，支持分页、排序、筛选等功能
 * 
 * @example
 * <data-table
 *   :columns="columns"
 *   :data-source="dataSource"
 *   :loading="loading"
 *   @change="handleTableChange"
 * />
 */
```

### 函数注释

函数应包含功能说明、参数和返回值描述：

```javascript
/**
 * 格式化金额为带千分位和货币符号的字符串
 * @param {number} amount - 金额数值
 * @param {string} [currency='CNY'] - 货币代码
 * @param {string} [locale='zh-CN'] - 地区设置
 * @returns {string} 格式化后的金额字符串
 */
export function formatCurrency(amount, currency = 'CNY', locale = 'zh-CN') {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency
  }).format(amount)
}
```

### 复杂逻辑注释

对于复杂的业务逻辑，应添加详细的注释说明：

```javascript
// 计算应纳税所得额
// 1. 收入总额减去免税收入
// 2. 减去各项扣除
// 3. 减去专项附加扣除
const taxableIncome = totalIncome - exemptIncome - deductions - specialDeductions

// 使用超额累进税率计算个人所得税
let tax = 0
if (taxableIncome <= 36000) {
  // 3%税率部分
  tax = taxableIncome * 0.03
} else if (taxableIncome <= 144000) {
  // 10%税率部分
  tax = 36000 * 0.03 + (taxableIncome - 36000) * 0.1
} else {
  // 其他税率部分...
}
```

## 🚀 构建和部署规范

### 环境配置

- 使用`.env`文件区分不同环境配置
- 至少包含开发、测试、生产三个环境

```
# .env.development
VITE_APP_API_BASE_URL=/api
VITE_APP_ENV=development

# .env.test
VITE_APP_API_BASE_URL=https://test-api.example.com
VITE_APP_ENV=test

# .env.production
VITE_APP_API_BASE_URL=https://api.example.com
VITE_APP_ENV=production
```

### 构建优化

- 启用代码分割和懒加载
- 优化资源大小（压缩、Tree-shaking）
- 使用现代模式构建

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { visualizer } from 'rollup-plugin-visualizer'

export default defineConfig(({ mode }) => {
  const isProd = mode === 'production'
  
  return {
    plugins: [
      vue(),
      // 在生产环境生成构建分析报告
      isProd && visualizer({
        open: true,
        filename: 'dist/stats.html',
        gzipSize: true,
        brotliSize: true
      })
    ].filter(Boolean),
    build: {
      target: 'es2015',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: isProd,
          drop_debugger: isProd
        }
      },
      rollupOptions: {
        output: {
          manualChunks: {
            vue: ['vue', 'vue-router', 'pinia'],
            antd: ['ant-design-vue'],
            vendor: ['axios', 'lodash-es']
          }
        }
      }
    }
  }
})
```

### CI/CD流程

- 使用GitLab CI/Jenkins等工具实现自动化构建和部署
- 包含代码检查、测试、构建、部署等步骤

```yaml
# .gitlab-ci.yml
stages:
  - lint
  - test
  - build
  - deploy

lint:
  stage: lint
  script:
    - npm ci
    - npm run lint

unit-test:
  stage: test
  script:
    - npm ci
    - npm run test:unit

e2e-test:
  stage: test
  script:
    - npm ci
    - npm run test:e2e

build-test:
  stage: build
  script:
    - npm ci
    - npm run build:test
  artifacts:
    paths:
      - dist/
  only:
    - develop

build-prod:
  stage: build
  script:
    - npm ci
    - npm run build:prod
  artifacts:
    paths:
      - dist/
  only:
    - master

deploy-test:
  stage: deploy
  script:
    - rsync -avz --delete dist/ user@test-server:/var/www/html/
  only:
    - develop

deploy-prod:
  stage: deploy
  script:
    - rsync -avz --delete dist/ user@prod-server:/var/www/html/
  only:
    - master
  when: manual
```

## ♿ 辅助功能(Accessibility)规范

### 基本原则

- 遵循WCAG 2.1标准
- 确保应用可以被屏幕阅读器正确解析
- 提供键盘导航支持

### 语义化HTML

- 使用语义化HTML元素（`<header>`, `<nav>`, `<main>`, `<section>`, `<article>`, `<footer>`等）
- 使用正确的标题层级（h1-h6）

```vue
<template>
  <div class="page-container">
    <header>
      <h1>企业管理系统</h1>
    </header>
    
    <nav aria-label="主导航">
      <!-- 导航内容 -->
    </nav>
    
    <main>
      <section aria-labelledby="section-title">
        <h2 id="section-title">企业列表</h2>
        <!-- 内容 -->
      </section>
    </main>
    
    <footer>
      <!-- 页脚内容 -->
    </footer>
  </div>
</template>
```

### ARIA属性

- 使用适当的ARIA角色和属性增强可访问性
- 为非语义元素添加适当的角色

```vue
<template>
  <div role="tablist">
    <button
      role="tab"
      :id="`tab-${id}-1`"
      :aria-selected="activeTab === 1"
      :aria-controls="`panel-${id}-1`"
      @click="activeTab = 1"
    >
      基本信息
    </button>
    <button
      role="tab"
      :id="`tab-${id}-2`"
      :aria-selected="activeTab === 2"
      :aria-controls="`panel-${id}-2`"
      @click="activeTab = 2"
    >
      财务信息
    </button>
    
    <div
      role="tabpanel"
      :id="`panel-${id}-1`"
      :aria-labelledby="`tab-${id}-1`"
      v-show="activeTab === 1"
    >
      <!-- 基本信息内容 -->
    </div>
    
    <div
      role="tabpanel"
      :id="`panel-${id}-2`"
      :aria-labelledby="`tab-${id}-2`"
      v-show="activeTab === 2"
    >
      <!-- 财务信息内容 -->
    </div>
  </div>
</template>
```

### 键盘可访问性

- 确保所有交互元素可通过键盘访问
- 提供可见的焦点状态
- 实现键盘快捷键

```scss
// 全局焦点样式
:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

// 交互元素样式
.interactive-element {
  &:focus-visible {
    outline: 2px solid #1890ff;
    outline-offset: 2px;
  }
  
  &:hover {
    background-color: #f5f5f5;
  }
}
```

## 📚 参考资源

- [Vue 3官方文档](https://vuejs.org/)
- [Vue 3风格指南](https://vuejs.org/style-guide/)
- [Ant Design Vue文档](https://antdv.com/)
- [Pinia文档](https://pinia.vuejs.org/)
- [Vue Router文档](https://router.vuejs.org/)
- [Vite文档](https://vitejs.dev/)
- [Vue 3 TypeScript指南](https://vuejs.org/guide/typescript/overview.html)
- [Vue I18n文档](https://vue-i18n.intlify.dev/)
- [Vitest文档](https://vitest.dev/)
- [Cypress文档](https://docs.cypress.io/)
- [Web内容无障碍指南(WCAG)](https://www.w3.org/WAI/standards-guidelines/wcag/)
- [MDN Web无障碍资源](https://developer.mozilla.org/zh-CN/docs/Web/Accessibility)
